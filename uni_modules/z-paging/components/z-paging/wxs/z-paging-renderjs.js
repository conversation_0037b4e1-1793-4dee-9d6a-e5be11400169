// [z-paging]使用renderjs在app-vue和h5中对touchmove事件冒泡进行处理

import u from '../js/z-paging-utils'
var data = {
	renderScrollTop: 0,
	renderUsePageScroll: false,
	startY: 0,
	isTouchFromZPaging: false
}

var currentVm = null;

export default {
	mounted() {
		this._handleTouch();
		// #ifdef APP-VUE
		this.$ownerInstance && this.$ownerInstance.callMethod('_checkVirtualListScroll');
		// #endif
	},
	methods: {
		//接收逻辑层发送的数据
		renderPropScrollTopChange(newVal, oldVal, ownerVm, vm) {
			if (newVal === -1) return;
			currentVm = ownerVm;
			data.renderScrollTop = newVal;
		},
		renderPropUsePageScrollChange(newVal, oldVal, ownerVm, vm) {
			if (newVal === -1) return;
			data.renderUsePageScroll = newVal;
		},
		//拦截处理touch事件
		_handleTouch() {
			if (window && !window.$zPagingRenderJsInited) {
				window.$zPagingRenderJsInited = true;
				window.addEventListener('touchstart', this._handleTouchstart, {
					passive: true
				})
				window.addEventListener('touchmove', this._handleTouchmove, {
					passive: false
				})
			}
		},
		_handleTouchstart(e) {
			const touch = u.getTouch(e);
			data.startY = touch.touchY;
			data.isTouchFromZPaging = u.getTouchFromZPaging(e.target);
			this.$ownerInstance && this.$ownerInstance.callMethod('_updateRenderJsData');
		},
		_handleTouchmove(e) {
			const touch = u.getTouch(e);
			var moveY = touch.touchY - data.startY;
			//v2.1.4起删除条件：(data.isTouchFromZPaging && data.renderIsIos && !data.renderUsePageScroll && moveY < 0)
			if (data.isTouchFromZPaging && data.renderScrollTop < 1 && moveY > 0) {
				if (e.cancelable && !e.defaultPrevented) {
					e.preventDefault();
				}
			}
		},
		_removeAllEventListener(){
			window.removeEventListener('touchstart');
			window.removeEventListener('touchmove');
		}
	}
};
