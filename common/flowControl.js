// 流程控制

// 
/**
 * 主流程控制方法

 * 1 开户（port:是否已实名）
 * 		用户未实名认证（跳转到实名认证页）（下一步）
 * 		用户已实名认证（下一步）
 * 2 绑定车辆（port:是否已上传行驶证）
 *		未上传车辆行驶证（上传行驶证页）
 *		已上传车辆行驶证（local：用户信息与行驶证信息验证身份证）
 * 			车主与用户不一致（上传车主身份证页）（下一步）
 * 			车主与用户一致（下一步）
 * 3 标签（port: 是否已办理过ETC）
 * 		该车辆未办理过ETC（选择发行方产品页）（下一步）
 * 		该车辆已办理，未绑定ETC账户或者已办理，绑定其他ETC账户（确认原标签是否可用页）
 * 			原标签存在（下一步）
 * 			原标签遗失（跳转到选择发行方产品页）（下一步）
 * 4 签约（port：是否有已签约的账户）
 * 		未签约（签约账户页）
 * 		已签约 （下一步）
 * 5 关联支付
 * 		选择支付方式关联（port：需要判断该账户是否可跟这辆车签约）（下一步）
 * 6 订单
 * 		不需要邮寄新标签/需要邮寄新标签（填写收货地址页）
 * 			需要支付标签费用（确认有支付订单）
 * 			不需要支付标签费用（确认无支付订单）
 * 				处理订单
 * 					完成申请
 * 		
 */

import {
	setFlowControlStorage,
	getFlowControlStorage,
	getCurrUserInfo,
	getCurrentCar,
	setCurrentCar,
	getTokenId
} from '@/common/storageUtil.js'
import {
	flowControlStep
} from '@/common/systemConstant.js'
import Api from '@/common/api/index.js';
import request from '@/common/request-1/request.js';
/**
 * @param {Object} step
 * 这里只处理流程中跳转到页面，不处理业务逻辑
 */
export function mainFlowControl(step, param) {
	console.log('step', step);
	// 判断车主是否已实名
	let userInfo = getCurrUserInfo();
	// 用户是否已实名
	console.log(JSON.stringify(userInfo))
	//let realNameFlag = userInfo && userInfo.status == 1 ? false : true;
	//未实名跳转实名页面
    //if(!realNameFlag){
	//	goShowModalAndSkip("您还没有实名认证，点击确定去实名",'/pages/etc/certification-comfirm/certification-comfirm')
	//	return;
	//}
	/**
	 * @param {Object} step
	 * -1 首页点击快捷办理
	 * 0 开始办理页 点击按钮,
	 * 1 用户实名认证页, 点击“申请办理ETC” 
	 * 2 上传行驶证页，点击下一步 
	 * 3 车辆资料核对页 点击“下一步”
	 * 4 上传车主身份证页, 点击“下一步flowControlStep”
	 * 5 ETC产品办理页, 点击“开始办理”
	 * 6 确认标签页，点击确认
	 * 7 签约列表页， 点击已签约列表的账户（如果是新签约则走新签约流程，这里不考虑）
	 * 8 车辆关联支付方式页，点击下一步
	 * 9 地址列表页，点击下一步
	 * 10 订单确认页面， 点击确认
	 */
	switch (step) {
		case flowControlStep.homeIndex:
			// -1 首页点击车牌
			if(param){
				goUrlByCarStatus(param);
			}else{
				goShowModalAndSkip("缺少车辆状态")
				return;
			}
			break;
		case flowControlStep.stratproc:
			/**
			 0 开始办理页 点击按钮
			 通过vehicleId 调用接口7.7，
			 返回 status
				1-	完成办理 (弹框，跳转首页【结束】)
				2- 	未上传车主身份证 (弹框提示 未上传车主身份证，确认继续办理，跳转上传车主身份证页【结束】取消，当前页【结束】)
				3-	未上传行驶证 (弹框提示 未上传行驶证，确认继续办理，跳转上传行驶证页【结束】取消，当前页【结束】)
				4-	未关联支付渠道 (弹框提示 未关联支付渠道，确认继续办理，跳转ETC产品列表【结束】取消，当前页【结束】)
				5-	未完成支付 (弹框提示 未完成支付，确认继续办理，跳转订单确认页面【结束】取消，当前页【结束】)
				6-	OBU未激活
				【结束】
			 */
			reqIssueStatus().then(res => {
						let { status } = res;
						let currCar = getCurrentCar();
						currCar.status = status;
						setCurrentCar(currCar);
						goUrlByCarStatus(status);
					},
					msg => {}
				)
				.catch(err => {});
			break;
		case flowControlStep.checkIdcard:
			/**
			 1用户实名认证页, 完成后,点击“申请办理ETC”
			 跳转首页【结束】
			 */
			uni.switchTab({
				url:'/pages/tab-bar/index/index.vue'
			})
			break;
		case flowControlStep.uploadLincense:
			/**
			 2上传行驶证页，点击下一步
			 跳转车辆资料核对页【结束】
			 */
			uni.navigateTo({
				url:'/pagesA/etc/car-info-fourth/car-info-fourth'
			})
			break;

		case flowControlStep.orderinfoEnsure:
			/**
			 3车辆资料核对页 点击“下一步”
			 ①调用接口7.11判断车主与当前用户是否一致
				不一致：跳转 上传车主身份证页【结束】
				一致：调用接口7.8是否可以选择发行方
					可以 跳转 ETC产品页【结束】
					不可以 跳转 确认标签页【结束】
			 */
			if(param){
				switch (param) {
					case 2: // 不一致
						goShowModalAndSkip("您的认证信息与行驶证信息不一致，需要上传车主身份证",
						"/pagesA/etc/car-certification-comfirm/car-certification-comfirm")
						break;
					case 1: // 一致
						// stepThreeFourCommon();
						// uni.navigateTo({
						// 	url:'/pages/etc/etc-product/etc-product'
						// })
						uni.navigateTo({
							url:'/pagesA/etc/relevance-pay-account/relevance-pay-account'
						});
						break;
					default:
						goShowModalAndSkip("未知状态")
						break;
				}
			}else{
				goShowModalAndSkip("缺少车户判断")
				return;
			}
			// reqCarOwnerEqualsUser().then(res => {
			// 			let { status } = res;
			// 			
			// 		},
			// 		msg => {}
			// 	)
			// 	.catch(err => {})
			break;
		case flowControlStep.vehicleOwner:
			/**
			 4上传车主身份证页, 点击“下一步”
			 调用接口7.8是否可以选择发行方
			 	可以 跳转 ETC产品页【结束】
			 	不可以 跳转 确认标签页【结束】
			 */
			// stepThreeFourCommon();
			break;
		case flowControlStep.selectProject:
			/**
			 * 5 ETC产品办理页, 点击“开始办理”
			 * 跳转 签约列表页【结束】
			 */
			uni.navigateTo({
				url:'/pagesA/etc/pay-account-list/pay-account-list"'
			})
			break;
		case flowControlStep.confirmLabel:
			/**
			 6 确认标签页
			 跳转 签约列表页【结束】
			 */
			uni.navigateTo({
				url:'/pagesA/etc/pay-account-list/pay-account-list"'
			})
			break;
		case flowControlStep.payAccountList:
			/**
			 7 签约列表页， 点击已签约列表的账户
			 跳转 车辆关联支付方式页【结束】
			 */
			uni.navigateTo({
				url:'/pagesA/etc/relevance-pay-account/relevance-pay-account'
			})
			break;
		case flowControlStep.relevancePayWay:
			/**
			 8 车辆关联支付方式页，点击下一步
			 调用接口7.14 判断是否邮寄新标签
				不需要 跳转订单确认页面【结束】
				需要 跳转地址列表页【结束】
				
			目前都是免费送，都是要邮寄新标签的
			 */
			uni.navigateTo({
				url:'/pagesA/etc/select-adress/select-adress'
			})
			// reqIsPostObu.then(res => {
			// 			let { status } = res;
			// 			switch (status) {
			// 				case 0: // 不需要
			// 					goShowModalAndSkip("您的认证信息与行驶证信息不一致，需要上传车主身份证",
			// 					"/pages/uni-home/uni-ehicle-owner/vehicle-owner")
			// 					uni.navigateTo({
			// 						url:'/pages/uni-ownMes/uni-ensure-order/ensure-order'
			// 					})
			// 					break;
			// 				case 1: // 需要
			// 					uni.navigateTo({
			// 						url:'/pages/uni-ownMes/uni-receive-address/receive-address'
			// 					})
			// 					break;
			// 				default:
			// 					goShowModalAndSkip("未知是否邮寄状态")
			// 					break;
			// 			}
			// 		},
			// 		msg => {}
			// 	)
			// 	.catch(err => {})
			break;
		case flowControlStep.receiveAddress:
			/**
			 9 地址列表页，点击下一步
				跳转订单确认页面【结束】
			 */
			uni.navigateTo({
				url:'/pagesA/etc/comfirm-order/comfirm-order'
			})
			break;
		case flowControlStep.ensureOrder:
			/**
			 10 订单确认页面， 点击确认
				结束，跳转首页【结束】
			 */
			uni.switchTab({
				url:'/pages/tab-bar/index/index.vue'
			})
			break;

		default:
			break;
	}
	// 
	// console.log('url',url)
	// return url
	// 

}

function reqIssueStatus() {
	let params = {
		//tokenId: uni.getStorageSync('tokenId'),
		tokenId:getTokenId(),
		vehicleId: getCurrentCar().vehicleId
	};
	let data = {
		fileName: Api.getIssueStatus.method,
		data: params
	};
	return request.post(Api.getIssueStatus.url, data)
};

function reqIssueObu() {
	let params = {
		//tokenId: uni.getStorageSync('tokenId'),
		tokenId:getTokenId(),
		vehicleId: getCurrentCar().vehicleId
	};
	let data = {
		fileName: Api.getIssueObu.method,
		data: params
	};
	return request.post(Api.getIssueObu.url, data)
};
// 弹框提示并跳转页面
function goShowModalAndSkip(content, confirmUrl, cancelUrl){
	uni.showModal({
		title:'提示',
		content:content,
		success:function(res){
			if (res.confirm && confirmUrl) {
				uni.navigateTo({
					url: confirmUrl
				});
			} else if (res.cancel && cancelUrl) {
				uni.navigateTo({
					url: cancelUrl
				});
			}
		}
	});
};
function goUrlByCarStatus(status){
	switch (status) {
		case 1:
		{
			uni.navigateTo({
				url:"/pagesB/personal/car-info/car-etcinfo"
			});
			break;
		}
		case 2:
			goShowModalAndSkip("您未上传车主身份证，点击确定继续办理",
			//"/pagesA/etc/car-certification-comfirm/car-certification-comfirm")
			"/pagesA/etc/car-certification-comfirm/s-car-certification-comfirm?type=third")
			break;
		case 3:
			goShowModalAndSkip("您未上传行驶证，点击确定继续办理",
			"/pagesA/etc/etc-product/etc-product?type=4")
			//"/pagesA/etc/car-info-third/car-info-third")
			break;
		case 4:
			goShowModalAndSkip("您未关联支付渠道，点击确定继续办理",
			"/pagesA/etc/relevance-pay-account/s-relevance-pay-account")
			break;
		case 5:
			goShowModalAndSkip("您未完成支付，点击确定继续办理",
			"/pagesA/etc/etc-product/etc-product?type=3")
			break;
		case 6:
			goShowModalAndSkip("您的OBU未激活，点击确定去激活","/pagesA/etc/car-activation/s-car-activation")
			break;
		default:
			goShowModalAndSkip("未知车辆状态")
			break;
	}
}