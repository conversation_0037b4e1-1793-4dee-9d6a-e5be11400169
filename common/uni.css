
@font-face {
  font-family: 'zjetcfont';  /* project id 1191489 */
  src: url('https://at.alicdn.com/t/font_1191489_x869ialpyic.eot');
  src: url('https://at.alicdn.com/t/font_1191489_x869ialpyic.eot?#iefix') format('embedded-opentype'),
  url('https://at.alicdn.com/t/font_1191489_x869ialpyic.woff2') format('woff2'),
  url('https://at.alicdn.com/t/font_1191489_x869ialpyic.woff') format('woff'),
  url('https://at.alicdn.com/t/font_1191489_x869ialpyic.ttf') format('truetype'),
  url('https://at.alicdn.com/t/font_1191489_x869ialpyic.svg#zjetcfont') format('svg');
}
.zjetc {
	font-family: zjetcfont;
	margin-left: 20upx;
	
}
.btn-color{
	background: #FDA150;
	color:#fff;
}
.all-border{
	border: 1upx solid #CCCCCC;
}
.border-top{
	border-top: 1upx solid #CCCCCC;
}
.border-right{
	border-right: 1upx solid #CCCCCC;
}
.border-bottom{
	border-bottom: 1upx solid #CCCCCC;
}
.border-left{
	border-left: 1upx solid #CCCCCC;
}
.page-section-spacing0{
			position: relative;
			background: url('~@/static/cp/bluelan.png') no-repeat;
			background-size: 100% 100%;
		}
.page-section-spacing2{
			background: url('~@/static/cp/yellow.png') no-repeat;
			background-size: 100% 100%;
		}
.page-section-spacing3{
			background: url('~@/static/cp/000.png') no-repeat;
			background-size: 100% 100%;
		}
.page-section-spacing4{
			background: url('~@/static/cp/bw.png') no-repeat;
			background-size: 100% 100%;
		}
.page-section-spacing5{
			background: url('~@/static/cp/gradualGreen.png') no-repeat;
			background-size: 100% 100%;
		}
.page-section-spacing6{
			background: url('~@/static/cp/whiteblue.png') no-repeat;
			background-size: 100% 100%;
		}