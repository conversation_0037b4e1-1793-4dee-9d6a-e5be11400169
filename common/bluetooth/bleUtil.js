//已经找到的设备列表
let foundDevices = []; //成功返回编码

const SUCCESS_CODE = 0;
const SUCCESS_MSG = "成功"; //打开适配器失败

const OPEN_ADAPTER_FAIL_CODE = 1001; // const OPEN_ADAPTER_FAIL_MSG = "打开蓝牙适配器失败"

const OPEN_ADAPTER_FAIL_MSG = "手机蓝牙功能未开启，请开启手机蓝牙功能后重试。"; //搜索设备失败

const SCAN_DEVICE_CODE = 1002; // const SCAN_DEVICE_MSG = "搜索设备失败"

const SCAN_DEVICE_MSG = "未检测到蓝牙标签设备，请确认标签设备蓝牙和定位已正确开启，并将手机靠近设备后重试。"; //连接设备失败

const CONNECT_FAIL_CODE = 1003;
const CONNECT_FAIL_MSG = "连接设备失败"; //停止搜索设备失败

const ERR_STOPSCANBLE_CODE = 1004;
const ERR_STOPSCANBLE_MSG = "停止搜索设备失败";
var isConnected = false;
var isAdapterState = false;

var scanTimeout = null;
var status = false; //开启蓝牙适配器扫描设备
var deviceId = ''

function getConnected() {
	return isConnected && isAdapterState;
}

function onBLEConnectionStateChange() {
	uni.onBLEConnectionStateChange(function(res) {
		// 该方法回调中可以用于处理连接意外断开等异常情况
		isConnected = res.connected;
		console.log(`device ${res.deviceId} state has changed, connected: ${res.connected}`)
	})
	uni.onBluetoothAdapterStateChange(function(res) {
		console.log('adapterState changed, now is', res)
		isAdapterState = res.available;
	})
}

function ScanDevice(deviceNameAry, callback, isList) {
	status = false;
	foundDevices = [];
	let list = [];
	let result = new Object();
	uni.openBluetoothAdapter({
		success: res => {
			console.log("openBluetoothAdapter:success", res);
			uni.startBluetoothDevicesDiscovery({
				allowDuplicatesKey: true,
				// services: ['FEE7'],
				success: res => {
					// 15s之后搜索不到即停止搜索
					scanTimeout = setTimeout(function() {
						console.log("StopScanDevice2222222222", status);

						if (!status) {
							StopScanDevice(result => {
								console.log("StopScanDevice11111111111111",
									result);
							});
							callback({
								code: 10002,
								err_msg: SCAN_DEVICE_MSG
							});
						}
					}, 15000);
					console.log("startBluetoothDevicesDiscovery:success", res);
					uni.onBluetoothDeviceFound(function(res) {
						//console.log('搜索设备', res.devices);
						let devices = res.devices;

						if (isList) {
							if (devices && devices[0] && devices[0].name) {
								list.push(devices[0].name);
							}

							if (list.length > 0) {
								callback(list);
							}
						} else {
							//console.log("onBluetoothDeviceFound:", devices && devices[0] && devices[0].name);

							for (let i = devices.length - 1; i >= 0; i--) {
								let isHave = false;

								for (let j = 0; j < foundDevices.length; j++) {
									if (devices[i].deviceId === foundDevices[j]
										.deviceId) {
										isHave = true;
										break;
									}
								}

								if (!isHave) {
									let device = devices[i];
									if (device.advertisServiceUUIDs && device
										.advertisServiceUUIDs.length > 0 && (device
											.advertisServiceUUIDs[0] == 'FEE7' || device
											.advertisServiceUUIDs[0] ==
											'0000FEE7-0000-1000-8000-00805F9B34FB')) {
										console.log("搜索到设备:" + device.deviceId, device
											.name, device.advertisServiceUUIDs);
										deviceId = device.deviceId;
										result = {
											code: SUCCESS_CODE,
											err_msg: SUCCESS_MSG,
											data: {
												...device,
												device_no: device.deviceId,
												device_name: device.name
											}
										};
										foundDevices.push(device);
										status = true;
										clearTimeout(scanTimeout);
										StopScanDevice(obj => {
											console.log("StopScanDevice",
												obj);
										});
										console.log(result, '--result--');
										callback(result);
									}
								}
							}
						}
					});
				},
				fail: err => {
					console.log("startBluetoothDevicesDiscovery:fail", err);
					result = {
						code: SCAN_DEVICE_CODE,
						err_msg: SCAN_DEVICE_MSG
					};
					callback(result);
				}
			});
		},
		fail: err => {
			console.log("openBluetoothAdapter:fail", err);
			result = {
				code: OPEN_ADAPTER_FAIL_CODE,
				err_msg: OPEN_ADAPTER_FAIL_MSG
			};
			callback(result);
		}
	});
}

; //停止设备搜索

function StopScanDevice(callback) {
	uni.stopBluetoothDevicesDiscovery({
		success(res) {
			console.log("停止设备搜索stopDiscovary:success", res);
			let resut = {
				code: SUCCESS_CODE,
				err_msg: SUCCESS_MSG
			};
			typeof callback === 'function' && callback(resut);
		},

		fail(err) {
			console.log("stopDiscovary:fail", err);
			let resut = {
				code: ERR_STOPSCANBLE_CODE,
				err_msg: ERR_STOPSCANBLE_MSG
			};
			typeof callback === 'function' && callback(resut);
		}

	});
}

; //关闭蓝牙适配器

function CloseBle(callback) {
	uni.closeBluetoothAdapter({
		success: res => {
			console.log("CloseBle:success", res);
			let result = {
				code: SUCCESS_CODE,
				err_msg: SUCCESS_MSG,
				data: res
			};
			callback(result);
		},
		fail: err => {
			console.log("CloseBle:fail", err);
			let result = {
				code: 1000,
				err_msg: "关闭蓝牙设备失败",
				data: err
			};
			callback(result);
		}
	});
}

; //蓝牙搜索接口
//关闭低功耗蓝牙
function closeBLEConnection() {
	console.log('+++++++uni.closeBLEConnection-----deviceId', deviceId)
	if (!deviceId) return;
	uni.closeBLEConnection({
		deviceId: deviceId,
		success(res) {
			console.log('+++++++uni.closeBLEConnection-----', res)
		},
		fail(err) {
			console.log('+++++++uni.closeBLEConnection-----fail', err)
		}
	})
}

// function onBLEConnectionStateChange(callback) {
// 	uni.onBLEConnectionStateChange((res) => {
// 		callback(res)
// 	})
// }
export default {
	ScanDevice,
	CloseBle,
	StopScanDevice,
	closeBLEConnection,
	onBLEConnectionStateChange,
	getConnected,

};
