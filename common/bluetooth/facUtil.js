//适配各厂家SDK配置项，其中的数据顺序不能变
const config = [
	// 	{
	// 	svrName: 'GXETC_OBU',
	// 	facSdk: () => require("./JLObuSDK/JLObuSDK.js")

	// },
	{
		svrName: 'GXETC_ARTC',
		facSdk: () => require("./JTObuSDK/JTObuSDK.js")

	}, {
		svrName: 'Artc_Wechat',
		facSdk: () => require("./JTObuSDK/JTObuSDK.js")

	},
	// {
	// 	svrName: 'GXETC_GV',
	// 	facSdk: () => require("./JLObuSDK/JLObuSDK.js")

	// },
	{
		svrName: 'ETC_GX_GV',
		facSdk: () => require("./JTObuSDK/JTObuSDK.js")
	}
]; //厂家SDK封装

export const facapi = {
	//厂家SDK实例
	sdkConfig: () => require("./JTObuSDK/JTObuSDK.js"),
	facSdk: null,
	//根据设备名称适配厂家SDK
	initFacSdk: (deviceName, callback) => {
		let tmp = facapi.sdkConfig();
		// if (!deviceName) return null;
		// for (let i = 0; i < config.length; i++) {
		// 	if (deviceName.toUpperCase().indexOf(config[i].svrName.toUpperCase()) == 0) {
		// 		tmp = config[i].facSdk();
		// 		break;
		// 	}
		// }
		console.log('tmp', tmp);
		if (tmp) {
			facapi.facSdk = tmp;
		}
		callback(tmp)
	},
	//连接设备
	ConnectDevice: (device, conStateChange, conCallback) => {
		if (!device) {
			let result = {
				code: 1109,
				err_msg: "设备名称为空"
			};
			typeof conCallback === 'function' && conCallback(result);
		}

		facapi.initFacSdk(device.device_name, sdk => {
			if (sdk) {
				facapi.facSdk.ConnectDevice(device, conStateChange, conCallback);
			} else {
				let result = {
					code: 1110,
					err_msg: "未找到蓝牙OBU厂家SDK"
				};
				typeof conCallback === 'function' && conCallback(result);
			}
		});
	},
	//断开连接
	DisConnectDevice: function(_callback) {
		facapi.facSdk.DisconnectDevice(_callback);
	},
	//打开OBU通道
	OpenChannel: function(_callback) {
		facapi.facSdk.OpenChannel(_callback);
	},
	//设备SN号
	GetSerialNo: function(_callback) {
		facapi.facSdk.GetSerialNo(_callback);
	},
	//检查卡片
	CheckCardExists: function(_callback) {
		facapi.facSdk.CheckCardExists(_callback);
	},
	//打开卡片
	OpenCard: function(_callback) {
		facapi.facSdk.OpenCard(_callback);
	},
	//获取卡号
	GetCardNo: function(_callback) {
		facapi.facSdk.GetCardNo(_callback);
	},
	//获取0015文件
	GetCardFile15: function(_callback) {
		facapi.facSdk.GetCardFile15(_callback);
	},
	//获取0016文件
	GetCardFile16: function(_callback) {
		facapi.facSdk.GetCardFile16(_callback);
	},
	//获取自定义文件文件
	GetCardFileCustom: function(fileName, length, _callback) {
		// facapi.facSdk.GetCardFileCustom('0015', '50', function(code) {
		// 	console.log("返回数据：" + code.data);
		// })
		facapi.facSdk.GetCardFileCustom(fileName, length, _callback);
	},
	// 	getMac: function(rand) {
	// 		console.log("随机数:" + rand);
	// 		var devResult = new Object();
	// 		devResult.data = "00A40000023F00";
	//
	// 		return devResult;
	// 	},
	//
	// 	getMacc: function(rand, trade_no, mac1, balance) {
	// 		console.log(rand);
	// 		console.log(trade_no);
	// 		console.log(mac1);
	// 		console.log(balance);
	//
	// 		var devResult = new Object();
	// 		devResult.data = "00A40000021001";
	// 		return devResult;
	// 	},
	//写入0015文件
	SetCardFile0015: function(getMac, _callback) {
		facapi.facSdk.SetCardFile0015(getMac, _callback);
	},
	//写入0016文件
	SetCardFile0016: function(getMac, _callback) {
		facapi.facSdk.SetCardFile0016(getMac, _callback);
	},
	//写入自定义文件
	SetCardFileCustomer: function(getMac, _callback) {
		facapi.facSdk.SetCardFileCustomer(getMac, _callback);
	},
	//圈存
	InitLoad: function(pinCode, money, time, termino_no, getMac, _callback) {
		// facapi.facSdk.InitLoad(that.getMacc, '123456', '1', '20180611112020', '123456123456', function(code) {
		// 	console.log("返回数据：" + code.data);
		// })
		facapi.facSdk.InitLoad(pinCode, money, time, termino_no, getMac, _callback);
	},
	//获取异常处理卡mac1
	GetTradeNoAndMac1: function(pinCode, money, time, termino_no, getMac, _callback) {
		facapi.facSdk.GetTradeNoAndMac1(pinCode, money, time, termino_no, getMac, _callback);
	},
	//圈存
	GetCurBalanceAndTradeNo: function(pinCode, money, time, termino_no, _callback) {
		facapi.facSdk.GetCurBalanceAndTradeNo(pinCode, money, time, termino_no, _callback);
	},
	//获取系统信息
	GetSystemInfo: function(_callback) {
		facapi.facSdk.GetSystemInfo(_callback);
	},
	//获取车辆密文
	GetVehicleInfo: function(random, _callback) {
		facapi.facSdk.GetVehicleInfo(random, _callback);
	},
	//写系统信息
	SetSystemInfo: function(getMac, _callback) {
		facapi.facSdk.SetSystemInfo(getMac, _callback);
	},
	//写车辆信息
	SetVehicleInfo: function(getMac, _callback) {
		facapi.facSdk.SetVehicleInfo(getMac, _callback);
	},
	//激活
	Activate: function(getMac, _callback) {
		console.log('facapi.facSdk.Activate', getMac, _callback)
		facapi.facSdk.Activate(getMac, _callback);
	},
	//ic卡通道
	CpuCommand: function(command, _callback) {
		// facapi.facSdk.CpuCommand('00A40000021001', function(code) {
		// 	console.log("返回数据：" + code.data);
		// })
		console.log('facapi.facSdk.CpuCommand++', facapi.facSdk.CpuCommand)
		facapi.facSdk.CpuCommand(command, _callback);
	},
	GetBalance: function(_callback) {
		facapi.facSdk.GetBalance(_callback);
	},
	//OBU通道
	ObuCommand: function(command, _callback) {
		// facapi.facSdk.ObuCommand('00A40000023F00', function(code) {
		// 	console.log("返回数据：" + code.data);
		// })
		facapi.facSdk.ObuCommand(command, _callback);
	},
	//设备版本号
	GetVesionNo: function(_callback) {
		facapi.facSdk.GetVesionNo(_callback);
	},

	//获取卡内流水
	GetTradeList: function(_callback) {
		facapi.facSdk.GetTradeList(_callback);
	}
};