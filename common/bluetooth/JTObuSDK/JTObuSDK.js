var artcBleUtil = require('./artcBleUtil.js')
var gvSDK = require('./gvLoadSDK.js')
var artcDataUtil = require('./artcDataUtil.js')
var artcDataHandler = require('./artcDataHandler.js')

var gbk2 = require('./gbk2.js')
var BleError = require('./bleHelpErrorCode')
var sdkType = 'artc'

export function ConnectDevice(device, conStateChange, conCallback) {
	setTimeout(function() {
		let deviceName = device.localName || device.name
		deviceName = deviceName.toLocaleLowerCase()
		if (deviceName == 'wanji' || deviceName.startsWith('quipass') || deviceName.startsWith('gxetc-wd')) {
			gvSDK.connectBleDevice(device, conStateChange, (code) => {
				if (code == 0) {
					sdkType = 'gv'
				}
				conCallback && conCallback({
					code: code
				})
			})
		} else {
			console.log('艾特斯sdk连接设备=======开始')
			artcBleUtil.connectBleDevice(device, (code) => {
				console.log('艾特斯sdk连接设备=======ing')
				if (code == 0) {
					sdkType = 'artc'
					console.log('艾特斯sdk连接设备=======成功')
					conCallback && conCallback({
						code: code
					})
				} else {
					console.log('艾特斯sdk连接设备=======失败，开始使用金溢sdk连接')
					conCallback && conCallback({
						code: code
					})
				}
			})
		}
	}, 500);
}

export function DisconnectDevice(callback) {
	return new Promise((resolve, reject) => {
		if (sdkType == 'artc') {
			artcBleUtil.disconnectBleDevice((code) => {
				callback && callback(code)
			})
		} else if (sdkType == 'gv') {
			gvSDK.disconnectBleDevice(callback)
		}
	})
}

export function OpenChannel(callback) {
	let resultData = {
		code: 0,
		err_msg: "成功"
	};
	callback && callback(resultData)
}

export function GetSerialNo(callback) {
	GetSystemInfo(result => {
		if (result.code == 0) {
			callback && callback({
				code: result.code,
				data: result.data.serialNumber
			})
		} else {
			callback && callback(result)
		}
	})
}

export function checkCardExists(callback) {
	let resultData = {
		code: 0,
		err_msg: "成功"
	};
	callback && callback(resultData)
}

export function OpenCard(callback) {
	let resultData = {
		code: 0,
		err_msg: "成功"
	};
	callback && callback(resultData)
}

export function GetCardNo(callback) {

	GetCardFile15(result => {
		if (result.code == 0) {
			callback && callback({
				code: result.code,
				data: result.data.cardNo
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "卡片读取卡号失败"
			})
		}
	})
}

export function GetCardFile15(callback) {
	CpuCommand(["00A40000023F00"], result => {
		if (result.code == 0) {
			CpuCommand(["00A40000021001"], result => {
				if (result.code == 0) {
					CpuCommand(["00b095002b"], result => {
						if (result.code == 0) {
							let reapdu = artcDataHandler.resolveTLV(result.data.slice(10))
							console.log("0015 reapdu", reapdu)

							let f0015 = {
								cardType: hex2int(reapdu[0].substr(16, 2)),
								version: reapdu[0].substr(18, 2),
								cardNo: reapdu[0].substr(20, 20),
								startDate: reapdu[0].substr(40, 8),
								endDate: reapdu[0].substr(48, 8),
								licenceNo: convertLisenceNo(reapdu[0].substr(56, 24)),
								licenceColor: parseInt(reapdu[0].substr(18, 2)) >= 40 ?
									code2Color(reapdu[0].substr(82, 2)) : code2Color(reapdu[
										0].substr(84, 2)),
								carType: reapdu[0].substr(18, 2) >= 40 ? reapdu[0].substr(
									84, 2) : ''
							}
							console.log("0015", f0015)
							callback && callback({
								code: result.code,
								data: f0015
							})
						} else {
							callback && callback({
								code: result.code,
								err_msg: "读取0015文件失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "读取0015文件进入1001目录失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "读取0015文件进入3F00目录失败"
			})
		}
	})
}

export function GetCardFile16(callback) {
	CpuCommand(["00A40000023F00"], result => {
		if (result.code == 0) {
			CpuCommand(["00b0960037"], result => {
				if (result.code == 0) {
					let reapdu = artcDataHandler.resolveTLV(result.data.slice(10))

					console.log("0016 reapdu", reapdu)
					let f0016 = {
						userTag: reapdu[0].substr(0, 2),
						employeeTag: reapdu[0].substr(2, 2),
						userName: reapdu[0].substr(4, 40),
						certificateId: reapdu[0].substr(44, 64),
						certificateType: reapdu[0].substr(108, 2)
					}
					console.log("0016 f0016", f0016)
					callback && callback({
						code: result.code,
						data: f0016
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "卡读取0016文件失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "卡读取0016文件进入3F00目录失败"
			})
		}
	})
}

export function SetCardFile0015(getMac, callback) {
	GetCardNo(result => {
		if (result.code == 0) {
			CpuCommand(["0084000004"], result => {
				if (result.code == 0) {
					let rand = result.data.substr(18, 8)
					getMac && getMac(rand, result => {
						if (result.code == 0) {
							CpuCommand([result.data], result => {
								callback && callback(result)
							})
						} else {
							callback && callback({
								code: result.code,
								data: result.data,
								err_msg: "写卡0015文件取Mac2失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "写卡0015文件取随机数失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "写卡0015读取卡号失败"
			})
		}
	})
}

export function SetCardFile0016(getMac, callback) {
	GetCardNo(result => {
		if (result.code == 0) {
			CpuCommand(["00a40000023f00"], result => {
				if (result.code == 0) {
					let cosArr = ["0084000004"]
					CpuCommand(cosArr, result => {
						if (result.code == 0) {
							let rand = result.data.substr(18, 8)
							//取密钥
							getMac && getMac(rand, result => {
								if (result.code == 0) {
									CpuCommand([result.data], result => {
										callback && callback({
											code: result.code,
											data: result.data,
											err_msg: result.err_msg
										})
									})
								} else {
									callback && callback({
										code: result.code,
										err_msg: "写0016文件取Mac2失败"
									})
								}
							})
						} else {
							callback && callback({
								code: result.code,
								err_msg: "写0016文件取随机数失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "写0016文件进入3F00目录失败"
					})
				}
			})

		}
	})
}
export function InitLoad(pinCode, money, issue_times, terminalNo, getMac2, nextDevResult) {
	GetCardFile15(result => {
		if (result.code == 0) {
			let cpuInfo = result.data;
			//验证pin
			checkPinCode(pinCode, result => {
				if (result.code == 0) {
					let version = '01'
					if(cpuInfo.version && Number(cpuInfo.version)>50){
						version = '41'
					}
					let p = parseInt(money).toString(16);
					let hexMoney = new Array(8 - (p + '').length + 1).join('0') + p;
					//初始化圈存
					CpuCommand(['805000020B'+ version + hexMoney + terminalNo + '10'],
						result => {
							if (result.code == 0) {
								
								let reapdu = artcDataHandler.resolveTLV(result.data.slice(10));
								let temp = reapdu[0];
								let balance = temp.substr(0, 8)
								let tradeNo = temp.substr(8, 4)
								let rand = temp.substr(16, 8)
								let mac1 = temp.substr(24, 8)
								var keyVersion = temp.substr(12, 2); //交易密钥版本
								var algorithm = temp.substr(14, 2); //算法标识
								//获取mac2
								getMac2 && getMac2(rand, tradeNo,mac1, balance,keyVersion,algorithm,result => {
									//获取mac2成功
									if (result.code == 0) {
										//圈存
										CpuCommand(['805200000B' + issue_times + result
											.data
										], result => {
											if (result.code == 0) {
												nextDevResult && nextDevResult(
													result)
											} else {
												nextDevResult && nextDevResult({
													code: BleError
														.rechargeWriteCardError,
													err_msg: "初始化圈存失败"
												})
											}
										})
									} else {
										nextDevResult && nextDevResult({
											code: BleError.rechargeInitError,
											err_msg: "初始化圈存获取mac2失败"
										})
									}
								})
							} else {
								nextDevResult && nextDevResult({
									code: BleError.rechargeInitError,
									err_msg: "初始化圈存（初始化）失败"
								})
							}
						})
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin马校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}
function checkPinCode (pinCode,nextDevResult){
	CpuCommand(['0020000006313233343536'],result=>{
		if(result.code== 0){
			nextDevResult && nextDevResult(result)
		} else {
			CpuCommand(['0020000003888888'],result =>{
				nextDevResult && nextDevResult(result)
			})
		}
	})
}
/*
国密改造版本获取交易号
*/
// export function GetTradeNoAndMac1(pinCode, money, issue_times, terminalNo, getMac2, nextDevResult) {
// 	GetCardFile15(result => {
// 		if (result.code == 0) {
// 			let cpuInfo = result.data;
// 			//验证pin
// 			let _pinCode = '0020000006313233343536'
// 			CpuCommand([_pinCode], result => {
// 				if(result.code == 0){
// 					_pinCode = '0020000006313233343536'
// 				} else {
// 					_pinCode = '0020000003888888'
// 				}
// 				CpuCommand([_pinCode], result => {
// 					if (result.code == 0) {
// 						let p = parseInt(money).toString(16);
// 						let hexMoney = new Array(8 - (p + '').length + 1).join('0') + p;
// 						//初始化圈存
// 						let version = '01'
// 						if(cpuInfo.version && Number(cpuInfo.version)>50){
// 							version = '41'
// 						}
// 						CpuCommand(['805000020B'+ version + hexMoney + terminalNo + '10'],
// 							result => {
// 								if (result.code == 0) {
// 									let balance = result.data.substr(18, 8)
// 									let tradeNo = result.data.substr(26, 4)
// 									let rand = result.data.substr(34, 8)
// 									let mac1 = result.data.substr(42, 8)
// 									//获取mac2
// 									getMac2 && getMac2(rand, tradeNo, mac1, balance, result => {
// 										//获取mac2成功
// 										if (result.code == 0) {
// 											nextDevResult && nextDevResult(
// 												result)
// 										} else {
// 											nextDevResult && nextDevResult({
// 												code: BleError.rechargeInitError,
// 												err_msg: "初始化圈存获取mac2失败"
// 											})
// 										}
// 									})
// 								} else {
// 									nextDevResult && nextDevResult({
// 										code: BleError.rechargeInitError,
// 										err_msg: "初始化圈存（初始化）失败"
// 									})
// 								}
// 							})
// 					} else {
// 						nextDevResult && nextDevResult({
// 							code: BleError.pinError,
// 							err_msg: "pin马校验失败"
// 						})
// 					}
// 				})
// 			})
			
// 		} else {
// 			nextDevResult && nextDevResult({
// 				code: BleError.checkCardNo,
// 				err_msg: "卡号读取失败"
// 			})
// 		}
// 	})
// }
export function GetTradeNoAndMac1(pinCode, money, issue_times, terminalNo, getMac2, nextDevResult) {
	GetCardFile15(result => {
		
		if (result.code == 0) {
			let cpuInfo = result.data;
			//验证pin
			checkPinCode(pinCode, result => {
				if (result.code == 0) {
					let p = parseInt(money).toString(16);
					let hexMoney = new Array(8 - (p + '').length + 1).join('0') + p;
					//初始化圈存
					let version = '01'
					if(cpuInfo.version && Number(cpuInfo.version)>50){
						version = '41'
					}
					CpuCommand(['805000020B'+ version + hexMoney + terminalNo + '10'],
						result => {
							if (result.code == 0) {
								let balance = result.data.substr(18, 8)
								let tradeNo = result.data.substr(26, 4)
								let rand = result.data.substr(34, 8)
								let mac1 = result.data.substr(42, 8)
								//获取mac2
								getMac2 && getMac2(rand, tradeNo, mac1, balance, result => {
									//获取mac2成功
									if (result.code == 0) {
										// //圈存
										// CpuCommand(['805200000B' + issue_times + result
										// 	.data
										// ], result => {
										// 	if (result.code == 0) {
										nextDevResult && nextDevResult(
											result)
										// 	} else {
										// 		nextDevResult && nextDevResult({
										// 			code: BleError
										// 				.rechargeWriteCardError,
										// 			err_msg: "初始化圈存失败"
										// 		})
										// 	}
										// })
									} else {
										nextDevResult && nextDevResult({
											code: BleError.rechargeInitError,
											err_msg: "初始化圈存获取mac2失败"
										})
									}
								})
							} else {
								nextDevResult && nextDevResult({
									code: BleError.rechargeInitError,
									err_msg: "初始化圈存（初始化）失败"
								})
							}
						})
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin码校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}
export function GetCurBalanceAndTradeNo(pinCode, money, issue_times, terminalNo, nextDevResult) {
	GetCardFile15(result => {
		if (result.code == 0) {
			let cpuInfo = result.data;
			//验证pin
			checkPinCode(pinCode, result => {
				if (result.code == 0) {
					let p = parseInt(money).toString(16);
					let hexMoney = new Array(8 - (p + '').length + 1).join('0') + p;
					//初始化圈存
					let version = '01'
					if(cpuInfo.version && Number(cpuInfo.version)>50){
						version = '41'
					}
					//初始化圈存
					CpuCommand(['805000020B'+ version + hexMoney + terminalNo + '10'],
						result => {
							if (result.code == 0) {
								let balance = result.data.substr(18, 8)
								let tradeNo = result.data.substr(26, 4)
								let rand = result.data.substr(34, 8)
								let mac1 = result.data.substr(42, 8)
								nextDevResult && nextDevResult({
									data: {
										balance: balance,
										tradeNo: tradeNo
									},
									code: result.code
								})
							} else {
								nextDevResult && nextDevResult({
									code: BleError.rechargeInitError,
									err_msg: "圈存（初始化）失败"
								})
							}
						})
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin马校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}
export function GetSystemInfo(callback) {
	ObuCommand(['00A40000023F00'], result => {
		if (result.code == 0) {
			ObuCommand(['00A4000002EF01'], result => {
				if (result.code == 0) {
					ObuCommand(['00B0810063'], result => {
						let {
							code,
							data,
							err_msg
						} = result
						if (code == 0) {
							let systemInfo = {
								provider: hexStr2GBKStr(data.substr(26, 8)),
								contractType: data.substr(34, 2),
								version: data.substr(36, 2),
								serialNumber: data.substr(38, 16),
								signedDate: data.substr(54, 8),
								expiredDate: data.substr(62, 8),
								flag: data.substr(70, 2)
							}
							console.log("OBU系统信息", systemInfo)
							callback && callback({
								code: code,
								data: systemInfo
							})
						} else {
							callback && callback({
								code: code,
								err_msg: "读OBU系统信息失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "进入EF01目录失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "进入3F00目录失败"
			})
		}
	})
}

export function GetVehicleInfo(decrypt, callback) {
	ObuCommand(['00A40000023F00'], result => {
		if (result.code == 0) {
			ObuCommand(['00A4000002DF01'], result => {
				if (result.code == 0) {
					ObuCommand(['00B400000A00000000000000003B00'], result => {
						if (result.code == 0) {
							let audp = artcDataHandler.resolveTLV(result.data.slice(10))
							decrypt && decrypt(audp[0].slice(0, -4), result => {
								if (result.code == 0) {
									let message = result.data
									let vehicleInfo = {
										licencePlateNumber: convertLisenceNo(message
											.substr(0, 24)),
										licencePlateColor: code2Color(message
											.substr(26, 2)),
										vehicleClass: message.substr(28, 2),
										userType: message.substr(30, 2),
										length: message.substr(32, 4),
										width: message.substr(36, 2),
										heigth: message.substr(38, 2),
										wheelNum: message.substr(40, 2),
										axleNum: message.substr(42, 2),
										wheelBases: message.substr(44, 4),
										vehicleCapacity: message.substr(48, 6),
										specificInfomation: hexStr2GBKStr(message
											.substr(54, 32)),
										engineNumber: hexStr2GBKStr(message.substr(
											86, 32)),
									}
									console.log("解密后OBU车辆信息", vehicleInfo)
									callback && callback({
										code: result.code,
										data: vehicleInfo
									})
								} else {
									callback && callback({
										code: result.code,
										err_msg: "OBU车辆信息解密失败"
									})
								}
							})

						} else {
							callback && callback({
								code: result.code,
								err_msg: "读OBU车辆信息失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "进入EF01目录失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "进入3F00目录失败"
			})
		}
	})
}

export function SetSystemInfo(getMac, callback) {
	ObuCommand(["00A40000023F00"], result => {
		if (result.code == 0) {
			ObuCommand(["0084000004"], result => {
				console.log(result, '写系统信息')
				if (result.code == 0) {

					let data = result.data;
					let rand = data.substr(18, 8)
					getMac && getMac(rand, result => {
						if (result.code == 0) {
							//写系统信息
							ObuCommand([result.data], result => {
								callback && callback(result)
							})
						} else {
							callback && callback({
								code: result.code,
								err_msg: "写系统信息取系统信息mac2失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "写系统信息取随机数失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "写系统信息进入3F00失败"
			})
		}
	})
}

export function SetVehicleInfo(getMac, callback) {
	ObuCommand(["00A4000002DF01"], result => {
		if (result.code == 0) {
			ObuCommand(["0084000004"], result => {
				if (result.code == 0) {
					let data = result.data;
					let rand = data.substr(18, 8)
					getMac && getMac(rand, result => {
						if (result.code == 0) {
							//写系统信息
							ObuCommand([result.data], result => {
								callback && callback(result)
							})
						} else {
							callback && callback({
								code: result.code,
								err_msg: "写车辆信息取系统信息mac2失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "写车辆信息取随机数失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "写车辆信息进入EF01失败"
			})
		}
	})
}

export function Activate(getMac, callback) {

	ObuCommand(["00a40000023f00", "00B0810058", "0084000004"], result => {
		if (result.code == 0) {
			let data = result.data;
			let reapdu = artcDataHandler.resolveTLV(data.slice(10))
			//reapdu元素顺序对于指令的处理结果
			console.log('进入3F00目录 获取随机数tlv', reapdu, reapdu[2].substr(0, 8))
			let rand = reapdu[2].substr(0, 8)
			getMac && getMac(rand, result => {
				if (result.code == 0) {
					ObuCommand(["04d6811a0501" + result.data], result => {
						if (result.code == 0) {
							callback && callback(result)
						} else {
							callback && callback({
								code: result.code,
								err_msg: "OBU激活写设备失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "OBU激活获取Mac2失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "OBU激活进入3F00目录失败或获取随机数失败"
			})
		}
	})
}

//获取卡内流水
//卡内交易列表
var tradeList = new Array()
//交易记录查询回调
var tradeCallback

var tradeIndex = 1
var tradeStr
export function GetTradeList(callback) {
	tradeCallback = callback
	GetCardNo(result => {
		if (result.code == 0) {
			//验证pin
			CpuCommand(["0020000006313233343536"], result => {
				if (result.code == 0) {
					tradeIndex = 1
					tradeStr = ''
					tradeList = new Array()
					RunGetTradList()
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}

function RunGetTradList() {
	let p = parseInt(tradeIndex).toString(16);
	let itemIndex = new Array(2 - (p + '').length + 1).join('0') + p;
	CpuCommand(["00b2" + itemIndex + "c417"], result => {
		if (result.code == '0' || result.code == '6a83' || result.code == '6A83') {
			// let realData = result.data
			let reapdu = artcDataHandler.resolveTLV(result.data.slice(10))
			let realData = reapdu[0]
			let tradeInfo = {
				tradeNo: hex2int(realData.substr(0, 4)),
				overDraft: realData.substr(4, 6),
				tradeMoney: hex2int(realData.substr(10, 8)),
				tradeType: realData.substr(18, 2),
				termId: realData.substr(20, 12),
				date: realData.substr(32, 8),
				time: realData.substr(40, 6)
			}
			console.log(realData, '00000000000000');
			if (result.code == 0 && tradeInfo.tradeType != 'ff' && tradeInfo.tradeType != 'FF' && tradeInfo
				.tradeType != '00') {
				console.log(tradeInfo, '流水信息')
				tradeList.push(tradeInfo)
				console.log(tradeList);
				tradeIndex++
				RunGetTradList()
			} else {
				if (tradeList.length == 0) {
					tradeCallback && tradeCallback({
						code: 0,
						data: "0|0"
					})
				} else {
					console.log(tradeList, '11111111111111')
					tradeStr = "0|" + tradeList.length
					for (let i = 0; i < tradeList.length; i++) {
						let item = tradeList[i]
						tradeStr += "|" + item.tradeNo +
							"|" + item.overDraft +
							"|" + item.tradeMoney +
							"|" + item.tradeType +
							"|" + item.termId +
							"|" + item.date +
							"|" + item.time
					}
					tradeCallback && tradeCallback({
						code: 0,
						data: tradeStr
					})
				}
			}
		} else {
			tradeCallback && tradeCallback({
				code: result.code,
				data: tradeStr,
				err_msg: "获取流水失败"
			})
		}
	})
}

function hexStr2IntStr(hexStr) {
	let intStr = ""
	for (let i = 0; i < parseInt(hexStr.length / 2); i++) {
		let bit = parseInt(hexStr.slice(i * 2, (i + 1) * 2), 16)
		let num = +(bit ^ 0x30)
		intStr += num
	}
	return intStr
}

function hex2int(hex) {
	var len = hex.length,
		a = new Array(len),
		code;
	for (var i = 0; i < len; i++) {
		code = hex.charCodeAt(i);
		if (48 <= code && code < 58) {
			code -= 48;
		} else {
			code = (code & 0xdf) - 65 + 10;
		}
		a[i] = code;
	}

	return a.reduce(function(acc, c) {
		acc = 16 * acc + c;
		return acc;
	}, 0);
}

function hexStr2GBKStr(hexStr) {
	return gbk2.decodeFromGb2312(hexStr)
}

function convertLisenceNo(carnocode) {
	carnocode = carnocode.toUpperCase()
	console.log('carnocode==>>', carnocode)
	var realCarno = ''

	for (var i = 0; i < carnocode.length;) {
		realCarno = realCarno + '%' + carnocode.substr(i, 2)
		i = i + 2
	}
	console.log('realCarno==>>', realCarno)
	var carno = gbk2.decodeFromGb2312(realCarno) + '';

	return carno.replace(/[^\u4e00-\u9fa5a-zA-Z\d]/g, '')
}

function code2Color(code) {
	switch (code) {
		case '00':
			return "0"
		case '01':
			return "1"
		case '02':
			return "2"
		case '03':
			return "3"
		case '04':
			return "4"
		case '05':
			return "5"
		case '06':
			return "6"
	}
}
export function GetBalance(callback) {
	CpuCommand(["805c000204"], result => {
		if (result.code == 0) {
			console.log('获取卡余额返回', result);
			let balance = result.data.substr(18, 8);
			callback && callback({
				code: result.code,
				data: balance
			})
		} else {
			callback && callback(result)
		}
	})
}
/**
 * 写卡指令
 * @param cosArr 写卡指令，可以是
 * @param callback 回调
 * @param stype 00明文  01密文
 */
export function CpuCommand(arr, callback, stype = '00') {
	let cosArr = arr
	if (!Array.isArray(cosArr))
		cosArr = [arr]
	console.log('卡通道指令发送', cosArr)
	let tlv = artcDataHandler.makeTLV(cosArr)

	let sendData = sdkType == 'artc' ? artcDataUtil.makeA3SendData(stype, tlv) : tlv

	let sdk = sdkType == 'artc' ? artcBleUtil : sdkType == 'gv' ? gvSDK : artcBleUtil
	console.log(sdkType, sdk, sendData)

	sdk.sendDataToDevice(sendData, (code, data, msg) => {
		console.log('卡通道指令接收', code, data, msg)
		if (code == 0) {
			let cosResult = data.substr(-4, 4)

			if (data.slice(2, 4) == "00" && cosResult == '9000') {
				callback && callback({
					code: code,
					data: data,
					err_msg: msg
				})
			} else {
				callback && callback({
					code: cosResult,
					data: data,
					err_msg: msg
				})
			}
		} else {
			callback && callback({
				code: BleError.writeError,
				data: data,
				err_msg: msg
			})
		}
	})
}

/**
 * 写OBU指令
 * @param cosArr 写OBU指令
 * @param callback 回调
 * @param stype 00明文  01密文
 */
function ObuCommand(cosArr, callback, stype = '00') {
	console.log('OBU通道指令发送', cosArr)
	let tlv = artcDataHandler.makeTLV(cosArr)
	artcBleUtil.sendDataToDevice(artcDataUtil.makeA8SendData(stype, tlv), (code, data, msg) => {
		console.log('OBU通道指令接收', code, data, msg)
		if (code == '0') {
			let cosResult = data.substr(-4, 4)
			let sl = data.slice(2, 4)
			let subs = data.substr(2, 2)
			console.log(cosResult, sl, subs)
			if (data.slice(2, 4) === "00" && cosResult == '9000') {
				callback && callback({
					code: code,
					data: data,
					err_msg: msg
				})
			} else {
				callback && callback({
					code: cosResult,
					data: data,
					err_msg: msg
				})
			}
		} else {
			callback && callback({
				code: BleError.obuWriteError,
				data: data,
				err_msg: `写OBU 指令发送失败${code}`
			})
		}
	})
}