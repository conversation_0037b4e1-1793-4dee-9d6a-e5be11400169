const dataHandler = require("./artcDataHandler.js")
const dataUtil = require("./artcDataUtil.js")
module.exports = {
    sendDataToDevice: sendDataToDevice,
    disconnectBleDevice: disconnectBleDevice,
    connectBleDevice: connectBleDevice,
    scanBleDevice: scanBleDevice
}
const codeEnum = {
    successCode: 0,
    failureCode: 1,
    stopScanFailure: 2,
    creatConnectionFailure: 3,
    getServiceFailure: 4,
    noTargetServiceId: 5,
    getCharacteristicsFailure: 6,
    noTargetCharacteristic: 7,
    monitorNotificationFailure: 8,
    authResponseFailure: 9,
    initResponseFailure: 10,
    initDeviceFailure: 11,
    dataFrameTransboundary: 12,
    notPassBccCheck: 13,
    sendDataFailure: 14,
    timeout: 100
}
const FUNCTION = "function";
const serviceUUID = "0000FEE7-0000-1000-8000-00805F9B34FB";
const writeUUID = "0000FEC7-0000-1000-8000-00805F9B34FB";
const readUUID = "0000FEC8-0000-1000-8000-00805F9B34FB";
const connectTime = (20 * 1000);
const startA2Time = 2000;
const waitA2ResponseTime = 2000;
const restrictionNameOne = "GXETC_ARTC";
const restrictionNameTwo = "Artc_Wechat";
var haveFoundDevice = new Array();
var connectDeviceId;
var connectCallback;
var connectTimer;
var startA2Timer;
var waitA2ResponseTimer;
var frame = "";
var frameLen = 0;
var frames = new Array();
var framesLen = 0;
var dataHandlerCallback;
var sendBufferArray = new Array();
var sendIndex;
var resendCount = 3;
var connectDevice = null;

// function scanBleDevice(ncZrHVo1) {
//   haveFoundDevice = new Array();
//   wx['startBluetoothDevicesDiscovery']({
//     // services: ["FEE7", "0000FEE7-0000-1000-8000-00805F9B34FB"],
//     allowDuplicatesKey: true,
//     success: function (zL_n2) {
//       wx['onBluetoothDeviceFound'](function (iYIkI3) {
//         for (let i = 0; i < iYIkI3['devices']['length']; i++) {
//           let device = iYIkI3['devices'][i];
//           let ishave = false;
//           for (let j = 0; j < haveFoundDevice['length']; j++) {
//             let foundDevice = haveFoundDevice[i];
//             if (device['deviceId'] == foundDevice['deviceId']) {
//               ishave == true;
//               break
//             }
//           }
//           if (ishave == false) {
//             haveFoundDevice['push'](device);
//             let isRecur = false;
//             let lastDevice = haveFoundDevice[haveFoundDevice['length'] - 1];
//             let lastDeviceId = lastDevice['deviceId'];
//             for (let j = 0; j < haveFoundDevice['length'] - 1; j++) {
//               let foundDevice = haveFoundDevice[i];
//               let foundDeviceId = foundDevice['deviceId'];
//               if (lastDeviceId == foundDeviceId) {
//                 isRecur = true
//               }
//             }
//             if (isRecur == false) {
//               typeof ncZrHVo1 == FUNCTION && ncZrHVo1(codeEnum['successCode'], device, "新设备")
//             } else {
//               haveFoundDevice['pop']()
//             }
//           }
//         }
//       })
//     },
//     fail: function () {
//       typeof ncZrHVo1 == FUNCTION && ncZrHVo1(codeEnum['failureCode'], null, "搜索失败")
//     }
//   })
// }

function scanBleDevice(ncZrHVo1) {
    haveFoundDevice = new Array();
    wx['startBluetoothDevicesDiscovery']({
        // services: ["FEE7"],
        allowDuplicatesKey: true,
        success: function (zL_n2) {
            // console.log('搜索到的设备', zL_n2)
            wx['onBluetoothDeviceFound'](function (iYIkI3) {
                console.log('搜索到的设备', iYIkI3)
                if (iYIkI3.devices[0].advertisServiceUUIDs && (iYIkI3.devices[0].advertisServiceUUIDs[0] == '0000FEE7-0000-1000-8000-00805F9B34FB' || iYIkI3.devices[0].advertisServiceUUIDs[0] == 'FEE7')) { // 各种设备兼容，手动过滤

                    for (let i = 0; i < iYIkI3['devices']['length']; i++) {
                        let device = iYIkI3['devices'][i];
                        let ishave = false;
                        for (let j = 0; j < haveFoundDevice['length']; j++) {
                            let foundDevice = haveFoundDevice[i];
                            if (device['deviceId'] == foundDevice['deviceId']) {
                                ishave == true;
                                break
                            }
                        }
                        if (ishave == false) {
                            haveFoundDevice['push'](device);
                            let isRecur = false;
                            let lastDevice = haveFoundDevice[haveFoundDevice['length'] - 1];
                            let lastDeviceId = lastDevice['deviceId'];
                            for (let j = 0; j < haveFoundDevice['length'] - 1; j++) {
                                let foundDevice = haveFoundDevice[i];
                                let foundDeviceId = foundDevice['deviceId'];
                                if (lastDeviceId == foundDeviceId) {
                                    isRecur = true
                                }
                            }
                            if (isRecur == false) {
                                typeof ncZrHVo1 == FUNCTION && ncZrHVo1(codeEnum['successCode'], device, "新设备")
                            } else {
                                haveFoundDevice['pop']()
                            }
                        }
                    }
                }
            })
        },
        fail: function () {
            typeof ncZrHVo1 == FUNCTION && ncZrHVo1(codeEnum['failureCode'], null, "搜索失败")
        }
    })
}

function disconnectBleDevice(GI1) {
    if (connectDeviceId != null) {
        wx['closeBLEConnection']({
            deviceId: connectDeviceId,
            success: function (cQlMv2) {
                typeof GI1 == FUNCTION && GI1(0)
            },
            fail: function () {
                typeof GI1 == FUNCTION && GI1(1)
            }
        })
    }
}

function connectBleDevice(device, conCallback) {
    // let name = device['name'];
    // if (name['length'] == 0) {
    //     name = device['localName']
    // } else {
    //     name = device['name']
    // }
    // if (true || (name === restrictionNameOne && name === "GXETC_ARTC") || (name === restrictionNameTwo && name === "Artc_Wechat")) {
        connectDeviceId = device['deviceId'];
        connectDevice = device
        connectCallback = conCallback;
        if (typeof connectTimer != undefined) {
            clearTimeout(connectTimer)
        }
        if (typeof startA2Timer != undefined) {
            clearTimeout(startA2Timer)
        }
        if (typeof waitA2ResponseTimer != undefined) {
            clearTimeout(waitA2ResponseTimer)
        }
        connectTimer = setTimeout(() => {
            typeof connectCallback == FUNCTION && connectCallback(codeEnum['timeout'])
        }, connectTime);
        wx['stopBluetoothDevicesDiscovery']({
            success: function (Oz3) {
                startConnectBle()
            },
            fail: function () {
                if (typeof connectTimer != undefined) {
                    clearTimeout(connectTimer)
                }
                typeof connectCallback == FUNCTION && connectCallback(codeEnum['stopScanFailure'])
            }
        })
    // } else {
    //     typeof l2 == FUNCTION && l2(codeEnum['failureCode'])
    // }
}

function startConnectBle() {
    wx['createBLEConnection']({
        deviceId: connectDeviceId,
        success: function (cjjKNR1) {
            startGetAndCheckService()
        },
        fail: function () {
            if (typeof connectTimer != undefined) {
                clearTimeout(connectTimer)
            }
            typeof connectCallback == FUNCTION && connectCallback(codeEnum['creatConnectionFailure'])
        }
    })
}

function startGetAndCheckService() {
    wx['getBLEDeviceServices']({
        deviceId: connectDeviceId,
        success: function (aLZKtMs1) {
            let haseTargetServiceId = false;
            for (let i = 0; i < aLZKtMs1['services']['length']; i++) {
                let itemServiceId = aLZKtMs1['services'][i]['uuid'];
                if (itemServiceId == serviceUUID) {
                    haseTargetServiceId = true;
                    break
                }
            }
            if (haseTargetServiceId == true) {
                startGetAndCheckCharacterisitc()
            } else {
                if (typeof connectTimer != undefined) {
                    clearTimeout(connectTimer)
                }
                typeof connectCallback == FUNCTION && connectCallback(codeEnum['noTargetServiceId'])
            }
        },
        fail: function () {
            if (typeof connectTimer != undefined) {
                clearTimeout(connectTimer)
            }
            typeof connectCallback == FUNCTION && connectCallback(codeEnum['getServiceFailure'])
        }
    })
}

function startGetAndCheckCharacterisitc() {
    wx['getBLEDeviceCharacteristics']({
        deviceId: connectDeviceId,
        serviceId: serviceUUID,
        success: function (WXqgSAD1) {
            let haveRead = false;
            let haveWrite = false;
            for (let i = 0; i < WXqgSAD1['characteristics']['length']; i++) {
                let itemUUID = WXqgSAD1['characteristics'][i]['uuid'];
                if (itemUUID == readUUID) {
                    haveRead = true
                } else if (itemUUID == writeUUID) {
                    haveWrite = true
                }
                if (haveRead == true && haveWrite == true) {
                    break
                }
            }
            if (haveRead == true && haveWrite == true) {
                monitorNotification()
            } else {
                if (typeof connectTimer != undefined) {
                    clearTimeout(connectTimer)
                }
                typeof connectCallback == FUNCTION && connectCallback(codeEnum['noTargetCharacteristic'])
            }
        },
        fail: function () {
            if (typeof connectTimer != undefined) {
                clearTimeout(connectTimer)
            }
            typeof connectCallback == FUNCTION && connectCallback(codeEnum['getCharacteristicsFailure'])
        }
    })
}

function monitorNotification() {
    wx['notifyBLECharacteristicValueChange']({
        deviceId: connectDeviceId,
        serviceId: serviceUUID,
        characteristicId: readUUID,
        state: true,
        success: function (qD1) {
        },
        fail: function () {
            if (typeof connectTimer != undefined) {
                clearTimeout(connectTimer)
            }
            typeof connectCallback == FUNCTION && connectCallback(codeEnum['monitorNotificationFailure'])
        }
    });
    wx['onBLECharacteristicValueChange'](function (ffyevz2) {
        if (ffyevz2['deviceId'] = connectDeviceId && ffyevz2['serviceId'] == serviceUUID && ffyevz2['characteristicId'] == readUUID) {
            analyticData(ffyevz2['value'])
        }
    })
}

function analyticData(klrLzc1) {
    let data = dataHandler['bufferArrayToHexString'](klrLzc1);
    console['log']("接收：" + data);
    if (frame['length'] == 0) {
        frameLen = parseInt(data['slice'](4, 8), 16) * 2
    }
    frame += data;
    if (frame['length'] > frameLen) {
        frame = "";
        frameLen = 0;
        typeof dataHandlerCallback == FUNCTION && dataHandlerCallback(codeEnum['dataFrameTransboundary'], "", "数据长度越界")
    } else if (frame['length'] == frameLen) {
        let bCmdId = frame['slice'](8, 12);
        console.log('bCmdId', bCmdId)
        if (bCmdId === "2711") {
            frame = "";
            frameLen = 0;
            sendDataToDevice(dataUtil['makeAuthResponse'](), (code, data, msg) => {
                if (code != 0) {
                    if (typeof connectTimer != undefined) {
                        clearTimeout(connectTimer)
                    }
                    typeof connectCallback == FUNCTION && connectCallback(codeEnum['authResponseFailure'])
                }
            })
        } else if (bCmdId === "2713") {
            frame = "";
            frameLen = 0;
            sendDataToDevice(dataUtil['makeInitResponse'](), (code, data, msg) => {
                if (code != 0) {
                    if (typeof connectTimer != undefined) {
                        clearTimeout(connectTimer)
                    }
                    if (typeof startA2Timer != undefined) {
                        clearTimeout(startA2Timer)
                    }
                    typeof connectCallback == FUNCTION && connectCallback(codeEnum['initResponseFailure'])
                }
            });
            startA2Timer = setTimeout(() => {
                initDevice();
                waitA2ResponseTimer = setTimeout(() => {
                    initDevice()
                }, waitA2ResponseTime)
            }, startA2Time)
        } else if (bCmdId === "2712") {
            let outWechatFrame = frame['slice'](16);
            let outProtoFrame = outWechatFrame['slice'](8, -6);
            frame = "";
            frameLen = 0;
            if (frames['length'] == 0) {
                let ctl = parseInt(outProtoFrame['slice'](4, 6), 16);
                framesLen = ctl - 0x80 + 1
            }
            frames['push'](outProtoFrame);
            if (frames['length'] == framesLen) {
                let allPaseBCC = true;
                for (let i = 0; i < frames['length']; i++) {
                    let itemFrame = frames[i];
                    let bcc = 0;
                    for (let j = 1; j < parseInt(itemFrame['length'] / 2) - 1; j++) {
                        let bit = parseInt(itemFrame['slice'](j * 2, (j + 1) * 2), 16);
                        bcc ^= bit
                    }
                    if (bcc != parseInt(itemFrame['slice'](-2), 16)) {
                        allPaseBCC = false;
                        break
                    }
                }
                if (allPaseBCC == false) {
                    frames = new Array();
                    framesLen = 0;
                    typeof dataHandlerCallback == FUNCTION && dataHandlerCallback(codeEnum['notPassBccCheck'], "", "bcc校验不通过")
                } else {
                    let completeData = "";
                    for (let i = 0; i < frames['length']; i++) {
                        let itemFrame = frames[i];
                        completeData += itemFrame['slice'](8, -2)
                    }
                    frames = new Array();
                    framesLen = 0;
                    typeof dataHandlerCallback == FUNCTION && dataHandlerCallback(codeEnum['successCode'], completeData, "成功")
                }
            }
        } else {
            console['log']("奇异数据");
            frame = "";
            frameLen = 0
        }
    }
}

function initDevice() {
    sendDataToDevice(dataUtil['makeA2SendData'](), (code, data, msg) => {
        if (typeof connectTimer != undefined) {
            clearTimeout(connectTimer)
        }
        if (typeof waitA2ResponseTimer != undefined) {
            clearTimeout(waitA2ResponseTimer)
        }
        if (code === 0 && data['slice'](2, 4) === "00") {
            activeDevice()
        } else {
            typeof connectCallback == FUNCTION && connectCallback(codeEnum['initDeviceFailure'])
        }
    })
}

function activeDevice() {
    sendDataToDevice(dataUtil['makeA5SendData']("c0"), (code, data, msg) => {
        if (code === 0 && data['slice'](2, 4) === "00") {
            let idInfo = data['slice'](8);
            let obuId = "";
            for (let i = 0; i < parseInt(idInfo['length'] / 2); i++) {
                let bit = parseInt(idInfo['slice'](i * 2, (i + 1) * 2), 16);
                let num = +(bit ^ 0x30);
                obuId += num
            }
            console.log('obuid', obuId, data, code)
            // if (obuId['slice'](6, 7) === "1") {
            if (code === 0 && data['slice'](2, 4) === "00") {
                typeof connectCallback == FUNCTION && connectCallback(codeEnum['successCode'])
            } else {
                wx['closeBLEConnection']({
                    deviceId: connectDeviceId,
                    success: function (kHpzRa1) {
                    },
                });
                typeof connectCallback == FUNCTION && connectCallback(codeEnum['failureCode'])
            }
        } else {
            wx['closeBLEConnection']({
                deviceId: connectDeviceId,
                success: function (TAkg2) {
                },
            });
            typeof connectCallback == FUNCTION && connectCallback(codeEnum['failureCode'])
        }
    })
}

function sendDataToDevice(ir_zSF1, JN2) {
    dataHandlerCallback = JN2;
    sendBufferArray = new Array()['concat'](ir_zSF1);
    sendIndex = 0;
    frame = "";
    frameLen = 0;
    frames = new Array();
    framesLen = 0;
    runningSendData()
}

function runningSendData() {
    let value = sendBufferArray[sendIndex];
    let hex = dataHandler['bufferArrayToHexString'](value);
    console['log']("发送：" + hex);
    wx['writeBLECharacteristicValue']({
        deviceId: connectDeviceId,
        serviceId: serviceUUID,
        characteristicId: writeUUID,
        value: value,
        success: function (hF1) {
            sendIndex++;
            resendCount = 3;
            if (sendIndex < sendBufferArray['length']) {
                runningSendData()
            }
        },
        fail: function () {
            if (resendCount > 0) {
                resendCount--;
                setTimeout(() => {
                    console['log']("第" + (3 - resendCount) + "次重发");
                    runningSendData()
                }, 200)
            } else {
                typeof dataHandlerCallback == FUNCTION && dataHandlerCallback(codeEnum['sendDataFailure'], "", "发送数据失败")
            }
        }
    })
}
