const dataHandler = require("./artcDataHandler.js");
module.exports = {
  makeA2SendData: makeA2SendData,
  makeA3SendData: makeA3SendData,
  makeA4SendData: makeA4SendData,
  makeA5SendData: makeA5SendData,
  makeA6SendData: makeA6SendData,
  makeA7SendData: makeA7SendData,
  makeA8SendData: makeA8SendData,
  makeABSendData: makeABSendData,
  makeAuthResponse: makeAuthResponse,
  makeInitResponse: makeInitResponse
}
/**
 * 接口使用说明：
 *  makeA2SendData：
      app和蓝牙盒子建立握手
 *  makeA3SendData：
      PICC通道，参数传入：
        dataType:"00"=明文，"01"=密文
        cos指令：TLV格式(最大长度不超384)
 *  makeA4SendData：
      SE指令通道，参数传入：
        dataType:"00"=明文，"01"=密文
        cos指令：TLV格式(最大长度不超384)
 *  makeA5SendData：
      蓝牙盒子通道，参数传入：
        cmd：指令说明：
          "c0"=获取蓝牙盒子设备编号
          "c1"=获取蓝牙盒子版本号
          "c2"=获取蓝牙盒子电池电量
          "c3"=强制蓝牙盒子断电
          "c4"=对蓝牙盒子超时计数器清零
          "c5"=卡片物理编号
          "c6"=获取obu mac
 *  makeA6SendData:
      认证通道，参数传入：
        cmd：指令说明：
          "c0"=认证指令1
          "c1"+渠道证书号+渠道证书+Rnd2=认证指令2：渠道证书号（1bytes），0x01表示渠道证书1（001C文件），0x02表示渠道证书2（001D文件），0x03表示渠道证书3（001E文件）。渠道证书为证书明文（xx bytes）。Rnd2为随机数，长度为32bytes(其中前8字节为UTC时间) 。
          "c2"+F1信息=认证指令3：F1:20bytes
          "c3"+渠道证书号+Rnd2=认证指令4：渠道证书号（1bytes），0x01表示渠道证书1（001C文件），0x02表示渠道证书2（001D文件），0x03表示渠道证书3（001E文件）。Rnd2为随机数，长度为32bytes(其中前8字节为UTC时间) 。
          "c4"=新版认证指令1(国密)
          "c5"+工作密钥密文+工作密钥校验值（8字节）+MAC密钥密文+MAC密钥校验值（8字节）+Rnd2（16字节）+S2=新版认证指令2(国密)S2: 工作密钥密文 + 工作密钥校验码 + MAC密钥密文 + MAC密钥校验码 + 随机数的签名值（服务器私钥签名）注：设备端由SE完成验签、密钥校验、密钥解密操作
 *  makeA7SendData:
      证书更新通道，参数传入：
      cmd：指令说明：
        "c0"=设备发行初始化
        "c1"=获取终端公钥
        "c2"+终端证书=灌入终端证书
 *  makeA8SendData:
      ESAM指令通道，参数传入：
        dataType:"00"=明文，"01"=密文
        cos指令：TLV格式(最大长度不超384)
 *  makeABSendData:
      获取记录指令通道，参数传入：
        cmd：指令说明：
          "c0"+索引=获取PICC通道指令记录索引（1bytes）:记录索引号，循环记录，最新的记录号为01，上一次的为02，依次类推……
 */
const frame_Len = (92 * 2);
const send_Len = (20 * 2);
const ST = "33";
const pre_Proto = "0a0012";
const end_Proto = "1800";
const bMagic = "fe";
const bVer = "01";
const bCmdId = "7531";
var SEQ = 3;

function makeFrame(oJAWe1) {
  let frameCount = parseInt(oJAWe1['length'] / frame_Len);
  let frameBalance = oJAWe1['length'] % frame_Len;
  let frames = new Array();
  for (let i = 0; i < frameCount; i++) {
    frames['push'](oJAWe1['slice'](i * frame_Len, (i + 1) * frame_Len))
  }
  if (frameBalance > 0) {
    frames['push'](oJAWe1['slice'](-frameBalance))
  }
  let manufacturerFrames = new Array();
  for (let i = 0; i < frames['length']; i++) {
    let temp = frames[i];
    let SN = dataHandler['numberToHexString'](i + 1, 1, true);
    let CTL = "";
    if (i == 0) {
      CTL = dataHandler['numberToHexString'](0x80 + frames['length'] - 1, 1, true)
    } else {
      CTL = dataHandler['numberToHexString'](frames['length'] - i - 1, 1, true)
    }
    let LEN = dataHandler['numberToHexString'](parseInt(temp['length'] / 2), 1, true);
    let frame = ST + SN + CTL + LEN + temp;
    let bcc = 0;
    for (let j = 1; j < parseInt(frame['length'] / 2); j++) {
      let bit = parseInt(frame['slice'](j * 2, (j + 1) * 2), 16);
      bcc = bcc ^ bit
    }
    frame += dataHandler['numberToHexString'](bcc, 1, true);
    manufacturerFrames['push'](frame)
  }
  let protoFrames = new Array();
  for (let i = 0; i < manufacturerFrames['length']; i++) {
    let temp = manufacturerFrames[i];
    let len = dataHandler['numberToHexString'](parseInt(temp['length'] / 2), 1, true);
    let frame = pre_Proto + len + temp + end_Proto;
    protoFrames['push'](frame)
  }
  let wechatFrames = new Array();
  for (let i = 0; i < protoFrames['length']; i++) {
    let temp = protoFrames[i];
    let nLen = dataHandler['numberToHexString'](parseInt(temp['length'] / 2) + 8, 2, true);
    let nSeq = dataHandler['numberToHexString'](SEQ, 2, true);
    let frame = bMagic + bVer + nLen + bCmdId + nSeq + temp;
    wechatFrames['push'](frame)
  }
  SEQ++;
  if (SEQ > 0xf) {
    SEQ = 1
  }
  let bufferArray = new Array();
  for (let i = 0; i < wechatFrames['length']; i++) {
    let temp = wechatFrames[i];
    let bufferCount = parseInt(temp['length'] / send_Len);
    let bufferBalance = temp['length'] % send_Len;
    for (let j = 0; j < bufferCount; j++) {
      let item = temp['slice'](j * send_Len, (j + 1) * send_Len);
      bufferArray['push'](dataHandler['hexStringToBufferArray'](item))
    }
    if (bufferBalance > 0) {
      let item = temp['slice'](-bufferBalance);
      bufferArray['push'](dataHandler['hexStringToBufferArray'](item))
    }
  }
  return bufferArray
}

function makeAuthResponse() {
  let prefix = "fe0100184e2100010a06080012024f4b12063132"; // 艾特斯1
  let endfix = "33313234";  // 艾特斯1
  // let prefix = "FE01001A271100010A0018848004200128023A06"; // 金溢1
  // let endfix = "021123345667";  // 金溢1
  let dataArray = new Array();
  dataArray['push'](dataHandler['hexStringToBufferArray'](prefix)); // 艾特斯1 金溢1
  dataArray['push'](dataHandler['hexStringToBufferArray'](endfix)); // 艾特斯1
  return dataArray
}

function makeInitResponse() {
  let prefix = "fe0100164e2300020a06080012024f4b10001800";
  let endfix = "2000";
  let dataArray = new Array();
  dataArray['push'](dataHandler['hexStringToBufferArray'](prefix));
  dataArray['push'](dataHandler['hexStringToBufferArray'](endfix));
  return dataArray
}

function makeA2SendData() {
  let data = "a2";
  return makeFrame(data)
}

function makeA3SendData(c1, s2) {
  let data = "a3" + c1;
  let len = dataHandler['numberToHexString'](parseInt(s2['length'] / 2), 2, false);
  data += len;
  data += s2;
  return makeFrame(data)
}

function makeA4SendData(QHJzV1, tA2) {
  let data = "a4" + QHJzV1;
  let len = dataHandler['numberToHexString'](parseInt(tA2['length'] / 2), 2, false);
  data += len;
  data += tA2;
  return makeFrame(data)
}

function makeA5SendData(iqm1) {
  let data = "a5";
  let len = dataHandler['numberToHexString'](parseInt(iqm1['length'] / 2), 1, true);
  data += len;
  data += iqm1;
  return makeFrame(data)
}

function makeA6SendData(HXFD1) {
  let data = "a6";
  let len = dataHandler['numberToHexString'](parseInt(HXFD1['length'] / 2), 2, false);
  data += len;
  data += HXFD1;
  return makeFrame(data)
}

function makeA7SendData(rssUNR1) {
  let data = "a7";
  let len = dataHandler['numberToHexString'](parseInt(rssUNR1['length'] / 2), 2, false);
  data += len;
  data += rssUNR1;
  return makeFrame(data)
}

function makeA8SendData($srGK1, gmSkjQ2) {
  let data = "a8" + $srGK1;
  let len = dataHandler['numberToHexString'](parseInt(gmSkjQ2['length'] / 2), 2, false);
  data += len;
  data += gmSkjQ2;
  return makeFrame(data)
}

function makeABSendData(b1) {
  let data = "ab";
  let len = dataHandler['numberToHexString'](parseInt(b1['length'] / 2), 2, false);
  data += len;
  data += b1;
  return makeFrame(data)
}
