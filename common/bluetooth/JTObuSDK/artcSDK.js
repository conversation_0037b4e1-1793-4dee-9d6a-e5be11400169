var JLSDK = require("./JLDeviceWechat+33Protocol"),
    code = require("./errorCode");
export
function ScanDevice(t, e) {
    JLSDK.scanDevice(t, 1e4, t =>{
        e.call(this, t)
    })
}
export
function StopScanDevice(t) {
    JLSDK.stopScanDevice(e =>{
        t.call(this, e)
    })
}
export
function ConnectDevice(t, e, c) {
    JLSDK.connectDevice(t, t =>{
        c.call(this, t),
        0 == t.code && JLSDK.onDisconnet(t =>{
            e.call(this, t)
        })
    })
}
export
function DisconnectDevice(t) {
    JLSDK.disConnectDevice(e =>{
        t.call(this, e)
    })
}
export
function OpenChannel(t) {
    JLSDK.ESAMRset(e =>{
        t.call(this, e)
    })
}
export
function GetSerialNo(t) {
    JLSDK.getObuSN(e =>{
        t.call(this, e)
    })
}
export
function checkCardExists(t) {
    JLSDK.checkCard(e =>{
        t.call(this, e)
    })
}
export
function OpenCard(t) {
    let resultData = {
        code: 0,
        err_msg: "成功"
    };
    t && t(resultData)
}
export
function GetCardNo(t) {
    JLSDK.getCardNum(e =>{
        t.call(this, e)
    })
}
export
function GetCardFile15(t) {
    JLSDK.getCardInfo(e =>{
        t.call(this, e)
    })
}
export
function GetCardFile16(t) {
    JLSDK.getCardholderInfo(e =>{
        t.call(this, e)
    })
}
export
function GetCardFileCustom(t, e, c) {
    JLSDK.readRetainFile(t, e, t =>{
        c.call(this, t)
    })
}
export
function SetCardFile0015(t, e) {
    JLSDK.getCardRandNum(0, c =>{
        c.code == code.successCode() ? t.call(this, c.data, t =>{
            0 == t.code ? JLSDK.writeCardInfomation(1, t.data, t =>{
                e.call(this, t)
            }) : e.call(this, t)
        }) : e.call(this, c)
    })
}
export
function SetCardFile0016(t, e) {
    JLSDK.getCardRandNum(1, c =>{
        c.code == code.successCode() ? t.call(this, c.data, t =>{
            0 == t.code ? JLSDK.writeCardInfomation(0, t.data, t =>{
                e.call(this, t)
            }) : e.call(this, t)
        }) : e.call(this, c)
    })
}
export
function SetCardFileCustomer(t, e) {
    JLSDK.getCardRandNum(0, c =>{
        c.code == code.successCode() ? t.call(this, c.data, t =>{
            0 == t.code ? JLSDK.writeRetainFile(t.data, t =>{
                e.call(this, t)
            }) : e.call(this, t)
        }) : e.call(this, c)
    })
}
export
function InitLoad(t, e, c, a, o, i) {
    JLSDK.creditForLoad_Init(t, "01", a, e, t =>{
        console.log("圈存初始化："+JSON.stringify(t)),
            t.code === code.successCode() ? o.call(this, t.data.rand, t.data.trade_no, t.data.mac1, t.data.balance, t =>{
                0 == t.code ? JLSDK.creditForLoad_Load(c, t.data, t =>{
                    i.call(this, t)
                }) : i.call(this, t)
            }) : i.call(this, t)
    })
}
export
function GetSystemInfo(t) {
    JLSDK.getSystemInfo(e =>{
        t.call(this, e)
    })
}
export
function GetVehicleInfo(t, e) {
    JLSDK.getCarInfo(t, t =>{
        e.call(this, t)
    })
}
export
function SetSystemInfo(t, e) {
    JLSDK.getObuRadom(0, c =>{
        c.code == code.successCode() ? t.call(this, c.data, t =>{
            0 == t.code ? JLSDK.writeObuIfo(1, t.data, t =>{
                e.call(this, t)
            }) : e.call(this, t)
        }) : e.call(this, c)
    })
}
export
function SetVehicleInfo(t, e) {
    JLSDK.getObuRadom(1, c =>{
        c.code == code.successCode() ? t.call(this, c.data, t =>{
            0 == t.code ? JLSDK.writeObuIfo(1, t.data, t =>{
                e.call(this, t)
            }) : e.call(this, t)
        }) : e.call(this, c)
    })
}
export
function Activate(t, e) {
    ObuCommand("00a40000023f00", c =>{
        0 == c.code ? JLSDK.getObuRadom(0, a =>{
            0 == a.code ? t.call(this, a.data, d =>{
                0 == d.code ? ObuCommand("04d6811a0501" + d.data, f =>{
                    e.call(this, f)
                }) : e.call(this, d)
            }) : e.call(this, a)
        }) : e.call(this, c)
    })
}
export
function CpuCommand(t, e) {
    JLSDK.ICCChannel(t, t =>{
        e.call(this, t)
    })
}
export
function ObuCommand(t, e) {
    JLSDK.ESAMChannel(t, t =>{
        e.call(this, t)
    })
}
