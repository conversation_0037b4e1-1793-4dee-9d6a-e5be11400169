module.exports =  {
  scan: 0, // 搜索失败
  connect: 1, // 连接失败
  disconnect: 2, // 断开连接失败
  readCardInfo: 3, // 读取卡片信息失败
  readCarNoInObu: 4, // 读取OBU车辆信息失败
  readObuSn: 5, // 读取OBU号失败
  checkCardNo: 6, // 卡内车牌和OBU内车牌不一致
  readObuEF01: 7, // 读取OBU EF01文件异常
  cardStatusError: 8, // 卡片状态异常
  cardManageQueryCardInfo: 9, // cardManageQueryCardInfo接口异常，查询不到卡片
  getACRam: 10, // 获取OBU激活随机数
  activating: 11, // 激活指令失败
  
  selectError: 12, // 卡片文件目录选择失败
  randomError: 13, // 卡片获取随机数失败
  writeError: 14, // 卡片写文件失败
  readCardAmountError: 15, // 读取卡片金额异常
  pinError: 16, // 卡片圈存前pin校验异常
  rechargeInitError: 17, // 圈存初始化异常
  rechargeWriteCardError: 18, // 圈存写卡异常

  obuSelectError: 19, // OBU文件选取失败
  obuReadInfoError: 20, // OBU文件读取失败
  obuWriteError: 21, // OBU写文件失败
  obuRandomError: 22, // OBU获取随机数失败
}