// var artcBleUtil = require('./artcBleUtil.js')
var artcBleUtil = require('./artcBleUtil.js')
var gvBleUtil = require('./gvBleUtil.js')
var artcDataUtil = require('./artcDataUtil.js')
var artcDataHandler = require('./artcDataHandler.js')

var gbk2 = require('./gbk2.js')
var BleError = require('./bleHelpErrorCode')

export function connectBleDevice(device,conStateChange,conCallback){
		gvBleUtil.connectBle(device, function(code) {
			if (code == 0) {
				console.log("连接ble成功", device);
				//部署设备，部署成功后才可以收发数据
				gvBleUtil.deployBle(function(code) {
				  if (code == 0) {
					console.log("部署ble成功", device);
							
					gvBleUtil.initDevice(function(code, res) {
					  if (code == 0) {
						conCallback && conCallback(code)
					  } else {
						conCallback && conCallback(code)
					  }
					})
				  } else {
					  conCallback && conCallback(code)
				  }
				})
			  } else {
				  conCallback && conCallback(code)
			  }
			})	
}

export function disconnectBleDevice(callback) {
	return new Promise((resolve, reject) => {
		gvBleUtil.disconnectBleDevice((code) => {
			console.log('断开连接', code)
			callback && callback(code)
		})
	})
}
export function DisconnectDeviceGV(callback) {
	return new Promise((resolve, reject) => {
		gvBleUtil.disconnectBleDevice((code) => {
			console.log('断开连接', code)
			callback && callback(code)
			// if (code == 0) {
			//     console.log('断开成功')
			//     resolve()
			// } else {
			//     console.log('断开连接失败')
			//     reject({
			//         type: BleError.disconnect,
			//         msg: `断开连接失败${code}`
			//     })
			// }
		})
	})
}
export function OpenChannel(callback) {
	let resultData = {
		code: 0,
		err_msg: "成功"
	};
	callback && callback(resultData)
}

export function checkCardExists(callback) {
	let resultData = {
		code: 0,
		err_msg: "成功"
	};
	callback && callback(resultData)
}

export function OpenCard(callback) {
	let resultData = {
		code: 0,
		err_msg: "成功"
	};
	callback && callback(resultData)
}

export function GetCardNo(callback) {

	GetCardFile15(result => {
		if (result.code == 0) {
			callback && callback({
				code: result.code,
				data: result.data.cardNo
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "卡片读取卡号失败"
			})
		}
	})
}

export function GetCardFile15(callback) {
	CpuCommand(["00A40000023F00"], result => {
		if (result.code == 0) {
			CpuCommand(["00A40000021001"], result => {
				if (result.code == 0) {
					CpuCommand(["00b095002b"], result => {
						if (result.code == 0) {
							let reapdu = artcDataHandler.resolveTLV(result.data.slice(10))
							console.log("0015 reapdu", reapdu)

							let f0015 = {
								cardType: hex2int(reapdu[0].substr(16, 2)),
								version: reapdu[0].substr(18, 2),
								cardNo: reapdu[0].substr(20, 20),
								startDate: reapdu[0].substr(40, 8),
								endDate: reapdu[0].substr(48, 8),
								licenceNo: convertLisenceNo(reapdu[0].substr(56, 24)),
								licenceColor: parseInt(reapdu[0].substr(18, 2)) >= 40 ?
									code2Color(reapdu[0].substr(82, 2)) : code2Color(reapdu[
										0].substr(84, 2)),
								carType: reapdu[0].substr(18, 2) >= 40 ? reapdu[0].substr(
									84, 2) : ''
							}
							console.log("0015", f0015)
							callback && callback({
								code: result.code,
								data: f0015
							})
						} else {
							callback && callback({
								code: result.code,
								err_msg: "读取0015文件失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "读取0015文件进入1001目录失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "读取0015文件进入3F00目录失败"
			})
		}
	})
}

export function GetCardFile16(callback) {
	CpuCommand(["00A40000023F00"], result => {
		if (result.code == 0) {
			CpuCommand(["00b0960037"], result => {
				if (result.code == 0) {
					let reapdu = artcDataHandler.resolveTLV(result.data.slice(10))

					console.log("0016 reapdu", reapdu)
					let f0016 = {
						userTag: reapdu[0].substr(0, 2),
						employeeTag: reapdu[0].substr(2, 2),
						userName: reapdu[0].substr(4, 40),
						certificateId: reapdu[0].substr(44, 64),
						certificateType: reapdu[0].substr(108, 2)
					}
					console.log("0016 f0016", f0016)
					callback && callback({
						code: result.code,
						data: f0016
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "卡读取0016文件失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "卡读取0016文件进入3F00目录失败"
			})
		}
	})
}

export function InitLoad(pinCode, money, issue_times, terminalNo, getMac2, nextDevResult) {
	GetCardNo(result => {
		if (result.code == 0) {
			//验证pin
			CpuCommand(["0020000006313233343536"], result => {
				if (result.code == 0) {
					let p = parseInt(money).toString(16);
					let hexMoney = new Array(8 - (p + '').length + 1).join('0') + p;
					//初始化圈存
					CpuCommand(['805000020B01' + hexMoney + terminalNo + '10'],
						result => {
							if (result.code == 0) {
								let balance = result.data.substr(18, 8)
								let tradeNo = result.data.substr(26, 4)
								let rand = result.data.substr(34, 8)
								let mac1 = result.data.substr(42, 8)
								//获取mac2
								getMac2 && getMac2(rand, tradeNo, mac1, balance, result => {
									//获取mac2成功
									if (result.code == 0) {
										//圈存
										CpuCommand(['805200000B' + issue_times + result
											.data
										], result => {
											if (result.code == 0) {
												nextDevResult && nextDevResult(
													result)
											} else {
												nextDevResult && nextDevResult({
													code: BleError
														.rechargeWriteCardError,
													err_msg: "记账卡初始化圈存失败"
												})
											}
										})
									} else {
										nextDevResult && nextDevResult({
											code: BleError.rechargeInitError,
											err_msg: "记账卡初始化圈存获取mac2失败"
										})
									}
								})
							} else {
								nextDevResult && nextDevResult({
									code: BleError.rechargeInitError,
									err_msg: "记账卡初始化圈存（初始化）失败"
								})
							}
						})
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin马校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}

function hexStr2IntStr(hexStr) {
	let intStr = ""
	for (let i = 0; i < parseInt(hexStr.length / 2); i++) {
		let bit = parseInt(hexStr.slice(i * 2, (i + 1) * 2), 16)
		let num = +(bit ^ 0x30)
		intStr += num
	}
	return intStr
}

function hex2int(hex) {
	var len = hex.length,
		a = new Array(len),
		code;
	for (var i = 0; i < len; i++) {
		code = hex.charCodeAt(i);
		if (48 <= code && code < 58) {
			code -= 48;
		} else {
			code = (code & 0xdf) - 65 + 10;
		}
		a[i] = code;
	}

	return a.reduce(function(acc, c) {
		acc = 16 * acc + c;
		return acc;
	}, 0);
}

function hexStr2GBKStr(hexStr) {
	return gbk2.decodeFromGb2312(hexStr)
}

function convertLisenceNo(carnocode) {
	carnocode = carnocode.toUpperCase()
	console.log('carnocode==>>', carnocode)
	var realCarno = ''

	for (var i = 0; i < carnocode.length;) {
		realCarno = realCarno + '%' + carnocode.substr(i, 2)
		i = i + 2
	}
	console.log('realCarno==>>', realCarno)
	var carno = gbk2.decodeFromGb2312(realCarno) + '';

	return carno.replace(/[^\u4e00-\u9fa5a-zA-Z\d]/g, '')
}

function code2Color(code) {
	switch (code) {
		case '00':
			return "0"
		case '01':
			return "1"
		case '02':
			return "2"
		case '03':
			return "3"
		case '04':
			return "4"
		case '05':
			return "5"
		case '06':
			return "6"
	}
}
export function GetBalance(callback) {
	CpuCommand(["805c000204"], result => {
		if (result.code == 0) {
			let balance = result.data.substr(18, 8);
			callback && callback({
				code: result.code,
				data: balance
			})
		} else {
			callback && callback(result)
		}
	})
}
/**
 * 写卡指令
 * @param cosArr 写卡指令，可以是
 * @param callback 回调
 * @param stype 00明文  01密文
 */
export function sendDataToDevice(tlv, callback, stype = '00') {
	// console.log('卡通道指令发送', tlv)
	// let tlv = artcDataHandler.makeTLV(cosArr)
	gvBleUtil.transCommand('00',tlv, (code, data) => {
	// artcBleUtil.sendDataToDevice(artcDataUtil.makeA3SendData(stype, tlv), (code, data, msg) => {
		// console.log('卡通道指令接收', code, data)
		callback && callback(code,data,'')
	})
}

/**
 * 写OBU指令
 * @param cosArr 写OBU指令
 * @param callback 回调
 * @param stype 00明文  01密文
 */
function ObuCommand(cosArr, callback, stype = '00') {
	console.log('OBU通道指令发送', cosArr)
	let tlv = artcDataHandler.makeTLV(cosArr)
	artcBleUtil.transCommand('00',tlv, (code, data, msg) => {
		console.log('OBU通道指令接收', code, data, msg)
		if (code == '0') {
			let cosResult = data.substr(-4, 4)
			let sl = data.slice(2, 4)
			let subs = data.substr(2, 2)
			console.log(cosResult, sl, subs)
			if (data.slice(2, 4) === "00" && cosResult == '9000') {
				callback && callback({
					code: code,
					data: data,
					err_msg: msg
				})
			} else {
				callback && callback({
					code: cosResult,
					data: data,
					err_msg: msg
				})
			}
		} else {
			callback && callback({
				code: BleError.obuWriteError,
				data: data,
				err_msg: `写OBU 指令发送失败${code}`
			})
		}
	})
}
/**
 * 写OBU指令
 * @param cosArr 写OBU指令
 * @param callback 回调
 * @param stype 00明文  01密文
 */
function ObuCommand_bak(cosArr, callback, stype = '00') {
	console.log('OBU通道指令发送', cosArr)
	let tlv = artcDataHandler.makeTLV(cosArr)
	artcBleUtil.sendDataToDevice(artcDataUtil.makeA8SendData(stype, tlv), (code, data, msg) => {
		console.log('OBU通道指令接收', code, data, msg)
		if (code == '0') {
			let cosResult = data.substr(-4, 4)
			let sl = data.slice(2, 4)
			let subs = data.substr(2, 2)
			console.log(cosResult, sl, subs)
			if (data.slice(2, 4) === "00" && cosResult == '9000') {
				callback && callback({
					code: code,
					data: data,
					err_msg: msg
				})
			} else {
				callback && callback({
					code: cosResult,
					data: data,
					err_msg: msg
				})
			}
		} else {
			callback && callback({
				code: BleError.obuWriteError,
				data: data,
				err_msg: `写OBU 指令发送失败${code}`
			})
		}
	})
}
