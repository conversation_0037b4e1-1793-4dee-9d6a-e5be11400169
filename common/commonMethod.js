import {
	setOpenidForRead,
	getOpenidForRead,
	getLoginUserInfo,
	setCurrentCar,
	getCurrentCar,
	getAccountId,
} from '@/common/storageUtil.js'
import request from "@/common/request-1/request.js";
import initInterfaces from '@/common/api/interface_4501.js'
//订阅服务
export function subscriptionMethod(callback) {
	console.log('订阅方法')
	console.log(initInterfaces)
	uni.requestSubscribeMessage({
		tmplIds: ['6Q0Praee_bD-2ZFNkFSQYSztnN3u8LKBWzM-i8W2Gc4'],
		success: (res) => {
			console.log('订阅方法', res['6Q0Praee_bD-2ZFNkFSQYSztnN3u8LKBWzM-i8W2Gc4'])
			wxGetOpenId(callback)
		},
		fail: (err) => {
			console.log(err)
		}
	})
}

export function wxGetOpenId(callback) {
	let that = this;
	let readOpenId = getOpenidForRead()
	console.log('readOpenId-product', readOpenId)
	if (readOpenId) {
		saveOpenId(readOpenId, callback)
	} else {
		// 微信手机号授权登录
		wx.login({
			success(res) {
				let params = {
					code: res.code
				}
				request.post(initInterfaces.getOpenid, {
					data: params
				}).then((res) => {
					if (res.code == 200) {
						if (res.data && res.data.openid) {
							console.log('获取微信openId', res.data.openid)
							setOpenidForRead(res.data.openid)
							saveOpenId(res.data.openid, callback)
						}
					}
				})
			}
		})
	}
}
//获取用户openId保存订阅
export function saveOpenId(openId, callback) {
	request.post(initInterfaces.saveOpenId, {
		data: {
			netUserId: getLoginUserInfo().userIdStr,
			openId: openId
		}
	}).then(res => {
		console.log(res)
		callback && callback()
	}).catch(err => {})
}


/*跳转充值 
/**dwz 2023/6/21
 */
export function goToRecharge(vehicleInfo) {
	setCurrentCar({})
	let data = {
		routePath: initInterfaces.vehicleBizSearch.method,
		bizContent: {
			vehicle_code: vehicleInfo.vehicleCode,
			vehicle_color: vehicleInfo.vehicleColor
		}
	}
	request
		.post(initInterfaces.issueRoute, {
			data: data
		})
		.then((res) => {

			if (res.code == 200 && res.data && res.data.length) {
				let item = res.data[0];
				if (vehicleInfo.vehicleCode == item.vehicle_code && vehicleInfo.cardNo == item
					.cpu_card_id) {

					goRechargeBusinessHandle(item);
				}

			}
		})
		.catch((error) => {
			console.log(error);
		})
}

// 根据查询出的卡类型进行不同充值业务
export function goRechargeBusinessHandle(item) {

	if (item.gx_card_type != '5' && item.gx_card_type != '0' && item.gx_card_type != '8' && item
		.gx_card_type != '9') {
		uni.showModal({
			title: '提示',
			content: '您当前车辆绑定的卡不是捷通日日通记账卡或储值卡，无法充值'
		})
		return
	}
	if (item.gx_card_type == '5' || item.gx_card_type == '8') {
		setCurrentCar(item)
		uni.navigateTo({
			url: '/pagesB/rechargeBusiness/recharge/p-recharge'
		})
		return
	}
	if (item.gx_card_type == '0') {
		setCurrentCar(item)
		uni.navigateTo({
			url: '/pagesB/loadBusiness/recharge/recharge'
		})
		return
	}
	if (item.gx_card_type == '9') {
		getClientAccountInfo()
		return
	}
}

//查询客账信息
export function getClientAccountInfo() {
	let params = {
		customerId: getAccountId()
	}
	request
		.post(initInterfaces.rechargeVehicleList, {
			data: params
		})
		.then((res) => {
			if (res.code == 200) {
				if (res.data.clientAccount) {
					let accountListData = res.data.clientAccount
					let params = JSON.parse(JSON.stringify(accountListData))
					params.bindVehicleList = JSON.stringify(params.bindVehicleList)
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/rechargeAccount/index?' +
							objToUrlParam(params)
					})
				}
			} else {
				uni.showModal({
					title: '提示',
					content: res.msg,
					showCancel: false
				})
			}
		})

}

export function objToUrlParam(obj) {
	if (obj && Object.keys(obj).length) {
		return Object.keys(obj)
			.map((key) => {
				return key + '=' + obj[key]
			})
			.join('&')
	}
	return ''
}


//图片转换base64
export async function imgToBase64(url) {
	return new Promise((resolve, reject) => {
		const image = new Image()
		image.src = url
		image.onload = () => {
			const canvas = document.createElement('canvas')
			canvas.width = image.naturalWidth // 使用 naturalwidth 为了保证图片的清晰度
			canvas.height = image.naturalHeight
			canvas.style.width = `${canvas.width / window.devicepixelRatio}px`
			canvas.style.height = `${canvas.height / window.devicepixelRatio}px`
			const ctx = canvas.getContext('2d')
			if (!ctx) {
				return null
			}
			ctx.drawImage(image, 0, 0)
			const base64 = canvas.toDataURL('image/png')
			return resolve(base64)
		}
		image.onerror = (err) => {
			return reject(err);
		}
	})
}