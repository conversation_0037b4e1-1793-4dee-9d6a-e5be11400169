
	.weui-card {
		background-color: #fff;

		.weui-card-hd {
			display: flex;
			position: relative;
			justify-content: center;
			flex-direction: column;
			// margin-bottom: -1px;
			padding: 30rpx 30rpx 35rpx 30rpx;
			color: #333333;
			font-weight: 500;
			font-size: 32rpx;

			.weui-card-hd-wrapper {
				width: 100%;
				display: flex;
				align-items: center;
			}

			.weui-card-hd-title {
				flex: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.weui-card-extra {
				.desc {
					font-size: 24rpx;
					font-weight: 400;
					color: #0081FF;
				}
			}
		}

		.weui-card-bd {
			padding: 0 30rpx;

			.list {
				.list-item {
					display: flex;
					margin-bottom: 20rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.list-item_label {
						font-size: 26rpx;
						width: 160rpx;
						margin-right: 15rpx;
						font-weight: 400;
						color: #999999;
					}

					.list-item_value {
						width: calc(100% - 140rpx);
						font-size: 26rpx;
						font-weight: 400;
						color: #333333;

						/deep/ .u-input {
							width: 100%;
							background: #FFFFFF;
							border-radius: 8rpx !important;
							border: 2rpx solid #DDDDDD !important;
							height: 64rpx;
						}
					}

					.list-item_view {
						width: calc(100% - 140rpx);
						height: 64rpx;
						background: #FFFFFF;
						border-radius: 8rpx;
						border: 2rpx solid #DDDDDD;
						line-height: 64rpx;
						padding-left: 32rpx;
						display: flex;

						view {
							flex: 1;
						}

						.down-icon {
							width: 34rpx;
							height: 26rpx;
							background-size: 100%;
							margin: 22rpx 16rpx 0 0
						}
					}
				}
			}
		}
	}