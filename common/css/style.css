@font-face {
  font-family: 'icomoon';
  src:  url('@/common/css/fonts/icomoon.eot?4u90pe');
  src:  url('@/common/css/fonts/icomoon.eot?4u90pe#iefix') format('embedded-opentype'),
    url('@/common/css/fonts/icomoon.ttf?4u90pe') format('truetype'),
    url('@/common/css/fonts/icomoon.woff?4u90pe') format('woff'),
    url('@/common/css/fonts/icomoon.svg?4u90pe#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.uicon-date-fill:before {
  content: "\e900";
}
