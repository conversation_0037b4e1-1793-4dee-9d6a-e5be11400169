.weui-form {
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-webkit-flex-direction: column;
		flex-direction: column;
		line-height: 1.4;
		min-height: 100%;
		box-sizing: border-box;
		background-color: #fff;
		margin-top: 20rpx;
	}

	.weui-cells__title {
		padding: 30rpx 30rpx;
		color: #333333;
		font-size: 32rpx;
		font-weight: 500;
		

		&+.weui-cells {
			padding-top: 0;
		}

	}

.weui-cells.weui-cells-no__title {
    padding-top: 0rpx
}
	.weui-cells {
		padding-top: 36rpx;
		background-color: #FFFFFF;
		font-size: 30rpx;
		overflow: hidden;
		position: relative;
	}

	.weui-cells:before {
		content: " ";
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		height: 1px;
		border-top: 1px solid #e9e9e9;
		color: #e9e9e9;
		-webkit-transform-origin: 0 0;
		transform-origin: 0 0;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
	}

	.weui-cell {
		padding: 26rpx 30rpx;
		position: relative;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;

		&:before {
			content: " ";
			position: absolute;
			left: 30rpx;
			top: 0;
			right: 0;
			height: 1px;
			border-top: 1px solid #e9e9e9;
			color: #e9e9e9;
			-webkit-transform-origin: 0 0;
			transform-origin: 0 0;
			-webkit-transform: scaleY(0.5);
			transform: scaleY(0.5);
		}
	}

	.weui-label {
		display: block;
		width: 200rpx;
		word-wrap: break-word;
		word-break: break-all;
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.weui-label__require:before {
		content: '*';
		color: #FA3534;
		margin-right: 4rpx;
	}

	.weui-cell_primary {
		align-items: flex-start;
	}

	.weui-cell__bd {
		flex: 1;

	}

	.weui-cell__ft {
		text-align: right;
	}

	.weui-input, .weui-cell__value {
		width: 100%;
		border: 0;
		outline: 0;
		-webkit-appearance: none;
		background-color: transparent;
		color: #333333;
		font-weight: 400;
		font-size: 30rpx;
		text-align: right;
		    line-height: 1.4em;
		    height: 1.4em;
		    min-height: 1.4em;

	}
	.weui-textarea{
		padding: 10rpx 0;
		min-height: 110rpx;
	}
	.weui-cell_disabled .weui-input,
	.weui-cell_readonly .weui-textarea,
	.weui-cell_disabled .weui-textarea {
		color: #999999;
	}
 
.weui-input {
	/deep/.placeholder{
		font-size: 30rpx;
		 color: #999999;
		 font-weight: 400;
	} 
}
.weui-cell_picker .weui-cell__ft {
		padding-right: 26rpx;
		position: relative;
	}

	.weui-cell_picker .weui-cell__ft:after {
		content: " ";
		display: inline-block;
		height: 8px;
		width: 8px;
		border-width: 2px 2px 0 0;
		border-color: #999999;
		border-style: solid;
		-webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
		transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
		position: relative;
		top: -5px;
		position: absolute;
		top: 50%;
		margin-top: -5px;
		right: 4px;
	}
	.weui-cell_picker .weui-picker-value{
		text-align: right;
		font-size: 30rpx;
		font-weight: 400;
		color: #333;
	}
	.weui-cell_picker .weui-picker-placeholder{
		text-align: right;
		font-size: 30rpx;
		 color: #999999;
		 font-weight: 400;
	}
	.weui-cells__title__decoration {
			position: relative;
			font-weight: 600;
			color: #333333;
			font-size: 30rpx;
			padding-left: 16rpx;
	}
		
	.weui-cells__title__decoration:before {
			content: ' ';
			position: absolute;
			left: 0rpx;
			top: 50%;
			width: 8rpx;
			height: 30rpx;
			-webkit-transform: translateY(-50%);
			transform: translateY(-50%);
			border-radius: 4rpx;
			background-color:#0066E9;
			border-top-left-radius: 2px;
			border-top-right-radius: 2px;
			border-bottom-right-radius: 2px;
			border-bottom-left-radius: 2px;
	}