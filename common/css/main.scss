// 底部固定区域
.weui-bottom-fixed{
	position: fixed;
	left: 0px;
	bottom: 0px;
	width: 100%;
	background-color: #FFFFFF;
	z-index: 999;
}
.weui-bottom-fixed__box{
	width: 100%;
	border-top: 1px solid #e9e9e9;
	padding: 20rpx 60rpx 48rpx 60rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.weui-btn{
	position: relative;
	border: 0upx;
	width: 100%;
	font-weight: 400;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0 30upx;
	font-size: 28upx;
	height: 84upx;
	line-height: 1;
	text-align: center;
	text-decoration: none;
	overflow: visible;
	margin-left: initial;
	transform: translate(0upx, 0upx);
	margin-right: initial;
}
.weui-btn:after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(0, 0, 0, 0.2);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 20rpx;
}
.weui-btn_mini {
    padding: 0 30rpx;
    height: 56rpx;
	line-height: 56rpx;
    font-size: 26rpx;
	width: auto;
}
.weui-btn_default:not(.weui-btn_disabled):active {
   background-color: #F8F8F8;
}
.weui-btn_default {
    color: #666666;
    background-color: #F8F8F8;
}
.weui-btn_primary:not(.weui-btn_disabled):active {
   background-color: #0066E9;
}
.weui-btn_primary {
    color: #FFFFFF;
    background-color: #0066E9;
}
.weui-btn_primary:after{
	display: none;
}
.weui-btn_disabled.weui-btn_primary {
    opacity: 0.5;
	color: #FFFFFF !important;
	background-color: #0066E9 !important;
}
.weui-btn_disabled.weui-btn_plain-default{
	opacity: 0.5;
}
.weui-btn_disabled.weui-btn_plain-primary{
	opacity: 0.5;
}
.weui-btn_plain-default{
	color: #323435;
	border: 1px solid #E8E8E8;
	background-color: transparent;
}
.weui-btn_plain-primary{
	color: #fff;
	border: 1px solid #0066E9;
	background-color: transparent;
}
.weui-btn_normal{
	border: 1px solid #0066E9;
	background: #fff;
	color: #0066E9;
}


.weui-btn_plain-default:after {
    border-width: 0;
}

.weui-btn_plain-primary:after {
    border-width: 0;
}

.weui-form-preview {
	position: relative;
	padding: 20rpx 30rpx;
	background-color: #ffffff;
}

.weui-form-preview__hd {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1px dashed #c3c3c3;

	.weui-form-preview__label {
		min-width: 200rpx;
		color: #333;
		font-size: 32rpx;
		font-weight: 500;
		text-align: left;
	}

	.weui-form-preview__value {
		font-style: normal;
		font-size: 28rpx;
		font-weight: 400;
	}
}

.weui-form-preview__bd {
	padding-top: 12rpx;
	.weui-form-preview__item {
		padding-bottom: 12rpx;
	}
}

.weui-form-preview__item {
	display: flex;
	-moz-box-pack: justify;
	-ms-box-pack: justify;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-justify-content: space-between;
	justify-content: space-between;
	-moz-box-align: center;
	-webkit-box-align: center;
	box-align: center;
	align-items: center;
	-webkit-align-items: center;
	-moz-align-items: center;
}

.weui-form-preview__label {
	color: #999999;
	font-size: 28rpx;
	font-weight: 400;
}

.weui-form-preview__value {
	color: #333;
	font-size: 28rpx;
	font-weight: 400;
	display: block;
	overflow: hidden;
	word-break: normal;
	word-wrap: break-word;
}
.weui-form-preview__ft {
	
}
.weui-form-preview__item.g-flex-start {
	-moz-box-pack: start;
	-ms-box-pack: start;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-justify-content: flex-start;
	justify-content: flex-start;
}

//激活公用样式
.apply-after-sale{
	.section{
		margin: 20rpx;
	}
	.weui-card {
		background-color: #fff;
		border-radius: 12rpx;
		// margin: 20rpx;
		.weui-card-hd {
			display: flex;
		    position: relative;
			justify-content: center;
			flex-direction: column;
			padding: 30rpx 30rpx 35rpx 30rpx;
			color: #333333;
			font-weight: 500;
			font-size: 32rpx;
		
			.weui-card-hd-wrapper {
				width: 100%;
				display: flex;
				align-items: center;
			}
	
			.weui-card-hd-title {
				flex: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
	
			.weui-card-extra {
				.desc {
					font-size: 24rpx;
					font-weight: 400;
					color: #0081FF;
				}
			}
		}
		.weui-card-bd {
			padding: 0 30rpx;
			.list {
				.list-item {
					display: flex;
					margin-bottom: 20rpx;
		
					&:last-child {
						margin-bottom: 0;
					}
		
					.list-item_label {
						font-size: 26rpx;
						width: 160rpx;
						margin-right: 15rpx;
						font-weight: 400;
						color: #999999;
					}
		
					.list-item_value {
						width: calc(100% - 140rpx);
						font-size: 26rpx;
						font-weight: 400;
						color: #333333;
						/deep/ .u-input {
							width: 100%;
							background: #FFFFFF;
							border-radius: 8rpx !important;
							border: 2rpx solid #DDDDDD !important;
							height: 64rpx;
						}
					}
					.list-item_value_ex{
						/deep/ .u-input {
							width: 90%;
							border: none !important;
						}
					}
					.list-item_view{
						    width: calc(100% - 140rpx);
						    height: 64rpx;
						    background: #FFFFFF;
						    border-radius: 8rpx;
						    border: 2rpx solid #DDDDDD;
						    line-height: 64rpx;
						    padding-left: 32rpx;
							display: flex;
							view{
								flex: 1;
							}
							.down-icon{
								width: 34rpx;
								height: 26rpx;
								background-size: 100%;
								margin:22rpx 16rpx 0 0
							}
					}
					.recipient-name{
						width: 80rpx;
					}
					.recipient{
						width: calc(100% - 80rpx);
					}
				}
			}
		}
	}
	.vehicle-info {
		padding-bottom: 50rpx;
	}
}
.align-items-center{
	align-items: center;
}
//取消button
.weui-btn_quit:not(.weui-btn_disabled):active {
   background-color: #FFFFFF;
}
.weui-btn_quit {
   background: #FFFFFF;
   border-radius: 10rpx;
   color: #0066E9;
   border: 2rpx solid #0066E9;
}
.weui-btn_quit:after{
	display: none;
}
.weui-btn_disabled.weui-btn_quit {
    opacity: 0.5;
	color: #FFFFFF !important;
	background-color: #C6C6C6 !important;
}
.line-block {
	height: 20rpx;
	width: 100%;
	background-color: #F6F6F6;
}
.status-value.warning {
	background-color: rgba(255, 145, 0, 0.1);
	color: #FF9100;
}


.status-value.success {
	background-color: rgba(0, 189, 50, 0.1);
	color: #00BD32;
}

.status-value.danger {
	background: rgba(255, 84, 84, 0.1);
	color: #FF5454;
}

.status-value.info {
	background: rgba(106, 105, 105, 0.1);
	color: #6A6969;
}
.status-value.normal {
	background: rgba(0,102,233,0.1);
	color: #0066E9;
}
.weui-card-bd-pay {
	width: 100%;
	height: 73rpx;
	background: #F0F0F0;
	border-radius: 6rpx;
	margin: 0 auto;
	text-align: center;
	line-height: 73rpx;
	position: relative;

	.ico_filter_down {
		width: 20rpx;
		height: 20rpx;
		position: absolute;
		right: 30rpx;
		top: 28rpx;
	}
}
.order-address__detail {
	.weui-card-bd {
		display: flex;
		width: 100%;
	}
	
	.vehicle-info {
		padding-bottom: 34rpx !important;
	}
	
	.list {
		width: calc(100% - 50rpx);
	}
	
	.location {
		width: 50rpx;
	}
	
	.location-icon {
		width: 35rpx;
		height: 36rpx;
		background-size: 100%;
	}
}