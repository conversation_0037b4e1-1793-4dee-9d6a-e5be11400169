let urlConfig = {
	addType: {
		type:'redirectTo',
		url: '/pagesA/newBusiness/addType/addType'
	},
	customerQuery:{
		type:'redirectTo',
		url: '/pagesB/customerQuery/index'
	},
	issue:{
		type:'redirectTo',
		url: '/pagesA/newBusiness/activation/activation'
	},
	orderManage:{
		type:'redirectTo',
		url: '/pagesA/sellBusiness/orderManage/index/index'
	},
	otherPage:{
		type:'redirectTo',
		url: '/pagesB/other/logout/logout'
	},
	deviceProbing:{
		type:'redirectTo',
		url: '/pages/device-probing/index'
	},
	carActivate:{
		type:'redirectTo',
		url: '/pagesB/carActivate/carActivate'
	}
}
export {
	urlConfig
};
