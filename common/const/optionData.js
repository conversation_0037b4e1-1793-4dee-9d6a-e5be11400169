//车辆用户类型字典
export const vehicleUserType = {
	'0': '普通车',
	'19': '广西警车',
	'20': '路政车',
	'24': '集装箱J1',
	'26': '应急救援车',
	'27': '普通牵引车',
	'28': '集装箱J2'
}
//车辆类型字典
export const VehicleClassType = {
	'1': '一型车',
	'2': '二型车',
	'3': '三型车',
	'4': '四型车',
	'5': '五型车',
	'6': '六型车',
	'7': '七型车',

}

//车型
export function getVehicleClassType(val) {
	return VehicleClassType[val] || ''
}

//车辆类型字典
export const vehicleType = {
	'2': '客',
	'1': '货',
	'3': '专项'
}

//车辆类型
export function getVehicleType(val) {
	return vehicleType[val] || ''
}

//个人证件类型字典
export const personalOCRType = {
	'0': '身份证',
	'1': '军官证',
	'2': '护照',
	'3': '入境证',
	'4': '临时身份证',
	'5': '港澳居民往来大陆通行证',
	'6': '台湾居民往来大陆通行证',
	'7': '武警警察身份证',
}
export function personalOCRTypeFilter(val) {
	return personalOCRType[val] || ''
}

//企业类型字典
export const enterpriseOCRType = {
	'1': '统一社会信用代码证',
	'2': '组织机构代码证',
	'3': '营业执照',
	'4': '事业单位法人证书',
	'5': '社会团体法人证书',
	'6': '律师事务所执业许可证'
}
export function enterpriseOCRTypeFilter(val) {
	return enterpriseOCRType[val] || ''
}



//车牌省份字典
export const provinces = {

	"浙": '浙',
	'沪': '沪',
	'苏': '苏',
	'皖': '皖',
	'赣': '赣',
	'闽': '闽',
	'京': '京',
	'津': '津',
	'渝': '渝',
	'冀': '冀',
	'豫': '豫',
	'云': '云',
	'辽': '辽',
	'黑': '黑',
	'湘': '湘',
	'鲁': '鲁',
	'新': '新',
	'鄂': '鄂',
	'桂': '桂',
	'甘': '甘',
	'晋': '晋',
	'蒙': '蒙',
	'陕': '陕',
	'吉': '吉',
	'贵': '贵',
	'粤': '粤',
	'青': '青',
	'藏': '藏',
	'川': '川',
	'宁': '宁',
	'琼': '琼',
	'使': '使',
	'领': '领',
}

//车牌省份
export function getProvince(val) {
	return provinces[val] || ''
}
// 省份
export const provincesOptions = [{
		label: '桂',
		value: '桂'
	},
	{
		label: "浙",
		value: '浙'
	},
	{
		label: '沪',
		value: '沪'
	},
	{
		label: '苏',
		value: '苏'
	},
	{
		label: '皖',
		value: '皖'
	},
	{
		label: '赣',
		value: '赣'
	},
	{
		label: '闽',
		value: '闽'
	},
	{
		label: '京',
		value: '京'
	},
	{
		label: '津',
		value: '津'
	},
	{
		label: '渝',
		value: '渝'
	},
	{
		label: '冀',
		value: '冀'
	},
	{
		label: '豫',
		value: '豫'
	},
	{
		label: '云',
		value: '云'
	},
	{
		label: '辽',
		value: '辽'
	},
	{
		label: '黑',
		value: '黑'
	},
	{
		label: '湘',
		value: '湘'
	},
	{
		label: '鲁',
		value: '鲁'
	},
	{
		label: '新',
		value: '新'
	},
	{
		label: '鄂',
		value: '鄂'
	},
	{
		label: '甘',
		value: '甘'
	},
	{
		label: '晋',
		value: '晋'
	},
	{
		label: '蒙',
		value: '蒙'
	},
	{
		label: '陕',
		value: '陕'
	},
	{
		label: '吉',
		value: '吉'
	},
	{
		label: '贵',
		value: '贵'
	},
	{
		label: '粤',
		value: '粤'
	},
	{
		label: '青',
		value: '青'
	},
	{
		label: '藏',
		value: '藏'
	},
	{
		label: '川',
		value: '川'
	},
	{
		label: '宁',
		value: '宁'
	},
	{
		label: '琼',
		value: '琼'
	},
	{
		label: '使',
		value: '使'
	},
	{
		label: '领',
		value: '领'
	},

]
export const vehicleColors = {
	'0': '蓝',
	'1': '黄',
	'2': '黑',
	'3': '白',
	'4': '渐变绿',
	'5': '黄绿双拼',
	'6': '蓝白渐变'
}
export const payTypeOptions = [{
		value: '20000601',
		label: '支付宝扫码支付',
	}, {
		value: '00000000',
		label: '现金支付',
	}, {
		value: '8000000',
		label: 'POS支付',
	},
	{
		value: '10000501',
		label: '微信扫码支付',
	}, ...payTypePartOptions
]
export const payTypePartOptions = [{
	value: '10000601',
	label: '微信支付',
}, {
	value: '90000201',
	label: '账户余额支付',
}]
export const goodsTypeOptions = [{
		label: "套装",
		value: "3",
	},
	{
		label: "单OBU",
		value: "2",
	},
	{
		label: "单卡",
		value: "1",
	},
]
export const gxCardTypeOptions = [{
		value: '0',
		label: '储值卡',
	}, {
		value: '2',
		label: '预付费记账卡',
	}, {
		value: '3',
		label: '预付费绑定记账卡',
	}, {
		value: '4',
		label: '后付费绑定记账卡',
	}, {
		value: '5',
		label: '捷通日日通记账卡',
	},
	// {
	// 	value: '6',
	// 	label: '捷通月月行记账卡',
	// },
]
export const departmentOption = [{
	value: 1,
	label: '本部',
}, {
	value: 2,
	label: '分支',
}]
export const payStatusOptions = [{
	value: '0',
	label: '订单创建',
}, {
	value: '1',
	label: '支付中',
}, {
	value: '2',
	label: '支付成功',
}, {
	value: '3',
	label: '支付失败',
}, {
	value: '4',
	label: '退款成功',
}, {
	value: '5',
	label: '退款失败',
}, {
	value: '6',
	label: '退款中',
}]

export const plateColorToFirstMap = new Map([
	['0', "car-license-A"],
	['1', "car-license-B"],
	['2', "car-license-C"],
	['3', "car-license-D"],
	['4', "car-license-E"],
	['5', "car-license-F"],
	['6', "car-license-G"]
])

//OBU状态字典
export const obuStatus = {
	'1': '初始化',
	'2': '未激活',
	'3': '正常',
	'4': '维护',
	'5': '已回收',
	'6': '损坏',
	'7': '挂失',
	'8': '已过户',
	'9': '已注销',
	'10': '有偿更换',
	'11': '已退货',
	'12': '正在维修',
	'13': '已免费更换',
	'15': '车型不符',
	'16': '异常停用',
	'17': '停用',
	'18': '无OBU注销',
}
//CPU卡状态
export const cpuStatus = {
	'0': '未发行',
	'1': '正常',
	'2': '挂失',
	'3': '已更换',
	'4': '已注销',
	'5': '已过户',
	'7': '挂失已补领',
	'8': '坏卡',
	'9': '异常停用',
	'10': '已退货',
	'11': '已免费更换',
	'14': '合作机构状态名单',
	'15': '车型不符',
	"16": "卡片停用"
}
// 支付状态
export const payStatus = [{
		value: '1',
		label: '支付中'
	},
	{
		value: '2',
		label: '支付成功'
	},
	{
		value: '3',
		label: '支付失败'
	},
	{
		value: '4',
		label: '已退款'
	}
]

// 错误提示信息
export const errorMessage = [
	// {
	// 	value: 7010,
	// 	label: '密码错误,请重新输入'
	// },
	// {
	// 	value: 7001,
	// 	label: '账号不存在,请先注册'
	// },
	// {
	// 	value: 9999,
	// 	label: "图片验证码验证失败,请重新输入"
	// },
	// {
	// 	value: 995,
	// 	label: "短信验证码错误，请仔细检查或重新获取短信验证码"
	// },
	// // {
	// // 	value: 7021,
	// // 	label: '密码过于简单，请包含数字、字母及， 。 * 等特殊字符'
	// // }
]

export const businessType = [{
		value: '1',
		label: '新发行'
	},
	{
		value: '2',
		label: '卡片更换'
	}, {
		value: '3',
		label: 'obu更换'
	}
]

export const applyStatus = [{
		value: '1',
		label: '开始'
	},
	{
		value: '2',
		label: '流转中'
	},
	{
		value: '3',
		label: '异常驳回'
	},
	{
		value: '4',
		label: '处理完成'
	}
]
export const codeStatus = [{
		value: '1000',
		label: '提交申请'
	},
	{
		value: '1010',
		label: "待审批"
	},
	{
		value: '1020',
		label: "系统销售下单"
	},
	{
		value: '1030',
		label: "支付订单"
	},
	{
		value: '1040',
		label: "快递发货"
	},
	{
		value: '2000',
		label: "更换申请"
	},
	{
		value: '2010',
		label: "待审批"
	},
	{
		value: '2020',
		label: "寄回旧设备"
	},
	{
		value: '2030',
		label: "确认收货"
	},
	{
		value: '2040',
		label: "系统销售下单"
	},
	{
		value: '2050',
		label: "订单支付"
	},
	{
		value: '2060',
		label: "快递发货"
	},
	{
		value: '1050',
		label: "完成"
	},
]
export const cardType = {
	'22': '储值',
	'23': '记账'
}

//集装箱退费申请状态
export const handleModeList = [{
		value: '',
		label: '全部'
	},
	{
		value: 1,
		label: '审核中'
	},
	{
		value: 2,
		label: '已通过'
	},
	{
		value: 3,
		label: '已驳回'
	},
	{
		value: 4,
		label: '退费(处理)中'
	},
	{
		value: 5,
		label: '退费(退款)中'
	},
	{
		value: 6,
		label: '已退费'
	},
	{
		value: 7,
		label: '退费失败'
	},
	{
		value: 8,
		label: '放弃申请'
	},
]



// 退款信息

export const returnStatus = [{
		value: '4',
		label: '退款成功'
	},
	{
		value: '5',
		label: '退款失败'
	},
	{
		value: '6',
		label: '退款中'
	}
]



//圈存状态
export const acLoadStatus = [{
		value: '0',
		label: '未圈存'
	},
	{
		value: '1',
		label: '已圈存'
	},
	{
		value: '2',
		label: '圈存中'
	},
	{
		value: '3',
		label: '已转存'
	},
	{
		value: '4',
		label: '转存中'
	}
]

//
export const vehicleColorPicker = [{
		label: "蓝",
		value: "0"
	},
	{
		label: "黄",
		value: "1"
	},
	{
		label: "黑",
		value: "2"
	},
	{
		label: "白",
		value: "3"
	},
	{
		label: "渐变绿",
		value: "4"
	},
	{
		label: "黄绿双拼",
		value: "5"
	},
	{
		label: "蓝白渐变",
		value: "6"
	},
]

//优惠券类型
export const coupons = [{
		value: '0',
		label: "抵价券"
	},
	{
		value: '1',
		label: '商城消费券'
	},
	{
		value: '2',
		label: '折扣券'
	},
	{
		value: '3',
		label: '充值加赠券'
	},

]

//充值金额
export const amountList = [{
		label: "100",
		value: "100"
	},
	{
		label: "200",
		value: "200"
	},
	{
		label: "500",
		value: "500"
	},
	{
		label: "1000",
		value: "1000"
	},
]
// 发票申请类型 
export const invoiceApplyTypeOptions = [{
		label: "设备票",
		value: "1"
	},
	{
		label: "权益票",
		value: "2"
	},
	{
		label: "月结账单服务费滞纳金",
		value: "3"
	},
	{
		label: "次次顺服务费滞纳金",
		value: "4"
	}
]
// 发票申请状态
export const taxStatusOptions = [{
		value: '0',
		label: "未开票"
	},
	{
		value: '1',
		label: '开票中'
	},
	{
		value: '2',
		label: '已开票'
	},
	{
		value: '3',
		label: '已拒绝'
	}
]

export const invoiceType = [
	// {
	// 	value: '1',
	// 	label: "设备票"
	// },
	{
		value: '2',
		label: "权益票"
	},
	{
		value: '4',
		label: "次次顺技术支持服务费"
	},
	// {
	// 	value: '3',
	// 	label: "月结账单服务费滞纳金"
	// }

]

export const sceneType = [{
		label: "全部",
		value: "",
	}, {
		label: "高速消费",
		value: "1",
	},
	{
		label: "停车场",
		value: "2",
	},
	{
		label: "加油站",
		value: "3",
	},
	// {
	// 	label: "服务区消费",
	// 	value: "4",
	// },
	// {
	// 	label: "市政拓展",
	// 	value: "5",
	// }

]

export const otherDisputeRefundBankName = [{
		label: '中国工商银行',
		value: '1'
	},
	{
		label: '中国农业银行',
		value: '2'
	},
	{
		label: '中国建设银行',
		value: '3'
	},
	{
		label: '中国银行总行',
		value: '4'
	},
	{
		label: '广西农村信用社',
		value: '5'
	},
	{
		label: '桂林银行股份有限公司',
		value: '6'
	},
	{
		label: '中国邮政储蓄银行',
		value: '7'
	},
	{
		label: '广西北部湾银行',
		value: '8'
	},
	{
		label: '其他',
		value: '9'
	},
]

export const disputeRefundBankName = [{
		label: "中国工商银行",
		value: "1"
	},
	{
		label: "中国农业银行",
		value: "2"
	},
	{
		label: "中国银行总行",
		value: "3"
	},
	{
		label: "中国建设银行",
		value: "4"
	},
	{
		label: "中国邮政储蓄银行",
		value: "5"
	},
	{
		label: "中国民生银行",
		value: "6"
	},
	{
		label: "中国光大银行",
		value: "7"
	},
	{
		label: "中信银行",
		value: "8"
	},
	{
		label: "广西农村信用社",
		value: "9"
	},
	{
		label: "广西北部湾银行",
		value: "10"
	},
	{
		label: "上海浦东发展银行",
		value: "11"
	},
	{
		label: "桂林银行股份有限公司",
		value: "12"
	},
	{
		label: "招商银行",
		value: "13"
	},
	{
		label: "兴业银行",
		value: "14"
	},
	{
		label: "平安银行",
		value: "15"
	},
	{
		label: "柳州银行",
		value: "16"
	},
	{
		label: "交通银行",
		value: "17"
	},
	{
		label: "广发银行",
		value: "18"
	},
	{
		label: "华夏银行",
		value: "19"
	},
	{
		label: "内蒙古自治区农村信用社联合社",
		value: "20"
	},
	{
		label: "广东省农村信用社联合社",
		value: "21"
	},
	{
		label: "福建省农村信用社联合社",
		value: "22"
	},
	{
		label: "贵州省农村信用社联合社",
		value: "23"
	},
	{
		label: "山东省农村信用社联合社",
		value: "24"
	},
	{
		label: "河北省农村信用社联合社",
		value: "25"
	},
	{
		label: "湖北省农村信用社联合社",
		value: "26"
	},
	{
		label: "云南省农村信用社联合社",
		value: "27"
	},
	{
		label: "江苏省农村信用社联合社",
		value: "28"
	},
	{
		label: "河南省农村信用社联合社",
		value: "29"
	},
	{
		label: "四川省农村信用社联合社",
		value: "30"
	},
	{
		label: "浙江省农村信用社联合社",
		value: "31"
	},
	{
		label: "湖南省农村信用社联合社",
		value: "32"
	},
	{
		label: "东莞银行股份有限公司",
		value: "33"
	},
	{
		label: "台州银行股份有限公司",
		value: "34"
	},
	{
		label: "贵州银行股份有限公司",
		value: "35"
	},
	{
		label: "渤海银行股份有限公司",
		value: "36"
	},
	{
		label: "衡水银行股份有限公司",
		value: "37"
	},
	{
		label: "厦门银行股份有限公司",
		value: "38"
	},
	{
		label: "兰州银行股份有限公司",
		value: "39"
	},
	{
		label: "江苏银行股份有限公司",
		value: "40"
	},
	{
		label: "上海银行股份有限公司",
		value: "41"
	},
	{
		label: "长沙银行股份有限公司",
		value: "42"
	},
	{
		label: "宁波银行股份有限公司",
		value: "43"
	},
	{
		label: "郑州银行",
		value: "44"
	},
	{
		label: "青岛银行",
		value: "45"
	},
	{
		label: "齐鲁银行",
		value: "46"
	},
	{
		label: "吉林银行",
		value: "47"
	},
	{
		label: "盛京银行",
		value: "48"
	},
	{
		label: "广州银行",
		value: "49"
	},
	{
		label: "北京银行",
		value: "50"
	},
	{
		label: "浙江农商银行",
		value: "51"
	},
	{
		label: "东莞农村商业银行股份有限公司",
		value: "52"
	},
	{
		label: "重庆农村商业银行股份有限公司",
		value: "53"
	},
	{
		label: "浙江网商银行股份有限公司",
		value: "54"
	},
	{
		label: "北京农村商业银行股份有限公司",
		value: "55"
	},
	{
		label: "上海农村商业银行",
		value: "56"
	},
	{
		label: "深圳农村商业银行股份有限公司",
		value: "57"
	},
	{
		label: "合浦国民村镇银行有限责任公司",
		value: "58"
	},
	{
		label: "东兴国民村镇银行有限责任公司",
		value: "59"
	},
	{
		label: "汇丰银行(中国)有限公司上海分行",
		value: "60"
	}
];

//次次顺签约状态
export const ccsSignStatus = [{
		label: '创建',
		value: '0',
	},
	{
		label: '签约中',
		value: '1',
	},
	{
		label: '已签约',
		value: '2',
	},
	{
		label: '重新签约',
		value: '4',
	},
]

export const tollInvoiceType = [{
		value: '',
		label: '全部'
	}, {
		label: '通行费消费发票',
		value: '1',
	},
	{
		label: '通行费充值发票',
		value: '2',
	}
]

export const tollInvoiceStatus = [{
		value: '',
		label: '全部'
	},
	{
		label: '开票中',
		value: '1',
	},
	{
		label: '已完成',
		value: '3',
	},
	{
		label: '开票失败',
		value: '4',
	},
	{
		label: '抬头更换中',
		value: '5',
	},
]

//通行费发票卡状态
export const tollCardStatus = {
	'1': '正常',
	'2': '有卡挂起',
	'3': '无卡挂起',
	'4': '有卡注销',
	'5': '无卡注销',
	'6': '卡挂失',
}

//其他消费发票发票类型
export const otherRecordType = [{
		label: '权益服务费/设备费',
		value: '5',
	},
	{
		label: '次次顺技术支持服务费',
		value: '4',
	},
	{
		label: '注销违约金',
		value: '6',
	},
]

//其他消费发票发票类型（包含月月行）
export const otherRecordAllType = [{
		label: '权益服务费/设备费',
		value: '5',
	},
	{
		label: '次次顺技术支持服务费',
		value: '4',
	},
	{
		label: '月结服务费/滞纳金',
		value: '3',
	},
	{
		label: '注销违约金',
		value: '6',
	},
]