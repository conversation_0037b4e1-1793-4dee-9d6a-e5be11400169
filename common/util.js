function formatTime(time) {
	if (typeof time !== 'number' || time < 0) {
		return time
	}

	var hour = parseInt(time / 3600)
	time = time % 3600
	var minute = parseInt(time / 60)
	time = time % 60
	var second = time

	return ([hour, minute, second]).map(function(n) {
		n = n.toString()
		return n[1] ? n : '0' + n
	}).join(':')
}

function formatLocation(longitude, latitude) {
	if (typeof longitude === 'string' && typeof latitude === 'string') {
		longitude = parseFloat(longitude)
		latitude = parseFloat(latitude)
	}

	longitude = longitude.toFixed(2)
	latitude = latitude.toFixed(2)

	return {
		longitude: longitude.toString().split('.'),
		latitude: latitude.toString().split('.')
	}
}
var dateUtils = {
	UNITS: {
		'年': 31557600000,
		'月': 2629800000,
		'天': 86400000,
		'小时': 3600000,
		'分钟': 60000,
		'秒': 1000
	},
	humanize: function(milliseconds) {
		var humanize = '';
		for (var key in this.UNITS) {
			if (milliseconds >= this.UNITS[key]) {
				humanize = Math.floor(milliseconds / this.UNITS[key]) + key + '前';
				break;
			}
		}
		return humanize || '刚刚';
	},
	format: function(dateStr) {
		var date = this.parse(dateStr)
		var diff = Date.now() - date.getTime();
		if (diff < this.UNITS['天']) {
			return this.humanize(diff);
		}
		var _format = function(number) {
			return (number < 10 ? ('0' + number) : number);
		};
		return date.getFullYear() + '/' + _format(date.getMonth() + 1) + '/' + _format(date.getDay()) + '-' +
			_format(date.getHours()) + ':' + _format(date.getMinutes());
	},
	parse: function(str) { //将"yyyy-mm-dd HH:MM:ss"格式的字符串，转化为一个Date对象
		var a = str.split(/[^0-9]/);
		return new Date(a[0], a[1] - 1, a[2], a[3], a[4], a[5]);
	}
};

// 统一格式化 money，保留两位
function formatMoney(money) {
	return parseFloat(money + "").toFixed(2);
}

function moneyFilter(val) {
	let value = val;
	if (value == 0) return value;
	value = value / 100;
	return toDecimal2(value);
}

function toDecimal2(x) {
	var f = parseFloat(x);
	if (isNaN(f)) {
		return false;
	}
	var f = Math.round(x * 100) / 100;
	var s = f.toString();
	var rs = s.indexOf(".");
	if (rs < 0) {
		rs = s.length;
		s += ".";
	}
	while (s.length <= rs + 2) {
		s += "0";
	}
	return s;
}

//身份证号码校验
function checkIdCard(card) {
	// 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X 
	var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
	if (!reg.test(card) || !card) {
		return false;
	} else {
		return true;
	}
}

//手机号码校验
function checkPhone(phone) {
	if (!(/^1\d{10}$/.test(phone)) || !phone) {
		return false;
	} else {
		return true;
	}
}

function checkEmail(email) {
	if (!(/\w@\w*\.\w/.test(email)) || !email) {
		return false;
	} else {
		return true;
	}
}

function checkEmailReg(email) {
	if (!(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) || !email) {
		return false;
	} else {
		return true;
	}
}

//保留两位小数校验
function twoDecimal(val) {
	if (!(/^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/.test(val)) || !val) {
		return false
	} else {
		return true
	}
}

//函数防抖
function debounce(fn, wait = 500) {
	let timer = null;
	return function() {
		let context = this;
		let args = arguments;
		if (timer) {
			clearTimeout(timer);
			timer = null;
		}
		timer = setTimeout(function() {
			fn.apply(context, args);
		}, wait);
	};
};

//函数节流
function throttle(fn, interval = 500) {
	let timer = null;
	let firstTime = true;

	return function(...args) {
		if (firstTime) {
			// 第一次加载
			fn.apply(this, args);
			return firstTime = false;
		}
		if (timer) {
			// 定时器正在执行中，跳过
			return;
		}
		timer = setTimeout(() => {
			clearTimeout(timer);
			timer = null;
			fn.apply(this, args);
		}, interval);
	};
}

//脱敏ETC用户名
function noPassByName(str) {
	if (null != str && str != undefined) {
		if (str.length <= 2 && str.length > 0) {
			return "*" + str.substring(1, str.length);
		} else {
			return "**" + str.substring(2, str.length);
		}
	} else {
		return "";
	}
}

function formatHandle(time, format) {
	var t = new Date(time);
	var tf = function(i) {
		return (i < 10 ? '0' : '') + i
	};
	// console.log(t, 'formatHandle')
	return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
		switch (a) {
			case 'yyyy':
				return tf(t.getFullYear());
				break;
			case 'MM':
				return tf(t.getMonth() + 1);
				break;
			case 'mm':
				return tf(t.getMinutes());
				break;
			case 'dd':
				return tf(t.getDate());
				break;
			case 'HH':
				return tf(t.getHours());
				break;
			case 'ss':
				return tf(t.getSeconds());
				break;
		}
	})
}

//脱敏ETC卡号
function noPassByCardNo(str) {
	if (null != str && str != undefined) {
		var pat = /(\d{3})\d*(\d{4})/;
		return str.replace(pat, '$1*************$2');
	} else {
		return "";
	}

}

function checkBluetoothObu(obu) {
	// 一、OBU生产年份≥19，则为蓝牙OBU。
	// 二、OBU生产年份＜19，则判断OBU类别。
	// OBU类别=1或2或8，则为非蓝牙OBU
	// OBU类别=其他数字，则为蓝牙OBU
	if (null != obu && obu != undefined && obu != '') {
		let obuDate = obu.substring(6, 4)
		console.log('obuDate', obuDate)
		let obuType = obu.substring(8, 7)
		console.log('obuType', obuType)
		if (obuDate < 19) {
			if (obuType == 1 || obuType == 2 || obuType == 8) {
				return false
			}
		}
		return true
	} else {
		return false
	}
}
module.exports = {
	formatTime: formatTime,
	formatLocation: formatLocation,
	dateUtils: dateUtils,
	formatMoney,
	checkIdCard,
	checkPhone,
	checkEmail,
	checkEmailReg,
	debounce,
	throttle,
	moneyFilter,
	twoDecimal,
	noPassByName,
	noPassByCardNo,
	checkBluetoothObu,
	formatHandle
}