var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
var util = {
	getTimestamp: () => {
		var myDate = new Date();
		return dayjs(myDate).format('YYYY-MM-DD HH:mm:ss');
	}
}
var logger = wx && wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : {}

//---------------------------------------------------
//true:仅当体验版在手机上想查看Log时间时，设置为true
//false:by Default
var config_add_timestamp = true;

//是否使用实时Log
var config_use_logger = false;
//---------------------------------------------------
// #ifdef MP-WEIXIN
config_use_logger = process.env.NODE_ENV != 'development'
// #endif

module.exports = {
	debug: debug,
	error: error,
	warn: warn,
	info: info,
}

function debug(tag, msg) {
	if (msg === undefined)
		msg = "";

	if (config_add_timestamp) {
	
		if (config_use_logger)
			logger.debug(util.getTimestamp() + " " + tag, msg);
	} else {
		
		if (config_use_logger)
			logger.debug(tag, msg);
	}
}

function error(tag, msg) {
	if (msg === undefined)
		msg = "";

	if (config_add_timestamp) {
		
		if (config_use_logger)
			logger.error(util.getTimestamp() + " " + tag, msg);
	} else {
		
		if (config_use_logger)
			logger.error(tag, msg);
	}
}

function warn(tag, msg) {
	if (msg === undefined)
		msg = "";

	if (config_add_timestamp) {
		if (config_use_logger)
			logger.warn(util.getTimestamp() + " " + tag, msg);
	} else {
		
		if (config_use_logger)
			logger.warn(tag, msg);
	}
}

function info(tag, msg) {
	 console.log(tag, msg,process.env.NODE_ENV)
	if (msg === undefined)
		msg = "";

	if (config_add_timestamp) {
		if (config_use_logger)
			logger.info(util.getTimestamp() + " " + tag, msg);
	} else {
		
		if (config_use_logger)
		   
			logger.info(tag, msg);
	}
}