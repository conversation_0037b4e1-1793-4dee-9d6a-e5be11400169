import {
	getLoginUserInfo
} from '@/common/storageUtil.js'
import vue from 'vue';
export const sendAccountOpenId = function() {
	if (!(getLoginUserInfo() && getLoginUserInfo().userNo)) return;
	let params = {
		userNo: getLoginUserInfo().userNo
	}
	vue.prototype.$request
		.post(vue.prototype.$interfaces.getAccountOpenId, {
			data: params
		}).then(res => {
			if (res.code == 200 && res.data) {
				try {
					uni.setStorageSync('accountOpenId', JSON.stringify(res.data));
				} catch (e) {

				}

			}
		})
}
