import {
	payStatus,
	cpuStatus,
	obuStatus,
	vehicleColors,
	VehicleClassType,
	vehicleType,
	payStatusOptions,
	goodsTypeOptions,
	specialCardType,
	errorMessage,
	businessType,
	applyStatus,
	gxCardTypeOptions,
	codeStatus,
	returnStatus,
	acLoadStatus,
	coupons,
	invoiceApplyTypeOptions,
	taxStatusOptions,
	ccsSignStatus,
	tollInvoiceType,
	tollInvoiceStatus,
	tollCardStatus
} from "../const/optionData.js";
import store from "@/store"
export function getVehicleColor(val) {
	return vehicleColors[val] || ''
}
export function getVehicleClassType(val) {
	return VehicleClassType[val] || ''
}
export function getVehicleType(val) {
	return vehicleType[val] || ''
}
export const payStatusOptionsFilter = function(val) {
	for (let i = 0; i < payStatusOptions.length; i++) {
		if (payStatusOptions[i].value == val && val) {
			return payStatusOptions[i].label
		}
	}
	return ''
}
export const goodsTypeFilter = function(val) {
	for (let i = 0; i < goodsTypeOptions.length; i++) {
		if (goodsTypeOptions[i].value == val && val) {
			return goodsTypeOptions[i].label
		}
	}
	return ''
}
//OBU状态
export function getObuStatus(val) {
	return obuStatus[val] || ''
}
//获取CPU卡状态
export function getCpuStatus(val) {
	return cpuStatus[val] || ''
}
// 支付信息
export const getPayStatus = function(val) {
	for (let i = 0; i < payStatus.length; i++) {
		if (payStatus[i].value == val && val) {
			return payStatus[i].label
		}
	}
	return ''
}

//错误提示信息
export function getErrorMessage(val) {
	for (let i = 0; i < errorMessage.length; i++) {
		if (val && errorMessage[i].value == val) {
			return errorMessage[i].label;
		}
	}
	return ''

}

//订单类型
export function getBusinessType(val) {
	for (let i = 0; i < businessType.length; i++) {
		if (val && businessType[i].value == val) {
			return businessType[i].label;
		}
	}
	return ''

}

// 订单所处节点 codeStatus
export function getCodeStatus(val) {
	for (let i = 0; i < codeStatus.length; i++) {
		if (val && codeStatus[i].value == val) {
			return codeStatus[i].label;
		}
	}
	return ''

}
// 订单处理状态

export function getApplyStatus(val) {
	for (let i = 0; i < applyStatus.length; i++) {
		if (val && applyStatus[i].value == val) {
			return applyStatus[i].label;
		}
	}
	return ''

}
// 卡片类型
export function gxCardTypeFilter(val) {
	let gxCardTypeAllOptions = store.getters['dict/gxCardTypeAllOptions']
	for (let i = 0; i < gxCardTypeAllOptions.length; i++) {
		if (gxCardTypeAllOptions[i].value == val && val) {
			return gxCardTypeAllOptions[i].label
		}
	}
	return ''

}
// 退款状态
export function returnStatusFilter(val) {
	for (let i = 0; i < returnStatus.length; i++) {
		if (val && returnStatus[i].value == val) {
			return returnStatus[i].label;
		}
	}
	return '未退款'

}
// 圈存状态
export function acLoadStatusFilter(val) {
	for (let i = 0; i < acLoadStatus.length; i++) {
		if (val && acLoadStatus[i].value == val) {
			return acLoadStatus[i].label;
		}
	}
	return '未圈存'

}

// coupons
export function getCoupons(val) {
	for (let i = 0; i < coupons.length; i++) {
		if (val && coupons[i].value == val) {
			return coupons[i].label;
		}
	}
	return ''

}

//次次顺签约状态
export function getCcsSignStatus(val) {
	for (let i = 0; i < ccsSignStatus.length; i++) {
		if (val && ccsSignStatus[i].value == val) {
			return ccsSignStatus[i].label;
		}
	}
	return ''
}


// 发票申请类型
export function invoiceApplyTypeFilter(val) {
	for (let i = 0; i < invoiceApplyTypeOptions.length; i++) {
		if (val && invoiceApplyTypeOptions[i].value == val) {
			return invoiceApplyTypeOptions[i].label;
		}
	}
	return ''
}
// 发票申请类型
export function taxStatusFilter(val) {
	for (let i = 0; i < taxStatusOptions.length; i++) {
		if (val && taxStatusOptions[i].value == val) {
			return taxStatusOptions[i].label;
		}
	}
	return ''
}

// 通行费发票类型
export function getTollInvoiceType(val) {
	for (let i = 0; i < tollInvoiceType.length; i++) {
		if (val && tollInvoiceType[i].value == val) {
			return tollInvoiceType[i].label;
		}
	}
	return ''
}

// 通行费发票状态
export function getTollInvoiceStatus(val) {
	for (let i = 0; i < tollInvoiceStatus.length; i++) {
		if (val && tollInvoiceStatus[i].value == val) {
			return tollInvoiceStatus[i].label;
		}
	}
	return ''
}

export function getTollCardStatus(val) {
	return tollCardStatus[val] || ''
}