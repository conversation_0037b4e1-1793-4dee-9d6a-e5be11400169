import vue from "vue";
import {
	getAccountId
} from '@/common/storageUtil.js';
import {request} from './requestFunc.js';

//获取车辆列表
export function getCarList(accountId, cb, errorFunc, catchFunc) {
	let page = 1
	let pageNo = 1
	let allList = []
	let data = {
		'data': {
			accountId: accountId,
			pageNo: 1,
			pageSize: 100
		}
	};
	request(vue.prototype.$interfaces.userVehiclePage, data, (res) => {
		pageNo = Math.ceil(res.totalCount / 100) < 1 ? 1 : Math.ceil(res.totalCount / 100)
		allList = res.data
		// console.log('页数')
		// console.log(pageNo)
		if (pageNo > 1) {
			for (let i = 1; i < pageNo; i++) {
				page++
				getMoreCar(accountId, cb, res, page, pageNo, allList)
			}
		} else {
			res.allList = []
			res.listData = allList
			cb(res)
			page = 1
			allList = []
		}

	}, (msg) => {
		errorFunc(msg)
	}, (err) => {
		catchFunc(err)
	})
}
// 用于分页查车
function getMoreCar(accountId, cb, res, page, pageNo, allList) {
	let params = {
		accountId: accountId,
		pageNo: page,
		pageSize: 100
	};
	let data = {
		'data': params
	};
	request(vue.prototype.$interfaces.userVehiclePage, data, (res) => {
		allList = [...allList, ...res.data]
		if (page === pageNo) {
			res.allList = []
			res.listData = allList
			cb(res)
			page = 1
			allList = []
		}
	})
}

//获取车辆信息
export function getCarInfo(list, cb, errorFunc, catchFunc) {
	if (list.length > 0) {
		list.forEach((item, index) => {
		let data = {
			'data': {
		
				accountId: getAccountId(),
				vehicleId: item.vehicleId
			}
		};
		request(vue.prototype.$interfaces.userVehicleQuery, data, (res) => {
			cb(res, item)
		}, (msg) => {
			errorFunc(item)
		}, (err) => {
			catchFunc(err)
		})
		})
	} else {
		catchFunc()
	}
}
//车辆发行状态查询
export function carStatus(list, cb, errorFunc, catchFunc) {
	if (list.length > 0) {
		list.forEach((item, index) => {
			if (item.registeredType == "3") {
				let data = {

					'data': {

						vehicleId: item.vehicleId
					}
				};
				request(vue.prototype.$interfaces.issueStatus, data, (res) => {
					console.log('前装发行状态', item.plateNum, res.data)
					cb(res, item)
				}, (msg) => {
					errorFunc(item)
				}, (err) => {
					catchFunc(err)
				})
			} else {
				console.log('线下办理')
				errorFunc(item)
			}
		})
	} else {
		catchFunc()
	}
}
//获取OBU信息
export function carObuInfo(list, cb, errorFunc, catchFunc) {
	console.log(list,'---++++++')
	if (list.length > 0) {
		list.forEach((item, index) => {

			// 发行
			console.log('获取OBU信息',item.vehicleId);
			if (item && item.obuId) {
				let data = {

					'data': {
						obuId:item.obuId,
						accountId: getAccountId(),
						vehicleId: item.vehicleId
					}
				};
				request(vue.prototype.$interfaces.userOBUQuery, data, (res) => {
					cb(item, res)
				}, (msg) => {
					errorFunc(msg)
				}, (err) => {
					catchFunc(err)
				})
			} else {
				cb(item, '')
			}
		});
	} else {
		catchFunc()
	}
}
