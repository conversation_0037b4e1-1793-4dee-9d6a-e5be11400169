import {
	verifyMd5Sign,
	md5Sign
} from '@/common/security/sign-util.js';
import {
	decryption,
	encryption
} from '@/common/security/security-util.js';
import {setTicket,
		setMd5Key,
		setAesKey,} from "@/common/storageUtil.js"
// 解密响应参数
let decryptionParams = function(params, aesKey,md5Key) {
	// console.log(aesKey,'------aesKey',md5Key,"------md5Key",params,'--------------params')
	//console.log(aesKey,md5Key);
	//1.验签
	let flag = !verifyMd5Sign(params,md5Key);
	if (flag) {
		// console.log('验签失败',aesKey,'------aesKey',md5Key,"------md5Key",params,'--------------params')
		console.log('验签失败',aesK<PERSON>,md5<PERSON><PERSON>);
		return;
	};
	//2.解密
	
	return decryption(params.encryptionData, aesKey);
}
// 加密请求参数
let encryptionParams = function(content,encryptionData, aesKey,md5Key) {
	
	// 生成密文
	let _encryptionData = encryption(JSON.stringify(encryptionData), aesKey);
    
	_encryptionData = _encryptionData || ''
	// 定义参数
	let postParams = {
        ...content,
		encryptionData: _encryptionData
	}
	// 生成签名
	postParams.sign = md5Sign(postParams,md5Key); 
	return postParams;

}
export {
	decryptionParams,
	encryptionParams
}
