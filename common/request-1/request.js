/**
 * version 1.1.7
 */
import {
	decryptionParams,
	encryptionParams
} from "./security.js"
import {
	dateFtt
} from '@/common/helper.js';
import {
	getAccessToken,
	getTicket,
	getMd5Key,
	getAesKey,
	setTicket
} from '@/common/storageUtil.js';

class Request {

	constructor(config = {}) {
		this.config = {};
		this.config.baseUrl = config.baseUrl ? config.baseUrl : '';
		this.config.dataType = config.dataType ? config.dataType : 'json';
		this.config.responseType = config.responseType ? config.responseType : 'text';
		this.config.decodingKey = config.decodingKey ? config.decodingKey : '';
		this.config.header = config.header ? config.header : {};
		this.reqInterceptors = null;
		this.resInterceptors = null;
		this.interceptors = {
			request: fn => {
				this.reqInterceptors = fn;
			},
			response: fn => {
				this.resInterceptors = fn;
			}
		}
		//publicKey=config.decodingKey;
	}
	async get(interfaces, config = {}) {
		return this._request('get', interfaces, config);
	}
	async tpost(interfaces, config = {}) {
		return this._request('post', interfaces, config);
	}
	async post(interfaces, config = {}) {
		let aesKey = getAesKey() || this.config.aesKey;
		let md5Key = getMd5Key() || this.config.md5Key;
		let ticket = getTicket() || "";
		if(config.aesKey == this.config.aesKey && config.md5Key == this.config.md5Key){
			aesKey = config.aesKey;
			md5Key = config.md5Key;
			ticket = ''
		}
		console.log('this.config.aesKey',this.config.aesKey)
		console.log('aesKey===>>', 'getTicket--' + getAesKey(), )
		console.log('md5Key===>>', 'getMd5Key--' + getMd5Key(), )
		console.log('ticket===>>', 'getTicket--' + getTicket(), )
		let _encryptionData = config.data;
		if (_encryptionData && ticket) {
			_encryptionData.ticket = ticket
		}
		if (!_encryptionData && ticket) {
			_encryptionData = {
				ticket: ticket
			}
		}
		let content = {
			ticket: ticket,
			appId: this.config.appId,
			method: interfaces.method,
			timestamp: dateFtt("yyyyMMddhhmmss", new Date()),
			signType: 'MD5',
			encryptionType: 'AES'
		}
		console.log(content,'1222222222222222222');
		console.log('请求参数', interfaces.method, JSON.stringify(_encryptionData))
		let params = encryptionParams(content, _encryptionData, aesKey, md5Key);
		params = JSON.parse(JSON.stringify(params));
		console.log('加密后参数', params)
		let data = {
			data: params
		}
		return this._request('post', interfaces.url, data);
	}
	async put(interfaces, config = {}) {
		return this._request('put', interfaces, config);
	}
	async delete(interfaces, config = {}) {
		return this._request('delete', interfaces, config);
	}
	setConfig(config = {}) {
		this.config = this._deepCopy(this._merge(this.config, config));
	}
	getConfig() {
		return this.config;
	}
	_request(type, url, params = {}) {
		let data = params.data ? params.data : '';
		let authorization = 'Bearer ' + getAccessToken();
		let config = {
			data: data,
			method: type,
			header: {
				Authorization: authorization,
				appId: this.config.appId
			}
		}
		const _this = this;
		let newConfig = this._deepCopy(this._merge(this.config, config));

		let lastConfig = {};
		if (this.reqInterceptors && typeof this.reqInterceptors === 'function') {
			let reqInterceptors = this.reqInterceptors(newConfig);
			if (!reqInterceptors && process.env.NODE_ENV === "development") {
				console.warn('请求被拦截，此消息仅在开发环境显示。')
				return false;
			} else if (Object.prototype.toString.call(reqInterceptors) === "[object Promise]") {
				return reqInterceptors;
			}
			lastConfig = this._deepCopy(reqInterceptors);
		} else {
			lastConfig = this._deepCopy(newConfig);
		}
		let _url = this._formatUrl(lastConfig.baseUrl, url);
		let _data = lastConfig.data ? lastConfig.data : {};
		let _header = lastConfig.header ? lastConfig.header : '';
		let _dataType = lastConfig.dataType ? lastConfig.dataType : '';
		let _responseType = lastConfig.responseType ? lastConfig.responseType : '';
		console.log(_url, '_url')
		return new Promise((resolve, reject) => {
			uni.request({
				url: _url,
				method: lastConfig.method,
				data: _data,
				header: _header,
				dataType: _dataType,
				responseType: _responseType,
				async complete(response) {
					let res = response;
					if (_this.resInterceptors && typeof _this.resInterceptors === 'function') {
						let resInterceptors = _this.resInterceptors(res);

						if (!resInterceptors) {
							reject('返回值已被您拦截！');
							return;
						} else if (Object.prototype.toString.call(resInterceptors) ===
							"[object Promise]") {
							try {
								let promiseRes = await resInterceptors;
								resolve(promiseRes)
							} catch (error) {
								reject(error)
							}
						} else {
							resolve(resInterceptors)
						}
					}
					const {
						code,
						msg,
						receiveTime,
						bizContent
					} = res;
					// console.log("res-------",JSON.stringify(res));
					if (code === "00") {
						let resultData = decrypt(bizContent, decodingKey)
						let returnData = JSON.parse(resultData)
						// console.log("返回参数：",returnData )
						if (returnData && returnData.statusCode) {
							if (returnData.statusCode === '0') {
								resolve(JSON.parse(returnData.bizContent));
							} else {
								reject(returnData.errorMsg);
							}
						} else {
							resolve(returnData);
						}
					} else if (code === "99") {
						//console.log("返回参数：",msg);
						reject(msg);
					} else if (code === "01") {
						reject("无效的token");
					} else {
						resolve(res); // 这里兼容前一版
					}
				}
			});
		})
	}
	_formatUrl(baseUrl, url) {
		if (!baseUrl) return url;
		let formatUrl = '';
		const baseUrlEndsWithSlash = baseUrl.endsWith('/');
		const urlStartsWithSlash = url.startsWith('/');
		if (baseUrlEndsWithSlash && urlStartsWithSlash) {
			formatUrl = baseUrl;
		} else if (baseUrlEndsWithSlash || urlStartsWithSlash) {
			formatUrl = baseUrl;
		} else {
			formatUrl = baseUrl;
		}
		return formatUrl + url;
	}
	_merge(oldConfig, newConfig) {
		let mergeConfig = this._deepCopy(oldConfig);
		if (!newConfig || !Object.keys(newConfig).length) return mergeConfig;
		for (let key in newConfig) {
			if (key !== 'header') {
				mergeConfig[key] = newConfig[key];
			} else {
				if (Object.prototype.toString.call(newConfig[key]) === '[object Object]') {
					for (let headerKey in newConfig[key]) {
						mergeConfig[key][headerKey] = newConfig[key][headerKey];
					}
				}
			}
		}
		return mergeConfig;
	}
	_deepCopy(obj) {

		var {
			...result
		} = obj;
		return result;
	}
}

if (!global.$request) {
	global.$request = new Request();
}

export default global.$request;
