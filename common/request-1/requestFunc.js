import  vue from "vue";
import {
	Base64
} from '@/js_sdk/js-base64/base64.min.js';
import {
	clientConfig
} from '@/common/const/systemData.js';
import {
	getTokenId,
	getAccountId,
	setAccessToken
} from '@/common/storageUtil.js';

// 公共请求
export function request(url, data, cb, errorFunc, catchFunc) {
	console.log(url, data)
	global.$request.post(url, data).then((res) => {
		cb(res)
	}, (msg) => {
		if (errorFunc) {
			errorFunc(msg)
		}
	}).catch((err) => {
		if (catchFunc) {
			catchFunc(err)
		}
	})
}

//获取车辆列表
export function getCarList(accountId, cb, errorFunc, catchFunc) {
	let page = 1
	let pageNo = 1
	let allList = []
	let data = {
		'data': {
			accountId: accountId,
			pageNo: 1,
			pageSize: 100
		}
	};
	request(vue.prototype.$interfaces.userVehiclePage, data, (res) => {
		pageNo = Math.ceil(res.totalCount / 100) < 1 ? 1 : Math.ceil(res.totalCount / 100)
		allList = res.data
		// console.log('页数')
		// console.log(pageNo)
		if (pageNo > 1) {
			for (let i = 1; i < pageNo; i++) {
				page++
				getMoreCar(accountId, cb, res, page, pageNo, allList)
			}
		} else {
			res.allList = []
			res.listData = allList
			cb(res)
			page = 1
			allList = []
		}

	}, (msg) => {
		errorFunc(msg)
	}, (err) => {
		catchFunc(err)
	})
}
// 用于分页查车
function getMoreCar(accountId, cb, res, page, pageNo, allList) {
	let params = {
		accountId: accountId,
		pageNo: page,
		pageSize: 100
	};
	let data = {
		'data': params
	};
	request(vue.prototype.$interfaces.userVehiclePage, data, (res) => {
		allList = [...allList, ...res.data]
		if (page === pageNo) {
			res.allList = []
			res.listData = allList
			cb(res)
			page = 1
			allList = []
		}
	})
}
//车辆发行状态查询
export function carStatus(list, cb, errorFunc, catchFunc) {
	if (list.length > 0) {
		list.forEach((item, index) => {
			if (item.registeredType == "3") {
				let data = {

					'data': {

						vehicleId: item.vehicleId
					}
				};
				request(vue.prototype.$interfaces.issueStatus, data, (res) => {
					cb(res, item)
				}, (msg) => {
					errorFunc(item)
				}, (err) => {
					catchFunc(err)
				})
			} else {
				console.log('线下办理')
				errorFunc(item)
			}
		})
	} else {
		catchFunc()
	}
}
//获取OBU信息
export function carObuInfo(list, cb, errorFunc, catchFunc) {
	if (list.length > 0) {
		list.forEach((item, index) => {
			
			// 发行状态已完成或者线下办理都要查obu
			if (item && item.status == 1) {
				let data = {

					'data': {
						accountId: getAccountId(),
						vehicleId: item.vehicleId
					}
				};
				request(vue.prototype.$interfaces.userOBUQuery, data, (res) => {
					cb(item, res)
				}, (msg) => {
					errorFunc(msg)
				}, (err) => {
					catchFunc(err)
				})
			} else {
				cb(item, '')
			}
		});
	} else {
		catchFunc()
	}
}
export function getAccessTokenRequest() {
	let str = clientConfig.clientId + ':' + clientConfig.clientSecurity;
	uni.request({
		url: 'https://micro-gateway.gxjettoll.cn:8443/uaa/oauth/token?grantType=client_credentials',
		method: 'post',
		header: {
			Authorization: 'Base64 ' + Base64.btoa(str)
		},
		success: (res => {
			let data = res.data;
			if (data.code === 200 && data.data && data.data.accessToken) {
				setAccessToken(data.data.accessToken);
			}
		})
	})
}
