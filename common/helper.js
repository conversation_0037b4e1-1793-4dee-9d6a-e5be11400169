export function getParam(key, strURL) {
	return new RegExp('(^|\\?|&)' + key + '=([^&]*)(\\s|&|$)', 'i').test(strURL) ? decodeURIComponent(RegExp.$2.replace(
		/\+/g, ' ')) : '';
}

export function getPlatform() {
	if (/MicroMessenger/.test(window.navigator.userAgent)) {
		// alert('微信客户端'); 
	} else if (/AlipayClient/.test(window.navigator.userAgent)) {
		// alert('支付宝客户端');
	} else {
		// alert('其他浏览器');
	}
}
export function getNetworkType() {
	uni.getNetworkType({
		success: function(res) {
			// console.log(res.networkType)
			return res.networkType;
		}
	});
}
export function getNowDate() {
	var myDate = new Date();
	var newMonth = myDate.getMonth() + 1 < 10 ? '0' + myDate.getMonth() + 1 : myDate.getMonth() + 1;
	var newDay = myDate.getDate() < 10 ? '0' + myDate.getDate() : myDate.getDate();
	var newDate = myDate.getFullYear() + '' + newMonth + '' + newDay + '' + myDate.getHours() + '' + myDate.getMinutes() +
		'' + myDate.getSeconds() + '' + myDate.getMilliseconds();
	return newDate;
}

export function dateFtt(fmt, date) { //author: meizz   
	var o = {
		"M+": date.getMonth() + 1, //月份   
		"d+": date.getDate(), //日   
		"h+": date.getHours(), //小时   
		"m+": date.getMinutes(), //分   
		"s+": date.getSeconds(), //秒   
		"q+": Math.floor((date.getMonth() + 3) / 3), //季度   
		"S+": date.getMilliseconds() //毫秒   
	};
	if (/(y+)/.test(fmt))
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
	for (var k in o)
		if (new RegExp("(" + k + ")").test(fmt))
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
	return fmt;
}

export function isLicensePlate(str) {
	return /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([A-Z]([A-HJ-NP-Z0-9])[A-HJ-NP-Z0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领试][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领试]))$/.test(str);
}

export function isPhone(str) {
	return /^1([38]\d|5[0-35-9]|7[3678])\d{8}$/.test(str);
}

export function isNumorChar(str) {
	return /^[A-Za-z0-9]+$/.test(str);
}

// export default {
// 	getParam,
// 	getPlatform,
// 	getNetworkType,
// 	getNowDate,
// 	dateFtt
// }

export function uni_encodeURIComponent(str) {
	return encodeURIComponent(str);
}

export function uni_decodeURIComponent(str) {
	return decodeURIComponent(str);
}
