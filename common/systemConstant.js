// 银行编码String
const bankCodes = "001,002,003,004,005,006,007,008,009,010,011,012,013,014,015,016,017,018,101,102,103,104,061";
// 通过银行编码获取对应银行名称
const bankCodeToBankNameMap = new Map([
	['001', '工商银行'], // 工商
	['004', '农业银行'], // 农行
	['002', '建设银行'], // 建行
	['005', '交通银行'], // 交通银行
	['003', '中国银行'], // 中国
	['006', '邮政银行'], // 邮政银行
	['007', '招商银行'], // 招商银行
	['008', '上海浦东发展银行'], // 上海浦东发展银行
	['009', '中信银行'], // 中信银行
	['010', '中国光大银行'], // 中国光大银行
	['011', '华夏银行'], // 华夏银行
	['012', '中国民生银行'], // 中国民生银行
	['013', '广发银行'], // 广发银行
	['014', '兴业银行'], // 兴业银行
	['015', '平安银行'], // 平安银行
	['016', '恒丰银行'], // 恒丰银行
	['017', '浙商银行'], // 浙商银行
	['018', '渤海银行'], // 渤海银行
	['103', '银联'], // 银联
	['102', '微信'], // 微信
	['101', '支付宝'], // 支付宝
	['104', '网商银行'], // 网商银行
	['061', '黑龙江农商银行'], // 黑龙江农商银行
]);

// 通过银行编码获取对应银行icon图标
const bankCodeToIconMap = new Map([
	['001', 'icon iconfont icon-yinhang-gongshang'], // 工商
	['004', 'icon iconfont icon-yinhang-nonghang'], // 农行
	['002', 'icon iconfont icon-yinhang-jianshe'], // 建行
	['005', 'icon iconfont icon-jiaotongyinhang-'], // 交通
	['003', 'icon iconfont icon-yinhang-zhongguoyinhang'], // 中国
	['006', 'icon iconfont icon-yinhanglogo-7'], // 邮政银行
	['007', 'icon iconfont icon-yinhang-zhaoshang'], // 招商银行
	['008', 'icon iconfont icon-yinhanglogo-6'], // 上海浦东发展银行
	['009', 'icon iconfont icon-zhongxinyinhang'], // 中信银行
	['010', 'icon iconfont icon-yinhanglogo-3'], // 中国光大银行
	['011', 'icon iconfont icon-huaxiayinhang'], // 华夏银行
	['012', 'icon iconfont icon-zhongguominshengyinhang'], // 中国民生银行
	['013', 'icon iconfont icon-yinhanglogo-4'], // 广发银行
	['014', 'icon iconfont icon-xingyeyinhang'], // 兴业银行
	['015', 'icon iconfont icon-pinganyinhang'], // 平安银行
	['016', 'icon iconfont icon-yinhanglogo-1'], // 恒丰银行
	['017', 'icon iconfont icon-yinhanglogo-'], // 浙商银行
	['018', 'icon iconfont icon-yinhanglogo-2'], // 渤海银行
	['103', 'icon iconfont icon-yinlian1193427easyiconnet'], // 银联
	['102', 'icon iconfont icon-weixin'], // 微信
	['101', 'icon iconfont icon-zhifubao'], // 支付宝
	['104', 'icon iconfont icon-yinhang-wangshang'], // 网商银行
	['061', 'icon iconfont icon-heilongjiangnongshang'], // 黑龙江农商银行
]);
// 通过银行编码获取对应银行图标底色
const bankCodeToIconColorMap = new Map([
	['001', '#CB0002'], // 工商
	['004', '#1B7F6D'], // 农行
	['002', '#103B97'], // 建行
	['005', '#1D2087'], // 交通银行
	['003', '#B00040'], // 中国
	['006', '#108C3E'], // 邮政银行
	['007', '#A61F23'], // 招商银行
	['008', '#1D2087'], // 上海浦东发展银行
	['009', '#D7000F'], // 中信银行
	['010', '#6A1684'], // 中国光大银行
	['011', '#d81e06'], // 华夏银行
	['012', '#5AA572'], // 中国民生银行
	['013', '#6E1318'], // 广发银行
	['014', '#004187'], // 兴业银行
	['015', '#EC681B'], // 平安银行
	['016', '#00428E'], // 恒丰银行
	['017', '#DFB70D'], // 浙商银行
	['018', '#00428E'], // 渤海银行
	['103', '#C30840'], // 银联
	['102', '#3CB035'], // 微信
	['101', '#2594C9'], // 支付宝
	['104', '#108C3E'], // 网商银行
	['061', '#5AA572'], // 黑龙江农商银行
])
// 通过银行编码获取对应银行卡组件底色
const bankCodeToBgMap = new Map([
	['001', '#CB0002'], // 工商
	['004', '#1B7F6D'], // 农行
	['002', '#103B97'], // 建行
	['005', '#1D2087'], // 交通银行
	['003', '#B00040'], // 中国
	['006', '#108C3E'], // 邮政银行
	['007', '#A61F23'], // 招商银行
	['008', '#1D2087'], // 上海浦东发展银行
	['009', '#D7000F'], // 中信银行
	['010', '#6A1684'], // 中国光大银行
	['011', '#d81e06'], // 华夏银行
	['012', '#5AA572'], // 中国民生银行
	['013', '#6E1318'], // 广发银行
	['014', '#004187'], // 兴业银行
	['015', '#EC681B'], // 平安银行
	['016', '#00428E'], // 恒丰银行
	['017', '#DFB70D'], // 浙商银行
	['018', '#00428E'], // 渤海银行
	['103', '#C3084'], // 银联
	['102', '#3CB035'], // 微信
	['101', '#2594C9'], // 支付宝
	['104', '#108C3E'], // 网商银行
	['061', '#5AA572'], // 黑龙江农商银行
])
// 通过状态码显示不同的状态名称
const getVehicleStatus = new Map([
	['1', '完成办理'],
	['2', '未上传车主身份证'],
	['3', '未上传行驶证'],
	['4', '未关联支付渠道'],
	['5', '未完成支付'],
	['6', 'OBU未激活'],
	['7', '撤销/退货完成'],
]);

const cityMotionCode = new Map([
	['北京市', '11'],
	['天津市', '12'],
	['河北省', '13'],
	['山西省', '14'],
	['内蒙古自治区', '15'],
	['辽宁省', '21'],
	['吉林省', '22'],
	['黑龙江省', '23'],
	['上海市', '31'],
	['江苏省', '32'],
	['浙江省', '33'],
	['安徽省', '34'],
	['福建省', '35'],
	['江西省', '36'],
	['山东省', '37'],
	['河南省', '41'],
	['湖北省', '42'],
	['湖南省', '43'],
	['广东省', '44'],
	['广西壮族自治区', '45'],
	['海南省', '46'],
	['重庆市', '50'],
	['四川省', '51'],
	['贵州省', '52'],
	['云南省', '53'],
	['西藏自治区', '54'],
	['陕西省', '61'],
	['甘肃省', '62'],
	['青海省', '63'],
	['宁夏回族自治区', '64'],
	['新疆维吾尔自治区', '65'],
	['台湾省', '71'],
	['香港特别行政区', '81'],
	['澳门特别行政区', '82']
])


const payTypeMap = new Map([
	['1', '支付宝'],
	['2', '微信'],
	['3', '银联'],
])

const cardTypeMap = new Map([
	['0', ''], // 支付宝或者微信
	['1', '信用卡'], // 农行
	['2', '借记卡'], // 建行
	['3', '其他'],
])

// 车牌颜色1位数字:
// 0-蓝色，
// 1-黄色，
// 2-黑色，
// 3-白色，
// 4-渐变绿色，
// 5-黄绿双拼色，
// 6-蓝白渐变色，
// 7-临时牌照，
// 8-未确定
const plateColorToColorMap = new Map([
	['0', "蓝色"],
	['1', "黄色"],
	['2', "黑色"],
	['3', "白色"],
	['4', "渐变绿色"],
	['5', "黄绿双拼色"],
	['6', "蓝白渐变色"],
	['7', "临时牌照"],
	['8', "未确定"],
]);

const orderStatusMap = new Map([
	['2', "未支付"],
	['3', "已支付"],
	['4', "已撤销"],
	['5', "退货中"],
	['6', "已退货"]
]);

const expressStatusMap = new Map([
	['1', "未发货"],
	['2', "已发货"],
	['3', "已激活"],
	['4', "已评价"]
]);

const expressTypeMap = new Map([
	['顺丰', '1'],
	['中通', '2'],
	['圆通', '3'],
	['申通', '4'],
	['韵达', '5'],
	['百世汇通快递', '6'],
	['京东', '7'],
	['EMS', '8'],
	['如风达', '9'],
	['天天', '10'],
	['宅急送', '11'],
	['全峰快递', '12'],
	['国通快递', '13'],
	['优速快递', '14'],
	['德邦', '15'],
	['快捷快递', '16'],
	['自提', '99']
]);

const reverseExpressTypeMap = new Map([
	['1', "顺丰"],
	['2', "中通"],
	['3', "圆通"],
	['4', "申通"],
	['5', "韵达"],
	['6', "百世汇通快递"],
	['7', "京东"],
	['8', "EMS"],
	['9', "如风达"],
	['10', "天天"],
	['11', "宅急送"],
	['12', "全峰快递"],
	['13', "国通快递"],
	['14', "优速快递"],
	['15', "德邦"],
	['16', "快捷快递"],
	['99', "自提"]
]);

const plateColorToClassMap = new Map([
	['0', "car-license-0"],
	['1', "car-license-1"],
	['2', "car-license-2"],
	['3', "car-license-3"],
	['4', "car-license-4"],
	['5', "car-license-5"],
	['6', "car-license-6"],
	// ['7', "car-license-7"],
	// ['8', "car-license-8"],
])
const plateColorToFirstMap = new Map([
	['0', "car-license-A"],
	['1', "car-license-B"],
	['2', "car-license-C"],
	['3', "car-license-D"],
	['4', "car-license-E"],
	['5', "car-license-F"],
	['6', "car-license-G"],
	// ['7', "car-license-7"],
	// ['8', "car-license-8"],
])
const plateColorOptions = ["蓝色", "黄色", "黑色", "白色", "渐变绿色", "黄绿双拼色", "蓝白渐变色"];
const expressTypeOptions = ["顺丰", "中通", "圆通", "申通", "韵达", "百世汇通快递", "京东", "EMS", "如风达", "天天", "宅急送", "全峰快递", "国通快递",
	"优速快递", "德邦", "快捷快递"
];
const expressStatusOptions = ["全部", "未发货", "已发货", "已激活"]; //,"已评价"
const orderTypeOptions = ["新办", "换签", "换卡", "卡签更换"];
const orderStatusOptions = ["全部", "未支付", "已支付", "已撤销", "退货中", "已退货"];
//车辆类型
const vehicleTypeOptions = new Map([
	['1', "一型客车"],
	['2', "二型客车"],
	['3', "三型客车"],
	['4', "四型客车"],
	['11', "一型货车"],
	['12', "二型货车"],
	['13', "三型货车"],
	['14', "四型货车"],
	['15', "五型货车"],
	['16', "六型货车"],
	['21', "一型专项作业车"],
	['22', "二型专项作业车"],
	['23', "三型专项作业车"],
	['24', "四型专项作业车"],
	['25', "五型专项作业车"],
	['26', "六型专业作业车"],
])

// 以下类型为客车
const checkCarType = [
	"大型普通客车",
	"大型双层客车",
	"大型卧铺客车",
	"大型铰接客车",
	"大型越野客车",
	"大型轿车",
	"大型专用客车",
	"大型专用校车",
	"中型普通客车",
	"中型双层客车",
	"中型卧铺客车",
	"中型铰接客车",
	"中型越野客车",
	"中型轿车",
	"中型专用客车",
	"中型专用校车",
	"小型普通客车",
	"小型越野客车",
	"小型轿车",
	"小型专用客车",
	"小型专用校车",
	"小型面包车",
	"微型普通客车",
	"微型越野客车",
	"微型轿车",
	"微型面包车"
]

// 车牌获取对应组件底色
const backColorMap = new Map([{
		icon: "~@/static/cp/bluelan.png",
		title: "蓝色",
		index: "0",
	}, {
		icon: "~@/static/cp/yellow.png",
		title: "黄色",
		index: "1",
	}, {
		icon: "~@/static/cp/000.png",
		title: "黑色",
		index: "2",
	},
	{
		icon: "~@/static/cp/bw.png",
		title: "白色",
		index: "3",
	},
	{
		icon: "~@/static/cp/gradualGreen.png",
		title: "渐变绿色",
		index: "4",
	}, {
		icon: "~@/static/cp/yellowgreen.png",
		title: "黄绿双拼色",
		index: "5",
	}, {
		icon: "~@/static/cp/whiteblue.png",
		title: "蓝白渐变色",
		url: "6",
	}
])
// 用户性别
// 1-	男
// 2-	女
// 3-	未知
const userGenderMap = new Map([
	['1', "男"],
	['2', "女"],
	['3', "未知"],
])
// 用户状态
// 1-	未实名
// 2-	已实名
const userStatusMap = new Map([
	['1', "未实名"],
	['2', "已实名"],
])

const dealTypeMap = new Map([
	['1', "通行费代扣"],
])

// 1:在用
// 2:解约
// 3.签约中
const payCardStatusMap = new Map([
	['1', "在用"],
	['2', "解约"],
	['3', "签约中"],
	['4', "解约"],
])

const carSignStatusMap = new Map([
	['1', '正 常'],
	['2', '未上传车主身份证'],
	['3', '未上传行驶证'],
	['4', '未关联支付渠道'],
	['5', '未完成支付'],
	['6', 'OBU未激活'],
]);

const CardStatusMap = new Map([
	['1', "正常"],
	['2', "有卡挂起"],
	['3', "无卡挂起"],
	['4', "有卡注销"],
	['5', "无卡注销"],
	['6', "卡挂失"]
]);

const ObuStatusMap = new Map([
	['1', "正常"],
	['101', "核销"],
	['3', "挂起"],
	['4', "未关联扣款渠道"],
	['5', "账户透支"],
	['6', "支付机构限制"]
]);
const cpuCardTypeOptions = [{
		value: '21',
		label: '年/月票卡'
	},
	{
		value: '22',
		label: '储值卡'
	},
	{
		value: '23',
		label: '记账卡'
	},
	{
		value: '51',
		label: '测试用年/月票卡'
	},
]
const vehicleColorOptions = [{
		value: '',
		label: '--请选择--'
	},
	{
		value: '0',
		label: '蓝色'
	},
	{
		value: '1',
		label: '黄色'
	},
	{
		value: '2',
		label: '黑色'
	},
	{
		value: '3',
		label: '白色'
	},
	{
		value: '4',
		label: '渐变绿色'
	},
	{
		value: '5',
		label: '黄绿双拼'
	},
	{
		value: '6',
		label: '蓝白渐变'
	},
	{
		value: '7',
		label: '临时牌照'
	},
	{
		value: '9',
		label: '未确定'
	},
	{
		value: '11',
		label: '绿色'
	},
	{
		value: '12',
		label: '红色'
	}
];
const vehicleUserTypeOptions = [{
		value: '',
		label: '--请选择--'
	},
	{
		value: '1',
		label: '一型客车'
	},
	{
		value: '2',
		label: '二型客车'
	},
	{
		value: '3',
		label: '三型客车'
	},
	{
		value: '4',
		label: '四型客车'
	},
	{
		value: '11',
		label: '一型货车'
	},
	{
		value: '12',
		label: '二型货车'
	},
	{
		value: '13',
		label: '三型货车'
	},
	{
		value: '14',
		label: '四型货车'
	},
	{
		value: '15',
		label: '五型货车'
	},
	{
		value: '16',
		label: '六型货车'
	},
	{
		value: '21',
		label: '一型专项作业车'
	},
	{
		value: '22',
		label: '二型专项作业车'
	},
	{
		value: '23',
		label: '三型专项作业车'
	},
	{
		value: '24',
		label: '四型专项作业车'
	},
	{
		value: '25',
		label: '五型专项作业车'
	},
	{
		value: '26',
		label: '六型专项作业车'
	}
];
const proviceCodeMap = new Map([
	["北京市", '11'],
	["天津市", '12'],
	["河北省", '13'],
	["山西省", '14'],
	["内蒙古自治区", '15'],
	["辽宁省", '21'],
	["吉林省", '22'],
	["黑龙江省", '23'],
	["上海市", '31'],
	["江苏省", '32'],
	["浙江省", '33'],
	["安徽省", '34'],
	["福建省", '35'],
	["江西省", '36'],
	["山东省", '37'],
	["河南省", '41'],
	["湖北省", '42'],
	["湖南省", '43'],
	["广东省", '44'],
	["广西壮族自治区", '45'],
	["海南省", '46'],
	["重庆市", '50'],
	["四川省", '51'],
	["贵州省", '51'],
	["云南省", '53'],
	["西藏自治区", '54'],
	["陕西省", '61'],
	["甘肃省", '62'],
	["青海省", '63'],
	["宁夏回族自治区", '64'],
	["新疆维吾尔自治区", '65'],
	["台湾省", '71'],
	["香港特别行政区", '81'],
	["澳门特别行政区", '82'],
]);

const proviceActiveUrlMap = new Map([
	["中国ETC-北京发行", 'https://open.txffp.com/ias/issuerInfo/bj.html'],
	["中国ETC-河南发行", 'https://open.txffp.com/ias/issuerInfo/hn.html'],
	["中国ETC-广东发行", 'https://open.txffp.com/ias/issuerInfo/gd.html'],
	["中国ETC-山东发行（齐鲁交通）", 'https://open.txffp.com/ias/issuerInfo/sdqn.html'],
	["中国ETC-江苏发行", 'https://open.txffp.com/ias/issuerInfo/js.html'],
	["中国ETC-安徽发行", 'https://open.txffp.com/ias/issuerInfo/ah.html'],
	["中国ETC-上海发行", 'https://open.txffp.com/ias/issuerInfo/sh.html'],
	["中国ETC-贵州发行", 'https://open.txffp.com/ias/issuerInfo/gz.html'],
	["中国ETC-广西发行", 'https://open.txffp.com/ias/issuerInfo/gx.html'],
	["中国ETC-陕西发行", 'https://open.txffp.com/ias/issuerInfo/sx.html'],
	["中国ETC-辽宁发行", 'https://open.txffp.com/ias/issuerInfo/ln.html'],
	["中国ETC-河北发行", 'https://open.txffp.com/ias/issuerInfo/hb.html'],
	["中国ETC-山东发行（山东高速）", 'https://open.txffp.com/ias/issuerInfo/sdxl.html'],
	["中国ETC-云南发行", 'https://open.txffp.com/ias/issuerInfo/yn.html']
]);

const issuerList = [];

const complaintProvinceList = [{
		id: "11",
		name: '北京服务方代理',
		lebel: '北京市'
	},
	{
		id: "12",
		name: '天津市服务方代理',
		lebel: '天津市'
	},
	{
		id: "13",
		name: '河北省服务方代理',
		lebel: '河北省'
	},
	{
		id: "14",
		name: '山西省太原高速公路有限公司',
		lebel: '山西省'
	},
	{
		id: "15",
		name: '内蒙古服务方代理',
		lebel: '内蒙古自治区'
	},
	{
		id: "21",
		name: '辽宁省服务方代理',
		lebel: '辽宁省'
	},
	{
		id: "22",
		name: '吉林服务方代理',
		lebel: '吉林省'
	},
	{
		id: "23",
		name: '黑龙江服务方代理',
		lebel: '黑龙江省'
	},
	{
		id: "31",
		name: '上海服务方代理',
		lebel: '上海市'
	},
	{
		id: "32",
		name: '江苏服务方代理',
		lebel: '江苏省'
	},
	{
		id: "33",
		name: '浙江省中心服务方代理',
		lebel: '浙江省'
	},
	{
		id: "34",
		name: '安徽服务方代理',
		lebel: '安徽省'
	},
	{
		id: "35",
		name: '福建服务方代理',
		lebel: '福建省'
	},
	{
		id: "36",
		name: '江西省高速公路联网管理中心2',
		lebel: '江西省'
	},
	{
		id: "37",
		name: '山东服务方代理',
		lebel: '山东省'
	},
	{
		id: "41",
		name: '河南省服务方代理',
		lebel: '河南省'
	},
	{
		id: "42",
		name: '湖北服务方代理',
		lebel: '湖北省'
	},
	{
		id: "43",
		name: '湖南服务方代理',
		lebel: '湖南省'
	},
	{
		id: "44",
		name: '广东服务方代理',
		lebel: '广东省'
	},
	{
		id: "45",
		name: '广西服务方代理',
		lebel: '广西省'
	},
	{
		id: "50",
		name: '重庆服务方代理',
		lebel: '重庆市'
	},
	{
		id: "51",
		name: '四川服务方代理',
		lebel: '四川省'
	},
	{
		id: "52",
		name: '贵州省服务方代理',
		lebel: '贵州省'
	},
	{
		id: "53",
		name: '云南服务方代理',
		lebel: '云南省'
	},
	{
		id: "61",
		name: '陕西服务方代理',
		lebel: '陕西省'
	},
	{
		id: "62",
		name: '甘肃服务方代理',
		lebel: '甘肃省'
	},
	{
		id: "63",
		name: '青海服务方',
		lebel: '青海省'
	},
	{
		id: "64",
		name: '宁夏服务方代理',
		lebel: '宁夏回族自治区'
	},
	{
		id: "65",
		name: '新疆服务方代理',
		lebel: '新疆维吾尔自治区'
	}
];

const flowControlStep = {
	homeIndex: -1,
	stratproc: 0,
	checkIdcard: 1,
	uploadLincense: 2,
	orderinfoEnsure: 3,
	vehicleOwner: 4,
	selectProject: 5,
	confirmLabel: 6,
	payAccountList: 7,
	relevancePayWay: 8,
	receiveAddress: 9,
	ensureOrder: 10
};

//个人证件类型字典
const personalType = [{
		value: '0',
		label: '身份证'
	},
	{
		value: '1',
		label: '军官证'
	},
	{
		value: '2',
		label: '护照'
	},
	{
		value: '3',
		label: '入境证'
	},
	{
		value: '4',
		label: '临时身份证'
	},
	{
		value: '5',
		label: '港澳居民往来大陆通行证'
	},
	{
		value: '6',
		label: '台湾居民往来大陆通行证'
	},
	{
		value: '7',
		label: '武警警察身份证'
	},
]

//企业类型字典
const certificatesTypes = [{
		value: '1',
		label: '统一社会信用代码证'
	},
	{
		value: '2',
		label: '组织机构代码证'
	},
	{
		value: '3',
		label: '营业执照'
	},
	{
		value: '4',
		label: '事业单位法人证书'
	},
	{
		value: '5',
		label: '社会团体法人证书'
	},
	{
		value: '6',
		label: '律师事务所执业许可证'
	}
]
//新办企业类型字典
const newCertificatesTypes = [{
		value: '1',
		label: '统一社会信用代码证'
	},
	{
		value: '2',
		label: '组织机构代码证'
	},
	{
		value: '3',
		label: '营业执照'
	},
	{
		value: '5',
		label: '社会团体法人证书'
	},
	{
		value: '6',
		label: '律师事务所执业许可证'
	}
]
const vehicleClassType = [{
		value: '1',
		label: '一类车'
	},
	{
		value: '2',
		label: '二类车'
	},
	{
		value: '3',
		label: '三类车'
	},
	{
		value: '4',
		label: '四类车'
	},
	{
		value: '5',
		label: '五类车'
	},
	{
		value: '6',
		label: '六类车'
	},
	{
		value: '7',
		label: '七类车'
	},
]
const vehicleType = [{
	value: '1',
	label: '货车'
}, {
	value: '2',
	label: '客车'
}, {
	value: '3',
	label: '专车'
}]
const vehicleUserType = [{
		value: '0',
		label: '普通车'
	},
	{
		value: '19',
		label: '广西警车'
	},
	{
		value: '20',
		label: '路政车'
	},
	{
		value: '24',
		label: '集装箱J1'
	},
	{
		value: '26',
		label: '应急救援车'
	},
	{
		value: '27',
		label: '普通牵引车'
	},
	{
		value: '28',
		label: '集装箱J2'
	}
]
//新办去掉广西警车和路政车
const newVehicleUserType = [{
		value: '0',
		label: '普通车'
	},
	// {
	// 	value: '19',
	// 	label: '广西警车'
	// },
	// {
	// 	value: '20',
	// 	label: '路政车'
	// },
	{
		value: '24',
		label: '集装箱J1'
	},
	// {
	// 	value: '26',
	// 	label: '应急救援车'
	// },
	{
		value: '27',
		label: '普通牵引车'
	},
	{
		value: '28',
		label: '集装箱J2'
	}
]
//抖音只有客车普通车办理
const dyVehicleUserType = [{
		value: '0',
		label: '普通车'
	},
	// {
	// 	value: '19',
	// 	label: '广西警车'
	// },
	// {
	// 	value: '20',
	// 	label: '路政车'
	// },
	// {
	// 	value: '24',
	// 	label: '集装箱J1'
	// },
	// {
	// 	value: '26',
	// 	label: '应急救援车'
	// },
	// {
	// 	value: '27',
	// 	label: '普通牵引车'
	// },
	// {
	// 	value: '28',
	// 	label: '集装箱J2'
	// }
]
// 省份
const provincesPicker = [{
		label: "浙",
		value: '浙'
	},
	{
		label: '沪',
		value: '沪'
	},
	{
		label: '苏',
		value: '苏'
	},
	{
		label: '皖',
		value: '皖'
	},
	{
		label: '赣',
		value: '赣'
	},
	{
		label: '闽',
		value: '闽'
	},
	{
		label: '京',
		value: '京'
	},
	{
		label: '津',
		value: '津'
	},
	{
		label: '渝',
		value: '渝'
	},
	{
		label: '冀',
		value: '冀'
	},
	{
		label: '豫',
		value: '豫'
	},
	{
		label: '云',
		value: '云'
	},
	{
		label: '辽',
		value: '辽'
	},
	{
		label: '黑',
		value: '黑'
	},
	{
		label: '湘',
		value: '湘'
	},
	{
		label: '鲁',
		value: '鲁'
	},
	{
		label: '新',
		value: '新'
	},
	{
		label: '鄂',
		value: '鄂'
	},
	{
		label: '桂',
		value: '桂'
	},
	{
		label: '甘',
		value: '甘'
	},
	{
		label: '晋',
		value: '晋'
	},
	{
		label: '蒙',
		value: '蒙'
	},
	{
		label: '陕',
		value: '陕'
	},
	{
		label: '吉',
		value: '吉'
	},
	{
		label: '贵',
		value: '贵'
	},
	{
		label: '粤',
		value: '粤'
	},
	{
		label: '青',
		value: '青'
	},
	{
		label: '藏',
		value: '藏'
	},
	{
		label: '川',
		value: '川'
	},
	{
		label: '宁',
		value: '宁'
	},
	{
		label: '琼',
		value: '琼'
	},
	{
		label: '使',
		value: '使'
	},
	{
		label: '领',
		value: '领'
	},

]
export {
	cityMotionCode,
	bankCodes,
	bankCodeToBankNameMap,
	bankCodeToIconMap,
	bankCodeToIconColorMap,
	bankCodeToBgMap,
	plateColorToColorMap,
	userGenderMap,
	userStatusMap,
	payTypeMap,
	cardTypeMap,
	dealTypeMap,
	payCardStatusMap,
	carSignStatusMap,
	getVehicleStatus,
	flowControlStep,
	plateColorOptions,
	plateColorToClassMap,
	vehicleTypeOptions,
	plateColorToFirstMap,
	proviceCodeMap,
	expressTypeMap,
	expressTypeOptions,
	proviceActiveUrlMap,
	orderStatusMap,
	expressStatusMap,
	reverseExpressTypeMap,
	expressStatusOptions,
	orderTypeOptions,
	orderStatusOptions,
	CardStatusMap,
	ObuStatusMap,
	issuerList,
	complaintProvinceList,
	personalType,
	vehicleColorOptions,
	vehicleUserTypeOptions,
	cpuCardTypeOptions,
	certificatesTypes,
	checkCarType,
	vehicleClassType,
	vehicleType,
	vehicleUserType,
	provincesPicker,
	newVehicleUserType,
	newCertificatesTypes
}
