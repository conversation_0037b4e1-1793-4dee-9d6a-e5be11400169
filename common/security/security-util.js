import CryptoJS from '@/js_sdk/crypto-js/crypto-js.min';
/**
 * 接口数据加密函数
 * @param str string 需加密的json字符串
 * @param key_hash string 加密key_hash
 * @return string 加密密文字符串
 */
const aesEncrypt = (str, key_hash) => {
	if (!str) {
		return str;
	}
	var key = CryptoJS.enc.Utf8.parse(key_hash);
	var encrypted = CryptoJS.AES.encrypt(str, key, {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7
	});
	return encrypted.toString();
}
/**
 * 接口数据解密函数
 * @param str string 已加密密文
 * @param key_hash string 加密key_hash
 * @returns {*|string} 解密之后的json字符串
 */
export const aesDecrypt = (str, key_hash) => {
	if (!str) {
		return str;
	}

	//密钥
	var key = CryptoJS.enc.Utf8.parse(key_hash);
	var decrypted = CryptoJS.AES.decrypt(str, key, {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7
	});
	return decrypted.toString(CryptoJS.enc.Utf8);
}
export const decryption = function(data,key){
	return aesDecrypt(data,key);
}
export const encryption = function(data,key){
	return aesEncrypt(data,key);
}
