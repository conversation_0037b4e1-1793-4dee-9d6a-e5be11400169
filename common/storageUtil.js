import store from '../store/index.js';
export function getStore(type) {
	try {
		let param = uni.getStorageSync(type);
		return param
	} catch (e) {
		return getApp().globalData[type];
	}
}

export function setStore(type, param) {
	try {
		if (typeof param === 'object') {
			uni.setStorageSync(type, JSON.stringify(param));
		} else {
			uni.setStorageSync(type, param);
		}
	} catch (e) {
		getApp().globalData[type] = param;
	}
}
export function getLoginUserInfo() {
	let userInfo = uni.getStorageSync('loginUserInfo');
	if (userInfo != '' && JSON.stringify(userInfo) != '{}') {
		return JSON.parse(userInfo);
	}
	return {}
}

export function setLoginUserInfo(userInfo) {
	let data = userInfo || {}
	try {
		uni.setStorageSync('loginUserInfo', JSON.stringify(userInfo));

	} catch (e) {

	}
}

export function getEtcVehicle() {
	let userInfo = uni.getStorageSync('etcVehicle');

	if (userInfo != '' && JSON.stringify(userInfo) != '{}') {
		return JSON.parse(userInfo);
	}
	return {}
}
export function getEtcAccountInfo() {
	let userInfo = uni.getStorageSync('etcAccount');

	if (userInfo != '' && JSON.stringify(userInfo) != '{}') {
		return JSON.parse(userInfo);
	}
	return {}
}


export function setEtcVehicle(userInfo) {
	let data = userInfo || {}
	try {
		uni.setStorageSync('etcVehicle', JSON.stringify(data));


	} catch (e) {

	}

}
export function setEtcAccount(userInfo) {
	let data = userInfo || {}
	try {
		uni.setStorageSync('etcAccount', JSON.stringify(data));


	} catch (e) {

	}
}
export function setAccessToken(data) {
	let key = data || '';
	try {
		uni.setStorageSync('accessToken', key);
		store.commit('SET_ACCESSTOKEN', key);
	} catch (e) {
		store.commit('SET_ACCESSTOKEN', key);
	}
}
export function getAccessToken() {
	var accessToken = uni.getStorageSync('accessToken');
	if (!accessToken) {
		accessToken = store.state.accessToken; //从Vuex里面获取
	}
	return accessToken;
}
export function setTicket(data) {
	let key = data || '';
	try {
		uni.setStorageSync('ticket', key);

	} catch (e) {


	}
}
export function getTicket() {
	var ticket = uni.getStorageSync('ticket');

	return ticket;
}
export function setMd5Key(data) {
	let key = data || '';
	try {
		uni.setStorageSync('md5Key', key);
	} catch (e) {


	}
}
export function getMd5Key() {
	var md5Key = uni.getStorageSync('md5Key');

	return md5Key;
}
export function setAesKey(data) {
	let key = data || '';
	try {
		uni.setStorageSync('aesKey', key);

	} catch (e) {

	}
}
export function getAesKey() {
	var aesKey = uni.getStorageSync('aesKey');

	return aesKey;
}
export function getCurrUserInfo() {
	let userInfo = uni.getStorageSync('currentPerson');
	if (userInfo != '' && JSON.stringify(userInfo) != '{}') {
		return JSON.parse(userInfo);
	} else {
		if (JSON.stringify(store.state.customerInfo) != '{}') {
			return JSON.parse(store.state.customerInfo);
		} else if (JSON.stringify(getApp().globalData.currentPerson) != '{}') {
			return getApp().globalData.currentPerson;
		} else {
			return null;
		}
	}
}

export function setCurrUserInfo(userInfo) {
	if (userInfo) {
		try {
			uni.setStorageSync('currentPerson', JSON.stringify(userInfo));
			store.commit('SET_CUSTOMERINFO', JSON.stringify(userInfo)); //存入Vuex
			getApp().globalData.currentPerson = userInfo;
		} catch (e) {
			store.commit('SET_CUSTOMERINFO', JSON.stringify(userInfo)); //存入Vuex
			getApp().globalData.currentPerson = userInfo;
		}
	}
}
export function removeStore(type) {
	uni.removeStorageSync(type);
	if (getApp() && getApp().globalData) {
		getApp().globalData[type] = "";
	}
}
// TODO 旧版本内容
// 后期不用 可以删除

//储存选择企业或个人accountID
export function setAccountId(id) {
	try {
		uni.setStorageSync('accountId', id);
		getApp().globalData.accountId = id;
	} catch (e) {
		store.commit('SET_ACCOUNTID', id); //存入Vuex
		getApp().globalData.accountId = id;
	}
}

//获取储存的企业或个人accountID
export function getAccountId() {
	let code = uni.getStorageSync('accountId');
	if (!code) {
		code = store.state.accountId; //从Vuex里面获取
		if (!code) {
			return getApp().globalData.accountId;
		} else {
			return null;
		}
	}
	return code;
}
export function getCurrentCar() {
	let currentCar = uni.getStorageSync('currentCar');
	if (currentCar != '' && JSON.stringify(currentCar) != '{}') {
		return JSON.parse(currentCar);
	} else {
		if (JSON.stringify(store.state.vehicleInfo) != '{}') {
			return JSON.parse(store.state.vehicleInfo);
		} else if (JSON.stringify(getApp().globalData.currentCar) != '{}') {
			return getApp().globalData.currentCar;
		} else {
			return null;
		}
	}
}

export function setCurrentCar(currentCar) {
	if (currentCar) {
		try {
			uni.setStorageSync('currentCar', JSON.stringify(currentCar));
			store.commit('SET_VEHICLEINFO', JSON.stringify(currentCar)); //存入Vuex
			getApp().globalData.currentCar = currentCar;
		} catch (e) {
			// error
			store.commit('SET_VEHICLEINFO', JSON.stringify(currentCar)); //存入Vuex
			getApp().globalData.currentCar = currentCar;
		}
	}
}

export function getProductInfo() {
	let productInfo = uni.getStorageSync('productinfo');
	if (productInfo != '' && JSON.stringify(productInfo) != '{}') {
		return JSON.parse(productInfo);
	} else {
		if (JSON.stringify(store.state.productInfo) != '{}') {
			return JSON.parse(store.state.productInfo);
		} else if (JSON.stringify(getApp().globalData.productinfo) != '{}') {
			return getApp().globalData.productinfo
		} else {
			return null;
		}
	}
}

export function setProductInfo(productInfo) {
	if (productInfo) {
		try {
			uni.setStorageSync('productinfo', JSON.stringify(productInfo));
			store.commit('SET_PRODUCTINFO', JSON.stringify(productInfo)); //存入Vuex
			getApp().globalData.productinfo = productInfo;
		} catch (e) {
			// error
			store.commit('SET_PRODUCTINFO', JSON.stringify(productInfo)); //存入Vuex
			getApp().globalData.productinfo = productInfo;
		}
	}
}

export function getTokenId() {
	var tokenId = uni.getStorageSync('tokenId');
	if (!tokenId) {
		tokenId = store.state.tokenId; //从Vuex里面获取
		if (!tokenId) {
			return getApp().globalData.tokenId;
		} else {
			return null;
		}
	}
	return tokenId;
}

export function setTokenId(tokenId) {
	if (tokenId) {
		try {
			uni.setStorageSync('tokenId', tokenId);
			getApp().globalData.tokenId = tokenId;
		} catch (e) {
			store.commit('SET_TOKENID', tokenId); //存入Vuex
			getApp().globalData.tokenId = tokenId;
		}
	}
}

export function getOpenid() {
	var openId = uni.getStorageSync('openId');
	return openId;
}
export function setOpenid(openId) {
	if (openId) {
		try {
			uni.setStorageSync('openId', openId);
		} catch (e) {

		}
	}
}

export function setOpenidForRead(readOpenId) {
	if (readOpenId) {
		try {
			uni.setStorageSync('readOpenId', readOpenId);
		} catch (e) {
			getApp().globalData.readOpenId = readOpenId;
			store.commit('SET_OPENID_READ', readOpenId);
		}
	}
}
export function getSessionKey() {
	var session_key = uni.getStorageSync('session_key');
	if (!session_key) {
		session_key = getApp().globalData.session_key;
		if (!session_key) {
			session_key = store.state.session_key; //从Vuex里面获取
		} else {
			return null
		}
	}
	return session_key;
}

export function setSessionKey(session_key) {
	if (session_key) {
		try {
			uni.setStorageSync('session_key', session_key);
		} catch (e) {
			getApp().globalData.session_key = session_key;
			store.commit('SET_SESSION_KEY', session_key);
		}
	}
}

export function getMustFresh() {
	var mustFresh = uni.getStorageSync('mustFresh');
	if (mustFresh == "") {
		mustFresh = store.state.mustFresh; //从Vuex里面获取
	}
	return mustFresh;
}

export function setMustFresh(mustFresh) {
	if (mustFresh) {
		try {
			uni.setStorageSync('mustFresh', mustFresh);
		} catch (e) {
			store.commit('SET_MUSTFRESH', mustFresh); //存入Vuex
		}
	}
}

// 车辆识别结果 后期删除
export function getIdentity() {
	let identity = uni.getStorageSync('identity');
	if (identity) {
		return JSON.parse(identity);
	} else {
		return {};
	}
}
// 车辆识别结果 后期删除
export function setIdentity(identity) {
	if (identity) {
		try {
			uni.setStorageSync('identity', JSON.stringify(identity));
		} catch (e) {
			// error
		}
	}
}

// 获取车辆列表
export function getVehicleInfoList() {
	let vehicleInfoList = uni.getStorageSync('vehicleInfo');
	if (vehicleInfoList) {
		return JSON.parse(vehicleInfoList);
	} else {
		return [];
	}
}
// 车辆列表
export function setVehicleInfoList(vehicleInfoList) {
	if (vehicleInfoList) {
		uni.setStorageSync('vehicleInfo', JSON.stringify(vehicleInfoList));
	}
}


// 办理流程缓存
export function getFlowControlStorage() {
	let flowControlStorage = uni.getStorageSync('flowControlStorage');
	if (flowControlStorage) {
		return JSON.parse(flowControlStorage);
	} else {
		return {};
	}
}
// 车辆列表
export function setFlowControlStorage(flowControlStorage) {
	if (flowControlStorage) {
		uni.setStorageSync('flowControlStorage', JSON.stringify(flowControlStorage));
	}
}
//获取wx个人信息
export function getWxAccount() {
	let account = uni.getStorageSync('wxAccountInfo');
	if (!account) {
		account = store.state.wxAccountInfo; //从Vuex里面获取
		if (!account) {
			return getApp().globalData.wxAccountInfo;
		} else {
			return null;
		}
	}
	return account;
}

//储存wx个人信息
export function setWxAccount(accountInfo) {
	try {
		uni.setStorageSync('wxAccountInfo', JSON.stringify(accountInfo));
		getApp().globalData.wxAccountInfo = JSON.stringify(accountInfo);
	} catch (e) {
		store.commit('SET_WX_ACCOUNTINFO', JSON.stringify(accountInfo)); //存入Vuex
		getApp().globalData.wxAccountInfo = JSON.stringify(accountInfo);
	}
}

//储存城市服务返回的code码
export function setReturnCode(code) {
	try {
		uni.setStorageSync('returnCode', code);
		getApp().globalData.returnCode = code;
	} catch (e) {
		store.commit('SET_RETURN_CODE', code); //存入Vuex
		getApp().globalData.returnCode = code;
	}
}

//获取城市服务返回的code码
export function getReturnCode() {
	let code = uni.getStorageSync('returnCode');
	if (!code) {
		code = store.state.returnCode; //从Vuex里面获取
		if (!code) {
			return getApp().globalData.returnCode;
		} else {
			return null;
		}
	}
	return code;
}

//清除城市服务返回的code码
export function clearReturnCode() {
	uni.removeStorageSync('returnCode')
	store.commit('CLEAR_RETURN_CODE'); //存入Vuex
	getApp().globalData.returnCode = ""
}




//设置已经授权的电话
export function setPhone(phone) {
	try {
		uni.setStorageSync('phone', phone);
		getApp().globalData.phone = phone;
	} catch (e) {
		store.commit('SET_PHONE', phone); //存入Vuex
		getApp().globalData.phone = phone;
	}
}

//获取已经授权的电话
export function getPhone() {
	let mobile = uni.getStorageSync('phone');
	if (!mobile) {
		mobile = store.state.phone; //从Vuex里面获取
		if (!mobile) {
			return getApp().globalData.phone;
		} else {
			return null;
		}
	}
	return mobile;
}


//设置业务类型
export function setBusinessTypes(type) {
	try {
		uni.setStorageSync('businessTypes', type);
	} catch (e) {
		getApp().globalData.businessTypes = type;
	}
}

//获取业务类型
export function getBusinessTypes() {
	let mobile = uni.getStorageSync('businessTypes');
	if (!mobile) {
		return getApp().globalData.businessTypes;
	}
	return mobile;
}

//设置渠道id
export function setChannelId(option) {
	try {
		uni.setStorageSync('channelId', option);
	} catch (e) {
		getApp().globalData.channelId = option;
	}
}

//获取渠道id
export function getChannelId() {
	let channelId = uni.getStorageSync('channelId');
	if (!channelId) {
		return getApp().globalData.channelId;
	}
	return channelId;
}

//设置Aeskey
export function setDecodingKey(key) {
	try {
		uni.setStorageSync('decodingKey', key);
	} catch (e) {
		store.commit('SET_AESKEY', key);
	}
}

//获取decodingKey
export function getDecodingKey() {
	let decodingKey = uni.getStorageSync('decodingKey');
	if (!decodingKey) {
		return store.state.decodingKey
	}
	return decodingKey;
}


//获取accountOpenId
export function getAccountOpenIdByStorage() {
	var accountOpenId = uni.getStorageSync('accountOpenId');

	return accountOpenId;
}


// 设置默认跳转路径
export function getDefaultUrl() {
	let defaultUrl = uni.getStorageSync('defaultUrl');
	if (defaultUrl) {
		return defaultUrl
	} else {
		return {};
	}
}
export function setDefaultUrl(defaultUrl) {
	if (defaultUrl) {
		uni.setStorageSync('defaultUrl', defaultUrl);
	}
}


export function getOpenidForRead() {
	let readOpenId = uni.getStorageSync('readOpenId');
	if (!readOpenId) {
		readOpenId = store.state.readOpenId; //从Vuex里面获取
		if (!readOpenId) {
			return getApp().globalData.readOpenId;
		} else {
			return null;
		}
	}
	return readOpenId;
}

export function getWechatUserInfo() {
	let userInfo = uni.getStorageSync('wechat_user_info');
	if (userInfo) {
		try {
			return JSON.parse(userInfo);
		} catch (e) {
			return null;
		}
	}
	return null;
}

export function setWechatUserInfo(userInfo) {
	if (userInfo) {
		try {
			uni.setStorageSync('wechat_user_info', JSON.stringify(userInfo));
		} catch (e) {
			// a
		}
	}
}

//设置充值方式
// 设置默认跳转路径
export function getRechargeType() {
	let rechargeType = uni.getStorageSync('rechargeType');
	if (rechargeType) {
		return rechargeType
	} else {
		return {};
	}
}
export function setRechargeType(rechargeType) {
	if (rechargeType) {
		uni.setStorageSync('rechargeType', rechargeType);
	}
}


//推广码操作员工号保存
export function setUserId(data) {
	let key = data || '';
	try {
		uni.setStorageSync('userId', key);
	} catch (e) {


	}
}
//推广码操作员工号获取
export function getUserId() {
	var userId = uni.getStorageSync('userId');

	return userId;
}

export function setTollTicket(data) {
	let key = data || '';
	try {
		uni.setStorageSync('tollTicket', key);

	} catch (e) {


	}
}
export function getTollTicket() {
	var ticket = uni.getStorageSync('tollTicket');

	return ticket;
}