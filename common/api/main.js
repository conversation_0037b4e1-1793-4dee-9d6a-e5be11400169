import interface_9901 from './interface_9901';
import interface_4501 from './interface_4501';
import afterSale from './afterSale';
import interface_afterSale from './interface_afterSale.js'
import vue from 'vue';
let initInterfaces = function(){
	let curBusiness = uni.getStorageSync('curBusiness');
	let currentInterfaces = {
		...interface_4501,
		...afterSale,
		...interface_afterSale
	}
	vue.prototype.$interfaces = currentInterfaces;
}
export default initInterfaces
