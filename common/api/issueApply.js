// 新发发行流程相关接口
const issue_apply = {
	//新发发行
	'getApplyId': { //保存草稿
		url: '/mp2c',
		method: '/apply/applyEntranc'
	},
	'saveOpenId': { //保存草稿
		url: '/mp2c',
		method: '/apply/saveOpenId'
	},
	//判断新发申请
	'applyStatus': { //保存草稿
		url: '/mp2c',
		method: '/apply/restricted'
	},
	'saveDraft': { //保存草稿
		url: '/mp2c',
		method: '/apply/saveDraft'
	},
	'getDraft': { //获取草稿
		url: "/mp2c",
		method: '/apply/getDraft'
	},
	'newApply': { //新开户申请
		url: "/mp2c",
		method: "/apply/submit"
	},
	//车牌号唯一性校验
	'checkVehicleNo': {
		url: "/mp2c",
		method: "/apply/checkVehicleNo"
	},
	//车辆校验
	'checkVehicle': {
		url: "/mp2c",
		method: "/apply/checkVehicle"
	},
	//高频车判断
	'checkIsFreeVehicle': {
		url: "/mp2c",
		method: "/apply/checkIsFreeVehicle"
	},
	//新监管档案上传
	'archivesUpload': {
		url: "/mp2c",
		method: "/archives/img/upload"
	},
	//查看合同状态
	'qeuryStatus': {
		url: "/mp2c",
		method: "/apply/status"
	},
	//新办查看最新合同
	'queryApplyContracts': {
		url: "/mp2c",
		method: '/apply/applyQueryContracts'
	},
	//查看是否需要签署
	'getSignStatus': {
		url: "/mp2c",
		method: "/apply/signStatus"
	},
	//创建合同
	'createSign': {
		url: "/mp2c",
		method: "/apply/contract/card"
	},
	//创建申请单
	'createApplySheet': {
		url: "/mp2c",
		method: "/apply/submit"
	},
	//获取产品权益服务价格
	'getPackagePrice': {
		url: "/mp2c",
		method: "/hsApplyProductConfig/cSideAppletConfig"
	},
	//获取产品列表
	'getProduct': {
		url: "/mp2c",
		method: "/hsApplyProductConfig/showProduct"
	},
	//获取支付金额
	'getPayPrice': {
		url: "/mp2c",
		method: "/hsApplyProductConfig/cSideAppletConfig"
	},
	//新发订单支付
	'applyPay': {
		url: "/mp2c",
		method: "/hsApplyPayOrder/pay"
	},
	//新发售后订单支付
	'applyAfterPay': {
		url: "/mp2c",
		method: "/hsApplyPaySaleOrder/pay "
	},
	//新发订单支付查询
	'applyPayOrderQuery': {
		url: '/mp2c',
		method: '/hsApplyPayOrder/query'
	},
	//新发售后订单支付查询
	'applyAfterPayOrderQuery': {
		url: '/mp2c',
		method: '/hsApplyPaySaleOrder/query'
	},
	'applyList': { //订单列表
		url: "/mp2c",
		method: '/apply/list'
	},
	'applyOrderDetail': { //订单详情
		url: "/mp2c",
		method: '/apply/orderDetails'
	},
	'cancelApplyOrder': { //取消订单
		url: "/mp2c",
		method: '/apply/cancelOrder'
	},
	'confirmProduct': { //确认收货
		url: "/mp2c",
		method: '/apply/receipt'
	},
	'confirmRefundProduct': { //确认收货
		url: "/mp2c",
		method: '/applyReturn/receipt'
	},
	'appealApply': { //申诉申请
		url: "/mp2c",
		method: '/apply/appeal'
	},
	'appealDetail': { //申诉详情
		url: "/mp2c",
		method: '/apply/appealDetails'
	},
	'applyAfterProduct': { //获取售后产品信息
		url: "/mp2c",
		method: '/applyReturn/returnOrderDetails'
	},
	'applyAfterCreate': { //售后申请提交
		url: "/mp2c",
		method: '/applyReturn/returnApply'
	},
	'applyAfterModify': { //售后申请提交
		url: "/mp2c",
		method: '/applyReturn/update'
	},
	'getFillAddress': { //售后获取收货地址
		url: "/mp2c",
		method: '/applyReturn/fillAddress'
	},
	'getReturnInfoList': { //售后列表
		url: "/mp2c",
		method: '/applyReturn/returnInfoList'
	},
	'getReturnInfoDetail': { //售后详情
		url: "/mp2c",
		method: '/applyReturn/returnInfo'
	},
	'returnCancel': { //售后取消
		url: "/mp2c",
		method: '/applyReturn/cancel'
	},
	'getDateRange': { //获取物流时间
		url: "/mp2c",
		method: '/applyReturn/pickUpTime'
	},
	'getAgainData': { //获取申请单重新提交的数据
		url: "/mp2c",
		method: '/applyReturn/againReturnApplyInfo'
	},
	'confirmReceipt': { //换货到货用户确认收货
		url: "/mp2c",
		method: '/applyReturn/receipt'
	},
	'sendLogisticsNo': { //填写物流单号
		url: "/mp2c",
		method: '/applyReturn/logisticsNo'
	},
	'getSfRouteResInfo': { //获取所有物流信息
		url: "/mp2c",
		method: '/apply/getSfRouteResInfo'
	},
	//查询办理进度时间轴
	'getTimeLineList': {
		url: '/mp2c',
		method: '/apply/timeAxis'
	},
	//修改新办订单
	'applyUpdate': {
		url: '/mp2c',
		method: '/apply/update'
	},
	//是否有进行中订单
	'isOrderIng': {
		url: '/mp2c',
		method: '/apply/isOrderIng'
	},
	// 新办次次顺签约相关接口
	// 新办次次顺签约查询
	'newSearchSilkyInfo': {
		url: '/mp2c',
		method: '/applyRecordSilkyBinding/info'
	},
	// 新办次次顺签约保存
	'newSaveSilkyInfo': {
		url: '/mp2c',
		method: '/applyRecordSilkyBinding/save',
	},
	//新办次次顺签约撤销
	'newSilkyCancel': {
		url: '/mp2c',
		method: '/applyRecordSilkyBinding/cancel'
	},
	//新办次次顺线上签约查询
	'checkUnderSign': {
		url: '/mp2c',
		method: '/applyRecordSilkyBinding/checkSilkyInfo'
	},
	//新办次次顺线上签约查询
	'newWeChatSign': {
		url: '/mp2c',
		method: '/applyRecordSilkyBinding/userState'
	},
	//新办次次顺查询服务费率
	'newWGetserviceRate': {
		url: '/mp2c',
		method: '/applySilkyBinding/serviceRate'
	},
	//新办次次顺激活校验
	'newSilkyActivationCheck': {
		url: '/mp2c',
		method: '/applyRecordSilkyBinding/silkyActivationCheck'
	},
	//办理须知
	'getRuleConfig': {
		url: '/mp2c',
		method: '/apply/getRuleConfig'
	},
	//获取车型
	'getCarClass': {
		url: '/mp2c',
		method: '/apply/matchVehicleModel'
	},
	//设置操作员
	'setPromoterId': {
		url: '/mp2c',
		method: '/apply/setPromoterId'
	},
	//日志打印
	'printLog': {
		url: '/mp2c',
		method: '/printLog/show'
	},
	/* 抖音线上发行  ============================================== */
	//日志打印
	'dyGetApplyId': {
		url: '/mp2c',
		method: '/hsDouYinOrder/addDouYinNo'
	},
	//日志打印
	'selectDouYinOrder': {
		url: '/mp2c',
		method: '/hsDouYinOrder/selectDouYinNo'
	},
	//获取草稿
	'dyGetDraft': {
		url: '/mp2c',
		method: '/hsDouYinOrder/getDraft'
	},
	//保存草稿
	'dySaveDraft': {
		url: '/mp2c',
		method: '/hsDouYinOrder/saveDraft'
	},
	//车牌唯一性校验
	'dyCheckVehicleNo': {
		url: '/mp2c',
		method: '/hsDouYinOrder/checkVehicleNo'
	},
	//车辆唯一性校验
	'dyCheckVehicle': {
		url: '/mp2c',
		method: '/hsDouYinOrder/checkVehicle'
	},
	//车辆唯一性校验
	'dyGetCarClass': {
		url: '/mp2c',
		method: '/hsDouYinOrder/matchVehicleModel'
	},
	//车辆唯一性校验
	'dyCreateApplySheet': {
		url: '/mp2c',
		method: '/hsDouYinOrder/submit'
	},
	//抖音详情
	'dyApplyOrderDetail': {
		url: '/mp2c',
		method: '/hsDouYinOrder/orderDetails'
	},
	//办理进度
	'dyGetTimeLineList': {
		url: '/mp2c',
		method: '/hsDouYinOrder/timeAxis'
	},
	//取消订单
	'dyDancelApplyOrder': {
		url: '/mp2c',
		method: '/hsDouYinOrder/cancelOrder'
	},
	//获取配置
	'dyGetRuleConfig': {
		url: '/mp2c',
		method: '/tikTok/configQuery'
	},
	//修改订单
	'dyOrderUpdate': {
		url: '/mp2c',
		method: '/hsDouYinOrder/update'
	},
	


}
export default issue_apply;