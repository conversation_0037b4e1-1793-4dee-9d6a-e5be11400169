// 发行激活流程相关接口
const interface_issue = {
	//新发发行与激活接口	
	//销售订单
	'saleOrder': {
		url: "/mp2c",
		method: '/issue/sale-order'
	},
	//更换补办卡发行前初校验
	'cardIssuedValid': {
		url: "/mp2c",
		method: '/afterSales/exchangeAndReissue/order/cardIssuedValid'
	},
	//更换补办OBU发行前初校验
	'obuActivateValid': {
		url: "/mp2c",
		method: '/afterSales/exchangeAndReissue/order/obuActivateValid'
	},
	//CPU换卡发行前校验
	'beforeCardChangeValid': {
		url: "/mp2c",
		method: '/issue/card-change-valid'
	},
	//CPU换卡发卡完成
	'cardChangeComplete': {
		url: "/mp2c",
		method: '/issue/card-change-complete'
	},
	//OBU更换前校验
	'beforeObuChangeValid': {
		url: "/mp2c",
		method: '/issue/obu-change-valid'
	},
	//OBU更换完成
	'obuChangeComplete': {
		url: "/mp2c",
		method: '/issue/obu-change-complete'
	},
	//卡发行前校验
	'beforeCardIssue': {
		url: "/mp2c",
		method: '/issue/card-valid'
	},
	//写0016
	'newIssueWrite0016': {
		url: "/mp2c",
		method: '/issue/card-write16'
	},
	//写0015
	'newIssueWrite0015': {
		url: "/mp2c",
		method: '/issue/card-write15'
	},
	//初始化圈存
	'cardInitLoad': {
		url: "/mp2c",
		method: '/issue/card-init-load'
	},
	//初始化圈存完成接口
	'initLoadComplete': {
		url: "/mp2c",
		method: '/issue/card-init-load-complete'
	},
	//发卡成功
	'cardComplete': {
		url: "/mp2c",
		method: '/issue/card-complete'
	},
	//OBU发行前校验
	'beforeObuIssue': {
		url: "/mp2c",
		method: '/issue/obu-valid'
	},
	//OBU写系统信息文件
	'obuWriteSysinfo': {
		url: "/mp2c",
		method: '/issue/obu-write-sysinfo'
	},
	//OBU写车辆信息文件
	'obuWriteCarinfo': {
		url: "/mp2c",
		method: '/issue/obu-write-carinfo'
	},
	//OBU读车辆信息
	'obuRead': {
		url: "/mp2c",
		method: '/issue/obu-read'
	},
	//OBU发行成功
	'obuWriteComplete': {
		url: "/mp2c",
		method: '/issue/obu-write-complete'
	},
	//OBU激活取密钥
	'obuIssueActivation': {
		url: "/mp2c",
		method: '/issue/obu-issue-activation'
	},
	//OBU激活上报
	'obuIssueActivationComplete': {
		url: "/mp2c",
		method: '/issue/obu-issue-activation-complete'
	},
	//检查设备发行状态
	'checkIssueStatus': {
		url: "/mp2c",
		method: '/tripartiteCall/checkIssueStatus'
	},
	//激活记录创建
	'addActivateRecord': {
		url: "/mp2c",
		method: '/activateRecord/add'
	},
	//激活记录更新
	'updateActivateRecord': {
		url: "/mp2c",
		method: '/activateRecord/update'
	},
	//获取客服工单跳转链接
	'customerServiceUrl': {
		url: "/mp2c",
		method: '/customer/customerServiceUrl'
	},
	//更换补办C端调用订单完成
	'exchangeAndReissueComplete': {
		url: "/mp2c",
		method: '/afterSales/exchangeAndReissue/order/complete'
	},
	
}
export default interface_issue;