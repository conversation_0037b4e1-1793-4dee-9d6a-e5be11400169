import interface_issue from './issueActivation.js';
import issue_apply from './issueApply.js';
import packageAPI from './package.js';
import rechargeAPI from './recharge.js';
import loginAPI from './login.js'
import tollInvoiceAPI from './tollInvoice.js'
import mapAPI from './map.js'
import evaluationAPI from './evaluation.js'
const advTransfer = {
	'advBannerList': { // 查询轮播广告
		url: '/mp2c',
		method: '/advTransfer/advBannerList'
	},
	'carouselList': { // 查询轮播资讯
		url: '/mp2c',
		method: '/advTransfer/carouselList'
	},
	'informationList': { // 资讯列表
		url: '/mp2c',
		method: '/advTransfer/informationList'
	},
	'informationDetails': { //资讯详情
		url: '/mp2c',
		method: '/advTransfer/informationDetails'
	},
	'trackUpload': { // 事件统计
		url: '/mp2c',
		method: '/advTransfer/trackUpload'
	},
	'getAdUrl': { // 事件统计
		url: '/mp2c',
		method: '/advTransfer/staticAdv'
	},
	'getFloatDialog': { //获取浮窗 
		url: '/mp2c',
		method: '/advTransfer/floatAdvList'
	},
	'getDialogList': { //获取弹窗列表
		url: '/mp2c',
		method: '/advTransfer/popUpList'
	},

}

const login = {
	'decodeWeChatPhone': { // 解密微信小程序手机号
		url: '/mp2c',
		method: '/internet/account/decodePhone'
	},
	'quickLogin': { // 微信一键登录
		url: '/mp2c',
		method: '/internet/account/quickLogin'
	},
	//获取公众号code码
	'wechatPublicCode': {
		url: '/mp2c',
		method: '/wechatPublic/code'
	},
	//获取公众号openID
	'wechatPublicOpenId': {
		url: '/mp2c',
		method: '/wechatPublic/openId'
	},
	//获取公众号openID
	'publicBindUser': {
		url: '/mp2c',
		method: '/wechatPublic/bindUser'
	},
	//微信公众号绑定关系查询
	'bindLogin': {
		url: '/mp2c',
		method: '/wechatPublic/bindLogin'
	}
}



const coupon = {
	'expandUrl': { // 跳转拓展平台招商接口
		url: '/mp2c',
		method: '/coupon/expandUrl'
	},
	'cacheImgUplaod': { // 临时图片上传接口
		url: '/mp2c',
		method: '/archives/img/upload'
	},
}

const activateApply = {
	'activateApplyVehicle': {
		url: '/mp2c',
		method: '/activateApply/inProgressList'
	},
	'activateApply': {
		url: '/mp2c',
		method: '/activateApply/submit'
	},
	//服务订单查询-自助激活
	'serviceOrderQuery': {
		url: '/mp2c',
		method: '/activateApply/serviceOrderQuery'
	},
	//订单详情
	activateApplyDetail: {
		url: '/mp2c',
		method: '/activateApply/details'
	},
	//取消订单
	activateApplyCancel: {
		url: '/mp2c',
		method: '/activateApply/cancel'
	},
	afterSalesInfo: {
		url: '/mp2c',
		method: '/activateApply/afterSalesInfo'
	},
	activateApplyModify: {
		url: '/mp2c',
		method: '/activateApply/modify'
	},
	progressDetails: {
		url: '/mp2c',
		method: '/activateApply/progressDetails'
	},
	activateApplyCheck: {
		url: '/mp2c',
		method: '/activateApply/check'
	},
	handlingInstructions: {
		url: '/mp2c',
		method: '/activateApply/handlingInstructions'
	}
}
const disputeRefund = { //争议退款相关功能开发
	'addAccount': { //新增用户
		url: '/mp2c',
		method: '/hsContactManager/addContactManager'
	},
	'authentication': { //授权
		url: '/mp2c',
		method: '/hsContactManager/authentication'
	},
	'selectVehicle': { //车辆列表
		url: '/mp2c',
		method: '/hsContactManager/chooseCar'
	},
	'accountInfo': { //用户信息
		url: '/mp2c',
		method: '/hsContactManager/chooseUser'
	},
	'accountDetail': { //用户详情
		url: '/mp2c',
		method: '/hsContactManager/details'
	},
	'editAccount': { //编辑用户
		url: '/mp2c',
		method: '/hsContactManager/editContactManager'
	},
	'accountList': { //用户列表
		url: '/mp2c',
		method: '/hsContactManager/list'
	},
	'deleteAccount': { //删除用户
		url: '/mp2c',
		method: '/hsContactManager/delete'
	},
	'getImgUrl': { //获取图片url通用接口
		url: '/mp2c',
		method: '/archives/img/upload'
	}
}
const invoiceApi = {
	'invoiceBlue': { // 普通发票申请
		url: '/mp2c',
		method: '/invoice/blue'
	},
	'invoiceRed': { // 发票红冲
		url: '/mp2c',
		method: '/invoice/red'
	},
	'invoiceQuery': { // 发票查询
		url: '/mp2c',
		method: '/invoice/query'
	},
	'invoiceQueryBizOrders': { // 待开发票原订单查询
		url: '/mp2c',
		method: '/invoice/query/bizOrders'
	},
	'invoiceQueryInvHistory': { // 已开票订单记录查询
		url: '/mp2c',
		method: '/invoice/query/invHistory'
	},
	'invoiceSend': { // 已开票订单发送邮箱
		url: '/mp2c',
		method: '/invoice/send'
	},
	'invoiceTemplateSave': { // 常用发票抬头保存及修改
		url: '/mp2c',
		method: '/invoice/templateSave'
	},
	'invoiceDelTemplate': { // 常用发票抬头删除
		url: '/mp2c',
		method: '/invoice/delTemplate'
	},
	'invoiceQueryTemplate': { // 常用发票抬头查询
		url: '/mp2c',
		method: '/invoice/queryTemplate'
	},
	'invoiceSelfApply': { // dwz自助开票抬头
		url: '/mp2c',
		method: '/invoice/invoiceSelfApply'
	},

}
const interface_4501 = {
	...login,
	...coupon,
	...activateApply,
	...interface_issue,
	...disputeRefund,
	...issue_apply,
	...invoiceApi,
	...rechargeAPI,
	...loginAPI,
	...packageAPI,
	...advTransfer,
	...tollInvoiceAPI,
	...mapAPI,
	...evaluationAPI,
	'issueRoute': {
		url: '/mp2c',
		method: '/issue/route'
	},
	'sendaAccountSms': {
		url: '/mp2c',
		method: '/internet/account/sms'
	},
	'personReg': {
		url: '/mp2c',
		method: '/internet/account/personReg'
	},
	'mobileLogin': {
		url: '/mp2c',
		method: '/internet/account/mobileLogin'
	},
	'accountLogin': {
		url: '/mp2c',
		method: '/internet/account/accountLogin'
	},
	'customerBizList': {
		url: '/mp2c',
		method: '/customerBiz/list'
	},
	'customerBizView': {
		url: '/mp2c',
		method: '/customerBiz/view'
	},
	'vehicleBizSearch': { //查询车辆信息
		url: '/mp2c',
		method: '/vehicleBiz/search'
	},
	'queryBlackList': {
		url: '/mp2c',
		method: '/vehicleBiz/blacklist'
	},
	'loadRecharge': {
		url: '/mp2c',
		method: '/load/recharge'
	},
	'loadRechargeQuery': {
		url: '/mp2c',
		method: '/load/rechargeQuery'
	},
	'loadRefund': {
		url: '/mp2c',
		method: '/load/refund'
	},
	// 圈存初始化
	'loadInit': {
		url: '/mp2c',
		method: '/load/init'
	},
	//圈存申请
	'loadApply': {
		url: '/mp2c',
		method: '/load/apply'
	},
	//圈存确认
	'loadComplete': {
		url: '/mp2c',
		method: '/load/complete'
	},
	// 锁定金额异常处理
	'loadCheck': {
		url: '/mp2c',
		method: '/load/check'
	},
	// 异常处理
	'abnormal': {
		url: '/mp2c',
		method: '/internet/card/loadAbnormal'
	},
	// 卡账户明细
	'cardAccountDetail': {
		url: '/mp2c',
		method: '/load/searchCardAccountDetail'
	},
	//圈存记录
	'loadDetail': {
		url: '/mp2c',
		method: '/load/searchLoadDetail'
	},
	'loadRefundQuery': {
		url: '/mp2c',
		method: '/load/refundQuery'
	},
	'loadRechargeList': {
		url: '/mp2c',
		method: '/load/recharge/list'
	},
	'loadCardAmount': {
		url: '/mp2c',
		method: '/load/cardAmount'
	},
	'etcAccountBind': {
		url: '/mp2c',
		method: '/etc/account/binding'
	},
	'getEtcAccountList': {
		url: '/mp2c',
		method: '/etc/account/queryAll'
	},
	'cardPayInfo': {
		url: '/mp2c',
		method: '/cardBiz/cardPayInfo'
	},
	'transfer': {
		url: '/mp2c',
		method: '/transfer'
	},
	'issueObuActiveComplet': {
		url: '/mp2c',
		method: '/obuBiz/obuActiveComplete'
	},
	//OBU激活完成
	'issueObuIssueActivation': {
		url: '/mp2c',
		method: '/obuBiz/obuActivation'
	},

	'getOpenid': { //微信code获取openid
		url: '/mp2c',
		method: '/internet/account/getOpenid'
	},
	'getCaptcha': { //获取图形验证码
		url: '/mp2c',
		method: '/internet/account/captcha'
	},
	'getCaptchaLogined': { //获取登录图形验证码
		url: '/mp2c',
		method: '/internet/account/captchaLogined'
	},
	'wxLogin': { // 微信登录
		url: '/mp2c',
		method: '/internet/account/wxLogin'
	},
	'getWeChatPhone': { // 获取微信授权号码
		url: '/mp2c',
		method: '/internet/account/getPhone'
	},
	'relationPublic': { // 关联公众号
		url: '/mp2c',
		method: '/internet/account/relation'
	},
	'uploadFile': { // 上传文件
		url: '/mp2c',
		method: '/archives/transfer'
	},
	'ocrFile': { // ocr识别
		url: '/mp2c',
		method: '/archives/transfer'
	},
	'containerRefundRecord': { //集装箱通行记录列表

		url: '/mp2c',
		method: '/outServer/http/container/issue/getRefundList',
	},
	'containerRecordDetail': { //集装箱通行记录详情
		url: '/mp2c',
		method: '/outServer/http/container/issue/getRefundDetail',
	},
	'refundUpload': { // 上传文件
		url: '/mp2c',
		method: '/archives/transfer'
	},
	'defaultBankQuery': { //查询银行账户
		url: '/mp2c',
		method: '/hsContactManager/defaultQuery',
	},
	'queryRefundConfig': { //查询是否支持原路退款
		url: '/mp2c',
		method: '/outServer/http/container/issue/queryRefundConfig',
	},
	'commitRefundForm': { //提交集装箱退费表单
		url: '/mp2c',
		method: '/outServer/http/container/issue/receiveUserRefundApply',
	},
	'refundApplyDetail': { //集装箱退费申请进度表
		url: '/mp2c',
		method: '/outServer/http/container/issue/getRefundApplyScheduleList',
	},
	'bindUser': { //微信绑定用户
		url: '/mp2c',
		method: '/internet/account/bindUser'
	},
	'resetPwd': { //忘记密码
		url: '/mp2c',
		method: '/internet/resetPwd'
	},


	'activitionAccount': {
		url: '/mp2c',
		method: '/internet/account/active'
	},
	'getAccountInfo': { // 获取用户其他信息
		url: '/mp2c',
		method: '/internet/account/view'
	},
	//获取ECSS信息
	'getEcssInfo': {
		url: '/mp2c',
		method: '/ecss/info'
	},
	//ECSS迁移
	'ecssSync': {
		url: '/mp2c',
		method: '/ecss/sync'
	},

	//OBU激活完成
	'issueObuIssueActivation': {
		url: '/obuBiz/obuActivation',
		method: '/obuBiz/obuActivation'
	},
	//订单校验
	'validOrder': {
		url: '/order/validOrder',
		method: '/order/validOrder'
	},
	//档案校验
	'validArchives': {
		url: '/archives/validArchives',
		method: '/archives/validArchives'
	},
	//激活上报
	'issueObuActiveComplet': {
		url: '/obuBiz/obuActiveComplete',
		method: '/obuBiz/obuActiveComplete'
	},
	//根据obu读取车辆
	'obuReadCar': {
		url: '/obuBiz/obuReadCar',
		method: '/obuBiz/obuReadCar'
	},
	//新卡发行前校验
	'issueChangeCardValid': {
		url: '/cardBiz/changeCardValid',
		method: '/cardBiz/changeCardValid'
	},
	//B端新发接口 end
	'getCarinfo': { // 查车辆信息
		url: '/vehicleBiz/search',
		method: '/vehicleBiz/search'
	},
	'replaceApply': { //卡片/obu补领换申请
		url: '/mp2c',
		method: '/apply/replaceApply'
	},
	'equipmentLoss': { //挂失解挂
		url: "/mp2c",
		method: "/apply/cardOperation"
	},
	'emailDevice': { //邮寄旧设备
		url: '/mp2c',
		method: "/apply/mailDevice"
	},

	//订单支付结果查询
	'getPayResult': {
		url: '/mp2c',
		method: '/order/pay/search'
	},
	'orderDetail': {
		url: '/mp2c',
		method: '/apply/appDet'
	},
	//订单支付
	'orderPay': {
		url: '/mp2c',
		method: '/order/pay'
	},
	'getOrderGoods': { //获取商品列表
		url: '/mp2c',
		method: "/order/goods"
	},
	'getOrderMarket': { //获取营销方案
		url: '/mp2c',
		method: "/order/market"
	},
	'getRegion': { //获取省市区
		url: '/mp2c',
		method: "/adress"
	},
	//根据车牌号和车牌颜色查询手机号
	'searchVeh': {
		url: '/mp2c',
		method: '/etc/account/searchVeh'
	},
	// 检验etc用户是否存在
	'accountqueryCheck': {
		url: '/mp2c',
		method: '/etc/account/queryCheck'
	},
	// ETC账户查询（其他etc用户）
	'accountqueryOther': {
		url: '/mp2c',
		method: '/etc/account/queryOther'
	},
	// 绑定其他etc账户
	'accountbindOther': {
		url: '/mp2c',
		method: '/etc/account/bindOther'
	},
	// 获取车辆列表
	'vehicleList': {
		url: '/mp2c',
		method: '/issue/vehicleList'
	},
	//查询车辆详情信息
	'queryVehicleInfo': {
		url: '/mp2c',
		method: '/internet/card/search'
	},
	//获取优惠券
	'getCouponList': {
		url: '/mp2c',
		method: '/coupon/list'
	},
	//获取分米商城跳转连接
	'getJumpUrl': {
		url: "/mp2c",
		method: "/coupon/jumpUrl"
	},

	//代充值列表
	'rechargeOther': {
		url: '/mp2c',
		method: '/load/agent/recharge/list'
	},

	//所有业务回执单
	'allReceipt': {
		url: '/mp2c',
		method: '/log/list'
	},
	//设置默认绑定用户信息
	'setDefaultUser': {
		url: '/mp2c',
		method: '/etc/account/setDefault'
	},

	//etc用户解绑
	'unBundling': {
		url: '/mp2c',
		method: '/etc/account/unbind'
	},
	//预分配
	'preAllocate': {
		url: '/mp2c',
		method: '/load/preAllocate'
	},
	//反预分配
	'disPre': {
		url: "/mp2c",
		method: '/load/deAllocate'
	},

	//根据车牌号和车牌颜色查询用户信息
	'searchCustomerInfo': {
		url: '/mp2c',
		method: '/card/findUserInfoByCarNoAndCarColor'
	},

	//补缴订单查询
	'repaymentOrderQuery': {
		url: '/mp2c',
		method: '/card/repaymentOrderQuery'
	},

	//补缴订单支付
	'afterPay': {
		url: '/mp2c',
		method: '/auditPayOrder/pay'
	},

	//补缴订单支付
	'afterPayUpdate': {
		url: '/mp2c',
		method: '/common/pay/update'
	},

	//补缴订单支付查询
	'afterPayOrderQuery': {
		url: '/mp2c',
		method: '/auditPayOrder/query'
	},

	//补缴订单详情验证授权
	'smsCheckCode': {
		url: '/mp2c',
		method: '/card/smsCheckCode'
	},

	//异议处理
	'disputeApply': {
		url: '/mp2c',
		method: '/hsAuditDispute/plead'
	},

	//异议处理详情
	'disputeDetail': {
		url: '/mp2c',
		method: '/hsAuditDispute/detail'
	},
	//查看用户合同
	'selectContracts': {
		url: '/mp2c',
		method: '/contract/contracts'
	},
	//预览合同
	'previewContracts': {
		url: '/mp2c',
		method: '/contract/preview'
	},
	//下载合同
	'downLoadContracts': {
		url: '/mp2c',
		method: '/contract/download'
	},
	//发送合同到邮箱
	'sendContractsEmail': {
		url: '/mp2c',
		method: '/contract/sendEmail'
	},
	//查看最新合同
	'queryLastContracts': {
		url: "/mp2c",
		method: '/contract/queryContracts'
	},


	//余额支付
	'balancePay': {
		url: '/mp2c',
		method: '/net/balanPayCard'
	},
	//V2.0-根据车牌号和车牌颜色查所有用户信息
	'searchCustomerInfoV2': {
		url: '/mp2c',
		method: '/card/v2/findAllUser'
	},
	//V2.0-补缴订单查询
	'repaymentOrderQueryV2': {
		url: '/mp2c',
		method: '/card/v2/repaymentOrderQuery'
	},
	//设备稽核-卡号获取车辆信息
	'getDeviceByCard': {
		url: '/mp2c',
		method: '/orderEntry/getInfo'
	},
	//设备稽核-卡号获取车辆信息
	'getDeviceDetailByCard': {
		url: '/mp2c',
		method: '/orderEntry/infoDetails'
	},
	//设备稽核-更新车辆信息
	'updateCar': {
		url: '/mp2c',
		method: '/orderEntry/rewriteInfo'
	},
	//设备稽核-更新状态
	'updateCarStatus': {
		url: '/mp2c',
		method: '/orderEntry/updateStatus'
	},
	//设备稽核-保存图片
	'updateCarSaveImg': {
		url: '/mp2c',
		method: '/orderEntry/saveImage'
	},
	//信息修正发行接口
	// 0016
	'issueWRITE0016': {
		url: '/mp2c',
		method: '/issueSystem/cardBiz/cpuWrite16'
	},
	// 0015
	'issueWRITE0015': {
		url: '/mp2c',
		method: '/issueSystem/cardBiz/cpuWrite15'
	},
	// 圈存
	'issueCPULoadApply': {
		url: '/mp2c',
		method: '/issueSystem/cardBiz/initLoad'
	},
	//obu激活成功
	'issueOBUISSUERESULT': {
		url: '/mp2c',
		method: '/issueSystem/transfer'
	},
	//卡发行成功
	'issueCPUISSUERESULT': {
		url: '/mp2c',
		method: '/issueSystem/cardBiz/cardComplete'
	},
	//obu发行前校验
	'issueOBUIssue': {
		url: '/mp2c',
		method: '/issueSystem/obuBiz/obuValid'
	},
	//卡发行前校验
	'issueCPUISSUED': {
		url: '/mp2c',
		method: '/issueSystem/cardBiz/cpuValid'
	},
	//写车辆信息
	'issueOBUISSUEDFEF01': {
		url: '/mp2c',
		method: '/issueSystem/obuBiz/obuWriteCar'
	},
	//OBU写系统信息
	'issueOBUISSUEMFEF01': {
		url: '/mp2c',
		method: '/issueSystem/obuBiz/obuWriteSys'
	},
	//OBU发行完成
	'issueObuIssueComplete': {
		url: '/mp2c',
		method: '/issueSystem/obuBiz/obuComplete'
	},
	//OBU激活完成
	'issueObuIssueActivation': {
		url: '/mp2c',
		method: '/issueSystem/obuBiz/obuActivation'
	},

	//V2.0-平安银行互联网账户登录弹框提醒
	'accountRemind': {
		url: '/mp2c',
		method: '/b2bic/remind'
	},
	//V2.0-平安银行开户详情
	'getAccountDetail': {
		url: '/mp2c',
		method: '/b2bic/view'
	},
	//车辆消费明细
	'getVehicleConsume': {
		url: '/mp2c',
		method: '/issueSystem/vehicleConsume'
	},
	//单位用户车辆账单信息
	'unitVehicleList': {
		url: '/mp2c',
		method: '/issue/unitVehicleList'
	},
	//查询可支持银行卡列表
	'getBankList': {
		url: '/mp2c',
		method: '/bankCard/supportBank'
	},

	//获取账户openId
	'getAccountOpenId': {
		url: "/mp2c",
		method: '/wechatPublic/accountOpenId'
	},
	//次次顺签约发送短信
	'silkySms': {
		url: '/mp2c',
		method: '/silky/sms'
	},

	//根据银行卡号识别名称
	'getBankName': {
		url: '/mp2c',
		method: '/bankCard/bankNameByCardNo'
	},
	//车辆状态查询列表
	'vehicleStatusList': {
		url: '/mp2c',
		method: '/issue/vehicleStatusList'
	},
	'editWarnLine': {
		url: '/mp2c',
		method: '/internet/card/warnLineModify'
	},
	'couponValue': {
		url: '/mp2c',
		method: '/coupon/couponValue'
	},
	'applyOrderDetail': { //订单详情
		url: "/mp2c",
		method: '/apply/orderDetails'
	},
	'getOpenid': { //微信code获取openid
		url: '/mp2c',
		method: '/internet/account/getOpenid'
	},
	'saveOpenId': { //保存草稿
		url: '/mp2c',
		method: '/apply/saveOpenId'
	},
	//保存openId
	'saveOpenId': {
		url: '/mp2c',
		method: '/apply/saveOpenId'
	},
	//根据银行卡号识别名称
	'getBankName': {
		url: '/mp2c',
		method: '/bankCard/bankNameByCardNo'
	},
	// 获取枚举字段
	'getDict': {
		url: '/mp2c',
		method: '/dict'
	},
	//客账充值
	'accountRecharge': {
		url: '/mp2c',
		method: '/clientAccount/accountRecharge'
	},
	//客账充值查询
	'accountRechargeSearch': {
		url: '/mp2c',
		method: '/clientAccount/accountRechargeQuery'
	},
	//互联网充值客账
	'netAccountRecharge': {
		url: '/mp2c',
		method: '/net/balanPayClient'
	},
	//客账充值记录查询
	'accountRechargeList': {
		url: '/mp2c',
		method: '/transfer/clentAccountFlow'
	},
	//客账充值退费
	'accountRechargeRefund': {
		url: '/mp2c',
		method: '/clientAccount/accountRefund'
	},

	//设备检测-蓝牙检测
	'getStatusByBlue': {
		url: '/mp2c',
		method: '/autoCheck/checkResult'
	},
	//设备检测-弹窗提醒
	'deviceRemind': {
		url: '/mp2c',
		method: '/autoCheck/remind'
	},
	//设备检测-保存弹窗提醒
	'saveDeviceRemind': {
		url: '/mp2c',
		method: '/autoCheck/saveRemind'
	},
	//充值车辆列表查询
	'rechargeVehicleList': {
		url: '/mp2c',
		method: '/issue/rechargeVehicleList'
	},

	// 次次顺签约相关接口
	// 次次顺签约查询
	'searchSilkyInfo': {
		url: '/mp2c',
		method: '/silky/info'
	},

	// 次次顺签约保存
	'saveSilkyInfo': {
		url: '/mp2c',
		method: '/silky/save',
	},
	//次次顺签约
	'silkySign': {
		url: '/mp2c',
		method: '/silky/sign'
	},
	//次次顺签约发送短信
	'silkySms': {
		url: '/mp2c',
		method: '/silky/sms'
	},
	//签约撤销
	'silkyCancel': {
		url: '/mp2c',
		method: '/silky/cancel'
	},
	//获取待签约车辆列表
	'getSikyVehicleList': {
		url: '/mp2c',
		method: '/issue/signVehicleList'
	},
	//根据银行卡号识别名称
	'getBankName': {
		url: '/mp2c',
		method: '/bankCard/bankNameByCardNo'
	},
	//查询可支持银行卡列表
	'getBankList': {
		url: '/mp2c',
		method: '/bankCard/supportBank'
	},
	//微信代扣签约
	'weChatSign': {
		url: '/mp2c',
		method: '/wechat/userState'
	},
	//微信指定卡代扣签约
	'weChatCardSign': {
		url: "/mp2c",
		method: '/wechat/preopen'
	},
	//微信指定卡代扣签约查询
	'weChatCardSignSearch': {
		url: "/mp2c",
		method: '/wechat/contracts'
	},
	//次次顺签约服务费
	'serviceRate': {
		url: "/mp2c",
		method: '/cardSign/silky/serviceRate'
	},
	'selfWorkflowList': {
		url: '/mp2c',
		method: '/customer/selfWorkflowList'
	},
	// aliyun 获取上传凭证
	'getUploadAuth': {
		url: '/mp2c',
		method: '/aliyunVod/getUploadAuth'
	},
	// 账户过期提醒
	'refundAccountPass': {
		url: '/mp2c',
		method: '/hsContactManager/overdue'
	},
	// 账户过期更新
	'updatePass': {
		url: '/mp2c',
		method: '/hsContactManager/editOverdue'
	},
	//获取签章浏览pdf---新办和抖音调用
	'getSignPreview': {
		url: '/mp2c',
		method: '/apply/sign/preview'
	},
	//签章pdf预览----产品转换B端调用
	'newSignPreview': {
		url: '/mp2c',
		method: '/contract/sign/preview'
	},
	// 完结确认B端确认
	'signConfirmByB': {
		url: '/mp2c',
		method: '/contract/sign/confirm'
	},
	// 完结确认
	'signConfirm': {
		url: '/mp2c',
		method: '/apply/sign/confirm'
	},
	// 查询是否需要补签协议
	'needResign': {
		url: '/mp2c',
		method: '/contract/needResign'
	},
	// 补签营销活动
	'resignMarketListV2': {
		url: '/mp2c',
		method: '/contract/resignMarketListV2'
	},
	//2025.2.18  判断签署个人承诺书方式
	'contractOwner': {
		url: '/mp2c',
		method: '/contract/owner'
	},
	// 吉鸿解约车辆
	'jiHongTermination': {
		url: '/mp2c',
		method: '/contract/list/termination'
	},

	// 线上OBU蓝牙校验接口
	'checkObuBlue': {
		url: '/mp2c',
		method: '/obuWhiteList/checkObu '
	},
	
}
export default interface_4501;