import interface_issue from './issueActivation.js';
const changeApply = {
	'exchangeApply': {
		url: '/mp2c',
		method: '/aftersales/exchange/apply'
	},
	'exchangeDoor': {
		url: '/mp2c',
		method: '/aftersales/exchange/door2door'
	},
	'exchangeSelfReturn': {
		url: '/mp2c',
		method: '/aftersales/exchange/selfReturn'
	},
	//更换支付接口
	'exchangePay': {
		url: '/mp2c',
		method: '/hsExchangeReissuePayOrder/pay'
	},
	'exchangeQueryPay': {
		url: '/mp2c',
		method: '/aftersales/exchange/queryPay'
	},
	'exchangeUpdatePayStatus': {
		url: '/mp2c',
		method: '/aftersales/exchange/updatePayStatus'
	},
	'confirmGoods': {
		url: '/mp2c',
		method: '/aftersales/exchange/confirmGoods'
	},
	'exchangeImgList': {
		url: '/mp2c',
		method: '/aftersales/exchange/imgList'
	},
	'getEtcAddress': {
		url: '/mp2c',
		method: '/aftersales/exchange/etcAddress'
	}
}
const replacementApply = {
	'reissueApply': {
		url: '/mp2c',
		method: '/aftersales/reissue/apply'
	},
	'reissueOrderDetail': {
		url: '/mp2c',
		method: '/aftersales/reissue/detail'
	},
	'reissueCancel': {
		url: '/mp2c',
		method: '/aftersales/reissue/cancel'
	},
	'exchangeExpress': {
		url: '/mp2c',
		method: '/aftersales/exchange/express'
	},
	'exchangeEditImg': {
		url: '/mp2c',
		method: '/aftersales/exchange/editImg'
	},
	'editAddress': {
		url: '/mp2c',
		method: '/aftersales/reissue/express/edit'
	},
	'exchangeProcess': {
		url: '/mp2c',
		method: '/aftersales/exchange/process'
	},
	'exchangeCheck': {
		url: '/mp2c',
		method: '/aftersales/exchange/check'
	},
	'exchangeInfomation': {
		url: '/mp2c',
		method: '/aftersales/exchange/information'
	},
	'getAmount': {
		url: '/mp2c',
		method: '/aftersales/reissue/fare/getAmount'
	}
}

const onlineLogout = {
	'beforCheckLogout': { //线上注销前校验接口
		url: '/mp2c',
		method: '/onlineCancel/checkLogout'
	},
	'getLogoutEnum': { //退款渠道枚举
		url: '/mp2c',
		method: '/onlineCancel/getLogoutEnum'
	},
	'getLogoutConfig': { //办理须知
		url: '/mp2c',
		method: '/onlineCancel/getLogoutConfig'
	},
	'logoutApply': { //申请提交
		url: '/mp2c',
		method: '/onlineCancel/logoutApply'
	},
	'logoutDeviceHandle': { //手动注销
		url: '/mp2c',
		method: '/onlineCancel/logoutDeviceHandle'
	},
	'logoutRecordCancel': { //订单取消
		url: '/mp2c',
		method: '/onlineCancel/logoutRecordCancel'
	},
	'getLogoutDetail': { //注销详情
		url: '/mp2c',
		method: '/onlineCancel/getLogoutDetail'
	},
	'getLogoutLog': { //注销办理日志
		url: '/mp2c',
		method: '/onlineCancel/getLogoutLog'
	},
	'smsCheckCodeV1': { //身份验证短信接口
		url: '/mp2c',
		method: '/onlineCancel/smsCheckCode'
	},
	'logoutUpdate': { //详情修改接口
		url: '/mp2c',
		method: '/onlineCancel/logoutUpdate'
	},
	'smsCheckCodeV2': { //新校验短信验证码--线上注销专用
		url: '/mp2c',
		method: '/onlineCancel/checkSmsCode'
	},
	'sendaAccountSmsV2': { //新发送短信验证码--线上注销专用
		url: '/mp2c',
		method: '/onlineCancel/sendSmsCode'
	},

}

const productConver = {
	'beforCheckConver': { //产品转换前校验接口
		url: '/mp2c',
		method: '/productChange/check'
	},
	'productConfig': { //产品转换办理须知
		url: '/mp2c',
		method: '/productChange/configDetail'
	},
	'productApply': { //产品转换前校验接口
		url: '/mp2c',
		method: '/productChange/changeApply'
	},
	'productConverDetail': { //产品转换详情接口
		url: '/mp2c',
		method: '/productChange/getChangeDetail'
	},
	'productConverUpdate': { //产品转换详情接口
		url: '/mp2c',
		method: '/productChange/changeUpdate'
	},
	'productMarket': { //产品转换获取营销方案
		url: '/mp2c',
		method: '/productChange/getMarket'
	},
	/* ============产品转换新接口==========  */
	'productApplyList': { //产品转换
		url: '/mp2c',
		method: '/productChange/getProductTypeList'
	},
	//查询卡余额是否需要退费
	'getCardAccountAmount': { //产品转换
		url: '/mp2c',
		method: '/productChange/getCardAccountAmount'
	},
	//查看签署状态
	'cardAccountRefund': {
		url: '/mp2c',
		method: '/productChange/cardAccountRefund'
	},
	//查看签署状态
	'searchSignStatus': {
		url: '/mp2c',
		method: '/contract/signStatus'
	},
	// 获取互联网账户信息
	'getAccountView': {
		url: '/account/view',
		method: '/account/view'
	},
	//产品转换支付
	'converPay': {
		url: '/mp2c',
		method: '/transform/pay'
	},
	//产品转换支付结果查询
	'converPaySearch': {
		url: '/mp2c',
		method: '/transform/paySearch'
	},


}

const interface_afterSale = {
	...changeApply,
	...replacementApply,
	...onlineLogout,
	...productConver
}
export default interface_afterSale;