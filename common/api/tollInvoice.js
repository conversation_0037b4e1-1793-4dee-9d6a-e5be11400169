// 通行费发票流程相关接口
const toll_invoice = {
	//鉴权校验
	'tollAuthCheck': {
		url: '/mp2c',
		method: '/toll/authCheck'
	},
	//验证码
	'tollAuthMobile': {
		url: '/mp2c',
		method: '/toll/authMobile'
	},
	//2.2发行方用户登录
	'tollAuthLogin': {
		url: '/mp2c',
		method: '/toll/authLogin'
	},
	//3.1用户卡列表查询
	'tollCardList': {
		url: '/mp2c',
		method: '/toll/cardList'
	},
	//3.2手机验证码绑卡校验
	'tollBindCardBindCheck': {
		url: '/mp2c',
		method: '/toll/cardBindCheck'
	},
	//3.3发送绑卡验证码
	'tollCardBindSendCode': {
		url: '/mp2c',
		method: '/toll/cardBindSendCode'
	},
	//3.4用户绑卡
	'tollCardBindNew': {
		url: '/mp2c',
		method: '/toll/cardBindNew'
	},
	//查询需要绑定的开票车辆
	'tollVehicleQuery': {
		url: '/mp2c',
		method: '/toll/vehicleQuery'
	},
	//获取图形验证码
	'tollGetCaptcha': {
		url: '/mp2c',
		method: '/toll/getCaptcha'
	},
	//校验图形验证码
	'tollCaptchaCheck': {
		url: '/mp2c',
		method: '/toll/captchaCheck'
	},
	//4.4发票抬头列表查询
	'tollTitleSearch': {
		url: '/mp2c',
		method: '/toll/titleSearch'
	},
	//查询ETC用户关联的车辆
	'tollAssociatedVehicle': {
		url: '/mp2c',
		method: '/toll/associatedVehicle'
	},
	//抬头是否存在校验
	'tollTitleExists': {
		url: '/mp2c',
		method: '/toll/titleExists'
	},
	//关联车辆并添加发票抬头
	'tollTitleAddProcess': {
		url: '/mp2c',
		method: '/toll/titleAddProcess'
	},
	//抬头信息详情
	'tollTitleDetail': {
		url: '/mp2c',
		method: '/toll/titleDetail'
	},
	//4.5发票抬头已关联卡查询
	'tollTitleCardList': {
		url: '/mp2c',
		method: '/toll/titleCardList'
	},
	//4.3删除发票抬头
	'tollTitleDelete': {
		url: '/mp2c',
		method: '/toll/titleDelete'
	},
	//4.6发票抬头关联用户卡
	'tollTitleAddCard': {
		url: '/mp2c',
		method: '/toll/titleAddCard'
	},
	//5.1通行交易查询
	'tollInvoiceTransList': {
		url: '/mp2c',
		method: '/toll/invoiceTransList'
	},
	//5.2充值交易查询
	'tollInvoiceRechargeList': {
		url: '/mp2c',
		method: '/toll/invoiceRechargeList'
	},
	//5.3消费发票开具申请
	'tollInvoiceTransApply': {
		url: '/mp2c',
		method: '/toll/invoiceTransApply'
	},
	//5.4充值发票开具申请
	'tollInvoiceRechargeApply': {
		url: '/mp2c',
		method: '/toll/invoiceRechargeApply'
	},
	//开票历史整合
	'tollInvoiceHistory': {
		url: '/mp2c',
		method: '/toll/invoiceHistory'
	},
	//发票重推
	'tollInvoicePush': {
		url: '/mp2c',
		method: '/toll/invoicePush'
	},
	//开票历史详情
	'tollInvoiceInvoiceList': {
		url: '/mp2c',
		method: '/toll/invoiceInvoiceList'
	},
	//5.7更换抬头申请
	'tollInvoiceChangeTitle': {
		url: '/mp2c',
		method: '/toll/invoiceChangeTitle'
	},
	//5.9获取通行记录
	'tollInvoiceTransInvoiced': {
		url: '/mp2c',
		method: '/toll/invoiceTransInvoiced'
	},
	//5.10获取充值记录
	'tollInvoiceRechargeInvoiced': {
		url: '/mp2c',
		method: '/toll/invoiceRechargeInvoiced'
	},
	//5.13按申请ID、卡号查询发票下载链接
	'tollInvoiceList': {
		url: '/mp2c',
		method: '/toll/invoiceList'
	},
	//记录按月份查询，日历
	'tollTrafficMonth': {
		url: '/mp2c',
		method: '/toll/trafficMonth'
	},

	//查询ETC用户是否含有月月行
	'defaultQueryOption': {
		url: '/mp2c',
		method: '/invoiceSelf/defaultQueryOption'
	},
	//待开发票查询
	'otherInvoiceBizOrders': {
		url: '/mp2c',
		method: '/invoiceSelf/query/bizOrders'
	},
	//开票
	'otherBlue': {
		url: '/mp2c',
		method: '/invoiceSelf/blue'
	},
	//模糊查询
	'getInvoiceCompanyInfo': {
		url: '/mp2c',
		method: '/hsInvoice/getInvoiceCompanyInfo'
	},
	//已开发票查询
	'otherInvHistory': {
		url: '/mp2c',
		method: '/invoiceSelf/query/invHistory'
	},
	//抬头查询
	'otherQueryTemplate': {
		url: '/mp2c',
		method: '/invoiceSelf/queryTemplate'
	},
	//新增抬头
	'otherTemplateSave': {
		url: '/mp2c',
		method: '/invoiceSelf/templateSave'
	},
	//发票重推
	'otherReSend': {
		url: '/mp2c',
		method: '/invoiceSelf/send'
	},
	//发票红冲
	'otherRed': {
		url: '/mp2c',
		method: '/invoiceSelf/red'
	},
	//是否添加新抬头
	'otherIsDefaultTemplate': {
		url: '/mp2c',
		method: '/invoiceSelf/isDefaultTemplate'
	},
	//删除抬头
	'otherDelTemplate': {
		url: '/mp2c',
		method: '/invoiceSelf/delTemplate'
	},
	//换票
	'otherChange': {
		url: '/mp2c',
		method: '/invoiceSelf/changeInvoiceHeader'
	},
	

}
export default toll_invoice;