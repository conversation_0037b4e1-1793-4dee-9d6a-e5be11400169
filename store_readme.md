----currentPerson(3.1 获取用户基本信息  6.1 身份证信息识别)	 ------ 缓存名称（---接口名---）  
存入文件路径 pages/tab-bar/index/index  components\authentication\id-auth

	字段名			描述      			类型     规则说明			
	cardNum      ---身份证号  			String
	realName     ---姓名      			String   
	address      ---地址      			String   
	phone        ---手机号    			String
	status		 ---状态      			Integer   1-未实名 2-已实名 3-活体认证
	
----prodectinfo(3.1 获取用户基本信息  6.1 身份证信息识别)	 ------ 

----currentCar(6.2 行驶证信息识别)	 ------ 缓存名称（---接口名---）  
存入文件路径 components\authentication\car-auth
	字段名			描述      	    类型     		规则说明			
	vin				车架号			string			
	engineNum		发动机号			string			
	issueDate		发证日期			string			YYYY-MM-DD
	name			姓名			string	
	plateNum		车牌号			string	
	registerDate	注册日期			string	
	useCharacter	使用性质        Integer         1:营运 2:非营运
	vehicleType		车辆类型        string
	approvedCount   核定人数 		Integer         客车必填

----currentCar(6.2 行驶证信息识别)	 ------ 

----recieveAddress(7.9 获取收货地址列表)	 ------ 缓存名称（---接口名---）  
存入文件路径 pages/etc/select-adress/select-adress
	字段名			描述      	    类型     		规则说明			
	areaId			区域编码   		String
	address			详细地址			string			
	phone			联系电话			string		
	name			联系人			String
----SET_RECIEVEADDRESS(7.9 获取收货地址列表)	 ------ 

----prodectinfo(5.8 获取发行方产品列表)	 ------ 缓存名称（---接口名---）  
存入文件路径 pages/etc/etc-product-detail/etc-product-detail
	字段名			描述      类型     规则说明
	productId    ---产品编号  String
	issuer       ---发行方    String   
	comment      ---描述      String   优惠活动等
	name         ---名称      String
				
----prodectinfo(5.8 获取发行方产品列表)	 ------ 

