var gbk2 = require('./gbk2.js')

var sendCallback;
var BleError = {
	scan: 0, // 搜索失败
	connect: 1, // 连接失败
	disconnect: 2, // 断开连接失败
	readCardInfo: 3, // 读取卡片信息失败
	readCarNoInObu: 4, // 读取OBU车辆信息失败
	readObuSn: 5, // 读取OBU号失败
	checkCardNo: 6, // 卡内车牌和OBU内车牌不一致
	readObuEF01: 7, // 读取OBU EF01文件异常
	cardStatusError: 8, // 卡片状态异常
	cardManageQueryCardInfo: 9, // cardManageQueryCardInfo接口异常，查询不到卡片
	getACRam: 10, // 获取OBU激活随机数
	activating: 11, // 激活指令失败

	selectError: 12, // 卡片文件目录选择失败
	randomError: 13, // 卡片获取随机数失败
	writeError: 14, // 卡片写文件失败
	readCardAmountError: 15, // 读取卡片金额异常
	pinError: 16, // 卡片圈存前pin校验异常
	rechargeInitError: 17, // 圈存初始化异常
	rechargeWriteCardError: 18, // 圈存写卡异常

	obuSelectError: 19, // OBU文件选取失败
	obuReadInfoError: 20, // OBU文件读取失败
	obuWriteError: 21, // OBU写文件失败
	obuRandomError: 22, // OBU获取随机数失败
}

//nfc实例
var nfc = null;
//isoDep实例
var isoDep = null;

//数据发送数组
var sendBufferArray = null
//发送索引
var sendIndex = 0
//重发次数
var resendCount = 3
//NFC错误码对照表
var NFCError = {
	"13000": "设备不支持NFC",
	"13001": "系统NFC开关未打开",
	"13010": "未知错误",
	"13019": "用户未授权",
	"13011": "参数无效",
	"13012": "将参数解析为NdefMessage失败",
	"13021": "已经开始NFC扫描",
	"13018": "尝试在未开始NFC扫描时停止NFC扫描",
	"13022": "标签已经连接",
	"13023": "尝试在未连接标签时断开连接",
	"13013": "未扫描到NFC标签",
	"13014": "无效的标签技术",
	"13015": "从标签上获取对应技术失败",
	"13024": "当前标签技术不支持该功能",
	"13017": "相关读写操作失败",
	"13016": "连接失败"
}

/**
 * 开始搜索卡片并连接
 * @param { function} callback 
 */
export function StartNfcAndConnect(callback) {
	nfc = wx.getNFCAdapter()
	nfc.onDiscovered(res => {
		if (res.techs.includes(nfc.tech.isoDep)) {
			isoDep = nfc.getIsoDep()
			isoDep.connect({
				success(data) {
					callback && callback({
						code: 0,
						err_msg: "连接卡片成功"
					})
				},
				fail(err) {
					callback && callback({
						code: err.errCode,
						err_msg: NFCError[err.errCode]
					})
				}
			})
		}
	})
	nfc.startDiscovery({
		fail(err) {
			console.log('failed to discover:', err)
			callback && callback({
				code: err.errCode,
				err_msg: NFCError[err.errCode]
			})
		}
	})
}

/**
 * 关闭NFC连接
 * @param {Object} callback 回调
 */
export function CloseNfc(callback) {
	if (isoDep && isoDep.isConnected) {
		isoDep.close({
			success: (res) => {
				console.log(res, '成功回调');
				let result = {
					data: res
				}
				console.log(nfc, '99999999999999');
				nfc.offDiscovered(res => {
					console.log(res, '000000000000000');
					nfc = null
					callback(result);
				})

			},
			fail: (err) => {
				console.log(err, '失败回调');
				let result = {
					data: err
				}
				callback(result);
			},

		})
		isoDep = null
	}


}
/**
 * 获取卡内余额
 * @param {Object} callback 回调
 */
export function GetBalance(callback) {
	CpuCommand(["805c000204"], result => {
		if (result.code == 0) {
			console.log("获取余额返回", result)
			// let balance = hex2int(result.data.substr(0, 8));
			let balance = result.data.substr(0, 8);
			callback && callback({
				code: result.code,
				data: balance
			})
		} else {
			callback && callback(result)
		}
	})
}
/**
 * 获取ETC卡号
 * @param {Object} callback 回调
 * 
 */
export function GetCardNo(callback) {
	GetCardFile15(result => {
		if (result.code == 0) {
			callback && callback({
				code: result.code,
				data: result.data.cardNo
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "卡片读取卡号失败"
			})
		}
	})
}

/**
 * 获取车辆信息
 * @param {Object} callback 回调
 * 
 */
export function GetVehicleInfo(callback) {
	GetCardFile15(result => {
		if (result.code == 0) {
			callback && callback({
				code: result.code,
				data: result.data
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "卡片读取卡号失败"
			})
		}
	})
}

/**
 * 获取0015文件信息
 * @param {Object} callback 回调
 */
export function GetCardFile15(callback) {
	CpuCommand(["00A40000023F00"], result => {
		if (result.code == 0) {
			CpuCommand(["00A40000021001"], result => {
				if (result.code == 0) {
					CpuCommand(["00b095002b"], result => {
						if (result.code == 0) {
							let reapdu = result.data
							let f0015 = {
								cardType: hex2int(reapdu.substr(16, 2)),
								version: reapdu.substr(18, 2),
								cardNo: reapdu.substr(20, 20),
								startDate: reapdu.substr(40, 8),
								endDate: reapdu.substr(48, 8),
								licenceNo: convertLisenceNo(reapdu.substr(56, 24)),
								licenceColor: parseInt(reapdu.substr(18, 2)) >= 40 ?
									code2Color(reapdu.substr(82, 2)) : code2Color(reapdu
										.substr(84, 2)),
								carType: reapdu.substr(18, 2) >= 40 ? reapdu.substr(84, 2) :
									''
							}
							console.log("0015", f0015)
							callback && callback({
								code: result.code,
								data: f0015
							})
						} else {
							callback && callback({
								code: result.code,
								err_msg: "读取0015文件失败"
							})
						}
					})
				} else {
					callback && callback({
						code: result.code,
						err_msg: "读取0015文件进入1001目录失败"
					})
				}
			})
		} else {
			callback && callback({
				code: result.code,
				err_msg: "读取0015文件进入3F00目录失败"
			})
		}
	})
}
//#region 
//卡内交易列表
var tradeList = new Array()
//交易记录查询回调
var tradeCallback

var tradeIndex = 1
var tradeStr
export function GetTradeList(callback) {
	tradeCallback = callback
	GetCardNo(result => {
		if (result.code == 0) {
			//验证pin
			CpuCommand(["0020000006313233343536"], result => {
				if (result.code == 0) {
					tradeIndex = 1
					tradeStr = ''
					tradeList = new Array()
					RunGetTradList()
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}

function RunGetTradList() {
	let p = parseInt(tradeIndex).toString(16);
	let itemIndex = new Array(2 - (p + '').length + 1).join('0') + p;
	CpuCommand(["00b2" + itemIndex + "c417"], result => {
		if (result.code == '0' || result.code == '6a83' || result.code == '6A83') {
			let realData = result.data
			let tradeInfo = {
				tradeNo: hex2int(realData.substr(0, 4)),
				overDraft: realData.substr(4, 6),
				tradeMoney: hex2int(realData.substr(10, 8)),
				tradeType: realData.substr(18, 2),
				termId: realData.substr(20, 12),
				date: realData.substr(32, 8),
				time: realData.substr(40, 6)
			}
			console.log(tradeInfo, '流水信息');
			if (result.code == 0 && tradeInfo.tradeType != 'ff' && tradeInfo.tradeType != 'FF' && tradeInfo
				.tradeType != '00') {
				tradeList.push(tradeInfo)
				tradeIndex++
				RunGetTradList()
			} else {
				if (tradeList.length == 0) {
					tradeCallback && tradeCallback({
						code: 0,
						data: "0|0"
					})
				} else {
					console.log(tradeList, '11111111111111')
					tradeStr = "0|" + tradeList.length
					for (let i = 0; i < tradeList.length; i++) {
						let item = tradeList[i]
						tradeStr += "|" + item.tradeNo +
							"|" + item.overDraft +
							"|" + item.tradeMoney +
							"|" + item.tradeType +
							"|" + item.termId +
							"|" + item.date +
							"|" + item.time
					}
					tradeCallback && tradeCallback({
						code: 0,
						data: tradeStr
					})
				}
			}
		} else {
			tradeCallback && tradeCallback({
				code: result.code,
				data: tradeStr,
				err_msg: "获取流水失败"
			})
		}
	})
}
function checkPinCode (pinCode,nextDevResult){
	CpuCommand(['0020000006313233343536'],result=>{
		if(result.code== 0){
			nextDevResult && nextDevResult(result)
		} else {
			CpuCommand(['0020000003888888'],result =>{
				nextDevResult && nextDevResult(result)
			})
		}
	})
}
//#endregion 
/**
 * 储值卡圈存
 * @param {Object} pinCode pin码
 * @param {Object} money 圈存金额
 * @param {Object} issue_times 圈存时间
 * @param {Object} terminalNo 终端机编号
 * @param {Object} getMac2 取mac2回调
 * @param {Object} nextDevResult 圈存结果回调
 */
export function Load(pinCode, money, issue_times, terminalNo, getMac2, nextDevResult) {
	GetCardFile15(result => {
		
		if (result.code == 0) {
			let cpuInfo = result.data;
			//验证pin
			checkPinCode(pinCode, result => {
				if (result.code == 0) {
					let p = parseInt(money).toString(16);
					let hexMoney = new Array(8 - (p + '').length + 1).join('0') + p;
					let version = '01'
					if(cpuInfo.version && Number(cpuInfo.version)>50){
						version = '41'
					}
					console.log('cpuInfo',cpuInfo);
					console.log('初始化圈存指令===>>>>>>','805000020B'+ version + hexMoney + terminalNo + '10')
					//初始化圈存
					CpuCommand(['805000020B'+ version + hexMoney + terminalNo + '10'],
						result => {

							if (result.code == 0) {
								console.log('初始化圈存===>>>>>',result.data)
								let realData = result.data.toLocaleUpperCase()
								let balance = realData.substr(0, 8)
								let tradeNo = realData.substr(8, 4)
								let rand = realData.substr(16, 8)
								let mac1 = realData.substr(24, 8)
								var keyVersion = realData.substr(12, 2); //交易密钥版本
								var algorithm = realData.substr(14, 2); //算法标识
								//获取mac2
								getMac2 && getMac2(rand, tradeNo, mac1, balance, keyVersion,algorithm,result => {
									//获取mac2成功
									if (result.code == 0) {
										//圈存
										CpuCommand(['805200000B' + issue_times + result
											.data
										], result => {
											if (result.code == 0) {
												nextDevResult && nextDevResult(
													result)
											} else {
												nextDevResult && nextDevResult({
													code: BleError
														.rechargeWriteCardError,
													err_msg: "卡片圈存失败"
												})
											}
										})
									} else {
										nextDevResult && nextDevResult({
											code: BleError.rechargeInitError,
											err_msg: "卡片圈存获取mac2失败"
										})
									}
								})
							} else {
								nextDevResult && nextDevResult({
									code: BleError.rechargeInitError,
									err_msg: "卡片初始化圈存失败"
								})
							}
						})
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin码校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}

/**
 * 获取异常处理卡mac1
 * @param {Object} pinCode pin码
 * @param {Object} money 圈存金额
 * @param {Object} issue_times 圈存时间
 * @param {Object} terminalNo 终端机编号
 * @param {Object} getMac2 取mac2回调
 * @param {Object} nextDevResult 圈存结果回调
 */
export function GetTradeNoAndMac1(pinCode, money, issue_times, terminalNo, getMac2, nextDevResult) {
	GetCardFile15(result => {
		if (result.code == 0) {
			let cpuInfo = result.data;
			//验证pin
			checkPinCode(pinCode, result => {
				if (result.code == 0) {
					let p = parseInt(money).toString(16);
					let hexMoney = new Array(8 - (p + '').length + 1).join('0') + p;
					//初始化圈存
					let version = '01'
					if (cpuInfo.version && Number(cpuInfo.version) > 50) {
						version = '41'
					}
					console.log('SDK获取版本等信息=======>>>>>', version, hexMoney, terminalNo)
					CpuCommand(['805000020B' + version + hexMoney + terminalNo + '10'],
						result => {
							console.log('SDK获取卡信息======>>>>>>', result, result.data)
							if (result.code == 0) {
								let realData = result.data.toLocaleUpperCase()
								let balance = realData.substr(0, 8)
								let tradeNo = realData.substr(8, 4)
								let rand = realData.substr(16, 8)
								let mac1 = realData.substr(24, 8)
								//获取mac2
								getMac2 && getMac2(rand, tradeNo, mac1, balance,
									result => {
										//获取mac2成功
										if (result.code == 0) {
											nextDevResult && nextDevResult(
												result)
										} else {
											nextDevResult && nextDevResult({
												code: BleError
													.rechargeInitError,
												err_msg: "初始化圈存获取mac2失败"
											})
										}
									})
							} else {
								nextDevResult && nextDevResult({
									code: BleError.rechargeInitError,
									err_msg: "初始化圈存（初始化）失败"
								})
							}
						})
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin马校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}


export function GetCurBalanceAndTradeNo(pinCode, money, issue_times, terminalNo, nextDevResult) {
	GetCardFile15(result => {
		if (result.code == 0) {
			let cpuInfo = result.data;
			//验证pin
			checkPinCode(pinCode, result => {
				if (result.code == 0) {
					let p = parseInt(money).toString(16);
					let hexMoney = new Array(8 - (p + '').length + 1).join('0') + p;
					//初始化圈存
					let version = '01'
					if(cpuInfo.version && Number(cpuInfo.version)>50){
						version = '41'
					}
					CpuCommand(['805000020B'+ version + hexMoney + terminalNo + '10'],
						result => {
							if (result.code == 0) {
								let balance = result.data.substr(0, 8)
								let tradeNo = result.data.substr(8, 4)
								let rand = result.data.substr(34, 8)
								let mac1 = result.data.substr(42, 8)
								nextDevResult && nextDevResult({
									data: {
										balance: balance,
										tradeNo: tradeNo
									},
									code: result.code
								})
							} else {
								nextDevResult && nextDevResult({
									code: BleError.rechargeInitError,
									err_msg: "圈存（初始化）失败"
								})
							}
						})
				} else {
					nextDevResult && nextDevResult({
						code: BleError.pinError,
						err_msg: "pin码校验失败"
					})
				}
			})
		} else {
			nextDevResult && nextDevResult({
				code: BleError.checkCardNo,
				err_msg: "卡号读取失败"
			})
		}
	})
}

function hex2int(hex) {
	var len = hex.length,
		a = new Array(len),
		code;
	for (var i = 0; i < len; i++) {
		code = hex.charCodeAt(i);
		if (48 <= code && code < 58) {
			code -= 48;
		} else {
			code = (code & 0xdf) - 65 + 10;
		}
		a[i] = code;
	}

	return a.reduce(function(acc, c) {
		acc = 16 * acc + c;
		return acc;
	}, 0);
}

function hexStringToBufferArray(zZyk1) {
	let bufferArray = new Uint8Array(zZyk1['\x6d\x61\x74\x63\x68'](/[\d\x61-f]{2}/gi)['\x6d\x61\x70'](function(h) {
		return parseInt(h, 16)
	}));
	return bufferArray['\x62\x75\x66\x66\x65\x72']
}

function bufferArrayToHexString(yfBG1) {
	let hex = Array.prototype.map.call(new Uint8Array(yfBG1), x => ('\x30\x30' + x['\x74\x6f\x53\x74\x72\x69\x6e\x67'](
		16))['\x73\x6c\x69\x63\x65'](-2))['\x6a\x6f\x69\x6e']('');
	return hex
}
/**
 * 转换车牌
 * @param {Object} carnocode
 */
function convertLisenceNo(carnocode) {
	carnocode = carnocode.toUpperCase()
	console.log('carnocode==>>', carnocode)
	var realCarno = ''

	for (var i = 0; i < carnocode.length;) {
		realCarno = realCarno + '%' + carnocode.substr(i, 2)
		i = i + 2
	}
	console.log('realCarno==>>', realCarno)
	var carno = gbk2.decodeFromGb2312(realCarno) + '';

	return carno.replace(/[^\u4e00-\u9fa5a-zA-Z\d]/g, '')
}

/**
 * 车牌颜色转换
 * @param {Object} code
 */
function code2Color(code) {
	switch (code) {
		case '00':
			return "0"
		case '01':
			return "1"
		case '02':
			return "2"
		case '03':
			return "3"
		case '04':
			return "4"
		case '05':
			return "5"
		case '06':
			return "6"
	}
}

/**
 * 写卡指令
 * @param cosArr 写卡指令，可以是
 * @param callback 回调
 * @param stype 00明文  01密文
 */
export function CpuCommand(arr, callback, stype = '00') {
	let cosArr = arr
	if (!Array.isArray(cosArr))
		cosArr = [arr]
	console.log('卡通道指令发送', cosArr)
	if (!isoDep) {
		callback && callback({
			code: 100,
			err_msg: "未连接"
		})
		return
	}
	sendDataToDevice(arr, result => {
		console.log('卡通道指令接收', result)
		if (result.code == 0) {
			let cosResult = result.data.substr(-4, 4)
			if (cosResult == '9000') {
				callback && callback({
					code: result.code,
					data: result.data,
					err_msg: result.err_msg
				})
			} else {
				callback && callback({
					code: cosResult,
					data: result.data,
					err_msg: result.err_msg
				})
			}
		} else if (result.code == '相关读写操作失败') {
			callback({
				code: result.code
			})
		} else {
			callback && callback({
				code: BleError.writeError,
				// data: data,
				err_msg: msg
			})
		}
	})
}

/**
 * 向设备发送数据
 * @param {String[]} cmds 
 * @param {function} callback 
 */
function sendDataToDevice(cmds, callback) {
	sendCallback = callback
	sendBufferArray = new Array()['concat'](cmds)
	sendIndex = 0;
	runningSendData()
}

/**
 * 递归发送数据至设备
 */
function runningSendData() {
	let value = sendBufferArray[sendIndex];
	isoDep.transceive({
		data: hexStringToBufferArray(value),
		success: function(res) {
			sendIndex++;
			resendCount = 3;
			if (sendIndex < sendBufferArray['length']) {
				runningSendData()
			} else {
				sendCallback && sendCallback({
					code: 0,
					data: bufferArrayToHexString(res.data),
					err_msg: "成功"
				})
			}
		},
		fail: function(err) {
			console.log(err)
			if (resendCount > 0) {
				resendCount--
				setTimeout(() => {
					console['log']("第" + (3 - resendCount) + "次重发");
					runningSendData()
				}, 200)
			} else {
				sendCallback && sendCallback({
					code: NFCError[err.errCode],
					data: "",
					err_msg: NFCError[err.errMsg]
				})
			}
		}
	})
}