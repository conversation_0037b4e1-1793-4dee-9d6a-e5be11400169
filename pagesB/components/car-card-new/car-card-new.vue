<template>
	<view class="index-car-card u-f-ajc u-f-dc">
		<view class="car-image">
			<view class="add-car"></view>
		</view>
		<view>添加更多车辆</view>
	</view>
</template>

<script>
</script>

<style lang="scss" scoped>
	.add-car{
		background-image:url('~@/static/toc/add-car.png');
		width:90rpx;
		height:90rpx;
		background-repeat:no-repeat;
		background-size: 90rpx;
	}
	.index-car-card{
		border:1px dashed #BBBBBB; 
		background: #FFFFFF;
		background-size: 100% ;
		height: 140upx;
		border-radius: 20upx;
		margin-top: 20upx;
		margin-left: 40upx;
		margin-right: 40upx;
	}
	.car-color{
		color: #2484E8;
		margin-top: 10upx;
	}
	.text-color{
		color: #FFFFFF;
	}
</style> 