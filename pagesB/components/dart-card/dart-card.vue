<template>
	<view class="dart-card">
		<view class="dart-card-head">
			<view class="dart-card-head-wrapper">
				<view class="dart-card-head-title">
					<slot name="title"></slot>
				</view>
				<view name='extra' class="dart-card-extra">
					<slot name="extra"></slot>
				</view>
			</view>
		</view>
		<view class="dart-card-body">
			<slot></slot>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {

			};
		},

		components: {},

		computed: {},

		methods: {}
	}
</script>
<style>
	.dart-card {
		box-sizing: border-box;
		margin: 0;
		padding: 0;
		color: #000000d9;
		font-size: 30rpx;
		height: 100%;
	}

	.dart-card-head {
		padding: 20rpx 30rpx;
		color: #000000d9;
		font-weight: 500;
		font-size: 30rpx;
	}

	.dart-card-head-wrapper {
		display: flex;
		align-items: center;
		-moz-box-pack: justify;
		-ms-box-pack: justify;
		-webkit-box-pack: justify;
		-webkit-justify-content: space-between;
		-moz-justify-content: space-between;
		justify-content: space-between;
	}

	.dart-card-head-title {

		display: flex;
		min-width: 200rpx;
		font-size: 30rpx;
		font-weight: 500;
		color: #333;
		overflow: hidden;
		padding-left: 20rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		position: relative;
	}

	.dart-card-head-title:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color: #0066E9;
	}



	.dart-card-extra {
		margin-left: auto;
		color: #000000d9;
		font-weight: 400;
		font-size: 28rpx;
		flex: 1;
	}

	.dart-card-body {
		padding: 0 30rpx;
		height: auto
	}
</style> 