<template>
	<view class="refund-steps bg-white">
		<view class="cu-form-group">
			<view class="c-title">申请进度流程</view>
		</view>
		<view class="step padding" v-if="stepListReverse.length > 0">
			<!-- 			<view class="spot-item">
				<view class="item" v-for="index of stepListReverse.length" :key="index">
					<view class="spot"></view>
					<view class="line" v-if="(index + 1) != stepListReverse.length"></view>
				</view>
			</view> -->
			<view class="content">
				<view class="list" v-for="(item,index) in stepListReverse" :key="index">
					<view class="type">{{getType(item.handleMode,handleModeList)}}</view>
					<view class="remark" v-if="item.handleMode !== 0 && item.handleMode !== 8 && item.remark">
						备注：{{item.remark}}
					</view>
					<view class=" time">{{item.operateTime}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			stepList: {
				type: Array,
				default () {
					return []
				}
			}
		},
		data() {
			return {
				stepListReverse: [],
				handleModeList: [{
						value: 0,
						label: '待申请'
					},
					{
						value: 1,
						label: '审核中'
					},
					{
						value: 2,
						label: '已通过'
					},
					{
						value: 3,
						label: '已驳回'
					},
					{
						value: 4,
						label: '退费(处理)中'
					},
					{
						value: 5,
						label: '退费(退款)中'
					},
					{
						value: 6,
						label: '已退费'
					},
					{
						value: 7,
						label: '退费失败'
					},
					{
						value: 8,
						label: '放弃申请'
					},
				]
			}
		},
		watch: {
			stepList(val) {
				console.log('valvalva', val)
				if (this.stepList.length > 0) {
					let stepList = JSON.parse(JSON.stringify(this.stepList))
					this.stepListReverse = stepList.reverse()
				}
			}
		},
		// computed: {
		// 	stepListReverse(val) {
		// 		val.reverse()
		// 	}
		// },
		methods: {
			getType(value, objList) {
				for (let i = 0; i < objList.length; i++) {
					if (objList[i].value == value) {
						return objList[i].label
					}
				}
				return ''
			},
		}

	}
</script>

<style lang="scss" scoped>
	.refund-steps {
		.cu-form-group {
			.c-title {
				height: 40rpx;
				padding-left: 14rpx;
				border-left: 8rpx solid #0066E9;
				color: rgba(51, 51, 51, 100);
				font-size: 30rpx;
				font-family: PingFangSC-bold;
			}
		}

		.step {

			// position: relative;
			display: flex;

			// flex-direction: column;
			.spot-item {
				display: flex;
				flex-direction: column;
			}

			.item {
				// margin-right: 20rpx;
				// position: absolute;

				.spot {
					width: 15rpx;
					height: 15rpx;
					border-radius: 50%;
					background-color: #0066E9;
					border: 1px solid #0066E9;
					display: inline-block;
					line-height: 15rpx;
				}

				.line {
					margin-left: 4rpx;
					background-color: #0066E9;
					width: 2rpx;
					height: 100rpx;
				}
			}

			.content {
				flex: 1;

				.list {
					flex: 1;
					display: flex;
					justify-content: center;
					flex-direction: column;
					margin-left: 30rpx;
					margin-top: 23rpx;
					border-bottom: 1rpx solid #e8e8e8;
					padding-bottom: 20rpx;

					&:first-child {
						margin-top: 0;
					}

					&:last-child {
						border: 0;
					}

					.time,
					.remark {
						margin-top: 20rpx;
					}

					.type {
						position: relative;

						&:before {
							content: ' ';
							position: absolute;
							left: -30rpx;
							top: 15rpx;
							width: 12rpx;
							height: 12rpx;
							background-color: #0066E9;
							border-radius: 6upx;
						}
					}
				}
			}
		}
	}
</style> 