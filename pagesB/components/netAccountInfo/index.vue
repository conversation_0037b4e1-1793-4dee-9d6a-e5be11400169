<!--
  * @desc:互联网账户信息组件
  * @author:z<PERSON><PERSON>
  * @date:2023-02-23 09:51:29
!-->
<template>
	<view>
		<view class="weui-form">
			<view class="weui-cells__title">
				互联网账户信息
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">账户名称</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{accountInfoData.userName}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">登录手机号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{accountInfoData.mobile}}
						</view>
					</view>

				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">账户类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{accountInfoData.accountType==='COMMON_USER'?'个人':'企业'}}
						</view>
					</view>

				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">可用余额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{moneyFilter(accountInfoData.availableAmount)}} 元
						</view>
					</view>

				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">账户状态</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{accountInfoData.status==='UNACTIVE'?'未激活':'已激活'}}
						</view>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		twoDecimal,
		moneyFilter
	} from '@/common/util.js'
	export default {
		name: '',
		props: {
			accountInfoData: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		components: {

		},
		data() {
			return {

			}
		},
		computed: {

		},
		watch: {

		},
		created() {

		},
		methods: {
			moneyFilter,
		},
	}
</script>

<style lang='scss' scoped>

</style>
