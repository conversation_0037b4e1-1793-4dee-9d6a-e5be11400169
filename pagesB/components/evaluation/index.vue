<template>
  <view class="evaluation-container">
    <!-- 共通标题部分 -->
    <view class="evaluation-box">
      <view class="evaluation-title">{{ getTitleText }}</view>
      <view class="evaluation-subtitle">此评价用于优化用户体验提升完善服务，感谢您的宝贵意见！</view>

      <!-- 星级评价部分 -->
      <t-star-rating :value="rateValue" :readonly="status === 'evaluated'" :size="56" :marginRight="34" @change="handleStarTap" />

      <view class="satisfaction-text" v-if="rateValue > 0">“{{ satisfactionText }}”</view>

      <!-- 未评价状态 -->
      <block v-if="status === 'unevaluated'">
        <!-- 未评价状态无其他展示内容 -->
      </block>

      <!-- 评价中状态 -->
      <block v-else-if="status === 'evaluating'">
        <view class="eva-box">
          <view class="input-box" v-if="showTextInput">
            <textarea v-model="evaluationText" :placeholder="getPlaceholderByRate" placeholder-class="placeholder-style"
              maxlength="200" auto-height></textarea>
          </view>
          <view class="upload-container">
            <!-- <view class="upload-title">
            <text>上传图片</text>
            <text class="upload-rules">最多3张，单张≤5MB (JPG/PNG)</text>
          </view> -->
            <view class="upload-from">
              <view class="upload" v-for="(item, index) in imgList" @tap="viewImage(item)" :key="index">
                <view class="upload-wrap">
                  <view class="upload-wrap-bd">
                    <image :src="item.url" class="upload-wrap__img" mode="aspectFill"></image>
                    <view class="upload-progress" v-if="item.status === 'uploading'">
                      <text class="upload-progress-text">{{ item.progress }}%</text>
                    </view>
                    <view class="upload-wrap__close" @tap.stop="delImgHandle(item, index)">
                      <image src="@/pagesD/static/close.png" mode="" class="close">
                      </image>
                    </view>
                  </view>
                </view>
              </view>
              <view class="upload upload-add" v-if="imgList.length < maxImgCount" @tap="chooseImage">
                <view class="upload-wrap">
                  <view class="upload-wrap-bd upload-placeholder">
                    <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/camera.png" mode="aspectFit"></image>
                  </view>
                </view>
              </view>
            </view>
            <view class="word-count" :class="{ 'word-count-warning': evaluationText.length > 180 }">
              {{ evaluationText.length }}/200</view>
            <!-- <view class="upload-count">{{imgList.length}}/{{maxImageCount}}</view> -->
          </view>
        </view>

        <view 
          class="finish-btn" 
          :class="{ 'submitting': submitting }"
          @tap="submitEvaluation"
          :style="{ pointerEvents: submitting ? 'none' : 'auto' }"
        >
          {{ submitting ? '提交中...' : submitButtonText }}
        </view>
      </block>

      <!-- 完成评价状态 -->
      <block v-else-if="status === 'evaluated'">
        <view class="eva-box">
          <view class="evaluation-content" v-if="evaluationText">
            {{ evaluationText }}
          </view>
          <view class="uploaded-images" v-if="imgList.length > 0">
            <view class="uploaded-image" v-for="(item, index) in imgList" :key="index" @tap="viewImage(item)">
              <image :src="item.url" class="image-preview" mode="aspectFill"></image>
            </view>
          </view>
          <view class="evaluation-time" v-if="evaluationTime">评价于: {{ evaluationTime }}</view>
        </view>
      </block>
    </view>

    <!-- 弹出提示 -->
    <view class="modal" v-if="showTipModal">
      <view class="modal-content">
        <view class="modal-title">温馨提示</view>
        <view class="modal-message">确定提交该评价?</view>
        <view class="modal-buttons">
          <view class="modal-button cancel" @tap="cancelSubmit">取消</view>
          <view class="modal-button confirm" @tap="confirmSubmit">确定</view>
        </view>
      </view>
    </view>


  </view>
</template>

<script>
import uniIcons from "@/components/uni-icons/uni-icons.vue";
import TStarRating from '@/components/common/t-star-rating.vue';
import evaluationUtils from '@/pagesD/components/evaluation/evaluationUtils.js';
import VODUpload from "@/pagesA/ali-video/aliSdk/aliyun-upload-sdk-1.0.1.min.js";
import dayjs from "@/js_sdk/dayjs/dayjs.min.js";
import { getAccountId } from "@/common/storageUtil.js";

export default {
  name: 'Evaluation',
  components: {
    uniIcons,
    TStarRating
  },
  props: {
    // 业务类型：serviceArea(服务区), tollStation(收费站), chargingService(充电服务), etcConsumption(ETC消费), rescue(一键救援)
    businessType: {
      type: String,
      default: 'serviceArea',
      validator(value) {
        return ['serviceArea', 'tollStation', 'chargingService', 'etcConsumption', 'rescue'].includes(value);
      }
    },
    // 初始状态：unevaluated(未评价), evaluating(评价中), evaluated(已评价)
    initialStatus: {
      type: String,
      default: 'unevaluated'
    },
    // 初始评分
    initialRate: {
      type: Number,
      default: 0
    },
    // 初始评价内容
    initialText: {
      type: String,
      default: ''
    },
    // 初始图片列表
    initialImgList: {
      type: Array,
      default: () => []
    },
    // 评价时间
    initialTime: {
      type: String,
      default: ''
    },
    // 最大图片数量
    maxImageCount: {
      type: Number,
      default: 3
    },
    // 图片上传限制提示文字
    maxImageUploadSize: {
      type: Number,
      default: 5
    },
    // 提交按钮文案
    submitButtonText: {
      type: String,
      default: '完成评价'
    }
  },
  data() {
    return {
      status: this.initialStatus,
      rateValue: this.initialRate,
      evaluationText: this.initialText,
      imgList: [],
      urlList: [...this.initialImgList], // 存储上传后的图片URL
      evaluationTime: this.initialTime,
      showTextInput: false,
      sourceType: ['camera', 'album'],
      showTipModal: false,
      maxImgCount: this.maxImageCount,
      submitting: false, // 提交中状态
      uploader: null,
    }
  },
  computed: {
    getTitleText() {
      // 基于业务类型和评价状态生成标题
      const titleMap = {
        serviceArea: {
          unevaluated: '请您对本次服务区体验评价',
          evaluating: '请您对本次服务区体验评价',
          evaluated: '本次服务区体验的评价'
        },
        tollStation: {
          unevaluated: '请您对本次收费站服务评价',
          evaluating: '请您对本次收费站服务评价',
          evaluated: '本次收费站服务的评价'
        },
        chargingService: {
          unevaluated: '请您对本次充电服务评价',
          evaluating: '请您对本次充电服务评价',
          evaluated: '本次充电服务的评价'
        },
        etcConsumption: {
          unevaluated: '请您对本次行程消费评价',
          evaluating: '请您对本次行程消费评价',
          evaluated: '本次行程消费的评价'
        },
        rescue: {
          unevaluated: '请您对本次救援服务评价',
          evaluating: '请您对本次救援服务评价',
          evaluated: '本次救援服务的评价'
        }
      };

      const businessTitles = titleMap[this.businessType];
      if (businessTitles) {
        return businessTitles[this.status] || businessTitles.unevaluated;
      }

      // 默认回退到高速行程
      return this.status === 'evaluated' ? '本次高速行程的评价' : '请您对本次高速行程评价';
    },
    satisfactionText() {
      return evaluationUtils.getSatisfactionText(this.rateValue);
    },
    getPlaceholderByRate() {
      // 根据星级返回不同的提示语
      if (this.rateValue <= 3) {
        return "哪些方面待提升？我们非常需要您的意见！";
      } else {
        return "夸一下吧～";
      }
    }
  },
  mounted() {
    this.aliImageSdkInit();
    if (this.initialImgList.length > 0) {
      this.imgList = this.initialImgList.map(url => ({
        url: url,
        status: 'success',
        progress: 100
      }));
    }
  },
  watch: {
    rateValue(newVal) {
      if (newVal > 0) {
        this.showTextInput = true;
      }
    },
    // 监听父组件传入的初始状态变化
    initialStatus(newVal) {
      this.status = newVal;
    },
    // 监听初始评分变化
    initialRate(newVal) {
      this.rateValue = newVal;
      if (newVal > 0) {
        this.showTextInput = true;
      }
    },
    // 监听初始评价内容变化
    initialText(newVal) {
      this.evaluationText = newVal;
    },
    // 监听初始图片列表变化
    initialImgList: {
      handler(newVal) {
        this.urlList = [...newVal];
        if (newVal && newVal.length > 0) {
          this.imgList = newVal.map(url => ({
            url: url,
            status: 'success',
            progress: 100
          }));
        } else {
          this.imgList = [];
        }
      },
      immediate: false
    },
    // 监听初始时间变化
    initialTime(newVal) {
      this.evaluationTime = newVal;
    }
  },
  methods: {
    // 处理星星点击
    handleStarTap(value) {
      if (this.status === 'unevaluated') {
        this.startEvaluation();
      }
      this.setRate(value);
    },

    // 设置评分
    setRate(value) {
      this.rateValue = value;
      // 通知父组件评分变化
      this.$emit('rate-change', value);
    },

    // 开始评价
    startEvaluation() {
      this.status = 'evaluating';
      this.$emit('status-change', 'evaluating');
    },

    // 完成评价（供外部调用）
    completeEvaluation() {
      this.status = 'evaluated';
      this.submitting = false; // 重置提交状态

      // 如果没有评价时间，设置为当前时间
      if (!this.evaluationTime) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        this.evaluationTime = `${year}/${month}/${day} ${hours}:${minutes}`;
      }

      this.$emit('status-change', 'evaluated');
    },

    // 重置提交状态（供外部调用，用于API失败时）
    resetSubmitStatus() {
      this.submitting = false;
    },

    // 阿里视频上传sdk初始化
    aliImageSdkInit() {
      let that = this;
      this.uploader = new VODUpload({
        timeout: 60000,
        region: "cn-shenzhen",
        addFileSuccess(uploadInfo) {
          that.imgList.push({
            url: uploadInfo.url,
            status: 'waiting',
            progress: 0,
            file: uploadInfo.file
          });
        },
        async onUploadstarted(uploadInfo) {
          const img = that.imgList.find(item => item.url === uploadInfo.url);
          if (img) {
            img.status = 'uploading';
          }

          
          let data = {
            type:'comment',
            title: `评价图片-小程序上传-${getAccountId()}-${dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")}`,
            fileName:`评价图片-小程序上传-${getAccountId()}-${dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")}`
          };
          try {
            let res = await that.$request.post(that.$interfaces.getImageUploadAuth, {
              data
            });
            let akInfo = res.data;

            that.uploader.setUploadAuthAndAddress(
              uploadInfo,
              akInfo.uploadAuth,
              akInfo.uploadAddress,
              akInfo.imageId
            );
          } catch (err) {
            that.uploader.stopUpload();
            const img = that.imgList.find(item => item.url === uploadInfo.url);
            if (img) {
              img.status = 'failed';
            }
            uni.showToast({
              title: '获取上传凭证失败',
              icon: 'none'
            });

          }
        },
        onUploadSucceed: function(uploadInfo) {

          const img = that.imgList.find(item => item.url === uploadInfo.url);
          if (img) {
            img.status = 'success';
            img.progress = 100;
            img.videoId = uploadInfo.videoId;
          }
          that.urlList.push(uploadInfo.videoId);
        },
        onUploadFailed: function(uploadInfo, code, message) {
          uni.hideLoading();
          const index = that.imgList.findIndex(item => item.url === uploadInfo.url);
          if (index > -1) {
            that.imgList.splice(index, 1);
          }
          uni.showToast({
            title: '图片上传失败',
            icon: 'none'
          });
          
        },
        onUploadProgress(uploadInfo, totalSize, progress) {
          let progressPercent = Math.ceil(progress);
          const img = that.imgList.find(item => item.url === uploadInfo.url);
          if (img) {
            img.progress = progressPercent;
          }
        },
        onUploadEnd(uploadInfo) {
          uni.hideLoading();
        }
      });
    },

    // 选择图片
    chooseImage() {
      if (this.imgList.some(item => item.status === 'uploading')) {
        uni.showToast({
          title: '请等待当前图片上传完成',
          icon: 'none'
        });
        return;
      }
      uni.chooseImage({
        count: this.maxImgCount - this.imgList.length,
        sizeType: ['compressed'],
        sourceType: this.sourceType,
        success: (res) => {
          // 检查文件大小
          const fileSizeValid = res.tempFiles.every(file => {
            // 将文件大小从字节转换为MB，并检查是否小于限制（5MB）
            const fileSizeMB = file.size / (1024 * 1024);
            return fileSizeMB <= this.maxImageUploadSize;
          });

          if (!fileSizeValid) {
            uni.showToast({
              title: `图片大小不能超过${this.maxImageUploadSize}MB`,
              icon: 'none'
            });
            return;
          }

          // 检查文件格式
          const formatValid = res.tempFiles.every(file => {
            const ext = file.path.split('.').pop().toLowerCase();
            return ['jpg', 'jpeg', 'png'].includes(ext);
          });

          if (!formatValid) {
            uni.showToast({
              title: '仅支持JPG/PNG格式图片',
              icon: 'none'
            });
            return;
          }
          res.tempFiles.forEach(tempFile => {
            const fileForUploader = {
              url: tempFile.path,
              file: tempFile
            };
            this.uploader.addFile(fileForUploader, null, null, null, '{"Vod":{}}');
          });
          this.uploader.startUpload();
        },
        fail: (err) => {
          if (err.errMsg !== "chooseImage:fail cancel") {
            uni.showToast({
              title: '图片选择失败',
              icon: 'none'
            });
          }
        }
      })
    },

    // 查看图片
    viewImage(item) {
      uni.previewImage({
        urls: this.imgList.map(i => i.url),
        current: item.url
      });
    },

    // 删除图片
    delImgHandle(item, index) {
      this.imgList.splice(index, 1);
      this.uploader.deleteFile(index);
      if (item.videoId) {
        const videoIdIndex = this.urlList.indexOf(item.videoId);
        if (videoIdIndex > -1) {
          this.urlList.splice(videoIdIndex, 1);
        }
      }
    },

    // 提交评价
    submitEvaluation() {
      try {
        if (this.rateValue <= 0) {
          uni.showToast({
            title: '请先选择评分',
            icon: 'none'
          });
          return;
        }

        if (this.imgList.some(item => item.status === 'uploading')) {
          uni.showToast({
            title: '请等待图片上传完成',
            icon: 'none'
          });
          return;
        }

        // 处理可能的自动填充问题
        this.handleAutofillIssues();

        this.showTipModal = true;
      } catch (error) {
        // 即使出错也继续执行提交流程
        this.showTipModal = true;
      }
    },

    // 处理自动填充相关问题
    handleAutofillIssues() {
      try {
        // 处理可能存在的自动填充下拉框问题
        if (typeof document !== 'undefined' && document.activeElement && document.activeElement.blur) {
          document.activeElement.blur();
        }
        
        // 查找页面中的textarea元素并主动失去焦点
        const textareas = uni.createSelectorQuery().in(this).selectAll('textarea');
        if (textareas) {
          // 在小程序环境中，通过设置focus为false来处理
          this.$nextTick(() => {
            // 这里可以添加额外的处理逻辑
          });
        }
      } catch (e) {
        // 忽略处理过程中的任何错误
      }
    },

    // 取消提交
    cancelSubmit() {
      this.showTipModal = false;
    },

    // 确认提交
    confirmSubmit() {
      try {
        this.showTipModal = false;

        // 在提交前处理可能的自动填充问题
        this.handleAutofillIssues();

        // 延迟执行，避免与自动填充机制冲突
        setTimeout(() => {
          this.executeSubmit();
        }, 100);
      } catch (error) {
        // 降级处理，直接执行提交
        this.executeSubmit();
      }
    },

    // 执行实际的提交逻辑
    executeSubmit() {
      try {
        // 设置提交中状态
        this.submitting = true;
        
        // 构建评价数据
        const evaluationData = {
          rate: this.rateValue,
          text: this.evaluationText,
          images: this.urlList,
          urlList: this.urlList,
          time: this.evaluationTime
        };

        // 触发提交事件，将评价数据传递给父组件
        this.$emit('submit', evaluationData);

        // 注意：不在这里调用completeEvaluation()
        // 改为由父组件根据API结果决定是否完成评价
      } catch (error) {
        this.submitting = false;
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.evaluation-container {
  width: 100%;
}

.evaluation-box {
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  padding-top: 10rpx;
}

.evaluation-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.evaluation-subtitle {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.satisfaction-text {
  margin-top: 30rpx;
  font-weight: 500;
  font-size: 36rpx;
  color: #333333;
  text-align: center;
}

.eva-box {
  background: #F6F6F6;
  padding: 20rpx 40rpx;
  margin: 30rpx 0;
  position: relative;
}

.input-box textarea {
  width: 100%;
  min-height: 116rpx;
  font-size: 28rpx;
  color: #999999;
  box-sizing: border-box;
  border: none;
  outline: none;
}

.placeholder-style {
  color: #BBBBBB;
  font-size: 28rpx;
}

.word-count {
  position: absolute;
  bottom: 20rpx;
  right: 40rpx;
  font-size: 24rpx;
  color: #BBBBBB;
}

.word-count-warning {
  color: #FF6A6A;
}

.upload-container {
  margin: 30rpx 0;
}

.upload-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #333333;
}

.upload-rules {
  font-size: 24rpx;
  color: #999999;
}

.upload-from {
  display: flex;
  flex-wrap: wrap;
  gap: 37rpx;
  padding-bottom: 16rpx;
}

.upload {   
  width: 168rpx;
  height: 168rpx;
}

.upload-wrap {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.upload-wrap-bd {
  width: 100%;
  height: 100%;
  background-color: #F8F8F8;
}

.upload-wrap__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
  border-radius: 12rpx;
}

.upload-progress-text {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
}

.upload-wrap__close {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  .close{
    width: 30rpx;
    height: 30rpx;
    background-size: 100%;
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  background: #E4E4E4;
  image{
    width: 49rpx;
    height: 44rpx;
  }
}

.add-icon {
  font-size: 60rpx;
  color: #CCCCCC;
  font-weight: 300;
  margin-bottom: 12rpx;
  line-height: 1;
}

.add-text {
  font-size: 26rpx;
  color: #999999;
}

.upload-count {
  font-size: 24rpx;
  color: #BBBBBB;
  text-align: right;
  margin-top: 10rpx;
}

.evaluation-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  word-wrap: break-word;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  gap: 37rpx;
}

.uploaded-image {
  width: 168rpx;
  height: 168rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.evaluation-time {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #999999;
  text-align: right;
}

.finish-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #0066E9;
  color: #FFFFFF;
  font-size: 30rpx;
  text-align: center;
  border-radius: 10rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 233, 0.2);
  transition: all 0.3s;
}

.finish-btn:active {
  transform: scale(0.98);
  background-color: #0055cc;
}

.finish-btn.submitting {
  background-color: #CCCCCC;
  color: #999999;
  box-shadow: none;
  cursor: not-allowed;
}

.finish-btn.submitting:active {
  transform: none;
  background-color: #CCCCCC;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.modal-content {
  width: 560rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  padding: 40rpx 30rpx 20rpx;
}

.modal-message {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  padding: 0 30rpx 40rpx;
}

.modal-buttons {
  display: flex;
  border-top: 1rpx solid #EEEEEE;
}

.modal-button {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 30rpx;
}

.cancel {
  color: #666666;
  border-right: 1rpx solid #EEEEEE;
}

.confirm {
  color: #0066E9;
  font-weight: 500;
}
</style>
