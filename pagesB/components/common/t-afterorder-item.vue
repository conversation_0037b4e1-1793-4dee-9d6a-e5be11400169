<template>
	<view>
		
		<view class="t-order-account">
			<view class="t-p-icon">
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/order-icon.png" class="png" mode="aspectFit"></image>
			</view>
			<view class="t-p-content">
				<view class='t-p-c-bank'>
					<view class='t-p-c-b-name'>订单号:{{bankInfo.orderId.length>16?bankInfo.orderId.substring(0,16):bankInfo.orderId}}
						<!-- <text style="color:#2484E8">[{{plateColorToColorMap.get(bankInfo.plateColor+'')}}]</text>
					    -->
					</view>
				</view>
				<view class="t-p-c-type" v-if="bankInfo.status==1">状态：申请中</view>
				<view class="t-p-c-type" v-if="bankInfo.status==2">状态：接收申请</view>
				<view class="t-p-c-type" v-if="bankInfo.status==3">状态：拒绝申请</view>
				<view class="t-p-c-id">申请时间:{{bankInfo.applyTime.replace("T", " ")}}</view>
			</view>
			<view class="arrow" v-show="showArrow">
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/right-arr.png" class="right-arrow"></image>
			</view>
			<view class="line" v-if="!hideLine"></view>
		</view>
	</view>
</template>

<script>
	import { plateColorToColorMap} from '@/common/systemConstant.js';
	export default {
		props: {
			bankInfo: {
				type: Object,
				required: true,
			},
			showArrow: {
				type: Boolean,
				default: true
			},
			showSign:{
				type: Boolean,
				default: false,
			},
			hideLine:{
				type:Boolean,
				default:false
			}
		},
		data() {
			return {
			    plateColorToColorMap,
			};
		},
		computed:{
			
		},
		onShow(){
		},
		methods:{
			
		}
	}
</script>

<style lang="scss">
	.png{
		width:1.6rem;
		height:1.6rem;
	}
	.arrow{
		.right-arrow{
			width: 50rpx;
			height: 50rpx;
		}
	}
	.line{
		border: 1px solid #E5E5E5;
		position: absolute;
		bottom: 0;
		width: 630rpx;
		right:40rpx;
	}
	.t-order-account{
		border-bottom: 1upx solid #EDEDED;
		position: relative;
		height: 180rpx;
		width: 698rpx;
		padding: 20rpx;
		/* background: rbga(256,256,256,0.2); */
		
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 20upx 0;
		text-align: center;
		/* justify-content: space-around	; */
	}
	.t-p-img{
		flex: 2;
		font-size: 100upx;
		/* color: green; */
	}
	.t-p-content{
		flex: 5;
		display: flex;
		flex-direction: column;
		text-align: left;
	}
	.t-p-c-bank{
		display: flex;
		flex-direction: row;
		margin-left: 5upx;
		/* justify-content: space-between; */
		align-items: center;
	}
	.t-p-c-b-name{
		font-weight: bold;
		font-size: 32upx;
		margin-left: 10upx;
		color: #333333;
	}
	.t-p-c-b-msg{
		padding-left: 26upx;
	}
	.t-p-c-type{
	    margin-left: 10upx;
	    font-size: 28rpx;
	    color:#666;
	    margin: 10rpx 0 4rpx 10rpx;
	}
	.t-p-c-id{
		margin-left: 10upx;
	}
	.t-p-status{
		flex: 2;
		background: #F18934;
		color:#FFFFFF;
		border-radius: 30upx;
	}
	.t-p-icon{
		flex: 1;
	}
	.qianyue{
		color:var(--blue)
	}
	
</style> 