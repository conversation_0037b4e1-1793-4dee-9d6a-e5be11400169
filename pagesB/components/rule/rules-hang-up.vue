<template>
    <view class="neil-modal" @touchmove.stop.prevent="bindTouchmove" :class="{'neil-modal--show':isOpen}">
        <view class="neil-modal__mask" @click="clickMask"></view>
        <view class="neil-modal__container">
            <view class="neil-modal__header">
				<text style='background-color: #fff;color:#222;padding:20upx;border-radius:10upx;'>ETC客户解除车牌占用的承诺书</text>
			</view>
            <view class="neil-modal__content" :class="content ? 'neil-modal--padding' : ''" :style="{textAlign:align}">
                <template>
                    <view class="uni-app-content">
						<scroll-view class="rules-scroll" scroll-y="true">
							<view class="rules-view-new">ETC 推广发行是深化收费公路支付改革取消高速公路省界收费站的重要内容，是实现高速公路不停车快捷收费、减少拥堵、便利群众的重要基础，对促进物流业提质增效、降低物流成本、减少污染排放具有重要意义。经客户申明，在其不知情的情况下，名下所登记车辆办理ETC发行手续,导致车牌被占用，待查验属实后，应立即予以解除。为给您提供更为便捷的办理渠道，请您仔细阅读以下信息：</view>
							<view class="rules-view-new">1、本人确认未自行申请或授权他人代为申请办理过本人名下车辆（车牌号码：{{plateNum}}）的ETC发行手续，故现申请立即解除该车的ETC卡绑定关系，取消车牌占用锁定，重新办理发行业务。</view>
							<view class="rules-view-new">2、本人保证本次申请解除车牌占用所提交证件的真实性、合法性，并承诺未隐瞒任何影响该车辆办理ETC手续的真实情况，若提交虚假证件、隐瞒车辆真实情况而侵害本人或他人的合法权益，所造成的一切不利后果均有本人承担。</view>
							</scroll-view>	
						 <view class="rule-cb">
							<checkbox-group @change="handleOnChange">
								<label><checkbox class="rule-checkbox"/>我已阅读并同意
								<text style="color:#007AFF">《ETC客户解除车牌占用的承诺书》</text></label>
							</checkbox-group>
						</view>
						<view style="color:red;padding:0 30upx;" v-if="isShowText">您还没有同意ETC客户解除车牌占用的承诺书</view>
					</view>
					<!-- <view v-html="content" class="modal-content"></view> -->
                </template>
            </view>
            <view class="neil-modal__footer">
                <view v-if="showCancel" class="neil-modal__footer-left" @click="clickLeft" :style="{color:cancelColor}"
                    hover-class="neil-modal__footer-hover" :hover-start-time="20" :hover-stay-time="70">
                    {{cancelText}}
                </view>
                <view class="neil-modal__footer-right" @click="clickRight" :style="{color:confirmColor}" hover-class="neil-modal__footer-hover"
                    :hover-start-time="20" :hover-stay-time="70">
                    {{confirmText}}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: 'neil-modal',
        props: {
            align: { //content 的对齐方式left/center/right
                type: String,
                default: 'left'
            },
            cancelText: { //取消按钮的文字，默认为"取消"
                type: String,
                default: '不同意'
            },
            cancelColor: { //取消按钮颜色
                type: String,
                default: '#fff'
            },
            confirmText: { //确定按钮的文字，默认为"确定"
                type: String,
                default: '同意'
            },
            confirmColor: { //确认按钮颜色
                type: String,
                default: '#fff'
            },
            showCancel: { //是否显示取消按钮，默认为 true
                type: [Boolean, String],
                default: true
            },
            show: { //是否显示模态框
                type: [Boolean, String],
                default: false
            },
            autoClose: { //点击遮罩是否自动关闭弹窗
                type: [Boolean, String],
                default: false
            },
            plateNum: { //车牌
                type: String,
                default: ''
            },
        },
		onShow()
		{
			
		},
        data() {
            return {
				content:'',
                isOpen: false,
				cb:null,
				isShowText:false
            }
        },
        watch: {
            show(val) {
				this.isOpen = val
            }
        },
        created() {
        	
        },
        methods: {
			handleOnChange(e){
				console.log(111)
				this.cb = e.detail.value;
				if(this.cb) {
					this.isShowText=false;
				}
			},
            bindTouchmove() {},
            clickLeft() {
                setTimeout(() => {
                	this.$emit('confirm',"0")
                }, 200)
            },
            clickRight() {
                setTimeout(() => {
					if(!this.cb || this.cb.length == 0) {
						// this.show = true;
						this.isShowText=true;
						return false;
					}else{
						// this.show = false;
						this.isShowText=false;
						this.$emit('confirm',"1")
					}
                }, 200)
                
            },
			clickMask(){
				if(this.autoClose){
					this.closeModal()
				}
			},
            closeModal() {
                this.showAnimation = false
				this.isOpen = false
				this.$emit('confirm',"0")
				this.closeModal()
            }
        }
    }
</script>

<style lang="scss">
    $bg-color-mask:rgba(0, 0, 0, 0.5); //遮罩颜色
    $bg-color-hover:#f1f1f1; //点击状态颜色
	
	.rules-title{
		font-size:32upx;
		text-align: center;
		margin-bottom: 35upx;
		font-weight: bold;
	}
	.rules-scroll{
		
		box-sizing: border-box;
		height: 768upx;
		border: 1upx solid #E3E3E3;
		line-height: 50upx;
		padding:20upx 30upx;
	}
	.rules-view{
		margin-bottom: 20upx;
		font-size: 26upx;
		color: #666666;
	}
	.rules-view-new{
		font-size: 26upx;
		color: #666666;
	}
	.rule-cb{
		font-size: 26upx;
		padding:20upx 30upx;
		color: #999999;
	}
	.rule-checkbox{
		zoom:70%
		// width: 32upx;
		// height: 32upx
	}
	.uni-checkbox-input-checked {
		border: 1px solid #0081ff;
		color: #fff !important;
		background-color: #0081ff !important;
	}
	.uni-btn-v{
		height: 72upx;
		line-height: 72upx;
		font-size: 36upx;
		margin-top:16upx;
	}
    .neil-modal {
        position: fixed;
		visibility: hidden;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 1000;
		transition:visibility 200ms ease-in;
		
		&.neil-modal--show{
			visibility: visible;
		}

        &__header {
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 18upx 24upx;
            line-height: 1.5;
            font-size: 32upx;
            text-align: center;
            &::after {
                content: " ";
                position: absolute;
                left: 0;
                bottom: 0;
                right: 0;
                height: 1px;
                transform-origin: 0 0;
                transform: scaleY(.5);
            }
        }

        &__container {
            position: absolute;
			z-index: 999;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) ;
            transition: transform 0.3s;
            width: 640upx;
            border-radius: 20upx;
            background-color: #fff;
            overflow: hidden;
            opacity: 0;
            transition: opacity 200ms ease-in;
        }

        &__content {
            position: relative;
            color: #333;
            font-size: 28upx;
            box-sizing: border-box;
            line-height: 1.5;

            &::after {
                content: " ";
                position: absolute;
                left: 0;
                bottom: -1px;
                right: 0;
                height: 1px;
                transform-origin: 0 0;
                transform: scaleY(.5);
            }
        }

        &__footer {
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #333;
            font-size: 32upx;
            display: flex;
            flex-direction: row;

            &-left,
            &-right {
                position: relative;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                height: 88upx;
                font-size: 28upx;
                line-height: 88upx;
                text-align: center;
                color: #fff;
				margin-top:20upx;
            }
			&-left{
				color: #fff;
				background-color: #beddf2;
			}
            &-right {
                color: #fff;
				background-color: var(--blue);
            }

            &-left::after {
                content: " ";
                position: absolute;
                right: -1px;
                top: 0;
                width: 1px;
                bottom: 0;
                transform-origin: 0 0;
                transform: scaleX(.5);
            }

            &-hover {
                background-color: $bg-color-hover;
            }
        }

        &__mask {
            display: block;
            position: absolute;
			z-index: 998;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: $bg-color-mask;
            opacity: 0;
            transition: opacity 200ms ease-in;
			&.neil-modal--show{
				opacity: 1;
			}
        }

        &--padding {
            padding: 32upx 24upx;
            min-height: 90upx;
        }
		&--show {
		    .neil-modal__container,.neil-modal__mask{
				opacity: 1;
			}
		}
    }
</style>
