<!--
  * @desc:模态框侧边栏组件
  * @author:zhang<PERSON>
  * @date:2023-02-09 15:36:43
!-->
<template>
	<view class="cu-modal drawer-modal justify-end" :class="showModal?'show':''" @tap="cancelModal">
		<view class="cu-dialog basis-lg" style="min-width: 75% !important;background-color: #fff;" @tap.stop=""
			:style="[{top:CustomBar+'px',height:'calc(100vh - ' + CustomBar + 'px)'}]">
			<view class="cu-list menu text-left">
				<view>
					<slot name="content"></slot>
				</view>
			</view>
			<view class="weui-bottom-fixed" >
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn " @tap="resetModal">
							重置
						</button>
					</view>
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @tap="okModal">
							搜索
						</button>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: '',
		props: {
			showModal: {
				type: Boolean,
				default: false,
			},
			footerStyle: {
				type: String,
				default: 'background: #fff'
			},
			showCancelFlag: {
				type: Boolean,
				default: true,
			},
			showOkFlag: {
				type: Boolean,
				default: true,
			},

		},
		components: {

		},
		data() {
			return {
				CustomBar: this.CustomBar,
			}
		},
		computed: {

		},
		watch: {

		},
		created() {

		},
		methods: {
			cancelModal() {
				this.$emit('cancelModal')
			},
			hideModal(e) {
				this.$emit("hideModal");
			},
			resetModal(e) {
				this.$emit("resetModal");
			},
			okModal(e) {
				this.$emit("okModal");
			}
		},
	}
</script>

<style lang='scss' scoped>
	.footer {
		position: fixed;
		bottom: 20upx;
		width: 100%;

		.footer-btn {
			width: 170rpx;
			height: 80rpx;
			align-items: center;
			line-height: 80rpx;
			margin: 0 30rpx;
			border: 1px solid #f3f3f3;
			font-size: 30rpx;
			border-radius: 40rpx;
		}
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}
</style> 