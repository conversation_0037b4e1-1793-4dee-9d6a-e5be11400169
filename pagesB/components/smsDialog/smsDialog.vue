<template>
	<view class="status-dialog" v-if="show">
		<view class="status-dialog__mask" @click="close"></view>
		<view class="status-dialog2__content">
			<view class="tips">身份验证</view>
			<view class="content">
				<view class="content-title">
					为了验证身份，我们需要向您的ETC预留手机号{{mobileStr}} 发送短信验证。
				</view>
				<view class="input-wrapper" style="margin-bottom: 20rpx;">
					<input placeholder="请输入图形验证码" name="captCode" v-model='captCode'></input>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">
				</view>
				<view class="input-wrapper">
					<input type="number" v-model="mobileCode" placeholder="请输入短信验证码">
					<view class="sms-btn" :class="{gray:time}" @click="sendSMS">
						{{smsName}}
					</view>
				</view>
			</view>
			<view class="interaction-two">
				<view class="interaction-one-comfirm" :style="{color:confirmColor}" @click="clickComfirm">
					提交
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import util from "@/common/util.js"
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getTollTicket
	} from '@/common/storageUtil.js';
	export default {
		name: 'status-dialog',
		components: {
			tLoading
		},
		props: {
			content: { // 提示的文案内容
				type: String,
				default: ''
			},
			show: { // 弹窗的显示
				type: [Boolean, String],
				default: false
			},
			contentStyle: {
				type: Object,
				default () {
					return {};
				}
			},
			mobile: {
				type: [Number, String]
			},
			cardId: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				isLoading: false,
				time: null,
				smsName: '发送验证码',
				mobileCode: '', //短信验证码
				captCode: '', // 图形验证码
				codeUrl: '', // 图形验证码连接
				uuid: '',
			}
		},
		computed: {
			mobileStr() {
				let str = this.mobile
				let mobileStr = str.replace(/(\w{3})\w{4}(\w{4})/, '$1****$2')
				return mobileStr
			}
		},
		methods: {
			sendHandle() {

			},
			getCaptcha() {
				this.isLoading = true
				let param = {
					ticketId: getTollTicket()
				}
				this.$request.post(this.$interfaces.tollGetCaptcha, {
					data: param
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						this.codeUrl = res.data.image
						this.uuid = res.data.uuid
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {
					this.isLoading = false
				})
			},
			checkCaptcha(callback) {
				this.isLoading = true
				let param = {
					ticketId: getTollTicket(),
					code: this.captCode,
					uuid: this.uuid
				}
				this.$request.post(this.$interfaces.tollCaptchaCheck, {
					data: param
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						callback && callback()
					} else {
						this.captCode = ''
						this.getCaptcha()
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {
					this.isLoading = false
				})
			},
			//发送短信
			sendSMS() {
				if (!this.captCode) {
					uni.showToast({
						title: "请输入图形验证码",
						icon: "none"
					})
					return;
				}
				this.checkCaptcha(() => {
					if (!this.time) {
						this.isLoading = true
						let countdown = 60
						let params = {
							mobile: this.mobile,
							ticketId: getTollTicket(),
							cardId: this.cardId,
							// cardId: '20190424171504237005'
						};
						this.$request.post(this.$interfaces.tollCardBindSendCode, {
							data: params
						}).then(res => {
							console.log(res);
							this.isLoading = false
							if (res.code == '200') {
								this.time = setInterval(() => {
									countdown = countdown - 1
									this.smsName = countdown + "秒后重新发送"
									if (countdown === 0) {
										clearInterval(this.time)
										this.time = null
										this.smsName = "重发验证码"
									}
								}, 1000)
							} else {
								uni.showModal({
									title: "提示",
									content: res.msg,
									showCancel: false
								});
							}
						}).catch(error => {
							this.isLoading = false
							uni.showModal({
								title: "提示",
								content: error.msg,
								showCancel: false
							});
						})
					}
				})
			},
			clickComfirm() {
				if (!this.captCode) {
					uni.showToast({
						title: "请输入图形验证码",
						icon: "none"
					})
					return
				}
				if (!this.mobileCode) {
					uni.showToast({
						title: "请输入短信验证码",
						icon: "none"
					})
					return
				}
				this.$emit('confirm', this.mobileCode)
			},
			close() {
				this.$emit('update:show', false)
			}
		}
	}
</script>

<style lang="scss">
	$bg-color-mask: rgba(0, 0, 0, 0.5); //遮罩颜色
	$bg-color-hover: #f1f1f1; //点击状态颜色

	.margin-top {
		margin-top: 50rpx !important;
	}

	.status-dialog {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 9998;

		&__mask {
			display: block;
			position: fixed;
			z-index: 1998;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: $bg-color-mask;
		}

		&__content {
			position: relative;
			z-index: 2000;
			top: calc(50% - 500rpx);
			left: 50%;
			margin-left: -310rpx;
			width: 564rpx;
			height: 1038rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.status-image {
				margin-top: 44rpx;
			}

			.tips {
				width: 100%;
				text-align: center;
				font-size: 16px;
				padding: 40rpx 0;
				border-bottom: 2rpx solid #E9E9E9;
			}

			.content {
				width: calc(100% - 90rpx);
				height: calc(100% - 330rpx);
				font-size: 28rpx;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				text-align: center;
				color: #666666;
				line-height: 50rpx;
				margin-top: 36rpx;
				overflow-y: scroll;
			}

			.radio {
				display: flex;
				align-items: center;

				.round {
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
					border: 1rpx solid #A8A8A8;
				}

				.check-round {
					border: 1rpx solid #0066E9;
					background-color: #0066E9;
					position: relative;
				}

				.jitter-div {
					animation: 1s jitter;
				}

				@keyframes jitter {
					0% {
						transform: scale(1.4, 1.4);
					}

					10% {
						transform: scale(1, 1);
					}

					25% {
						transform: scale(1.2, 1.2);
					}

					50% {
						transform: scale(1, 1);
					}

					70% {
						transform: scale(1.2, 1.2);
					}

					100% {
						transform: scale(1, 1);
					}
				}

				.check-round::before {
					font-family: "cuIcon";
					content: "\e645";
					position: absolute;
					color: #fff;
					top: 0;
					right: 0;
					font-size: 24rpx;
				}

				.value {
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #666666;
					line-height: 40rpx;
					margin-left: 18rpx;
				}
			}

			.comfirm {
				width: 100%;
				height: 88rpx;
				border-top: .5px solid #f5f5f5;
				text-align: center;
				line-height: 88rpx;
				font-size: 15px;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				position: absolute;
				bottom: 0;
			}

			.interaction-two {
				// border-top: .5px solid #f5f5f5;
				width: 100%;
				height: 71rpx;
				position: absolute;
				margin-top: 42rpx;
				bottom: 35rpx;
				display: flex;
				justify-content: center;

				.interaction-two-cancel {
					width: 50%;
					height: 88rpx;
					// border-right: .5px solid #F5F5F5;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					line-height: 88rpx;

				}

				.interaction-two-comfirm {
					width: 50%;
					height: 88rpx;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #fff;
					line-height: 88rpx;
					border-bottom-right-radius: 24rpx;
					background-color: #0066E9;
				}

				.interaction-one-comfirm {
					width: 336rpx;
					height: 71rpx;
					font-size: 30rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 71rpx;
					text-align: center;
					background-color: #0066E9;
					border-radius: 14rpx;
				}
			}
		}
	}


	.status-dialog2 {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 99999;

		&__mask {
			display: block;
			position: fixed;
			z-index: 1998;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: $bg-color-mask;
		}

		&__content {
			position: relative;
			z-index: 2000;
			top: calc(50% - 300rpx);
			left: 50%;
			margin-left: -310rpx;
			width: 620rpx;
			height: 620rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.status-image {
				margin-top: 44rpx;
			}

			.tips {
				width: 100%;
				text-align: center;
				margin-top: 44rpx;
				font-size: 16px;
				// margin-bottom: 20rpx;
				font-weight: bold;
				padding-bottom: 43rpx;
				border-bottom: 2rpx solid #E9E9E9;
			}

			.content {
				// width: calc(100% - 80rpx);
				height: auto;
				font-size: 28rpx;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				color: #666666;
				line-height: 40rpx;

				.content-title {
					margin: 40rpx 40rpx 17rpx 40rpx;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;
					line-height: 50rpx;
				}

				.input-wrapper {
					display: flex;
					align-items: center;
					margin: 0 40rpx;
					height: 76rpx;
					background: #FFFFFF;
					border-radius: 8rpx;
					border: 2rpx solid #DDDDDD;

					&>input {
						padding: 0 20rpx;
						height: 76rpx;
						line-height: 76rpx;
						flex: 1;

					}

					.code-img {
						width: 240rpx;
						height: 76rpx;
						// margin-left: 20upx;
					}

					.sms-btn {
						flex: 0 0 210rpx;
						width: 210rpx;
						text-align: right;
						height: 76rpx;
						line-height: 76rpx;
						margin-right: 20rpx;
						color: #3874FF;
					}

					.sms-btn.gray {
						color: #777777;
					}
				}

				.search-wrapper {

					text-align: center;

					.search {
						margin: 43rpx 0 68rpx 0;
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #323435;
						line-height: 40rpx;
					}
				}

				.result-wrapper {
					margin-top: 27rpx;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;
					line-height: 48rpx;

					.result {
						margin-bottom: 20rpx;
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #323435;
						line-height: 48rpx;

						.link {
							color: #0066E9;
						}
					}
				}
			}

			.comfirm {
				width: 100%;
				height: 88rpx;
				border-top: .5px solid #f5f5f5;
				text-align: center;
				line-height: 88rpx;
				font-size: 15px;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				position: absolute;
				bottom: 0;
			}

			.interaction-two {
				// border-top: .5px solid #f5f5f5;
				width: 100%;
				height: 71rpx;
				position: absolute;
				bottom: 60rpx;
				display: flex;
				justify-content: center;

				.interaction-two-cancel {
					width: 50%;
					height: 88rpx;
					// border-right: .5px solid #F5F5F5;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					line-height: 88rpx;

				}

				.interaction-two-comfirm {
					width: 50%;
					height: 88rpx;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #fff;
					line-height: 88rpx;
					border-bottom-right-radius: 24rpx;
					background-color: #0066E9;
				}

				.interaction-one-comfirm {
					width: 232rpx;
					height: 71rpx;
					font-size: 30rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 71rpx;
					text-align: center;
					background-color: #0066E9;
					border-radius: 14rpx;
				}
			}

			.blue-img {
				display: flex;
				margin-top: 55rpx;

				.blue,
				.zgjt {
					width: 48%;
					text-align: center;

					.value {
						margin-top: 39rpx;
					}
				}

				.dash-line {
					width: 1rpx;
					height: 154rpx;
					width: 1rpx;
					height: 154rpx;
					background-image: linear-gradient(to bottom, #C3C3C3 0%, #C3C3C3 60%, transparent 20%);
					background-size: 100% 8px;
					background-repeat: repeat-y;
				}
			}
		}
	}
</style>