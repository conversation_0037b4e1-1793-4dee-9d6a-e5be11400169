<template>
	<view class="sellPay-container">
		<view class="sellPay-Info">
			<view class="c-title">储值资金信息</view>
			<view class="cu-form-group">
				<view class="title">卡账金额:</view>
				<view class="group_money">{{ moneyFilter(cardAccount_money) + ' 元' }}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">卡内未圈存金额:</view>
				<view class="group_money">{{ moneyFilter(card_money) + ' 元' }}</view>
			</view>
			<!-- <view class="cu-form-group">
        <view class="title">卡内金额:</view>
        <view class="group_money">{{ moneyFilter(balance_money) + ' 元' }}</view>
      </view> -->
			<view class="cu-form-group">
				<view class="title">圈存异常被锁金额:</view>
				<view class="group_money">{{ moneyFilter(abnormal_money) + ' 元' }}</view>
			</view>
		</view>

		<view class="certification">
			<TButton v-if="isOwe" title="补缴欠费" @clickButton="payOwe" />
			<TButton v-else title="圈存" @clickButton="load" />
		</view>

		<tLoading :isShow="isLoading" />
		<view class="load_desc">
			<view class="desc_text">
				<text style="color:#0066E9">注意：</text>
				<view>
					1.实际圈存金额=用户账户可分配余额+卡内未圈存金额；
				</view>
				<view>
					2.如果用户账户可分配余额为负，请先填平欠费才能圈存。
				</view>
			</view>
		</view>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,
		getEtcVehicle
	} from '@/common/storageUtil.js'
	import TButton from '@/components/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue'
	import bleapi from '@/common/bluetooth/bleUtil.js'
	import {
		facapi
	} from '@/common/bluetooth/facUtil.js'
	import Api from '@/common/api/index.js'
	import float from '@/common/method/float.js'
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import {
		moneyFilter
	} from '@/common/util.js'
	const pinCode = '************'
	const terminalNo = '************'
	var _that
	import vue from 'vue'
	const SERVICENAME_PREFIX = vue.prototype.$serviceName
	export default {
		name: '',
		components: {
			TButton,
			tLoading
		},
		data() {
			return {
				moneyFilter,
				card_money: '', //卡内未圈存金额
				balance_money: '', //卡内金额
				abnormal_money: '', //异常被锁金额
				lock_money: '', //异常被锁金额
				cardAccount_money: '', //卡账金额
				isLoading: false,
				formData: {},
				comParams: {
					customer_id: '',
					vehicle_code: '',
					vehicle_color: ''
				},
				cpuCardInfo: {
					cpuCardNo: '',
					licenseColor: '',
					licensePlate: '',
					tollVehicleType: '',
					issueId: '',
					cpuCardType: '',
					signDate: '',
					expirationDate: ''
				},
				issue_times: '', // 发行服务器时间
				issue_code: '',
				isDisabled: false,
				isBtnLoaderObu: false,
				isReadCpu: false,
				isConnect: false,
				isActive: false,
				showActive: false,
				isBtnLoader: false,
				trade_no: '',
				accept_id: '',
				balance: '',
				oweMoney: '', //欠费金额
				isOwe: false, //是否欠费
				isDisable: false,
				card_balance: '', // 卡内余额
				trade_no_after: '' // 计数器
			}
		},
		computed: {
			customerInfo() {
				return getEtcVehicle() || {}
			},
			vehicleInfo() {
				return getEtcVehicle() || {}
			}
		},
		created() {},
		onLoad() {
			_that = this
			this.closeBle()
		},
		onShow() {
			this.getAmountInfo()
		},
		methods: {
			...mapActions(['setOWeData']),
			//补缴欠费
			payOwe() {
				uni.navigateTo({
					url: '../recharge/recharge'
				})
			},
			//获取账户余额并判断是否需要预分配或者反预分配
			getAmountInfo(callback) {
				let data = {
					routePath: this.$interfaces.loadCardAmount.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log(res, '查询账户余额')
						if (res.code == 200) {
							this.card_money = res.data.load_money
							this.balance_money = res.data.wallet_money
							this.abnormal_money = res.data.lock_money
							this.lock_money = res.data.lock_money
							this.cardAccount_money = res.data.card_money
							this.oweMoney = float.add(this.cardAccount_money, this.card_money)

							if (this.oweMoney < 0) {
								this.setOWeData(Math.abs(this.oweMoney))
								this.isOwe = true
							} else {
								this.setOWeData('')
								this.isOwe = false
							}
						}
						callback && callback()
					})
			},

			// 获取圈存初始金额
			getInitMoney() {
				let data = {
					routePath: this.$interfaces.loadInit.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id,
						vehicle_code: this.vehicleInfo.vehicle_code,
						vehicle_color: this.vehicleInfo.vehicle_color,
						customer_id: this.vehicleInfo.customer_id
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log(res, '圈存初始化')
						if (res.code == 200) {
							this.card_money = res.data.card_money
							this.balance_money = res.data.wallet_money
							this.abnormal_money = res.data.abnormal_money
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										uni.navigateBack()
									}
								}
							})
						}
					})
			},
			// 圈存
			load() {
				// if (this.lock_money) {
				// 	uni.showModal({
				// 		title: '提示',
				// 		content: '存在圈存异常被锁金额，请去处理',
				// 		showCancel: false,
				// 		success: (res) => {
				// 			if (res.confirm) {
				// 				uni.navigateBack({
				// 					delta: 1,
				// 				})
				// 			}
				// 		}
				// 	})
				// 	return
				// }
				if (this.isDisable) return
				this.isLoading = true
				this.isDisable = true
				this.isBtnLoader = true
				this.isActive = false
				this.isConnect = false
				let money = float.add(this.card_money, this.cardAccount_money)

				if (this.card_money == 0 && this.cardAccount_money == 0) {
					this.isLoading = false
					this.isBtnLoader = false
					uni.showModal({
						title: '提示',
						content: '卡内未圈存余额为零，请充值',
						showCancel: false
					})
					return
				}
				if (this.cardAccount_money > 0) {
					this.preAllocated()
					return
				}
				if (this.cardAccount_money < 0) {
					this.disPre()
					return
				}

				bleapi.CloseBle((obj) => {
					this.scanDevice((result) => {
						this.isConnect = result
						if (result) {
							setTimeout(() => {
								this.connectedCallback(this.isConnect)
							}, 1000)
						}
					})
				})
			},
			// //读卡
			// readCardHandle() {
			// 	this.isLoading = true
			// 	this.isBtnLoader = true
			// 	this.isActive = false
			// 	this.isConnect = false
			// 	this.scanDevice((result) => {
			// 		this.isConnect = result
			// 		if (result) {
			// 			this.openCard()
			// 		}
			// 	})
			// },

			// openCard() {
			// 	facapi.OpenCard((devResult) => {
			// 		if (devResult.code == 0) {
			// 			facapi.GetCardNo((devResult) => {
			// 				console.log('GetCardNo==>>', devResult, '打开卡片')
			// 				_that.disConnect()
			// 				_that.isActive = false
			// 				if (devResult.code == 0) {
			// 					console.log(devResult, '************')
			// 					this.formData.cardId = devResult.data
			// 					this.cpu_id = devResult.data
			// 					_that.isReadCpu = true
			// 					uni.showModal({
			// 						title: '提示',
			// 						content: '读卡成功',
			// 						showCancel: false
			// 					})
			// 					if (devResult.data !== this.vehicleInfo.cpu_card_id) {
			// 						this.isLoading = false
			// 						this.isBtnLoader = false
			// 						uni.showModal({
			// 							title: '提示',
			// 							content: '与请求卡号不一致，请重试',
			// 							showCancel: false
			// 						})
			// 					}
			// 				} else {
			// 					this.isLoading = false
			// 					this.disConnect()
			// 					_that.showModal({
			// 						title: '错误',
			// 						devResult: devResult,
			// 						content: '获取卡号失败：' +
			// 							devResult.code +
			// 							':' +
			// 							devResult.err_msg +
			// 							(devResult.msg ? ':' + devResult.msg : '')
			// 					})
			// 				}
			// 			})
			// 		} else {
			// 			_that.isActive = false
			// 			_that.showModal({
			// 				title: '错误',
			// 				devResult: devResult,
			// 				content: '打开卡失败：' +
			// 					devResult.code +
			// 					':' +
			// 					devResult.err_msg +
			// 					(devResult.msg ? ':' + devResult.msg : '')
			// 			})
			// 			_that.disConnect()
			// 		}
			// 	})
			// },

			// 1.开始扫描蓝牙设备
			scanDevice(callback) {
				bleapi.ScanDevice(SERVICENAME_PREFIX, (devResult) => {
					console.log('ScanDevice', devResult)
					if (devResult.code != 0) {
						console.log('搜索失败', devResult)
						_that.disConnect()
						_that.closeLoading()
						//搜索设备失败
						_that.showModal({
							title: '错误',
							devResult: devResult,
							content: devResult.err_msg,
							showMyContent: true
						})
					} else {
						console.log(
							'搜索到设备:' + devResult + ' ' + devResult.data.device_name
						)
						facapi.ConnectDevice(
							devResult.data,
							function(onDisconnect) {
								console.log('连接回调：', onDisconnect)
							},
							function(result) {
								console.log(result, 'result')
								bleapi.StopScanDevice(function(code) {
									console.log('返回数据', code)
								})
								if (result.code == 0) {
									console.log('连接标签设备成功')
									callback(true)
								} else {
									_that.closeLoading()
									_that.disConnect()
									_that.showModal({
										title: '错误',
										devResult: result,
										content: '设备连接失败，请将手机靠近设备后重试。',
										showMyContent: true
									})
									_that.$logger.error ('蓝牙连失败：',JSON.stringify(result))
									callback(false)
								}
							}
						)
					}
				})
			},
			// 1.1连接成功回调
			async connectedCallback(isConnected) {
				console.log('连接成功回调')
				if (isConnected) {
					facapi.OpenCard((devResult) => {
						console.log(devResult, '999999999999')
						if (devResult.code == 0) {
							facapi.GetCardNo((devResult) => {
								console.log(devResult, '777777777777')
								if (devResult.code == 0) {
									_that.cpu_id = devResult.data
									_that.getBalance(devResult)
								} else {
									_that.showModal({
										title: '错误',
										devResult: devResult,
										content: '获取卡号失败：' +
											devResult.code +
											':' +
											devResult.err_msg +
											(devResult.msg ? ':' + devResult.msg : '')
									})
									_that.disConnect()
								}
							})
						} else {
							_that.showModal({
								title: '错误',
								devResult: devResult,
								content: '打开卡失败：' +
									devResult.code +
									':' +
									devResult.err_msg +
									(devResult.msg ? ':' + devResult.msg : '')
							})
							_that.disConnect()
						}
					})
				}
			},
			// 2.获取卡内余额
			getBalance(devResult) {
				if (devResult.code == 0) {
					facapi.GetBalance((devResult) => {
						//获取余额成功
						console.log('获取余额成功-devResult', devResult)
						if (devResult.code == 0) {
							if (devResult.data && devResult.data.length == 8) {
								let balance = parseInt(devResult.data, 16)
								console.log('圈存余额balance', balance)
								this.balance = balance
								_that.initLoad(devResult)
							} else {
								_that.showModal({
									title: '错误',
									devResult: {
										code: '11013',
										err_msg: '获取卡余额失败：长度不符'
									},
									content: '获取卡余额失败：长度不符'
								})
								_that.cpuComplete({
									code: '11013',
									err_msg: '获取卡余额失败：长度不符'
								})
							}
						} else {
							console.log(devResult, 'devResultdevResultdevResult')
							_that.showModal({
								title: '错误',
								devResult: devResult,
								content: '获取卡额失败：' +
									devResult.code +
									':' +
									devResult.err_msg +
									(devResult.msg ? ':' + devResult.msg : '')
							})
							_that.disConnect()
						}
					})
				}
			},
			// 3.圈存
			initLoad(devResult) {
				this.issue_times = this.formatDate(new Date())
				let money = this.card_money
				if (devResult.code == 0) {
					console.log(
						'初始化圈存',
						this.issue_times,
						pinCode,
						money,
						terminalNo,
						devResult
					)
					facapi.InitLoad(
						pinCode,
						money,
						this.issue_times,
						terminalNo,
						(rand, trade_no, mac1, balance, callback) => {
							console.log('initLoad:getMac', rand, trade_no, mac1, balance)
							this.trade_no = trade_no
							_that.getInitLoad(rand, trade_no, balance, mac1, (result) => {
								callback(result)
							})
						},
						(nextDevResult) => {
							//初始化圈存完成成功
							console.log('初始化圈存结果', nextDevResult)
							this.isLoading = false
							if (nextDevResult.code == 0) {
								_that.getCurBalanceAndTradeNo()
							} else {
								_that.showModal({
									title: '错误',
									devResult: nextDevResult,
									content: '圈存初始化失败：' + nextDevResult.code
								})
								_that.disConnect()
							}
						}
					)
				} else {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '写0015文件失败：' +
							devResult.code +
							':' +
							devResult.err_msg +
							(devResult.msg ? ':' + devResult.msg : '')
					})
					_that.disConnect()
				}
			},
			//4.调用圈存申请接口
			async getInitLoad(rand, trade_no, balance, mac1, callback) {
				let tmpData = {
					rand: rand,
					money: this.card_money,
					term_id: terminalNo,
					trade_no: trade_no,
					dec_time: this.issue_times,
					mac1: mac1,
					card_balance: balance,
					cpu_card_id: this.cpu_id
				}
				let comparams = {
					customer_id: this.vehicleInfo.customer_id,
					vehicle_code: this.vehicleInfo.vehicle_code,
					vehicle_color: this.vehicleInfo.vehicle_color
				}
				this.comparams = comparams
				Object.assign(tmpData, comparams)

				console.log('初始化圈存入参', tmpData)
				let data = {
					routePath: this.$interfaces.loadApply.method,
					bizContent: {
						...tmpData
					}
				}
				await this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log(res, '圈存申请')
						if (res.code == 200) {
							this.accept_id = res.data.accept_id
							let code = res.code == 200 ? 0 : res.code
							callback({
								code: code,
								err_msg: res.msg,
								data: res.data.mac2
							})
						} else {
							this.disConnect()
							this.isLoading = false
							this.isBtnLoader = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						callback({
							code: 11003,
							err_msg: error.msg
						})
					})
			},
			//5.获取卡内余额和卡内圈存计数器
			getCurBalanceAndTradeNo() {
				let money = this.card_money
				this.isLoading = true
				console.log(
					'getCurBalanceAndTradeNo',
					this.issue_times,
					pinCode,
					money,
					terminalNo
				)
				facapi.GetCurBalanceAndTradeNo(
					pinCode,
					money,
					this.issue_times,
					terminalNo,
					(nextDevResult) => {
						if (nextDevResult.code == 0) {
							this.card_balance = parseInt(nextDevResult.data.balance, 16)
							this.trade_no_after = nextDevResult.data.tradeNo
							console.log('圈存上报信息查询', nextDevResult.data)
							this.loadComplete(this.getAmountInfo)
						} else {
							this.isLoading = false
							_that.disConnect()
							_that.showModal({
								title: '错误',
								devResult: nextDevResult,
								content: '圈存上报信息查询异常：' + nextDevResult.code
							})
						}
					}
				)
			},

			//6.圈存确认
			async loadComplete(callback) {
				let data = {
					routePath: this.$interfaces.loadComplete.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id,
						vehicle_code: this.vehicleInfo.vehicle_code,
						vehicle_color: this.vehicleInfo.vehicle_color,
						customer_id: this.vehicleInfo.customer_id,
						accept_id: this.accept_id,
						load_money: this.card_money,
						tac: '',
						card_balance: this.card_balance,
						term_id: terminalNo,
						trade_no_before: this.trade_no,
						trade_no_after: this.trade_no_after,
						load_from: '3'
					}
				}
				console.log(data, '圈存确认入参')
				await this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.disConnect()
						this.isLoading = false
						if (res.code != 200) {
							uni.showModal({
								title: '提示',
								content: '圈存上报失败' + res.msg + '【' + res.code + '】',
								showCancel: false
							})
						} else {
							// if (this.lock_money) {
							// 	uni.showModal({
							// 		title: '提示',
							// 		content: '圈存异常',
							// 		showCancel: false
							// 	})
							// 	return
							// }
							uni.showModal({
								title: '提示',
								content: '圈存成功',
								showCancel: false
							})
						}
					})
				callback && callback()
			},
			//预分配
			preAllocated() {
				let params = {
					customer_id: this.customerInfo.customer_id,
					cpu_card_id: this.vehicleInfo.cpu_card_id,
					vehicle_code: this.vehicleInfo.vehicle_code,
					vehicle_color: this.vehicleInfo.vehicle_color,
					money: this.cardAccount_money,
					source: 3
				}
				let data = {
					routePath: this.$interfaces.preAllocate.method,
					bizContent: {
						...params
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						if (res.code == 200) {
							this.getAmountInfo(this.toLoad)
						} else {
							this.isLoading = false
							this.isBtnLoader = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((err) => {
						this.isLoading = false
						this.isBtnLoader = false
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						})
					})
			},
			disPre() {
				let params = {
					customer_id: this.customerInfo.customer_id,
					cpu_card_id: this.vehicleInfo.cpu_card_id,
					vehicle_code: this.vehicleInfo.vehicle_code,
					vehicle_color: this.vehicleInfo.vehicle_color,
					money: Math.abs(this.card_money)
					// money: Math.abs(1)

				}
				let data = {
					routePath: this.$interfaces.disPre.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						this.getAmountInfo(this.dispreandPre)

					} else {
						this.disConnect()
					}
				})
			},
			dispreandPre() {
				let money = float.add(this.cardAccount_money, this.card_money)
				if (this.cardAccount_money == 0 && this.card_money == 0) {
					this.disConnect()
					uni.showModal({
						title: '提示',
						content: '补缴欠费后实际圈存金额为零，请充值',
						showCancel: false
					})
				} else if (this.cardAccount_money >= 0 && money > 0) {
					console.log('preAllpcated', '========================');
					this.preAllocated()
				}
			},
			toLoad() {
				this.scanDevice((result) => {
					this.isConnect = result
					if (result) {
						setTimeout(() => {
							this.connectedCallback(this.isConnect)
						}, 1000)
					}
				})
			},
			disConnect() {
				this.isDisable = false
				facapi.facSdk.DisconnectDevice(function(code) {
					console.log('关闭连接结果', code)
					_that.isLoading = false
					_that.isBtnLoader = false
				})
				// 完成后关闭蓝牙模块
				bleapi.CloseBle((obj) => {
					console.log(obj)
				})
			},
			closeBle() {
				// 关闭蓝牙模块，防止中途断开，连接不上
				bleapi.CloseBle((obj) => {
					console.log(obj)
				})
			},
			// 显示弹框
			showModal(data) {
				//隐藏loading
				this.isLoading = false
				_that.isBtnLoader = false

				console.log(data.content, 'sdk报错')

				//显示弹框
				let obj = {
					...data
				}
				obj = data.showMyContent ?
					obj : {
						...data,
						content: '操作失败，请打开手机蓝牙，将手机靠近设备后重试。(' +
							data.devResult.code +
							':' +
							data.devResult.err_msg +
							')'
					}
				uni.showModal({
					...obj,
					showCancel: false,
					success: () => {}
				})
			},
			closeLoading() {
				this.connectLoaing = false
				// this.isLoading = false;
			},
			// 圈存存入当前时间
			formatDate(now) {
				var year = now.getFullYear()
				var month =
					now.getMonth() + 1 < 10 ?
					'0' + (now.getMonth() + 1) :
					now.getMonth() + 1
				var date = now.getDate() < 10 ? '0' + now.getDate() : now.getDate()
				var hour = now.getHours() < 10 ? '0' + now.getHours() : now.getHours()
				var minute =
					now.getMinutes() < 10 ? '0' + now.getMinutes() : now.getMinutes()
				var second =
					now.getSeconds() < 10 ? '0' + now.getSeconds() : now.getSeconds()
				return (
					year + '' + month + '' + date + '' + hour + '' + minute + '' + second
				)
			},

		}
	}
</script>

<style lang="scss">
	.sellPay-Info {
		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.group_money {
		/* margin-right: 300upx; */
		float: right;
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.load_desc {
		margin: 60rpx 36rpx;

		.desc_title {
			font-size: 28rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #555555;
			margin-bottom: 20rpx;
		}

		.desc_text {
			text-align: left;
			line-height: 50rpx;
			text-indent: 2em;
			padding-bottom: 10rpx;
			font-size: 26rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #aaaaaa;
		}
	}
</style>
