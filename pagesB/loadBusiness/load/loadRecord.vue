<template>
	<view class="sellPay-container">
		<view class="scroll-box">
			<scroll-view :style="'height:'+(windowHeight-10)+'px;'" :scroll-top="scrollTop" scroll-y="true"
				class="scroll-Y" :lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower"
				@scroll="scroll">
				<view class="sellPay-Info">
					<view class="c-title">用户信息</view>
					<form>
						<view class="cu-form-group">
							<view class="title">用户名称:</view>
							<input placeholder="用户名称" :value="vehicleInfo.customer_name" name="a" disabled></input>
						</view>
						<view class="cu-form-group">
							<view class="title">车牌号码:</view>
							<input placeholder="车牌号码" :value="vehicleInfo.vehicle_code" name="a" disabled></input>
						</view>
						<view class="cu-form-group">
							<view class="title">ETC卡号:</view>
							<input placeholder="ETC卡号" :value="vehicleInfo.cpu_card_id" name="a" disabled></input>
						</view>
						<view class="cu-form-group">
							<view class="title">卡内未圈存金额:</view>
							<view>{{moneyFilter(card_money)+' 元'}}</view>
						</view>
						<!-- <view class="cu-form-group">
							<view class="title">卡内金额:</view>
							<view>{{moneyFilter(balance_money)+' 元'}}</view>
						</view> -->
					</form>
				</view>
				<rechargeList @getRechargeInfo='refreshList' style="height: 100px;" v-for="(item,index) in rechargeList"
					:key='index' :rechargeInfo='item'>
				</rechargeList>
				<load-more :loadStatus="noticeLoadStatus" />
			</scroll-view>
		</view>


	</view>
</template>
<script>
	import TButton from "@/components/t-button.vue";
	import rechargeList from './item.vue'
	import loadMore from '../../components/load-more/index.vue';
	import {
		getVehicleColor,

	} from '@/common/method/filter.js';
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOrderSource,
		setOrderSource,
		getEtcVehicle
	} from "@/common/storageUtil.js";
	export default {
		components: {
			TButton,
			rechargeList,
			loadMore
		},
		data() {
			return {
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				cardAmount: {},
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				balance_money: '',
				card_money: '',
				rechargeList: [],
				page_index: 1,
				page_size: 100,
				formData: {
					"card_no": ""
				},
				vehicleInfo:{}
			};
		},


		onLoad(obj) {
			console.log(obj, 'objojbojboboobbj');
			if (obj && obj.cardNo) {
				this.formData.card_no = obj.cardNo
				this.vehicleBizSearch()
				return
			}
			this.formData.card_no = getCurrentCar() ? getCurrentCar().cpu_card_id : ''
			this.vehicleBizSearch()
		},
		methods: {
			vehicleColorStr() {
				return getVehicleColor(this.vehicleInfo.vehicle_color);
			},
			refreshList() {
				this.loadRechargeList();
				this.loadCardAmount()
			},
			vehicleBizSearch() {
				let data = {
					routePath: this.$interfaces.vehicleBizSearch.method,
					bizContent: {
						cpu_card_id: this.formData.card_no
					}
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;

						if (res.code == 200 && res.data && res.data.length) {
							this.vehicleInfo = res.data[0]
							this.loadRechargeList();
							this.loadCardAmount()
						}
					})
					.catch(error => {
						this.isLoading = false;
					});
			},
			loadCardAmount() {
				let data = {
					routePath: this.$interfaces.loadCardAmount.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id,

					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						this.balance_money = res.data.wallet_money
						this.card_money = res.data.card_money
					}
				}).catch(error => {

				})
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				/* if (this.request) return;
				let self = this;
			
			
				setTimeout(function() {
					self.formData.pageNum = self.formData.pageNum + 1;
					self.getOrderListHandle(self.formData);
				}, 500) */

			},
			scroll: function(e) {

				this.old.scrollTop = e.detail.scrollTop;

			},
			loadRechargeList() {
				this.noticeLoadStatus = 1;
				let params = {
					routePath: this.$interfaces.loadDetail.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id,
						page_index: this.page_index,
						page_size: this.page_size,
					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					console.log(res, '圈存记录');
					if (res.code == 200) {
						if (res.data && res.data.length) {
							this.rechargeList = res.data
							this.noticeLoadStatus = 3;
						} else {
							this.noticeLoadStatus = 0;
						}
					} else {
						this.noticeLoadStatus = 2;
					}
					console.log(res);
				}).catch((err) => {
					this.noticeLoadStatus = 2;
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
		destroyed() {

		}
	};
</script>
<style lang="scss">
	.sellPay-Info {
		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.sellPay-Info .cu-form-group radio-group {

		flex: 1;
		text-align: left;
	}
</style>
