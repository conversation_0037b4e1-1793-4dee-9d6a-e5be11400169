// 定义一个 mixin
const newPinCode = "0020000003888888";
const pinCode = "0020000006313233343536";
const terminalNo = "112233445566";
const defaultRand = "00000000";
const defaultObuId = "0000000000000000";
import bleapi from '@/common/bluetooth/bleUtil.js';
import {
	facapi
} from '@/common/bluetooth/facUtil.js';
import * as nfcSdk from '@/pagesB/js_sdk/NFCjs/NFCSdk.js';
export const nfcLoadMixin = {
	data() {
		return {
			_loadSuccessInterface: null,
			loadTradeNo: '', // 圈存交易流水号
			initLoadPin: pinCode,
			obuSystemInfo: null, // 标签系统信息
			cpu0015Info: null, // 卡片0015文件信息
		};
	},
	methods: {
		_baseInitLoad(devResult,fn) {
			this._initLoad(devResult)
			this._loadSuccessInterface = fn 
		},
		// 调用圈存初始化SDK
		_initLoad(devResult) {
			this.issue_times = this._formatDate(new Date());
			if (devResult.code == 0) {
				this.connectionStatus && this.connectionStatus()
				console.log("初始化圈存", this.issue_times, this.initLoadPin, this.moneyData.load_money, terminalNo, devResult);
				nfcSdk.Load(
					this.initLoadPin,
					this.moneyData.load_money,
					this.issue_times,
					terminalNo,
					(...args) => {
						const safeArgs = Array.isArray(args) ? args : Array.from(args);
						const lastArgHandle = typeof safeArgs[safeArgs.length - 1] === "function" ? safeArgs[
							safeArgs.length - 1] : null;
						const restArgs = safeArgs.slice(0, lastArgHandle ? -1 : safeArgs.length);

						this._getInitLoad(...restArgs, result => {
							if (result.code == 0) {
								lastArgHandle && lastArgHandle(result);
							} else {
								this.showModal({
									title: '错误',
									devResult: result,
									content: '圈存失败：' + result.code + ':' + result.err_msg
								});
							}
						});
					},
					nextDevResult => {
						if (nextDevResult.code == 0) {
							this._getCurBalanceAndTradeNo()
						} else {
							this.showModal({
								title: '错误',
								devResult: nextDevResult,
								content: '圈存初始化失败：' + nextDevResult.code
							})
						}
					}
				);
			} else {
				this.showModal({
					title: "错误",
					devResult: devResult,
					content: "读余额失败：" +
						devResult.code +
						":" +
						devResult.err_msg +
						(devResult.msg ? ":" + devResult.msg : ""),
				});
			}
		},
		
		//圈存申请接口
		async _getInitLoad(...args) {
			const safeArgs = Array.isArray(args) ? args : Array.from(args);
			//rand, trade_no,mac1, balance,callback
			//rand, trade_no,mac1, balance,keyVersion,flag, callback
			const lastArgHandle = typeof safeArgs[safeArgs.length - 1] === "function" ? safeArgs[safeArgs
				.length -
				1] : null;
			// 必须参数
			const rand = safeArgs[0];
			const tradeNo = safeArgs[1];
			const mac1 = safeArgs[2];
			const balance = safeArgs[3];
			// 可选参数
			const version = safeArgs.length > 5 ? safeArgs[4] : null;
			const algorithm = safeArgs.length > 6 ? safeArgs[5] : null;
			var terminal_id = terminalNo;
			
			const baseParams = {
				...this.vehicle,
				dec_time: this.issue_times,
				cpu_card_id: this.scanDevice_cpu_card_id
			}
			const loadParams = {
				rand: rand,
				money: this.moneyData.load_money,
				term_id: terminal_id,
				trade_no: tradeNo,
				mac1: mac1,
				card_balance: this._convertToHexLength8(balance),
				keyId: '01'
				
			}
			let sm4Params = {
				"version": version,
				algorithm: algorithm,
			}
			let params = Object.assign(baseParams, loadParams);
			this.trade_no = tradeNo
			this.loadTradeNo = tradeNo;
			if (this.cpu0015Info.version && Number(this.cpu0015Info.version) > 50) {
				params = Object.assign(params, sm4Params);
				params.keyId = '41'
			}
			let data = {
				routePath: this.$interfaces.loadApply.method,
				bizContent: {
					...params
				}
			}
			await this.$request
					.post(this.$interfaces.issueRoute, {
					data: data
				})
				.then((res) => {
					let code = res.code == '200' ? 0 : res.code
					this.accept_id = res.data.accept_id
					lastArgHandle && lastArgHandle({
						code: code,
						err_msg: res.msg,
						data: res.data.mac2,
					});
				})
				.catch((error) => {
					lastArgHandle && lastArgHandle({
						code: 11003,
						err_msg: error.msg,
					});
				});
		},
		//获取卡内余额和卡内圈存计数器
		_getCurBalanceAndTradeNo() {
			this.isLoading = true
			nfcSdk.GetCurBalanceAndTradeNo(
				pinCode,
				this.moneyData.load_money,
				this.issue_times,
				terminalNo,
				(nextDevResult) => {
					if (nextDevResult.code == 0) {
						this.card_balance = parseInt(nextDevResult.data.balance, 16)
						this.trade_no_after = nextDevResult.data.tradeNo
						console.log('圈存上报信息查询', nextDevResult.data)
						this._loadSuccessInterface && this._loadSuccessInterface();
						//this.loadComplete(this.getAmountInfo)
					} else {
						this.isLoading = false;
						this.showModal({
							title: '错误',
							devResult: nextDevResult,
							content: '圈存上报信息查询异常：' + nextDevResult.code
						});
					}
				}
			)
		},
		// 读取卡内余额
		_getBalanceCommand(callback) {
			nfcSdk.GetBalance((devResult) => {
				//获取余额成功
				console.log("获取余额成功-devResult", devResult);
				if (devResult.code == 0) {
					if (devResult.data && devResult.data.length == 8) {
						let balance = parseInt(devResult.data, 16);
						console.log("圈存余额balance", balance);
						callback && callback(devResult)
					} else {
						this.showModal({
							title: "错误",
							devResult: {
								code: "11013",
								err_msg: "获取卡余额失败：长度不符",
							},
							content: "获取卡余额失败：长度不符",
						});
					}
				} else {
					console.log(devResult, "devResultdevResultdevResult");
					this.showModal({
						title: "错误",
						devResult: devResult,
						content: "获取卡余额失败：" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
				}
			});
		},
		
		_convertTradeNo(tradeNo) {
			// 将16进制的字符串转换为整数
			let number = parseInt(tradeNo, 16);
			// 对该数加1
			number += 1;
			// 转换为16进制并补齐4位
			return number.toString(16).padStart(4, '0');
		},
		_convertToHexLength8(input) {
			let hexString;

			// 如果输入是字符串，并且是有效的16进制字符串
			if (typeof input === 'string' && /^[0-9A-Fa-f]{1,8}$/.test(input)) {
				hexString = input.toUpperCase().padStart(8, '0'); // 保证是大写且长度为8
			} else if (typeof input === 'string' || typeof input === 'number') {
				const num = Number(input);
				// 检查是否是非负整数
				if (!Number.isInteger(num) || num < 0) {
					return ''; // 无效数字返回空字符串
				}

				// 转换为16进制字符串，并填充至8位
				hexString = num.toString(16).toUpperCase().padStart(8, '0');
			} else {
				return ''; // 无效输入，返回空字符串
			}

			// 确保16进制字符串不超过8位
			return hexString.length === 8 ? hexString : '';
		},
		// 圈存存入当前时间
		_formatDate(now) {
			var year = now.getFullYear();
			var month =
				now.getMonth() + 1 < 10 ?
				"0" + (now.getMonth() + 1) :
				now.getMonth() + 1;
			var date = now.getDate() < 10 ? "0" + now.getDate() : now.getDate();
			var hour = now.getHours() < 10 ? "0" + now.getHours() : now.getHours();
			var minute =
				now.getMinutes() < 10 ? "0" + now.getMinutes() : now.getMinutes();
			var second =
				now.getSeconds() < 10 ? "0" + now.getSeconds() : now.getSeconds();
			return (
				year + "" + month + "" + date + "" + hour + "" + minute + "" + second
			);
		},
	}
};