<template>
	<view class="sellPay-container">

		<!-- 互联网账户信息 -->
		<!-- <netAccountInfo :accountInfoData='accountInfoData'> </netAccountInfo> -->

		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="">
					车辆信息
				</view>
				<view class="">
					<view class='cu-tag light ' :class="vehicleInfo.cpu_status=='1'?'bg-olive':'bg-red'">
						{{vehicleInfo.cpu_status=='1'?'正常':'限制通行'}}
					</view>
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">车牌号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{vehicleInfo.vehicle_code}}
							<!-- 【{{vehicleColorStr}}】 -->
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">ETC卡号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{noPassByCardNo(vehicleInfo.cpu_card_id)}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡片可用余额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{moneyFilter(cardAccount_money)}}元
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">可圈存金额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{moneyFilter(card_money)}}元
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">圈存异常被锁金额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{moneyFilter(lock_money)}}元
						</view>
					</view>
				</view>

			</view>
		</view>
		<view class="sellPay-Info">
			<view class="c-title g-flex g-flex-align-center g-flex-justify">
				<view class="">
					请选择充值金额
				</view>
				<view style="font-weight: normal;color: #1978ec;" @click="refundVisible">
					充值退款说明 <text style="margin-left: 10rpx;" class="cuIcon-question"></text>
				</view>
			</view>
			<view class="amount-box g-flex g-flex-wrap ">
				<view v-for="(item,index) in amountList" :key="index" class="amount-item"
					@click="selectAmout(item,index)"
					:style="(amountMoneyIndex!=null&&amountMoneyIndex==index)?'border:1px solid  #1978ec':''">
					<view class="g-flex g-flex-center g-flex-align-center">
						<view class="amount-text">
							{{item.label}}
						</view>
						<view class="amount-item-label">元</view>
					</view>
				</view>
				<view class="amount-item g-flex g-flex-align-center g-flex-justify" style="width: 60%;padding: 0 20upx;"
					:style="isAnyMount?'border:1px solid #1978ec':''">
					<input type="digit" @focus="clickInput" class="amount-custom-input" placeholder="其他支付金额" name="b"
						v-model="otherMoney" @input="(e)=> handleInput('otherMoney',e)"></input>
					<text style="margin-right: 10upx;">元</text>
				</view>
			</view>

		</view>
		<view class="sellPay-Info">
			<view class="c-title">充值信息</view>
			<form>
				<view class="cu-form-group">
					<view class="title">支付方式:</view>
					<picker style="width:100%;" @change="bindPayTypeChange" :range="payTypePartOptions"
						:disabled="accountInfoData.availableAmount&&accountInfoData.availableAmount=='0'"
						range-key="label">
						<view class="weui-picker-value">{{ payTypePartOptions[pay_type_index].label }}</view>
					</picker>
					<text class="cuIcon-right"></text>
				</view>
				<view class="available-money" v-if='formData.recharge_type == "********"'>
					<view class="available-money-item">账户可用余额：
						<text
							v-if="showMoney">{{moneyFilter(accountInfoData.availableAmount)+'元(余额不足，请选择其他支付方式)'}}</text>
						<text v-else>{{moneyFilter(accountInfoData.availableAmount)+'元'}}</text>
					</view>
				</view>
				<!-- 				<view class="cu-form-group" v-if="formData.recharge_type=='********'">
					<view class="title form_label-require">图片验证码:</view>
					<input placeholder="请输入验证码" class="input" name="smsCode" :value='balancePayment.captchaCode'
						@input="changeInput($event,'captchaCode')"></input>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">

				</view> -->
				<view class="cu-form-group" v-if="formData.recharge_type=='********'">
					<view class="title form_label-require">短信验证码:</view>
					<input placeholder="请输入短信验证码" class="input" name="smsCode" :value='balancePayment.mobileCode'
						@input="changeInput($event,'mobileCode')"></input>
					<button class="codebtn" :style="codebr?'background:#0066E9':''" :disabled="codebr" @tap="toget">
						<text v-if="codebr">重新发送({{count}})s</text>
						<text v-else>获取验证码</text>
					</button>


				</view>
			</form>
		</view>
		<view class="certification">
			<TButton title='去充值' @clickButton="goPay" :isLoadding="isBtnLoader" />
		</view>
		<TModal :showModal='dialogVisible' modalTitle='请仔细核对充值信息' :showCancelFlag='true'
			@cancelModal='dialogVisible=false' @okModal='onPayHandle' okText='去支付'>
			<form slot='content' class="sellPay-Info">
				<view class="cu-form-group">
					<view class="title">充值账户</view>
					<view class="value">{{vehicleInfo.customer_name}}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">充值车辆</view>
					<view class="value" style="font-size: 34rpx;font-weight: bold;">
						{{vehicleInfo.vehicle_code}}【{{vehicleColorStr}}】
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">支付金额</view>
					<view class="value">{{formData.money}}元</view>
				</view>
			</form>
		</TModal>

		<!-- 跳转提示框 -->
		<TModal :showModal='jumpDialogVisible' modalTitle='提示' :showCancelFlag='true' @okModal='jumpLoad'
			@cancelModal='jumpLoadCancel' :okText="businessType != 'cancel'?'前往圈存':'前往注销'" cancelText='返回'>
			<view slot='content' class="jump-dialog" v-if="businessType != 'cancel'">
				<view class="jump-text">
					{{rechargeStatus+'！3秒后将自动跳转圈存操作界面，请勿退出'}}
				</view>
				<view class="jump-tips">
					(温馨提示:充值到帐可能会有延迟)
				</view>
			</view>
			<!-- 线上注销弹框内容 -->
			<view slot='content' class="jump-dialog" v-if="businessType == 'cancel'">
				<view class="jump-text">
					充值成功，可返回继续操作
				</view>
				<view class="jump-tips">
					(温馨提示:充值到帐可能会有延迟)
				</view>
			</view>
		</TModal>
		<t-captcha id="captcha" app-id="*********" @verify="handlerVerify" @ready="handlerReady" @close="handlerClose"
			@error="handlerError" />
		<tLoading :isShow="isLoading" />

	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,
		getEtcVehicle,
		getLoginUserInfo,
		getEtcAccountInfo,
		setOpenid
	} from "@/common/storageUtil.js";
	import {
		amountList,
		payTypePartOptions
	} from '@/common/const/optionData.js'
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import TModal from '@/components/t-modal/t-modal.vue'
	import float from '@/common/method/float.js'
	import {
		twoDecimal,
		moneyFilter,
		noPassByCardNo,
		noPassByName
	} from '@/common/util.js'
	import {
		getVehicleColor,
	} from '@/common/method/filter.js';
	import {
		mapGetters,
		mapActions
	} from 'vuex';
	import netAccountInfo from '@/pagesB/components/netAccountInfo/index.vue'
	export default {
		name: '',
		components: {
			TButton,
			TModal,
			tLoading,
			netAccountInfo
		},
		data() {
			return {

				card_money: '', //卡内未圈存金额
				cardAccount_money: '', //卡账金额
				lock_money: '', //异常被锁金额
				formData: {
					"recharge_type": "********", //充值值方式
					"card_no": "", //卡号
					"money": '', //金额
					"source": "3", //来源
					"auth_code": "",
					"open_id": "",
					"tradeType": 'CARD_LOAD', //场景
					"agent_user": '', //用户编号
				},
				isBtnLoader: false,
				dialogVisible: false,
				isLoading: false,
				type: '',
				vehicleInfo: {},
				amountList,
				amountMoneyIndex: null,
				otherMoney: '',
				oweData: "",
				isFirst: true,
				isAnyMount: '',
				acceptData: {},
				checkDialogVisible: false,
				rechargeOther: false,
				checkFirstName: "",
				hideFirstName: '',
				isFocus: false,
				payTypePartOptions,
				accountInfoData: {}, //互联网账户信息
				pay_type_index: 0,
				balancePayment: {
					captchaCode: "", // 图形验证码code
					mobileCode: "", // 短信验证码
					mobile: "",
					userNo: ''
				},
				codeUrl: '', //图形验证码url
				timer: null,
				count: 60,
				codebr: false,
				codeid: '', //图形验证码ID，
				rechargeStatus: '',
				jumpDialogVisible: false,
				countdowner: null, //倒计时定时器
				businessType: '',
				codeTicket: '' //腾讯验证码

			}
		},
		computed: {
			vehicleColorStr() {
				return getVehicleColor(this.vehicleInfo.vehicle_color);
			},
			showMoney() {
				if (this.formData.money) {
					return float.mul(this.formData.money, 100) > float.mul(this.accountInfoData.availableAmount, 1)
				}
			}
		},
		onUnload() {
			clearTimeout(this.countdowner)
		},
		onLoad(option) {
			console.log(option, 'optionoptionoption');
			if (option.type) {
				this.type = option.type || "";
			}
			this.businessType = option.businessType || ''
			if (option.vehicleInfoData) {
				this.acceptData = JSON.parse(option.vehicleInfoData)
			}
			this.formData.agent_user = getLoginUserInfo().userNo;
			this.balancePayment.mobile = getEtcAccountInfo().mobile
			this.formData.open_id = getOpenid() || ''
			this.formData.card_no = getCurrentCar() ? getCurrentCar().cpu_card_id : ''
			this.vehicleInfo = getCurrentCar() ? getCurrentCar() : {}
			this.customerInfo = getCurrUserInfo() ? getCurrUserInfo() : {}
			this.getAccountInfo()
		},

		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
		},
		methods: {
			moneyFilter,
			noPassByCardNo,
			noPassByName,
			getOpenIdHandle() {

				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.formData.open_id = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			// 验证码验证结果回调
			handlerVerify(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
				if (ev.detail.ret === 0) {
					// 验证成功
					this.codeTicket = ev.detail.ticket
					console.log('ticket:', ev.detail.ticket)
					this.getverification()
				} else {
					// 验证失败
					// 请不要在验证失败中调用refresh，验证码内部会进行相应处理
				}
			},
			// 验证码准备就绪
			handlerReady() {
				console.log('验证码准备就绪')
			},
			// 验证码弹框准备关闭
			handlerClose(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
				if (ev && ev.detail.ret && ev.detail.ret === 2) {
					console.log('点击了关闭按钮，验证码弹框准备关闭');
				} else {
					console.log('验证完成，验证码弹框准备关闭');
				}
			},
			// 验证码出错
			handlerError(ev) {
				console.log(ev.detail.errMsg)
			},
			//弹框确认圈存取消
			jumpLoadCancel() {
				uni.reLaunch({
					url: "/pagesB/rechargeBusiness/selectVehicle/index"
				})
				this.jumpDialogVisible = false
			},
			//弹框确认圈存确定
			jumpLoad() {
				// 添加线上注销内容跳转
				if (this.businessType == 'cancel') {
					uni.reLaunch({
						url: '/pagesC/afterSaleBusiness/home/<USER>'
					})
				} else {
					uni.reLaunch({
						url: '/pagesB/loadBusiness/loadType'
					})
				}
				this.jumpDialogVisible = false
			},
			refundVisible() {
				uni.showModal({
					title: "充值退款说明",
					content: '充值后未圈存、未使用的,可在24小时内到网点或联系客服申请原路退款。客服热线:0771-5896333',
					showCancel: false,
				});
			},
			//选择金额
			selectAmout(item, index) {
				this.formData.money = ''
				this.otherMoney = ''
				this.amountMoneyIndex = index
				this.formData.money = item.value
				this.isAnyMount = false
			},
			//输入框聚焦事件
			clickInput() {
				this.amountMoneyIndex = null
				if (!this.isAnyMount) {
					this.formData.money = ''
				}
				this.isAnyMount = true
			},
			bindPayTypeChange(e) {
				this.pay_type_index = e.detail.value;
				this.formData.recharge_type = this.payTypePartOptions[e.detail.value].value || '';
				console.log(this.formData.recharge_type);
				this.balancePayment.mobileCode = ''
				if (this.formData.recharge_type == "********") {
					this.getCaptcha();
				}
			},
			changeInput(event, data) {
				this.balancePayment[data] = event.detail.value;
			},
			//获取图形验证码
			getCaptcha() {
				this.balancePayment.captchaCode = '',
					this.$request
					.post(this.$interfaces.getCaptcha).then(res => {
						console.log(res, '===============');
						if (res.code == 200) {
							this.codeUrl = res.data.image
							this.codeid = res.data.captchaId
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
					})
			},
			toget() {
				// if (this.validateCodeImg()) {
				// this.getverification()
				this.selectComponent('#captcha').show()
				// }
			},
			validateCodeImg() {
				if (!this.balancePayment.captchaCode) {
					uni.showModal({
						title: "提示",
						content: "请输入图形证码",
						showCancel: false,
					});
					return false;
				}
				return true;
			},
			//获取短息验证码
			getverification() {
				this.codebr = true
				let params = {
					// mobileCode: this.balancePayment.captchaCode,
					// captchaId: this.codeid,
					ticket: this.codeTicket,
					mobile: this.accountInfoData.mobile
				}
				//手机号取不到就取userNo
				if (!params.mobile) {
					params.userNo = this.accountInfoData.userNo
				}
				this.$request
					.post(this.$interfaces.sendaAccountSms, {
						data: params,
					}).then(res => {
						if (res.code === 200) {
							this.timer = setInterval(() => {
								if (this.count > 0 && this.count <= 60) {
									this.count--;
								} else {
									this.codebr = false;
									clearInterval(this.timer);
									this.timer = null;
									this.count = 60;
								}
							}, 1000);
							console.log(res.data, '11111')
						} else {
							this.codebr = false
							let that = this
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
								// success: function(res) {
								// 	if (res.confirm) {
								// 		that.getCaptcha()
								// 	}
								// }
							});

						}
					}).catch((error) => {
						this.codebr = false
						uni.showModal({
							title: "提示",
							content: "获取失败",
							showCancel: false,
						});
					});
			},
			//获取互联网账户信息
			getAccountInfo() {
				let params = {
					userNo: getLoginUserInfo().userNo,
				};
				this.isLoading = true
				this.$request.post(this.$interfaces.getAccountInfo, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.isLoading = false
						this.accountInfoData = res.data
						if (this.accountInfoData.availableAmount && this.accountInfoData.availableAmount !== '0') {
							this.formData.recharge_type = "********"
							this.getCaptcha()
							this.pay_type_index = 1
						}
						if (res.data.status === "UNACTIVE") {
							this.isActivition();
						} else if (res.data.status === "ACTIVE") {
							this.vehicleBizSearch()
						}
					} else {
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
				})

			},
			isActivition() {
				let _self = this;
				uni.showModal({
					title: '提示',
					content: '您当前账户未激活，是否激活该账户？激活后可使用账户余额支付',
					success: function(res) {
						if (res.confirm) {
							_self.activitionAccount();
						}
					}
				});
			},
			activitionAccount() {
				let params = {
					userNo: getLoginUserInfo().userNo,
				};
				this.$request.post(this.$interfaces.activitionAccount, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.getAccountInfo()
						// uni.showModal({
						// 	title: "提示",
						// 	content: '激活账户成功',
						// 	showCancel: false,
						// });
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			// 根据卡号查询用户信息
			vehicleBizSearch() {
				console.log('ssssssssssss');
				let data = {
					routePath: this.$interfaces.vehicleBizSearch.method,
					bizContent: {
						cpu_card_id: this.formData.card_no
					}
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;
						if (res.code == 200) {
							if (res.data && res.data.length) {
								this.vehicleInfo = res.data[0]
								this.hideFirstName = (res.data[0].customer_name).substring(1)
								this.loadCardAmount()
							}

						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch(error => {
						this.isLoading = false;
					});
			},

			//查询账户余额信息
			loadCardAmount(val) {
				let data = {
					routePath: this.$interfaces.loadCardAmount.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id
					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						this.card_money = res.data.load_money //卡内未圈存金额
						this.cardAccount_money = res.data.card_money //卡账金额
						this.lock_money = res.data.lock_money //异常被锁金额
						let card_return_loading = res.data.card_return_loading
						//判断是否存在圈存异常锁定
						let lockFlag = float.add(this.lock_money, card_return_loading) > 0
						if ((this.businessType != 'cancel' && float.add(this.cardAccount_money,
									this.card_money) >
								0 && !val) || lockFlag) {
							uni.showModal({
								title: "提示",
								content: lockFlag ? `您的账户有异常被锁金额，请先前往圈存页面进行圈存异常处理` :
									`您的账户有待圈存金额，请先将余额圈存至卡片内`,
								success: (res) => {
									if (res.confirm) {
										uni.redirectTo({
											url: '/pagesB/loadBusiness/loadType'
										})
									} else {
										uni.reLaunch({
											url: '/pagesB/rechargeBusiness/selectVehicle/index'
										})
									}
								}

							});
							return
						} else if (float.add(res.data.lock_money,
								res.data.card_return_loading) >
							0) {
							//判断是否存在圈存异常被锁定金额

						}
						if (float.add(this.cardAccount_money, this.card_money) < 0 && !val) {
							uni.showModal({
								title: "提示",
								content: `您的账户有欠费，请先将补缴欠费`,
							});
							return
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {

				})
			},
			//输入框输入事件，匹配对应的键值对
			handleInput(type, event) {
				if (type == 'otherMoney') {
					this.formData.money = event.target.value
				} else {
					this.formData[type] = event.target.value
				}
			},
			validate() {

				if (this.vehicleInfo.card_type != '22') {
					uni.showModal({
						title: "提示",
						content: "该卡不是储值卡，无法储值卡充值",
						showCancel: false,
					});
					return false;
				}
				if (!this.formData.money) {
					uni.showModal({
						title: "提示",
						content: "请输入充值金额",
						showCancel: false,
					});
					return false;
				}
				if (!twoDecimal(this.formData.money)) {
					uni.showModal({
						title: "提示",
						content: "充值金额必须大于零并最多保留小数点后两位！",
						showCancel: false,
					});
					return false;
				}
				// if (float.mul(this.formData.money, 1) > 10000) {
				// 	uni.showModal({
				// 		title: "提示",
				// 		content: "单笔充值金额不得大于1万！",
				// 		showCancel: false,
				// 	});
				// 	return false;
				// }

				return true;
			},
			goPay() {
				if (!this.validate()) return
				if (this.formData.recharge_type == '********' && this.showMoney) {
					uni.showModal({
						title: "提示",
						content: "余额不足，请选择其他支付方式",
						showCancel: false,
					});
					return
				}
				if (!this.balancePayment.mobileCode && this.formData.recharge_type == '********') {
					uni.showModal({
						title: "提示",
						content: "请输入短信验证码",
						showCancel: false,
					});
					return
				}

				this.dialogVisible = true;
			},
			hideCheckDialogVisible() {
				this.checkDialogVisible = false
				this.isFocus = false
				this.checkFirstName = ''
			},
			onPayHandle() {
				if (this.formData.recharge_type == '********') {
					this.loadRecharge();
					return
				}
				this.balanceRecharge()
			},
			//余额支付
			balanceRecharge() {
				let params = {
					user_no: this.formData.agent_user,
					recharge_type: this.formData.recharge_type,
					card_no: this.formData.card_no,
					source: this.formData.source,
					money: this.formData.money,
					mobile: this.accountInfoData.mobile,
					mobile_code: this.balancePayment.mobileCode
				}
				params.money = float.mul(params.money, 100)
				this.isLoading = true;
				this.dialogVisible = false;
				let data = {
					routePath: this.$interfaces.balancePay.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						this.balancePayment.mobileCode = ''
						this.balancePayment.captchaCode = ''
						this.otherMoney = ''
						this.amountMoneyIndex = null
						let statusVal = {
							1: "充值中",
							2: "充值成功",
							3: "充值失败",
						};
						let msg = statusVal[res.data.status]
						this.rechargeStatus = msg
						this.jumpDialogVisible = true

						//线上注销多加一个判断
						if (this.businessType != 'cancel') {
							this.countdowner = setTimeout(() => {
								uni.reLaunch({
									url: '/pagesB/loadBusiness/loadType'
								})
								this.jumpDialogVisible = false
								clearTimeout(this.countdowner)
							}, 3000)
						}
						// uni.showModal({
						// 	title: "提示",
						// 	content: msg + '！3秒后将自动跳转圈存操作界面，请勿退出(温馨提示:充值到帐可能会有延迟)',
						// 	confirmText: '前往圈存',
						// 	success: function(res) {
						// 		if (res.confirm) {
						// 			clearTimeout(time)
						// 			uni.redirectTo({
						// 				url: '/pagesB/loadBusiness/loadType'
						// 			})
						// 		} else {
						// 			uni.redirectTo({
						// 				url: '/pagesB/rechargeBusiness/selectVehicle/index'
						// 			})
						// 		}
						// 	}
						// });
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						this.$logger.info('储值卡余额支付请求参数：', JSON.stringify(params))
						this.$logger.error('储值卡余额支付返回结果：', JSON.stringify(res))
					}
					this.loadCardAmount('noMsg');
					console.log(res, '--------');
				})
			},
			//充值
			loadRecharge() {
				let _self = this;
				this.isLoading = true;
				let params = JSON.parse(JSON.stringify(this.formData))
				this.dialogVisible = false;
				params.money = float.mul(params.money, 100)
				let data = {
					routePath: this.$interfaces.loadRecharge.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let data = res.data;
						let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};
						//拉起微信支付
						wx.requestPayment({
							...payMessage,
							"success": function(res) {
								console.log(res);
								_self.isLoading = true;
								//异步调用，确保能查到状态
								setTimeout(() => {
									_self.loadRechargeQuery(data);
								}, 6000)
								/* uni.showModal({
									title: '提示',
									content: '是否完成支付',
									confirmText: '支付完成',
									success: function(res) {
										if (res.confirm) {
											
											
			
										}
									}
								}); */

							},
							"fail": function(res) {
								// let msg = res.errMsg || ''
								// if (!msg) return
								// uni.showModal({
								// 	title: '提示',
								// 	content: msg
								// })
							},
							"complete": function(res) {}
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						this.$logger.info('储值卡充值请求参数：', JSON.stringify(params))
						this.$logger.error('储值卡充值返回结果：', JSON.stringify(res))
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//查询支付状态
			loadRechargeQuery(data) {
				let params = {
					routePath: this.$interfaces.loadRechargeQuery.method,
					bizContent: {
						order_Id: data.order_id
					}
				}

				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {

					let status = res.data.status;
					let statusVal = {
						1: "充值中",
						2: "充值成功",
						3: "充值失败",
					};
					let msg = statusVal[status] || "充值失败";
					this.isLoading = false;
					this.otherMoney = ''
					this.amountMoneyIndex = null
					this.rechargeStatus = msg
					this.jumpDialogVisible = true
					this.countdowner = setTimeout(() => {
						uni.reLaunch({
							url: '/pagesB/loadBusiness/loadType'
						})
						this.jumpDialogVisible = false
						clearTimeout(this.countdowner)
					}, 3000)
					// uni.showModal({
					// 	title: "提示",
					// 	content: msg + '！3秒后将自动跳转圈存操作界面，请勿退出(温馨提示:充值到帐可能会有延迟)',
					// 	confirmText: '确定',
					// 	success: function(res) {
					// 		if (res.confirm) {
					// 			clearTimeout(time)
					// 			uni.redirectTo({
					// 				url: '/pagesB/loadBusiness/loadType'
					// 			})
					// 		} else {
					// 			uni.reLaunch({
					// 				url: '/pagesB/rechargeBusiness/selectVehicle/index'
					// 			})
					// 		}
					// 	}
					// });
					this.formData.money = ''
					this.loadCardAmount('noMsg');
				})
			},
		},

	}
</script>

<style lang='scss'>
	.sellPay-Info {
		background-color: #FFFFFF;

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}

		.amount-box {

			padding: 20rpx;

			.amount-item {
				margin: 10upx 20upx;
				width: 27%;
				height: 110upx;
				background-color: #f8f9fe;
				border-radius: 10px;
				position: relative;

				.amount-text {
					line-height: 100upx;
					color: #1978ec;
					font-size: 34upx;
					font-weight: bold;
				}

				.amount-item-label {
					color: #1978ec;
					font-size: 26upx;
				}


			}
		}
	}

	.load_desc {
		margin: 60rpx 36rpx;

		.desc_title {
			font-size: 30rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #555555;
			margin-bottom: 20rpx;
		}

		.desc_text {
			text-align: left;
			line-height: 50rpx;
			text-indent: 2em;
			padding-bottom: 10rpx;
			font-size: 28rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #aaaaaa;
		}
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.code-img {
		width: 210upx;
		height: 90upx;
		margin-right: 10upx;
	}

	.codebtn {
		background: #0066E9;
		min-width: 210upx;
		font-size: 24upx;
		height: 70upx;
		margin-right: 10upx;
	}

	.codebtn text {
		display: inline-block;
		color: #fff;
		line-height: 70upx;
	}

	.available-money {
		padding: 6rpx 0 6rpx 30rpx;

		.available-money-item {
			color: #adadad;
			font-size: 26rpx;
		}

	}

	.weui-label {
		width: 280rpx !important;
	}

	.weui-cells__title {
		font-size: 30rpx;
		font-weight: bold;
	}

	.weui-cell {
		padding: 20rpx 30rpx;
	}

	.jump-dialog {
		font-size: 32rpx;
		padding: 30rpx;

		.jump-tips {
			font-size: 26rpx;
			color: #969696;
			margin-top: 30rpx;
		}
	}
</style>