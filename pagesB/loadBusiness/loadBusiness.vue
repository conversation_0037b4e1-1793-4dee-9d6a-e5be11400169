<template>
	<view class="sellPay-container">
		<view class="sellPay-Info">
			<view class="c-title">卡片信息</view>
			<form>
				<view class="cu-form-group">
					<view class="title">用户名称:</view>
					<input placeholder="用户名称" :value="customerInfo.customer_name" name="a" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title">车牌号码:</view>
					<input placeholder="车牌号码" :value="vehicleInfo.vehicle_code+' ['+vehicleColorStr+'] '" name="a"
						disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title">ETC卡号:</view>
					<input placeholder="ETC卡号" :value="vehicleInfo.cpu_card_id" name="a" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title">用户编号:</view>
					<input placeholder="用户编号" :value="customerInfo.customer_id" name="a" disabled></input>

				</view>
				<view class="cu-form-group">
					<view class="title">车辆类型:</view>
					<input placeholder="车辆类型" :value="vehicleType+'车'" name="a" disabled></input>
				</view>
			</form>
		</view>

		<view class="sellPay-Info">
			<view class="c-title">储值资金信息</view>
			<view class="cu-form-group">
				<view class="title">卡账金额:</view>
				<view>{{ moneyFilter(cardAccount_money) + ' 元' }}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">卡内未圈存金额:</view>
				<view>{{moneyFilter(card_money)+' 元'}}</view>
			</view>
			<!-- <view class="cu-form-group">
				<view class="title">卡内金额:</view>
				<view>{{moneyFilter(balance_money)+' 元'}}</view>
			</view> -->
			<view class="cu-form-group">
				<view class="title">圈存异常被锁金额:</view>
				<view>{{moneyFilter(abnormal_money)+' 元'}}</view>
			</view>

		</view>

		<view class="bottom-card">
			<view class="bottom-card-title">
				相关业务
			</view>
			<view class="nav-icon-index">
				<!-- <view class="nav-icon" @click="loadBusinessHandle('recharge')">
					<view>
						<image src="../static/loadBusiness/recharge.png" class="nav-icon-image" mode="monthlyBillt">
						</image>
					</view>
					<view class="nav-icon-text">储值卡充值</view>
				</view> -->
				<view class="nav-icon" @click="loadBusinessHandle('load')">
					<view>
						<image src="../static/loadBusiness/load.png" class="nav-icon-image" mode="monthlyBillt">
						</image>
					</view>
					<view class="nav-icon-text">圈存</view>
				</view>

				<!-- <view class="nav-icon" @click="loadBusinessHandle('rechargeHis')">
					<view>
						<image src="../static/loadBusiness/rechargeHis.png" class="nav-icon-image" mode="monthlyBillt">
						</image>
					</view>
					<view class="nav-icon-text">充值记录</view>
				</view> -->
				<view class="nav-icon" @click="loadBusinessHandle('loadHis')">
					<view>
						<image src="../static/loadBusiness/loadHis.png" class="nav-icon-image" mode="monthlyBillt">
						</image>
					</view>
					<view class="nav-icon-text">圈存记录</view>
				</view>
				<view class="nav-icon" @click="loadBusinessHandle('abormal')">
					<view>
						<image src="../static/loadBusiness/abormal.png" class="nav-icon-image" mode="monthlyBillt">
						</image>
					</view>
					<view class="nav-icon-text">异常处理</view>
				</view>
			</view>
		</view>

		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,
		getEtcVehicle,
	} from "@/common/storageUtil.js";
	import {
		mapGetters,
		mapActions
	} from 'vuex';
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
	} from "@/common/method/filter.js";
	import * as nfcSdk from "@/pagesB/js_sdk/NFCjs/NFCSdk.js";
	import {
		facapi
	} from "@/common/bluetooth/facUtil.js";
	import bleapi from "@/common/bluetooth/bleUtil.js";
	import tLoading from "@/components/common/t-loading.vue";
	import {
		Base64
	} from "@/js_sdk/js-base64/base64.min.js";
	import float from '@/common/method/float.js';
	const SERVICENAME_PREFIX = ["obu", "etc", "jl"];
	export default {
		name: "",
		components: {
			tLoading,
		},
		data() {
			return {
				card_money: "", //未圈存余额
				balance_money: "", //卡内余额
				abnormal_money: "", //异常被锁金额
				cardAccount_money: "", //卡账金额
				type: "",
				isLoading: false,
				cpu_id: "",
				isOwe: false, //判断是否欠费
				oweMoney: '', //欠费金额
			};
		},
		computed: {
			vehicleType() {
				return getVehicleType(this.vehicleInfo.vehicle_type);
			},
			vehicleColorStr() {
				return getVehicleColor(this.vehicleInfo.vehicle_color);
			},
			customerInfo() {
				return getEtcVehicle() || {};
			},
			vehicleInfo() {
				return getEtcVehicle() || {};
			},
		},
		onShow() {
			this.getAmountInfo();
		},
		onLoad(option) {
			console.log(option);
			if (option.type) {
				this.type = option.type || "";
			}
		},
		created() {
			console.log();
			this.getAmountInfo();
		},
		methods: {
			...mapActions([
				'setOWeData'
			]),
			getAmountInfo() {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.loadCardAmount.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id,
					},
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data,
					})
					.then((res) => {
						this.isLoading = false
						console.log(res, "查询账户余额");
						if (res.code == 200) {
							this.isLoading = false
							this.card_money = res.data.load_money;
							this.balance_money = res.data.wallet_money;
							this.abnormal_money = res.data.lock_money;
							this.cardAccount_money = res.data.card_money
							this.oweMoney = float.add(this.cardAccount_money, this.card_money)
							console.log(this.oweMoney, '============');
							if (this.oweMoney < 0) {
								this.setOWeData(Math.abs(this.oweMoney))
								this.isOwe = true
							} else {
								this.setOWeData('')
								this.isOwe = false
							}
						} else {
							this.isLoading = false
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
					});
			},
			loadBusinessHandle(val) {
				if (val == "recharge") {
					uni.navigateTo({
						url: "/pagesB/loadBusiness/recharge/recharge?type=" + this.type,
					});
				} else if (val == "rechargeHis") {
					uni.navigateTo({
						url: "/pagesB/loadBusiness/recharge/rechargeRecord",
					});
				} else if (val == "load") {
					if (this.type == "bluetooth") {
						uni.navigateTo({
							url: "/pagesB/loadBusiness/load/toLoadByCard",
						});
					} else {
						uni.navigateTo({
							url: "/pagesB/loadBusiness/load/toLoadByNFC",
						});
					}
				} else if (val == "loadHis") {
					uni.navigateTo({
						url: "/pagesB/loadBusiness/load/loadRecord",
					});
				} else if (val == "abormal") {
					if (this.abnormal_money <= 0) {
						uni.showModal({
							title: "提示",
							content: "没有圈存异常记录，不需要进行异常圈存处理！",
							showCancel: false,
						});
						return;
					}
					this.loadAbnormal();
				}
			},
			//圈存异常处理
			loadAbnormal() {
				console.log(this.type);
				if (this.type == "bluetooth") {
					this.loadAbnormalByCard();
				} else {
					this.loadAbnormalByNFC();
				}
			},
			//蓝牙圈存异常处理
			loadAbnormalByCard() {
				this.isLoading = true;
				bleapi.CloseBle((obj) => {
					this.scanDevice((result) => {
						this.isConnect = result
						if (result) {
							setTimeout(() => {
								this.connectedCallback(this.isConnect)
							}, 1000)
						}
					})
				})

			},
			//NFC圈存异常处理
			loadAbnormalByNFC() {
				this.isLoading = true;
				nfcSdk.GetTradeList((result) => {
					console.log("最新一条记录", result);
					if (result.code == 0) {
						this.sendAbnormalFile(result);
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: "获取异常流水文件失败",
							showCancel: false,
						});
					}
				});
			},
			// 开启扫描蓝牙设备
			scanDevice(callback) {
				bleapi.ScanDevice(SERVICENAME_PREFIX, (devResult) => {
					console.log("ScanDevice", devResult);
					if (devResult.code != 0) {
						//搜索设备失败
						this.showModal({
							title: "错误",
							devResult: devResult,
							content: devResult.err_msg,
							showMyContent: true,
						});
					} else {
						console.log(
							"搜索到设备:" + devResult + " " + devResult.data.device_name
						);
						facapi.ConnectDevice(
							devResult.data,
							function(onDisconnect) {
								console.log("连接回调：", onDisconnect);
							},
							function(result) {
								console.log(result, "result");
								bleapi.StopScanDevice(function(code) {
									console.log("返回数据", code);
								});
								if (result.code == 0) {
									console.log("连接标签设备成功");
									callback(true);
								} else {
									this.showModal({
										title: "错误",
										devResult: result,
										content: "设备连接失败，请将手机靠近设备后重试。",
										showMyContent: true,
									});
									callback(false);
								}
							}
						);
					}
				});
			},
			// 连接成功回调
			async connectedCallback(isConnected) {
				console.log("连接成功回调");
				if (isConnected) {
					facapi.OpenCard((devResult) => {
						console.log(devResult, "999999999999");
						if (devResult.code == 0) {
							facapi.GetCardNo((devResult) => {
								console.log(devResult, "777777777777");
								if (devResult.code == 0) {
									this.cpu_id = devResult.data;
									this.getBalance(devResult);
								} else {
									this.showModal({
										title: "错误",
										devResult: devResult,
										content: "获取卡号失败：" +
											devResult.code +
											":" +
											devResult.err_msg +
											(devResult.msg ? ":" + devResult.msg : ""),
									});
									this.disConnect();
								}
							});
						} else {
							this.showModal({
								title: "错误",
								devResult: devResult,
								content: "打开卡失败：" +
									devResult.code +
									":" +
									devResult.err_msg +
									(devResult.msg ? ":" + devResult.msg : ""),
							});
							this.disConnect();
						}
					});
				}
			},
			getBalance(devResult) {
				facapi.GetTradeList((result) => {
					console.log("最新一条记录", result);
					if (result.code == 0) {
						this.sendAbnormalFile(result);
					} else {
						this.isLoading = false;
					}
				});
			},
			//异常处理发送文件
			sendAbnormalFile(res) {
				let params = {
					customer_id: this.customerInfo.customer_id,
					cpu_card_id: this.vehicleInfo.cpu_card_id,
					vehicle_code: this.vehicleInfo.vehicle_code,
					vehicle_color: this.vehicleInfo.vehicle_color,
					file_name: "file",
					load_from: "3",
					// files:Base64.btoa(res.data)
					files: res.data,
				};

				this.$request
					.post(this.$interfaces.abnormal, {
						data: params,
					})
					.then((res) => {
						this.isLoading = false;
						console.log(res, "异常处理结果");
						if (res.code == 200) {
							this.getAmountInfo();
							if (res.data.strResult == '1') {
								this.isLoading = false;
								uni.showModal({
									title: "提示",
									content: "异常处理成功",
									showCancel: false,
								});
							} else {
								this.isLoading = false;
								uni.showModal({
									title: "提示",
									content: '异常处理失败',
									showCancel: false,
								});
							}

						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
					});
			},
			disConnect() {
				facapi.facSdk.DisconnectDevice(function(code) {
					console.log("关闭连接结果", code);
					this.isLoading = false;
					this.isBtnLoader = false;
				});
				// 完成后关闭蓝牙模块
				bleapi.CloseBle((obj) => {
					console.log(obj);
				});
			},
			closeBle() {
				// 关闭蓝牙模块，防止中途断开，连接不上
				bleapi.CloseBle((obj) => {
					console.log(obj);
				});
			},
			// 显示弹框
			showModal(data) {
				//隐藏loading
				this.isLoading = false;
				this.isBtnLoader = false;
				console.log(data.content, "sdk报错");

				//显示弹框
				let obj = {
					...data,
				};
				obj = data.showMyContent ?
					obj : {
						...data,
						content: "操作失败，请打开手机蓝牙，将手机靠近设备后重试。(" +
							data.devResult.code +
							":" +
							data.devResult.err_msg +
							")",
					};
				uni.showModal({
					...obj,
					showCancel: false,
					success: () => {},
				});
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
	};
</script>

<style lang='scss'>
	.sellPay-Info {
		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.bottom-card {
		top: 40upx;
		background-color: #fff;
		width: 100%;
		height: 402rpx;
		position: relative;

		.bottom-card-title {
			font-size: 32rpx;
			color: #666;
			padding: 24rpx 0 24rpx 48rpx;
			font-weight: bold;

			&:before {
				content: "|";
				font-weight: bold;
				color: #0066E9;
				position: relative;
				right: 10rpx;
				top: -2rpx;
			}
		}

		.card-list {
			display: flex;
			flex-wrap: wrap;
		}
	}

	.nav-icon {
		border-radius: 5px;
		color: #666;
		font-size: 28rpx;
		height: 150rpx;
		width: 170rpx;
		text-align: center;
	}

	.nav-icon-index {
		color: #666;
		font-size: 28rpx;
		height: 120rpx;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-center;
		align-items: center;
		// justify-content: space-around;
		padding: 0 20rpx;
		margin-bottom: 30rpx;
	}

	.nav-icon-image {
		margin-top: 20rpx;
		width: 60rpx;
		height: 60rpx;
		display: inline-block;
	}

	.nav-icon-text {
		text-align: center;
		color: #555555;
	}

	.group_money {
		margin-right: 350upx;
	}
</style>