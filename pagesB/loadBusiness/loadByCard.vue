<template>
	<view class="loadBusiness">
		<view class="card">
			<view class="card-box">
				<view class="bd">
					<view class="title" v-if='vehicle && vehicle.vehicle_code'>
						{{vehicle.vehicle_code}}[{{getVehicleColor(vehicle.vehicle_color+'')}}]
					</view>
					<view class="des" v-if='vehicle && vehicle.customer_name'>
						{{vehicle.customer_name}}
					</view>
					<view class="des" v-if="vehicle && vehicle.customer_id">
						{{vehicle.customer_id}}
					</view>
				</view>
			</view>
		</view>
		<view class="weui-form" v-if="vehicle&& vehicle.vehicle_code">
			<view class="weui-cells__title">
				当前车辆信息
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{vehicle.cpu_card_id}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡片状态</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{getCpuStatus(vehicle.cpu_status)}}
						</view>
					</view>

				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡片类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{gxCardTypeFilter(vehicle.gx_card_type)}}
						</view>
					</view>

				</view>
			</view>
		</view>

		<view class="weui-form" v-if="vehicle&& vehicle.vehicle_code&&vehicle.gx_card_type=='0'">
			<view class="weui-cells__title">
				储值资金信息
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡账金额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{ moneyFilter(moneyData.card_money)}}元
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡内未圈存金额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{ moneyFilter(moneyData.load_money)}}元
						</view>
					</view>

				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">圈存异常被锁金额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{moneyFilter(moneyData.lock_money)}}元
						</view>
					</view>

				</view>
			</view>
		</view>

		<view class="weui-form"
			v-if="vehicle&& vehicle.vehicle_code&&vehicle.gx_card_type=='5'||vehicle.gx_card_type=='8'">
			<view class="weui-cells__title">
				账户余额
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">最低预存金标准值</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{ moneyFilter(moneyData.black_line)}}元
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">账户可用余额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{ moneyFilter(moneyData.trueMoney)}}元
						</view>
					</view>

				</view>

			</view>
		</view>

		<view class="sellPay-Info" v-if="isRechargeFlag||vehicle&&vehicle.gx_card_type&&vehicle.gx_card_type!='0'">
			<view class="c-title">请选择充值金额</view>
			<view class="amount-box g-flex g-flex-wrap ">
				<view v-for="(item,index) in amountList" :key="index" class="amount-item"
					@click="selectAmout(item,index)"
					:style="(amountMoneyIndex!=null&&amountMoneyIndex==index)?'border:1px solid  #1978ec':''">
					<view class="g-flex g-flex-center g-flex-align-center">
						<view class="amount-text">
							{{item.label}}
						</view>
						<view class="amount-item-label">元</view>
					</view>
				</view>
				<view class="amount-item g-flex g-flex-align-center g-flex-justify" style="width: 60%;padding: 0 20upx;"
					:style="isAnyMount?'border:1px solid #1978ec':''">
					<input type="digit" @focus="clickInput" class="amount-custom-input" placeholder="其他支付金额" name="b"
						v-model="otherMoney" @input="(e)=> handleInput('otherMoney',e)"></input>
					<text style="margin-right: 10upx;">元</text>
				</view>
			</view>

		</view>
		<view class="sellPay-Info" v-if="isRechargeFlag||vehicle&&vehicle.gx_card_type&&vehicle.gx_card_type!='0'">
			<view class="c-title">充值信息</view>
			<form>
				<view class="cu-form-group">
					<view class="title">支付方式:</view>
					<picker style="width:100%;" @change="bindPayTypeChange" :range="payTypePartOptions" disabled
						range-key="label">
						<view class="weui-picker-value">{{ payTypePartOptions[pay_type_index].label }}</view>
					</picker>
					<text class="cuIcon-right"></text>
				</view>

			</form>
		</view>
		<view class="tips-bottom" v-if="Object.keys(vehicle).length==0">
			温馨提示：请停车熄火关闭车载蓝牙或其他设备，以确保手机连接设备蓝牙时不受干扰。
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="goNextProbingObu()">
						读取卡片
					</button>
				</view>
				<view class="btn-item">
					<button v-if="isRechargeFlag||vehicle&&vehicle.gx_card_type&&vehicle.gx_card_type!='0'"
						class="weui-btn weui-btn_primary" @click="recharge">充值</button>
					<button v-else-if="isLoadFlag" class="weui-btn weui-btn_primary"
						:class="{ 'weui-btn_disabled': !isLoad }" :disabled="!isLoad" @click="load">圈存</button>
					<button v-else-if="lockFlag" class="weui-btn weui-btn_primary" @click="exceptionHandle">异常处理</button>
					<button v-else class="weui-btn weui-btn_primary weui-btn_disabled">充值/圈存</button>
				</view>

			</view>
		</view>
		<TModal :showModal='dialogVisible' modalTitle='请仔细核对充值信息' :showCancelFlag='true'
			@cancelModal='dialogVisible=false' @okModal='confirmHandle' okText='去支付'>
			<form slot='content' class="sellPay-Info" v-if="dialogVisible">
				<view class="cu-form-group">
					<view class="title">充值账户</view>
					<view class="value">{{vehicle.customer_name}}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">充值车辆</view>
					<view class="value" style="font-size: 34rpx;font-weight: bold;">
						{{vehicle.vehicle_code}}【{{getVehicleColor(vehicle.vehicle_color)}}】
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">支付金额</view>
					<view class="value">{{formData.money}}元</view>
				</view>
			</form>
		</TModal>

		<TModal :showModal='checkDialogVisible' modalTitle='请确认车主信息' :showCancelFlag='true' @okModal='checked'
			@cancelModal='hideCheckDialogVisible' okText='确认'>
			<form slot='content' class="sellPay-Info">
				<view style="color: #fa5151;font-size: 28rpx;font-weight: bold;margin: 20rpx 0;">
					请填写车主中文姓名首字，以确认车主身份
				</view>
				<view class="checkname g-flex g-flex-align-center g-flex-center">
					<input style="border: 1px solid #000000;width: 70rpx;height: 70rpx;margin-right: 10rpx;"
						v-model="checkFirstName" :focus='isFocus'></input>
					<text>{{hideSecondName}}</text>

				</view>

			</form>
		</TModal>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue'
	import bleapi from '@/common/bluetooth/bleUtil.js'
	import vue from 'vue';
	import {
		facapi
	} from '@/common/bluetooth/facUtil.js'
	import {
		vehicleColorOptions,
		vehicleUserTypeOptions,
		cpuCardTypeOptions,
		plateColorToColorMap
	} from '@/common/systemConstant.js'
	import {
		amountList,
		payTypePartOptions
	} from '@/common/const/optionData.js'
	import {
		getVehicleColor,
		gxCardTypeFilter,
		getCpuStatus,
	} from '@/common/method/filter.js';
	import {
		twoDecimal,
		moneyFilter
	} from '@/common/util.js'
	import {
		getTokenId,
		getAccountId,
		setEtcVehicle,
		getLoginUserInfo,
		getOpenid,
		getRechargeType
	} from '@/common/storageUtil.js';
	import TModal from '@/components/t-modal/t-modal.vue'
	const SERVICENAME_PREFIX = vue.prototype.$serviceName;
	import float from '@/common/method/float.js'
	const pinCode = '************'
	const terminalNo = '************'
	var _that;
	import { storedLoadMixin } from './mixins/storedLoadMixin.js';
	export default {
		mixins: [storedLoadMixin], 
		components: {
			TButton,
			tLoading,
			TModal
		},
		data() {
			return {
				vehicleColorOptions,
				vehicleUserTypeOptions,
				cpuCardTypeOptions,
				payTypePartOptions,
				vehicle: {},
				isLoading: false,
				isLoad: false, // 是否可以圈存,
				isDisable: false,
				moneyData: {},
				scanDevice_cpu_card_id: "",
				trade_no: '',
				accept_id: '',
				balance: '',
				oweMoney: '', //欠费金额
				isOwe: false, //是否欠费
				card_balance: '', // 卡内余额
				trade_no_after: '', // 计数器
				issue_times: '', // 发行服务器时间
				issue_code: '',
				isRechargeFlag: false,
				amountList,
				amountMoneyIndex: null,
				otherMoney: '',
				isAnyMount: false,
				formData: {
					"recharge_type": "10000601", //充值值方式
					"card_no": "", //卡号
					"money": '', //金额
					"source": "3", //来源
					"auth_code": "",
					"open_id": "",
					"tradeType": 'CARD_LOAD', //场景
					"agent_user": '', //用户编号
				},
				pay_type_index: 0,
				dialogVisible: false,
				checkDialogVisible: false,
				checkFirstName: "",
				hideFirstName: '',
				isFocus: false,
				isLoadFlag: false,
				hideSecondName: '',
				device_no: '', //obu设备id
				isFirstOpenDevice: true,
				completeFlag: false,
				lockFlag: false, //异常处理

			}
		},
		onLoad() {

			this.closeBle()
		},
		created() {
			this.formData.agent_user = getLoginUserInfo().userNo;
			this.formData.open_id = getOpenid() || ''

			_that = this;
			setEtcVehicle({});
		},
		methods: {
			moneyFilter,
			getVehicleColor,
			gxCardTypeFilter,
			getCpuStatus,
			//选择金额
			selectAmout(item, index) {
				this.formData.money = ''
				this.otherMoney = ''
				this.amountMoneyIndex = index
				this.formData.money = item.value
				this.isAnyMount = false
			},
			//输入框聚焦事件
			clickInput() {
				this.amountMoneyIndex = null
				if (!this.isAnyMount) {
					this.formData.money = ''
				}
				this.isAnyMount = true
			},
			//输入框输入事件，匹配对应的键值对
			handleInput(type, event) {
				console.log(type)
				if (type == 'otherMoney') {
					this.formData.money = event.target.value
					console.log(this.formData.money);
				} else {
					this.formData[type] = event.target.value
				}
			},
			bindPayTypeChange(e) {
				this.pay_type_index = e.detail.value;
				this.formData.recharge_type = this.payTypePartOptions[e.detail.value].value || '';
				console.log(this.formData.recharge_type);
			},
			//点击充值调起信息确认弹框
			recharge() {
				if (!this.validate()) return
				this.checkDialogVisible = true
				console.log(this.checkDialogVisible);
			},
			//检验通过，调用充值接口
			confirmHandle() {
				this.loadRecharge()
			},
			hideCheckDialogVisible() {
				this.checkDialogVisible = false
				this.isFocus = false
				this.checkFirstName = ''
			},
			checked() {
				let fullname = this.checkFirstName + this.hideFirstName
				if (fullname !== this.vehicle.customer_name) {
					uni.showModal({
						title: "提示",
						content: "您输入的中文名首字和查询出的不符，请重试！",
						showCancel: false,
					});
					return

				}
				this.checkDialogVisible = false
				this.isFocus = false
				this.checkFirstName = ''
				this.dialogVisible = true;
			},
			//充值
			loadRecharge() {
				let _self = this;
				this.isLoading = true;
				this.formData.card_no = this.vehicle.cpu_card_id
				let params = JSON.parse(JSON.stringify(this.formData))
				this.dialogVisible = false;
				params.money = float.mul(params.money, 100)
				params.tradeType = this.vehicle.gx_card_type == '0' ? 'CARD_LOAD' : 'CARD_RECHARGE'
				let data = {
					routePath: this.$interfaces.loadRecharge.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let data = res.data;
						let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};
						//拉起微信支付
						wx.requestPayment({
							...payMessage,
							"success": function(res) {
								console.log(res);
								_self.isLoading = true;
								//异步调用，确保能查到状态
								setTimeout(() => {
									_self.loadRechargeQuery(data);
								}, 6000)
								/* uni.showModal({
									title: '提示',
									content: '是否完成支付',
									confirmText: '支付完成',
									success: function(res) {
										if (res.confirm) {
											
											
			
										}
									}
								}); */

							},
							"fail": function(res) {

							},
							"complete": function(res) {}
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//查询支付状态
			loadRechargeQuery(data) {
				let params = {
					routePath: this.$interfaces.loadRechargeQuery.method,
					bizContent: {
						order_Id: data.order_id
					}
				}

				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {

					let status = res.data.status;
					let statusVal = {
						1: "充值中",
						2: "充值成功",
						3: "充值失败",
					};
					let msg = statusVal[status] || "充值失败";
					this.isLoading = false;
					this.otherMoney = ''
					this.amountMoneyIndex = null
					if (this.vehicle.gx_card_type == '0') {
						uni.showModal({
							title: "提示",
							content: msg,
							confirmText: '前往圈存',
							cancelText: '返回',
							success: (res) => {
								if (res.confirm) {

								} else {
									uni.reLaunch({
										url: '/pages/home/<USER>/p-home'
									})
								}
							}

						});
					} else {
						uni.showModal({
							title: "提示",
							content: msg + '！是否查看充值记录(温馨提示:充值到帐可能会有延迟)',
							confirmText: '查看',
							cancelText: '返回',
							success: (res) => {
								if (res.confirm) {
									uni.redirectTo({
										url: '/pagesB/rechargeBusiness/rechargeList/p-rechargeList?cardNo=' +
											this.vehicle.cpu_card_id
									})
								} else {
									uni.reLaunch({
										url: "/pagesB/rechargeBusiness/selectVehicle/index"
									})
								}
							}

						});
					}

					this.formData.money = ''
					this.isRechargeFlag = false
					this.getAmountInfo();
				})
			},
			goLoadBusiness() {
				// setEtcVehicle(this.vehicle);
				// uni.navigateTo({
				// 	url: "/pagesB/loadBusiness/loadBusiness?type=bluetooth"
				// })
			},
			//根据读取设备卡号查询车辆信息
			getVehicleInfo() {
				this.isLoading = true;
				let data = {
					routePath: this.$interfaces.vehicleBizSearch.method,
					bizContent: {
						cpu_card_id: this.scanDevice_cpu_card_id
					}
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					}).then(res => {
						this.isLoading = false
						if (res.code == 200 && res.data && res.data.length) {
							this.vehicle = res.data[0]
							this.hideFirstName = (res.data[0].customer_name).substring(1)
							if (this.hideFirstName.length > 1) {
								this.hideSecondName = "*" + this.hideFirstName.substring(1, this.hideFirstName.length)
							} else {
								this.hideSecondName = this.hideFirstName
							}
							if (this.vehicle.gx_card_type == '0') {
								this.getAmountInfo(this.validateOwe)
							} else {
								uni.showModal({
									title: '提示',
									content: '您办理的ETC产品无需链接设备写卡圈存,使用"输入车牌充值"更为便捷,欢迎下次使用',
									showCancel: false,
									confirmText: '继续充值'
								});
								this.getAmountInfo()
							}
							this.isLoad = Boolean(this.vehicle.cpu_card_id && this.vehicle.card_type == 22 && this
								.vehicle.cpu_status == '1'); // 储值卡可以圈存
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					}).catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					})
			},
			//锁定金额异常处理
			exceptionHandle() {
				if (this.isDisable) return
				this.isLoading = true
				this.isDisable = true
				this.isBtnLoader = true
				this.isActive = false
				this.isConnect = false

				bleapi.CloseBle((obj) => {
					this.scanDevice((result) => {
						this.isConnect = result
						if (result) {
							setTimeout(() => {
								this.connectedCallback(this.isConnect, 'lock')
							}, 1000)
						}
					})
				})
			},
			validate() {
				if (!this.formData.money) {
					uni.showModal({
						title: "提示",
						content: "请输入充值金额",
						showCancel: false,
					});
					return false;
				}
				if (!twoDecimal(this.formData.money)) {
					uni.showModal({
						title: "提示",
						content: "充值金额必须大于零并最多保留小数点后两位！",
						showCancel: false,
					});
					return false;
				}
				// if (float.mul(this.formData.money, 1) > 10000) {
				// 	uni.showModal({
				// 		title: "提示",
				// 		content: "单笔充值金额不得大于1万！",
				// 		showCancel: false,
				// 	});
				// 	return false;
				// }

				return true;
			},
			validException() {
				this.isRechargeFlag = false
				this.isLoadFlag = false
				this.lockFlag = false
				// 根据用户的卡账余额及异常圈存被锁金额加总判断是否提示圈存
				let totalMoney = float.add(this.moneyData.card_return, this.moneyData.load_money)
				if (totalMoney > 0) {
					uni.showModal({
						title: '提示',
						content: `您的账户有待圈存金额${float.div(totalMoney,100)}元，请先将余额圈存至卡片内`,
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								this.isRechargeFlag = false
								this.lockFlag = false
								this.isLoadFlag = true
								this.load()
							} else {
								this.isRechargeFlag = false
								this.lockFlag = false
								this.isLoadFlag = true
							}
						}
					})
					return
				}
				//异常处理完成，成功提示
				if (totalMoney < 0) {
					uni.showModal({
						title: '提示',
						content: `锁定金额处理完成或圈存成功，已无待圈存金额`,
						showCancel: false,
					})
					this.isRechargeFlag = true
					this.lockFlag = false
					this.isLoadFlag = false
					return
				}
			},
			validateOwe() { //如果卡内余额和待圈存余额都为0，提示去充值
				this.isRechargeFlag = false
				this.isLoadFlag = false
				//圈存锁定金额异常处理
				let lockFlag = float.add(this.moneyData.lock_money, this.moneyData.card_return_loading) > 0
				if (lockFlag) {
					// this.isRechargeFlag = false
					// this.isLoadFlag = true
					uni.showModal({
						title: '提示',
						content: `您的账户有异常被锁定金额，请先处理`,
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								this.isRechargeFlag = false
								this.isLoadFlag = false
								this.lockFlag = true
								this.exceptionHandle()
							} else {
								this.isRechargeFlag = false
								this.isLoadFlag = false
								this.lockFlag = true
							}
						}
					})
					return
				}
				if (this.moneyData.load_money == 0 && this.moneyData.card_money == 0) {
					this.isRechargeFlag = true
					this.isLoadFlag = false
					this.isLoading = false
					this.isBtnLoader = false
					uni.showModal({
						title: '提示',
						content: '您的账户暂无待圈存金额，请先充值',
						showCancel: false,
					})
					return
				}
				if (float.add(this.moneyData.load_money, this.moneyData.card_money) < 0) {
					this.isRechargeFlag = true
					this.isLoadFlag = false
					uni.showModal({
						title: '提示',
						content: `您的账户有欠费，请先充值`,
						showCancel: false,
					})
					return
				}
				// 根据用户的卡账余额及异常圈存被锁金额加总判断是否提示圈存
				let totalMoney = float.add(this.moneyData.card_money, this.moneyData.load_money)
				this.isRechargeFlag = false
				this.isLoadFlag = true
				if (totalMoney > 0 && getRechargeType() == 'rechargeOther') {
					uni.showModal({
						title: '提示',
						content: `您的账户有待圈存金额${float.div(totalMoney,100)}元，请先将余额圈存至卡片内`,
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								this.isRechargeFlag = false
								this.isLoadFlag = true
								this.load()
							} else {
								this.isRechargeFlag = false
								this.isLoadFlag = true
							}
						}
					})
					return
				}

			},
			// 获取账户余额
			getAmountInfo(callback) {
				let data = {
					routePath: this.$interfaces.loadCardAmount.method,
					bizContent: {
						cpu_card_id: this.vehicle.cpu_card_id
					}
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log(res, '查询账户余额')
						if (res.code == 200) {
							this.moneyData = res.data
							this.moneyData.trueMoney = float.sub(this.moneyData.card_money, this.moneyData
								.black_line)
							this.isLoading = false
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
							this.isLoading = false
						}
						callback && callback()
					})
			},
			//读卡
			goNextProbingObu() {
				if (this.isDisable) return
				this.obuCardInfo = {};
				this.cpuCardInfo = {};
				this.isDisable = true
				this.isLoading = true
				bleapi.CloseBle((obj) => {
					this.scanDevice((result) => {
						this.isConnect = result
						// this.connectionStatus()
						if (result) {
							setTimeout(() => {
								//这里多穿了个type参数，为了区分是读卡还是圈存，在加一个异常处理类型
								this.connectedCallback(this.isConnect, 'read')
							}, 1000)
						}
					})
				})
			},

			connectionStatus() {


				bleapi.onBLEConnectionStateChange((res) => {
					console.log(res, '----res-----1111');
					if (res.connected) {
						this.isFirstOpenDevice = false
					}
					if (!res.connected && this.device_no == res.deviceId && !this.isFirstOpenDevice && !
						this
						.completeFlag) {
						this.closeLoading()
						this.isDisable = false
						//搜索设备失败
						uni.showModal({
							title: '提示',
							content: '设备蓝牙断开，请重新插拔卡开启设备蓝牙！',
							showCancel: false
						})

						this.disConnect()

					}
				})
			},

			// 圈存
			load() {
				if (this.isDisable) return
				this.isLoading = true
				this.isDisable = true
				this.isBtnLoader = true
				this.isActive = false
				this.isConnect = false
				console.log('this.moneyData.card_money===>>>',this.moneyData.card_money)
				if (this.moneyData.card_money > 0) {
					this.preAllocated()
					return
				}
				if (this.moneyData.card_money < 0) {
					this.disPre()
					return
				}

				bleapi.CloseBle((obj) => {
					this.scanDevice((result) => {
						this.isConnect = result
						if (result) {
							setTimeout(() => {
								this.connectedCallback(this.isConnect, 'load')
							}, 1000)
						}
					})
				})
			},
			// 1.开始扫描蓝牙设备
			scanDevice(callback) {
				bleapi.ScanDevice(SERVICENAME_PREFIX, (devResult) => {
					console.log('ScanDevice', devResult)
					if (devResult.code != 0) {
						console.log('搜索失败', devResult)

						_that.closeLoading()
						_that.isDisable = false
						//搜索设备失败
						_that.showModal({
							title: '错误',
							devResult: devResult,
							content: devResult.err_msg,
							showMyContent: true
						})
						// _that.disConnect()
					} else {
						console.log(
							'搜索到设备11:' + devResult + ' ' + devResult.data.device_name
						)
						_that.device_no = devResult.data.device_no
						facapi.ConnectDevice(
							devResult.data,
							function(onDisconnect) {
								console.log('连接回调：', onDisconnect)
							},
							function(result) {
								console.log(result, 'result')
								bleapi.StopScanDevice(function(code) {
									console.log('返回数据', code)
								})
								if (result.code == 0) {
									console.log('连接标签设备成功')
									callback(true)
								} else {
									_that.closeLoading()
									_that.isDisable = false
									_that.showModal({
										title: '错误',
										devResult: result,
										content: '设备连接失败，请将手机靠近设备后重试。',
										showMyContent: true
									})
									_that.device_no = ''
									_that.disConnect()
									_that.$logger.error('蓝牙连失败：', JSON.stringify(result))
									callback(false)
								}
							}
						)
					}
				})
			},
			/* 
			 储值卡圈存流程
			 1、连接设备
			 2、读取卡信息
			 3、读取卡内余额
			 4、调用初始化圈存SDK
			 5、读取卡内余额和卡内圈存计数器
			 6、圈存确认
			 */
			// 1、连接设备
			async connectedCallback(isConnected, type) {
				console.log('连接成功回调')
				if (isConnected) {
					facapi.OpenCard((devResult) => {
						console.log(devResult, '999999999999')
						if (devResult.code == 0) {
							facapi.GetCardFile15((devResult) => {
								console.log(devResult, '777777777777')
								if (devResult.code == 0) {
									_that.cpu0015Info = devResult.data;
									_that.scanDevice_cpu_card_id = devResult.data.cardNo
									//类型read是读卡，不进行后续的圈存业务
									if (type == 'read') {
										_that.getVehicleInfo()
										_that.isDisable = false
										_that.isLoading = false
										return
									}
									_that.getBalance(devResult, type)
								} else {
									_that.showModal({
										title: '错误',
										devResult: devResult,
										content: '获取卡号失败：' +
											devResult.code +
											':' +
											devResult.err_msg +
											(devResult.msg ? ':' + devResult.msg : '')
									})
									_that.disConnect()
								}
							})
						} else {
							_that.showModal({
								title: '错误',
								devResult: devResult,
								content: '打开卡失败：' +
									devResult.code +
									':' +
									devResult.err_msg +
									(devResult.msg ? ':' + devResult.msg : '')
							})
							_that.disConnect()
						}
					})
				}
			},
			// 2.获取卡内余额
			getBalance(devResult, type) {
				if (devResult.code == 0) {
					facapi.GetBalance((devResult) => {
						//获取余额成功
						console.log('获取余额成功-devResult', devResult)
						if (devResult.code == 0) {
							if (devResult.data && devResult.data.length == 8) {
								let balance = parseInt(devResult.data, 16)
								console.log('圈存余额balance', balance)
								this.balance = balance
								//新增type区分异常处理
								if (type == 'lock') {
									_that.GetTradeNoAndMac1(devResult)
									return
								}
								_that.initLoadHandle(devResult)
							} else {
								_that.showModal({
									title: '错误',
									devResult: {
										code: '11013',
										err_msg: '获取卡余额失败：长度不符'
									},
									content: '获取卡余额失败：长度不符'
								})
								_that.cpuComplete({
									code: '11013',
									err_msg: '获取卡余额失败：长度不符'
								})
							}
						} else {
							console.log(devResult, 'devResultdevResultdevResult')
							_that.showModal({
								title: '错误',
								devResult: devResult,
								content: '获取卡额失败：' +
									devResult.code +
									':' +
									devResult.err_msg +
									(devResult.msg ? ':' + devResult.msg : '')
							})
							_that.disConnect()
						}
					})
				}
			},
			initLoadHandle(devResult){
				this._baseInitLoad(devResult,this.loadComplete)
				//this._baseInitLoad(devResult,()=>{})
			},
			//6.圈存确认
			async loadComplete() {
				let data = {
					routePath: this.$interfaces.loadComplete.method,
					bizContent: {
						cpu_card_id: this.vehicle.cpu_card_id,
						vehicle_code: this.vehicle.vehicle_code,
						vehicle_color: this.vehicle.vehicle_color,
						customer_id: this.vehicle.customer_id,
						accept_id: this.accept_id,
						load_money: this.moneyData.load_money,
						tac: '',
						card_balance: this.card_balance,
						term_id: terminalNo,
						trade_no_before: this.trade_no,
						trade_no_after: this.trade_no_after,
						load_from: '3'
					}
				}
				console.log(data, '圈存确认入参')
				await this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.completeFlag = true
						this.disConnect()
						this.isLoading = false
						if (res.code != 200) {
							uni.showModal({
								title: '提示',
								content: '圈存上报失败' + res.msg + '【' + res.code + '】',
								showCancel: false
							})
						} else {
							uni.showModal({
								title: '提示',
								content: '圈存成功!是否查看充值/圈存记录',
								confirmText: '查看',
								cancelText: '返回',
								success: (res) => {
									if (res.confirm) {
										uni.redirectTo({
											url: '/pagesB/loadBusiness/load/loadRecord?cardNo=' +
												this.vehicle.cpu_card_id
										})
									} else {
										uni.reLaunch({
											url: "/pages/home/<USER>/p-home"
										})
									}
								}
							})
						}
					})
				this.getAmountInfo && this.getAmountInfo()
			},
			
			GetTradeNoAndMac1(devResult) {
				this.issue_times = this.formatDate(new Date())
				let money = 0 //异常处理默认金额0
				if (devResult.code == 0) {
					console.log(
						'获取mac1信息',
						this.issue_times,
						pinCode,
						money,
						terminalNo,
						devResult
					)
			
					this.connectionStatus()
					facapi.GetTradeNoAndMac1(
						pinCode,
						money,
						this.issue_times,
						terminalNo,
						(rand, trade_no, mac1, balance, callback) => {
							console.log('initLoad:getMac', rand, trade_no, mac1, balance)
							this.trade_no = trade_no
							_that.LoadCheck(rand, trade_no, balance, mac1, (result) => {
								callback(result)
							})
						},
						(nextDevResult) => {
							//获取mac1信息成功
							console.log('初始化圈存结果', nextDevResult)
							this.isLoading = false
							if (nextDevResult.code == 0) {
								// _that.getCurBalanceAndTradeNo()
								//圈存异常处理接口调用成功，获取余额
								_that.isDisable = false
								_that.isLoading = false
								_that.getAmountInfo(this.validException)
			
							} else {
								_that.showModal({
									title: '错误',
									devResult: nextDevResult,
									content: '获取mac1信息失败：' + nextDevResult.code
								})
								_that.disConnect()
							}
						}
					)
				} else {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '写0015文件失败：' +
							devResult.code +
							':' +
							devResult.err_msg +
							(devResult.msg ? ':' + devResult.msg : '')
					})
					_that.disConnect()
				}
			},
			//异常处理接口
			async LoadCheck(rand, trade_no, balance, mac1, callback) {
				let tmpData = {
					rand: rand,
					money: 0, //异常处理默认金额0
					term_id: terminalNo,
					trade_no: trade_no,
					dec_time: this.issue_times,
					mac1: mac1,
					card_balance: balance,
					cpu_card_id: this.scanDevice_cpu_card_id
				}
				let comparams = {
					customer_id: this.vehicle.customer_id,
					vehicle_code: this.vehicle.vehicle_code,
					vehicle_color: this.vehicle.vehicle_color
				}
				this.comparams = comparams
				Object.assign(tmpData, comparams)
			
				console.log('初始化圈存入参', tmpData)
				let data = {
					routePath: this.$interfaces.loadCheck.method,
					bizContent: {
						...tmpData
					}
				}
				await this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log(res, '圈存异常处理获取参数')
						if (res.code == 200) {
							// this.accept_id = res.data.accept_id
							let code = res.code == 200 ? 0 : res.code
							callback({
								code: code,
								err_msg: res.msg,
								data: res.data.mac2
							})
						} else {
							this.isLoading = false
							this.isBtnLoader = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
							this.disConnect()
						}
					})
					.catch((error) => {
						callback({
							code: 11003,
							err_msg: error.msg
						})
					})
			},
			//预分配
			preAllocated() {
				let params = {
					customer_id: this.vehicle.customer_id,
					cpu_card_id: this.vehicle.cpu_card_id,
					vehicle_code: this.vehicle.vehicle_code,
					vehicle_color: this.vehicle.vehicle_color,
					money: this.moneyData.card_money,
					source: 3
				}
				let data = {
					routePath: this.$interfaces.preAllocate.method,
					bizContent: {
						...params
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						if (res.code == 200) {
							this.isDisable = false
							this.getAmountInfo(this.load)
						} else {
							this.isLoading = false
							this.isBtnLoader = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((err) => {
						this.isLoading = false
						this.isBtnLoader = false
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						})
					})
			},
			disPre() {
				let params = {
					customer_id: this.vehicle.customer_id,
					cpu_card_id: this.vehicle.cpu_card_id,
					vehicle_code: this.vehicle.vehicle_code,
					vehicle_color: this.vehicle.vehicle_color,
					money: Math.abs(this.moneyData.load_money)
					// money: Math.abs(1)

				}
				let data = {
					routePath: this.$interfaces.disPre.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						this.getAmountInfo(this.dispreandPre)

					} else {
						this.disConnect()
					}
				})
			},
			dispreandPre() {
				let money = float.add(this.moneyData.load_money, this.moneyData.card_money)
				if (this.moneyData.load_money == 0 && this.moneyData.card_money == 0) {
					this.disConnect()
					this.isRechargeFlag = true
					this.isLoadFlag = false
					uni.showModal({
						title: '提示',
						content: '补缴欠费后实际圈存金额为零，请充值',
						showCancel: false
					})
				} else if (this.moneyData.card_money >= 0 && money > 0) {
					console.log('preAllpcated', '========================');
					this.preAllocated()
				}
			},
			disConnect() {
				this.isDisable = false
				this.isFirstOpenDevice = true
				this.isLoading = false
				facapi.facSdk.DisconnectDevice(function(devResult) {
					console.log('关闭连接结果=>', devResult);
					_that.isLoading = false
					_that.isBtnLoader = false
				})
				// 完成后关闭蓝牙模块
				bleapi.CloseBle((obj) => {
					console.log(obj)
				})
			},
			closeBle() {
				// 关闭蓝牙模块，防止中途断开，连接不上
				bleapi.CloseBle((obj) => {
					console.log(obj)
				})
			},
			// 显示弹框
			showModal(data) {
				//隐藏loading
				this.isLoading = false
				_that.isBtnLoader = false

				console.log(data.content, 'sdk报错')

				//显示弹框
				let obj = {
					...data
				}
				obj = data.showMyContent ?
					obj : {
						...data,
						content: '操作失败，请打开手机蓝牙，将手机靠近设备后重试。(' +
							data.devResult.code +
							':' +
							data.devResult.err_msg +
							')'
					}
				uni.showModal({
					...obj,
					showCancel: false,
					success: () => {}
				})
			},
			closeLoading() {
				this.connectLoaing = false
				// this.isLoading = false;
			},
			// 圈存存入当前时间
			formatDate(now) {
				var year = now.getFullYear()
				var month =
					now.getMonth() + 1 < 10 ?
					'0' + (now.getMonth() + 1) :
					now.getMonth() + 1
				var date = now.getDate() < 10 ? '0' + now.getDate() : now.getDate()
				var hour = now.getHours() < 10 ? '0' + now.getHours() : now.getHours()
				var minute =
					now.getMinutes() < 10 ? '0' + now.getMinutes() : now.getMinutes()
				var second =
					now.getSeconds() < 10 ? '0' + now.getSeconds() : now.getSeconds()
				return (
					year + '' + month + '' + date + '' + hour + '' + minute + '' + second
				)
			},



		},
		filters: {
			linkStatusFilter: function(val) {
				return val ? '连接成功' : ''
			},
			issueStatusFilter: function(val) {
				return val ? '发行成功' : ''
			},
		}
	}
</script>

<style scoped lang="scss">
	.loadBusiness {
		position: relative;
		padding-bottom: 160rpx;
	}

	.loadBusiness .card {
		margin: 30rpx;
	}

	.loadBusiness .card-box {
		width: 100%;
		height: 240rpx;
		background: url('~@/static/toc/etc_device.png') no-repeat;
		background-size: 100% 100%;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
	}

	.loadBusiness .card-box .bd {
		padding: 0 30rpx;
	}

	.loadBusiness .card-box .bd .title {
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: 500;
		padding-bottom: 10rpx;
	}

	.loadBusiness .card-box .bd .des {
		font-size: 28rpx;
		color: #FFFFFF;
		font-weight: 500;
		opacity: 0.6;
		padding-bottom: 10rpx;
	}

	.loadBusiness .bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;

	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.weui-label {
		width: 300rpx !important;
	}

	.amount-box {

		padding: 20rpx;

		.amount-item {
			margin: 10upx 20upx;
			width: 27%;
			height: 110upx;
			background-color: #f8f9fe;
			border-radius: 10px;
			position: relative;

			.amount-text {
				line-height: 100upx;
				color: #1978ec;
				font-size: 34upx;
				font-weight: bold;
			}

			.amount-item-label {
				color: #1978ec;
				font-size: 26upx;
			}


		}
	}

	.sellPay-Info {
		background-color: #FFFFFF;

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.tips-bottom {
		padding: 0 30rpx;
		position: fixed;
		bottom: 12%;
		color: #878787;
		font-size: 26rpx
	}

	.weui-cells__title {
		font-size: 30rpx;
		font-weight: bold;
	}

	.weui-cell {
		padding: 20rpx 30rpx;
	}
</style>