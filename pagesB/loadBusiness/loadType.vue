<template>
	<view class="load-type">
		<view class="content g-flex g-flex-column g-flex-justify">
			<view class="item-img" @click="loadHandle('bluetooth')">
				<img src="../static/loadRecharge/load_bluetooth.png" alt="">
			</view>
			<view class="item-img" @click="loadHandle('nfc')">
				<img src="../static/loadRecharge/load_nfc.png" alt="">
			</view>

		</view>
		<view class="load_desc">
			<view class="desc_title">
				圈存须知：
			</view>
			<view class="desc_text">
				<text style="color:#0072C0">连接蓝牙设备充值/圈存</text>：目前仅支持带有蓝牙设备的标签
			</view>
			<view class=" g-flex g-flex-column g-flex-align-center g-flex-center">
				<view class="desc_icon">
					<img src="../static/loadRecharge/bluetooth_icon.png" alt="">
				</view>
				<view class="desc_tips g-flex g-flex-align-center g-flex-center">
					<text>蓝牙标志图示</text>
				</view>


			</view>
			<view class="desc_text">
				<text style="color:#0066E9">NFC充值/圈存</text>：目前仅支持带有NFC功能的手机
			</view>
			<view class=" g-flex g-flex-horizontal-vertical ">
				<view class=" g-flex g-flex-column g-flex-align-center g-flex-center" style="margin: 0 40rpx;">
					<view class="desc_icon ">
						<img src="../static/loadRecharge/nfc_icon1.png" alt="">
					</view>
					<view class="desc_tips">
						<text>NFC标志图示1</text>
					</view>
				</view>

				<view class=" g-flex g-flex-column g-flex-align-center g-flex-center" style="margin: 0 40rpx;">
					<view class="desc_icon ">
						<img src="../static/loadRecharge/nfc_icon2.png" alt="">
					</view>
					<view class="desc_tips ">
						<text>NFC标志图示2</text>
					</view>
				</view>

			</view>

		</view>
		<TModal :showModal='btDialogVisible' modalTitle='提示' :showCancelFlag='true' @cancelModal='btDialogVisible=false'
			@okModal='next("bluetooth")' okText='我已了解\n并已打开手机蓝牙' :modalStyle="'width:80%'"
			:comfirmBtnStyle="'white-space: pre-wrap'">
			<view slot='content'>

				<view class="load_desc_dialog">
					<view class="desc_title tips_title">
						打开设备蓝牙
					</view>
					<view class="desc_text tips_text">
						请在三秒内完成：1.插卡 — 2.拔卡 — 3.插卡
					</view>
					<view class="desc_text tips_text">
						激活设备蓝牙功能（显示“蓝牙打开”）
					</view>
					<image class="img" src="../static/loadRecharge/open_device.gif" alt="">
						<view class="link text-cyan" @click="showVideo">
							设备蓝牙无法打开?
						</view>
				</view>


			</view>
		</TModal>

		<TModal :showModal='nfcDialogVisible' modalTitle='提示' :showCancelFlag='true'
			@cancelModal='nfcDialogVisible=false' @okModal='next("nfc")' okText='我已了解\n并已打开NFC功能'
			:modalStyle="'width:80%'" :comfirmBtnStyle="'white-space: pre-wrap'">
			<view slot='content'>
				<view class="load_desc_dialog">
					<view class="desc_title tips_title">
						请确保认真阅读下方圈存须知
					</view>
					<view class="desc_text tips_text">
						具体操作方式如下:
						<view>
							1.确认您的手机是否有NFC功能？如果有就在您手机“设置”中开启“NFC”功能
						</view>
						<view>
							2.打开微信NFC功能：登陆微信->我->设置->通用->开启NFC功能
						</view>
						<view>
							3.将卡片放在手机后背面读取信息
						</view>
						<view>
							4.如果读取卡片无反应，请尝试轻微移动卡片位置或者拿开卡片重新贴放
						</view>
						<view>
							5.为确保圈存成功，圈存和异常处理过程中请保持卡片贴在手机背面的NFC处
						</view>
					</view>
				</view>

			</view>
		</TModal>
	</view>

</template>

<script>
	import {
		getTokenId,
		getAccountId,
		setEtcVehicle
	} from '@/common/storageUtil.js';
	import TModal from '@/components/t-modal/t-modal.vue'
	export default {
		components: {
			TModal
		},
		data() {
			return {
				btDialogVisible: false,
				nfcDialogVisible: false
			}
		},
		onLoad() {

		},
		created() {

		},
		methods: {
			showVideo() {
				let url =
					'https://mp.weixin.qq.com/s?__biz=MzAxNjUxNDYwNg==&mid=**********&idx=1&sn=780e51d1bbeccf23d3ec8dde2e502aa7&chksm=80b4490cb7c3c01a13ac2b109b7d52fe4178c6af9c5810b8c26ea837c7ecc73e9fb1ddd96ec3#rd'
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(url)
				})
			},

			loadHandle(type) {
				if (type == 'bluetooth') {
					this.btDialogVisible = true
					return;
				}
				if (type == 'nfc') {
					this.nfcDialogVisible = true
					return;
				}

			},
			next(val) {
				if (val == 'bluetooth') {
					uni.navigateTo({
						url: "./loadByCard"
					})
					this.btDialogVisible = false
					return
				}
				if (val == 'nfc') {
					uni.navigateTo({
						url: "./loadByNFC"
					})
					this.nfcDialogVisible = false
					return
				}

			}
		}

	}
</script>

<style scoped lang="scss">
	.load-type {
		width: 100%;
		height: 100%;

		.content {
			.item-img {
				margin: 60rpx 60rpx 30rpx 60rpx;
				width: 630rpx;
				height: 130rpx;

				&>img {
					width: 100%;
					height: 100%;
				}
			}

		}
	}

	.load_desc_dialog {
		margin: 30rpx 36rpx;
		.tips_title {
			font-weight: 400;
		}
	}
	.load_desc {
		margin: 60rpx 36rpx;

		.desc_icon {
			margin-top: 40rpx;
			width: 100rpx;
			height: 100rpx;

			&>img {
				width: 100%;
				height: 100%;
			}
		}




	}

	.desc_tips {
		font-size: 24rpx;
		color: #666666;
		margin: 10rpx 0 30rpx 0;
		text-align: center;
	}

	.desc_title {
		font-size: 28rpx;
		font-family: PingFangSC, PingFangSC-Light;
		font-weight: 500;
		color: #555555;
		margin-bottom: 20rpx;
	}

	.desc_text {
		text-align: left;
		line-height: 50rpx;
		padding: 0 10px;
		font-size: 26rpx;
		font-family: PingFangSC, PingFangSC-Light;
		font-weight: 300;
		color: #aaaaaa;
	}

	.tips_text {
		color: #555555;
		font-weight: 400;
		font-size: 28rpx;
	}


	.tips_text view {
		margin-top: 20rpx;
	}

	.self-check {
		width: 100%;
		height: 100%;
		background-color: $uni-bg-color;
		font-family: PingFangSC-Medium, PingFang SC;
		color: #777777;
	}

	.container {
		padding: 30rpx 50rpx;
	}

	.title {
		margin-left: 30rpx;
		margin-bottom: 20rpx;
		font-size: 30rpx;
		color: #000;
		font-weight: 500;
	}

	.desc {
		color: #717171;
		margin-bottom: 10rpx;
		letter-spacing: 2rpx;
	}

	.img {
		width: 570rpx;
		height: 200rpx;
		text-align: center;
	}

	.bottom-container {
		position: fixed;
		right: 0;
		left: 0;
		margin: auto;
		bottom: 130rpx;
	}

	.confirm {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 35rpx;
	}

	.confirm-title {
		font-size: 30rpx;
		margin-left: 20rpx;
		letter-spacing: 4rpx;
		color: #7a7a7a;

	}

	.link {
		margin-top: 30rpx;
		text-align: center;
		text-decoration: underline;
		text-underline-offset: 10rpx;
	}
</style>
