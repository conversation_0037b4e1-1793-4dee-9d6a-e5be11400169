<template>
	<view class="add-car">
		<view v-if="hasPersion">
			<view class="car-title">以下为您名下个人ETC账户</view>
			<view class="card-list">
				<view class="card" @click="changeUser(personalObj, 1)">
					<image class="card-img" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/persion-Icon.png"></image>
					<view class="card-text">
						<text class="c-first">{{personalObj.name}}</text>
						<text class="c-second">{{personalObj.carNumber}}辆车</text>
					</view>
					<view class="arrow">
						<image src="../../../static/tab-bar/index/right-ar.png" class="right-arrow"></image>
					</view>
				</view>
			</view>
		</view>
		<view v-if="!isLoading && accountList && accountList.length > 0" :class="hideBtn ? 'corpList' : ''">
			<view class="car-title" >以下为您名下单位ETC账户</view>
			<view class="card-list" @click="changeUser(item, 2)" v-for="(item,index) in accountList" :key="item.accountId">
				<view class="card">
					<image class="card-img" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/unit.png"></image>
					<view class="card-text">
						<text class="c-first">{{item.name}}</text>
						<text class="c-second">证件类型：{{item.corpType}}</text>
						<text class="c-second">证件号码：{{item.idNum}}</text>
						<text class="c-second">分支机构：{{item.department}}</text>
						<text class="c-second">车辆数量：{{item.carNumber > 0 ? item.carNumber + '辆车' : '无车辆'}}</text>
					</view>
					<view class="arrow">
						<image src="../../../static/tab-bar/index/right-ar.png" class="right-arrow"></image>
					</view>
				</view>
			</view>
		</view>
		<view @click='addCorpUser' v-if="type === 'changeUser'">
			<addCorpUser />
		</view>
		<!-- <view class="goback" v-if="hideBtn">
			<TButton title="返回首页" @clickButton="goHome"/>
		</view> -->
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import Api from "@/common/api/index.js"
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button"
	import {unitType} from'@/common/systemConstant.js'
	import addCorpUser from "@/pagesB/components/t-add-user.vue";
	import {
		getTokenId,
		setAccountId,
		removeStore,
		setStore,
		getStore
	} from '@/common/storageUtil.js';
	import {
		request,
		getCarList
	} from '@/common/request-1/requestFunc.js'
	export default {
		components:{
			tLoading,
			TButton,
			addCorpUser
		},
		data() {
			return {
				accountList: [],
				showBackBtn:true,
				hideBtn:false,
				personalObj:{
					name:'',
					carNumber:0
				},
				hasPersion:false,
				isLoading:false,
				type:"",
				num:0,
				navObj:{
					"selectUser":"/pages/tab-bar/index/index", // 入口选户
					"changeUser":"/pages/tab-bar/index/index" // 用户切换
				}
			}
		},
		onLoad(param) {
			this.type = param.type
			this.getAccountList()
			// 登陆后进入选户，防止不选户点击后退到首页，更新列表标记
			if(this.type == 'selectUser'){
				setStore("intoSelectUser", 1)
			}
		},
		methods: {
			goHome(){
				uni.reLaunch({
					url:'/pages/tab-bar/index/index'
				})
			},
			//获取个人账户列表
			getAccountList() {
				let data = {
					'fileName': Api.getAccountList.method,
					'data':{
						tokenId: getTokenId()
					}
				}
				this.isLoading = true
				request(Api.getAccountList.url, data,(res) => {
					// console.log(res)
					this.accountList = []
					let list = res.data || []
					let countNum = list.length
					if(countNum > 0){
						for(let obj of list){
							obj.carNumber = 0
							this.getCarCount(obj,countNum)
							if(obj.accountType === "1"){
								if(this.type == 'selectUser'){
									setAccountId(obj.accountId)
								}
								this.personalObj.name = obj.name
								this.personalObj.accountId = obj.accountId
								this.personalObj.accountType = "1"
								this.hasPersion = true
							}else{
								for(let oj of unitType){
									if(oj.value == obj.idType){
										obj.corpType = oj.key
									}
								}
								this.accountList.push(obj)
							}
						}
					}else{					
						//没有账户就跳首页
						if(this.type === "login"){
							uni.reLaunch({
								url:'/pages/tab-bar/index/index'
							})
						}
					}
					this.isLoading = false
				},(msg) => {
					this.isLoading = false
				},(err) => {
					this.isLoading = false
				})
			},
			//获取账户车辆数量
			getCarCount(obj,countNum) {
				getCarList(obj.accountId,(res)=>{
					this.num ++
					if(obj.accountType === "1"){
						this.personalObj.carNumber = res.listData.length
						if(res.listData.length > 0){
							this.showBackBtn = false
						}
					}else{
						obj.carNumber = res.totalCount
						if(res.totalCount > 0){
							this.showBackBtn = false
						}
					}
					if(this.num === countNum){
						this.isLoading = false
						this.num = 0
						this.hideBtn = false
						if(this.showBackBtn){
							this.hideBtn = true
						}
					}
				},()=>{
				},()=>{
				})
			},
			// 切换用户
			changeUser(item, type) {
				setAccountId(item.accountId)
				removeStore("intoSelectUser")
				// 扫码进入，选择户后直接跳转前装办理
				console.log(getStore("scan"))
				if(getStore("scan")){
					let t = type === 1 ? "persion" : "corp"
					uni.reLaunch({
						url:"/pagesA/etc/add-car/addCarType?type=" + t
					})
				}else{
					uni.reLaunch({
						url:this.navObj[this.type]
					})
				}
			},
			// 添加企业户
			addCorpUser(){
				setStore("changeUserAddCar", 1)
				uni.reLaunch({
					url:"/pagesA/etc/add-car/addCarType?type=corp"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.add-car {
		display: flex;
		flex-direction: column;
		padding: 28rpx;
		.corpList{
			position: absolute;
			height: calc(100% - 518rpx);
			overflow: auto;
			top: 300rpx;
		}
		.car-title {
			font-size: 36rpx;
			color: #666;
		}

		.card-list {
			margin-top: 28rpx;

			.card {
				display: flex;
				background-color: #fff;
				width: 700rpx;
				min-height: 180rpx;
				align-items: center;
				border-radius: 10rpx;
				position: relative;
				box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
				margin-bottom: 20rpx;
				opacity: .8;
				padding:20rpx 0;
				.arrow {
					position: absolute;
					right: 34rpx;
					.right-arrow{
						width: 30rpx;
						height: 30rpx;
					}
				}

				.card-img {
					width: 108rpx;
					height: 108rpx;
					margin: 0 36rpx;
				}

				.card-text {
					display: flex;
					flex-direction: column;

					.c-first {
						color: #666;
						font-weight:bold;
						font-size: 40rpx;
						width:500rpx;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
						word-break: break-all;
					}

					.c-second{
						color: #333;
						font-size: 28rpx;
						margin-top: 10rpx;
						
					}
					.der-value{
						display: inline-block;
						width:56rpx;
					}
				}
			}
		}
		.goback{
			position:absolute;
			bottom:20rpx;
			width: 718rpx;
			/deep/.cu-btn.radius{
				border-radius: 10rpx;
			}
		}
	}
</style>
