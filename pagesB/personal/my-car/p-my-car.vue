<!-- 我的车辆 -->
<template>
	<view>
		<uni-search-bar @confirm="searchConfirm" @input="searchInput" cancelButton="none" v-if="totalCount > 20" placeholder="请输入要查询的车牌号"></uni-search-bar>
		<scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltolower="lower" lower-threshold="60" @scroll="scroll">
			<view v-if="vehicleAllDataList.length > 0">
				<view style="font-size: 36rpx;color: #666666;padding:30upx 40upx;">{{ vehicleAllDataList.length > 2 ? '您名下存在多个车辆,请选择' : '' }}</view>
				<view class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;" v-if="!showSearchList">
					<view v-for="(item, index) in vehicleAllDataList" :key="index">
						<view @click="vehicleInfo(item, index)"><CarItem :carInfo="item" /></view>
					</view>
				</view>
				<view v-else>
					<view v-if="searchList.length > 0" class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;">
						<view v-for="(item, index) in searchList" :key="index">
							<view @click="checkRealInfo(item, index)"><CarItem :carInfo="item" /></view>
						</view>
					</view>
					<view v-else class="emptySarch">未查询到车辆</view>
				</view>
				
			</view>
			<TButton v-if="isShowBtn" title="充值其他车辆" @clickButton="rechargeOther"></TButton>
		</scroll-view>
		<!-- <paging :loadingType="loadingType" class="paging" v-if="pageNo > 1 && !showSearchList && vehicleAllDataList.length > 0"></paging>
		<view :class="totalCount > 20 ? 'bgProcess' : ''"  v-if="!isCorp && !isAfter">
			<view @click='addCarHandle'>
				<CarCardNew />
			</view>
		</view> -->
		<tLoading :isShow="isLoading" />
		<view v-if="isAfter && vehicleAllDataList.length === 0"><xw-empty :isShow="true" text="您还没有可售后的车辆"></xw-empty></view>

		<TModal :showModal="dialogVisible" modalTitle="请输入充值车牌号码" :showCancelFlag="true" @okModal="onPayHandle" @cancelModal="dialogVisible = false" okText="去充值">
			<form slot="content">
				<view class="weui-form">
					<view class="">
						<view class="vux-x-input weui-cell weui-cell_picker">
							<view class="weui-cell__bd weui-cell__primary">
								<picker style="width:100%;" @change="PickerColorChange" :range="colorPicker" range-key="label">
									<view class="weui-picker-value">{{ colorPicker[color_index].label }}</view>
								</picker>
							</view>
							<view class="weui-cell__ft" style="margin-left: 10rpx;"></view>
							<input placeholder="车牌号码" name="b" style="width: 432rpx;" :value="searchForm.vehicle_code" @input="changeInput($event, 'vehicle_code')" />
						</view>
					</view>
				</view>
			</form>
		</TModal>
	</view>
</template>

<script>
import paging from '@/pagesB/components/load-paging/paging.vue';
import CarCard from '@/pagesB/components/car-card/car-card.vue';
import CarItem from '@/pagesB/components/common/t-car-item-4501.vue';
import Api from '@/common/api/index.js';
import store from 'store/index.js';
import TButton from '@/components/t-button.vue';
import uniSearchBar from '@/pagesB/components/uni-search-bar/uni-search-bar.vue';
import xwEmpty from '@/pagesB/components/xw-empty/xw-empty';
import TModal from '@/components/t-modal/t-modal.vue';
import { mainFlowControl } from '@/common/flowControl.js';
import { vehicleColorPicker } from '@/common/const/optionData.js';
import { flowControlStep, proviceActiveUrlMap } from '@/common/systemConstant.js';

import tLoading from '@/components/common/t-loading.vue';
import {
	setCurrentCar,
	getCurrentCar,
	getTokenId,
	setTokenId,
	getCurrUserInfo,
	setCurrUserInfo,
	getAccountId,
	setBusinessTypes,
	setStore,
	getStore,
	setEtcVehicle
} from '@/common/storageUtil.js';
import CarCardNew from '@/pagesB/components/car-card-new/car-card-new.vue';
import { uni_encodeURIComponent } from '@/common/helper.js';
import util from '@/common/util.js';
import neilModal from '@/components/neil-modal/neil-modal.vue';
import { request, getCarList, carStatus } from '@/common/request-1/requestFunc.js';
import { carObuInfo, getCarInfo } from '@/common/request-1/requestFor4501.js';
export default {
	components: {
		CarCard,
		tLoading,
		CarCardNew,
		CarItem,
		paging,
		uniSearchBar,
		neilModal,
		TButton,
		xwEmpty,
		TModal
	},
	data() {
		return {
			showDialog: false,
			isLoading: false,
			dialogVisible: false,
			vehicleInfoList: [],
			vehicleAllDataList: [],
			carInfoList: [],
			vehicleStatusList: [],
			proviceActiveUrlMap,
			scrollTop: 0,
			loadingType: 0,
			page: 1,
			pageNo: 1,
			pageSize: 20,
			old: {
				scrollTop: 0
			},
			colorPicker: vehicleColorPicker,

			allCarList: [],
			searchList: [],
			showSearchList: false,
			totalCount: 0,
			isAfter: false,
			isCorp: false,
			businessType: '',
			searchForm: { vehicle_code: '', vehicle_color: '0' },
			color_index: 0,
			isShowBtn: false
		};
	},
	computed: {
		style() {
			var systemInfo = uni.getSystemInfoSync();
			let maxHeight = 150;
			let height = 40;
			//显示搜索框的时候
			if (this.totalCount > 20) {
				maxHeight = 200;
				height = 90;
			}
			if (!this.isCorp) {
				return `max-height:${systemInfo.windowHeight - maxHeight}px`;
			} else {
				return `height:${systemInfo.windowHeight - height}px`;
			}
		}
	},
	onLoad(obj) {
		console.log(obj, '传过来的obj');
		this.businessType = obj.fontType || '';
		/* console.log('我的车页面onload', obj)
			if (getAccountId() !== getStore('persionAccountId')) {
				this.isCorp = true
			} */
		
		if (obj.fontType === 'afterSale') {
			uni.setNavigationBarTitle({
				title: '车辆售后'
			});
			this.isAfter = true;
		}
		
		let setTitle = {
			recharge: '充值',
			rechargeList: '充值记录',
			carPayList: '通行记录',
			blackList: '卡状态查询',
			loadRecharge: '圈存充值',
			newApply: '新发申请单',
			otherRecharge:'卡账代充',
			signature:"电子签章查看"
		};
		if (obj.fontType && setTitle[obj.fontType]) {
			uni.setNavigationBarTitle({
				title: setTitle[obj.fontType]
			});
		}
		if(getAccountId()){
			this.getVerhicleList();
		}
		
	},
	methods: {
		changeInput(event, data) {
			this.searchForm[data] = event.detail.value.trim().toUpperCase();
		},
		// 车牌颜色选择
		PickerColorChange(e) {
			this.color_index = e.detail.value;
			this.searchForm.vehicle_color = this.colorPicker[e.detail.value].value || '';
		},
		//绑定其他车辆
		rechargeOther() {
			uni.navigateTo({
				url:'/pagesB/rechargeBusiness/rechargeOther/rechargeOther'
			})
		},
		
		getVerhicleList() {
			let data = {
				routePath: this.$interfaces.customerBizView.method,
				bizContent: {
					customer_id: getAccountId()
				}
			};
			request(
				this.$interfaces.issueRoute,
				{
					data: data
				},
				res => {
					console.log(res);
					if (res.code == 200) {
						this.vehicleAllDataList = res.data.vehicles || [];
					} else if (res.code == 401) {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									uni.reLaunch({
										url: '/pagesD/login/p-login'
									});
								}
							}
						});
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}
			);
		},
		// 查询车辆详情接口
		vehicleInfo(item) {
			if (this.isLoading) return;
			this.isLoading = true;
			setCurrentCar({});
			let data = {
				routePath: this.$interfaces.vehicleBizSearch.method,
				bizContent: {
					vehicle_code: item.vehicle_code,
					vehicle_color: item.vehicle_color
				}
			};
			this.$request
				.post(this.$interfaces.issueRoute, {
					data: data
				})
				.then(res => {
					this.isLoading = false;
					if (res.code == 200 && res.data && res.data.length) {
						setCurrentCar(res.data[0]);
						this.goCarBusinessPage(res.data[0]);
					}
				})
				.catch(error => {
					this.isLoading = false;
				});
		},
		goCarBusinessPage(item) {
			if (this.businessType == 'signature') {
				uni.navigateTo({
					url:'/pagesC/userAgreement/agreementList'
				});
				return;
			}
			if (!item.cpu_card_id) {
				uni.showModal({
					title: '提示',
					content: '您当前车辆' + item.vehicle_code + '未绑定ETC卡'
				});
				return;
			}
			if (this.businessType == 'recharge') {
				if (item.gx_card_type != '5'&&item.gx_card_type != '8') {
					uni.showModal({
						title: '提示',
						content: '车牌号为' + item.vehicle_code + '的卡不是捷通日日通记账卡,无法进行充值'
					});
					return;
				}
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/recharge/p-recharge'
				});
				return;
			}
			if (this.businessType == 'rechargeList') {
				if (item.gx_card_type != '5'&&item.gx_card_type != '8') {
					uni.showModal({
						title: '提示',
						content: '车牌号为' + item.vehicle_code + '的卡不是捷通日日通记账卡,无法查看充值记录'
					});
					return;
				}
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/rechargeList/p-rechargeList'
				});
				return;
			}
			if (!item.obu_id) {
				uni.showModal({
					title: '提示',
					content: '您当前车辆' + item.vehicle_code + '未绑定OBU标签'
				});
				return;
			}
			// 激活功能目前不需要上线
			// // obu 未激活
			// if(item.obu_status == 2){
			// 	uni.navigateTo({
			// 		url: "/pagesA/newBusiness/p-activation/activation"
			// 	});
			// 	return;
			// }
			if (this.businessType == 'afterSale') {
				uni.navigateTo({
					url: '/pagesD/afterSale/home/<USER>' + item.vehicle_code
				});
				return;
			}
			
			
			if (this.businessType == 'newApply') {
				uni.navigateTo({
					url: '/pagesA/orderBusiness/orderBusiness'
				});
				return;
			}
			if (this.businessType == 'carPayList') {
				uni.navigateTo({
					url: '/pagesB/cardBusiness/payInfo/p-payInfo'
				});
				return;
			}
			if (this.businessType == 'blackList') {
				uni.navigateTo({
					url: '/pagesB/cardBusiness/blackList/p-blackInfo'
				});
				return;
			}
			if (this.businessType == 'loadRecharge') {
				setEtcVehicle(item);
				uni.navigateTo({
					url: '/pagesB/loadBusiness/loadBusiness'
				});
				return;
			}

			if (this.businessType == 'containerRefund') {
				uni.navigateTo({
					url: '/pagesB/containerRefund/car-record/record-list'
				});
				return;
			}
		},
		//获取车辆信息
		reqCarInfo(currentCar) {
			let params = {
				vehicleId: getCurrentCar().vehicleId
			};
			let data = {
				fileName: Api.fontGetUserQuery.method,
				childernMethod: Api.fontGetUserQuery.childernMethod,
				data: params
			};
			console.log('获取车辆信息...');
			request(Api.fontGetUserQuery.url, data, data => {
				let res = JSON.parse(data.encryptedData);
				console.log('获取车辆信息成功...', JSON.parse(data.encryptedData));
				currentCar.name = res.ownerName;
				setCurrentCar(currentCar);
			});
		},
		searchConfirm(e) {
			//用于精确查询
			// if(e.value){
			// 	this.showSearchResult(e.value)
			// }
		},
		//搜索查询
		searchInput(e) {
			if (!e.value) {
				this.showSearchList = false;
				this.searchList = [];
			} else {
				//模糊匹配
				this.showSearchResult(e.value);
			}
		},
		lower() {
			console.log('底部');
			/* this.getMoreList() */
		},
		scroll(e) {
			// console.log(e)
			this.old.scrollTop = e.detail.scrollTop;
		},
		//获取车辆列表
		reqVerhicleList(type) {
			this.isLoading = true;
			let data = {
				data: {
					accountId: getAccountId(),
					pageNo: 1,
					pageSize: this.pageSize
				}
			};
			request(
				this.$interfaces.userVehiclePage,
				data,
				res => {
					console.log('车辆列表', res);
					this.isLoading = false;
					this.carInfoList = res.data;
					if (type == 1 || type == 11) {
						let font = []; //前装车辆
						for (let obj of this.carInfoList) {
							font.push(obj);
						}
						console.log('前装车辆', font);
						if (this.carInfoList.length - font.length >= 5 && type == 1) {
							uni.showModal({
								title: '提示',
								content: '您当前账号下已办理5辆车辆，不能再办理。'
							});
							return;
						}
						if (font.length >= 5 && type == 11) {
							uni.showModal({
								title: '提示',
								content: '您当前账号当前已办理5辆前装车辆，不能再办理。'
							});
							return;
						}
					}
					this.totalCount = res.totalCount;
					//当车辆数大于20条需要查询所有车辆用于搜索查询
					if (this.totalCount > 20) {
						this.getAllCarList();
					}
					if (this.carInfoList.length > 0) {
						this.sendCarInfo(this.carInfoList, '');
					}
					this.pageNo = Math.ceil(res.totalCount / this.pageSize) < 1 ? 1 : Math.ceil(res.totalCount / this.pageSize);
					this.page++;
				},
				msg => {
					this.isLoading = false;
				},
				err => {
					this.isLoading = false;
				}
			);
		},
		// 获取车辆详情
		sendCarInfo(list, type) {
			if (type) {
				this.allVehiclelist = [];
			} else {
				this.vehicleStatusList = [];
			}
			this.isLoading = true;

			let time = 0;
			getCarInfo(
				list,
				(res, item) => {
					time++;

					item['obuId'] = res.data.obuId || '';
					item['black_type'] = res.data.blackType;
					if (type) {
						this.allVehiclelist.push(item);
					} else {
						this.vehicleStatusList.push(item);
					}
					if (time == list.length) {
						//console.log("获取OBU信息...");
						this.isLoading = false;
						//获取标签状态
						this.reqObuInfo(type);
					}
				},
				item => {
					time++;
					this.isLoading = false;
					this.vehicleStatusList.push(item);
					if (time == list.length) {
						this.isLoading = false;
						//获取标签状态
						this.reqObuInfo();
					}
				},
				() => {
					this.isLoading = false;
				}
			);
		},
		//获取OBU信息
		reqObuInfo(type) {
			let forEachList = [];
			if (type) {
				forEachList = this.allVehiclelist;
				this.allCarList = [];
			} else {
				forEachList = this.vehicleStatusList;
			}
			this.isLoading = true;
			let time = 0;
			console.log('--+++', forEachList, type);
			carObuInfo(
				forEachList,
				(item, data) => {
					time++;
					let res = data.data;
					console.log('获取OBU信息', item.plateNum, res);
					if (res) {
						if (res.result == 1) {
							let obuitem = res.data;
							item.obustatus = res.data.status;
							item.obuActiveFlag = res.data.activeFlag;
							if (type) {
								this.allCarList.push(item);
							} else {
								this.vehicleAllDataList.push(item);
							}
						}
					} else {
						if (type) {
							this.allCarList.push(item);
						} else {
							this.vehicleAllDataList.push(item);
						}
					}
					console.log(this.vehicleAllDataList, 'vehicleAllDataList');
					if (time === forEachList.length) {
						// 售后只展示已办理完成的前后装车辆
						if (this.isAfter) {
							let fontAry = [];
							for (let obj of this.vehicleAllDataList) {
								if (obj.obustatus == 3 && obj.obuActiveFlag == '0') {
									fontAry.push(obj);
								}
							}
							this.vehicleAllDataList = fontAry;
						}
						this.isLoading = false;
						console.log(this.vehicleAllDataList);
					}
				},
				() => {
					this.isLoading = false;
				},
				() => {
					this.isLoading = false;
				}
			);
		},
		//根据车辆列表数据进入不同页面
		checkRealInfo(item, idx) {
			console.log(item);
			this.isTokenId = true;
			if (!this.isTokenId) {
				this.isLogin();
			} else {
				let currentCar = {
					plateNum: item.plateNum, //车牌号
					plateColor: item.plateColor, //车牌颜色
					vehicleId: item.vehicleId, //车辆编号
					status: item.status
				};
				setCurrentCar(currentCar);
				uni.navigateTo({
					url: '/pagesB/personal/car-info/p-car-etcinfo'
				});
				// 查车辆信息
				setBusinessTypes(1);
				if (item && item.issueState == '0') {
					uni.showModal({
						title: '提示',
						content: '您的OBU未激活，点击确定去激活',
						success: function(res) {
							if (res.confirm) {
								// uni.navigateTo({
								// 	url: '/pagesA/etc/car-activation/p-car-activation'
								// });
							}
						}
					});

					return;
				}
				if (item && item.issueState == '1' && !(item.obustatus == 3 && item.obuActiveFlag == '0')) {
					uni.showModal({
						title: '提示',
						content: '您的OBU未激活，点击确定去激活',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pagesA/etc/car-activation/p-car-activation'
								});
							}
						}
					});

					return;
				}
				if (item && item.issueState == '1' && (item.obustatus == 3 && item.obuActiveFlag == '0')) {
					console.log(this.isAfter, 'this.isAfter');
					if (this.isAfter) {
						uni.navigateTo({
							url: '/pagesD/afterSale/home/<USER>' + item.plateNum
						});
					} else {
						uni.navigateTo({
							url: '/pagesB/personal/car-info/p-car-etcinfo'
						});
					}
				}
			}
		},
		//添加车辆
		reqProcess(type) {
			/* this.showDialog = false
				setBusinessTypes(type)
				this.reqVerhicleList(type); */
		},
		addCarHandle() {
			let userType = 'persion';
			if (this.isCorp) {
				userType = 'corp';
			}
			setStore('businessHandle', 'addCar');
			uni.navigateTo({
				url: '/pagesA/etc/add-car/p-addCarType?type=' + userType
			});
		},
		//获取激活url
		reqActiveUrl() {
			let datas = getCurrentCar();
			//console.log('12112'+JSON.stringify(getCurrentCar()));
			this.isLoading = true;

			//获取当前日期
			let myDate = new Date();
			let nowY = myDate.getFullYear();
			let nowM = myDate.getMonth() + 1;
			let nowD = myDate.getDate();
			let enddate = nowY + '-' + (nowM < 10 ? '0' + nowM : nowM) + '-' + (nowD < 10 ? '0' + nowD : nowD); //当前日期
			//获取三十天前日期
			let lw = new Date(myDate - 1000 * 60 * 60 * 24 * 30); //最后一个数字30可改，30天的意思
			let lastY = lw.getFullYear();
			let lastM = lw.getMonth() + 1;
			let lastD = lw.getDate();
			let startdate = lastY + '-' + (lastM < 10 ? '0' + lastM : lastM) + '-' + (lastD < 10 ? '0' + lastD : lastD); //三十天之前日期
			let params = {
				//tokenId:uni.getStorageSync("tokenId"),
				tokenId: getTokenId(),
				startDate: startdate,
				endDate: enddate,
				type: 1,
				status: 1,
				expressStatus: 1
			};
			let data = {
				fileName: Api.getOrderList.method,
				data: params
			};
			request(
				Api.getAccountSignchannellist.url,
				data,
				res => {
					// 这里正常数据返回
					console.log('获取订单列表' + JSON.stringify(res));
					let OrderList = res.list;
					var hasOrder = false;
					//遍历list
					try {
						OrderList.forEach((item, index) => {
							if (item.plateNum == datas.plateNum && item.plateColor == datas.plateColor) {
								let productId = item.productId;
								console.log('reqActiveUrl productId == ' + productId);
								//请求发行方activeurl
								this.reqProductList(productId);
								hasOrder = true;
								//跳出当前循环
								throw new Error();
							}
						});
					} catch (e) {
						console.log('退出订单循环');
					}
					this.isLoading = false;
					if (hasOrder == false) {
						uni.showModal({
							title: '提示',
							content: '未找到对应激活地址'
						});
					}
				},
				msg => {
					this.isLoading = false;
				},
				err => {
					console.log(err);
					this.isLoading = false;
				}
			);
		},
		reqProductList(productId) {
			this.isLoading = true;
			var activeurl = '';
			let params = {
				tokenId: getTokenId(),
				province: '11',
				pageNo: '0',
				pageSize: '50'
			};
			let data = {
				fileName: Api.getConfigProductService.method,
				data: params
			};
			request(
				Api.getConfigProductService.url,
				data,
				res => {
					const { products } = res;
					var hasOrder = false;
					try {
						//遍历list
						products.forEach((item, index) => {
							if (item.productId == productId) {
								let productId = item.productId;
								console.log('reqProductList productId == ' + productId);
								//请求发行方activeurl
								//activeurl = item.h5Url;
								//console.log(item.name);
								//console.log(this.proviceActiveUrlMap.get(item.name));
								//activeurl = this.proviceActiveUrlMap.get(item.name);
								activeurl = item.h5Url;
								hasOrder = true;
								console.log('...h5Url...' + activeurl);
								//跳出当前循环
								console.log('...跳出当前循环...');

								throw new Error();
							}
						});
					} catch (e) {
						console.log('退出产品列表循环');
					}

					this.isLoading = false;
					if (hasOrder == false) {
						uni.showModal({
							title: '提示',
							content: '未找到对应激活地址'
						});
					} else {
						uni.navigateTo({
							url: '/pages/uni-webview/uni-webview?ownPath=' + uni_encodeURIComponent(activeurl)
						});
					}
				},
				msg => {
					this.isLoading = false;
				},
				err => {
					// console.log("err", err);
					uni.showModal({
						title: '提示',
						content: err.message
					});
					this.isLoading = false;
				}
			);
		},
		//分页获取更多
		getMoreList() {
			if (this.loadingType !== 0) {
				//loadingType!=0;直接返回
				return false;
			}
			this.loadingType = 1;
			uni.showNavigationBarLoading(); //显示加载动画
			let data = {
				data: {
					accountId: getAccountId(),
					pageNo: this.page,
					pageSize: this.pageSize
				}
			};
			request(
				this.$interfaces.userVehiclePage,
				data,
				res => {
					console.log(res);
					if (this.page > this.pageNo) {
						//超过最大页数没有数据
						this.loadingType = 2;
						uni.hideNavigationBarLoading(); //关闭加载动画
						return;
					}
					this.page++; //每触底一次 page +1
					this.carInfoList = [...this.carInfoList, ...res.data]; //将数据拼接在一起
					this.sendCarInfo(res.data, '');
					this.loadingType = 0; //将loadingType归0重置
					uni.hideNavigationBarLoading(); //关闭加载动画
				},
				msg => {},
				err => {}
			);
		},
		//获取所有车辆
		getAllCarList() {
			this.isLoading = true;
			getCarList(
				getAccountId(),
				res => {
					if (res.listData.length > 0) {
						this.sendCarInfo(res.listData, 'all');
					} else {
						this.isLoading = false;
					}
				},
				() => {
					this.isLoading = false;
				},
				() => {
					this.isLoading = false;
				}
			);
		},
		//搜索查询
		showSearchResult: util.debounce(function(value) {
			this.searchList = [];
			for (let obj of this.allCarList) {
				if (obj.plateNum.includes(value.trim().toUpperCase())) {
					this.searchList.push(obj);
				}
			}
			this.showSearchList = true;
		})
	}
};
</script>

<style lang="scss" scoped>
.bgProcess {
	/deep/.index-car-card {
		margin-top: 0px;
	}
}

.modal {
	/deep/.neil-modal {
		.neil-modal__header {
			text {
				color: #000000 !important;
				background-color: #fff !important;
			}
		}

		.neil-modal__footer-left,
		.neil-modal__footer-right {
			color: #47a8ee !important;
			background-color: #fff;
			border: 1px solid #1d82d2;
			border-radius: 10rpx;
			height: 70rpx;
			line-height: 70rpx;
		}

		.neil-modal__footer-right {
			color: #fff !important;
			background-color: #1d82d2;
			border: 1px solid#1D82D2;
		}
	}

	.dialog {
		.user-btn {
			/deep/.cu-btn {
				position: relative;
				font-size: 26rpx;
				height: 90rpx;
			}
		}
	}
}

.paging {
	display: flex;
	justify-content: center;
	align-items: center;

	/deep/.load-more {
		width: 670rpx;
		position: relative;
		top: -40rpx;
	}
}

.i-card {
	padding: 0 24upx;
	margin-top: 30upx;
}

.p-a-content {
	margin: 0 40upx 40upx 40upx;
	border-radius: 16upx;

	/deep/.t-order-account {
		background-color: #fff;
	}
}

.emptySarch {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	position: absolute;
	width: 100%;
	font-size: 30rpx;
}
.weui-cell_picker .weui-picker-value{
	width: 130rpx;
}
.weui-cell{
	border-top: 1px solid #f3f3f3;
	border-bottom:1px solid #f3f3f3; 
}
</style>
