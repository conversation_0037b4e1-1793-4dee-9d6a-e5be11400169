<!-- 我的ETC账户 -->
<template>
	<view class="add-car">
		<view v-if="!isLoading && accountList && accountList.length > 0">
			<view class="car-title">您名下存在多个人ETC账户,请选择</view>
			<view class="card-list" @click="changeUser(item, 2)" v-for="(item,index) in accountList" :key="item.accountId">
				<view class="card">
					<image class="card-img" v-if='item.accountType==="1"' src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/persion-Icon.png"></image>
					<image class="card-img" v-if='item.accountType==="2"' src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/unit.png"></image>
					<view class="card-text">
						<text class="c-first">{{item.name}}</text>
						<text class="c-second">证件类型：{{item.corpType}}</text>
						<text class="c-second">证件号码：{{item.idNum}}</text>
						<text class="c-second" v-if='item.accountType==="2"'>部门：{{item.department}}</text>
						<text class="c-second">车辆数量：{{item.carNumber > 0 ? item.carNumber + '辆车' : '无车辆'}}</text>
					</view>
					<view class="arrow">
						<image src="../../../static/tab-bar/index/right-ar.png" class="right-arrow"></image>
					</view>
				</view>
			</view>
		</view>
		<view @click='addCorpUser'>
			<addCorpUser />
		</view>
		<!-- <view class="goback" v-if="hideBtn">
			<TButton title="返回首页" @clickButton="goHome"/>
		</view> -->
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import Api from "@/common/api/index.js"
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button"
	import {
		unitType,
		certificatesTypes
	} from '@/common/systemConstant.js'
	import addCorpUser from "@/pagesB/components/t-add-user.vue";
	import {
		getTokenId,
		setAccountId,
		removeStore,
		setStore,
		getStore
	} from '@/common/storageUtil.js';
	import {
		request,
		getCarList
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			tLoading,
			TButton,
			addCorpUser
		},
		data() {
			return {
				accountList: [],
				showBackBtn: true,
				hideBtn: false,
				personalObj: {
					name: '',
					carNumber: 0
				},
				hasPersion: false,
				isLoading: false,
				type: "",
				num: 0,
				navObj: {
					"selectUser": "/pages/tab-bar/index/index", // 入口选户
					"changeUser": "/pages/tab-bar/index/index" // 用户切换
				}
			}
		},
		onLoad(param) {
			this.type = param.type
			this.getAccountList()
			// 登陆后进入选户，防止不选户点击后退到首页，更新列表标记
			if (this.type == 'selectUser') {
				setStore("intoSelectUser", 1)
			}
		},
		methods: {


			goHome() {
				uni.reLaunch({
					url: '/pages/tab-bar/index/index'
				})
			},

			//获取个人账户列表
			getAccountList() {
				let data = {

					'data': {

					}
				}
				this.isLoading = true
				request(this.$interfaces.getUserAccountList, data, (res) => {
					console.log(res);
					if (res.data) {
						this.accountList = []
						let list = res.data || []
						let countNum = list.length;

						if (countNum > 0) {
							for (let obj of list) {
								obj.carNumber = 0
								this.getCarCount(obj, countNum);
								console.log(obj.accountType === "1")
								if (obj.accountType === "1") {
									for (let oj of certificatesTypes) {

										if (oj.value == obj.idType) {
											console.log(oj)
											obj.corpType = oj.label
										}
									}
								}
								if (obj.accountType === "2") {
									for (let oj of unitType) {
										if (oj.value == obj.idType) {
											obj.corpType = oj.key
										}
									}
								}
								console.log(obj);
								this.accountList.push(obj);
							}
						}
					} else {
						this.addPersionUser();
					}
					this.isLoading = false
				}, (msg) => {
					this.isLoading = false
				}, (err) => {
					this.isLoading = false
				})
			},
			//获取账户车辆数量
			getCarCount(obj, countNum) {

				getCarList(obj.accountId, (res) => {
					this.num++;
					if (obj.accountType === "1") {
						obj.carNumber = res.listData.length
						if (res.listData.length > 0) {
							this.showBackBtn = false
						}
					} else {
						obj.carNumber = res.listData.length
						if (res.listData > 0) {
							this.showBackBtn = false
						}
					}
					if (this.num === countNum) {
						this.isLoading = false
						this.num = 0
						this.hideBtn = false
						if (this.showBackBtn) {
							this.hideBtn = true
						}
					}
				}, () => {}, () => {})
			},
			// 切换用户
			changeUser(item, type) {
				setAccountId(item.accountId);
				uni.reLaunch({
					url: '/pages/home/<USER>/s-home'
				})
				return;
				removeStore("intoSelectUser")
				// 扫码进入，选择户后直接跳转前装办理
				console.log(getStore("scan"))
				if (getStore("scan")) {
					let t = type === 1 ? "persion" : "corp"
					uni.reLaunch({
						url: "/pagesA/etc/add-car/addCarType?type=" + t
					})
				} else {
					uni.reLaunch({
						url: this.navObj[this.type]
					})
				}
			},
			// 添加企业户
			addCorpUser() {
				setStore("changeUserAddCar", 1)
		
				setStore('businessHandle', 'addCorpUser');
				
				uni.reLaunch({
					url: "/pagesA/etc/add-car/s-addCarType?type=corp"
				})
			},
			// 添加个人户
			addPersionUser() {
			    setStore('businessHandle', 'addPersionUser');
				uni.reLaunch({
					url: "/pagesA/etc/add-car/s-addCarType?type=persion"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.add-car {
		display: flex;
		flex-direction: column;
		padding: 28rpx;

		.corpList {
			position: absolute;
			height: calc(100% - 518rpx);
			overflow: auto;
			top: 300rpx;
		}

		.car-title {
			font-size: 36rpx;
			color: #666;
		}

		.card-list {
			margin-top: 28rpx;

			.card {
				display: flex;
				background-color: #fff;
				width: 700rpx;
				min-height: 180rpx;
				align-items: center;
				border-radius: 10rpx;
				position: relative;
				box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
				margin-bottom: 20rpx;
				opacity: .8;
				padding: 20rpx 0;

				.arrow {
					position: absolute;
					right: 34rpx;

					.right-arrow {
						width: 30rpx;
						height: 30rpx;
					}
				}

				.card-img {
					width: 108rpx;
					height: 108rpx;
					margin: 0 36rpx;
				}

				.card-text {
					display: flex;
					flex-direction: column;

					.c-first {
						color: #666;
						font-weight: bold;
						font-size: 40rpx;
						width: 500rpx;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
						word-break: break-all;
					}

					.c-second {
						color: #333;
						font-size: 28rpx;
						margin-top: 10rpx;

					}

					.der-value {
						display: inline-block;
						width: 56rpx;
					}
				}
			}
		}

		.goback {
			position: absolute;
			bottom: 20rpx;
			width: 718rpx;

			/deep/.cu-btn.radius {
				border-radius: 10rpx;
			}
		}
	}
</style>
