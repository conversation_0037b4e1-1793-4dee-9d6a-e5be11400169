<!-- 个人中心 -->
<template>
	<view>
		<view class="index-top">
		</view>

		<view class="nav-icon-index">
			<view class="nav-icon" @click="skipAppointment(1)">
				<view  class="green-travel"></view>
				<view>绿通预约</view>
			</view>
			<view class="nav-icon" @click="skipAppointment(2)">
				<view  class="harvester"></view>
				<view>收割机运输</view>
				<view>车辆预约</view>
			</view>
			<view class="nav-icon" @click="skipAppointment(3)">
				<view class="container"></view>
				<view>集装箱预约</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import Api from '@/common/api/index.js';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getCurrUserInfo,
		setCurrUserInfo,
		getTokenId,
		getAccountId
	} from '@/common/storageUtil.js';
	import {
		uni_encodeURIComponent
	} from '@/common/helper.js';
	import {
		request,
		getCarList,
		carStatus,
		carObuInfo
	} from '@/common/request-1/requestFunc.js'
	var _self;
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				timer: 0,
				isRedirt: false,
				pixelRatio: 1,
				textarea: '',
				isLoading: false,
				vehicleInfoList: [],
				carsLength: '0',
				vehicleAllDataList: [],
				carInfoList: [],
				skipType: 1
			}
		},
		onLoad() {},
		methods: {
			skipAppointment(skipType) {
				this.skipType = skipType;
				let userInfo = getCurrUserInfo();

				if (this.isRedirt) {
					return;
				}
				//未实名认证或者已认证没有户，直接调用小程序
                if (userInfo && !userInfo.isCertified || !getAccountId()) {
					this.skipApp();
				} else {
					this.reqVerhicleList();
				}

				this.isRedirt = true;
				setTimeout(() => {
					this.isRedirt = false;
				}, 500);
			},
			//获取车辆列表
			reqVerhicleList() {
				this.isLoading = true;
				getCarList(getAccountId(), (res) => {
					this.carInfoList = res.listData;
					if (res.listData.length > 0) {
						this.carsLength = res.listData.length
						this.reqVehicleInfo();
					} else {
						this.skipApp();
						this.carsLength = 0;
						this.isLoading = false;
					}
				}, (msg) => {
					if (msg !== "无效的token") {
						this.skipApp();
						this.carsLength = 0;
					}
					this.isLoading = false;
					this.topLoading = false;
				}, () => {
					this.skipApp();
					this.isLoading = false;
					this.topLoading = false;
				})
			},
			reqVehicleInfo() { //获取车辆信息
				this.timer = 0;
				var isSkip = false;
				this.vehicleAllDataList = [];
				this.carInfoList.forEach((item, index) => {
					let params = {
						tokenId: getTokenId(),
						vehicleId: item.vehicleId,
						accountId:getAccountId()
					};
					let data = {
						'fileName': Api.getUserQuery.method,
						'data': params
					};

					this.isLoading = true;
					request(Api.getUserQuery.url, data, (res) => {
						console.log('查询成功')
						// console.log(res)
						item['useCharacter'] = res.useCharacter;
						this.vehicleAllDataList.push(item);
						this.timer++;
						if (this.timer === this.carInfoList.length) {
							this.reqVehicleObuInfo();
						};
					}, (msg) => {
						this.timer++;
						if (this.timer === this.carInfoList.length) {
							this.reqVehicleObuInfo();
						}
						this.isLoading = false;
					}, (err) => {
						if (isSkip == false) {
							isSkip = true;
							this.skipApp();
						}
						this.isLoading = false;
					});
				});
			},
			reqVehicleObuInfo() {
				this.timer = 0;
				var isSkip = false;
				this.carInfoList = [];
				// console.log(this.vehicleAllDataList)
				if(this.vehicleAllDataList.length === 0){
					this.skipApp();
					return
				}
				this.vehicleAllDataList.forEach((item, index) => {
					let params = {
						tokenId: getTokenId(),
						vehicleId: item.vehicleId,
						accountId:getAccountId()
					};
					let data = {
						'fileName': Api.getUserObu.method,
						'data': params
					};

					this.isLoading = true;
					request(Api.getUserObu.url, data,(res) => {
						var obulist = res.obuData;
						// console.log("obulist:" + JSON.stringify(res.obuData));
						if (obulist && obulist.length > 0) {
							obulist.forEach((obuitem, obuindex) => {
								if (obuitem.status == 1) {
									item['obuId'] = obuitem.obuId;
									item['obuSign'] = obuitem.obuSign == null ? 2 : obuitem.obuSign;
									item['obuStatus'] = obuitem.status;
									this.carInfoList.push(item);
								}
							});
							this.timer++;
							// console.log("this.vehicleAllDataList.length:" + this.vehicleAllDataList.length);
							if (this.timer === this.vehicleAllDataList.length) {
								this.reqVehicleCpuInfo();
							}
						} else {
							this.timer++;
							if (this.timer === this.vehicleAllDataList.length) {
								this.reqVehicleCpuInfo();
							}
							//this.isLoading = false;
						}
					}, (msg) => {
						if (isSkip == false) {
							isSkip = true;
							this.skipApp();
						}
						this.isLoading = false;
					},(err) => {
						console.log("获取OBU信息失败...");
						if (isSkip == false) {
							isSkip = true;
							this.reqVehicleCpuInfo();
							//this.skipApp();
						}
						this.isLoading = false;
					});
				});
			},
			//查询CPU信息
			reqVehicleCpuInfo() {
				var isSkip = false;
				this.vehicleInfoList = [];

				if (this.carInfoList.length == 0) { //无有效的OBU
					console.log("没有有效的OBU...");
					this.isLoading = false;
					this.skipApp();
					return;
				};
				let time = 0
				this.carInfoList.forEach((item, index) => {

					let params = {
						tokenId: getTokenId(),
						obuId: item.obuId
					};
					let data = {
						'fileName': Api.getObuCard.method,
						'data': params
					};
					this.isLoading = true;
					request(Api.getObuCard.url, data, (res) => {
						time++
						var cpulist = res.cardData;
						if (cpulist.length > 0) {
							cpulist.forEach((cpuitem, cpuindex) => {
								if (cpuitem.status == 1) {
									item['etcCardId'] = cpuitem.id;
									item['etcCardStatus'] = cpuitem.status;
									this.vehicleInfoList.push(item);
								}
							});

							if (time === this.carInfoList.length) {
								this.isLoading = false;
								this.skipApp();
							}
						} else {
							if (isSkip == false) {
								isSkip = true;
								this.skipApp();
							}
							this.isLoading = false;
						}
					}, (msg) => {
						if (!isSkip) {
							isSkip = true;
							this.skipApp();
						}
						this.isLoading = false;
					}, (err) => {
						if (!isSkip) {
							isSkip = true;
							this.skipApp();
						}
						this.isLoading = false;
					});
				});
			},
			skipApp() {
				let userInfo = getCurrUserInfo();
				var extraData;
				//console.log("跳转携带参数:"+JSON.stringify(extraData));
				if (this.skipType == 1 || this.skipType == 2) {
					//console.log("跳转绿通车小程序...");
					//跳转绿通车预约小程序
					extraData = {
						mobile: userInfo && userInfo.mobile,
						vehicleInfoList: this.vehicleInfoList
					};
					// console.log("extraData:" + JSON.stringify(extraData));
					wx.navigateToMiniProgram({
						appId: 'wx6412b2560c6b1aa9',
						path: '/pages/appointment/view/index/index',
						extraData: extraData,
						success(res) {
							//console.log("绿通预约小程序");
						}
					})
					return;
				} else {
					//console.log("跳转集装箱小程序...");
					extraData = {
						token: getTokenId(),
						vehicleInfoList: this.vehicleInfoList
					};
					// console.log("extraData:" + JSON.stringify(extraData));
					//跳转集装箱预约小程序
					wx.navigateToMiniProgram({
						appId: 'wx07e128202169f81e',
						path: '/pages/empty/empty',
						extraData: extraData,
						success(res) {
							//console.log("跳转集装箱预约小程序");
						}
					})
					return;
				}
			}
		}
	}
</script>

<style scoped>
	.index-top {
		position: relative;
		height: 600upx;
		background: no-repeat url(data:image/png;base64,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);
		background-size: 100%;
	}

	.nav-icon-index {
		color: #333333;
		font-size: 28upx;
		margin-top: 20upx;
		height: 200upx;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-center;
		align-items: center;
		justify-content: space-around;
	}

	.nav-icon {
		border-radius: 5px;
		color: #333333;
		font-size: 28upx;
		height: 200upx;
		width: 29%;
		text-align: center;
		background-color: #FFFFFF;
		box-shadow: 3px 3px 1px #DDDDDD;
	}

	.nav-icon:active {
		top: 2px;
		box-shadow: 0 12px 12px 0 rgba(0, 0, 0, 0.24);
	}

	.green-travel,.harvester,.container{
		background-image:url('~@/static/tab-bar/index/ltc.png');
		width:120rpx;
		height:120rpx;
		margin-left: 50rpx;
		margin-top: 20rpx;
		background-repeat:no-repeat;
		background-size: 120rpx;
	}
	.harvester{
		width:125rpx;
		height:100rpx;
		background-image:url('~@/static/tab-bar/index/sgj.png');
	}
	.container{
		background-position: center;
		background-image:url('~@/static/tab-bar/index/jzx.png');
	}
	.nav-icon-image {
		margin-top: 10upx;
		width: 120upx;
		height: 120upx;
		display: inline-block;
	}

	.nav-icon-image-lhsgj {
		margin-top: 10upx;
		width: 100upx;
		height: 100upx;
		display: inline-block;
	}
</style>
