<template>
	<view class="welcome">
		<view class="title">中国
		<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/welcome-text.png" class="t-img" mode="aspectFit"></image>
		服务</view>
		<view class="welImg">
			<!-- <image src="/static/tab-bar/index/welcome-xx.png" class="img-xx" mode="aspectFit"></image>
			<image src="/static/tab-bar/index/welcome.png" class="img" mode="aspectFit"></image> -->
		</view>
		<view class="title2">高速ETC&nbsp;&nbsp;畅行全中国</view>
		<view class="title3-content">
			<view class="bg"></view>
			<view class="title3">专业车道&nbsp;&nbsp;&nbsp;快人一步</view>
		</view>
		<button class="btn" @click="begain">开始办理ETC</button>
		<view class="opyright-bottom">广西捷通高速科技有限公司</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import Api from '@/common/api/index.js';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getTokenId,
		setStore,
		getAccountId,
		removeStore
	} from '@/common/storageUtil.js';
	import {
		request,
		getCarList
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			tLoading,
		},
		data() {
			return {
				isLoading: false,
				persionAccountId:"",
				isCorp:false,
				accountList:[]
			}
		},
		onLoad() {
			// 扫码进入标致
			setStore("scan", 1)
			uni.getLocation({
			    type: 'wgs84',
			    success: function (res) {
			        console.log('当前位置的经度：' + res.longitude);
			        console.log('当前位置的纬度：' + res.latitude);
			    }
			});
		},
		methods: {
			begain(){
				if (!getTokenId()) {
					uni.reLaunch({
						url: "/pages/login/login"
					})
				}else{
					this.getAccountList(()=>{
						if(this.isCorp){
							uni.reLaunch({
								url:"/pagesA/etc/add-car/addCarType?type=corp"
							})
						}else{
							this.reqUserInfo()
						}
					});
				}
			},
			//获取个人账户列表
			getAccountList(cb) {
				this.isLoading = true
				let data = {
					'fileName': Api.getAccountList.method,
					'data': {
						tokenId: getTokenId()
					}
				}
				request(Api.getAccountList.url, data, (res) => {
					this.isLoading = false
					this.accountList = res.data || []
					this.persionAccountId = ""
					for (let obj of this.accountList) {
						if (obj.accountType === "1") {
							this.persionAccountId = obj.accountId
							setStore('persionAccountId', this.persionAccountId)
						}
					}
					// 当前个人id和缓存id不一致，当前户为企业类
					if(this.persionAccountId && getAccountId() && this.persionAccountId != getAccountId()){
						this.isCorp = true
					}
					cb()
				}, (error) => {
					this.isLoading = false
				})
			},
			//获取用户基本信息
			reqUserInfo() {
				let params = {
					tokenId: getTokenId()
				};
				let data = {
					'fileName': Api.getUserUser.method,
					'data': params
				};
				request(Api.getUserUser.url, data, (res) => {
					console.log("基本信息")
					// console.log(res)
					this.isLoading = false;
						//已认证且有个人账户的时候
						if (res.isCertified) {
							if(this.accountList.length === 1 && this.persionAccountId){
								removeStore("scan")
								uni.reLaunch({
									url:"/pagesA/etc/add-car/addCarType?type=persion"
								})
							}else{
								uni.redirectTo({
									url: "/pagesB/personal/my-car/my-car-info?type=selectUser"
								});
							}
						} else {
							uni.reLaunch({
								url:'/pagesA/etc/certification-new/certification-confirm'
							})
						}
				}, (msg) => {
					this.isLoading = false;
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.welcome{
		position: absolute;
		height:100%;
		width:100%;
		background: #fff;
		color: #1D82D2;
		display: flex;
		flex-direction: column;
		align-items: center;
		.title{
			font-size: 64rpx;
			letter-spacing: 4.1px;
			margin-top:123rpx;
			color: #225A87;
			.t-img{
				width: 168rpx;
				height:42rpx;
				margin: 0 20rpx;
			}
		}
		.welImg{
			width: 626rpx;
			height: 47%;
			.img,.img-xx{
				position: absolute;
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
			}
			.img{
				top: 130rpx;
				height:calc(100% - 130rpx)
			}
			.img-xx{
				height: 91%;
				top: 20rpx;
				left: 0;
			}
		}
		.title2{
			margin-top: 50rpx;
			font-size: 44rpx;
			letter-spacing: 5.21px;
		}
		.title3-content{
			position: relative;
			margin-top:20rpx;
			.bg{
				width: 330rpx;
				height:50rpx;
				opacity: 0.1;
				background: #1D82D2;
				border-radius: 13px;
				border-radius: 13px;
			}
			.title3{
				font-size: 32rpx;
				letter-spacing: 0.93px;
				margin-top: 18rpx;
				position: absolute;
				top: -16rpx;
				width: 330rpx;
				text-align: center;
			}
		}
		.btn{
			margin-top: 126rpx;
			width: 720rpx;
			height: 104rpx;
			line-height: 104rpx;
			font-size: 32rpx;
			color: #1D82D2;
			background: #fff;
			border: 1px solid #1D82D2;
		}
		.btn:active{
			background:#000;
			opacity: .1;
		}
		.opyright-bottom{
			position: absolute;
			bottom:20rpx;
			font-size: 24rpx;
			color: #717171;
		}
	}
</style>
