<template>
	<view>
			<form>
				<view class="cu-form-group">
					<view class="title">新密码</view>
					<input name="a" v-model="password" maxlength="32" type="password"></input>
				</view>
				<view class="cu-form-group">
					<view class="title">请确认密码</view>
					<input name="b" v-model="repeatPassword" maxlength="32" type="password"></input>
				</view>
			</form>
			<view class="certification">
				<TButton :title="title" @clickButton="updatePassword" :isLoadding="isBtnLoader"/>
			</view>
			<tLoading :isShow="isLoading"/>
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue';
	import Api from '@/common/api/index.js'
	import {encrypt,decrypt}  from "@/common/crypto/aesutils.js"
    import { getTokenId,setCurrUserInfo } from '@/common/storageUtil.js'
	import {
		request
	} from '@/common/request-1/requestFunc.js'
    export default {
		components:{
			TButton,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				isEdit:false,
				title:'重置密码',
				password:'',
				repeatPassword:'',
				mobile:''
			}
		},
		onReady() {
			this.reqUserInfo();
		},
		methods: {
			updatePassword(){
				if(!this.password ){
					uni.showToast({title:'请输入新密码',icon:'none'});
					return ;
				}
				if(!this.repeatPassword ){
					uni.showToast({title:'请输入确认密码',icon:'none'});
					return ;
				}
				if(this.repeatPassword != this.password){
					uni.showToast({title:'输入的密码前后不一致，请确认',icon:'none'});
					return ;
				}
				this.isLoading = true;
				let data = {
					'fileName': Api.changePassword.method,
					'data': {
						tokenId:getTokenId(),
						mobile:this.mobile,
						password:this.repeatPassword,
						oldPassword:''
					}
				};
				request(Api.changePassword.url, data,(res) => {
					this.isLoading = false;
					setTimeout(()=>{
						uni.showModal({
							title:"提示",
							content:'修改成功',
							success: function (res) {
								if (res.confirm) {
									uni.navigateBack({
										delta:1
									})
								} else if (res.cancel) {
									uni.navigateBack({
										delta:1
									})
								}
							}
						})
					},1000)
				}, (msg)=>{
					this.isLoading = false;
				},(err) => {
					this.isLoading = false;
				})
			},
			//获取用户基本信息
			reqUserInfo() {
				let data = {
					'fileName': Api.getUserUser.method,
					'data': {
						tokenId: getTokenId()
					}
				};
				request(Api.getUserUser.url, data, (res) => {
					// 这里正常数据返回
					let userInfo = res;
					this.mobile = res.mobile;
					setCurrUserInfo(userInfo);
				})
			},
		}
	}
</script>

<style>
	.c-title{
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #FFFFFF;
	}
	.must-input{
		font-size: 36upx;
		color: #F00;
		font-weight: bold;
		background: #FFFFFF;
	}
	.sendcode{
		color: #1684fb;
	}
</style>
