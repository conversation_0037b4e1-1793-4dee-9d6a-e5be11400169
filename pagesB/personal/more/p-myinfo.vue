<template>
	<view>
		<view v-show="!isLoading">
		    <view class="c-title">个人资料</view>
				<form>
	
					<view class="cu-form-group">
						<view class="title">昵称</view>
						<input name="b" :value="wxInfo.nickName" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">性别</view>
						<input name="b"v-if='UserInfo.gender'  :value="UserInfo.gender == '2' ? '男':'女'" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">电话号码</view>
						<input name="b" :value="UserInfo.phone" disabled></input>
					</view>
					
					<!-- <view class="cu-form-group">
						<view class="title">证件类型</view>
						<input name="b" value="身份证" ></input>
					</view> -->
					<!-- <view class="cu-form-group">
						<view class="title">地址</view>
						<input name="e" :value="address"></input>
						<text v-if="isEdit" class='cuIcon-write text-blue'></text>
					</view> -->
				</form>
		</view>
		<tLoading :isShow="isLoading"/>
	</view>
</template>

<script>
	
	import tLoading from '@/components/common/t-loading.vue';
	import Api from '@/common/api/index.js'
	import {personalType} from'@/common/systemConstant.js'
    import {getTokenId, getWxAccount, getStore} from '@/common/storageUtil.js'
	import {
		request
	} from '@/common/request-1/requestFunc.js'
    export default {
		components:{
			tLoading
		},
		data() {
			return {
				isLoading: true,
				UserInfo:{},
				wxInfo:{}
			}
		},
		onReady() {
			this.reqUserInfo();
			
		},
		methods: {
			//获取用户信息
			reqUserInfo(){
				this.isLoading = true;
				
				let data = {
					
					'data': {}
				};
				request(this.$interfaces.getUserInfo, data,(res) => {
					this.UserInfo = res.data;
					this.$nextTick(()=>{
						this.isLoading = false
					});
					// console.log(this.UserInfo);
				}, (msg)=>{
					this.isLoading = false;
				},(err) => {
					this.isLoading = false;
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.c-title{
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #FFFFFF;
	}
	.must-input{
		font-size: 36upx;
		color: #F00;
		font-weight: bold;
		background: #FFFFFF;
	}
	.avatar{
		width: 88rpx;
		height: 88rpx;
		border-radius: 50%;
		position: absolute;
		left: 100rpx;
	}
</style>
