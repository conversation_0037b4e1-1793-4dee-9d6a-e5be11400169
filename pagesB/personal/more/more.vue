<!-- 个人中心 -->
<template>
	<view class="more">
		<view class="nav-list">
			<view class="cu-list menu">
				<view class="cu-item click-bg" @tap="goClick('3')">
					<view class="content u-f-a">
						<view class="png re-pwd"></view>
						<text class="text-grey">重置密码</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
						<view class="line"></view>
					</view>
				</view>
				
				<!-- <view class="cu-item click-bg" @tap="goClick('1')">
					<view class="content u-f-a">
						<view class="png change-mobile"></view>
						<text class="text-grey">更换手机号码</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
						<view class="line"></view>
					</view>
				</view> -->
				<view class="cu-item click-bg" @tap="accountSync">
					<view class="content u-f-a">
						<view class="png account-sync"></view>
						<text class="text-grey">账户同步</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
						<view class="line"></view>
					</view>
				</view>
				
				<view class="cu-item click-bg" @tap="goClick('2')">
					<view class="content u-f-a">
						<view class="png logout"></view>
						<text class="text-grey">退出登录</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import { uni_encodeURIComponent } from '@/common/helper.js';
	import Api from '@/common/api/index.js';
	import store from 'store/index.js';
	import {getTokenId} from '@/common/storageUtil.js';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading:false
			}
		},
		methods: {
			goClick(index){
				setTimeout(()=>{
					switch (index){
						case '1':
							uni.showModal({
								title: "提示",
								content: "暂未开放，敬请期待...",
								success: function (res) {
									if (res.confirm) {
										return;
									} else if (res.cancel) {
										return;
									}
								}
							});
							break;
						case '2':
							this.loginOut();
							break;
						case '3':
							this.resetPassWord();
							break;
					}
				},500)
				
			},
			loginOut(){
				this.isLoading = true
				//清除缓存
				try{
					uni.clearStorageSync();
				}catch (e) {
				}
				getApp().globalData = {
					tokenId:'',
				    currentPerson:{},
					currentCar:{},
					productinfo:{},
					wxAccountInfo:{},
					returnCode:"",
					accountId:'',
					openId:"",
					session_key:"",
					phone:""
				}
				store.commit('CLEAR_DATA');
				setTimeout(()=>{
					this.isLoading = false
					uni.reLaunch({
						url:'/pages/tab-bar/index/index'
					});
				},1000)
			},
			resetPassWord(){
				uni.navigateTo({
					url:'/pagesB/personal/more/resetpassword'
				});
			},
			accountSync(){
				let data = {
					'fileName': Api.accountSync.method,
					'data': {
						tokenId: getTokenId()
					}
				}
				this.isLoading = true
				request(Api.accountSync.url, data, (res) => {
					uni.showModal({
						title: "提示",
						content: "同步成功",
						success: function (res) {
							if (res.confirm) {
								return;
							} else if (res.cancel) {
								return;
							}
						}
					});
					this.isLoading = false
				}, (error) => {
					this.isLoading = false
				}, (error) => {
					this.isLoading = false
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content>text{
		margin-left: 15upx;
		font-size: 34rpx;
		color: #4D4D4D;
	}
	.cu-list{
		.cu-item{
			height:104rpx;
			padding-left:0;
			padding-right:40rpx;
			&:after{
				border:none;
			}
		}
	}
	.content{
		// .re-pwd{
		// 	background-image:url('~@/static/tab-bar/personal/etcpwd.png');
		// }
		// .change-mobile{
		// 	background-image:url('~@/static/tab-bar/personal/etcphone.png');
		// }
		// .logout{
		// 	background-image:url('~@/static/tab-bar/personal/logout.png');
		// }
		// .account-sync{
		// 	background-image:url('~@/static/tab-bar/personal/accountSync.png');
		// }
		.re-pwd,
		.change-mobile,
		.logout,
		.account-sync,
		.png{
			width:48rpx;
			height:48rpx;
			margin: 0 48rpx 0 38rpx !important;
			background-repeat:no-repeat;
			background-size: 48rpx;
		}
		.arrow{
			position: absolute;
			right: 40rpx;
			display: flex;
			align-items: center;
			.right-arrow{
				width: 60rpx;
				height: 60rpx;
				background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/right-arr.png');
				background-repeat: no-repear;
				background-size: 60rpx;
			}
		}
	}
	.line{
		border:0.5px solid #F4F5F7;
		position: absolute;
		width: 616rpx;
		bottom: 0px;
		right: 0px;
	}
	.more{
		padding-top: 30upx;
	}
	.personal-title{
		border-radius: 20upx;
		background: #FFFFFF;
	}
	.p-item{
		padding: 44upx 46upx 28upx;
		
	}
	.p-item>view:last-of-type{
		font-size: 28upx;
		margin-top: 20upx;
		color: #333333;
	}
	.p-item>view:first-of-type{
		color: #2484E8;
		font-size: 36upx;
	}
	
	.click-bg:active{
		background: #CCC;
	}

</style>
