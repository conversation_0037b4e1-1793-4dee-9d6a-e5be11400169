<template>
	<view class="smsLogin">
		<view class="GX-logo">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc-logo.png"
				class="home-img"></image>
			<view class="login-title">桂小通(广西捷通)</view>
		</view>
		<!--  #ifdef MP-WEIXIN -->
		<view class='btn u-f-ajc t-padding' style="margin-top: 60rpx;">
			<button class="cu-btn lg" style="background-color:#0066E9;" @click="toLogin">
				<view class="login-box g-flex g-flex-align-center">
					<view class="login-text">
						登录账号
					</view>
				</view>
			</button>
		</view>
		<view class="bottom" style="height:120rpx">
			<tabbar current="spread" />
		</view>
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tabbar from "@/components/base/tab-bar/index.vue"
	export default {
		components: {
			tabbar
		},
		data() {
			return {}
		},
		methods: {
			toLogin() {
				uni.redirectTo({
					url: '/pages/login/p-login'
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.GX-logo {
		width: 100%;
		text-align: center;
	}



	.home-img {
		height: 180rpx;
		width: 170rpx;
	}

	.login-title {
		color: #1D2225;
		font-size: 36rpx;
	}

	.smsLogin {
		padding-top: 40rpx;



		.btn {

			width: 100%;


			.login-btn {

				width: 730rpx;
				height: 104rpx;
				font-size: 32rpx;
				line-height: 104rpx;
				text-align: center;

			}
		}

	}

	.resetPwd {
		margin: 20rpx;

		.resetPwd-text {
			padding: 10rpx 0;
			color: #999;
		}
	}

	.login-box {
		.login-img {
			width: 60rpx;
			height: 60rpx;
		}

		.login-text {
			font-size: 34rpx;
			color: #fff;
			font-weight: 400;
			margin-left: 10rpx;
			color: #fff
		}
	}
</style>