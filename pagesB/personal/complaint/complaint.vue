<template>
	<view>
		<view class="c-info">
			<view class="img-title">客户信息(*为必填项)</view>
			<form>
				<view class="cu-form-group">
					<view class="title">*姓名:</view>
					<input v-model="identity.consumer"></input>
				</view>
				<view class="cu-form-group">
					<view class="title">*手机号码:</view>
					<input v-model="identity.consumerTel" disabled class="disabled-input"></input>
				</view>
				<view class="cu-form-group" v-if="inputPlateNum">
					<view class="title">*车牌号码:</view>
					<picker :model="selectPlateNum" :range="carInfoList" range-key="plateNum"
						@change="PickerplateNumOptions">
						<view class="picker">
							{{carInfoList[selectPlateNum].plateNum}}
						</view>
					</picker>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view>
				<view class="cu-form-group" v-else>
					<view class="title">*车牌号码:</view>
					<input v-model="identity.plateNum" @tap="showKeyboard"></input>
					<keyboard :plateNum="identity.plateNum" :plateColor="identity.plateColor" @keyboard="keyboardChange"
						@updatePlateNum="keyboardUpdate" :show.sync="show" active-border="#0deafe" base-border="38f8f8f"
						extra-key="完成">
					</keyboard>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view>
				<view class="cu-form-group">
					<view class="title">*车牌颜色:</view>
					<picker :model="identity.plateColor" :range="plateColorOptions" @change="PickerplateColorOptions"
						disabled>
						<view class="picker">
							{{plateColorOptions[identity.plateColor]}}
						</view>
					</picker>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view>
				<view class="cu-form-group">
					<view class="title">*通行卡卡号:</view>
					<picker :model="selectCardIndex" :range="cardList" range-key="lebel" @change="PickerCardIdOptions"
						disabled="selectCardEnable">
						<view class="picker">
							{{cardList[selectCardIndex].lebel}}
						</view>
					</picker>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view>
			</form>
		</view>

		<view class="c-info">
			<view class="img-title">投诉信息(*为必填项)</view>
			<form>
				<view class="cu-form-group">
					<view class="title">投诉类型:</view>
					<input v-model="complaintTypeOptions[complaintType]" disabled class="disabled-input"></input>
				</view>
				<!-- <view class="cu-form-group">
					<view class="title">*三级投诉类型:</view>
					<picker :model="selectComplaints" :range="complaintChildClassOptions" range-key="name" @change="pickerComplaints">
						<view class="picker">
							{{complaintChildClassOptions[selectComplaints].name}}
						</view>
					</picker>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view> -->
				<view class="cu-form-group">
					<view class="title">*发行省份:</view>
					<picker :value="product" :range="selectProductOptions" range-key="lebel"
						@change="PickerProductOptions" :disabled="selectProvinceEnable">
						<view class="picker">
							{{selectProductOptions[product].lebel}}
						</view>
					</picker>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view>
			</form>
		</view>

		<view class="c-info">
			<view class="img-title">投诉内容(*为必填项)</view>
			<form>
				<view class="cu-form-group">
					<view class="title">现金发票号:</view>
					<input v-model="identity.invoiceNum"></input>
				</view>
				<view class="cu-form-group">
					<view class="title">*收费车型:</view>
					<view class="picker">
						{{carType ? vehicleTypeOptions.get(carType+'') : ''}}
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">*扣费时间:</view>
					<timeSelector showType="dateToTime" beginDate="2019-01-01" :endDate="endTime" beginTime="00:00:00"
						endTime="23:59:59" @btnConfirm="datePayChange" class="picker-select">
						<view>
							{{payStartDate}}
						</view>
					</timeSelector>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view>
				<view class="cu-form-group">
					<view class="title">*通行开始日期:</view>
					<timeSelector showType="dateToTime" beginDate="2019-01-01" :endDate="endTime" beginTime="00:00:00"
						endTime="23:59:59" @btnConfirm="(e)=>passDateChange(e,'begain')">
						<view>
							{{passBegainDate}}
						</view>
					</timeSelector>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view>
				<view class="cu-form-group">
					<view class="title">*通行截止日期:</view>
					<timeSelector showType="dateToTime" beginDate="2019-01-01" :endDate="endTime" beginTime="00:00:00"
						endTime="23:59:59" @btnConfirm="(e)=>passDateChange(e,'end')">
						<view>
							{{passEndDate}}
						</view>
					</timeSelector>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</view>
				<view class="cu-form-group">
					<view class="title">*通行出口收费站名称:</view>
					<input v-model="identity.caseSite"></input>
				</view>
				<view class="cu-form-group align-start">
					<view class="title">*事件描述：</view>
					<textarea v-model="identity.caseDesc" maxlength="-1" auto-height placeholder="请输入事件描述"></textarea>
				</view>
				<view class="cu-form-group align-start">
					<view class="title">*诉求：</view>
					<textarea v-model="identity.appeal" maxlength="-1" auto-height placeholder="请输入您的诉求"></textarea>
				</view>

				<view class="cu-bar bg-white border">
					<view class="action">
						附件上传
					</view>
					<view class="action">
						{{imgList.length}}/4
					</view>
				</view>
				<view class="cu-form-group">
					<view class="grid col-4 grid-square flex-sub">
						<view class="bg-img" v-for="(item,index) in imgList" :key="index" @tap="ViewImage"
							:data-url="imgList[index]">
							<image :src="imgList[index]" mode="aspectFill"></image>
							<view class="cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
								<text class='cuIcon-close'></text>
							</view>
						</view>
						<view class="solids" @tap="ChooseImage" v-if="imgList.length<4">
							<!-- <text class='cuIcon-cameraadd'></text> -->
							<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/camer.png" class="cameraadd"></image>
							<view class="upload-text">上传照片</view>
						</view>
					</view>
				</view>
			</form>
		</view>
		<view>
			<TButton title="立即投诉" @clickButton="goNext" :isLoadding="isBtnLoader" />
		</view>
		<view class="_cpimg">
			<canvas id="_myCanvas" canvas-id="_myCanvas" :style="{width:cWidth+'px',height:cHeight+'px'}" />
		</view>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import timeSelector from '@/pagesB/components/wing-time-selector/wing-time-selector.vue';
	import Api from '@/common/api/index.js'
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue"
	import {
		complaintProvinceList,
		vehicleTypeOptions
	} from '@/common/systemConstant.js';
	import keyboard from '@/components/mpvue-keyboard/platenum-keyboard.vue'
	import {
		getCurrUserInfo,
		setCurrUserInfo,
		getTokenId,
		getAccountId
	} from '@/common/storageUtil.js';
	import {
		pathToBase64,
		base64ToPath
	} from '../../../js_sdk/gsq-image-tools/image-tools/index.js'
	import {
		isLicensePlate
	} from '@/common/helper.js';
	import {
		uni_encodeURIComponent,
		uni_decodeURIComponent
	} from '@/common/helper.js';
	import {
		request,
		getCarList
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			TButton,
			tLoading,
			keyboard,
			timeSelector
		},
		data() {
			return {
				isLoading: false,
				isBtnLoader: false,
				identity: {
					unitPro: '99',
					acceptUser: '客户自助',
					complainSource: 7,
					acceptTime: '',
					consumer: '',
					consumerTel: '',
					plateNum: '',
					plateColor: 0,
					cardNo: '',
					caseTime: '2020-01-01',
					caseSite: '',
					invoiceNum: '',
					caseDesc: '',
					appeal: '',
				},
				plateColorOptions: ["请选择车牌颜色", "蓝色", "黄色", "黑色", "白色", "渐变绿色", "黄绿双拼色", "蓝白渐变色"],
				complaintTypeOptions: ["ETC使用-通行费争议"],
				// complaintChildClassOptions: [
				// 	{
				// 		"name": "请选择争议类型",
				// 		"id": "0"
				// 	}
				// ],
				// selectComplaints:0,
				productOptions: [],
				//provinceOptions:["请选择通行省份","北京市","天津市","河北市"],
				//provinceOptions:[{value:'11',label:'北京市'},{value:'12',label:'天津市'},{value:'22',label:'河北省'}],
				provinceOptions: [],
				provinceProxyList: [],
				complaintProvinceList,
				selectProductOptions: [],
				cardList: [],
				selectCardIndex: 0,
				selectCardEnable: true,
				selectProvinceEnable: false,
				//plateColor:0,
				complaintType: 0,
				product: 0,
				selectProductId: '',
				provinceId: 0,
				imgList: [],
				imgBase64_1: '',
				imgBase64_2: '',
				imgBase64_3: '',
				imgBase64_4: '',
				imageNum: 0,
				//date:'2020-01-01',
				show: false,
				selectedProvince: '请选择通行省份',
				selectedProvinceText: '请选择通行省份',
				selectedProvinceId: '',
				isLoaded: 0,
				carInfoList: [],
				selectPlateNum: 0,
				inputPlateNum: true,
				vehicleTypeOptions,
				endTime: '2030-12-31',
				caseDate: '2020-01-01',
				payStartDate: '2020-01-01 00:00:00',
				caseTime: '00:00',
				passBegainDate: "2020-01-01 00:00:00",
				passEndDate: "2020-01-08 00:00:00",
				complaintItem: {},
				carType: '',
				maxWidth: 750,
				type: 'base64',
				ql: 0.92,
				src: '',
				number: 1,
				fixOrientation: true,
				size: 500000,
				cWidth: 750,
				cHeight: 750
			}
		},
		onLoad: function(event) {
			this.complaintItem = JSON.parse(uni_decodeURIComponent(event.complaintItem));
			console.log("complaintItem:" + JSON.stringify(this.complaintItem));
			this.isLoaded = 0;

			let date = new Date();
			let y = date.getFullYear();
			let MM = date.getMonth() + 1;
			MM = MM < 10 ? ('0' + MM) : MM;
			let d = date.getDate();
			d = d < 10 ? ('0' + d) : d;
			this.endTime = y + '-' + MM + '-' + d;
			console.log('endTime')
			console.log(this.endTime)
			let userInfo = getCurrUserInfo();
			this.identity.consumerTel = userInfo.mobile;


			this.selectProductOptions = [];
			this.selectProductOptions.push({
				"id": "00",
				"lebel": "请选择发行省份",
				"name": "请选择发行省份"
			});
			complaintProvinceList.forEach((item, index) => {
				this.selectProductOptions.push(item);
			});
			//this.selectProductOptions = complaintProvinceList;
			this.cardList.push({
				"id": "0000",
				"lebel": "请选择通行卡卡号"
			});

			this.reqVerhicleList();
		},
		methods: {
			//校验时间选择是否超过当前时间
			checkTimeCurrent(selectTime) {
				let currentDate = new Date()
				let hour = currentDate.getHours()
				let minutes = currentDate.getMinutes()
				let seconds = currentDate.getSeconds()
				let time = ` ${hour}:${minutes}:${seconds}`
				if (new Date(selectTime).getTime() > new Date(this.endTime + time).getTime()) {
					uni.showToast({
						title: "时间选择不能超过当前时间",
						duration: 2000,
						icon: 'none'
					})
					return false
				}
				return true
			},
			datePayChange(e) {
				if (!this.checkTimeCurrent(e.key)) {
					return
				}
				this.payStartDate = e.key;
			},
			passDateChange(e, type) {
				if (!this.checkTimeCurrent(e.key)) {
					return
				}
				if (type === "begain") {
					let begain = new Date(e.key).getTime()
					let end = new Date(this.passEndDate).getTime()
					let day = 24 * 60 * 60 * 1000 * 7
					if (end - begain > day) {
						uni.showToast({
							title: "时间发生开始结束时间间隔不大于7天",
							duration: 2000,
							icon: 'none'
						})
						return
					}
					this.passBegainDate = e.key
				} else {
					let begain = new Date(this.passBegainDate).getTime()
					let end = new Date(e.key).getTime()
					let day = 24 * 60 * 60 * 1000 * 7
					if (end - begain > day) {
						uni.showToast({
							title: "时间发生开始结束时间间隔不大于7天",
							duration: 2000,
							icon: 'none'
						})
						return
					}
					this.passEndDate = e.key
				}
			},
			PickerplateColorOptions(e) {
				this.identity.plateColor = e.detail.value;
			},
			//选择三级投诉类型
			pickerComplaints(e) {
				this.selectComplaints = e.detail.value;
			},
			PickerProductOptions(e) {
				this.product = e.detail.value;

				//匹配出客服系统中的ID
				this.productOptions.forEach((item, index) => {
					if (item.issuerOwnerId != "") {
						if (this.selectProductOptions[this.product].id == item.issuerOwnerId.substring(0, 2)) {
							this.selectProductId = item.id;
							//console.log("选择了发行方编号:"+this.selectProductId);
							//console.log("发行方信息:"+JSON.stringify(item));
						}
					}
				});
			},
			PickerCardIdOptions(e) {
				this.selectCardIndex = e.detail.value;
				this.identity.cardNo = this.cardList[selectCardIndex].lebel;

				//设置发行方
				if (this.selectProductOptions.length > 0) {
					//console.log("开始自动选择发行方...");
					this.selectProductOptions.forEach((item, index) => {
						if (item.issuerOwnerId != '' && this.identity.cardNo != '') {
							//console.log("item.id:"+item.id);
							if (item.id == this.identity.cardNo.substring(0, 2)) {
								this.product = index;
								var issuerId = this.identity.cardNo.substring(0, 4);
								//this.selectProvinceEnable = true;
								//console.log("选中发行方..."+item.id);

								//匹配出客服系统中的ID
								this.productOptions.forEach((tempItem, tempIndex) => {
									if (tempItem.issuerOwnerId != "") {
										var issuerOwnerId = tempItem.issuerOwnerId.substring(0, 2) +
											tempItem.issuerOwnerId.substring(4, 6);

										if (this.selectProductOptions[this.product].id == tempItem
											.issuerOwnerId.substring(0, 2) &&
											issuerOwnerId == issuerId) {
											//console.log("issuerOwnerId:"+issuerOwnerId);
											//console.log("issuerId:"+issuerId);
											this.selectProductId = tempItem.id;
											//console.log("选择了发行方编号:"+this.selectProductId);
											//console.log("发行方信息:"+JSON.stringify(item));
										}
									}
								});
							}
						}
					});
				}
			},
			showKeyboard() {
				this.show = true;
			},
			keyboardChange(e) {
				this.identity.plateColor = e;
			},
			keyboardUpdate(plateNum) {
				this.identity.plateNum = plateNum;
				//console.log("车牌号:"+this.identity.plateNum);
			},
			PickerplateNumOptions(e) {
				this.selectPlateNum = e.detail.value;
				if (this.carInfoList[this.selectPlateNum].plateNum == "手动输入车牌号码") {
					//console.log("手动输入车牌");
					this.inputPlateNum = false;
					this.identity.plateNum = '';
					this.plateColor = 0;
					this.identity.cardNo = '';
					this.product = 0;
				} else if (this.carInfoList[this.selectPlateNum].plateNum == "请选择车牌") {
					this.identity.plateNum = '';
					this.plateColor = 0;
					this.identity.cardNo = '';
					this.product = 0;
					this.carType = "";
				} else {
					this.identity.plateNum = this.carInfoList[this.selectPlateNum].plateNum;
					//this.plateColor = parseInt(this.carInfoList[this.selectPlateNum].plateColor)+1;
					this.identity.plateColor = parseInt(this.carInfoList[this.selectPlateNum].plateColor) + 1;
					//查找卡号
					this.selectCardIndex = 0;
					this.identity.cardNo = '';
					this.product = 0;

					this.reqObuInfo(this.carInfoList[this.selectPlateNum].vehicleId);
					this.checkCarType(this.carInfoList[this.selectPlateNum].vehicleId);
				}
			},
			goNext() {
				if (this.imgList.length > 0) {
					this.imageNum = 0;
					this._cpImg(0);
				} else {
					//提交接口
					this.submitOrder();
				}
			},
			submitOrder() {
				if (this.isBtnLoader) {
					return;
				}
				if (this.identity.consumer == '') {
					uni.showModal({
						title: '提示',
						content: "请输入客户姓名"
					});
					return;
				}
				if (this.identity.consumerTel == '') {
					uni.showModal({
						title: '提示',
						content: "请输入手机号码"
					});
					return;
				}
				if (this.identity.plateNum == '') {
					uni.showModal({
						title: '提示',
						content: "请输入车牌号码"
					});
					return;
				}
				if (this.identity.plateColor == 0) {
					uni.showModal({
						title: '提示',
						content: "请选择车牌颜色"
					});
					return;
				}
				if (this.identity.cardNo == "" || this.identity.cardNo.length < 20) {
					uni.showModal({
						title: '提示',
						content: "您选择的车牌下无ETC卡，请选择其他车辆！"
					});
					return;
				}
				if (this.product == 0) {
					uni.showModal({
						title: '提示',
						content: "请选择发行方"
					});
					return;
				}
				if (this.identity.caseSite == '') {
					uni.showModal({
						title: '提示',
						content: "请输入通行出口收费站名称"
					});
					return;
				}
				if (this.identity.caseDesc == '') {
					uni.showModal({
						title: '提示',
						content: "请输入事件描述"
					});
					return;
				}
				if (this.identity.appeal == '') {
					uni.showModal({
						title: '提示',
						content: "请输入用户诉求"
					});
					return;
				}
				if ((this.identity.plateColor - 1) == 4) {
					if (this.identity.plateNum.length < 8) {
						uni.showModal({
							content: "请核对新能源车牌信息",
						})
						return;
					}
				} else {
					if (this.identity.plateNum.length >= 8) {
						uni.showModal({
							content: "请核对车牌长度信息",
						})
						return;
					}
				}
				// 校验数据是否正确
				const isLicense = isLicensePlate(this.identity.plateNum);
				if (!isLicense) {
					uni.showModal({
						content: "请核对车牌信息",
					});
					return;
				}

				let date = new Date();
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				let nowTime = `${y}${MM}${d}${h}${m}${s}`;

				let params = {
					tokenId: getTokenId(),
					unitPro: this.identity.unitPro,
					acceptUser: this.identity.acceptUser,
					complainSource: this.identity.complainSource,
					acceptTime: nowTime,
					consumer: this.identity.consumer,
					consumerTel: this.identity.consumerTel,
					plateNum: this.identity.plateNum,
					plateColorType: parseInt(this.identity.plateColor) - 1,
					cardNum: this.identity.cardNo,
					cardInfo: '',
					obuNum: '',
					obuBrand: '',
					complainLevel: '1', //1-一般  2-紧急
					complainClass: '302', //ETC使用  a0352b175c114c67aa9b30bd618625ab
					//complainType:'a0352b175c114c67aa9b30bd618625ab',//通行费争议编号  a0352b175c114c67aa9b30bd618625ab
					complainType: this.complaintItem.complaintId,
					// complainSubType:this.complaintChildClassOptions[this.selectComplaints].id,
					//issuer:this.productOptions[this.product].issuerId,
					//issuer:this.productOptions[this.product].id,
					issuer: this.selectProductId,
					newDefIds: this.selectedProvinceId,
					caseTime: this.changeTimeType(this.passBegainDate),
					endCaseTime: this.changeTimeType(this.passEndDate),
					chargingTime: this.changeTimeType(this.payStartDate),
					vehicleType: this.carType, //车辆类型
					caseSite: this.identity.caseSite,
					invoiceNum: this.identity.invoiceNum,
					coopChannel: '',
					caseDesc: this.identity.caseDesc,
					appeal: this.identity.appeal,
					base64Image1: this.imgBase64_1,
					base64Image2: this.imgBase64_2,
					base64Image3: this.imgBase64_3,
					base64Image4: this.imgBase64_4,
				};

				console.log(params);
				this.isLoading = true;

				let data = {
					'fileName': Api.submitComplaintService.method,
					'data': params
				};
				console.log(data)
				request(Api.submitComplaintService.url, data, (res) => {
					this.isLoading = false;
					console.log("提交工单返回结果：" + JSON.stringify(res));

					uni.showModal({
						title: '提示',
						content: '投诉工单提交成功，请耐心等待...',
						success: function(res) {
							uni.navigateBack({
								delta: 2
							});
						}
					});

				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			},
			//时间转换 2020-01-01 00:00:00 - 20200101000000
			changeTimeType(time) {
				let changeTime = time.split('-').join('').split(':').join('').split(' ').join('')
				return changeTime
			},
			getIssuerList() { //获取发行方列表
				let userInfo = getCurrUserInfo();

				this.isLoading = true;
				let params = {
					tokenId: getTokenId(),
					province: '',
					unitType: 1
				};

				let data = {
					'fileName': Api.getComplaintIssuerListService.method,
					'data': params
				};
				request(Api.getComplaintIssuerListService.url, data, (res) => {
					this.isLoading = false;
					this.productOptions = res.list;
					//console.log(JSON.stringify(this.productOptions));
					//this.productOptions.unshift({"id":"000000","name":"请选择发行方","proType":"99","unitType":"1","issuerOwnerId":"000000"});
					//console.log("this.productOptions："+JSON.stringify(this.productOptions));
					this.getProvinceList();
				}, (msg) => {
					uni.showModal({
						title: '提示',
						content: msg
					});

					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				});
			},
			getProvinceList() { //获取服务方列表
				let userInfo = getCurrUserInfo();

				this.isLoading = true;
				let params = {
					tokenId: getTokenId(),
					province: '',
					unitType: 2
				};

				let data = {
					'fileName': Api.getComplaintIssuerListService.method,
					'data': params
				};
				request(Api.getComplaintIssuerListService.url, data, (res) => {
					this.isLoading = false;
					this.provinceProxyList = res.list;

					// this.getChildClassList();
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				});
			},
			getChildClassList() { //获取三级投诉类型
				//console.log("...getChildClassList...");
				let userInfo = getCurrUserInfo();

				this.isLoading = true;
				let params = {
					tokenId: getTokenId(),
					id: this.complaintItem.complaintId,
					subId: ''
				};
				console.log(params)
				let data = {
					'fileName': Api.getComplaintChildClassListService.method,
					'data': params
				};
				request(Api.getComplaintChildClassListService.url, data, (res) => {
					this.isLoading = false;
					console.log("getChildClassList:" + JSON.stringify(res));
					this.complaintChildClassOptions = res.list || [];
					this.complaintChildClassOptions.unshift({
						"name": "请选择争议类型",
						"id": "0"
					});
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				});
			},
			//获取车辆列表
			reqVerhicleList() {
				this.isLoading = true;
				getCarList(getAccountId(), (res) => {
					this.isLoading = false;
					this.carInfoList = res.listData;
					if (this.carInfoList.length > 0) {
						this.carInfoList.unshift({
							"vehicleId": "",
							"plateNum": "请选择车牌",
							"plateColor": 0,
							"vehicleType": 1
						});
						this.getIssuerList();
					} else {
						this.inputPlateNum = false;
						uni.showModal({
							title: '提示',
							content: "1、当前登录手机号下无匹配车辆数据，请核实登录手机号是否为办理ETC时开户预留手机号。\r\n\
						2、如确认登录手机号与办理ETC时开户预留手机号一致但仍无车辆信息，请您联系办理ETC发行服务机构处理。",
							confirmText: '联系客服',
							cancelText: '确定',
							success: function(res) {
								if (res.confirm) {
									let isTokenId = getTokenId();
									var callcenter =
										"https://cc.cywetc.com/kn2/?h5channel=99999908&customerNo=" +
										isTokenId;
									uni.redirectTo({
										url: '/pages/uni-webview/uni-webview?ownPath=' +
											uni_encodeURIComponent(callcenter)
									});
								} else if (res.cancel) {
									uni.navigateBack({
										delta: 1
									});
								}
							}
						});
					}
				}, (msg) => {
					this.inputPlateNum = false;
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: "1、当前登录手机号下无匹配车辆数据，请核实登录手机号是否为办理ETC时开户预留手机号。\r\n\
					2、如确认登录手机号与办理ETC时开户预留手机号一致但仍无车辆信息，请您联系办理ETC发行服务机构处理。",
						confirmText: '联系客服',
						cancelText: '确定',
						success: function(res) {
							if (res.confirm) {
								let isTokenId = getTokenId();
								var callcenter =
									"https://cc.cywetc.com/kn2/?h5channel=99999908&customerNo=" +
									isTokenId;
								uni.redirectTo({
									url: '/pages/uni-webview/uni-webview?ownPath=' +
										uni_encodeURIComponent(callcenter)
								});
							} else if (res.cancel) {
								uni.navigateBack({
									delta: 1
								});
							}
						}
					});
				}, () => {
					this.isLoading = false;
				})
			},
			//获取OBU信息
			reqObuInfo(vehicleId) {
				this.isLoading = true;
				let params = {
					tokenId: getTokenId(),
					vehicleId: vehicleId,
					accountId: getAccountId()
				};
				let data = {
					'fileName': Api.getUserObu.method,
					'data': params
				};
				request(Api.getUserObu.url, data, (res) => {
					this.isLoading = false;
					var isNomalObu = false;
					if (res.result == 1) {
						res.obuData.forEach((item, index) => {
							if (item.status == 1) {
								isNomalObu = true;
								//获取CPU信息
								this.reqCardInfo(item.obuId);
							}
						});
						if (isNomalObu == false) {
							//console.log("无正常状态的OBU");
							this.identity.cardNo = '';
							uni.showModal({
								title: '提示',
								content: "您选择的当前车辆下无ETC卡,请选择其他车辆！",
								confirmText: '确定',
								showCancel: false,
								success: function(res) {
									if (res.confirm) {

									}
								},
							});
							return;
						}
					} else {
						//console.log("当前车辆无OBU");
						this.identity.cardNo = '';
						uni.showModal({
							title: '提示',
							content: "您选择的当前车辆下无ETC卡,请选择其他车辆！",
							confirmText: '确定',
							showCancel: false,
							success: function(res) {
								if (res.confirm) {

								}
							},
						});
					}
				}, (msg) => {
					this.identity.cardNo = '';
					uni.showModal({
						title: '提示',
						content: "您选择的当前车辆下无ETC卡,请选择其他车辆！",
						confirmText: '确定',
						showCancel: false,
						success: function(res) {

						}
					});
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			},
			//获取卡信息
			reqCardInfo(obuId) {
				this.isLoading = true;
				var isNomalObu = false;
				let params = {
					tokenId: getTokenId(),
					obuId: obuId
				};
				let data = {
					'fileName': Api.getObuCard.method,
					'data': params
				};
				request(Api.getObuCard.url, data, (res) => {
					this.isLoading = false;
					if (res.result == 1) {
						this.cardList = [];
						this.cardList.push({
							"id": "0000",
							"lebel": "请选择通行卡卡号"
						});
						this.selectCardEnable = true;

						res.cardData.forEach((item, index) => {
							if (item.status == 1) {
								isNomalObu = true;
								this.identity.cardNo = item.id;
								this.cardList.push({
									"id": item.id,
									"lebel": item.id
								});
							}
						});
						if (isNomalObu == false) {
							//console.log("无正常状态的CPU卡信息...");
							this.identity.cardNo = '';
							uni.showModal({
								title: '提示',
								content: "您选择的当前车辆下无ETC卡,请选择其他车辆！",
								confirmText: '确定',
								showCancel: false,
								success: function(res) {
									if (res.confirm) {

									}
								},
							});
						} else {
							if (this.cardList.length == 2) {
								this.selectCardIndex = 1;
								//this.selectCardEnable = false;
								//设置发行方
								if (this.selectProductOptions.length > 0) {
									this.selectProductOptions.forEach((item, index) => {
										if (item.issuerOwnerId != '' && this.identity.cardNo != '') {
											if (item.id == this.identity.cardNo.substring(0, 2)) {
												this.product = index;
												var issuerId = this.identity.cardNo.substring(0, 4);
												//this.selectProvinceEnable = true;

												//匹配出客服系统中的ID
												this.productOptions.forEach((tempItem, tempIndex) => {
													var issuerOwnerId = tempItem.issuerOwnerId
														.substring(0, 2) + tempItem.issuerOwnerId
														.substring(4, 6);
													if (tempItem.issuerOwnerId != "") {
														if (this.selectProductOptions[this.product]
															.id == tempItem.issuerOwnerId
															.substring(0, 2) &&
															issuerOwnerId == issuerId) {

															this.selectProductId = tempItem.id;
															console.log("选择发行方编号：" + this
																.selectProductId);
															//console.log("issuerOwnerId:"+issuerOwnerId);
															//console.log("issuerId:"+issuerId);
														}
													}
												});
											}
										}
									});
								}
							} else if (this.cardList.length > 2) {
								this.product = index;
							}
						}
					} else {
						this.isLoading = false;
						this.identity.cardNo = '';
						uni.showModal({
							title: '提示',
							content: "您选择的当前车辆下无ETC卡,请选择其他车辆！",
							confirmText: '确定',
							showCancel: false,
							success: function(res) {
								if (res.confirm) {

								}
							},
						});
					}
				}, (msg) => {
					this.identity.cardNo = '';
					uni.showModal({
						title: '提示',
						content: "您选择的当前车辆下无ETC卡,请选择其他车辆！",
						confirmText: '确定',
						showCancel: false,
						success: function(res) {},
					});
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			},
			//查询车辆车型
			checkCarType(vehicleId) {
				let params = {
					tokenId: getTokenId(),
					vehicleId: vehicleId,
					accountId: getAccountId()
				};
				let data = {
					'fileName': Api.getUserQuery.method,
					'data': params
				};
				console.log("获取车辆信息...");
				request(Api.getUserQuery.url, data, (res) => {
					console.log("获取车辆信息成功...");
					console.log(res);
					this.carType = res.type;
				})
			},
			ChooseImage() {
				var that = this;
				uni.chooseImage({
					count: 4, //默认9
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album'], //从相册选择
					success: (res) => {
						if (this.imgList.length != 0) {
							this.imgList = this.imgList.concat(res.tempFilePaths);
						} else {
							this.imgList = res.tempFilePaths
						}
					}
				});
			},
			ViewImage(e) {
				uni.previewImage({
					urls: this.imgList,
					current: e.currentTarget.dataset.url
				});
			},
			DelImg(e) {
				uni.showModal({
					title: '删除照片',
					content: '请确定删除该图片？',
					cancelText: '取消',
					confirmText: '确定',
					success: res => {
						if (res.confirm) {
							this.imgList.splice(e.currentTarget.dataset.index, 1);
						}
					}
				})
			},
			// 返回图片数据
			_result(src, index) {
				this.imageNum++;
				uni.hideLoading();
				//console.log("第"+index+"图片压缩完成");
				switch (index) {
					case 0:
						this.imgBase64_1 = src;
						break;
					case 1:
						this.imgBase64_2 = src;
						break;
					case 2:
						this.imgBase64_3 = src;
						break;
					case 3:
						this.imgBase64_4 = src;
						break;
				}

				//console.log("imageNum:"+this.imageNum+" imgList.length"+this.imgList.length);
				if (this.imageNum == this.imgList.length) {
					//提交接口
					//console.log("提交接口...");
					this.submitOrder();
				} else {
					this._cpImg(index + 1);
				}
			},
			// 压缩图片
			_cpImg(_index) {

				let that = this,
					resPath = this.imgList[_index];
				//// #ifdef APP-PLUS
				uni.hideLoading();
				//// #endif
				uni.showLoading({
					title: `图片压缩中 ${_index + 1}/${this.imgList.length}`,
					mask: true
				});
				// 获取图片信息
				// #ifndef H5
				uni.getImageInfo({
					src: resPath,
					success: function(image) {
						// 如果图片的大小大于设定值才压缩
						uni.getFileInfo({
							filePath: resPath,
							success: function(res) {
								_cpimg(image, res.size)
							},
							fail: function(e) {
								that._err(e)
							}
						})
					},
					fail: function(e) {
						that._err(e)
					}
				});
				// #endif
				// #ifdef H5
				that._getH5ImageInfo(resPath, d => {
					_cpimg(d, d.size)
				})
				// #endif 
				// 处理图片
				function _cpimg(image, size) {
					//#ifdef APP-PLUS
					// if (image.type.indexOf('png') >= 0) {
					// 	that._result(resPath)
					// 	return false
					// }
					//#endif
					let oW = image.width,
						oH = image.height,
						scaleWidth = 1,
						scaleHeight = 1,
						ctxWidth, ctxHeight;
					if (size / 1024 >= that.size || image.width >= that.maxWidth) {
						// 控制上传图片的宽高
						if (image.width >= image.height && image.width >= that.maxWidth) {
							image.height = that.maxWidth * image.height / image.width;
							image.width = that.maxWidth;
						} else if (image.width < image.height && image.height >= that.maxWidth) {
							image.width = that.maxWidth * image.width / image.height;
							image.height = that.maxWidth;
						}
						scaleWidth = image.width / oW
						scaleHeight = image.height / oH
					}
					ctxWidth = oW * scaleWidth
					ctxHeight = oH * scaleHeight
					const ctx = uni.createCanvasContext('_myCanvas', that);
					that.cWidth = image.width;
					that.cHeight = image.height;
					// 图片旋转修正，只有H5有效
					if (that.fixOrientation) {
						// 旋转图片
						let ot = image.orientation
						if ([5, 6, 7, 8, 'right', 'left', 'right-mirrored', 'left-mirrored'].indexOf(ot) > -1) {
							that.cWidth = image.height;
							that.cHeight = image.width;
						}
						// 代码参考 https://stackoverflow.com/questions/19463126/how-to-draw-photo-with-correct-orientation-in-canvas-after-capture-photo-by-usin
						if (ot == 2 || ot == "up-mirrored") {
							ctx.translate(ctxWidth, 0);
							ctx.scale(-1, 1);
						} else if (ot == 3 || ot == "down") {
							ctx.translate(ctxWidth, ctxHeight);
							ctx.rotate(Math.PI);
						} else if (ot == 4 || ot == "down-mirrored") {
							ctx.translate(0, ctxHeight);
							ctx.scale(1, -1);
						} else if (ot == 5 || ot == "right-mirrored") {
							ctx.rotate(0.5 * Math.PI);
							ctx.scale(1, -1);
						} else if (ot == 6 || ot == "right") {
							ctx.rotate(0.5 * Math.PI);
							ctx.translate(0, -ctxHeight);
						} else if (ot == 7 || ot == "left-mirrored") {
							ctx.rotate(0.5 * Math.PI);
							ctx.translate(ctxWidth, -ctxHeight);
							ctx.scale(-1, 1);
						} else if (ot == 8 || ot == "left") {
							ctx.rotate(-0.5 * Math.PI);
							ctx.translate(-ctxWidth, 0);
						} else {
							ctx.translate(0, 0);
						}
					}
					let ctxTime = 0;
					// #ifndef H5
					ctxTime = 10;
					// #endif
					// #ifdef H5
					ctxTime = cTime();
					// #endif
					setTimeout(() => {
						ctx.drawImage(resPath, 0, 0, ctxWidth, ctxHeight)
						ctx.draw(false, () => {
							let time = 0;
							// #ifndef MP-WEIXIN
							time = 10;
							// #endif
							// #ifdef MP-WEIXIN
							time = cTime();
							// #endif
							setTimeout(() => {
								uni.canvasToTempFilePath({
									width: Number(that.cWidth),
									height: Number(that.cHeight),
									destWidth: Number(that.cWidth),
									destHeight: Number(that.cHeight),
									canvasId: '_myCanvas',
									fileType: 'jpg',
									quality: Number(that.ql),
									success: function(res) {
										if (that.type == 'base64') {
											let img = '';
											//#ifdef MP-WEIXIN

											img = 'data:image/jpg;base64,' + wx
												.getFileSystemManager().readFileSync(
													res.tempFilePath, "base64")
											that._result(img, _index)
											//#endif 
											//#ifdef APP-PLUS
											pathToBase64(res.tempFilePath)
												.then(base64 => {
													img = base64;
												})
											// console.log(JSON.stringify(res))
											plus.io.resolveLocalFileSystemURL(res
												.tempFilePath,
												function(entry) {
													entry.file(function(file) {
														let fileReader =
															new plus.io
															.FileReader();
														// console.log("getFile:" + JSON.stringify(file));
														fileReader
															.readAsDataURL(
																file);
														fileReader
															.onloadend =
															function(evt) {
																// console.log(JSON.stringify(evt))
																if (evt
																	.target
																	.readyState ==
																	2) {
																	that._result(
																		evt
																		.target
																		.result,
																		_index
																		)
																} else {
																	that._err(
																		evt
																		)
																}
															}
													});
												},
												function(e) {
													that._err(e)
												});
											//#endif
											//#ifdef H5
											that._result(res.tempFilePath, _index)
											//#endif
										} else {
											that._result(res.tempFilePath, _index)
										}
									},
									fail: function(e) {
										that._err(e)
									}
								}, that)

							}, time);
						});
					}, ctxTime);

					function cTime() {
						let time = that.maxWidth / 5
						if (time >= 600) {
							return 600
						} else if (time <= 100) {
							return 100
						} else {
							return time
						}
					}
				}
			},
			// ios翻转图片
			_reverseImgData(res) {
				let w = res.width
				let h = res.height
				let con = 0
				for (let i = 0; i < h / 2; i++) {
					for (let j = 0; j < w * 4; j++) {
						con = res.data[i * w * 4 + j]
						res.data[i * w * 4 + j] = res.data[(h - i - 1) * w * 4 + j]
						res.data[(h - i - 1) * w * 4 + j] = con
					}
				}
				return res
			},
			_getH5ImageInfo(url, callback) {
				let image = new Image()
				image.src = url
				image.onload = function(d) {
					let imgSelf = this
					let http = new XMLHttpRequest();
					http.open("GET", url, true);
					http.responseType = "blob";
					http.onload = function(e) {
						let httpSelf = this
						if (httpSelf.status == 200 || httpSelf.status === 0) {
							let reader = new FileReader();
							reader.onload = function(e) {
								// 代码参考 https://www.jianshu.com/p/eb855b580780
								let view = new DataView(this.result);
								if (view.getUint16(0, false) != 0xFFD8) return callback({
									size: httpSelf.response.size,
									type: httpSelf.response.type,
									width: imgSelf.width,
									height: imgSelf.height,
									orientation: -1
								});
								let length = view.byteLength,
									offset = 2;
								while (offset < length) {
									let marker = view.getUint16(offset, false);
									offset += 2;
									if (marker == 0xFFE1) {
										if (view.getUint32(offset += 2, false) != 0x45786966) return callback({
											size: httpSelf.response.size,
											type: httpSelf.response.type,
											width: imgSelf.width,
											height: imgSelf.height,
											orientation: -1
										});
										let little = view.getUint16(offset += 6, false) == 0x4949;
										offset += view.getUint32(offset + 4, little);
										let tags = view.getUint16(offset, little);
										offset += 2;
										for (let i = 0; i < tags; i++)
											if (view.getUint16(offset + (i * 12), little) == 0x0112)
												return callback({
													size: httpSelf.response.size,
													type: httpSelf.response.type,
													width: imgSelf.width,
													height: imgSelf.height,
													orientation: view.getUint16(offset + (i * 12) + 8,
														little)
												});
									} else if ((marker & 0xFF00) != 0xFF00) break;
									else offset += view.getUint16(offset, false);
								}
								return callback({
									size: httpSelf.response.size,
									type: httpSelf.response.type,
									width: imgSelf.width,
									height: imgSelf.height,
									orientation: -1
								});
							};
							reader.readAsArrayBuffer(httpSelf.response);
						}
					};
					http.send();
				}
			},
			_err(src) {
				uni.hideLoading();
				//console.log("err..."+src);
				//this.$emit("err", src);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.c-info {
		margin-bottom: 30rpx;
		font-size: 28rpx;
		color: #333333;
		position: relative;

		.align-start {
			min-height: 216rpx;
		}

		.border {
			border-top: 1rpx solid #eee;
		}

		.down-ar {
			position: absolute;
			width: 32rpx;
			height: 40rpx;
			right: 20rpx;
		}
	}

	.cu-form-group {
		picker {
			&::after {
				content: ""
			}
		}
	}

	.solids {
		background: rgba(37, 151, 235, 0.03);
		border-radius: 4rpx;
		text-align: center;

		&:after {
			border: none;
		}

		.cameraadd {
			width: 82rpx;
			height: 82rpx;
			margin-top: 20rpx;
		}

		.upload-text {
			color: #47A8EE;
			font-size: 28rpx;
			opacity: 0.6;
		}
	}

	.img-title {
		padding: 0 45upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #fff;
		border: 1px solid #EEEEEE;

		&:before {
			content: "|";
			font-weight: bold;
			color: #47A8EE;
			position: relative;
			right: 20rpx;
			top: -2rpx;
		}
	}

	.disabled-input {
		text-align: right;
	}

	._cpimg {
		position: fixed;
		top: -99999upx;
		left: -99999upx;
		z-index: -99999;
	}
</style>