<template>
	<view>
		<view class="pay-account-index">
			<view v-if="myOrderList&&myOrderList.length > 0">
				<view class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;">
					<view v-for="(item, index) in myOrderList" :key="index">
						
						<view @click="goOrderDetail(item,index)" >
							<tComplaintItem :complaitInfo="item" :hideLine="index+1 === myOrderList.length"/>
						</view>
					</view>
				</view>
			</view>
			<view v-else>
				<xw-empty :isShow="true" text="您还没有相关的投诉工单" ></xw-empty>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tComplaintItem from '@/components/common/t-complaint-item.vue'
	import Api from '@/common/api/index.js'
	import tLoading from '@/components/common/t-loading.vue';
	import {getCurrUserInfo, setCurrUserInfo, getTokenId} from '@/common/storageUtil.js';
	import { uni_encodeURIComponent,uni_decodeURIComponent } from '@/common/helper.js';
	import xwEmpty from '@/pagesB/components/xw-empty/xw-empty';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			tComplaintItem,
			tLoading,
			xwEmpty
		},
		
		data() {
			return {
				isLoading: false,
				myOrderList: []
			}
		},
		
		onReady() {
			this.getPretreatmentOrder();
		},
		
		methods: {
			goOrderDetail(item,index) {
				uni.navigateTo({
					url:'/pagesB/personal/complaint/complaint-detail?allDatas=' + uni_encodeURIComponent(JSON.stringify(item))
				});
			},
			//预处理工单
			getPretreatmentOrder(){
				let userInfo = getCurrUserInfo();
				this.isLoading=true;
				let params={
					tokenId:getTokenId(),
					tel:userInfo.mobile
				};
				let data = {
					'fileName': Api.pretreatmentOrder.method,
					'data': params
				};
				request(Api.pretreatmentOrder.url, data,(res) => {
					// console.log(res)
					this.getComplaintList(params,res.list)
				},(msg)=>{				
					this.isLoading=false;
				},(err) => {
					this.isLoading=false;
				});
			},
			getComplaintList(params,preList){
				let data = {
					'fileName': Api.getComplaintListService.method,
					'data': params
				};
				this.myOrderList = []
				request(Api.getComplaintListService.url, data,(res) => {
					// console.log(preList)
					// console.log(res.list)
					this.isLoading=false;
					let curntList = res.list
					//有相同的工单，剔除预处理的
					let diff = preList.filter(item => !curntList.some(ele=>ele.complainNum===item.complainNum));
					// console.log(diff)
					let allList = [...diff,...curntList]
					this.myOrderList = this.sort(allList)
				},(msg)=>{				
					this.isLoading=false;
				},(err) => {
					this.isLoading=false;
				});
			},
			sort(arr) {
			  var len = arr.length;
			  for (var i = 0; i < len-1; i++) {
			    for (var j = 0; j < len - 1 - i; j++) {
			         // 最新的时间排在前面
			        if (arr[j].acceptTime < arr[j + 1].acceptTime) {
			            var temp = arr[j];
			            arr[j] = arr[j+1];
			            arr[j+1] = temp;
			        }
			    }
			  }
			  return arr;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.p-a-title {
		font-size: 28upx;
		font-weight: bold;
		margin: 20upx 20upx;
	}
	.p-a-content{
		border-radius: 10rpx;
		margin: 20px;
		background: rgba(255,255,255,0.80);
		box-shadow: 0 1px 2px 1px rgba(0,0,0,0.08);
	}
</style>
