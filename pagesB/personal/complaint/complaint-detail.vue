<template>
	<view>
		<view v-show="!isLoading">
			<view class="c-info top">
				<view class="img-title">客户信息</view>
				<view class="cu-group">
					<view class="title">姓名:</view>
					<view>{{identity.consumer}}</view>
				</view>
				<view class="cu-group">
					<view class="title">手机号码:</view>
					<view>{{identity.consumerTel}}</view>
				</view>
				<view class="cu-group">
					<view class="title">车牌号码:</view>
					<view>{{identity.plateNum }}</view>
				</view>
				<view class="cu-group">
					<view class="title">车牌颜色:</view>
					<view>{{plateColorToColorMap.get(identity.plateColorType+'')}}</view>
				</view>
				<view class="cu-group end">
					<view class="title">卡号:</view>
					<view>{{identity.cardNum || ""}}</view>
				</view>
			</view>

			<view class="c-info">
				<view class="img-title">投诉信息</view>
				<!-- <view class="cu-group">
				<view class="title">投诉单位:</view>
				<view class="textrate">{{provinceName || ""}}</view>
			</view> -->
				<view class="cu-group">
					<view class="title">投诉类型:</view>
					<view>{{identity.complainType || ""}}</view>
				</view>
				<view class="cu-group end">
					<view class="title">ETC卡发行方:</view>
					<view>{{issuerId || ""}}</view>
				</view>
			</view>

			<view class="c-info">
				<view class="img-title">投诉内容</view>
				<view class="cu-group">
					<view class="title">现金发票号:</view>
					<view>{{identity.invoiceNum || ""}}</view>
				</view>
				<view class="cu-group">
					<view class="title">通行时间:</view>
					<view>{{caseTime || ""}}</view>
				</view>

				<view class="cu-group">
					<view class="title">通行出口收费站名称:</view>
					<view>{{identity.caseSite || ""}}</view>
				</view>
				<view class="cu-group align-start">
					<view class="title">事件描述：</view>
					<view class="textrate">{{identity.caseDesc || ""}}</view>
				</view>
				<view class="cu-group align-start end">
					<view class="title">诉求：</view>
					<view class="textrate">{{identity.appeal || ""}}</view>
				</view>

				<!--
				<view class="cu-bar bg-white margin-top">
					<view class="action">
						附件
					</view>
					<view class="action">
						{{identity.imgList.length}}/4
					</view>
				</view>
				<view class="cu-group">
				<view class="grid col-4 grid-square flex-sub">
					<view class="bg-img" v-for="(item,index) in identity.imgList" :key="index" @tap="ViewImage" :data-url="identity.imgList[index]">
					 <image :src="identity.imgList[index]" mode="aspectFill"></image>
						<view class="cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
							<text class='cuIcon-close'></text>
						</view>
					</view>
					<view class="solids" @tap="ChooseImage" v-if="identity.imgList.length<4">
						<text class='cuIcon-cameraadd'></text>
					</view>
				</view>
			</view>
			-->

			</view>

			<view class="c-info">
				<view class="img-title">处理状态</view>

				<view class="cu-group">
					<view class="title">处理状态:</view>
					<view>{{identity.complainStatus || ""}}</view>
				</view>
				<view class="cu-group">
					<view class="title">处理时间:</view>
					<view>{{dealTime || ""}}</view>
				</view>
				<view class="cu-group align-start end">
					<view class="title">处理意见：</view>
					<view class="textrate">{{identity.issuerFinalDeal.result || ""}}</view>
				</view>
			</view>
		</view>
		<t-loading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		plateColorToColorMap
	} from '@/common/systemConstant.js';
	import {
		uni_decodeURIComponent
	} from '@/common/helper.js';

	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: true,
				isBtnLoader: false,
				identity: '',
				caseTime: '',
				dealTime: '',
				plateColorToColorMap,
				issuerId: '',
				provinceName: ''
			}
		},
		onLoad: function(event) {
			this.identity = JSON.parse(uni_decodeURIComponent(event.allDatas));
			this.caseTime = this.transferTime(this.identity.caseTime);
			this.dealTime = this.identity.issuerFinalDeal && this.transferTime(this.identity.issuerFinalDeal.dealTime);
			//截取发行方
			let issuerIndex = this.identity.issuer.indexOf('发行', 0);
			if (issuerIndex >= 0) {
				this.issuerId = this.identity.issuer.substring(0, issuerIndex);
			} else {
				issuerIndex = this.identity.issuer.indexOf('省', 0);
				if (issuerIndex >= 0) {
					this.issuerId = this.identity.issuer.substring(0, issuerIndex);
				} else {
					this.issuerId = this.identity.issuer;
				}
			}
			this.$nextTick(() => {
				this.isLoading = false
			});
		},
		methods: {
			transferTime(time) {
				let tempTime = time;
				var timeFormat;
				if (tempTime != "" && tempTime != null) {
					timeFormat = tempTime.substring(0, 4) + '-' + tempTime.substring(4, 6) + '-' + tempTime.substring(6, 8) +
						' ' + tempTime.substring(8, 10) + ':' + tempTime.substring(10, 12) + ':' + tempTime.substring(12, 14);
				} else {
					timeFormat = tempTime;
				}
				return timeFormat;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.top {
		border-top: 1px solid #eee
	}

	.c-info {
		background: rgba(255, 255, 255, 0.80);
		box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
		margin-bottom: 30upx;

		.cu-group {
			display: flex;
			justify-content: space-between;
			background-color: #fff;
			font-size: 28rpx;
			color: #333;
			padding: 6rpx 26rpx;

			.title {
				opacity: 0.6
			}

			.textrate {
				max-width: 480rpx;
			}
		}

		.end {
			padding-bottom: 26rpx;
		}

		.img-title {
			padding: 0 35upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #fff;

			&:before {
				content: "|";
				font-weight: bold;
				color: #47A8EE;
				position: relative;
				right: 10rpx;
				top: -2rpx;
			}
		}
	}
</style>
