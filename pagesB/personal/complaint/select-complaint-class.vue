<template>
	<view>
		<view class="c-title">
			请选择您想反馈的问题类型：
		</view>
		<view>
			<view class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;">
				<view v-for="(item, index) in itemlist" :key="index">
					<view @click="goNext(item)">
						<ComplaintTypeItem :itemInfo="item" />
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import Api from '@/common/api/index.js'
	import TButton from "@/components/button/button-height.vue"
	import ComplaintTypeItem from "@/components/common/t-complaint-type.vue";
	import {
		getCurrUserInfo,
		setCurrUserInfo,
		getTokenId,
		getAccountId
	} from '@/common/storageUtil.js';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		uni_encodeURIComponent,
		uni_decodeURIComponent
	} from '@/common/helper.js';
	import {
		request,
		getCarList,
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			TButton,
			ComplaintTypeItem
		},
		data() {
			return {
				isBtnLoader: false,
				classId: '',
				itemlist: [
					// {
					// 	iconurl: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/feedback.png',
					// 	title: '未通行但收到ETC扣费信息',
					// 	value: '延期扣费'
					// },
					// {
					// 	iconurl: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/feedback.png',
					// 	title: '一次通行中，收到多条小额扣费短信',
					// 	value: '未整合扣费'
					// },
					{
						iconurl: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/feedback.png',
						title: '通行相同的路段时，当次收费高于前次',
						value: '相同路径不一致扣费'
					},
					{
						iconurl: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/feedback.png',
						title: '一次通行中，现金和ETC同时扣费或ETC被多次扣费',
						value: '重复扣费'
					},
					// {
					//    iconurl:'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/feedback.png',
					//    title:'一次通行中，ETC被多次扣费',
					//    value:'重复扣费-ETC卡',
					//    classId:'4a4a4dff4ca44547a1d8a65dfabbb031'
					// },
					// {
					//    iconurl:'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/feedback.png',
					//    title:'运输绿通货物，出收费站时查验合格后被扣费',
					//    value:'应免未免-绿通',
					//    classId:'1176f15712a844dab186928f1f70c4af'
					// },
					{
						iconurl: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/feedback.png',
						title: '符合国家规定的免费政策，ETC通行被扣费',
						value: '应免未免'
					}
				]
			}
		},
		onReady() {
			//this.getChildClassList();
			this.reqVerhicleList();
		},
		methods: {
			goNext(item) {
				uni.navigateTo({
					url: "/pagesB/personal/complaint/complaint?complaintItem=" + uni_encodeURIComponent(JSON.stringify(item))
				});
			},
			getChildClassList() { //获取三级投诉类型
				let userInfo = getCurrUserInfo();

				this.isLoading = true;
				let params = {
					tokenId: getTokenId(),
					complainClass: '302',
					id: '',
				};

				let data = {
					'fileName': Api.getComplaintClassListService.method,
					'data': params
				};
				request(Api.getComplaintClassListService.url, data, (res) => {
					this.isLoading = false;
					//console.log("getClassList:"+JSON.stringify(res));
					let list = res.list;

					this.itemlist.forEach((item, index) => {
						list.forEach((tempItem, tempIndex) => {
							if (item.value == tempItem.name) {
								//console.log("tempItem"+JSON.stringify(tempItem));
								this.$set(item, 'complaintId', tempItem.id);
								//console.log("item"+JSON.stringify(item));
							}
						});
					});
					//console.log("itemlist:"+JSON.stringify(this.itemlist));
				}, (msg) => {
					uni.showModal({
						title: '提示',
						content: msg
					});

					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				});
			},
			//获取车辆列表
			reqVerhicleList() {
				this.isLoading = true;
				getCarList(getAccountId(), (res) => {
					this.isLoading = false;
					if (res.listData.length > 0) {
						this.getChildClassList();
					} else {
						uni.showModal({
							title: '提示',
							content: "1、当前登录手机号下无匹配车辆数据，请核实登录手机号是否为办理ETC时开户预留手机号。\r\n\
						2、如确认登录手机号与办理ETC时开户预留手机号一致但仍无车辆信息，请您联系办理ETC发行服务机构处理。",
							confirmText: '联系客服',
							cancelText: '确定',
							success: function(res) {
								if (res.confirm) {
									let isTokenId = getTokenId();
									var callcenter = "https://cc.cywetc.com/kn2/?h5channel=********&customerNo=" + isTokenId;
									uni.redirectTo({
										url: '/pages/uni-webview/uni-webview?ownPath=' + uni_encodeURIComponent(callcenter)
									});
								} else if (res.cancel) {
									uni.navigateBack({
										delta: 1
									});
								}
							}
						});
					}
				}, () => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: "1、当前登录手机号下无匹配车辆数据，请核实登录手机号是否为办理ETC时开户预留手机号。\r\n\
					2、如确认登录手机号与办理ETC时开户预留手机号一致但仍无车辆信息，请您联系办理ETC发行服务机构处理。",
						confirmText: '联系客服',
						cancelText: '确定',
						success: function(res) {
							if (res.confirm) {
								let isTokenId = getTokenId();
								var callcenter = "https://cc.cywetc.com/kn2/?h5channel=********&customerNo=" + isTokenId;
								uni.redirectTo({
									url: '/pages/uni-webview/uni-webview?ownPath=' + uni_encodeURIComponent(callcenter)
								});
							} else if (res.cancel) {
								uni.navigateBack({
									delta: 1
								});
							}
						}
					});
				}, () => {
					this.isLoading = false;
				})
			},

		}
	}
</script>

<style>
	.c-title {
		margin-top: 30upx;
		margin-left: 40upx;
	}

	.c-lebel {
		margin-left: 30upx;
		margin-right: 30upx;
		margin-top: 30upx;
		height: 300upx;
		background: #007AFF;
		text-align: center;
		line-height: 200upx;
		border-radius: 10px;
	}

	.p-a-content {
		margin-top: 10upx;
		margin-left: 40upx;
		margin-right: 40upx;
		background: #FCFCFC;
		border-radius: 16upx;
	}
</style>
