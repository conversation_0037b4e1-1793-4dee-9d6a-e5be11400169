<template>
	<view>
		<view class="top"></view>
		<checkbox-group @change="checkboxChange">
			<view class="nav-check-index" v-for="(item,index) in funThreeArrays" :key="index">
				<view class="nav-check-item" v-for="(item_0,index_0) in item" :key="index_0">
						<label>
							<checkbox :value="item_0.value" :checked="item_0.checked" @change="checkboxChange"/>    {{item_0.label}}
						</label>
				</view>
			</view>
		</checkbox-group>
		<!--
		<view class="uni-list">
			<checkbox-group @change="checkboxChange">
				<label class="uni-list-cell uni-list-cell-pd" v-for="item in items" :key="item.value">
					<view>
						<checkbox :value="item.value" :checked="item.checked" />
					</view>
					<view>{{item.label}}</view>
				</label>
			</checkbox-group>
		</view>
		-->
		<view>
			<TButton title="确定" @clickButton="goNext" :isLoadding="isBtnLoader"/>
		</view>
	</view>
</template>

<script>
	import "../../../common/uni-official.css";
	// import provinceData from '../../../components/mpvue-citypicker/city-data/province_complaint.js';
	import TButton from "@/components/t-button.vue"
	
	export default {
		components:{
			TButton
		},
		data() {
			return {
				isBtnLoader:false,
				funThreeArrays:[],
				funArrays:[],
			}
		},
		onLoad:function(event){
			// this.items = provinceData;
			// //console.log("event.isLoaded:"+event.isLoaded);
			// if(event.isLoaded == '0'){//
			//     console.log("初始化选择省份...");
			// 	this.items.forEach((item,index)=>{
			// 		this.$set(item,'checked',false);
			// 	});
			// }
			
			this.funArrays = provinceData;
			if(event.isLoaded == '0'){//
				this.funArrays.forEach((item,index)=>{
					this.$set(item,'checked',false);
				});
			}
			
			var items = [];
			for(let i=0;i<this.funArrays.length;i++){
				console.log(JSON.stringify(this.funArrays[i]));
                if((i+1) % 2 === 0){
                    items.push(this.funArrays[i]);
                    this.funThreeArrays.push(items);
                    items = [];
                    continue;
                }
                items.push(this.funArrays[i]);
            }
            if(this.funArrays.length % 2 !==0 ){
                this.funThreeArrays.push(items);
            }
		},
		
		methods: {
			checkboxChange: function (e) {
				var items = this.funArrays,
					values = e.detail.value;
				console.log("values:"+JSON.stringify(values));	
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					if(values.includes(item.value)){
						this.$set(item,'checked',true);
						//console.log("选中省份:"+JSON.stringify(item));
					}else{
						this.$set(item,'checked',false);
						//console.log("不选中省份:"+JSON.stringify(item));
					}
				}
			},
			goNext(){
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>

<style>
	.uni-list-cell {
		justify-content: flex-start
	}
	.top{
		height: 20upx;
	}
	.uni-padding-wrap{
		width:690upx;
		padding:0 30upx;
	}
	.uni-common-mt{
		margin-top:30upx;
	}
	.nav-check-index{
		font-size: 28upx;
		height: 70upx;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-center;
		align-items: center;
		justify-content: space-around;
	}
	.nav-check-item{
		height: 60upx;
		width: 40%;
    }
</style>
