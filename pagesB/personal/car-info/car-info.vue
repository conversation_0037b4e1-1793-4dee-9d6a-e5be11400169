<template>
	<view>
        <view class="c-title">车辆信息(*为必填项)</view>
			<form>
				<view class="cu-form-group">
					<view class="must-input">*</view>
					<view class="title">车主姓名</view>
					<input v-model="identity.ownerName" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="title">车牌号码</view>
					<input v-model="identity.plateNum" disabled></input> <!--disabled-->
				</view>
				<view class="cu-form-group">
					<view class="title" >车牌颜色</view>
					<input name="c" :value="plateColorToColorMap.get(identity.plateColor+'')" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title">车辆类型</view>
					<input v-model="identity.vehicleType" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title">核定人数</view>
					<input v-model="identity.approvedCount" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="must-input">*</view>
					<view class="title">车辆识别码</view>
					<input v-model="identity.vin" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="must-input">*</view>
					<view class="title">发动机号</view>
					<input v-model="identity.engineNo" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="must-input">*</view>
					<view class="title">发证日期</view>
					<!--
					<input v-model="identity.issueDate" :disabled="isEdit?false:true"></input>
					* -->
					<picker mode="date" :value="issueDate" start="2019-01-01" end="2030-12-31" @change="issueDateChange" :disabled="isEdit?false:true">
						<view class="picker">
							{{issueDate}}
						</view>
					</picker>
					<!--
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
					-->
				</view>
				<view class="cu-form-group">
					<view class="must-input">*</view>
					<view class="title">注册日期</view>
					<picker mode="date" :value="registerDate" start="2019-01-01" end="2030-12-31" @change="registerDateChange" :disabled="isEdit?false:true">
						<view class="picker">
							{{registerDate}}
						</view>
					</picker>
					<!--
					<input v-model="identity.registerDate" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
					* -->
				</view>
				<view class="cu-form-group">
					<view class="title">档案编号</view>
					<input v-model="identity.fileNum" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="title">整备质量</view>
					<input v-model="identity.maintenaceMass" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="title">核定载质量</view>
					<input v-model="identity.permittedWeight" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="title">准牵引总质量</view>
					<input v-model="identity.permittedTowWeight" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="must-input">*</view>
					<view class="title">车辆尺寸</view>
					<input v-model="identity.outsideDimensions" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				
				<view class="cu-form-group">
					<view class="title">行驶证品牌型号</view>
					<input v-model="identity.vehicleModel" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="title">检验记录</view>
					<input v-model="identity.testRecord" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="title">车轮数</view>
					<input v-model="identity.wheelCount" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				
				<view class="cu-form-group">
					<view class="title">轴距</view>
					<input v-model="identity.axleDistance" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				<view class="cu-form-group">
					<view class="title">轴型</view>
					<input v-model="identity.axisType" :disabled="isEdit?false:true"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
				</view>
				
				<view class="cu-form-group">
					<view class="must-input">*</view>
					<view class="title">使用性质</view>
						<picker :model="useCharacter" :range="useCharacterOptions" @change="PickerChangeuseCharacter" :disabled="isEdit?false:true">
							<view class="picker">
								{{useCharacterOptions[useCharacter]}}
							</view>
						</picker>
					<!--
					<input name="i" :value="identity.useCharacter"></input>
					<text v-if="isEdit" class='cuIcon-write text-blue'></text>
					-->
				</view>
				<view class="cu-form-group">
					<view class="title">短信验证码:</view>
					<input v-model="msgcode"></input>

					<view class="cu-form-new-group" >
						<template v-if="isGetCheckCodeing==true">
							<view class="sendcode">{{timeCount}}s后重新获取</view>
						</template>
						<template v-else>
							<view class="sendcode" @tap="getPhoneCode" >点击获取验证码</view>
						</template>
					</view>
				</view> 
			</form>
			<view class="certification">
				<TButton :title="title" @clickButton="goNext" :isLoadding="isBtnLoader"/>
			</view>
			<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
    import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue';
	import Api from '@/common/api/index.js'
	import {encrypt,decrypt}  from "@/common/crypto/aesutils.js"
    import { setCurrentCar, getCurrentCar,getTokenId,setTokenId,getCurrUserInfo,setCurrUserInfo,getAccountId,getDecodingKey } from '@/common/storageUtil.js'
    import { plateColorToColorMap} from '@/common/systemConstant.js';

	export default {
		components:{
			TButton,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				isEdit:false,
				title:'编辑',
				identity:{
				},
				useCharacter:1,
				plateColorToColorMap,
				useCharacterOptions:["营运","非营运"],
				timeCount: 120,
				isGetCheckCodeing: false,
				msgcode:'',
				issueDate:'2019-01-01',
				registerDate:'2019-01-01'
			}
		},
		onReady() {
			this.reqCarInfo();
		},
		methods: {
			//获取车辆信息
			reqCarInfo(){
				this.isLoading = true;
				let params={
					tokenId:getTokenId(),
					vehicleId:getCurrentCar().vehicleId,
					accountId:getAccountId()
				};
				let data = {
					'fileName': Api.getUserQuery.method,
					'data': params
				};
				console.log("获取车辆信息...");
				this.$request.post(Api.getUserQuery.url, data).then((res) => {
					this.isLoading = false;
					console.log("获取车辆信息成功...");
					this.identity = res;
					//this.useCharacter = res.useCharacter;
					console.log("identity:"+JSON.stringify(this.identity));
				}, (msg)=>{
					uni.showModal({
						title: '提示',
						content: msg
					});
					this.isLoading = false;
				}).catch((err) => {
					this.isLoading = false;
				})
			},
			updateCarInfo(){
				if(!this.identity.ownerName){
					uni.showToast({title:'请输入车主姓名',icon:'none'});
					return ;
				}
				if(!this.identity.vin){
					uni.showToast({title:'请输入车架号',icon:'none'});
					return ;
				}
				if(!this.identity.engineNo){
					uni.showToast({title:'请输入发动机号',icon:'none'});
					return ;
				}
				if(!this.issueDate){
					uni.showToast({title:'请输入发证日期',icon:'none'});
					return ;
				}
				if(!this.registerDate){
					uni.showToast({title:'请输入注册日期',icon:'none'});
					return ;
				}
				if(!this.identity.outsideDimensions){
					uni.showToast({title:'请输入车辆尺寸',icon:'none'});
					return ;
				}
				if(!this.msgcode ){
					uni.showToast({title:'请输入短信验证码',icon:'none'});
					return ;
				}
				// console.log("this.useCharacter:"+this.useCharacter);
				// if(!this.useCharacter){
				// 	uni.showToast({title:'请选择车辆使用性质',icon:'none'});
				// 	return ;
				// }
				
				this.isLoading = true;
				var carinfoData = {
					vin:this.identity.vin,
					engineNum:this.identity.engineNo,
					issueDate:this.issueDate,
					name:this.identity.ownerName,
					registerDate:this.registerDate,
					fileNum:this.identity.fileNum,
					maintenaceMass:this.identity.maintenaceMass,
					permittedWeight:this.identity.permittedWeight,
					outsideDimensions:this.identity.outsideDimensions,
					permittedTowWeight:this.identity.permittedTowWeight,
					vehicleModel:this.identity.vehicleModel,
					testRecord:this.identity.testRecord,
					wheelCount:this.identity.wheelCount,
					axleDistance:this.identity.axleDistance,
					axisType:this.identity.axisType,
					useCharacter:parseInt(this.useCharacter)+1
				};
				
				let bizContent = encrypt(JSON.stringify(carinfoData), getDecodingKey());
				
				let params={
					tokenId:getTokenId(),
					vehicleId:getCurrentCar().vehicleId,
					code:this.msgcode,
					encryptedData:bizContent
				};
				console.log("车辆信息："+JSON.stringify(carinfoData));
				console.log("修改车辆信息："+JSON.stringify(params));
				
				let data = {
					'fileName': Api.getVehicleChangeUpService.method,
					'data': params
				};
				this.$request.post(Api.getVehicleChangeUpService.url, data).then((res) => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: res.info
					});
				}, (msg)=>{
					uni.showModal({
						title: '提示',
						content: msg
					});
					this.isLoading = false;
				}).catch((err) => {
					this.isLoading = false;
				})
			},
			goNext(){
				if(this.isEdit){
					this.updateCarInfo();
				}else{
					this.isEdit = true;
					this.title = "保存"
				}
			},
			issueDateChange(e) {
				this.issueDate = e.detail.value;
			},
			registerDateChange(e){
				this.registerDate = e.detail.value;
			},
			PickerChangeuseCharacter(e) {
				this.useCharacter = e.detail.value;
			},
			getPhoneCode(){
					console.log("获取短信验证码");
					if(this.isGetCheckCodeing == true){
						uni.showToast({
							title: '请稍候...',
							icon: 'loading',
						});
						return;
					}
					this.timeTick();
					this.getPhonePwd();
			},
			timeUp() {
					clearInterval(this.timer)
					this.isGetCheckCodeing = false;
					this.timeCount = 10;
				},
			timeTick() {
				this.isGetCheckCodeing = true;
				this.timer = setInterval(() => {
					this.timeCount = this.timeCount - 1
					if (this.timeCount < 0) {
						this.timeUp()
						return
					}
				}, 1000)
			},
			getPhonePwd(){

				let params = {
					tokenId:getTokenId(),
					mobile:getCurrUserInfo().phone,
					type:'2' //1:登录  2:关联车辆   3:解除关联
				};
				   
				let data = {
					fileName: Api.getIssueMsg.method,
					data: params
				};
				
				this.$request.post(Api.getIssueMsg.url, data).then(res => {
					this.isGetCode = true;
					uni.showToast({
						title: '验证码已发送',
						// icon:'loading',
					})
							
				},
				msg => {
					this.isGetCode = true;
					uni.showModal({
						title: '提示',
						content: msg
					});
				}).catch(err => {
						// console.log(err);
					});
			}
		}
	}
</script>

<style>
	.c-title{
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #FFFFFF;
	}
	.must-input{
		font-size: 36upx;
		color: #F00;
		font-weight: bold;
		background: #FFFFFF;
	}
	.sendcode{
		color: #1684fb;
	}
</style>
