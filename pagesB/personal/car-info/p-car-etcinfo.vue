<template>
	<view>
		<view v-show="!isLoading">
			<view>
				<view class="c-title">车辆信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">车牌号码:</view>
						<input class="values" v-model="identity.plateNum" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车牌颜色:</view>
						<input class="values" :value="plateColorToColorMap.get(identity.plateColor+'')" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车辆状态:</view>
						<input class="values" :value="identity.blackType == 2 ? '黑名单' : '正常'" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车辆使用性质:</view>
						<input class="values" :value="useCharacterMap.get(identity.useCharacter+'')" disabled></input>
					</view>
				</form>
			</view>

			<view v-if="hasBankInfo">
				<view class="c-title">签约信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">签约银行:</view>
						<input class="values" v-model="bankCardInfo.name" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">签约银行卡类型:</view>
						<input class="values" :value="bankCardInfo.accountType==1?'信用卡':'借记卡'" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">签约银行卡号:</view>
						<input class="values" :value="bankCardInfo.accountNum" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">签约状态:</view>
						<input class="values" :value="bankStatusMap[bankCardInfo.status]" disabled></input>
					</view>
				</form>
			</view>

			<view v-if="hasObuInfo">
				<view class="c-title">电子标签信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">电子标签编号:</view>
						<input class="values" v-model="obuinfo.obuId" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">电子标签启用时间:</view>
						<input class="values" :value="obuinfo.enableTime | removeTime" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">电子标签到期时间:</view>
						<input class="values" :value="obuinfo.expireTime" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">电子标签状态:</view>
						<input class="values" :value="ObuStatusMap.get(obuinfo.status+'')" disabled></input>
					</view>
				</form>
			</view>

			<view v-if="hasCardInfo">
				<view class="c-title">卡信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">通行卡编号:</view>
						<input class="values" :value="cardinfo.id" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">卡类型:</view>
						<input class="values" :value="getCardType" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">通行卡启用时间:</view>
						<input class="values" :value="cardinfo.enableTime | removeTime" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">通行卡到期时间:</view>
						<input class="values" :value="cardinfo.expireTime | removeTime" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">卡状态:</view>
						<input class="values" :value="CardStatusMap.get(cardinfo.status+'')" disabled></input>
					</view>
				</form>
			</view>

		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue';
	import Api from '@/common/api/index.js'
	import {
		encrypt,
		decrypt
	} from "@/common/crypto/aesutils.js"
	import {
		getBusinessTypes,
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getCurrUserInfo,
		setCurrUserInfo,
		getAccountId
	} from '@/common/storageUtil.js'
	import {
		plateColorToColorMap,
		vehicleTypeOptions
	} from '@/common/systemConstant.js';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			TButton,
			tLoading
		},
		computed: {
			getCardType() {
				if (this.cardinfo.cardType === 23) {
					return '记账卡'
				}
				if (this.cardinfo.cardType === 22) {
					return '储值卡'
				}
			}
		},
		filters: {
			removeTime: function(values) {
				if (values && values.indexOf("T") > 0) {
					// `this` 指向 vm 实例
					return values.split("T")[0]
				}
				return values
			}
		},
		data() {
			return {
				isLoading: true,
				isEdit: false,
				title: '修改车辆信息',
				identity: {},
				obuinfo: {},
				cardinfo: {},
				bankCardInfo: {},
				useCharacter: 1,
				plateColorToColorMap,
				vehicleTypeOptions,
				useCharacterMap: new Map([
					['0', "普通车"],
					['19', "广西警车"],
					['20', "路政车"],
					['26', "应急救援车"]
				]),
				CardStatusMap: new Map([
					['0', "未发行"],
					['1', "正常"],
					['2', "挂失"],
					['3', "已更换"],
					['4', "已注销"],
					['5', "已过户"],
					['7', "挂失已补领"],
					['8', "坏卡"],
					['9', "异常停用"],
					['10', "已退货"],
					['11', "已免费更换"],
					['14', "合作机构状态名单"],
					['15', "车型不符"]
					
				]),
				ObuStatusMap: new Map([
					['1', "初始化"],
					['2', "未激活"],
					['3', "正常"],
					['4', "维护"],
					['5', "已回收"],
					['6', "损坏"],
					['7', "挂失"],
					['8', "已过户"],
					['9', "已注销"],
					['10', "有偿更换"],
					['11', "已退货"],
					['12', "正在维修"],
					['13', "已免费更换"],
					['15', "车型不符"],
					['16', "异常停用（欠费）"],
					['17', "停用"],
					['18', "无OBU注销"]
				]),
				useCharacterOptions: ["营运", "非营运"],
				timeCount: 120,
				isGetCheckCodeing: false,
				msgcode: '',
				issueDate: '2019-01-01',
				registerDate: '2019-01-01',
				bankStatusMap: ['', '在用', '解约', '签约中'],
				hasBankInfo: false,
				hasObuInfo: false,
				hasCardInfo: false,
				useCarList: [{
						key: 1,
						value: "营运"
					},
					{
						key: 2,
						value: "非营运"
					},
					{
						key: 6,
						value: "特种车"
					},
					{
						key: 7,
						value: "挂车"
					},
					{
						key: 8,
						value: "应急救援车"
					},
					{
						key: 9,
						value: "J1"
					},
					{
						key: 10,
						value: "J2"
					}
				],
				isFont: false
			}
		},
		onReady() {
			this.fontReqCarInfo()
		},
		methods: {

			//前装获取车辆信息
			fontReqCarInfo() {
				let params = {
					accountId: getAccountId(),
					vehicleId: getCurrentCar().vehicleId
				};
				let data = {

					'data': params
				};
				console.log("获取车辆信息...");
				request(this.$interfaces.userVehicleQuery, data, (data) => {
					let res = data.data
					
					this.identity = res;
					console.log("获取车辆信息成功...:" + JSON.stringify(this.identity));
					this.reqObuInfo();
					/* this.getBankInfo(); */
				})
			},
			//获取OBU信息
			reqObuInfo() {
				this.isLoading = true;
				let params = {
					obuId: this.identity.obuId,
					vehicleId: getCurrentCar().vehicleId,
					accountId: getAccountId()
				};
				let data = {

					'data': params
				};
				console.log("获取OBU信息...");
				request(this.$interfaces.userOBUQuery, data, (res) => {
					var isNomalObu = false;
					console.log("获取OBU信息成功...", res);
					if (res.data.result == 1) {
						if (res.data.data.status == 3) {
							this.obuinfo = res.data.data;
							isNomalObu = true;
							this.hasObuInfo = true;
						}
						this.reqCardInfo();
					}
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			},
			//获取卡信息
			reqCardInfo() {
				this.isLoading = true;
				var isNomalObu = false;
				let params = {
					vehicleId: getCurrentCar().vehicleId,
					accountId: getAccountId()
				};
				let data = {

					'data': params
				};
				console.log("获取CPU信息...");
				request(this.$interfaces.userCardQuery, data, (res) => {
					this.isLoading = false;
					console.log("获取CPU信息成功...", res.data);
					if (res.data.result == 1) {
						if (res.data.data.status == 1) {
							this.cardinfo = res.data.data;
							isNomalObu = true;
							this.hasCardInfo = true;
						}
						if (isNomalObu == false) {
							//console.log("无正常状态的CPU卡信息...");
							this.cardinfo = res.data.data;
							this.hasCardInfo = true;
						}
					}
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			},
			//获取签约银行信息
			getBankInfo() {
				//console.log("获取银行信息...");
				this.isLoading = true;
				let params = {
					vehicleId: getCurrentCar().vehicleId,
					accountId: getAccountId()
				};
				let data = {

					'data': params
				};
				request(this.$interfaces.accountReList, data, (res) => {

					this.isLoading = false;
					res.data.list.forEach((item, index) => {
						if (item.status == 1) {

							this.bankCardInfo = item;
							console.log("关联支付渠道列表:" + JSON.stringify(this.bankCardInfo));
							this.hasBankInfo = true;
						}
					});
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			}
		}
	}
</script>

<style>
	.c-title {
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #FFFFFF;
	}

	.values {
		-webkit-text-fill-color: #000000;
	}
</style>
