<template>
	<view>
		<view>
			<view class="c-title">车辆信息</view>
			<form>
				<view class="cu-form-group">
					<view class="title">车牌号码:</view>
					<input class="values" v-model="identity.plateNum" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title" >车牌颜色:</view>
					<input class="values" :value="plateColorToColorMap.get(identity.plateColor+'')" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title">收费车型:</view>
					<input class="values" :value="identity.type==null?'':vehicleTypeOptions.get(identity.type+'')" disabled></input>
				</view>
			</form>
		</view>
		
		<view>
			<view class="c-title">卡信息</view>
			<form>
				<view class="cu-form-group">
					<view class="title">通行卡编号:</view>
					<input class="values" :value="cardinfo.id" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title" >卡类型:</view>
					<input class="values" :value="getCardType" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title" >通行卡启用时间:</view>
					<input class="values" :value="cardinfo.enableTime | removeTime" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title">通行卡到期时间:</view>
					<input class="values" :value="cardinfo.expireTime | removeTime" disabled></input>
				</view>
				<view class="cu-form-group">
					<view class="title">卡状态:</view>
					<input class="values" :value="CardStatusMap.get(cardinfo.status+'')" disabled></input>
				</view>
			</form>
		</view>
		<view class="no-my-etc" @click="unbindEtc">非该ETC账户车辆?</view>
		<view>
			<tLoading :isShow="isLoading" />
		</view>
		
	</view>
</template>

<script>
    import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue';
	import Api from '@/common/api/index.js'
	import {encrypt,decrypt}  from "@/common/crypto/aesutils.js"
    import { setCurrentCar, getCurrentCar,getTokenId,setTokenId,getCurrUserInfo,setCurrUserInfo,getAccountId } from '@/common/storageUtil.js'
    import { plateColorToColorMap,vehicleTypeOptions,CardStatusMap,ObuStatusMap} from '@/common/systemConstant.js';
	export default {
		components:{
			TButton,
			tLoading
		},
		computed:{
			getCardType(){
				if(this.cardinfo.cardType<200){
					return '记账卡'
				}else{
					return '储值卡'
				}
			}
		},
		filters: {
			removeTime: function (values) {
				if(values && values.indexOf("T")>0){
					// `this` 指向 vm 实例
					return values.replace("T", " ")
				}
			}
		},
		data() {
			return {
				isLoading: false,
				isEdit:false,
				title:'修改车辆信息',
				identity:{
				},
				obuinfo:{},
				cardinfo:{},
				bankCardInfo:{},
				useCharacter:1,
				plateColorToColorMap,
				vehicleTypeOptions,
				CardStatusMap,
				ObuStatusMap,
				useCharacterOptions:["营运","非营运"],
				timeCount: 120,
				isGetCheckCodeing: false,
				msgcode:'',
				issueDate:'2019-01-01',
				registerDate:'2019-01-01',
				bankStatusMap:['','在用','解约','签约中'],
				hasBankInfo:false,
				hasObuInfo:false,
				hasCardInfo:false
			}
		},
		onReady() {
		},
		methods: {
		}
	}
</script>

<style>
	.c-title{
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #FFFFFF;
	}
	.values{
		-webkit-text-fill-color: #000000;
	}
	.no-my-etc{
		display: flex;
		align-items: center;
		justify-content: center;
		height: 160rpx;
		color:#1D82D2;
	}
</style>
