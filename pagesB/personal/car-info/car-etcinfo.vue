<template>
	<view>
		<view v-show="!isLoading">
			<view>
				<view class="c-title">车辆信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">车牌号码:</view>
						<input class="values" v-model="identity.plateNum" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车牌颜色:</view>
						<input class="values" :value="plateColorToColorMap.get(identity.plateColor+'')" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">收费车型:</view>
						<input class="values" :value="identity.type==null?'':vehicleTypeOptions.get(identity.type+'')" disabled></input>
					</view>
					<view class="cu-form-group" v-if="isFont">
						<view class="title">车辆状态:</view>
						<input class="values" :value="identity.blackType == 2 ? '黑名单' : '正常'" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车辆使用性质:</view>
						<input class="values" :value="identity.useCarType" disabled></input>
					</view>
				</form>
			</view>

			<view v-if="hasBankInfo">
				<view class="c-title">签约信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">签约银行:</view>
						<input class="values" v-model="bankCardInfo.name" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">签约银行卡类型:</view>
						<input class="values" :value="bankCardInfo.accountType==1?'信用卡':'借记卡'" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">签约银行卡号:</view>
						<input class="values" :value="bankCardInfo.accountNum" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">签约状态:</view>
						<input class="values" :value="bankStatusMap[bankCardInfo.status]" disabled></input>
					</view>
				</form>
			</view>

			<view v-if="hasObuInfo">
				<view class="c-title">电子标签信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">电子标签编号:</view>
						<input class="values" v-model="obuinfo.obuId" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">电子标签启用时间:</view>
						<input class="values" :value="obuinfo.enableTime | removeTime" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">电子标签到期时间:</view>
						<input class="values" :value="obuinfo.expireTime" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">电子标签状态:</view>
						<input class="values" :value="ObuStatusMap.get(obuinfo.status+'')" disabled></input>
					</view>
				</form>
			</view>

			<view v-if="hasCardInfo">
				<view class="c-title">卡信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">通行卡编号:</view>
						<input class="values" :value="cardinfo.id" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">卡类型:</view>
						<input class="values" :value="getCardType" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">通行卡启用时间:</view>
						<input class="values" :value="cardinfo.enableTime | removeTime" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">通行卡到期时间:</view>
						<input class="values" :value="cardinfo.expireTime | removeTime" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">卡状态:</view>
						<input class="values" :value="CardStatusMap.get(cardinfo.status+'')" disabled></input>
					</view>
				</form>
			</view>

		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue';
	import Api from '@/common/api/index.js'
	import {
		encrypt,
		decrypt
	} from "@/common/crypto/aesutils.js"
	import {
		getBusinessTypes,
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getCurrUserInfo,
		setCurrUserInfo,
		getAccountId
	} from '@/common/storageUtil.js'
	import {
		plateColorToColorMap,
		vehicleTypeOptions,
		CardStatusMap,
		ObuStatusMap
	} from '@/common/systemConstant.js';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			TButton,
			tLoading
		},
		computed: {
			getCardType() {
				if (this.cardinfo.cardType < 200) {
					return '记账卡'
				} else {
					return '储值卡'
				}
			}
		},
		filters: {
			removeTime: function(values) {
				if (values && values.indexOf("T") > 0) {
					// `this` 指向 vm 实例
					return values.split("T")[0]
				}
				return values
			}
		},
		data() {
			return {
				isLoading: true,
				isEdit: false,
				title: '修改车辆信息',
				identity: {},
				obuinfo: {},
				cardinfo: {},
				bankCardInfo: {},
				useCharacter: 1,
				plateColorToColorMap,
				vehicleTypeOptions,
				CardStatusMap,
				ObuStatusMap,
				useCharacterOptions: ["营运", "非营运"],
				timeCount: 120,
				isGetCheckCodeing: false,
				msgcode: '',
				issueDate: '2019-01-01',
				registerDate: '2019-01-01',
				bankStatusMap: ['', '在用', '解约', '签约中'],
				hasBankInfo: false,
				hasObuInfo: false,
				hasCardInfo: false,
				useCarList: [{
						key: 1,
						value: "营运"
					},
					{
						key: 2,
						value: "非营运"
					},
					{
						key: 6,
						value: "特种车"
					},
					{
						key: 7,
						value: "挂车"
					},
					{
						key: 8,
						value: "应急救援车"
					},
					{
						key: 9,
						value: "J1"
					},
					{
						key: 10,
						value: "J2"
					}
				],
				isFont: false
			}
		},
		onReady() {
			this.fontReqCarInfo()
		},
		methods: {
			//获取车辆信息
			reqCarInfo() {
				this.isLoading = true;
				console.log("getCurrentCar:" + JSON.stringify(getCurrentCar()));
				let params = {
					tokenId: getTokenId(),
					vehicleId: getCurrentCar().vehicleId,
					accountId: getAccountId()
				};
				let data = {
					'fileName': Api.getUserQuery.method,
					'data': params
				};
				console.log("获取车辆信息...");
				request(Api.getUserQuery.url, data, (res) => {
					// this.isLoading = false;
					console.log("获取车辆信息成功...");
					this.identity = res;
					for (let obj of this.useCarList) {
						if (obj.key == this.identity.useCharacter) {
							this.identity.useCarType = obj.value
						}
					}
					console.log("identity:" + JSON.stringify(this.identity));
					this.reqObuInfo();
					this.getBankInfo();
				}, (msg) => {
					uni.showModal({
						title: '提示',
						content: msg
					});
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			},
			//前装获取车辆信息
			fontReqCarInfo() {
				let params = {
					accountId: getAccountId(),
					vehicleId: getCurrentCar().vehicleId
				};
				let data = {

					'data': params
				};
				console.log("获取车辆信息...");
				request(this.$interfaces.userVehicleQuery, data, (data) => {
					let res = data.data
					console.log("获取车辆信息成功...", res);
					this.identity = res;
					for (let obj of this.useCarList) {
						if (obj.key == this.identity.useCharacter) {
							this.identity.useCarType = obj.value
						}
					}
					console.log("identity:" + JSON.stringify(this.identity));
					this.reqObuInfo();
					this.getBankInfo();
				})
			},
			//获取OBU信息
			reqObuInfo() {
				this.isLoading = true;
				let params = {
					vehicleId: getCurrentCar().vehicleId,
					accountId: getAccountId()
				};
				let data = {

					'data': params
				};
				console.log("获取OBU信息...");
				request(this.$interfaces.userOBUQuery, data, (res) => {
					var isNomalObu = false;
					console.log("获取OBU信息成功...",res);
					if (res.data.result == 1) {
						if (res.data.data.status == 1) {
							this.obuinfo = res.data.data;
							isNomalObu = true;
							this.hasObuInfo = true;
						}
						if (isNomalObu == false) {
							console.log("无正常状态的OBU");
							this.obuinfo = res.data.data;
							this.hasCardInfo = true;
							this.isLoading = false;
						}
						this.reqCardInfo();
					}
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			},
			//获取卡信息
			reqCardInfo() {
				this.isLoading = true;
				var isNomalObu = false;
				let params = {
					vehicleId: getCurrentCar().vehicleId,
					accountId: getAccountId()
				};
				let data = {
					
					'data': params
				};
				console.log("获取CPU信息...");
				request(this.$interfaces.userCardQuery, data, (res) => {
					this.isLoading = false;
					console.log("获取CPU信息成功...",res.data);
					if (res.data.result == 1) {
						if (res.data.data.status == 1) {
							this.cardinfo = res.data.data;
							isNomalObu = true;
							this.hasCardInfo = true;
						}
						if (isNomalObu == false) {
							//console.log("无正常状态的CPU卡信息...");
							this.cardinfo = res.data.data;
							this.hasCardInfo = true;
						}
					}
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			},
			//获取签约银行信息
			getBankInfo() {
				//console.log("获取银行信息...");
				this.isLoading = true;
				let params = {
					vehicleId: getCurrentCar().vehicleId,
					accountId: getAccountId()
				};
				let data = {
					
					'data': params
				};
				request(this.$interfaces.accountReList, data, (res) => {
					
					this.isLoading = false;
					res.data.list.forEach((item, index) => {
						if (item.status == 1) {
							
							this.bankCardInfo = item;
							console.log("关联支付渠道列表:"+JSON.stringify(this.bankCardInfo));
							this.hasBankInfo = true;
						}
					});
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					this.isLoading = false;
				})
			}
		}
	}
</script>

<style>
	.c-title {
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #FFFFFF;
	}

	.values {
		-webkit-text-fill-color: #000000;
	}
</style>
