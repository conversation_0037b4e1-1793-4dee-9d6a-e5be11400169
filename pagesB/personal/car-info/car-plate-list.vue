<template>
		<view class="add-car">
			<view class="card-list">
				<view class="card" v-for="(item,index) of carInfoList" :key="index" @click="goPage(index)">
					<CarCard :plateNum="item.plateNum" :plateColor="item.plateColor+''" :status="item.status"></CarCard>
					<view class="status"> 正常 </view>
					<view class="arrow">
						<image src="../../../static/tab-bar/index/right-ar.png" class="right-arrow"></image>
					</view>
				</view>
			</view>
			<tLoading :isShow="isLoading" />
		</view>
</template>

<script>
	import CarCard from "@/pagesB/components/car-card/car-card.vue";
	import Api from "@/common/api/index.js"
	import {
		getTokenId,
		setCurrentCar
	} from '@/common/storageUtil.js';
	import {request} from '@/common/request-1/requestFunc.js'
	export default {
		components:{
			CarCard
		},
		data() {
			return {
				isLoading:false,
				carInfoList:[
					{
						plateNum:'浙K521K9',
						plateColor:0,
						status:'0'
					},
					{
						plateNum:'浙K521K9',
						plateColor:2,
						status:'0'
					},
					{
						plateNum:'浙K521K9',
						plateColor:0,
						status:'0'
					},
				]
			}
		},
		methods: {
			//获取车辆列表
			reqVerhicleList(type) {
				
			},
			goPage(index){
				setCurrentCar(this.carInfoList[index])
				uni.navigateTo({
					url:"/pagesB/personal/car-info/car-info-new"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.add-car{
		display: flex;
		flex-direction:column;
		padding: 28rpx;
		.card-list{
			margin-top: 28rpx;
			.card{
				display: flex;
				background-color: #fff;
				width:700rpx;
				height:180rpx;
				align-items: center;
				border-radius: 10rpx;
				position: relative;
				box-shadow: 0 1px 2px 1px rgba(0,0,0,0.08);
				margin-bottom:20rpx;
				opacity: .8;
				/deep/.index-car-card{
					background-color:#fff;
					height: 130rpx;
					margin-top: 57rpx;
					.son-card{
						width: 420rpx;
						margin-bottom: 40rpx;
					}
				}
				.status{
					margin-left: 60rpx;

				}
				.arrow{
					position: absolute;
					right: 34rpx;
					.right-arrow{
						width: 30rpx;
						height: 30rpx;
					}
				}
			}
		}
	}
</style>
