<template>
	<view>
		<image :src="url" class="qrcode"></image>
	</view>
</template>

<script>
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	import Api from '@/common/api/index.js';
	export default {
		data() {
			return {
				url:''
			}
		},
		onLoad(query) {
			let data = {
				'fileName': Api.getqrcode.method,
				'data': {
					path:"pages/tab-bar/index/index?type=123",
					width:"1000"
				}
			}
			request(Api.getqrcode.url, data, (res) => {
				console.log(res)
				this.url = "data:image/jpeg;base64," + res.buffer
			}, (error) => {
				console.log(error)
			})
			
			
		},
		onShareAppMessage(res) {
			let path = "/pages/tab-bar/index/index"
			if(getChannelId()){
				try{
					let channelObj = JSON.parse(getChannelId())
					if(Object.keys(channelObj) > 0){
						path = `/pages/tab-bar/index/index?id=${channelId.id+1}&chinid=${channelId.chinid}`
					}
				}catch{
					console.log('未获取到渠道')
				}
			}
			console.log(path)
			if (res.from === 'menu') { // 来自页面内分享按钮
				return {
					title: '中国ETC服务',
					path: path,
					imageUrl: '/static/etc/share-logo.png',
					success: (res) => {
						console.log(res)
					},
					fail: (res) => {
						console.log(res)
					}
				}
			}
		},
		methods: {
			
		}
	}
</script>

<style>
	.qrcode{
		width: 200rpx;
		height:200rpx;
	}
</style>
