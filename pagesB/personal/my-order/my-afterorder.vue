<!-- 签约支付方式 -->
<template>
	<view>
		
		<form>
		    <view class="cu-form-group margin-top">
				<view class="title">开始日期</view>
				<picker mode="date" :value="beginDate" start="2019-01-01" end="2030-12-31" @change="DateBeginChange">
					<view class="picker">
						{{beginDate}}
					</view>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="title">结束日期</view>
				<picker mode="date" :value="endDate" start="2019-01-01" end="2030-12-31" @change="DateEndChange">
					<view class="picker">
						{{endDate}}
					</view>
				</picker>
			</view>
		</form>
		<TButton title="查询" @clickButton="reqOrderList" :isLoadding="isBtnLoader"/>
		 
		<view class="pay-account-index">
			<view v-if="myOrderList&&myOrderList.length>0">
				<view class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;">
					<view v-for="(item, index) in myOrderList" :key="index">
						
						<view @click="goOrderDetail(item,index)" >
							<tOrderItem :bankInfo="item" :hideLine="index+1 === myOrderList.length"/>
						</view>
					</view>
				</view>
			</view>
			<view v-else>
				<xw-empty :isShow="true" text="您还没有相关订单" ></xw-empty>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<view class="bottom">
		</view>
	</view>
	
</template>

<script>
	import { setCurrentCar, getCurrentCar,getTokenId,setTokenId,getCurrUserInfo,setCurrUserInfo } from '@/common/storageUtil.js'
	import tOrderItem from '@/pagesB/components/common/t-afterorder-item.vue'
	import Api from '@/common/api/index.js'
	import tLoading from '@/components/common/t-loading.vue';
	import { uni_encodeURIComponent } from '@/common/helper.js';
	import TButton from "@/components/t-button.vue"
	import xwEmpty from '@/pagesB/components/xw-empty/xw-empty';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			tOrderItem,
			tLoading,
			TButton,
			xwEmpty
		},
		data() {
			return {
				isLoading: false,
				num: 1,
				myOrderList: [],
				beginDate:'',
				endDate:'',
				isBtnLoader:false
			}
		},
		
		onReady(){
			let myDate = new Date();
			let nowY = myDate.getFullYear();
			let nowM = myDate.getMonth()+1;
			let nowD = myDate.getDate();
			this.endDate = nowY+"-"+(nowM<10 ? "0" + nowM : nowM)+"-"+(nowD<10 ? "0"+ nowD : nowD);//当前日期
			//获取三十天前日期
			let lw = new Date(myDate - 1000 * 60 * 60 * 24 * 30);//最后一个数字30可改，30天的意思
			let lastY = lw.getFullYear();
			let lastM = lw.getMonth()+1;
			let lastD = lw.getDate();
			this.beginDate=lastY+"-"+(lastM<10 ? "0" + lastM : lastM)+"-"+(lastD<10 ? "0"+ lastD : lastD);//三十天之前日
			// console.log("获取订单列表")
			this.reqOrderList();
		},
		onPullDownRefresh() {
			console.log('refresh');
			setTimeout( () =>{
				this.reqOrderList();
				uni.stopPullDownRefresh();
			}, 1000);
		},
		methods: {
			
			goOrderDetail(item,index) {
				uni.navigateTo({
					url: "/pagesB/personal/my-order/myAfterOrderDetail?allDatas=" + uni_encodeURIComponent(JSON.stringify(item))
				});
			},
			DateBeginChange(e) {
				this.beginDate = e.detail.value;
			},
			DateEndChange(e) {
				this.endDate = e.detail.value;
			},
			reqOrderList(){
				//两个时间比较
				var sDate1 = Date.parse(this.endDate);
				var sDate2 = Date.parse(this.beginDate);
				var dateSpan = sDate2 - sDate1;
				var dateSpan = Math.abs(dateSpan);
				var iDays = Math.floor(dateSpan / (24 * 3600 * 1000));
				if(parseInt(iDays) >30){
					uni.showModal({
						title: '提示',
						content: '时间范围不能超过30天，请重新选择时间段'
					});
					return ;
				}
				
				//uni.getStorageSync("tokenId");
				
				this.isLoading=true;
				// console.log('获取订单列表')
				//let datas=getCurrentCar();
				//console.log('12112'+JSON.stringify(getCurrentCar()))
				//获取当前日期
				// let myDate = new Date();
				// let nowY = myDate.getFullYear();
				// let nowM = myDate.getMonth()+1;
				// let nowD = myDate.getDate()+1;
				// let enddate = nowY+"-"+(nowM<10 ? "0" + nowM : nowM)+"-"+(nowD<10 ? "0"+ nowD : nowD);//当前日期
				// //获取三十天前日期
				// let lw = new Date(myDate - 1000 * 60 * 60 * 24 * 30);//最后一个数字30可改，30天的意思
				// let lastY = lw.getFullYear();
				// let lastM = lw.getMonth()+1;
				// let lastD = lw.getDate()+1;
				// let startdate=lastY+"-"+(lastM<10 ? "0" + lastM : lastM)+"-"+(lastD<10 ? "0"+ lastD : lastD);//三十天之前日期
				let params={
					//tokenId:uni.getStorageSync("tokenId"),
					tokenId:getTokenId(),
					startDate:this.beginDate,
					endDate:this.endDate,
					type:99,
					status:99
				};
				let data = {
					'fileName': Api.afterOrderService.method,
					'data': params
				};
				request(Api.afterOrderService.url, data,(res) => {
					// 这里正常数据返回			
					// console.log('获取订单列表'+JSON.stringify(res))
					this.myOrderList=res.list;
					this.isLoading=false;
				}, (msg)=>{
					this.isLoading=false;
				},(err) => {
					this.isLoading=false;
				})
			}
		}
		
	}
</script>

<style lang="scss">
	
	.p-a-title {
		font-size: 28upx;
		font-weight: bold;
		margin: 20upx 20upx;
		/* border-bottom: 1upx solid #CCCCCC; */
	}
	.pay-account-index{
		display: flex;
		justify-content: center;
		.p-a-content {
			margin: 10upx;
			background: #FCFCFC;
			border-radius: 10upx;
			box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
			.t-order-account{
				border-bottom: none;
			}
		}
	}
	.bottom{
		margin-top: 50upx;
		height: 50upx;
		width: 100%;
	}
</style>

