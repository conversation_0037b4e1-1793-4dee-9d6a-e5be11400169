<!-- 签约支付方式 -->
<template>
	<view>
		<form>
		    <view class="cu-form-group margin-top">
				<view class="title">开始日期</view>
				<picker mode="date" :value="beginDate" start="2019-01-01" end="2030-12-31" @change="DateBeginChange">
					<view class="picker">
						{{beginDate}}
					</view>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="title">结束日期</view>
				<picker mode="date" :value="endDate" start="2019-01-01" end="2030-12-31" @change="DateEndChange">
					<view class="picker">
						{{endDate}}
					</view>
				</picker>
			</view>
			
			<view class="cu-form-group" >
				<view class="title">订单类型</view>
				<picker :model="orderTypeIndex" :range="orderTypeOptions" @change="PickerChangeType">
					<view class="picker">
						{{orderTypeOptions[orderTypeIndex]}}
					</view>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="title">订单状态</view>
				<picker @change="PickerChangeOrderStatus" :model="orderStatusIndex" :range="orderStatusOptions">
					<view class="picker">
						{{orderStatusOptions[orderStatusIndex]}}
					</view>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="title">发货状态</view>
				<picker @change="PickerChangeStatus" :model="expressStatusIndex" :range="expressStatusOptions">
					<view class="picker">
						{{expressStatusOptions[expressStatusIndex]}}
					</view>
				</picker>
			</view>
		</form>
		<TButton title="查询" @clickButton="getOrderList" :isLoadding="isBtnLoader"/>
		
		<view class="pay-account-index">
			<view v-if="myOrderList&&myOrderList.length>0">
				<view class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;">
					<view v-for="(item, index) in myOrderList" :key="index">
						
						<view @click="goOrderDetail(item,index)" >
							<tOrderItem :bankInfo="item" :hideLine="index+1=== myOrderList.length"/>
						</view>
					</view>
				</view>
			</view>
			<view v-else>
				<xw-empty :isShow="true" text="您还没有相关订单" ></xw-empty>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<view class="bottom">
		</view>
	</view>
</template>

<script>
	import { setCurrentCar, getCurrentCar,getTokenId,setTokenId,getCurrUserInfo,setCurrUserInfo } from '@/common/storageUtil.js'
	import tOrderItem from '@/components/common/t-order-item.vue'
	import TButton from "@/components/t-button.vue"
	import Api from '@/common/api/index.js'
	import tLoading from '@/components/common/t-loading.vue';
	import { uni_encodeURIComponent } from '@/common/helper.js';
	import { expressStatusOptions,orderTypeOptions,orderStatusOptions } from '@/common/systemConstant.js'
	import { request } from "@/common/request-1/requestFunc.js"
	import xwEmpty from '@/pagesB/components/xw-empty/xw-empty';
	export default {
		components: {
			tOrderItem,
			tLoading,
			TButton,
			xwEmpty
		},
		data() {
			return {
				isLoading: false,
				num: 1,
				myOrderList: [],
				orderType:'1',
				expressStatus:'1',
				expressStatusIndex:0,
				orderTypeIndex:0,
				orderStatusIndex:0,
				expressStatusOptions,
				orderTypeOptions,
				orderStatusOptions,
				beginDate:'',
				endDate:'',
				isBtnLoader:false
			}
		},
		
		onReady(){
			let myDate = new Date();
			let nowY = myDate.getFullYear();
			let nowM = myDate.getMonth()+1;
			let nowD = myDate.getDate();
			this.endDate = nowY+"-"+(nowM<10 ? "0" + nowM : nowM)+"-"+(nowD<10 ? "0"+ nowD : nowD);//当前日期
			//获取三十天前日期
			let lw = new Date(myDate - 1000 * 60 * 60 * 24 * 30);//最后一个数字30可改，30天的意思
			let lastY = lw.getFullYear();
			let lastM = lw.getMonth()+1;
			let lastD = lw.getDate();
			this.beginDate=lastY+"-"+(lastM<10 ? "0" + lastM : lastM)+"-"+(lastD<10 ? "0"+ lastD : lastD);//三十天之前日
			
			// console.log("获取订单列表")
			this.getOrderList();
		},
		onPullDownRefresh() {
			console.log('refresh');
			setTimeout( () =>{
				this.getOrderList();
				uni.stopPullDownRefresh();
			}, 1000);
		},
		methods: {
			
			goOrderDetail(item,index) {
				//console.log("allDatas:"+uni_encodeURIComponent(JSON.stringify(item)));
				uni.navigateTo({
					url: "/pagesB/personal/my-order/myOrderDetail?allDatas=" + uni_encodeURIComponent(JSON.stringify(item))
				});
			},
			getOrderList(){
				//两个时间比较
				var sDate1 = Date.parse(this.endDate);
				var sDate2 = Date.parse(this.beginDate);
				var dateSpan = sDate2 - sDate1;
				var dateSpan = Math.abs(dateSpan);
				var iDays = Math.floor(dateSpan / (24 * 3600 * 1000));
				if(parseInt(iDays) >30){
					uni.showModal({
						title: '提示',
						content: '时间范围不能超过30天，请重新选择时间段'
					});
					return ;
				}
				
				if(this.expressStatusIndex == 0){//发货状态为全部
				    this.myOrderList = [];
					this.reqAllOrderList(parseInt(this.orderTypeIndex)+1,parseInt(this.orderStatusIndex)+1,1);
					this.reqAllOrderList(parseInt(this.orderTypeIndex)+1,parseInt(this.orderStatusIndex)+1,2);
					this.reqAllOrderList(parseInt(this.orderTypeIndex)+1,parseInt(this.orderStatusIndex)+1,3);
				}else{
					this.reqOrderList(parseInt(this.orderTypeIndex)+1,parseInt(this.orderStatusIndex)+1,parseInt(this.expressStatusIndex));
				}  
			},
			reqOrderList(type,orderStatus,expressStatus){
				console.log("type : "+type);
				console.log("orderStatus : "+orderStatus);
				console.log("expressStatus : "+expressStatus);
				
				//uni.getStorageSync("tokenId");
				
				this.isLoading=true;
				this.isBtnLoader=true;
				// console.log('获取订单列表')
				//let datas=getCurrentCar();
				//console.log('12112'+JSON.stringify(getCurrentCar()))
				//获取当前日期
				// let myDate = new Date();
				// let nowY = myDate.getFullYear();
				// let nowM = myDate.getMonth()+1;
				// let nowD = myDate.getDate();
				// let enddate = nowY+"-"+(nowM<10 ? "0" + nowM : nowM)+"-"+(nowD<10 ? "0"+ nowD : nowD);//当前日期
				// //获取三十天前日期
				// let lw = new Date(myDate - 1000 * 60 * 60 * 24 * 30);//最后一个数字30可改，30天的意思
				// let lastY = lw.getFullYear();
				// let lastM = lw.getMonth()+1;
				// let lastD = lw.getDate();
				// let startdate=lastY+"-"+(lastM<10 ? "0" + lastM : lastM)+"-"+(lastD<10 ? "0"+ lastD : lastD);//三十天之前日期
				let params={
					//tokenId:uni.getStorageSync("tokenId"),
					tokenId:getTokenId(),
					startDate:this.beginDate,
					endDate:this.endDate,
					type:type,
					status:orderStatus,
					expressStatus:expressStatus
				};
				let data = {
					'fileName': Api.getOrderList.method,
					'data': params
				};
				request(Api.getAccountSignchannellist.url, data,(res) => {
					// 这里正常数据返回			
					console.log('获取订单列表'+JSON.stringify(res))
					if(res.list != null){
						if(this.isAddList){
							this.myOrderList = this.myOrderList.concat(res.list);
						}else{
							this.myOrderList = res.list;
						}
					}else{
						this.myOrderList = [];
					}
					this.isLoading=false;
					this.isBtnLoader=false;
				}, (msg)=>{
					this.isLoading=false;
					this.isBtnLoader=false;
				},(err) => {
					// console.log(err)
					this.isLoading=false;
					this.isBtnLoader=false;
				})
			},
			reqAllOrderList(type,orderStatus,expressStatus){
				console.log("type : "+type);
				console.log("orderStatus : "+orderStatus);
				console.log("expressStatus : "+expressStatus);
				
				this.isLoading=true;
				this.isBtnLoader=true;
				let params={
					tokenId:getTokenId(),
					startDate:this.beginDate,
					endDate:this.endDate,
					type:type,
					status:orderStatus,
					expressStatus:expressStatus
				};
				let data = {
					'fileName': Api.getOrderList.method,
					'data': params
				};
				request(Api.getAccountSignchannellist.url, data,(res) => {
					// 这里正常数据返回			
					console.log('获取订单列表'+JSON.stringify(res))
					if(res.list != null){
						this.myOrderList = this.myOrderList.concat(res.list);
					}
					
					this.isLoading=false;
					this.isBtnLoader=false;
				}, (msg)=>{
					this.isLoading=false;
					this.isBtnLoader=false;
				},(err) => {
					// console.log(err)
					this.isLoading=false;
					this.isBtnLoader=false;
				})
			},
			DateBeginChange(e) {
				this.beginDate = e.detail.value;
			},
			DateEndChange(e) {
				this.endDate = e.detail.value;
			},
			PickerChangeType(e) {
				this.orderTypeIndex = e.detail.value;
			},
			PickerChangeStatus(e) {
				this.expressStatusIndex = e.detail.value;
			},
			PickerChangeOrderStatus(e){
				this.orderStatusIndex = e.detail.value
			}
		}
		
	}
</script>

<style lang="scss">
	
	.p-a-title {
		font-size: 28upx;
		font-weight: bold;
		margin: 20upx 20upx;
		/* border-bottom: 1upx solid #CCCCCC; */
	}
	.pay-account-index{
		display: flex;
		justify-content: center;
		.p-a-content {
			margin: 10upx;
			background: #FCFCFC;
			border-radius: 10upx;
			box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
		}
	}
	.bottom{
		margin-top: 50upx;
		height: 50upx;
		width: 100%;
	}
</style>

