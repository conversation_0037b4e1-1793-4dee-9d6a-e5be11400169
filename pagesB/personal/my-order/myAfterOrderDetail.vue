<template>
	<view class="content">
		<view class="content-view">
			<view class="rules-view">
				<view class="title">申请编号:</view> 
				<view class="title-value">{{allDatas.applyId}}</view>
			</view>
			<view class="rules-view">
				<view class="title">申请时间:</view> 
				<view class="title-value">{{allDatas.applyTime | removeTime}}</view> 
			</view>
			<view class="rules-view">
				<view class="title">产品编号:</view> 
				<view class="title-value">{{allDatas.productId}}</view>
				</view>
			<view class="rules-view">
				<view class="title">&nbsp;&nbsp;OBU编号:</view> 
				<view v-if="allDatas.obuId" class="title-value">{{allDatas.obuId}}</view>
			</view>
			<view class="rules-view">
				<view class="title">用户卡编号:</view> 
				<view v-if="allDatas.cardId" class="title-value">{{allDatas.cardId}}</view>
			</view>
			<view class="rules-view">
				<view class="title">服务类型:</view> 
				<view v-if="allDatas.serviceType==1" class="title-value">撤单</view>
				<view v-if="allDatas.serviceType==2" class="title-value">退货</view>
				<view v-if="allDatas.serviceType==3" class="title-value">换签</view>
				<view v-if="allDatas.serviceType==4" class="title-value">换卡</view>
				<view v-if="allDatas.serviceType==5" class="title-value">卡签更换</view>
			</view>
		</view>
		<view class="content-view-new">
			<view class="rules-view">
				<view class="title">订单号:</view> 
				<view class="title-value">{{allDatas.orderId}}</view>
			</view>
			<view class="rules-view">
				<view class="title">快递渠道:</view> 
				<view v-if="allDatas.expressType==0"></view>
				<view v-else class="title-value">{{reverseExpressTypeMap.get(allDatas.expressType+'')}}</view>
			</view>
			<view class="rules-view">
				<view class="title">快递单号:</view> 
				<view v-if="allDatas.expressId" class="title-value">{{allDatas.expressId}}</view>
			</view>
			<view class="rules-view">
				<view class="title">&nbsp;&nbsp;状态:</view>
				<view v-if="allDatas.status==1" class="title-value">申请中</view>
				<view v-if="allDatas.status==2" class="title-value">接收申请</view>
				<view v-if="allDatas.status==3" class="title-value">拒绝申请</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { plateColorToColorMap,vehicleTypeOptions,reverseExpressTypeMap} from '@/common/systemConstant.js';
	import { uni_decodeURIComponent } from '@/common/helper.js';
	
	export default{
		data(){
			return{
				allDatas:{},
				vehicleTypeOptions,
				plateColorToColorMap,
				reverseExpressTypeMap
			}
		},
		filters: {
			removeTime: function (values) {
				if(values && values.indexOf("T")>0){
					// `this` 指向 vm 实例
					return values.replace("T", " ")
				}
			
			}
		},
		onLoad:function(event){
			this.allDatas=JSON.parse(uni_decodeURIComponent(event.allDatas));
			console.log('this.allDatas'+event.allDatas);
		},
		methods:{
		    
		}
	}
</script>

<style lang="scss" scoped>
	.content-view,
	.content-view-new{
		padding:20upx 30upx 20px 0;
		background-color: #fff;
	}
	.content-view-new{
		border-top:2upx dashed #bababa;
		border-bottom:2upx dashed #bababa;
	}
	.rules-view{
		margin-bottom: 20upx;
		font-size: 26upx;
		color: #666666;
		letter-spacing: 2upx;
		display: flex;
		align-items: center;
	
		.title{
			opacity: 0.6;
			font-size: 28rpx;
			color: #333333;
		}
		.title-value{
			font-size: 28rpx;
			color: #333333;
		}
	}
	.rules-view>view:first-child{
		width: 160rpx;
		text-align:right;
		margin-right:20upx;
	}
	.color-btn{
		margin-top: 20upx;
		padding: 10upx;
	}
	.undobutton{
		float: left;
		margin-left: 10upx;
	}
	.returnbutton{
		float: right;
		margin-right: 50upx;
	}
</style>
