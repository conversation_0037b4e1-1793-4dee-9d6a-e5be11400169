<template>
	<view class="content">
		<view class="content-view">
			<view class="rules-view">
				<view class="title">订单编号:</view> 
				<view class="text-value">{{allDatas.orderId}}</view>
			</view>
			<view class="rules-view">
				<view class="title">订单时间:</view> 
				<view class="text-value">{{allDatas.createTime | removeTime}}</view> 
			</view>
			<view class="rules-view">
				<view class="title">订单金额:</view> 
				<view class="text-value">{{allDatas.fee}}</view>
			</view>
			<view class="rules-view">
				<view class="title">订单状态:</view> 
				<view class="title-value">{{orderStatusMap.get(allDatas.status+'')}}</view>
			</view>
			<view class="rules-view">
				<view class="title">物流状态:</view> 
				<view class="title-value">{{expressStatusMap.get(allDatas.expressStatus+'')}}</view>
			</view>
			<view class="rules-view">
				<view class="title">&nbsp;&nbsp;车牌号:</view> 
				<view class="title-value">{{allDatas.plateNum}}</view>
			</view>
			<view class="rules-view">
				<view class="title">车牌颜色:</view> 
				<view class="title-value">{{plateColorToColorMap.get(allDatas.plateColor+'')}}</view>
			</view>
			<view class="rules-view">
				<view class="title">收费类型:</view> 
				<view class="title-value">{{vehicleTypeOptions.get(allDatas.vehicleType+'')}}</view>
			</view>
		</view>
		<view class="content-view-new">
			<view class="rules-view">
				<view class="title">商品类型:</view> 
				<view class="title-value">{{allDatas.sellerName}}</view>
			</view>
			<view class="rules-view">
				<view class="title">商品详情:</view> 
				<view class="title-value">{{allDatas.detail}}</view>
			</view>
			<view class="rules-view">
				<view class="title">&ensp;&ensp;收货人:</view> 
				<view class="title-value">{{allDatas.name}}</view>
			</view>
			<view class="rules-view">
				<view class="title">联系电话:</view> 
				<view class="title-value">{{allDatas.phone}}</view>
			</view>
			<view class="rules-view">
				<label class="title" style="width:160upx;text-align:right;margin-right:20upx;">收货地址:</label> 
				<textarea :value="allDatas.addr" auto-height disabled style="wdith:auto;" class="title-value">
				</textarea>
			</view>
			<view class="rules-view">
				<view class="title">快递类型:</view> 
				<view v-if="allDatas.expressType" class="title-value">{{reverseExpressTypeMap.get(allDatas.expressType+'')}}</view>
			</view>
			<view class="rules-view">
				<view class="title">快递单号:</view>
				<view v-if="allDatas.expressId" class="title-value">{{allDatas.expressId}}</view>
			</view>
		</view>
		<view class="content-view-new pay">
			<view class="rules-view">
				<text class="title">支付渠道名称:</text> 
				<view class="title-value">{{allDatas.channelName}}</view>
			</view>
			<view class="rules-view">
				<text class="title">支付渠道账户:</text> 
				<view class="title-value">{{allDatas.accountNum}}</view>
			</view>
		</view>
		<view class="bottom">
			<view class="color-btn">
				<button class="cu-btn round undobutton line-new-blue" :disabled="!isAfterSale1" @tap="aftersale(1)">撤销订单</button>
				<button class="cu-btn round returnbutton line-new-blue" :disabled="!isAfterSale2" @tap="aftersale(2)">申请退货</button>
			</view>
			<view class="color-btn">
				<button class="cu-btn round undobutton line-new-blue" :disabled="!isAfterSale5" @tap="aftersale(5)">卡签更换</button>
				<button class="cu-btn round line-new-blue" :disabled="!isAfterSale3" @tap="aftersale(3)">申请换签</button>
				<button class="cu-btn round returnbutton line-new-blue" :disabled="!isAfterSale4" @tap="aftersale(4)">申请换卡</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { plateColorToColorMap,vehicleTypeOptions,orderStatusMap,expressStatusMap,expressTypeMap,reverseExpressTypeMap} from '@/common/systemConstant.js';
	import { uni_encodeURIComponent,uni_decodeURIComponent } from '@/common/helper.js';
	
	export default{
		data(){
			return{
				allDatas:{},
				vehicleTypeOptions,
				plateColorToColorMap,
				orderStatusMap,
				expressStatusMap,
				expressTypeMap,
				reverseExpressTypeMap,
				isAfterSale1:false,
				isAfterSale2:false,
				isAfterSale3:false,
				isAfterSale4:false,
				isAfterSale5:false
			}
		},
		filters: {
			removeTime: function (values) {
				if(values && values.indexOf("T")>0){
					// `this` 指向 vm 实例
					return values.replace("T", " ")
				}
			
			}
		},
		onLoad:function(event){
			//this.allDatas=JSON.parse(event.allDatas);
			this.allDatas=JSON.parse(uni_decodeURIComponent(event.allDatas));
			
			if(this.allDatas.expressStatus == 2){//已发货
				this.isAfterSale1 = false;
				this.isAfterSale2 = true;
				//this.isAfterSale3 = true;
				//this.isAfterSale4 = true;
			}else if(this.allDatas.expressStatus == 3){//已激活
				this.isAfterSale3 = true;
				this.isAfterSale4 = true;
				this.isAfterSale5 = true;
			}
			
			if(this.allDatas.status == 3){
				if(this.allDatas.expressStatus == 1){
					   this.isAfterSale1 = true;
				}
			}else if(this.allDatas.status == 4){
				this.isAfterSale1 = false;
				this.isAfterSale2 = false;
				this.isAfterSale3 = false;
				this.isAfterSale4 = false;
			}else if(this.allDatas.status == 5 || this.allDatas.status == 6){
				this.isAfterSale1 = false;
				this.isAfterSale2 = false;
				this.isAfterSale3 = false;
				this.isAfterSale4 = false;
			}
		},
		methods:{
			aftersale(type){
				console.log("this.allDatas.status:"+JSON.stringify(this.allDatas));//this.allDatas
				//添加状态判断
				if(type==1){
					if(this.allDatas.status == 4){
						uni.showModal({
							title: '提示',
							content: '订单已撤销，不能重复撤销'
						});
						return;
					}else if(this.allDatas.status == 5){
						uni.showModal({
							title: '提示',
							content: '订单退货中，不能进行撤销'
						});
						return;
					}else if(this.allDatas.status == 6){
						uni.showModal({
							title: '提示',
							content: '订单已退货，不能进行撤销'
						});
						return;
					}
				}else if(type==2){
					if(this.allDatas.status == 4){
						uni.showModal({
							title: '提示',
							content: '订单已撤销，不能进行退货'
						});
						return;
					}else if(this.allDatas.status == 5){
						uni.showModal({
							title: '提示',
							content: '订单退货中，不能进行退货'
						});
						return;
					}else if(this.allDatas.status == 6){
						uni.showModal({
							title: '提示',
							content: '订单已退货，不能进行退货'
						});
						return;
					}
				}else if(type==3){
					if(this.allDatas.status == 4){
						uni.showModal({
							title: '提示',
							content: '订单已撤销，不能进行换签'
						});
						return;
					}else if(this.allDatas.status == 5){
						uni.showModal({
							title: '提示',
							content: '订单退货中，不能进行换签'
						});
						return;
					}else if(this.allDatas.status == 6){
						uni.showModal({
							title: '提示',
							content: '订单已退货，不能进行换签'
						});
						return;
					}
				}else if(type==4){
					if(this.allDatas.status == 4){
						uni.showModal({
							title: '提示',
							content: '订单已撤销，不能进行换卡'
						});
						return;
					}else if(this.allDatas.status == 5){
						uni.showModal({
							title: '提示',
							content: '订单退货中，不能进行换卡'
						});
						return;
					}else if(this.allDatas.status == 6){
						uni.showModal({
							title: '提示',
							content: '订单已退货，不能进行换卡'
						});
						return;
					}
				}
			
				uni.navigateTo({   
					url: '/pagesB/personal/my-order/order-aftersale?allDatas='+uni_encodeURIComponent(JSON.stringify(this.allDatas))+'&type='+type
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content-view,
	.content-view-new{
		padding:20upx 30upx 20px 0;
		background-color: #fff;
	}
	.content-view-new{
		border-top:2upx dashed #E5E5E5;
	}
	.pay{
		padding-left:20rpx;
		border-bottom:2upx dashed #E5E5E5;
	}
	.rules-view{
		margin-bottom: 20upx;
		font-size: 26upx;
		color: #666666;
		letter-spacing: 2upx;
		display: flex;
		align-items: center;

		.title{
			opacity: 0.6;
			font-size: 28rpx;
			color: #333333;
		}
		.title-value{
			font-size: 28rpx;
			color: #333333;
		}
	}
	.rules-view>view:first-child{
		width:150upx;
		text-align:right;
		margin-right:20upx;
	}
	.color-btn{
		padding: 10upx;
		height: 90rpx;
		width: 100%;
		text-align: center;
		
		/deep/.cu-btn{
			width: 168rpx;
			height:56rpx;
			opacity: 1;
			font-size: 26rpx;
		}
	}
	.undobutton{
		float: left;
		margin-left: 10upx;
	}
	.centerbutton{
		text-align: center;
	}
	.returnbutton{
		float: right;
		margin-right: 50upx;
	}
	
	.bottom{
		padding: 20rpx 0;
		padding-left: 30rpx;
		width: 100%;
		background-color: #fff;
	}
</style>
