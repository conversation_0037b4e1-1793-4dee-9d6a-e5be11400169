<template>
	<view>
		<view class="c-title" v-if="type==1">撤销订单</view>
		<view class="c-title" v-if="type==2">申请退货</view>
		<view class="c-title" v-if="type==3">申请换签</view>
		<view class="c-title" v-if="type==4">申请换卡</view>
		<view class="c-title" v-if="type==5">卡签更换</view>
		<form>
			<view class="cu-form-group">
				<view class="title">车牌号:</view>
				<input :value="plateNum" disabled></input>
				<!-- <text class='cuIcon-write text-blue'></text> -->
			</view>

			<view class="cu-form-group" v-if="type==1">
				<view class="title">申请撤销原因:</view>
				<input placeholder="请输入撤销订单原因" v-model="reason"></input>
			</view>
			<view class="cu-form-group" v-if="type==2">
				<view class="title">申请退货原因:</view>
				<input placeholder="请输入退货订单原因" v-model="reason"></input>
			</view>
			<view class="cu-form-group" v-if="type==3">
				<view class="title">申请换签原因:</view>
				<input placeholder="请输入换签订单原因" v-model="reason"></input>
			</view>
			<view class="cu-form-group" v-if="type==4">
				<view class="title">申请换卡原因:</view>
				<input placeholder="请输入换卡订单原因" v-model="reason"></input>
			</view>
			<view class="cu-form-group" v-if="type==5">
				<view class="title">卡签更换原因:</view>
				<input placeholder="请输入卡签更换原因" v-model="reason"></input>
			</view>

			<view class="cu-form-group" v-if="type==2">
				<view class="title">快递类型:</view>
				<input :value="expressType"></input>
				<picker @change="PickerChange" v-model="expressType" :range="expressTypeOptions">
					<view class="express-sel">
						选择
					</view>
				</picker>
			</view>

			<view class="cu-form-group" v-if="type==2">
				<view class="title">快递单号:</view>
				<input placeholder="请输入快递单号" v-model="expressId"></input>
			</view>
		</form>
		<TButton title="提交申请" @clickButton="submitRequest" :isLoadding="isBtnLoader" />
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import Api from '@/common/api/index.js'
	import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue';
	import {
		expressTypeOptions,
		expressTypeMap
	} from '@/common/systemConstant.js'
	import {
		getCurrentCar,
		setCurrentCar,
		getCurrUserInfo,
		setCurrUserInfo,
		getTokenId,
		setTokenId,
		getAccountId
	} from '@/common/storageUtil.js'
	import {
		dateFtt,
		isNumorChar
	} from '@/common/helper.js';
	import {
		uni_encodeURIComponent,
		uni_decodeURIComponent
	} from '@/common/helper.js';
	import {
		request,
		getCarList
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			TButton,
			tLoading
		},
		data() {
			return {
				isBtnLoader: false,
				isLoading: false,
				timeCount: 120,
				timer: null,
				type: 1, //1:撤销订单  2:申请退货  3:申请换签  4:申请换卡  5:卡签更换
				expressTypeOptions,
				expressTypeMap,
				expressType: '',
				expressId: '',
				reason: '',
				plateNum: '',
				plateColor: '',
				vehicleId: '',
				allDatas: {}
			}
		},
		onLoad: function(event) { //option为object类型，会序列化上个页面传递的参数
			//console.log("order-afterasle onLoad",event.allDatas);
			//console.log("order-afterasle onLoad",event.type);

			this.allDatas = JSON.parse(uni_decodeURIComponent(event.allDatas));
			this.type = event.type;
			this.plateNum = this.allDatas.plateNum;
			this.plateColor = this.allDatas.plateColor;
		},
		methods: {
			submitRequest() {
				if (!this.reason) {
					uni.showToast({
						title: '请输入原因',
						icon: 'none'
					});
					return;
				}
				if (this.type == 2) {
					if (!this.expressType) {
						uni.showToast({
							title: '请选择快递类型',
							icon: 'none'
						});
						return;
					}
					if (!this.expressId) {
						uni.showToast({
							title: '请输入快递单号',
							icon: 'none'
						});
						return;
					}

					//快递单号校验
					const isNumOrChar = isNumorChar(this.expressId);
					if (!isNumOrChar) {
						uni.showModal({
							content: "快递单号含非法字符，请仔细核对快递单号！",
							showCancel: false
						})
						return;
					}
				}
				this.reqVerhicleList();
			},
			PickerChange(e) {
				//this.expressType = this.expressTypeMap.get(this.expressTypeOptions[e.detail.value]);
				this.expressType = this.expressTypeOptions[e.detail.value];

				console.log("快递种类：" + this.expressType);
			},
			timeUp() {
				clearInterval(this.timer)
				this.timeCount = 10;
				// this.$emit('timeup')
			},
			reqOrderAfterSale() {
				this.isLoading = true;
				this.isBtnLoader = true;

				let myDate = new Date();
				console.log("myDate" + myDate);
				//var myDateString = myDate.getFullYear()+'-'+myDate.getMonth()+'-'+myDate.getDay()+'T'+myDate.getHours()+':'+myDate.getMinutes()+':'+myDate.getSeconds();
				var myDateString = dateFtt("yyyy-MM-ddThh:mm:ss", myDate);
				console.log("myDateString:" + myDateString);
				var params = {
					tokenId: getTokenId(),
					accountId: getAccountId(),
					vehicleId: this.vehicleId,
					productId: this.allDatas.productId,
					serviceType: this.type,
					applyTime: myDateString,
					orderId: this.allDatas.orderId,
					msg: this.reason
				};
				if (this.type == 2) {
					params.expressType = this.expressTypeMap.get(this.expressType)
					params.expressId = this.expressId
				}
				console.log("params:" + JSON.stringify(params));
				let data = {
					'fileName': Api.orderService.method,
					'data': params
				};
				request(Api.orderService.url, data, (res) => {
					// 这里正常数据返回
					// console.log(res);
					this.isLoading = false;
					this.isBtnLoader = false;

					uni.navigateTo({
						url: '/pagesA/etc/order-success/orderafter-success?type=' + this.type,
					});


				}, (msg) => {
					this.isLoading = false;
					this.isBtnLoader = false;
					this.isLoading = false;
				}, (err) => {
					// console.log(err)
					this.isLoading = false;
					this.isBtnLoader = false;
				})
			},
			//获取车辆列表
			reqVerhicleList() {
				this.isLoading = true;
				this.isBtnLoader = true;
				getCarList(getAccountId(),(res)=>{
					this.isLoading = false;
					this.isBtnLoader = false;
					let vehicleDatas = res.listData;
					console.log('车辆列表：' + res.listData)
					if (vehicleDatas.length > 0) {
						console.log("vehicleDatas.length>0");
						vehicleDatas.forEach((item, index) => {
							console.log("item.plateNum:" + item.plateNum);
							console.log("this.allDatas.plateNum:" + this.allDatas.plateNum);
							if (item.plateNum == this.allDatas.plateNum && item.plateColor == this.allDatas.plateColor) {
								this.vehicleId = item.vehicleId;
								console.log("this.vehicleId" + this.vehicleId);
							}
						});
						this.reqOrderAfterSale();
					} else {
						uni.showModal({
							title: '提示',
							content: '未找到对应车辆信息'
						});
					}
				},()=>{
					this.isLoading = false;
					this.topLoading = false;
				},()=>{
					this.isLoading = false;
					this.topLoading = false;
				})
			}
		}
	}
</script>

<style>
	.c-title {
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #FFFFFF;
	}

	,
	.cu-form-new-group {
		padding: 1upx 20upx;
		display: flex;
		align-items: center;
		min-height: 100upx;
		justify-content: flex-end;
	}

	.sendcode {
		color: #1684fb;
	}

	.express-sel {
		float: right;
		color: #1684fb;
	}

	.color-sel {
		float: right;
		color: #007AFF;
	}
</style>
