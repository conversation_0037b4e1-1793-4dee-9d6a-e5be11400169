<!-- 变更支付方式 -->
<template>
	<view>
		<view class="pay-account-index">
			<!--
			<EtcSteps :num="num"/>
			-->
			<view v-if="bankInfoList&&bankInfoList.length>0">
				<view class="p-a-title">
					已签约渠道
					<!-- <tCreditTitle title="当前已签约" /> -->
				</view>
				<view class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;">
					<block v-for="(item, index) in bankInfoList" :key="index">
						<view @click="goSignDetail(item,index)">
							<tPayAccountForShow :bankInfo="item" :showArrow="false" />
						</view>
					</block>
				</view>
			</view>
			<view @click="addNewPayAccount">
				<PayAccountNew />
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import {
		setStore,
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getCurrUserInfo,
		setCurrUserInfo,
		setBusinessTypes,
		getAccountId
	} from '@/common/storageUtil.js'
	import TButton from "@/components/t-button.vue"
	import tPayAccountForShow from '@/components/common/t-pay-account-for-show.vue'
	import EtcSteps from "@/components/etc-steps/etc-steps.vue"
	import Api from '@/common/api/index.js'
	import {
		mainFlowControl
	} from '@/common/flowControl.js';
	import {
		flowControlStep
	} from '@/common/systemConstant.js'
	import tLoading from '@/components/common/t-loading.vue';
	import PayAccountNew from "@/components/common/t-pay-account-new.vue";
	import {
		uni_encodeURIComponent
	} from '@/common/helper.js';
	import neilModal from '@/components/neil-modal/neil-modal.vue';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			TButton,
			tPayAccountForShow,
			EtcSteps,
			tLoading,
			PayAccountNew,
			neilModal
		},
		data() {
			return {
				num: 2,
				showEntry: false,
				bankInfoList: [],
				isLoading: false,
			}
		},
		onPullDownRefresh() {
			console.log('refresh');
			setTimeout(() => {
				this.reqHavedPayWay();
				uni.stopPullDownRefresh();
			}, 1000);
		},
		onReady() {
			this.reqHavedPayWay();
		},
		methods: {
			goSignDetail(item) {
				const {
					signChannelId,
					type,
					cardType,
					account,
					status
				} = item;
				//uni.navigateTo({
				//	url: '/pages/etc/sign-pay-account/sign-pay-account'
				// url: '/pages/uni-ownMes/pay-account-manage/pay-account-detail/pay-account-detail?signChannelId='
				// + signChannelId +'&type='+type+'&cardType='+cardType+'&account='+account
				//})
			},

			reqHavedPayWay() {
				this.isLoading = true;

				let params = {

					accountId: getAccountId()
				};
				let data = {

					'data': params
				};
				request(this.$interfaces.accountSignChannelList, data, (res) => {
					// 这里正常数据返回			
					// console.log('已签约列表'+JSON.stringify(res))
					this.isLoading = false;
					this.bankInfoList = res.data.list;
				}, (msg) => {
					this.isLoading = false;
				}, (err) => {
					// console.log(err)
					this.isLoading = false;
				})
			},
			addNewPayAccount(type) {
				setStore('addSignCount',1)
				uni.navigateTo({
					url: '/pagesA/etc/pay-account-list/pay-account-list'
				});
			}
		},

	}
</script>

<style scoped lang="scss">
	.p-a-title {
		font-size: 28upx;
		font-weight: bold;
		margin: 20upx 20upx;
		/* border-bottom: 1upx solid #CCCCCC; */
	}

	.modal {
		/deep/.neil-modal {
			.neil-modal__header {
				text {
					color: #000000 !important;
					background-color: #fff !important;
				}
			}

			.neil-modal__footer-left,
			.neil-modal__footer-right {
				color: #47A8EE !important;
				background-color: #FFF;
				border: 1px solid #1D82D2;
				border-radius: 10rpx;
				height: 70rpx;
				line-height: 70rpx;
			}

			.neil-modal__footer-right {
				color: #fff !important;
				background-color: #1D82D2;
				border: 1px solid#1D82D2;
			}
		}

		.dialog {
			.user-btn {
				/deep/.cu-btn {
					position: relative;
					font-size: 26rpx;
					height: 90rpx;
				}
			}
		}
	}

	.p-a-content {
		margin: 20upx;
		background: #FCFCFC;
		border-radius: 16upx;
	}
</style>
