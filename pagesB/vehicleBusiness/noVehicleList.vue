<template>
	<view class="no-vehicle">
		<view class="tips">
			您可以
			<text class="bind" @click="goBind">{{businessType=='recharge'?'绑定ETC后充值':'绑定ETC后'}}</text>，
			<text v-if="businessType=='afterPay'">直接选择车辆进行操作，或点击下方按钮输入车牌号操作</text>
			<text v-if="businessType=='recharge'">或点击下方按钮输入车牌号充值</text>
	
		</view>
		<view class="weui-btn_wrapper">
			<TButton :title="btnTitle" @clickButton="goBusinessHandle"></TButton>
		</view>
	</view>
</template>

<script>
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType
	} from '@/common/method/filter.js';
	import {
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getCurrUserInfo,
		setCurrUserInfo,
		getAccountId,
		setBusinessTypes,
		setStore,
		getStore,
		setEtcVehicle
	} from '@/common/storageUtil.js';
	import TButton from '@/components/t-button.vue';
	import tLoading from '@/components/common/t-loading.vue';
	export default {
		data() {
			return {
				isNoVehicle: false,
				isLoading: false,
				formData: {},
				bindTicket: '',
				vehicleAllDataList: [],
				businessType: '',
				btnTitle: '',
				pageConfig: [{
					type: 'blackList',
					url: '/pagesB/cardBusiness/blackList/p-blackInfo',
					title: '黑名单查询',
				}]
			}
		},
		components: {
			tLoading,
			TButton
		},
		onLoad(obj) {
			this.businessType = obj.fontType;
			console.log(obj, '传过来的obj');
			if (obj.fontType == 'recharge') {
				this.isShowBtn = true;
			}
			if (obj.fontType === 'afterSale') {
				uni.setNavigationBarTitle({
					title: '车辆售后'
				});
				this.isAfter = true;
			}
			let setTitle = {
				recharge: '充值',
				rechargeList: '充值记录',
				carPayList: '通行记录',
				blackList: '黑名单查询',
				loadRecharge: '圈存充值',
				newApply: '新发申请单',
				afterPay: '欠费补缴',
			};
			let setBtnTitle = {
				afterPay: '补缴其他车辆',
				recharge:'充值其他车辆'
			}
			if (obj.fontType && setTitle[obj.fontType]) {
				uni.setNavigationBarTitle({
					title: setTitle[obj.fontType]
				});
			}
			if (obj.fontType && setBtnTitle[obj.fontType]) {
				this.btnTitle = setBtnTitle[obj.fontType]
			}
		},
		methods: {
			goBind() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList?type=' + this.businessType
				});
			},
			goBusinessHandle() {
				if (this.businessType == 'afterPay') {
					uni.navigateTo({
						url: '/pagesC/afterPay/searchInfo/searchInfo'
					});
				}
				if (this.businessType == 'recharge') {
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/rechargeOther/rechargeOther'
					});
				}
				return
			},
		}
	}
</script>

<style scoped lang="scss">
	.no-vehicle {
		padding: 70rpx 60rpx;
	}

	.no-vehicle .weui-btn_wrapper {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
	}

	.no-vehicle {
		.tips {
			.bind {
				line-height: 60rpx;
				font-size: 34rpx;
				color: #2993db;
				margin: 0 8upx;
				font-weight: bold;
			}
		}
	}
</style>
