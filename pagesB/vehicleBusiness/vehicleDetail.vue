<template>
	<view class="vehicle-detail">
		<!-- 车辆信息 -->
		<view class="weui-card vehicle-detail-item">
			<view class="weui-card-hd">
				<view class="weui-card-hd-wrapper">
					<view class="weui-card-hd-title">车辆信息</view>
				</view>
			</view>
			<view class="weui-card-bd">
				<view class="list vehicle-info">
					<view class="list-item">
						<view class="list-item_label">
							车牌号码
						</view>
						<view class="list-item_value">
							{{vehicleDetail.vehicleCode}}[{{getVehicleColor(vehicleDetail.vehicleColor)}}]
						</view>
					</view>

					<view class="list-item">
						<view class="list-item_label">
							收费车型
						</view>
						<view class="list-item_value">
							{{getVehicleClassType(vehicleDetail.vehicleClass)}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							ETC状态
						</view>
						<view :class="vehicleDetail.cardStatus=='正常'?'list-item_value':'list-item_value abnormal'">
							{{vehicleDetail.cardStatus}}
						</view>
					</view>
					<view class="list-item" v-if="vehicleDetail.cardStatus!='正常'">
						<view class="list-item_label">
							限制时间
						</view>
						<view class="list-item_value abnormal">
							{{vehicleDetail.cardStopTime}}
						</view>
					</view>
					<view class="list-item" v-if="vehicleDetail.cardStatus!='正常'">
						<view class="list-item_label">
							限制原因
						</view>
						<view class="list-item_value abnormal">
							{{vehicleDetail.cardStopReason}}
						</view>
					</view>

					<view class="" v-if="vehicleDetail.cardStatus!='正常'">
						<view class="method" @click='method'>
							查看处理办法
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 签约信息 -->
		<view class="weui-card vehicle-detail-item" v-if="vehicleDetail.contract">
			<view class="weui-card-hd">
				<view class="weui-card-hd-wrapper">
					<view class="weui-card-hd-title">产品信息</view>
				</view>
			</view>
			<view class="weui-card-bd">
				<view class="list vehicle-info">
					<view class="list-item">
						<view class="list-item_label">
							签约银行
						</view>
						<view class="list-item_value">
							{{vehicleDetail.contract.bankPayName?vehicleDetail.contract.bankPayName:''}}
						</view>
					</view>
					<view class="list-item" v-if="vehicleDetail.cardProduct=='4'">
						<view class="list-item_label">
							银行卡类型
						</view>
						<view class="list-item_value">
							{{vehicleDetail.contract.bankCardType_str?vehicleDetail.contract.bankCardType_str:''}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							银行卡号
						</view>
						<view class="list-item_value">
							{{vehicleDetail.contract.bankAccount?vehicleDetail.contract.bankAccount:''}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							签约状态
						</view>
						<view class="list-item_value">
							{{vehicleDetail.contract.status=='2'?'正常':'未签约'}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 产品信息 -->
		<view class="weui-card vehicle-detail-item" v-else>
			<view class="weui-card-hd">
				<view class="weui-card-hd-wrapper">
					<view class="weui-card-hd-title">产品信息</view>
				</view>
			</view>
			<view class="weui-card-bd">
				<view class="list vehicle-info">
					<view class="list-item">
						<view class="list-item_label">
							产品名称
						</view>
						<view class="list-item_value">
							{{gxCardTypeFilter(vehicleDetail.cardProduct)}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label"
							v-if='vehicleDetail.cardProduct=="5"||vehicleDetail.cardProduct=="7"||vehicleDetail.cardProduct=="9"'>
							<view v-if='vehicleDetail.cardProduct=="5"'>卡账余额</view>
							<view v-if='vehicleDetail.cardProduct=="9"'>客账余额</view>
							<view v-if='vehicleDetail.cardProduct=="7"'>出账日期</view>
						</view>
						<view class="list-item_label" v-else>
							账户可用余额


						</view>
						<view class="list-item_value" v-if='vehicleDetail.cardProduct=="7"'>
							{{vehicleDetail.billingDate}}
						</view>
						<view class="list-item_value" v-else>
							{{moneyFilter(vehicleDetail.cardAmount)}}元
						</view>
					</view>
					<view class="list-item"
						v-if='vehicleDetail.cardProduct=="0"||vehicleDetail.cardProduct=="9"||vehicleDetail.cardProduct=="5"'>
						<view class="list-item_label">
							最低预存金标准值
						</view>
						<view class="list-item_value">
							{{moneyFilter(vehicleDetail.blackAmount)}}元
						</view>
					</view>
					<view class="list-item g-flex g-flex-center"
						v-if='vehicleDetail.cardProduct=="0"||vehicleDetail.cardProduct=="9"||vehicleDetail.cardProduct=="5"'>
						<view class="list-item_label">
							提醒线
						</view>
						<view class="list-item_value g-flex g-flex-justify" style="position: relative;">
							{{moneyFilter(vehicleDetail.warringAmount)}}元 <text
								v-if='vehicleDetail.cardProduct=="5"||vehicleDetail.cardProduct=="0"' class="edit-btn"
								@click="dialogVisible=true">点击修改</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 设备信息 -->
		<view class="weui-card vehicle-detail-item">
			<view class="weui-card-hd">
				<view class="weui-card-hd-wrapper">
					<view class="weui-card-hd-title">设备信息</view>
				</view>
			</view>
			<view class="weui-card-bd">
				<view class="list vehicle-info">
					<view class="list-item">
						<view class="list-item_label">
							ETC卡号
						</view>
						<view class="list-item_value">
							{{vehicleDetail.cardNo}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							质保期限
						</view>
						<view class="list-item_value">
							{{vehicleDetail.cardWarrantyPeriod}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							OBU编号
						</view>
						<view class="list-item_value">
							{{vehicleDetail.obuNo}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							质保期限
						</view>
						<view class="list-item_value">
							{{vehicleDetail.obuWarrantyPeriod}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<TModal :showModal='dialogVisible' :showCancelFlag='true' @okModal='confirmHandle'
			@cancelModal='dialogVisible=false' okText='确认' modalTitle='修改提醒线'>
			<form slot='content' class="sellPay-Info">
				<view class="cu-form-group">
					<view class="title">原提醒线:</view>
					<input :value="moneyFilter(vehicleDetail.warringAmount)" disabled></input>
					<view class="value">元</view>
				</view>
				<view class="cu-form-group">
					<view class="title">新提醒线:</view>
					<input placeholder="请输入新提醒线" v-model="form.warnLine"></input>
					<view class="value">元</view>
				</view>
				<view class="cu-form-group tips">
					提示：提醒线指卡内余额低于限值时，“广西捷通”公众号给您发送的消息提醒
				</view>

			</form>
		</TModal>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import TModal from '@/components/t-modal/t-modal.vue'

	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		gxCardTypeFilter
	} from '@/common/method/filter.js';

	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,
		getLoginUserInfo,
		getEtcAccountInfo,
		setOpenid,
		setCurrentCar
	} from "@/common/storageUtil.js";
	import {
		noPassByCardNo,
		noPassByName
	} from '@/common/util.js'
	import float from '@/common/method/float.js'
	import netAccountInfo from '@/pagesB/components/netAccountInfo/index.vue'
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		components: {
			TButton,
			TModal,
			tLoading,
			netAccountInfo
		},
		data() {
			return {

				dialogVisible: false,
				form: {
					warnLine: '',
					netUserId: '',
					vehicleCode: '',
					vehicleColor: '',
					cardNo: ''

				}
			};
		},
		onLoad(options) {

		},
		created() {

		},
		computed: {
			...mapGetters(['vehicleDetail'])
		},
		methods: {
			getVehicleColor,
			getVehicleClassType,
			gxCardTypeFilter,
			moneyFilter(value) {
				if (!value || value == 0) {
					return value
				}
				return float.div(value, 100)
			},
			method() {
				if (this.vehicleDetail.solutionStatus == '5') {
					let url = 'https://mp.weixin.qq.com/s/_0mvlJbF1BTFrr24s2W4qw'
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(url)
					})
					return
				}
				uni.showModal({
					title: "处理办法",
					content: this.vehicleDetail.solution,
					showCancel: false,
					confirmText: this.vehicleDetail.solutionStatus == '1' ? '前往补缴' : '确定',
					success: (res) => {
						if (res && this.vehicleDetail.solutionStatus == '0') {
							if (this.vehicleDetail.cardProduct == '9') {
								this.jumpAccountRecharge()
								return
							}
							this.sendVehicleBizSearch()
						}
						if (res && this.vehicleDetail.solutionStatus == '1') {
							let nextData = {
								vehicle_code: this.vehicleDetail.vehicleCode,
								vehicle_color: this.vehicleDetail.vehicleColor,
							}
							uni.navigateTo({
								url: '/pagesC/afterPay/recordDetail/recordDetail?nextData=' +
									encodeURIComponent(JSON
										.stringify(nextData))
							})
						}

					}
				});

			},
			jumpAccountRecharge() {
				let params = JSON.parse(JSON.stringify(this.vehicleDetail.clientAccount))
				params.bindVehicleList = JSON.stringify(params.bindVehicleList)
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/rechargeAccount/index?' + this.objToUrlParam(params)
				})
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},
			sendVehicleBizSearch() {
				setCurrentCar({})
				let data = {
					routePath: this.$interfaces.vehicleBizSearch.method,
					bizContent: {
						vehicle_code: this.vehicleDetail.vehicleCode,
						vehicle_color: this.vehicleDetail.vehicleColor
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false

						if (res.code == 200 && res.data && res.data.length) {
							let item = res.data[0];
							if (this.vehicleDetail.vehicleCode == item.vehicle_code && this.vehicleDetail.cardNo ==
								item
								.cpu_card_id) {

								this.goRechargeBusinessHandle(item);
							}

						}
					})
					.catch((error) => {
						console.log(error);
						this.isLoading = false
					})
			},
			// 根据查询出的卡类型进行不同充值业务
			goRechargeBusinessHandle(item) {
				if (item.gx_card_type == '5' || item.gx_card_type == '8') {
					setCurrentCar(item)
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/recharge/p-recharge'
					})
					return
				}
				if (item.gx_card_type == '0') {
					setCurrentCar(item)
					uni.navigateTo({
						url: '/pagesB/loadBusiness/recharge/recharge'
					})
					return
				}
			},

			confirmHandle() {
				if (!this.form.warnLine) {
					uni.showModal({
						title: "提示",
						content: '请输入新提醒线值',
						showCancel: false,
					});
					return
				}
				this.form.vehicleCode = this.vehicleDetail.vehicleCode
				this.form.vehicleColor = this.vehicleDetail.vehicleColor
				this.form.cardNo = this.vehicleDetail.cardNo
				this.form.netUserId = getLoginUserInfo() ? getLoginUserInfo().userIdStr : ''
				let params = JSON.parse(JSON.stringify(this.form))
				params.warnLine = params.warnLine * 100
				// let data = {
				// 	routePath: this.$interfaces.editWarnLine.method,
				// 	bizContent: {
				// 		...params
				// 	}
				// };
				this.$request.post(this.$interfaces.editWarnLine, {
						data: params
					})
					.then(res => {
						if (res.code == 200) {
							this.dialogVisible = false
							uni.showModal({
								title: "提示",
								content: '提醒线修改成功',
								showCancel: false,
								success: (res) => {
									if (res) {
										uni.navigateBack({
											delta: 1
										});
									}
								}
							});
							this.form.warnLine = ''
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
					})


			},
		}
	};
</script>

<style lang="scss" scoped>
	.vehicle-detail {
		margin-bottom: 40rpx;
	}

	.vehicle-detail-item {
		margin: 20rpx 20rpx 0 20rpx;

		/deep/ .weui-card-hd {
			font-size: 30rpx;
			color: #323435;
			padding: 20rpx 30rpx 30rpx 30rpx;
		}

		/deep/ .weui-card-bd {
			padding-bottom: 30rpx;

			.list {
				.list-item {
					display: flex;
					margin-bottom: 20rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.list-item_label {
						font-size: 26rpx;
						width: 160rpx;
						margin-right: 15rpx;
						font-weight: 400;
						color: #999999;
					}

					.abnormal {
						color: #F65B5B !important;
					}

					.list-item_value {
						flex: 1;
						font-size: 26rpx;
						font-weight: 400;
						color: #333333;
					}
				}
			}
		}
	}

	.edit-btn {
		position: absolute;
		left: 65%;
		top: -27%;
		height: 50rpx;
		text-align: center;
		padding: 2rpx 8rpx;
		width: 160rpx;
		font-weight: 400;
		color: #5591FF;
		font-size: 28rpx;
		border-radius: 15rpx;
		border: 2rpx solid #5591FF;
	}



	.method {
		height: 50rpx;
		text-align: center;
		line-height: 50rpx;
		padding: 2rpx 10rpx;
		margin: 10rpx 30rpx;
		font-weight: 400;
		color: #5591FF;
		font-size: 28rpx;
		border-radius: 15rpx;
		border: 2rpx solid #5591FF;
	}

	.tips {
		text-align: left;
		font-size: 26rpx;
		color: #828c82;
	}

	.sellPay-Info .cu-form-group .value {
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}
</style>