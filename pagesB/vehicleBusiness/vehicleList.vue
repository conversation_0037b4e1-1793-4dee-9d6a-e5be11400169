<template>
	<view class="vehicle-list">
		<view class="no-vehicle" v-if="isNoVehicle">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/addcar_nocar.png"
				mode="aspectFilt" class="no-vehicle_icon">
			</image>
			<view class="no-vehicle_des">
				暂无车辆
			</view>
		</view>
		<!-- <view :class="[isRecharge ? 'fixed-margin-bottom':'']"> -->
		<view class="fixed-margin-bottom" :class="[businessType == 'containerRefund'?'fixed-padding-top':'']">
			<block v-if="businessType == 'containerRefund'">
				<view class="weui-media" v-for='(item,index) in vehicleAllDataList' :key='index'
					@click="vehicleInfo(item)" v-if="item.vehicleType =='1'">
					<view class="weui-media-hd">
						<!-- 					<image v-if="item.vehicleType =='2'" src="../../static/toc/passenger-car_icon.png" mode="aspectFilt"
						class="weui-media-hd_icon">
					</image> -->
						<image
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/trucks_icon.png"
							mode="aspectFilt" class="weui-media-hd_icon">
						</image>
					</view>
					<view class="weui-media-bd">
						<view class="title">{{item.vehicleCode}}【{{getVehicleColor(item.vehicleColor)}}】</view>
						<view class="value">{{getVehicleType(item.vehicleType)}}车</view>
					</view>
					<view class="weui-media-ft">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/right.png" mode="aspectFilt" class="weui-media-ft_icon"></image>
					</view>
				</view>
			</block>
			<block v-else>
				<view style="font-size: 36rpx;color: #666;margin: 20rpx 30rpx;" v-if="vehicleAllDataList.length>1">
					您名下存在多个辆车,请选择</view>
				<view class="weui-media" v-for='(item,index) in vehicleAllDataList' :key='index'
					@click="vehicleInfo(item)">
					<view class="weui-media-hd">
						<image v-if="item.vehicleType =='2'" src="../static/vehicleIcon/car_icon.png" mode="aspectFilt"
							class="weui-media-hd_icon">
						</image>
						<image v-if="item.vehicleType =='1'" src="../static/vehicleIcon/truck_icon.png"
							mode="aspectFilt" class="weui-media-hd_icon">
						</image>
						<image v-if="item.vehicleType =='3'" src="../static/vehicleIcon/priatecar_icon.png"
							mode="aspectFilt" class="weui-media-hd_icon">
						</image>
					</view>
					<view class="weui-media-bd">
						<view class="title">{{item.vehicleCode}}【{{getVehicleColor(item.vehicleColor)}}】</view>
						<view class="value">{{getVehicleType(item.vehicleType)}}车</view>
						<view class="value" v-if="businessType=='ccsSign'">
							签约状态：{{item.isSign?getCcsSignStatus(item.signRecord.signStatus):'未签约'}}
						</view>
					</view>
					<!-- 修补业务需求 增加充值按钮入口 -->
					<view class="weui-media-ft g-flex" v-if="businessType=='recharge'">
						<view class="weui-media-ft_btn"
							v-if="!(item.gxCardType != '5' && item.gxCardType != '0'&& item.gxCardType != '8')">
							去充值
						</view>
					</view>
					<view class="weui-media-ft" v-else>
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/right.png" mode="aspectFilt" class="weui-media-ft_icon"></image>
					</view>
				</view>
			</block>
		</view>

		<view class="weui-bottom-fixed" v-if="isRecharge">
			<view class="weui-bottom-fixed__box bottom-box"><button class="weui-btn weui-btn_primary"
					@click="goRechargeOtherHandle">充值其他车辆</button>
			</view>
		</view>
		<view class="weui-bottom-fixed" v-if="isAfterPay">
			<view class="weui-bottom-fixed__box bottom-box"><button class="weui-btn weui-btn_primary"
					@click="rechargeOther">{{btnTitle}}</button>
			</view>
		</view>

		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		getCcsSignStatus
	} from '@/common/method/filter.js'
	import {
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getCurrUserInfo,
		setCurrUserInfo,
		getLoginUserInfo,
		getAccountId,
		setBusinessTypes,
		setStore,
		getStore,
		setEtcVehicle
	} from '@/common/storageUtil.js'
	import TButton from '@/components/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue'
	export default {
		data() {
			return {
				isNoVehicle: false,
				isLoading: false,
				formData: {},
				bindTicket: '',
				vehicleAllDataList: [],
				businessType: '',
				btnTitle: '',
				pageConfig: [{
					type: 'blackList',
					url: '/pagesB/cardBusiness/blackList/blackInfo',
					title: '黑名单查询'
				}, {
					type: 'consumptionRecord',
					url: '/pagesB/cardBusiness/expenseRecord/record-list',
					title: '消费记录'
				}],
				currentVehicleInfo: null,
				// isOwner: '0' //判断获取个人承诺书
			}
		},
		components: {
			tLoading,
			TButton
		},
		computed: {
			isRecharge() {
				return this.businessType == 'recharge'
			},
			isAfterPay() {
				return this.businessType == 'afterPay';
			}

		},
		onLoad(obj) {
			this.businessType = obj.fontType
			console.log(obj, '传过来的obj')
			if (obj.fontType == 'recharge') {
				this.isShowBtn = true
			}
			if (obj.fontType === 'afterSale') {
				uni.setNavigationBarTitle({
					title: '车辆售后'
				})
				this.isAfter = true
			}
			let setTitle = {
				recharge: '充值',
				rechargeList: '充值记录',
				blackList: '黑名单查询',
				loadRecharge: '圈存充值',
				newApply: '新发申请单',
				afterPay: '欠费补缴',
				containerRefund: '集装箱退费',
				signature: '电子协议查询',
				updateDevice: '信息修正',
				consumptionRecord: '消费记录',
				ccsSign: '次结产品签约'
			}
			let setBtnTitle = {
				afterPay: '补缴其他车辆'
			}
			if (obj.fontType && setTitle[obj.fontType]) {
				uni.setNavigationBarTitle({
					title: setTitle[obj.fontType]
				})
			}
			if (obj.fontType && setBtnTitle[obj.fontType]) {
				this.btnTitle = setBtnTitle[obj.fontType]
			}
			if (this.businessType == 'ccsSign') return
			this.getVerhicleList()
		},
		onShow() {
			//获取次次顺签约车辆列表
			if (this.businessType == 'ccsSign') {
				this.getSignVehicleList()
				return
			}
		},
		methods: {
			getVehicleColor,
			getVehicleClassType,
			getVehicleType,
			getCcsSignStatus,
			getVerhicleList() {
				let params = {
					customerId: getAccountId()
				}
				this.$request
					.post(this.$interfaces.vehicleList, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.vehicleAllDataList = res.data || []
							this.vehicleAllDataList = this.vehicleAllDataList.filter((item) => {
								if (item.cardNo) {
									return item
								}
							})
							this.isNoVehicle = this.vehicleAllDataList.length == 0
							if (this.businessType == 'containerRefund') {
								//集装箱没有货车时车辆列表的判断
								let truckArr = this.vehicleAllDataList.filter(item => {
									return item.vehicleType == '1'
								})
								this.isNoVehicle = truckArr.length == 0
							}

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
			},
			// 查询车辆详情接口
			vehicleInfo(item) {
				if (this.businessType == 'ccsSign') {
					//代扣协议不需要补签
					this.currentVehicleInfo = item;
					if (this.isLoading) return
					this.isLoading = true
					setCurrentCar({})
					let data = {
						routePath: this.$interfaces.vehicleBizSearch.method,
						bizContent: {
							vehicle_code: item.vehicleCode,
							vehicle_color: item.vehicleColor
						}
					}
					this.$request
						.post(this.$interfaces.issueRoute, {
							data: data
						})
						.then((res) => {
							this.isLoading = false
							if (res.code == 200 && res.data && res.data.length) {
								setCurrentCar(res.data[0])
								this.goCarBusinessPage(res.data[0])
							} else {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
						})
						.catch((error) => {
							this.isLoading = false
						})
				} else {
					this.getNeedResign(item, resp => {
						if (resp.data.isNecessary && !resp.data.conStatus) {
							//需要签署协议。
							let etcUser = getCurrUserInfo()
							//判断ETC用户是否一致
							if (resp.data.isOwner == '0') {
								uni.showModal({
									title: '提示',
									content: '为给您提供更好的服务，请通知' + item.vehicleCode +
										'客户' + etcUser.customer_name + '登录‘桂小通’微信小程序签署电子协议',
									showCancel: false
								})
								return
							} else {
								uni.showModal({
									title: '提示',
									content: '为给您提供更好的服务，明确双方权利和义务关系，请您签署用户电子协议',
									showCancel: false,
									confirmText: '前往签署',
									success: (res) => {
										if (res.confirm) {
											//判断获取个人承诺书
											// this.getContractOwner(item)
											this.getMarketId(item)
										}
									}
								})
							}
						} else {
							this.currentVehicleInfo = item;
							if (this.isLoading) return
							this.isLoading = true
							setCurrentCar({})
							let data = {
								routePath: this.$interfaces.vehicleBizSearch.method,
								bizContent: {
									vehicle_code: item.vehicleCode,
									vehicle_color: item.vehicleColor
								}
							}
							this.$request
								.post(this.$interfaces.issueRoute, {
									data: data
								})
								.then((res) => {
									this.isLoading = false
									if (res.code == 200 && res.data && res.data.length) {
										setCurrentCar(res.data[0])
										this.goCarBusinessPage(res.data[0])
									} else {
										uni.showModal({
											title: '提示',
											content: res.msg,
											showCancel: false
										})
									}
								})
								.catch((error) => {
									this.isLoading = false
								})
						}
					})
				}


			},
			//废弃，isOwner还是得默认写死
			// getContractOwner(item) {
			// 	this.isLoading = true
			// 	//单位用户不需要个人承诺书,直接下一步
			// 	if (getCurrUserInfo().customer_type == '1') {
			// 		//一致就获取营销活动id
			// 		this.getMarketId(item)
			// 		return
			// 	}
			// 	let data = {
			// 		routePath: this.$interfaces.contractOwner.method,
			// 		bizContent: {
			// 			customerId: getCurrUserInfo().customer_id,
			// 			carNo: item.vehicleCode,
			// 			carNoColor: item.vehicleColor
			// 		}
			// 	}
			// 	this.$request
			// 		.post(this.$interfaces.issueRoute, {
			// 			data: data
			// 		})
			// 		.then((res) => {
			// 			this.isLoading = false
			// 			if (res.code == 200) {
			// 				this.isOwner = res.data.isOwner
			// 				this.getMarketId(item)
			// 			} else {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				})
			// 			}
			// 		})
			// 		.catch((error) => {
			// 			this.isLoading = false
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: res.msg,
			// 				showCancel: false
			// 			})
			// 		});
			// },
			createUserSign(item, marketList) {
				console.log('签约==========>>>>')
				let params = {
					source: '1', //存在etc用户
					customerId: getCurrUserInfo().customer_id,
					// custType: '1', // customerId必填
					vehicles: {
						vehicleCode: item.vehicleCode,
						vehicleColor: item.vehicleColor,
					},
					signName: getCurrUserInfo().customer_name,
					signPhone: getCurrUserInfo().link_mobile,
					signIdNo: getCurrUserInfo().certificates_code,
					marketId: marketList.marketActiveMastId,
					businessType: '7', //7补签
					productType: item.cardProduct,
					isOwner: '1' //默认本人
				}

				// console.log('prams===========>', params)

				// let data = {
				// 	data: params,
				// };
				let data = {
					routePath: this.$interfaces.newSignPreview.method,
					bizContent: params
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('签章返回===>>>', res)
							let signKey = res.data.signKey
							uni.setStorageSync('signKey', signKey)
							// uni.setStorageSync('applyType', '3') //通用签约类型3
							let pdfInfo = res.data.data
							//btnType=B B是使用袁翔的B端接口签约回调
							let signUrl = 'https://portal.gxetc.com.cn/new-agreement?btnType=B&type=sign&signInfo=' +
								encodeURIComponent(JSON.stringify(
									pdfInfo))

							uni.reLaunch({
								url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
									.stringify(
										signUrl))
							})

						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			getMarketId(item) {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.resignMarketListV2.method,
					bizContent: {
						customerId: getAccountId(),
						vehicleCode: item.vehicleCode,
						vehicleColor: item.vehicleColor,
						productType: item.cardProduct
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('查询营销活动', res)
							if (res.data.length > 0) {
								let marketList = res.data[0]
								this.createUserSign(item, marketList)
							} else {
								uni.showModal({
									title: "提示",
									content: '获取不到营销方案！',
									showCancel: false,
								});
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						})
					})
			},
			getNeedResign(item, callback) {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.needResign.method,
					bizContent: {
						netUserNo: getLoginUserInfo().userNo,
						customerId: getAccountId(),
						vehicleCode: item.vehicleCode,
						vehicleColor: item.vehicleColor,
						productType: item.cardProduct
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('查询补签', res)
							callback(res)
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						})
					})
			},
			goRechargeOtherHandle() {
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/rechargeOther/rechargeOther'
				})
			},
			goCarBusinessPage(item) {
				if (this.businessType == 'signature') {
					uni.navigateTo({
						url: '/pagesC/userAgreement/agreementList'
					})
					return
				}
				if (this.businessType == 'ccsSign') {
					if (!this.currentVehicleInfo.isSign || this.currentVehicleInfo.signRecord.signStatus == 4) {
						if (!(this.currentVehicleInfo.vehicleType == 2 && [0, 4].toString().includes(this
								.currentVehicleInfo.vehicleColor))) {
							// uni.showModal({
							// 	title: '提示',
							// 	content: '该车型暂不支持办理次次顺产品',
							// 	showCancel: false
							// })
							// return;
						}
					}
					uni.navigateTo({
						url: '/pagesC/ccsSign/index'
					})
					return
				}
				if (!item.cpu_card_id) {
					uni.showModal({
						title: '提示',
						content: '您当前车辆' + item.vehicle_code + '未绑定ETC卡'
					})
					return
				}
				if (this.businessType == 'afterPay') {
					let nextData = {
						vehicle_code: item.vehicle_code,
						vehicle_color: item.vehicle_color,
						// cardNo: item.cpu_card_id
					}
					uni.navigateTo({
						url: '/pagesC/afterPay/recordDetail/recordDetail?nextData=' + encodeURIComponent(JSON
							.stringify(nextData))
					})
					return;
				}


				console.log(this.businessType, '-------------------------', item)
				if (this.businessType == 'couponsRecharge') {
					if (item.gx_card_type != '5' && item.gx_card_type != '8') {
						uni.showModal({
							title: '提示',
							content: '您当前车辆绑定的卡不是捷通日日通记账卡，无法充值'
						})
						return
					}
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/recharge/p-recharge'
					})
					return
				}
				if (this.businessType == 'recharge') {
					if (item.gx_card_type != '5' && item.gx_card_type != '0' && item.gx_card_type != '8') {
						uni.showModal({
							title: '提示',
							content: '您当前车辆绑定的卡不是捷通日日通记账卡或储值卡，无法充值'
						})
						return
					}
					if (item.gx_card_type == '5' || item.gx_card_type == '8') {
						uni.navigateTo({
							url: '/pagesB/rechargeBusiness/recharge/p-recharge'
						})
						return
					}

					if (!item.obu_id) {
						uni.showModal({
							title: '提示',
							content: '您当前车辆' + item.vehicle_code + '未绑定OBU标签'
						})
						return
					}
					if (item.gx_card_type == '0') {
						uni.navigateTo({
							url: '/pagesB/loadBusiness/recharge/recharge'
						})
						return
					}
					return
				}
				if (this.businessType == 'rechargeList') {
					if (item.gx_card_type != '5' && item.gx_card_type != '0' && item.gx_card_type != '8') {
						uni.showModal({
							title: '提示',
							content: '您当前车辆绑定的卡不是捷通日日通记账卡或储值卡，无法查看充值记录'
						})
						return
					}
					if (item.gx_card_type == '5' || item.gx_card_type == '8') {
						uni.navigateTo({
							url: '/pagesB/rechargeBusiness/rechargeList/p-rechargeList'
						})
						return
					}
					uni.navigateTo({
						url: '/pagesB/loadBusiness/recharge/rechargeRecord'
					})
					return
				}
				if (!item.obu_id) {
					uni.showModal({
						title: '提示',
						content: '您当前车辆' + item.vehicle_code + '未绑定OBU标签'
					})
					return
				}
				// 激活功能目前不需要上线
				// // obu 未激活
				// if(item.obu_status == 2){
				// 	uni.navigateTo({
				// 		url: "/pagesA/newBusiness/p-activation/activation"
				// 	});
				// 	return;
				// }
				if (this.businessType == 'afterSale') {
					uni.navigateTo({
						url: '/pagesD/afterSale/home/<USER>' + item.vehicle_code
					})
					return
				}

				if (this.businessType == 'newApply') {
					uni.navigateTo({
						url: '/pagesA/orderBusiness/orderBusiness'
					})
					return
				}
				if (this.businessType == 'carPayList') {
					uni.navigateTo({
						url: '/pagesB/cardBusiness/payInfo/p-payInfo'
					})
					return
				}

				let current = null
				for (let i = 0; i < this.pageConfig.length; i++) {
					if (this.pageConfig[i].type == this.businessType) {
						current = this.pageConfig[i]
						break;
					}
				}
				console.log(current, this.businessType, 'current')
				if (current) {
					uni.navigateTo({
						url: current.url
					})
					return
				}
				if (this.businessType == 'loadRecharge') {
					setEtcVehicle(item)
					uni.navigateTo({
						url: '/pagesB/loadBusiness/loadBusiness'
					})
					return
				}

				if (this.businessType == 'containerRefund') {
					uni.navigateTo({
						url: '/pagesB/containerRefund/car-record/record-list'
					})
					return
				}

				if (this.businessType == 'updateDevice') {
					let nextData = {
						// vehicle_code: item.vehicle_code,
						// vehicle_color: item.vehicle_color,
						cardNo: item.cpu_card_id
					}
					uni.redirectTo({
						url: '/pagesB/deviceBusiness/entry/entry?nextData=' + encodeURIComponent(JSON
							.stringify(nextData))
					})
					return
				}
			},
			rechargeOther() {
				if (this.businessType == 'afterPay') {
					uni.navigateTo({
						url: '/pagesC/afterPay/searchInfo/searchInfo'
					})
					return
				}
			},

			//次次顺产品签约需要显示未发行的车辆
			getSignVehicleList() {
				let params = {
					customerId: getAccountId()
				}
				this.$request
					.post(this.$interfaces.getSikyVehicleList, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.vehicleAllDataList = res.data || []
							this.isNoVehicle = this.vehicleAllDataList.length == 0
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
			},
		}
	}
</script>

<style scoped lang="scss">
	.fixed-margin-bottom {
		padding-bottom: 160rpx;
	}

	.fixed-padding-top {
		padding-top: 20rpx;
	}

	.vehicle-list {
		width: 100%;
		height: 100%;
		background-color: #f9f9f9;
	}

	.vehicle-list .vehicle-list_wrapper {
		padding: 20rpx 0 120rpx 0;
	}

	.vehicle-list .weui-btn_wrapper {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
	}

	.no-vehicle {
		padding-top: 320rpx;
		width: 100%;
		background-color: #f9f9f9;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 29rpx;
		color: #333333;
		font-weight: 400;
		text-align: center;
		margin-top: 60rpx;
	}

	.weui-media {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		margin: 20rpx 30rpx;
		border-radius: 16rpx;
		padding: 32rpx 30rpx;
		box-shadow: 0px 0px 20rpx 0px rgba(71, 123, 217, 0.12);

		&:first-child {
			margin-top: 0;
		}
	}

	.weui-media .weui-media-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;
	}

	.weui-media .weui-media-hd .weui-media-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.weui-media .weui-media-bd {
		flex: 1;
		margin-left: 30rpx;
	}

	.weui-media .weui-media-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.weui-media .weui-media-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
	}

	.weui-media .weui-media-ft {
		min-width: 28rpx;
	}

	.weui-media .weui-media-ft .weui-media-ft_btn {
		width: 158rpx;
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;
		background: #0066E9;
		border-radius: 36rpx;
		font-size: 26rpx;
		color: #fff;
		font-weight: 400;
	}

	.weui-media .weui-media-ft .weui-media-ft_icon {
		width: 28rpx;
		height: 28rpx;
		display: block;
	}
</style>