<template>
	<view class="entry">
		<tHandleStep :current="0"></tHandleStep>
		<view class="tips">
			<text style="color: #0066E9;">操作提示：</text>您可以连接设备蓝牙、或选择已绑定车辆，快速查看当前设备是否需要更新。
		</view>
		<view class="section-wrapper">
			<view class="item">
				<view class="item-desc">
					卡号
				</view>
				<view class="item-value">
					{{cpuId}}
				</view>
				<button class="item-button" @click="connect" :disabled="isBtnLoader">读卡</button>
			</view>
			<view class="item">
				<view class="item-desc">
					OBU号
				</view>
				<view class="item-value">
					{{formData.obuNo}}
				</view>
				<view class="item-stand"></view>
			</view>
			<view class="item">
				<view class="item-desc">
					车牌号
				</view>
				<view class="item-value" v-if="formData.carNo">
					{{formData.carNo + '[' + vehicleColors[formData.carColor] +']'}}
				</view>
				<view class="item-value" v-else>

				</view>
				<view class="item-stand"></view>
			</view>
			<view class="select-wrapper">
				<button class="weui-btn weui-btn_primary" @click="selectCar()">
					选择已绑定车辆
				</button>
			</view>
		</view>
		<view class="btn-wrapper" v-if="nextFlag">
			<TButton title="下一步" @clickButton="nextStep" />
		</view>
		<view class="cu-load load-modal-car-activation" v-if="connectLoaing">
			<image src="/static/etc/loading.gif" class="link-image-gif" v-if="!isLoading"></image>
			<image src="/static/components/delete.png" class="delete-image" v-if="isLoading" @click="closeLoading">
			</image>
			<view class="gray-text" v-if="!isLoading && isActive">获取设备信息中...</view>
			<view v-if="!isActive">
				<view class="gray-text" v-if="!isConnect">设备连接中，请耐心等待</view>
				<view class="gray-text" v-else>连接设备成功</view>
			</view>
		</view>
		<tLoading :isShow="isShowLoding" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from "@/components/common/t-loading.vue";
	import tHandleStep from '@/pagesB/components/t-handleStep/handleStep.vue'
	import bleapi from "@/common/bluetooth/bleUtil.js";
	import {
		facapi
	} from "@/common/bluetooth/facUtil.js";

	import {
		vehicleColors,
	} from "@/common/const/optionData.js";

	import float from '@/common/method/float.js';

	// 正式，顺序优先
	const newPinCode = "123456";
	// 正式，顺序优先
	const pinCode = "************";
	const terminalNo = "123456789012";
	const money = 2100000000;
	const defaultRand = "00000000";
	const defaultObuId = "0000000000000000";
	var _that;
	//蓝牙服务名前缀
	import Vue from 'vue';
	const SERVICENAME_PREFIX = Vue.prototype.$serviceName;

	export default {
		components: {
			tHandleStep,
			TButton,
			tLoading
		},
		data() {
			return {
				vehicleColors,
				isShowLoding: false,
				isSuccess: false,
				isBtnLoader: false,
				connectLoaing: false,
				isLoading: false,
				isActive: false,
				isConnect: false,
				nextFlag: false,
				cpuId: '',
				formData: {
					id: '',
					bizType: '',
					carColor: '',
					carNo: '',
					cardNo: '',
					imgStatus: '',
					infoStatus: '',
					obuNo: '',
					remark: [],
				}
			}
		},
		onLoad(options) {
			console.log(options)
			_that = this
			this.closeBle();
			if (options && Object.keys(options).length > 0) {
				let carInfo = JSON.parse(decodeURIComponent(options.nextData));
				this.cpuId = carInfo.cardNo
				this.getCardInfo(this.cpuId)
			}
		},
		methods: {
			selectCar() {
				uni.redirectTo({
					url: '/pagesB/vehicleBusiness/vehicleList?fontType=updateDevice'
				})
			},
			nextStep() {
				if (this.formData.bizType == '1') {
					uni.navigateTo({
						url: '/pagesB/deviceBusiness/updateImg/updateImg?deviceData=' + encodeURIComponent(JSON
							.stringify(this.formData))
					})
				} else if (this.formData.bizType == '2') {
					uni.navigateTo({
						url: '/pagesB/deviceBusiness/updateDevice/updateDevice?deviceData=' + encodeURIComponent(
							JSON
							.stringify(this.formData))
					})
				} else if (this.formData.bizType == '3') {
					if (this.formData.imgStatus == '2') {
						uni.navigateTo({
							url: '/pagesB/deviceBusiness/updateDevice/updateDevice?deviceData=' +
								encodeURIComponent(
									JSON
									.stringify(this.formData))
						})
					} else {
						uni.navigateTo({
							url: '/pagesB/deviceBusiness/updateImg/updateImg?deviceData=' + encodeURIComponent(JSON
								.stringify(this.formData))
						})
					}
				}
			},
			getCardInfo(cpuId) {
				// this.isShowLoding = true
				let params = {
					cardNo: cpuId
				}

				this.$request.post(this.$interfaces.getDeviceByCard, {
					data: params
				}).then(res => {
					// this.isShowLoding = false;
					if (this.isConnect) {
						_that.disConnect();
					}

					if (res.code == 200) {
						console.log('返回卡片信息', res)
						this.nextFlag = true

						this.formData.custMastId = res.data.custMastId
						this.formData.id = res.data.id
						this.formData.bizType = res.data.bizType
						this.formData.carNo = res.data.carNo
						this.formData.carColor = res.data.carColor
						this.formData.obuNo = res.data.obuNo
						this.formData.cardNo = res.data.cardNo
						this.formData.imgStatus = res.data.imgStatus
						this.formData.infoStatus = res.data.infoStatus
						this.formData.remark = res.data.remark


					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					// this.isShowLoding = false;
					if (this.isConnect) {
						_that.disConnect();
					}
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})

			},
			connect() {
				if (this.isBtnLoader) return
				this.isBtnLoader = true;
				this.connectLoaing = true;
				this.isActive = false;

				bleapi.CloseBle((obj) => {
					_that.scanDevice((result) => {
						_that.isConnect = result;
						if (result) {
							_that.activeFlag()
						}
					});
				})

			},
			connectedCallback(isConnected) {
				console.log("连接成功回调");
				if (isConnected) {
					facapi.OpenCard((devResult) => {
						if (devResult.code == 0) {
							facapi.GetCardNo((devResult) => {
								if (devResult.code == 0) {
									console.log("GetCardNo==>>", JSON.stringify(devResult));
									//先获取卡号
									_that.cpuId = devResult.data;
									//再获取Obu
									_that.getCardInfo(devResult.data)
								} else {
									_that.showModal({
										title: "错误",
										devResult: devResult,
										content: "获取卡号失败：" +
											devResult.code +
											":" +
											devResult.err_msg +
											(devResult.msg ? ":" + devResult.msg : ""),
									});
									_that.disConnect();
								}
							});
						} else {
							_that.showModal({
								title: "错误",
								devResult: devResult,
								content: "打开卡失败：" +
									devResult.code +
									":" +
									devResult.err_msg +
									(devResult.msg ? ":" + devResult.msg : ""),
							});
							_that.disConnect();
						}
					});
				}
			},

			activeFlag() {
				this.isActive = true;
				this.connectedCallback(this.isConnect);
				// 60秒连接超时
				setTimeout(() => {
					if (this.isActive) {
						this.disConnect();
						uni.showModal({
							title: "提示",
							content: "连接超时,请重试",
							showCancel: false,
						});
					}
				}, 60000);
			},

			scanDevice(callback) {
				bleapi.ScanDevice(SERVICENAME_PREFIX, (devResult) => {
					console.log("ScanDevice", devResult);
					if (devResult.code != 0) {
						console.log("搜索失败", devResult);
						_that.closeLoading();
						_that.closeBle()
						//搜索设备失败
						_that.showModal({
							title: "错误",
							devResult: devResult,
							content: devResult.err_msg,
							showMyContent: true,
						});
					} else {
						console.log(
							"搜索到设备:" + devResult + " " + devResult.data.device_name
						);
						facapi.ConnectDevice(
							devResult.data,
							function(onDisconnect) {
								console.log("连接回调：", onDisconnect);
							},
							function(result) {
								console.log(result, "result");
								bleapi.StopScanDevice(function(code) {
									console.log("返回数据", code);
								});
								if (result.code == 0) {
									console.log("连接标签设备成功");
									callback(true);
								} else {
									_that.closeLoading();
									_that.closeBle()
									_that.showModal({
										title: "错误",
										devResult: result,
										content: "设备连接失败，请将手机靠近设备后重试。",
										showMyContent: true,
									});
									callback(false);
								}
							}
						);
					}
				});
			},
			// 显示弹框
			showModal(data) {
				//隐藏loading
				_that.isBtnLoader = false;
				console.log(data.content, 'sdk报错');
				_that.closeLoading();
				//显示弹框
				let obj = {
					...data
				};
				obj = data.showMyContent ?
					obj : {
						...data,
						content: '操作失败，请打开手机蓝牙，并确认已按步骤完成激活前准备。请将手机靠近设备后重试。(' + data.devResult.code + ':' + data
							.devResult
							.err_msg + ')'
					};
				uni.showModal({
					...obj,
					showCancel: false,
					success: () => {}
				});
			},
			// 中断或结束断开连接关闭蓝牙
			disConnect() {
				this.closeLoading();
				this.isActive = false;
				this.isConnect = false;
				this.isBtnLoader = false;
				bleapi.closeBLEConnection();
				facapi.facSdk.DisconnectDevice(function(code) {
					console.log("关闭连接结果", code);
				});
				// 完成后关闭蓝牙模块
				// bleapi.CloseBle((obj) => {
				// 	console.log(obj);
				// });
			},
			closeLoading() {
				this.connectLoaing = false;
				this.isLoading = false;
			},
			closeBle() {
				// 关闭蓝牙模块，防止中途断开，连接不上
				// bleapi.CloseBle((obj) => {
				// 	console.log(obj);
				// });
				bleapi.closeBLEConnection();
			},
			// 圈存存入当前时间
			formatDate(now) {
				var year = now.getFullYear();
				var month =
					now.getMonth() + 1 < 10 ?
					"0" + (now.getMonth() + 1) :
					now.getMonth() + 1;
				var date = now.getDate() < 10 ? "0" + now.getDate() : now.getDate();
				var hour = now.getHours() < 10 ? "0" + now.getHours() : now.getHours();
				var minute =
					now.getMinutes() < 10 ? "0" + now.getMinutes() : now.getMinutes();
				var second =
					now.getSeconds() < 10 ? "0" + now.getSeconds() : now.getSeconds();
				return (
					year + "" + month + "" + date + "" + hour + "" + minute + "" + second
				);
			},
		},
		onUnload() {
			bleapi.closeBLEConnection();
		},

	}
</script>

<style lang="scss" scoped>
	.entry {
		.tips {
			margin-top: 50rpx;
			padding: 0 30rpx;
			font-size: 30rpx;
			line-height: 50rpx;
			color: #333333;
		}

		.section-wrapper {
			margin-top: 50rpx;

			.item {
				display: flex;
				align-items: center;
				font-size: 30rpx;
				padding: 0 30rpx;
				margin-top: 30rpx;

				&-desc {
					flex: 0 0 120rpx;
					width: 120rpx;
				}

				&-value {
					flex: 1;
					height: 80rpx;
					line-height: 80rpx;
					border: 2rpx solid #cccccc;
					margin-right: 20rpx;
					border-radius: 10rpx;
					padding: 0 20rpx;
				}

				&-button {
					flex: 0 0 160rpx;
					width: 160rpx;
					// margin-left: 30rpx;
					height: 80rpx;
					line-height: 80rpx;
					font-size: 28rpx;
					background-color: #0066E9;
					color: #ffffff;
				}

				&-stand {
					flex: 0 0 160rpx;
					width: 160rpx;
				}
			}
		}
	}

	.btn-wrapper {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 30rpx;
		width: 100%;
	}

	.select-wrapper {
		margin-top: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.weui-btn {
			width: 300rpx;
		}
	}

	.load-modal-car-activation {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 140rpx;
		left: 0;
		margin: auto;
		width: 350rpx;
		height: 350rpx;
		background-color: var(--white);
		border-radius: 10rpx;
		box-shadow: 0 0 0rpx 2000rpx rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		font-size: 28rpx;
		z-index: 9999;
		align-items: center;
		flex-direction: column;

		.link-image {
			width: 200rpx;
			height: 200rpx;
		}

		.link-image-gif {
			width: 120rpx;
			height: 120rpx;
			margin-bottom: 20rpx;
		}

		.delete-image {
			width: 20rpx;
			height: 20rpx;
			position: absolute;
			right: 20rpx;
			top: 20rpx;
		}
	}
</style>
