<template>
	<view class="udpate-device">
		<tHandleStep :current="2"></tHandleStep>
		<view class="card">
			<view class="card-box">
				<view class="bd">
					<view class="title" v-if="Object.keys(formData).length > 0 && formData.carNo">
						{{formData.carNo}}[{{getVehicleColor(formData.carColor+'')}}]
					</view>
					<view class="des" v-if="Object.keys(formData).length > 0 && formData.custName">
						{{formData.custName}}
					</view>
					<view class="des" v-if="Object.keys(formData).length > 0 && formData.custMastId">
						{{formData.custMastId}}
					</view>
				</view>
			</view>
		</view>
		<view class="weui-form">
			<view class="weui-cells__title">
				当前车辆信息
			</view>
			<view class="weui-cells" v-if="Object.keys(formData).length > 0">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{formData.cardNo}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡片状态</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{getCpuStatus(formData.cardStatus)}}
						</view>
					</view>

				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">卡片类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{gxCardTypeFilter(formData.gxCarType)}}
						</view>
					</view>

				</view>
			</view>
		</view>
		<view class="progress">
			<view class="desc">
				{{updateMsg}}
			</view>
			<progress class="progress-box" :percent="progress.percent" :activeColor="progress.activeColor"
				:stroke-width="progress.strokeWidth" :show-info="progress.showInfo"
				:border-radius="progress.borderRadius" />
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" :disabled="isBtnLoader" @click="connect()">
						信息修正
					</button>
				</view>
			</view>
		</view>
		<!-- <view class="cu-load load-modal-car-activation" v-if="connectLoaing">
			<image src="/static/etc/loading.gif" class="link-image-gif" v-if="!isLoading"></image>
			<image src="/static/components/delete.png" class="delete-image" v-if="isLoading" @click="closeLoading">
			</image>
			<view class="gray-text" v-if="!isLoading && isActive">获取设备信息中...</view>
			<view v-if="!isActive">
				<view class="gray-text" v-if="!isConnect">设备连接中，请耐心等待</view>
				<view class="gray-text" v-else>连接设备成功</view>
			</view>
		</view> -->
		<!-- <tLoading :isShow="isShowLoding" /> -->
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from "@/components/common/t-loading.vue";
	import tHandleStep from '@/pagesB/components/t-handleStep/handleStep.vue'
	import bleapi from "@/common/bluetooth/bleUtil.js";
	import {
		facapi
	} from "@/common/bluetooth/facUtil.js";

	import {
		getVehicleColor,
		gxCardTypeFilter,
		getCpuStatus
	} from '@/common/method/filter.js';
	import {
		vehicleColors,
	} from "@/common/const/optionData.js";

	import float from '@/common/method/float.js';
	const defaultRand = "00000000";
	const defaultObuId = "0000000000000000";
	var _that;
	//蓝牙服务名前缀
	import Vue from 'vue';
	const SERVICENAME_PREFIX = Vue.prototype.$serviceName;

	import { initLoadMixin } from '@/common/mixins/initLoadMixin.js';
	export default {
		mixins: [initLoadMixin], 
		components: {
			tHandleStep,
			TButton,
			tLoading
		},
		data() {
			return {
				vehicleColors,
				isShowLoding: false,
				isSuccess: false,
				isBtnLoader: false,
				connectLoaing: false,
				isLoading: false,
				isActive: false,
				isConnect: false,
				nextFlag: false,
				cpu_id: '',
				obu_id: '',
				issue_code: '',
				updateMsg: '',
				faildMsg: '更新失败，请重试，或联系客服',
				formData: {
					// 	// carColor: '',
					// 	// carNo: '',
					// 	// cardNo: '',
					// 	// cardType: '', //卡类型
					// 	// cardStatus: '', //卡状态
					// 	// custMastId: '', //客户ID
					// 	// custName: '' //客户名称,
				},
				comParams: {
					//用户编号
					customer_id: '',
					//车牌颜色
					vehicle_color: '',
					//车牌号码
					vehicle_code: '',
					gx_card_type: ''
				},
				deviceData: {},
				progress: {
					percent: 0,
					activeColor: '#0066E9',
					strokeWidth: '30',
					showInfo: true,
					borderRadius: '20',
				},
				cardType: '',
				cpuId: '', // 兼容字段
				issueCode: '', // 兼容字段
				issueTimes: '', // 兼容字段
			}
		},
		onLoad(options) {
			_that = this
			this.deviceData = JSON.parse(decodeURIComponent(options.deviceData));
			this.obu_id = this.deviceData.obuNo
			this.getCardInfo(this.deviceData.cardNo)
			this.closeBle();
			console.log('deviceData', this.deviceData)
		},
		methods: {
			getVehicleColor,
			gxCardTypeFilter,
			getCpuStatus,
			nextStep() {},
			getCardInfo(cpuId) {
				// this.isShowLoding = true
				let params = {
					cardNo: cpuId
				}

				this.$request.post(this.$interfaces.getDeviceDetailByCard, {
					data: params
				}).then(res => {
					// this.isShowLoding = false;
					if (res.code == 200) {
						console.log('返回卡片信息', res)

						this.formData = res.data

						//要合并的请求数据
						this.comParams['customer_id'] = res.data.custMastId
						this.comParams['vehicle_code'] = res.data.carNo
						this.comParams['vehicle_color'] = res.data.carColor
						this.comParams['gx_card_type'] = res.data.gxCarType

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					// this.isShowLoding = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})

			},
			updateCarStatus(callback) {
				let params = {
					id: this.deviceData.id
				}
				this.$request.post(this.$interfaces.updateCarStatus, {
						data: params
					})
					.then(res => {
						_that.disConnect()
						if (res.code == 200) {
							callback(true)
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						_that.disConnect()
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			updateCarInfo(callback) {
				// this.isShowLoding = true
				let params = {
					carColor: this.deviceData.carColor,
					carNo: this.deviceData.carNo
				}

				this.$request.post(this.$interfaces.updateCar, {
					data: params
				}).then(res => {
					// this.isShowLoding = false;
					if (res.code == 200) {
						callback(true)
					} else {
						_that.disConnect();
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					// this.isShowLoding = false;
					_that.disConnect();
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})

			},
			connect() {
				if (this.isBtnLoader) return
				this.isBtnLoader = true;
				this.connectLoaing = true;
				this.isActive = false;

				this.progress.percent = 0

				bleapi.CloseBle((obj) => {
					_that.scanDevice((result) => {
						_that.isConnect = result;
						if (result) {
							_that.activeFlag()
						}
					});
				})

			},
			connectedCallback(isConnected) {
				this.updateCarInfo(updateFlag => {
					if (updateFlag) {
						console.log("连接成功回调");
						this.updateMsg = '信息修正中'
						this.progress.percent = 10
						if (isConnected) {
							facapi.OpenCard((devResult) => {
								if (devResult.code == 0) {
									facapi.GetCardFile15((devResult) => {
										if (devResult.code == 0) {
											this.cpu0015Info = devResult.data;
											console.log("GetCardNo==>>", JSON.stringify(
												devResult));
											//先获取卡号
											_that.cpu_id = devResult.data.cardNo
											_that.cpuId = _that.cpu_id;
											//获取卡类型22储值卡23记账卡
											_that.cardType = _that.cpu_id.substring(8, 10)
											if (_that.deviceData.cardNo != _that.cpu_id) {
												// _that.isActive = false;
												//新卡再校验
												uni.showModal({
													title: "错误",
													content: '要更新的卡片不是该车辆绑定的卡片',
													showCancel: false,
												});
												_that.disConnect();
											} else {
												_that.checkCardHandle()
											}
										} else {
											_that.showModal({
												title: "错误",
												devResult: devResult,
												content: "获取卡号失败：" +
													devResult.code +
													":" +
													devResult.err_msg +
													(devResult.msg ? ":" + devResult.msg :
														""),
											});
											_that.disConnect();
										}
									});
								} else {
									_that.showModal({
										title: "错误",
										devResult: devResult,
										content: "打开卡失败：" +
											devResult.code +
											":" +
											devResult.err_msg +
											(devResult.msg ? ":" + devResult.msg : ""),
									});
									_that.disConnect();
								}
							});
						}
					}
				})

			},

			activeFlag() {
				this.isActive = true;
				this.connectedCallback(this.isConnect);
				// 60秒连接超时
				setTimeout(() => {
					if (this.isActive) {
						this.disConnect();
						uni.showModal({
							title: "提示",
							content: "连接超时,请重试",
							showCancel: false,
						});
					}
				}, 60000);
			},

			scanDevice(callback) {
				this.updateMsg = '设备连接中'
				bleapi.ScanDevice(SERVICENAME_PREFIX, (devResult) => {
					console.log("ScanDevice", devResult);
					if (devResult.code != 0) {
						console.log("搜索失败", devResult);
						_that.closeLoading();
						_that.closeBle()
						//搜索设备失败
						_that.showModal({
							title: "错误",
							devResult: devResult,
							content: devResult.err_msg,
							showMyContent: true,
						});
					} else {
						console.log(
							"搜索到设备:" + devResult + " " + devResult.data.device_name
						);
						facapi.ConnectDevice(
							devResult.data,
							function(onDisconnect) {
								console.log("连接回调：", onDisconnect);
							},
							function(result) {
								console.log(result, "result");
								bleapi.StopScanDevice(function(code) {
									console.log("返回数据", code);
								});
								if (result.code == 0) {
									console.log("连接标签设备成功");
									_that.updateMsg = '设备连接成功'
									callback(true);
								} else {
									_that.closeLoading();
									_that.closeBle()
									_that.showModal({
										title: "错误",
										devResult: result,
										content: "设备连接失败，请将手机靠近设备后重试。",
										showMyContent: true,
									});
									callback(false);
								}
							}
						);
					}
				});
			},
			checkCardHandle() {
				this.cardCheck((res) => {
					console.log('校验卡片信息', res);
					if (res.code == 200) {
						// if (res.data.card_type == '22') {
						// 	uni.showModal({
						// 		title: "错误",
						// 		content: '储值卡不允许发行',
						// 		showCancel: false,
						// 	});
						// 	_that.disConnect();
						// 	return;
						// }
						this.progress.percent = 30
						_that.set0016();
					} else {
						uni.showModal({
							title: "错误",
							content: res.msg,
							showCancel: false,
						});
						_that.disConnect();
					}
				});
			},
			// 卡发行前校验
			async cardCheck(callback) {
				let data = {
					cpu_card_id: this.cpu_id,
					issue_type: "45",
				};
				Object.assign(data, this.comParams);
				let params = {
					data: data,
				};

				await this.$request
					.post(this.$interfaces.issueCPUISSUED, params)
					.then((res) => {
						this.issue_code = res.data.issue_code;
						this.issueCode = this.issue_code;
						callback(res);
					})
					.catch((e) => {
						callback(false);
					});
			},
			// 写0016
			set0016() {
				facapi.SetCardFile0016(
					(rand, callback) => {
						_that.getCpu0016WriteFile(rand, (result) => {
							console.log("|||=0016writeData=>", result);
							callback(result);
						});
					},
					(devResult) => {
						//写0016文件
						console.log("写0016文件结果", devResult);
						_that.setOO15(devResult);
					}
				);
			},
			// 写0015
			setOO15(devResult) {
				console.log("setOO15-devResult=>", devResult);
				if (devResult.code == 0) {
					facapi.SetCardFile0015(
						(rand, callback) => {
							_that.getCpu0015WriteFile(rand, (devResult) => {
								console.log("0015writeData", devResult);
								callback(devResult);
							});
						},
						(devResult) => {
							//写0016文件
							console.log("写0015文件", devResult);

							if (devResult.code == 0) {
								_that.progress.percent = 40
								//如果卡为记账卡，调用初始化圈存
								if (_that.cardType == '23') {
									_that.getBalance(devResult);
								} else {
									_that.cardSuccess((res) => {
										if (res.code == '200') {
											_that.progress.percent = 50
											//如果有OBU，重写OBU
											_that.openChannel();
										} else {
											uni.showModal({
												title: "提示",
												content: res.msg,
												showCancel: false,
											});
											_that.closeLoading();
											_that.disConnect();
										}
									});
								}
							}
						}
					);
				} else {
					_that.showModal({
						title: "错误",
						devResult: devResult,
						content: "写0015文件失败：" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
					_that.disConnect();
				}
			},
			// 0015
			async getCpu0015WriteFile(rand, callback) {
				let data = {
					rand: rand,
					cpu_card_id: this.cpu_id,
					issue_code: this.issue_code,
				};
				Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("写0015文件入参", params);
				await this.$request
					.post(this.$interfaces.issueWRITE0015, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.issue_info,
						});
					})
					.catch((error) => {
						callback({
							code: 11006,
							err_msg: error,
						});
					});
			},
			// 0016
			async getCpu0016WriteFile(rand, callback) {
				let data = {
					rand: rand,
					cpu_card_id: this.cpu_id,
					issue_code: this.issue_code,
				};
				Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("写0016文件入参", params);
				await this.$request
					.post(this.$interfaces.issueWRITE0016, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.issue_info,
						});
					})
					.catch((error) => {
						callback({
							code: error.code,
							err_msg: error.msg,
						});
					});
			},
			getBalance(devResult){
				if (devResult.code == 0) {
					_that._getBalance(_that.cardSuccessHandle,this.setLogger);
				} else {
					_that.showModal({
						title: "错误",
						devResult: devResult,
						content: "写0015文件失败：" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
					_that.disConnect();
				}
			},
			cardSuccessHandle(){
				//如果卡内余额不为0，调用发卡完成接口
				this.cardSuccess((res) => {
					if (res.code == '200') {
						this.progress.percent = 50
						//如果有OBU，重写OBU
						_that.openChannel();
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						_that.closeLoading();
						_that.disConnect();
					}
				});
			},
			// 获取卡内余额
			getBalance1(devResult) {
				if (devResult.code == 0) {
					facapi.GetBalance((devResult) => {
						//获取余额成功
						console.log("获取余额成功-devResult", devResult);
						if (devResult.code == 0) {
							if (devResult.data && devResult.data.length == 8) {
								let balance = parseInt(devResult.data, 16);
								console.log("圈存余额balance", balance);
								this.setLogger(balance)
								if (balance == 0) {
									//如果卡内余额为0，调用初始化圈存
									_that.initLoad(devResult);
								} else {
									
								}
							} else {
								_that.showModal({
									title: "错误",
									devResult: {
										code: "11013",
										err_msg: "获取卡余额失败：长度不符",
									},
									content: "获取卡余额失败：长度不符",
								});
								_that.cpuComplete({
									code: "11013",
									err_msg: "获取卡余额失败：长度不符",
								});
							}
						} else {
							console.log(devResult, "devResultdevResultdevResult");
							_that.showModal({
								title: "错误",
								devResult: devResult,
								content: "获取卡余额失败：" +
									devResult.code +
									":" +
									devResult.err_msg +
									(devResult.msg ? ":" + devResult.msg : ""),
							});
							_that.disConnect();
						}
					});
				}
			},
			setLogger(balance) {
				let params = {
					balance: balance,
					remark: '信息修正圈存日志'

				};
				this.$request.post(this.$interfaces.printLog, {
					data: params
				}).then(res => {}).catch(error => {})
			},
			// 圈存
			initLoad1(devResult) {
				this.issue_times = this.formatDate(new Date());
				if (devResult.code == 0) {
					console.log("初始化圈存", this.issue_times, pinCode, money, terminalNo, devResult);
					facapi.InitLoad(
						pinCode,
						money,
						this.issue_times,
						terminalNo,
						(rand, trade_no, mac1, balance, callback) => {
							console.log("initLoad:getMac", rand, trade_no, mac1, balance);
							_that.getInitLoad(rand, trade_no, balance, mac1, (result) => {
								callback(result);
							});
						},
						(nextDevResult) => {
							//初始化圈存完成成功
							//读卡内余额
							console.log("初始化圈存结果", nextDevResult);
							if (nextDevResult.code != 0) {
								_that.showModal({
									title: "错误",
									devResult: nextDevResult,
									content: "圈存初始化失败：" + nextDevResult.code,
								});
								_that.disConnect();
							} else {
								//圈存成功，调用发卡完成接口

								this.cardSuccess((res) => {
									if (res.code == '200') {
										this.progress.percent = 50
										//如果有OBU，重写OBU
										_that.openChannel();
									} else {
										uni.showModal({
											title: "提示",
											content: res.msg,
											showCancel: false,
										});
										_that.closeLoading();
										_that.disConnect();
									}
								});
							}
						}
					);
				} else {
					_that.showModal({
						title: "错误",
						devResult: devResult,
						content: "写0015文件失败：" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
					_that.disConnect();
				}
			},
			// 圈存
			async getInitLoad(rand, tradeNo, balance, mac1, callback) {
				let data = {
					customerId: this.comParams.customer_id,
					vehicleColor: this.comParams.vehicle_color,
					vehicleCode: this.comParams.vehicle_code,
					rand: rand,
					money: money,
					termId: terminalNo,
					tradeNo: tradeNo,
					decTime: this.issue_times,
					mac1: mac1,
					cardBalance: balance,
					issueCode: this.issue_code,
					cpuCardId: this.cpu_id,
				};
				// Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("初始化圈存入参", params);
				await this.$request
					.post(this.$interfaces.cardInitLoad, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.mac2,
						});
					})
					.catch((error) => {
						callback({
							code: 11003,
							err_msg: error.msg,
						});
					});
			},
			// 卡发行成功
			async cardSuccess(callback) {
				let data = {
					rand: "123456",
					issue_code: this.issue_code,
					cpu_card_id: this.cpu_id,
				};
				let params = {
					data: data,
				};
				Object.assign(data, this.comParams);
				console.log("卡发行成功入参", params);
				await this.$request
					.post(this.$interfaces.issueCPUISSUERESULT, params)
					.then((res) => {
						console.log("卡发行成功-res", res);
						callback(res);
					})
					.catch((error) => {
						callback(error);
					});
			},
			// 打开OBU通道，读取obu号
			openChannel() {
				facapi.OpenChannel((devResult) => {
					console.log("sdk的OpenChannel调用", devResult);
					if (devResult.code == 0) {
						// 读取设备sn号
						facapi.GetSerialNo((devResult) => {
							console.log("getSerialNo==>>", JSON.stringify(devResult));
							_that.setSystemInfo(devResult);
						});
					} else {
						_that.showModal({
							title: "错误",
							devResult: devResult,
							content: "打开Obu失败:" +
								devResult.code +
								":" +
								devResult.err_msg +
								(devResult.msg ? ":" + devResult.msg : ""),
						});
						_that.getSystemInfo(
							devResult.code,
							devResult.err_msg + (devResult.msg ? ":" + devResult.msg : ""),
							defaultObuId,
							defaultRand,
							(result) => {}
						);
						_that.disConnect();
					}
				});
			},
			// 获取oub号后进行obu发行前校验
			setSystemInfo(devResult) {
				console.log("|||setSystemInfo==>", JSON.stringify(devResult));
				if (devResult.code == 0) {
					console.log("获取obuid成功", devResult.data);
					// this.obu_id = devResult.data;
					if (this.obu_id != devResult.data) {
						uni.showModal({
							title: "错误",
							content: '要更新的标签不是该车辆绑定的标签',
							showCancel: false,
						});
						_that.disConnect();
					} else {
						// obu发行前校验
						_that.obuCheckHandle()
					}
				} else {
					_that.showModal({
						title: "错误",
						devResult: devResult,
						content: "获取Obu编号失败:" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
					_that.disConnect();
				}
			},
			// obu发行前校验
			async obuCheck(callback) {
				let data = {
					obu_id: this.obu_id,
				};
				Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("obu发行前校验", params);
				await this.$request
					.post(this.$interfaces.issueOBUIssue, params)
					.then((res) => {
						console.log("obu发行前校验res", res);
						this.issue_code = res.data.issue_code;
						callback(res);
					})
					.catch((error) => {
						callback(error);
					});
			},
			// 校验OBU信息
			obuCheckHandle() {
				this.obuCheck((res) => {
					console.log('obuCheckHandle', res, res && res.code == 200)
					if (res && res.code == 200) {
						this.progress.percent = 60
						_that.obuReaday(res);
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						this.disConnect();
					}
				});
			},
			// obu发行准备完成，准备写卡写签
			obuReaday(devResult) {
				if (devResult.code == 200) {
					console.log("obu发行准备完成", devResult);
					facapi.SetSystemInfo(
						(rand, callback) => {
							console.log("setSystemInfo.getMac", JSON.stringify(devResult));
							_that.getSystemInfo(
								this.obu_id,
								devResult.err_msg,
								rand,
								(result) => {
									callback(result);
								}
							);
						},
						(devResult) => {
							console.log("写系统信息结果", devResult);
							_that.setVehicleInfo(devResult);
						}
					);
				} else {
					_that.showModal({
						title: "错误",
						devResult: devResult,
						content: "圈存失败：" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
					_that.disConnect();
				}
			},
			// OBU写系统信息
			async getSystemInfo(obu_id, errMsg, rand, callback) {
				let data = {
					obu_id: this.obu_id,
					rand: rand,
					issue_code: this.issue_code,
				};
				Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("OBU写系统信息", JSON.stringify(params));
				await this.$request
					.post(this.$interfaces.issueOBUISSUEMFEF01, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.issue_info,
						});
					})
					.catch((error) => {
						callback({
							code: 11001,
							err_msg: error.msg,
						});
					});
			},
			// 写车辆信息
			setVehicleInfo(devResult) {
				if (devResult.code == 0) {
					facapi.SetVehicleInfo(
						(rand, callback) => {
							_that.getVehicleInfo(
								devResult.code,
								devResult.err_msg,
								rand,
								(result) => {
									callback(result);
								}
							);
						},
						(devResult) => {
							console.log("setVehicleInfo：写车辆信息结果：", devResult);
							this.progress.percent = 80
							_that.obuResult(devResult);
						}
					);
				} else {
					_that.showModal({
						title: "错误",
						devResult: devResult,
						content: "写Obu系统信息失败:" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
					_that.disConnect();
				}
			},
			// 写车辆信息
			async getVehicleInfo(errCode, errMsg, rand, callback) {
				let data = {
					obu_id: this.obu_id,
					rand: rand,
					issue_code: this.issue_code,
				};
				Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("写车辆信息参数", JSON.stringify(params));
				await this.$request
					.post(this.$interfaces.issueOBUISSUEDFEF01, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.issue_info,
						});
					})
					.catch((error) => {
						callback({
							code: 11000,
							err_msg: error.msg,
						});
					});
			},
			// obu发行完成业务处理结果上报
			obuResult(devResult) {
				if (devResult.code != 0) {
					_that.showModal({
						title: "错误",
						devResult: devResult,
						content: "写Obu车辆信息失败:" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
					_that.disConnect();
				} else {
					// obu发行成功
					this.obuIssueComplete((res) => {
						if (res.code == '200') {
							this.updateCarStatus(updateStatus => {
								if (updateStatus) {
									this.progress.percent = 100
									uni.reLaunch({
										url: '/pagesB/deviceBusiness/updateSuccess/updateSuccess'
									})
								}
							})
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
						this.disConnect();
					});
				}
			},
			// obu发行成功
			async obuIssueComplete(callback) {
				let data = {
					obu_id: this.obu_id,
					issue_code: this.issue_code,
				};
				Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("obu激活成功入参", params);
				await this.$request
					.post(this.$interfaces.issueObuIssueComplete, params)
					.then((res) => {
						callback(res);
					})
					.catch((error) => {
						callback(error);
					});
			},
			// 显示弹框
			showModal(data) {
				//隐藏loading
				_that.isBtnLoader = false;
				console.log(data.content, 'sdk报错');
				_that.setFaildMsg()
				_that.closeLoading();
				//显示弹框
				let obj = {
					...data
				};
				obj = data.showMyContent ?
					obj : {
						...data,
						content: '操作失败，请打开手机蓝牙，并确认已按步骤完成激活前准备。请将手机靠近设备后重试。(' + data.devResult.code + ':' + data
							.devResult
							.err_msg + ')'
					};
				uni.showModal({
					...obj,
					showCancel: false,
					success: () => {}
				});
			},
			setFaildMsg() {
				this.updateMsg = this.faildMsg //失败信息。
			},
			// 中断或结束断开连接关闭蓝牙
			disConnect() {
				this.closeLoading();
				this.setFaildMsg();
				this.isActive = false;
				this.isConnect = false;
				this.isBtnLoader = false;
				bleapi.closeBLEConnection();
				facapi.facSdk.DisconnectDevice(function(code) {
					console.log("关闭连接结果", code);
				});
				// 完成后关闭蓝牙模块
				// bleapi.CloseBle((obj) => {
				// 	console.log(obj);
				// });
			},
			closeLoading() {
				this.connectLoaing = false;
				this.isLoading = false;
			},
			closeBle() {
				// 关闭蓝牙模块，防止中途断开，连接不上
				// bleapi.CloseBle((obj) => {
				// 	console.log(obj);
				// });
				bleapi.closeBLEConnection();
			},
			// 圈存存入当前时间
			formatDate(now) {
				var year = now.getFullYear();
				var month =
					now.getMonth() + 1 < 10 ?
					"0" + (now.getMonth() + 1) :
					now.getMonth() + 1;
				var date = now.getDate() < 10 ? "0" + now.getDate() : now.getDate();
				var hour = now.getHours() < 10 ? "0" + now.getHours() : now.getHours();
				var minute =
					now.getMinutes() < 10 ? "0" + now.getMinutes() : now.getMinutes();
				var second =
					now.getSeconds() < 10 ? "0" + now.getSeconds() : now.getSeconds();
				return (
					year + "" + month + "" + date + "" + hour + "" + minute + "" + second
				);
			},
		},
		onUnload() {
			bleapi.closeBLEConnection();
		},
	}
</script>

<style lang="scss" scoped>
	.udpate-device .card {
		margin: 30rpx;
	}

	.udpate-device .card-box {
		width: 100%;
		height: 240rpx;
		background: url('~@/static/toc/etc_device.png') no-repeat;
		background-size: 100% 100%;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
	}

	.udpate-device .card-box .bd {
		padding: 0 30rpx;
	}

	.udpate-device .card-box .bd .title {
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: 500;
		padding-bottom: 10rpx;
	}

	.udpate-device .card-box .bd .des {
		font-size: 28rpx;
		color: #FFFFFF;
		font-weight: 500;
		opacity: 0.6;
		padding-bottom: 10rpx;
	}

	.progress {
		margin: 50rpx 30rpx;
	}

	.progress .desc {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 40rpx;
	}


	.loadBusiness .bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;

	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}
</style>