<!-- 车辆OCR识别档案上传 -->
<template>
	<view class="ocr">
		<tHandleStep :current="1"></tHandleStep>
		<view class="weui-cells__title">请上传车辆相关照片</view>
		<view class="orc-form">
			<view class="upload" v-for="(item, index) in fileList" :key="index" v-if="item.isShow">
				<template v-if="item.isShow">
					<view class="upload-box" @tap="ViewImage(item.base64img)" v-if="item.base64img != ''">
						<image :src="item.base64img" class="upload-box__img" mode="aspectFilt"></image>
						<view class="cu-tag bg-brown upload-box__close" @tap.stop="DelImg(item)"><text
								class="cuIcon-close close"></text></view>
					</view>
					<view v-if="!item.base64img" class="upload-box" @tap="ChooseImage(item)">
						<image :src="item.url" class="upload-box__img"></image>
					</view>
					<view class="upload__tip">{{ item.label }}</view>
				</template>
			</view>
		</view>
		<!-- 		<neil-modal @confirm="methodEnsure" :show="alltoastData.showFlag" :align="alltoastData.allign"
			:title="alltoastData.title" :content="alltoastData.msg" :confirmText="alltoastData.btnTextEnsure"
			:show-cancel="alltoastData.btnTextCloseFlag"></neil-modal> -->
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag="ownFlag" :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64"></cpimg>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box"><button class="weui-btn weui-btn_primary"
					@click="onSubmitHandle">上传档案</button></view>
		</view>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import cpimg from '@/components/uni-yasuo/cpimg.vue';

	import {
		setCurrentCar,
		getLoginUserInfo,
		getStore
	} from '@/common/storageUtil.js';

	import neilModal from '@/components/neil-modal/neil-modal.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import tHandleStep from '@/pagesB/components/t-handleStep/handleStep.vue'

	export default {
		components: {
			cpimg,
			neilModal,
			tLoading,
			tHandleStep
		},
		props: {
			carType: {
				type: Number,
				default: 1
			},
			sourceType: {
				type: Number,
				default: 0
			},
			showOne: {
				type: Boolean,
				default: false
			},
			type: {
				type: Number,
				default: 1
			}
		},
		data() {
			return {
				showLoad: false,
				isLoading: false,
				ownFlag: 0,
				customer_id: '',
				redirectUrl: '',
				cpuId: '',
				sides: '',
				scene: '',
				deviceData: {},
				remark: [],
				fileList: [{
						photo_code: "3",
						label: "行驶证正页",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_front.png",
						code: '',
						image: '',
						base64img: '',
						scene: 2,
						isShow: false,
					},
					{
						photo_code: "12",
						label: "行驶证副页",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_back.png",
						code: '',
						base64img: '',
						scene: 2,
						isShow: false,
					},
					{
						photo_code: "1",
						label: "身份证人像面",
						base64img: '',
						code: '',
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png",
						scene: 1,
						isShow: false,
					},
					{
						photo_code: "11",
						label: "身份证国徽面",
						base64img: '',
						code: '',
						isShow: false,
						scene: 1,
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png",
					},
					{
						photo_code: "15",
						label: "车头照片",
						base64img: '',
						code: '',
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/car_img.png",
						scene: 2,
						isShow: false,
					},
					{
						photo_code: '6',
						label: '行驶证车辆照片',
						base64img: '',
						code: '',
						isShow: false,
						scene: 2,
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unload_bg_img.png'
					},
					{
						photo_code: "2",
						label: "单位营业执照副本",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_back.png",
						base64img: '',
						code: '',
						scene: 1,
						isUpload: false,
					},
					{
						photo_code: '20',
						label: '道路运输证',
						base64img: '',
						code: '',
						isShow: false,
						scene: 2,
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unload_bg_img.png'
					}
				],
				imageList: {
					image1: '',
					image11: '',
					image12: '',
					image15: '',
					image2: '',
					image20: '',
					image3: '',
					image6: ''
				}
			};
		},
		onLoad(option) {
			this.redirectUrl = option.redirectUrl || '';
			this.deviceData = JSON.parse(decodeURIComponent(option.deviceData));
			this.customer_id = this.deviceData.custMastId;
			this.showImg()
		},
		// created() {
		// 	this.customer_id = getLoginUserInfo().userNo;
		// 	let vehicle_type = getStore('vehicle_type') || 1;
		// 	//拿车牌号
		// 	this.getDraft()
		// 	for (let i = 0; i < this.fileList.length; i++) {
		// 		if (this.fileList[i].photo_code == 20 && vehicle_type == 1) {
		// 			this.fileList[i].isShow = true;
		// 		}
		// 	}
		// },
		methods: {
			showImg() {
				let imgList = JSON.parse(this.deviceData.remark)
				for (let i = 0; i < this.fileList.length; i++) {
					if (imgList.includes(parseInt(this.fileList[i].photo_code))) {
						this.fileList[i].isShow = true;
					}
				}

			},
			ChooseImage(data) {
				this.sides = data.photo_code;
				this.scene = data.scene;
				this.$refs.cpimg._changImg(this.sourceType);
			},
			saveImgList(callback) {
				this.isLoading = true
				let params = {
					id: this.deviceData.id,
					...this.imageList
				}
				this.$request
					.post(this.$interfaces.updateCarSaveImg, {
						data: params
					})
					.then(res => {
						this.isLoading = false;
						if (res.code == 200) {
							callback(true)
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
						this.isLoading = false;
					});
			},
			//进行下一步
			toNext() {
				//保存图片
				this.saveImgList(saveFlag => {
					if (saveFlag) {
						if (this.deviceData.bizType == '3') {
							uni.navigateTo({
								url: '/pagesB/deviceBusiness/updateDevice/updateDevice?deviceData=' +
									encodeURIComponent(
										JSON
										.stringify(this.deviceData))
							})
						} else if (this.deviceData.bizType == '1') {
							// this.updateCarStatus(updateStatus => {
							// 	if (updateStatus) {
									uni.reLaunch({
										url: '/pagesB/deviceBusiness/updateSuccess/updateSuccess'
									})
								// }
							// })
						}
					}
				})
			},
			// updateCarStatus(callback) {
			// 	this.isLoading = true
			// 	let params = {
			// 		id: this.deviceData.id
			// 	}
			// 	this.$request
			// 		.post(this.$interfaces.updateCarStatus, {
			// 			data: params
			// 		})
			// 		.then(res => {
			// 			this.isLoading = false;
			// 			if (res.code == 200) {
			// 				callback(true)
			// 			} else {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				});
			// 			}
			// 		})
			// 		.catch(error => {
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error.msg,
			// 				showCancel: false
			// 			});
			// 			this.isLoading = false;
			// 		});
			// },
			// //获取草稿
			// getDraft() {
			// 	this.$request.post(this.$interfaces.getDraft).then(res => {
			// 		console.log(res, '获取草稿');
			// 		if (res.code == 200) {
			// 			this.formData.vehicle_code = res.data.vehicle_code
			// 			this.formData.vehicle_color = res.data.vehicle_color
			// 		}
			// 	})
			// },
			onSubmitHandle() {
				let current = null;
				for (let i = 0; i < this.fileList.length; i++) {
					if (this.fileList[i].isShow && !this.fileList[i].file_serial) {
						current = this.fileList[i];
						break;
					}
				}
				if (current) {
					uni.showModal({
						title: '提示',
						content: '请上传' + current.label + '！',
						cancelText: '取消',
						confirmText: '确认'
					});
					return;
				}
				if (this.redirectUrl) {
					uni.navigateTo({
						url: this.redirectUrl
					});
					return;
				}

				for (let i = 0; i < this.fileList.length; i++) {
					if (this.fileList[i].isShow && this.fileList[i].file_url) {
						this.imageList['image' + this.fileList[i].photo_code] = this.fileList[i].code
					}
				}

				// uni.navigateTo({
				// 	url: '/pagesA/newBusiness/vehicle/vehicle'
				// });
				this.toNext()
			},
			////图片压缩成功
			cpimgOk(file) {
				this.isLoading = true;
				this.unloadFile(file);
			},
			// 上传文件
			unloadFile(file) {
				let base64Img = file.toString();
				let ocr_type = '';
				let current = {};
				var imgStr = base64Img.split(';')[1].split(',')[1] + '';
				for (let i = 0; i < this.fileList.length; i++) {
					if (this.fileList[i].photo_code == this.sides) {
						current = this.fileList[i];
					}
				}
				this.$set(current, 'base64img', base64Img);
				this.sendUploadFile(current);
			},
			sendUploadFile(fileParams) {
				let current = fileParams ? JSON.parse(JSON.stringify(fileParams)) : {};
				let biz_content = {
					customer_id: this.customer_id,
					photo_code: current.photo_code,
					vehicle_code: this.deviceData.carNo,
					vehicle_color: this.deviceData.carColor,
					scene: this.scene // C端档案上传
				};
				// console.log('params---------------', biz_content)
				let params = {
					file_content: current.base64img,
					method_code: '2', // 档案上传
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request
					.post(this.$interfaces.uploadFile, {
						data: params
					})
					.then(res => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('res.data', res.data)
							this.$set(fileParams, 'file_serial', res.data.file_serial);
							this.$set(fileParams, 'file_url', res.data.file_url);
							this.$set(fileParams, 'code', res.data.code);
							// if (this.sides == '6') {
							// 	this.formData.driving_car_path = res.data.file_url
							// }
							// if (this.side == '20') {
							// 	this.formData.driving_car_trans_path = res.data.file_url
							// }
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
						this.isLoading = false;
					});
			},
			//图片压缩失败
			cpimgErr(e) {
				// console.log(e);
				uni.showModal({
					title: '提示',
					content: '图片压缩失败',
					showCancel: false
				});
			},
			//图片预览
			ViewImage(path) {
				let newArr = [];
				newArr.push(path);
				uni.previewImage({
					urls: newArr
				});
			},
			// 删除图片
			DelImg(data) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							let current = {};
							for (let i = 0; i < this.fileList.length; i++) {
								if (this.fileList[i].photo_code == data.photo_code) {
									this.fileList[i].base64img = '';
									current = this.fileList[i];
								}
							}
							this.$set(current, 'file_serial', '');
						}
					}
				});
			}
		}
	};
</script>

<style scoped lang="scss">
	$uploadWidth: 340rpx;
	$uploadHeight: 200rpx;

	.ocr {
		background-color: #ffffff;
		padding-bottom: 180rpx;
	}

	.orc-form {
		padding: 0 30rpx 30rpx 30rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		background-color: #ffffff;
	}

	.upload {
		width: $uploadWidth;
	}

	.upload .upload__tip {
		font-size: 28rpx;
		text-align: center;
		width: 100%;
		color: #333333;
		font-weight: 400;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.upload .upload-box {
		width: $uploadWidth;
		height: $uploadHeight;
		position: relative;
	}

	.upload .upload-box .upload-box__img {
		width: $uploadWidth;
		height: $uploadHeight;
	}

	.upload .upload-box .upload-box__close {
		position: absolute;
		right: 0;
		top: 0;
		padding: 0 10rpx;
		font-size: 36rpx;
	}

	.upload .upload-box .upload-box__close .close {
		font-size: 36rpx;
	}
</style>