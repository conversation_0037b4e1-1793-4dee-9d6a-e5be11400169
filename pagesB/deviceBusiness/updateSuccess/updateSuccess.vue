<template>
	<view class="update-success">
		<tHandleStep :current="3"></tHandleStep>
		<view class="protocol">
			<view class="msg">
				<view class="msg-icon">
					<image src="../../static/pic_success.png" class="img"></image>
				</view>
				<view class="msg-text">
					<view class="msg-title">更新成功</view>
				</view>
				<!-- <view class="weui-bottom-fixed g-flex">
					<view class="weui-bottom-fixed__box bottom-box" id="btnToLast">
						<view class="weui-btn weui-btn_primary" type="button">返回首页</view>
					</view>
				</view> -->
				<view class="bottom-box g-flex justify-center">
					<button class="weui-btn weui-btn_primary" @click="toHome">返回首页</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import tHandleStep from '@/pagesB/components/t-handleStep/handleStep.vue'

	export default {
		components: {
			tHandleStep,
		},
		data() {
			return {
			};
		},
		methods: {
			toHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>/p-home'
				})
			}
		},
	};
</script>

<style>

	.msg {
		margin-top: 48rpx;
	}

	.msg .msg-icon {
		width: 100%;
		display: flex;
		justify-content: center;
		margin-bottom: 24rpx;
	}

	.msg-title {
		font-weight: 500;
		font-size: 32rpx;
		text-align: center;
	}


	.bottom-box {
		border: 0;
		margin-top: 80rpx;
	}

	.weui-btn {
		border-radius: 12rpx;
		width: 300rpx;
	}
</style>
