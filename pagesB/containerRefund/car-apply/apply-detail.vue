<template>
	<view class="record-detail">
		<view class="detail">
			<view class="cu-form-group">
				<view class="title">入口站：</view>
				<view class="desc">{{detail.enStation}}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">出口站：</view>
				<view class="desc">{{detail.exStation}}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">交易金额：</view>
				<view class="desc">{{moneyFilter(detail.fee)}}</view>
			</view>
			<view class="cu-form-group" style="padding: 30rpx;">
				<view class="title">证明图片：</view>
				<view class="desc" v-if="imgList.length > 0">
					<image class="img" v-for="(item,index) in imgList" :key="index" :src="item.url" mode="aspectFill"
						@tap="ViewImage" :data-url="item.url"></image>
				</view>
			</view>
			<view class="cu-form-group" style="padding: 30rpx;">
				<view class="title">授权材料：</view>
				<view class="desc" v-if="detail.contactDto.attorneyUrl">
					<image class="img" :src="detail.contactDto.attorneyUrl" mode="aspectFill" @tap="ViewOtherImage">
					</image>
				</view>
			</view>
			<view class="info">
				<view class="c-title">接收退款的信息</view>
			</view>
			<template v-if="detail.bankCustName">
				<view class="cu-form-group">
					<view class="title">户名：</view>
					<view class="desc">{{detail.bankCustName | noPassByName}}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">开户行：</view>
					<view class="desc">{{detail.bankName}}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">银行卡号：</view>
					<view class="desc">{{detail.bankCardNo | noPassByCardNo}}</view>
				</view>
			</template>
			<view class="cu-form-group">
				<view class="title">申请时间：</view>
				<view class="desc">{{detail.applyTime}}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">申请状态：</view>
				<view :class="{'red-value':detail.handleMode==3 ||
					detail.handleMode==7,'blue-value':detail.handleMode==1,'green-value':detail.handleMode==2}" class="desc">
					{{getType(detail.handleMode,handleModeList)}}
				</view>
			</view>
			<view class="cu-form-group">
				<view class="title">备注：</view>
				<view class="desc" style="word-break: break-all;">{{detail.remark}}</view>
			</view>
		</view>
		<view class="btn-wrapper">
			<button v-if="detail.handleMode == 3" type="default" @click="updateForm()">修改申请</button>
			<button v-if="detail.handleMode == 3 || detail.handleMode == 7" type="default"
				@click="cancelForm()">结束申请</button>
			<button type="default" @click="toProgress()">办理进度</button>
		</view>
		<tLoading :isShow="isShowLoding" />
	</view>
</template>

<script>
	// import CarCard from "@/components/car-card-4501/car-card.vue";
	import CarCard from "@/pagesB/components/car-card-4501/car-card.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import float from '@/common/method/float.js'
	import {
		handleModeList
	} from '@/common/const/optionData.js'
	export default {
		components: {
			CarCard,
			tLoading
		},
		data() {
			return {
				handleModeList,
				isShowLoding: false,
				applyId: '',
				detail: {},
				handlePartyList: [{
						value: '1',
						label: '发行方'
					},
					{
						value: '2',
						label: '路方'
					},
				],
				imgList: []
			}
		},
		onLoad(options) {
			console.log(options)
			this.applyId = options.applyId
			this.getApplyDetail()
			// this.getRecordDetail()
		},
		methods: {
			getType(value, objList) {
				for (let i = 0; i < objList.length; i++) {
					if (objList[i].value == value) {
						return objList[i].label
					}
				}
				return ''
			},
			getApplyDetail() {
				this.isShowLoding = true;
				let params = {
					applyId: this.applyId
				}
				let data = {
					'data': params,
				}
				console.log('入参', data)
				this.$request.post(this.$interfaces.containerRecordDetail, data)
					.then(res => {
						this.isShowLoding = false;
						console.log('detaildetaildetail', res)
						this.detail = res.data
						for (let i = 1; i < 6; i++) {
							let urlStr = 'imgData' + i
							let codeStr = 'code' + i
							// console.log('res.data[imgData + i]', res.data[imgData + i])
							if (res.data[urlStr]) {
								this.imgList.push({
									url: res.data[urlStr],
									code: res.data[codeStr]
								})
							}
						}
						console.log('this.imgList', this.imgList)
					}).catch(err => {
						this.isShowLoding = false;
					})
			},
			// getRecordDetail() {
			// 	// this.isShowLoding = true;
			// 	let params = {
			// 		applyId: this.applyId
			// 	}
			// 	let data = {
			// 		'data': params,
			// 	}
			// 	console.log('入参', data)
			// 	this.$request.post(this.$interfaces.refundApplyDetail, data)
			// 		.then(res => {
			// 			// this.isShowLoding = false;
			// 			// console.log('detail', res)
			// 			this.remark = res.data[0].remark
			// 		}).catch(err => {
			// 			// this.isShowLoding = false;
			// 		})
			// },
			cancelForm() {
				uni.showModal({
					title: '结束申请操作',
					content: '请确认是否结束该退费申请单？结束后不可再次申请或修改订单。',
					success: (res) => {
						if (res.confirm) {
							this.cancelHandle()
						}
					}
				})
			},
			cancelHandle() {
				this.isLoading = true
				console.log('this.imgList', this.imgList)
				let params = {
					applyId: this.applyId,
					imgData1: '',
					imgData2: '',
					imgData3: '',
					imgData4: '',
					imgData5: '',
					operateType: 3,
					bankCardNo: this.detail.bankCardNo || '',
					bankCustName: this.detail.bankCustName || '',
					bankName: this.detail.bankName || '',
				}

				for (let i = 0; i < this.imgList.length; i++) {
					if (this.imgList[i] && this.imgList[i].url) {
						params['imgData' + (i + 1)] = this.imgList[i].url
					}
				}

				let data = {
					'data': params
				}
				console.log('params', JSON.stringify(params))
				this.$request.post(this.$interfaces.commitRefundForm, data).then(res => {
					this.isLoading = false;
					console.log('res', res)
					if (res.code == 200) {
						//提交申请成功
						uni.navigateTo({
							url: 'apply-list'
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg
						});
						return
					}
				}).catch(err => {
					this.isLoading = false;
				})
			},
			updateForm() {
				uni.navigateTo({
					url: './refund-apply?applyId=' + this.applyId
				})
			},
			ViewImage(e) {
				let viewArr = []
				this.imgList.forEach(item => {
					viewArr.push(item.url)
				})
				uni.previewImage({
					urls: viewArr,
					current: e.currentTarget.dataset.url
				});
			},
			ViewOtherImage(e) {
				let viewArr = []
				viewArr.push(this.detail.contactDto.attorneyUrl)
				uni.previewImage({
					urls: viewArr
				});
			},
			toRefundApply() {
				uni.navigateTo({
					url: '../car-apply/refund-apply?applyId=' + this.applyId
				})
			},
			toProgress() {
				uni.navigateTo({
					url: './apply-progress?applyId=' + this.applyId
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
		filters: {
			//脱敏用户名
			noPassByName(str) {
				if (null != str && str != undefined) {
					if (str.length <= 2 && str.length > 0) {
						return "*" + str.substring(1, str.length);
					} else if (str.length > 2 && str.length <= 3) {
						return "**" + str.substring(2, str.length);
					} else if (str.length > 3) {
						let num = float.div(str.length, 3)
						let star = ''
						let newLength = float.sub(str.length, float.add(num, num))
						num = Math.round(num)
						for (let i = 0; i < newLength; i++) {
							star += "*";
						}
						return str.substring(0, num) + star + str.substring(float.sub(str.length, newLength), str
							.length)
					}
				} else {
					return "";
				}
			},
			//脱敏etc卡号
			noPassByCardNo(str) {
				if (null != str && str != undefined) {
					var pat = /(\d{3})\d*(\d{4})/;
					return str.replace(pat, '$1*************$2');
				} else {
					return "";
				}

			}
		},
	}
</script>

<style lang="scss" scoped>
	.img {
		width: 220rpx;
		height: 130rpx;

		&:nth-child(2n-1) {
			margin-right: 20rpx;
		}

		&:nth-child(3),
		&:nth-child(4),
		&:nth-child(5) {
			margin-top: 20rpx;
		}

		// &:nth-child(2n) {
		// 	justify-content: flex-end;
		// }
	}

	.info {
		padding: 30rpx 0;

		.c-title {
			height: 40rpx;
			padding-left: 14rpx;
			border-left: 8rpx solid #0066E9;
			color: rgba(51, 51, 51, 100);
			font-size: 30rpx;
			font-family: PingFangSC-bold;
		}
	}


	.record-detail {
		.detail {
			margin: 30rpx;
			// border: 1rpx solid #e9e9e9;
			border-radius: 10rpx;
			// background-color: var(--white);

			.cu-form-group {
				justify-content: flex-start;
				// background-color: transparent;

				.title {
					flex: 0 0 160rpx;
					width: 160rpx;
					height: 60rpx;
					line-height: 60rpx;
					color: #999999;
					font-size: 28rpx;
					font-family: PingFangSC-bold;
				}

				.desc {
					flex: 1;
					color: #333333;
					line-height: 28rpx;
				}

				.blue-value {
					color: #007AFF;
				}

				.red-value {
					color: red;
				}

				.green-value {
					color: #0066E9;
				}

				/deep/.index-car-card {
					background: transparent;
					height: 0;
					border-radius: 0;
					margin: 0;

					.son-card {
						width: 100%;
						height: 75rpx;
						padding: 10rpx 20rpx;
						border-radius: 10rpx;
						margin-bottom: 0;
						font-size: 40rpx;
					}
				}
			}
		}

		.btn-wrapper {
			display: flex;
			align-items: center;
			padding: 0 30rpx;

			&>button {
				flex: 1;
				background-color: #0066E9;
				color: #FFFFFF;
				height: 98rpx;
				line-height: 98rpx;
				font-size: 32rpx;
				text-align: center;
				border: 0;
				margin-right: 20rpx;

				&:last-child {
					margin-right: 0;
				}
			}

			.btn-disabled {
				color: #333333;
				cursor: disabled;
			}
		}
	}
</style>