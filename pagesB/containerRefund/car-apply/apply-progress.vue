<template>
	<view class="apply-progress">
		<refund-steps :stepList="stepList"></refund-steps>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	// import refundSteps from '@/components/refund-steps/refund-steps.vue'
	import refundSteps from '@/pagesB/components/refund-steps/refund-steps.vue'
	import tLoading from "@/components/common/t-loading.vue"
	export default {
		components: {
			refundSteps,
			tLoading
		},
		data() {
			return {
				applyId: '',
				stepList: []
			}
		},
		onLoad(options) {
			this.applyId = options.applyId
			this.getProgress()
		},
		methods: {
			getProgress() {
				this.isLoading = true;
				let params = {
					applyId: this.applyId
				}
				let data = {
					'data': params,
				}
				console.log('入参')
				this.$request.post(this.$interfaces.refundApplyDetail, data).then(res => {
					this.isLoading = false;
					console.log('res', res)
					if (res.code == 200) {
						//提交申请成功
						res.data.forEach(item => {
							this.stepList.push({
								handleMode: item.handleMode,
								operateTime: item.operateTime,
								remark: item.remark
							})
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg
						});
						return
					}
				}).catch(err => {
					this.isLoading = false;
				})
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>
