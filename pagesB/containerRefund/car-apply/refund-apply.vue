<template>
	<view class="refund-apply">
		<view class="cu-form-group">
			<view class="c-title">证明材料</view>
		</view>
		<view class="upload-container">
			<view class="image-box">
				<view class="view-image my-flex-center" v-for="(item,index) in imgList" :key="index" @tap="ViewImage"
					:data-url="item.url">
					<view class="border-container">
						<image :src="item.url" class="view-img" mode="aspectFill"></image>
						<view class="my-tag cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
							<text class='cuIcon-close'></text>
						</view>
					</view>
				</view>
				<view class="view-image my-flex-center">
					<view class="border-container" style="border: 0;padding: 0;" @tap="ChooseImage('origin')"
						v-if="imgList.length<5">
						<image style="width: 336rpx;height: 222rpx;" src="../../static/container_upload.png"
							class="view-img" mode="widthFix"></image>
			</view>
		</view>
				<!-- <view class="account" @click="getAccount">
					选择退款账户
				</view> -->
			</view>
			</view>
		<view class="view-container">
			<view class="tips-container">
				<view class="tips">提示：</view>
				<view class="tips">1. 如有多张单据，每张单据上传一张图片</view>
				<view class="tips">2.《集装箱发放/设备交接单》应包含清晰完整的业务章/闸口章《汽车载货清单》应包含清晰完整的业务章及报关章。</view>
				<view class="tips">3.同一图片请勿重复上传。</view>
			</view>
		</view>
		<form v-if="vehicleInfo.gx_card_type == '4' && !hasRefund">
			<view class="cu-form-group">
				<view class="c-title">退款银行账户</view>
				</view>
			<view class="cu-form-group" style="border: 0;">
				<view class="title form_label-require">户名:</view>
				<input class="input" v-model="formData.bankCustName"></input>
			</view>
			<view class="cu-form-group" style="border: 0;">
				<view class="title form_label-require">开户行:</view>
				<!-- <input class="input" v-model="formData.bankName"></input> -->
				<view class="picker-wrapper">
					<picker :range="disputeRefundBankName" @change="bindPayTypeChange" range-key="label"
						:value="picker_index">
						<view style="color: #999999;" class="picker" v-if="picker_index==null">
							请选择开户行
					</view>
						<view class="picker" style="font-size: 28rpx;" v-else>
							{{ disputeRefundBankName[picker_index].label }}
					</view>
					<!-- <input class="input" v-model="formData.bankName" v-else disabled /> -->
				</picker>
			</view>
			</view>
			<view class="cu-form-group" style="border: 0;">
				<view class="title form_label-require">银行卡号:</view>
				<view class="btn-wrapper">
					<input class="input" type="number" style="padding-right: 35px;" maxlength="26"
						@input="changeInput($event)" :value="formData.bankCardNo"></input>
					<!-- <button type="default">识别卡号</button> -->
					<text class='cuIcon-cameraadd' @click="ocrBankImg"
						style="font-size: 40rpx;position: absolute;right: 6px;top: 4px;"></text>
				</view>
			</view>
			<view class="cu-form-group g-flex-column" style="position: relative;border: 0;align-items: flex-start;">
				<view class="title-wrapper g-flex g-flex-align-center g-flex-justify" style="flex: 1;width: 100%;">
					<view class="title" style="font-size: 30rpx;font-weight: bold;">授权材料</view>
					<view class="g-flex g-flex-align-center operator-btn">
						<view class="fileHandle" @click="authorizationFile('preview')">
							查看样例
						</view>
						<view class="fileHandle" @click="authorizationFile('downLoad')">
							下载模板
						</view>
					</view>
				</view>
				<view class="view-image">
					<view class="border-container" style="border: 0;padding: 0;">
						<image v-if="!formData.attorneyUrl" @tap="ChooseImage('other')"
							style="width: 336rpx;height: 222rpx;" src="../../static/container_upload.png"
							class="view-img" mode="widthFix">
						</image>
						<image v-if="formData.attorneyUrl" @tap="ViewOtherImage" style="width: 336rpx;height: 222rpx;"
							:src="formData.attorneyUrl" class="view-img" mode="aspectFill">
						</image>
						<view v-if="formData.attorneyUrl" class="my-tag cu-tag bg-red" @tap.stop="DelOtherImg">
							<text class='cuIcon-close'></text>
						</view>
					</view>
				</view>
			</view>
			<view class="view-container">
				<view class="tips-container">
					<view class="tips">材料要求说明：</view>
					<view class="tips">1. 退款银行账户用于接收通行费退费等，请仔细核对，并确保银行卡状态正常</view>
					<view class="tips">2. 退款银行账户信息属长期有效材料，如有变更，请及时更改账户信息。</view>
					<view class="tips">3. 如收款账户名与ETC账户名不一致，需提供ETC开户人的授权材料。</view>
				</view>
			</view>
			<!-- </view> -->
			<!-- <view class="cu-form-group" style="border-bottom: 1rpx solid #eee;">
				<view class="title">备注信息:</view>
				<textarea class="textarea" v-model="formData.remark"></textarea>
			</view> -->
		</form>
		<view class="remark-wrapper">
			<view class="cu-form-group">
				<view class="c-title">备注信息</view>
			</view>
			<view class="textarea-wrapper">
				<textarea class="textarea" @input="monitorInput" maxlength="80" cols="20" rows="5"
					v-model="formData.remark" placeholder="补充描述，有助于商家更好的处理售后问题"
					placeholder-style="color:#B9B9B9;font-size:24rpx;"></textarea>
				<view class="limit">
					{{num}}/80
			</view>
			</view>
		</view>
		<view class="tips-container" style="padding:30rpx;"
			v-if="showTipsOne || showTipsTwo() || vehicleInfo.gx_card_type == '7'">
			<view class="tips">温馨提示：</view>
			<view class="tips" v-if="showTipsOne">您的退费金额可直接退至您的ETC账户，后续请留意消息提醒。</view>
			<view class="tips" v-if="showTipsTwo()">您的ETC卡支持原路退款，退费金额可直接退至您的代扣账户，后续请留意消息提醒。</view>
			<view class="tips" v-if="vehicleInfo.gx_card_type == '7'">您的产品为月结型产品，确定可退款后，会抵扣您下月的月结账单金额。</view>
		</view>
		<view class="bottom-wrapper">
			<TButton style="background-color: #007AFF;" :title="'提交申请'" @clickButton="submit()"
				:isLoadding="isBtnLoader" />
		</view>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import {
		mapGetters
	} from 'vuex'
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import TButton from "@/components/t-button.vue"
	import tLoading from "@/components/common/t-loading.vue"
	import {
		getEtcAccountInfo,
		getCurrentCar
	} from '@/common/storageUtil.js'
	import {
		disputeRefundBankName
	} from '@/common/const/optionData.js'
	export default {
		components: {
			TButton,
			tLoading,
			cpimg
		},
		computed: {
			...mapGetters(['refundList'])
		},
		data() {
			return {
				disputeRefundBankName,
				isBtnLoader: false,
				isLoading: false,
				applyId: '',
				ownFlag: 0,
				formData: {
					custMastId: '',
					cardNo: '',
					carNo: '',
					carColor: '',
					containerId: '',
					bankCardNo: '', //银行卡号
					bankCustName: '', //卡主姓名
					bankName: '', //开户行
					remark: '',
					operateType: 1,
					attorneyUrl: '',
					attorneyUrlCode: '',
				},
				// bankList: [
				// 	'中国工商银行',
				// 	'中国建设银行',
				// 	'中国银行总行',
				// 	'中国农业银行',
				// 	'中国邮政储蓄银行',
				// 	'招商银行',
				// 	'中信银行',
				// 	'中国民生银行',
				// 	'兴业银行',
				// 	'上海浦东发展银行',
				// 	'广发银行',
				// 	'中国光大银行',
				// 	'柳州银行',
				// 	'交通银行',
				// 	'桂林银行',
				// 	'广西农村信用社',
				// 	'广西北部湾银行',
				// 	'贵阳银行',
				// 	'华夏银行',
				// 	'平安银行'
				// ],
				imgList: [],
				imgType: '', //照片类型
				num: 0, //字数
				otherBankName: '', //开户行其他
				picker_index: null,
				isOcr: false,
				hasRefund: true,
				accountList: {}, //银行账户信息
			}
		},
		computed: {
			vehicleInfo() {
				return getCurrentCar() || {}
			},
			showTipsOne() {
				if (this.vehicleInfo.gx_card_type == '0' || this.vehicleInfo.gx_card_type == '2' || this.vehicleInfo
					.gx_card_type == '3' || this.vehicleInfo.gx_card_type == '5') {
					return true
				}
			}
		},
		onLoad(options) {
			console.log('options=================', options)
			//新增
			if (options.id) {
				this.applyId = options.id
			}
			//修改
			if (options.applyId) {
				this.applyId = options.applyId
				this.formData.operateType = 2
				//回填数据
				this.setData()
			}
			this.initData()
			console.log('vehicleInfo', this.vehicleInfo)

			if (options.accountId) {
				//关联退款账户的集装箱ID
				this.applyId = options.accountId
			}
			//退款账号关联
			if (options.accountItem) {
				//回填数据
				const item = JSON.parse(decodeURIComponent(options.accountItem))
				this.setAccountData(item)
			}
		},
		methods: {
			getAccount() {
				this.$store.dispatch('setRefundList', this.imgList)
				uni.redirectTo({
					url: '/pagesB/disputeRefund/accountList?type=containerRefund&applyId=' + this.applyId
				})
			},
			showTipsTwo() {
				console.log('this.hasRefund', this.hasRefund)
				if ((this.vehicleInfo.gx_card_type == '4' && this.hasRefund) || this.vehicleInfo.gx_card_type == '10') {
					return true
				}
			},
			initData() {
				if (this.vehicleInfo.gx_card_type == '4') {
					//如果是后付费卡，接口判断是否支持原路退款
					this.getRefundFlag()
				}

				this.formData.custMastId = this.vehicleInfo.customer_id
				this.formData.cardNo = this.vehicleInfo.cpu_card_id
				this.formData.carNo = this.vehicleInfo.vehicle_code
				this.formData.carColor = this.vehicleInfo.vehicle_color
			},
			getRefundFlag() {
				this.isLoading = true;
				let params = {
					cardNo: this.vehicleInfo.cpu_card_id
				}
				console.log('params', params)
				this.$request.post(this.$interfaces.queryRefundConfig, {
						data: params
					})
					.then(res => {
						this.isLoading = false;
						if (res.code == 200) {
							// console.log('res', res)
							//1支持原路退款，其他不支持
							if (res.data.refundChannel == 1) {
								this.hasRefund = true

							} else {
								this.hasRefund = false
								this.getAccountList()
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg
							})
						}
					}).catch(err => {
						this.isLoading = false;
					})
			},
			monitorInput() {
				let len = this.formData.remark.length
				this.num = len
			},
			setData() {
				this.getApplyDetail()
				// this.getRecordDetail()
			},
			setAccountData(item) {
				console.log('item====>>>>>', item)
				this.formData.bankCardNo = item.bankNoStr
				this.formData.bankCustName = item.bankAccount
				this.formData.bankName = item.bankName
				this.imgList = this.refundList
				// this.imgList.push(item.attorneyUrl)
			},
			getAccountList() {
				this.isLoading = true
				let params = {
					custMastId: this.vehicleInfo.customer_id,
					carNo: this.vehicleInfo.vehicle_code,
					carNoColor: this.vehicleInfo.vehicle_color
				}
				this.$request
					.post(this.$interfaces.defaultBankQuery, {
						data: params
					})
					.then((res) => {
						console.log(res, 'userList');
						this.isLoading = false
						if (res.code == 200) {
							this.accountList = res.data
							let result = res.data
							if (Object.keys(res.data).length > 0 && this.formData.operateType == 1) {
								//车辆有银行账户信息，提示用户是否回填
								uni.showModal({
									title: "提示",
									content: '车辆【' + this.vehicleInfo.vehicle_code + '】已绑定卡号为【' + result
										.bankNo +
										'】的银行账户，是否选择使用?',
									confirmText: '选择使用',
									cancelText: '不需要',
									success: (res) => {
										if (res.confirm) {
											//回填信息
											this.formData.bankCardNo = result.bankNoStr
											this.formData.bankCustName = result.bankAccount
											this.formData.bankName = result.bankName
											this.formData.containerId = result.id

											this.otherBankName = result.bankName
											this.picker_index = '8'
											for (let i = 0; i < this.disputeRefundBankName.length; i++) {
												if (this.disputeRefundBankName[i].label == result
													.bankName) {
													this.formData.bankName = this.disputeRefundBankName[i]
														.label
													this.picker_index = i
													this.otherBankName = ''
												}
											}
										}
									}
								});
							}
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg
						})
					})
			},
			getApplyDetail() {
				console.log('获取详情')
				this.isLoading = true;
				let params = {
					applyId: this.applyId
				}
				let data = {
					'data': params,
				}
				console.log('入参', data)
				this.$request.post(this.$interfaces.containerRecordDetail, data)
					.then(res => {
						this.isLoading = false;
						if (res.code == 200) {
							this.changeData(res.data)
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg
							});
							return
						}
					}).catch(err => {
						this.isLoading = false;
					})
			},
			changeData(result) {
				if (this.vehicleInfo.gx_card_type == '4' && !this.hasRefund) {
					console.log('detaildetaildetail', result)
					this.formData.bankCardNo = result.bankCardNo
					this.formData.bankCustName = result.bankCustName
					// this.formData.bankName = res.data.bankName
					//银行信息
					this.otherBankName = result.bankName
					this.picker_index = '8'
					for (let i = 0; i < this.disputeRefundBankName.length; i++) {
						if (this.disputeRefundBankName[i].label == result.bankName) {
							this.formData.bankName = this.disputeRefundBankName[i].label
							this.picker_index = i
							this.otherBankName = ''
						}
					}
				}
				console.log('this.picker_index', this.picker_index)
				for (let j = 1; j < 6; j++) {
					let urlStr = 'imgData' + j
					let codeStr = 'code' + j
							// console.log('res.data[imgData + i]', res.data[imgData + i])
					if (result[urlStr]) {
								this.imgList.push({
							url: result[urlStr],
							code: result[codeStr]
								})
							}
						}

				this.formData.containerId = result.contactDto.id
				this.formData.attorneyUrl = result.contactDto.attorneyUrl
				this.formData.remark = result.remark
						console.log('this.imgList', this.imgList)
			},
			changeInput(event) {
				//对银行卡号做空格处理
				let value = event.detail.value;
				console.log('value-------------', value)
				// if (/\S{5}/.test(value)) {
				this.formData['bankCardNo'] = value.replace(/\s/g, '').replace(/(.{4})/g, "$1 ")
				// }
				console.log('bankCardNo', this.formData['bankCardNo'])
			},
			validate() {
				if (this.imgList.length == 0) {
					uni.showModal({
						title: '提示',
						content: '请先上传证明材料'
					});
					return false
				}
				if (this.vehicleInfo.gx_card_type == '4' && !this.hasRefund) {
				//先处理银行卡号空格。
				let cardStr = this.formData.bankCardNo
				cardStr = cardStr.replace(/\s*/g, "");
				let reg = new RegExp("^[0-9]*$");
					if (!this.formData.bankCustName) {

						uni.showModal({
							title: '提示',
							content: '请先输入户名'
						});
					return false
					}
					if (!this.formData.bankCardNo) {
						uni.showModal({
							title: '提示',
							content: '请先输入银行卡号'
						});
					return false
					}
					if (!reg.test(cardStr)) {
						uni.showModal({
							title: '提示',
							content: '请输入正确格式的银行卡号'
						});
					return false
					}
					if (this.picker_index == null) {
						uni.showModal({
							title: '提示',
							content: '请先输入开户行'
						});
					return false
				}
					if (this.picker_index == '8' && !this.otherBankName) {
						uni.showModal({
							title: '提示',
							content: '请先输入开户行'
						});
						return false
					}
					if (this.formData.bankCustName && this.vehicleInfo.customer_name != this.formData.bankCustName && !
						this.formData.attorneyUrl) {
						uni.showModal({
							title: '提示',
							content: '收款账户名与ETC账户名不一致，需提供ETC开户人的授权材料'
						});
						return false
					}
				}
				return true
			},
			submit() {
				console.log(this.validate())
				if (this.validate()) {
					uni.showModal({
						title: '提交',
						content: '确定提交退费申请审核吗？',
						cancelText: '取消',
						confirmText: '确定',
						success: res => {
							if (res.confirm) {
								this.sendApply()
							}
						}
					})
				}
			},
			sendApply() {
				this.isLoading = true
				console.log('imagList', this.imgList)
				let imgListData = {
					imgData1: '',
					imgData2: '',
					imgData3: '',
					imgData4: '',
					imgData5: ''
				}
				let codeData = {
					code1: '',
					code2: '',
					code3: '',
					code4: '',
					code5: ''
				}
				this.imgList.forEach((item, index) => {
					imgListData['imgData' + (index + 1)] = item.url
					codeData['code' + (index + 1)] = item.code
				})
				if (this.formData.bankCardNo) {
				this.formData.bankCardNo = this.formData.bankCardNo.replace(/\s*/g, "");
				}

				let params = {
					applyId: this.applyId,
					...this.formData,
					...imgListData,
					...codeData
				}

				if (this.picker_index == '8') {
					params.bankName = this.otherBankName
				}

				if (this.vehicleInfo.gx_card_type != '4') {
					delete params.attorneyUrl
					delete params.attorneyUrlCode
					delete params.containerId
				}

				let data = {
					'data': params
				}
				console.log('params', JSON.stringify(params))
				this.$request.post(this.$interfaces.commitRefundForm, data).then(res => {
					this.isLoading = false;
					console.log('res', res)
					if (res.code == 200) {
						this.$store.dispatch('setRefundList', [])
						//提交申请成功
						uni.redirectTo({
							url: 'apply-list'
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg
						});
						return
					}
				}).catch(err => {
					this.isLoading = false;
				})
			},
			// pickerChange(e) {
			// 	console.log('tasdas', e.target)
			// 	this.formData.bankName = this.bankList[e.target.value]
			// },
			bindPayTypeChange(e) {
				this.picker_index = e.detail.value;
				this.formData.bankName = this.disputeRefundBankName[e.detail.value].label || '';
				if (this.picker_index == '8') {
					this.otherBankName = ''
				}
			},
			////图片压缩成功
			cpimgOk(file) {
				console.log('file', file)
				this.uploadImg(file)
			},
			cpimgOk(file) {
				console.log(file);
				if (this.isOcr) {
					// this.uploadImg(file, 'ocr')
					this.sendOCR(file);
					return
				}
				this.uploadImg(file)
			},
			uploadImg(file) {
				this.isLoading = true
				let base64Img = file.toString()

				if (this.imgList.length > 0) {
					//校验图片是否相同
					let filter = this.imgList.filter(item => {
						return item.base64Img == base64Img
					})
					if (filter.length > 0) {
						uni.showModal({
							title: '提示',
							content: '该图片已上传过，请勿上传相同图片。'
						})
						this.isLoading = false
						return
					}
				}

				let biz_content = {
					customer_id: getEtcAccountInfo().custMastId,
					// customer_id: '3589062',
					scene: 10,
					photo_code: '100',
					file_name: 'file_name',
				}
				// console.log(biz_content, '********');
				let params = {
					file_content: file.toString(),
					method_code: '2',
					biz_content: JSON.stringify(biz_content)
				};
				this.$request.post(this.$interfaces.refundUpload, {
					data: params
				}).then(res => {
					this.isLoading = false;
					console.log('图片返回res', res)
					if (res.code == 200) {
						if (this.imgType == 'origin') {
						this.imgList.push({
							url: res.data.file_url,
							code: res.data.code,
							base64Img: base64Img
						})
						} else {
							this.formData.attorneyUrl = res.data.file_url
							this.formData.attorneyUrlCode = res.data.code
						}

						// this.imgList.push(res.data.file_url)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg
						})
					}

				}).catch(err => {
					this.isLoading = false;
				})
			},
			ChooseImage(type) {
				this.isOcr = false
				this.imgType = type
				let sourceType = 0
				this.$refs.cpimg._changImg(sourceType);
			},
			ocrBankImg() {
				this.isOcr = true
				let sourceType = 0
				this.$refs.cpimg._changImg(sourceType);
			},
			// ocr识别
			sendOCR(file) {
				let base64Img = file.toString()
				let ocr_type = '';
				let current = {};
				var imgStr = base64Img.split(';')[1].split(",")[1] + '';
				let biz_content = {
					ocr_type: 1,
					file_name: 'file_name',
				}
				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};

				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log('res', res)
						this.isLoading = false;
						let encryptedData = res.data.encryptedData;
						if (encryptedData.bankNo) {
							// let trimbankCardNo = encryptedData.bankNo.replace(/\s*/g, "")
							this.formData.bankCardNo = encryptedData.bankNo
						}
						if (encryptedData.bankName) {
							for (let i = 0; i < this.disputeRefundBankName.length; i++) {
								if (this.disputeRefundBankName[i].label == encryptedData.bankName) {
									this.picker_index = i
									this.otherBankName = ''
									this.formData.bankName = this.disputeRefundBankName[this.picker_index].label ||
										''
									return
								}
							}
							this.picker_index = '8'
							this.otherBankName = encryptedData.bankName
						}
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e);
			},
			ViewImage(e) {
				let viewArr = []
				this.imgList.forEach(item => {
					viewArr.push(item.url)
				})
				uni.previewImage({
					urls: viewArr,
					current: e.currentTarget.dataset.url
				});
			},
			ViewOtherImage(e) {
				let viewArr = []
				viewArr.push(this.formData.attorneyUrl)
				uni.previewImage({
					urls: viewArr,
				});
			},
			DelImg(e) {
				uni.showModal({
					title: '删除照片',
					content: '请确定删除该图片？',
					cancelText: '取消',
					confirmText: '确定',
					success: res => {
						if (res.confirm) {
							this.imgList.splice(e.currentTarget.dataset.index, 1);
						}
					}
				})
			},
			DelOtherImg(e) {
				uni.showModal({
					title: '删除照片',
					content: '请确定删除该图片？',
					cancelText: '取消',
					confirmText: '确定',
					success: res => {
						if (res.confirm) {
							this.formData.attorneyUrl = ''
							this.formData.attorneyUrlCode = ''
		}
	}
				})
			},
			// webview跳转
			authorizationFile(val) {
				let previewFile = 'https://portal.gxetc.com.cn/public-static/file/通行费退费委托书样例.png';
				if (val == 'preview') {
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(previewFile)
					});
					return
				}
				if (val == 'downLoad') {
					this.downLoadFile()
					return
				}
			},
			downLoadFile() {
				let url = 'https://portal.gxetc.com.cn/public-static/file/通行费退费委托书模板.doc'
				let fileExt = url.substring(url.lastIndexOf('.') + 1).toLowerCase()
				let fileName = '授权书模板.docx'
				wx.downloadFile({
					url: url,
					filePath: wx.env.USER_DATA_PATH + '/' + fileName,
					success: res => {
						let filePath = res.filePath
						wx.openDocument({
							filePath: filePath,
							fileType: fileExt,
							showMenu: true,
							success: res => {
								console.log('文档打开成功');
							}
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.my-flex-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.form_label-require {
		&:before {
			content: "*";
			font-weight: bold;
			color: red;
			position: relative;
			right: 6rpx;
			// top: -2rpx;
		}
	}

	.tips-container {
		padding: 20rpx 0;
		background-color: #FFFFFF;

		.tips {
			line-height: 40rpx;
			font-size: 24rpx;
			color: #0066E9;
		}
	}

	.textarea {
		line-height: 44rpx;
		padding-left: 20rpx;
	}

	.refund-apply {
		.cu-form-group {
			justify-content: space-between;

			.c-title {
				height: 40rpx;
				padding-left: 14rpx;
				border-left: 8rpx solid #0066E9;
				color: rgba(51, 51, 51, 100);
				font-size: 30rpx;
				font-family: PingFangSC-bold;
			}

			.account {
				padding: 12rpx 16rpx;
				border: 1rpx solid #E4EFFF;
				background: #E4EFFF;
				border-radius: 6rpx;
				color: #0066E9;
			}

			.title {
				font-weight: bold;
				width: 160rpx;
			}

			.btn-wrapper {
				position: relative;
				display: flex;
				align-items: center;
				flex: 1;

				&>button {
					background-color: #0066E9;
					color: #FFFFFF;
					height: 55rpx;
					font-size: 24rpx;
					line-height: 55rpx;
					text-align: center;
				}
			}

			.input {
				// width: 100%;
				height: 64rpx;
				text-align: right;
				border: 2rpx solid #DDDDDD;
				border-radius: 8rpx;
			}

			.picker-wrapper {
				flex: 1;
				height: 64rpx;
				line-height: 64rpx;
				text-align: right;
				border: 2rpx solid #DDDDDD;
				border-radius: 8rpx;

				.picker {
					height: 64rpx;
					line-height: 64rpx;
					padding-right: 10rpx;
					font-size: 24rpx;
		}
			}
		}

		/deep/.cu-form-group picker::after {
			line-height: 64rpx;
			right: -8rpx;
		}

		.upload-container {
			// margin-bottom: 20rpx;
			padding: 0 30rpx;
			background-color: #FFFFFF;

			// .upload {
			// 	display: flex;
			// 	justify-content: center;
			// 	align-items: center;
			// 	position: relative;
			// 	width: 336rpx;
			// 	height: 222rpx;
			// 	border: 2rpx dashed #0066E9;
			// 	// padding: 10rpx;
			// 	border-radius: 10rpx;
			// 	text-align: center;

			// 	.image-upload {
			// 		width: 336rpx;
			// 		height: 222rpx;
			// 	}

			// 	.upload-text {
			// 		position: absolute;
			// 		bottom: 10rpx;
			// 		left: 0;
			// 		right: 0;
			// 		margin: 0 auto;
			// 		text-align: center;
			// 	}
			// }

				}

			.image-box {
				display: flex;
				flex-wrap: wrap;
				box-sizing: border-box;
		}

				.view-image {
					flex: 1;
			padding: 10rpx 0;

					&:nth-child(2n-1) {
						justify-content: flex-start;
					}

					&:nth-child(2n) {
						justify-content: flex-end;
					}

					.border-container {
						position: relative;
						border: 2rpx dashed #0066E9;
						padding: 20rpx;
						border-radius: 10rpx;

						.view-img {
					width: 287rpx;
							height: 172rpx;
							border-radius: 10rpx;
						}

						.my-tag {
							width: 40rpx;
							height: 40rpx;
							position: absolute;
							right: -20rpx;
							top: -20rpx;
							border-radius: 50%;
						}
					}
				}

		.view-container {
			padding: 0 30rpx;
			background-color: #FFFFFF;

			}

		.bottom-wrapper {
			// margin-top: 30rpx;

			// /deep/t-button {
			// 	button {
			// 		background-color: #3E62E8 !important;
			// 	}
			// }
		}

		.textarea-wrapper {
			position: relative;
			padding: 0 30rpx;
			// border-bottom: 1rpx solid #e8e8e8;
			padding-bottom: 20rpx;
			background: #ffffff;

			.limit {
				position: absolute;
				right: 30rpx;
				bottom: 22rpx;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #B9B9B9;
	}

			&>textarea {
				padding: 20rpx;
				width: 100%;
				border-radius: 4rpx;
				height: 202rpx;
				background-color: #f8f8f8;
				// border: 1rpx solid #BBBBBB;
			}
		}
	}

	.operator-btn {
		// position: absolute;
		// right: 5%;
		// bottom: 75%;

		.fileHandle {
			font-size: 24rpx;
			color: #0066E9;
			margin: 0 8rpx;
		}
	}
</style>