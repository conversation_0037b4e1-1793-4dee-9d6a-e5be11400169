<template>
	<view class="order-list">
		<view class="weui-form-preview">
			<view class="weui-form-preview__hd">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label" style="font-size: 14px;font-weight: 600;">
						{{vehicleInfo.vehicle_code+'[' +plateColorToColorMap.get(vehicleInfo.vehicle_color + '') +']'}}
					</view>
					<view class="weui-form-preview__value" style="color: #0066E9;">金额：{{moneyFilter(info.fee)}}元</view>
				</view>
			</view>
			<view class="weui-form-preview__bd">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">入口站点：</view>
					<view class="weui-form-preview__value">{{info.enStation?info.enStation:''}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">入口时间：</view>
					<view class="weui-form-preview__value">{{info.enTime?info.enTime:''}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">出口站点：</view>
					<view class="weui-form-preview__value">{{info.exStation?info.exStation:''}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">出口时间</view>
					<view class="weui-form-preview__value">{{info.exTime?info.exTime:""}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">申请状态：</view>
					<view class="weui-form-preview__value"
						:class="{'red-value':info.handleMode == 3 || info.handleMode == 7,'blue-value':info.handleMode == 1,'green-value':info.handleMode == 2}">
						{{getType(info.handleMode,handleModeList)}}
					</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">申请时间</view>
					<view class="weui-form-preview__value">{{info.applyTime}}</view>
				</view>
			</view>
			<view class="weui-form-preview__ft">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label"></view>
					<view class="weui-form-preview__value">
						<view class="btn-item">
							<button v-if="info.handleMode != 0" class="weui-btn_mini weui-btn_primary"
								@click="toDetail">查看详情</button>
						</view>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isShowLoding" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getPayStatus
	} from '@/common/method/filter'
	import {
		handleModeList
	} from '@/common/const/optionData.js'
	import {
		plateColorToColorMap
	} from '@/common/systemConstant.js'
	import {
		getCurrUserInfo,
		getCurrentCar
	} from "@/common/storageUtil.js";
	export default {
		props: {
			info: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {
				plateColorToColorMap,
				handleModeList,
				isShowLoding: false,
				schoolDetail: {

				},
			}
		},
		watch: {},
		components: {
			tLoading
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},


		},
		created() {
			console.log('this.info', this.info)
		},
		methods: {
			getPayStatus,
			toDetail() {
				this.$emit('click', 'detail')
			},
			getType(value, objList) {
				for (let i = 0; i < objList.length; i++) {
					if (objList[i].value == value) {
						return objList[i].label
					}
				}
				return ''
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>
<style lang="scss" scoped>
	.order-list {
		margin-bottom: 20rpx;
	}

	.blue-value {
		color: #007AFF;
	}

	.red-value {
		color: red;
	}

	.green-value {
		color: #0066E9;
	}
</style>
