<template>
	<view class="sellPay-container">
		<view class="fixed-top">
			<view class="fixed-top">
				<view class="weui-form">
					<view class="weui-cells__title">
						申请记录查询
					</view>
					<view class="weui-cells">
						<view class="vux-x-input weui-cell weui-cell_picker">
							<view class="weui-cell__hd">
								<view class="weui-label">申请开始日期</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<picker mode="date" style="width:100%;" v-model="start_date" @change="startDateChange">
									<view class="weui-picker-value">
										{{start_date || '请选择'}}
									</view>
								</picker>
							</view>
							<view class="weui-cell__ft">
							</view>
						</view>


						<view class="vux-x-input weui-cell weui-cell_picker">
							<view class="weui-cell__hd">
								<view class="weui-label">申请结束日期</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<picker mode="date" style="width:100%;" v-model="end_date" @change="endDateChange">
									<view class="weui-picker-value">
										{{end_date || '请选择'}}
									</view>
								</picker>
							</view>
							<view class="weui-cell__ft">
							</view>
						</view>

						<view class="vux-x-input weui-cell weui-cell_picker">
							<view class="weui-cell__hd">
								<view class="weui-label">申请状态</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<picker :model="formData.handleMode" range-key="label" :value="index"
									:range="handleModeList" @change="pickerChange">
									<view class="weui-picker-value">
										{{handleModeList[index].label}}
									</view>
								</picker>
							</view>
							<view class="weui-cell__ft">
							</view>
						</view>

					</view>
				</view>
				<view class="search-btn">
					<button class="weui-btn weui-btn_primary " @click="onSearchHandle()">
						查询
					</button>
					<button v-if="formData.applyStartTime || formData.applyEndTime || formData.handleMode"
						class="weui-btn weui-btn_primary " @click="reset()">
						重置
					</button>
				</view>
			</view>
		</view>
		<view class="scroll-box">
			<item @click="toDetail(item.applyId)" v-for="(item,index) in carRecord" :key="index" :info="item">
			</item>
			<load-more :loadStatus="noticeLoadStatus" />
		</view>

		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import item from './apply-item.vue'
	import loadMore from '../../components/load-more/index.vue';
	import float from '@/common/method/float.js'
	import {
		handleModeList
	} from '@/common/const/optionData.js'
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		components: {
			TButton,
			item,
			tLoading,
			loadMore
		},
		data() {
			return {
				handleModeList,
				isLoading: false,
				index: 0,
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				cardAmount: {},
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				carRecord: [],
				start_date: '',
				end_date: '',
				sumMoney: 0,
				formData: {
					applyId: '',
					handleMode: '',
					notEqHandleMode: '0',
					// "customer_id": "",
					// "cpu_card_id": "",
					// "vehicle_color": "",
					// "vehicle_code": "",
					// "pay_start_date": "",
					// "pay_end_date": ""
					// carColor: '0',
					// carNo: '浙A7389W',
					applyStartTime: '',
					applyEndTime: '',
					pageNo: 1,
					pageSize: 10
				}
			};
		},
		onLoad(options) {

		},

		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},
		},
		watch: {
			start_date(val) {
				if (val != '') {
					this.formData.applyStartTime = this.formatHandle(new Date(val).getTime(), "yyyy-MM-dd") +
						' 00:00:00'
				} else {
					this.formData.applyStartTime = ''
				}
				console.log('valval', this.formData.applyStartTime)
			},
			end_date(val) {
				if (val != '') {
					this.formData.applyEndTime = this.formatHandle(new Date(val).getTime(), "yyyy-MM-dd") +
						' 23:59:59'
				} else {
					this.formData.applyEndTime = ''
				}
				console.log('valval', this.formData.applyEndTime)
			},
		},
		//加载更多
		onReachBottom() {
			this.formData.pageNo += 1;
			this.getRecord('add');
		},
		onLoad() {
			console.log('vehicleInfo', this.vehicleInfo)
			this.formData.carColor = this.vehicleInfo.vehicle_color
			this.formData.carNo = this.vehicleInfo.vehicle_code
			// this.start_date = this.formatHandle(new Date().getTime(), "yyyy-MM-dd")
			// this.end_date = this.formatHandle(new Date().getTime(), "yyyy-MM-dd")
			this.getRecord()
		},
		methods: {
			toDetail(applyId) {
				uni.navigateTo({
					url: './apply-detail?applyId=' + applyId
				})
				// this.formData.applyId = applyId
				// this.getRecord()
			},
			pickerChange(e) {
				console.log('tasdas', e.target)
				this.index = e.target.value
				this.formData.handleMode = this.handleModeList[e.target.value].value
			},
			foramtDate(time, fmt) {
				time = time.replace('.0', '');
				let value = time && time.replace(/-/g, '/');
				let getDate = new Date(value);
				let o = {
					'M+': getDate.getMonth() + 1,
					'd+': getDate.getDate(),
					'h+': getDate.getHours(),
					'm+': getDate.getMinutes(),
					's+': getDate.getSeconds(),
					'q+': Math.floor((getDate.getMonth() + 3) / 3),
					'S': getDate.getMilliseconds()
				};
				if (/(y+)/.test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))
				}
				for (let k in o) {
					if (new RegExp('(' + k + ')').test(fmt)) {
						fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k])
							.length)))
					}
				}
				return fmt;
			},
			formatHandle(time, format) {
				var t = new Date(time);
				var tf = function(i) {
					return (i < 10 ? '0' : '') + i
				};
				console.log(t, 'formatHandle')
				return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
					switch (a) {
						case 'yyyy':
							return tf(t.getFullYear());
							break;
						case 'MM':
							return tf(t.getMonth() + 1);
							break;
						case 'mm':
							return tf(t.getMinutes());
							break;
						case 'dd':
							return tf(t.getDate());
							break;
						case 'HH':
							return tf(t.getHours());
							break;
						case 'ss':
							return tf(t.getSeconds());
							break;
					}
				})
			},
			onSearchHandle() {
				this.getRecord();
			},
			reset() {
				this.start_date = ''
				this.end_date = ''
				this.index = 0
				this.formData.handleMode = ''
			},
			startDateChange(e) {
				this.start_date = e.detail.value
			},
			endDateChange(e) {
				this.end_date = e.detail.value
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {


			},
			scroll: function(e) {

				this.old.scrollTop = e.detail.scrollTop;

			},
			getRecord(type) {
				//没有更多直接返回
				if (type === 'add') {
					if (this.noticeLoadStatus == 3) {
						return;
					}
				} else {
					this.formData.pageNo = 1;
				}
				//加载中
				this.noticeLoadStatus = 1;
				if (this.formData.pageNo == 1) {
					this.isLoading = true
				}
				let params = JSON.parse(JSON.stringify(this.formData))
				let data = {

					'data': params
				}
				console.log('入参', params)
				this.noticeLoadStatus = 1;
				this.$request.post(this.$interfaces.containerRefundRecord, data).then(res => {
					console.log('通行记录', res);
					this.isLoading = false;
					if (res.code == 200) {
						let resData = res.data.data

						if (this.formData.pageNo == 1) {
							this.carRecord = resData;
							if (resData.length < 1) {
								this.noticeLoadStatus = 0
								return;
							}
						} else {
							this.carRecord = this.carRecord.concat(resData);
						}

						if (resData.length < 1 || resData.length < this.formData.pageSize) {
							//没数据了
							this.noticeLoadStatus = 3
							return
						}

						// 	// this.carRecord.map(item => {
						// 	// 	return this.sumMoney = float.add(item.fee, this.sumMoney)
						// 	// })
						// 	this.noticeLoadStatus = 3;
						// } else {
						// 	this.noticeLoadStatus = 0;
						// }
					} else if (res.code == 401) {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.reLaunch({
										url: '/pagesD/login/p-login'
									});
								}
							}
						});
						this.isLoading = false;

					} else {
						this.noticeLoadStatus = 2;
						this.isLoading = false;
					}

				}).catch(() => {
					this.noticeLoadStatus = 2;
					this.isLoading = false;
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
		destroyed() {

		}
	};
</script>
<style lang="scss" scoped>
	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.search-btn {
		display: flex;
		align-items: center;
		margin: 20rpx 30rpx 20rpx 30rpx;
	}

	.weui-btn {
		flex: 1;
		margin-top: 0;
		margin-right: 20rpx;
	}

	.weui-btn:last-child {
		margin-right: 0;
	}

	.fixed-top {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		// bottom: 0;
		overflow: hidden;
		z-index: 10;
		background-color: #F3F3F3;
	}

	.scroll-box {
		padding-top: 528rpx;
	}

	.apply-record {
		width: 150rpx;
		margin-left: 10rpx;
		color: #0066E9;
	}
</style>
