<template>
	<view class="record-detail">
		<view class="detail">
			<view class="cu-form-group">
				<view class="title">ETC卡号：</view>
				<view class="desc">{{detail.cardNo}}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">车牌号：</view>
				<!-- <view class="desc">{{detail.carNo}}</view> -->
				<CarCard class="home-card" :item='item' :plateNum="detail.carNo" :plateColor="detail.carColor +''"
					:registeredType='1'></CarCard>
			</view>
			<view class="cu-form-group">
				<view class="title">入站口：</view>
				<view class="desc">{{detail.enStation}}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">入口时间：</view>
				<view class="desc">{{detail.enTime}}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">出口站：</view>
				<view class="desc">{{detail.exStation}}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">出口时间：</view>
				<view class="desc">{{detail.exTime}}</view>
			</view>
			<view class="cu-form-group">
				<view class="title">收费金额：</view>
				<!-- <input class="input" :value="detail.feeStr + '元'" disabled></input> -->
				<view class="desc"> {{moneyFilter(detail.fee)}}元</view>
			</view>
		</view>
		<view class="btn-wrapper">
			<button v-if="detail.handleMode == '0'" @tap="toRefundApply()" type="default">申请退费</button>
			<button v-if="detail.handleMode != '0'" @tap="toApplyDetail()" type="default">申请详情</button>
		</view>
		<tLoading :isShow="isShowLoding" />
	</view>
</template>

<script>
	// import CarCard from "@/components/car-card-4501/car-card.vue";
	import CarCard from "@/pagesB/components/car-card-4501/car-card.vue";
	import tLoading from '@/components/common/t-loading.vue';
	export default {
		components: {
			CarCard,
			tLoading
		},
		data() {
			return {
				isShowLoding: false,
				applyId: '',
				detail: {
					// id: '1401',
					// cardNo: '****************',
					// carNo: '桂A3203E',
					// carNoColor: '1',
					// enStation: '南宁东站',
					// exStation: '南宁南站',
					// enTime: '2021-10-26 23:22:33',
					// exTime: '2021-10-26 23:22:33',
					// feeStr: '659.65',
					// handleMode: '0'
				}
			}
		},
		onLoad(options) {
			console.log(options)
			this.applyId = options.applyId
			this.getRecordDetail(options.applyId)
		},
		methods: {
			getRecordDetail() {
				this.isShowLoding = true;
				let data = {
					'data': {
						applyId: this.applyId
					}
				}
				console.log('入参', data)
				this.$request.post(this.$interfaces.containerRecordDetail, data).then(res => {
					this.isShowLoding = false;
					console.log('res', res)
					this.detail = res.data
				}).catch(err => {
					this.isShowLoding = false;
				})
			},
			toRefundApply() {
				uni.navigateTo({
					url: '../car-apply/refund-apply?id=' + this.applyId
				})
			},
			toApplyDetail() {
				uni.navigateTo({
					url: '../car-apply/apply-detail?applyId=' +
						this.applyId
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}

	}
</script>

<style lang="scss" scoped>
	.record-detail {
		.detail {
			margin: 30rpx;
			border: 1rpx solid #e9e9e9;
			border-radius: 10rpx;
			background-color: var(--white);

			.cu-form-group {
				justify-content: flex-start;
				background-color: transparent;

				.title {
					flex: 0 0 160rpx;
					width: 160rpx;
					height: 60rpx;
					line-height: 60rpx;
					color: #999999;
					font-size: 28rpx;
					font-family: PingFangSC-bold;
				}

				.desc {
					flex: 1;
					color: #333333;
					line-height: 28rpx;
				}

				/deep/.index-car-card {
					background: transparent;
					height: 0;
					border-radius: 0;
					margin: 0;

					.son-card {
						width: 100%;
						height: 75rpx;
						padding: 10rpx 20rpx;
						border-radius: 10rpx;
						margin-bottom: 0;
						font-size: 40rpx;
					}
				}
			}
		}

		.btn-wrapper {
			display: flex;
			align-items: center;
			padding: 0 30rpx;

			&>button {
				flex: 1;
				background-color: #0066E9;
				color: #FFFFFF;
				height: 98rpx;
				line-height: 98rpx;
				font-size: 32rpx;
				text-align: center;
				border: 0;

				&:first-child {
					margin-right: 20rpx;
				}
			}

			.btn-disabled {
				color: #333333;
				cursor: disabled;
			}
		}
	}
</style>
