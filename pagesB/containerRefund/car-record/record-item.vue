<template>
	<view class="order-list">
		<view class="weui-form-preview">
			<view class="weui-form-preview__hd">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label" style="font-size: 14px;font-weight: 600;">
						<!-- 						<CarCard class="home-card" :item='item' :plateNum="vehicleInfo.vehicle_code"
							:plateColor="vehicleInfo.vehicle_color +''" :registeredType='1'></CarCard> -->
						{{vehicleInfo.vehicle_code+'[' +plateColorToColorMap.get(vehicleInfo.vehicle_color + '') +']'}}
					</view>
					<view class="weui-form-preview__value" style="color: #0066E9;">金额：{{moneyFilter(info.fee)}}元</view>
				</view>
			</view>
			<view class="weui-form-preview__bd">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">入口站点：</view>
					<view class="weui-form-preview__value">{{info.enStation?info.enStation:''}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">入口时间：</view>
					<view class="weui-form-preview__value">{{info.enTime?info.enTime:''}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">出口站点：</view>
					<view class="weui-form-preview__value">{{info.exStation?info.exStation:''}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">出口时间：</view>
					<view class="weui-form-preview__value">{{info.exTime?info.exTime:""}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">是否减半：</view>
					<view class="weui-form-preview__value">{{info.halfFlag == '1'?'已减半':"未减半"}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">退费状态：</view>
					<view class="weui-form-preview__value">{{getStatus(info.handleMode)}}</view>
				</view>
			</view>
			<view class="weui-form-preview__ft">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label"></view>
					<view class="weui-form-preview__value">
						<view class="btn-item">
							<button v-if="info.handleMode == 0 && info.halfFlag != '1'"
								class="weui-btn_mini weui-btn_primary" @click="toApply">去申请</button>
							<!-- 							<button v-else class="weui-btn_mini weui-btn_primary"
								@click="this.$emit('click','detail')">查看详情</button> -->
						</view>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isShowLoding" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getPayStatus
	} from '@/common/method/filter'
	import {
		getCurrUserInfo,
		getCurrentCar
	} from "@/common/storageUtil.js";
	import {
		plateColorToColorMap
	} from '@/common/systemConstant.js'
	import {
		handleModeList
	} from '@/common/const/optionData.js'
	export default {
		components: {
			tLoading
		},
		props: {
			info: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {
				handleModeList,
				plateColorToColorMap,
				isShowLoding: false,
				schoolDetail: {

				},
			}
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},


		},
		methods: {
			toApply() {
				this.$emit('click', 'apply')
			},
			getStatus(val) {
				// console.log('val', val)
				if (val == 0) {
					return '待申请'
				}
				for (let i = 0; i < this.handleModeList.length; i++) {
					if (this.handleModeList[i].value == val) {
						return this.handleModeList[i].label
					}
				}
				return '';
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>
<style lang="scss" scoped>
	.order-list {
		margin-bottom: 20rpx;
	}
</style>
