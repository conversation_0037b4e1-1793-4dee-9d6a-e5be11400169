<template>
	<view class="ecss-sync">
		<view class="ecss-sync__bd">
			<view class="weui-form">
				<view class="weui-cells__title">
					账户信息
				</view>
				<view class="weui-cells">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">账户名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="accountInfoData.userName">
								{{accountInfoData.userName}}
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">登陆手机号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="accountInfoData.mobile">
								{{accountInfoData.mobile}}
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">账户类型</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="accountInfoData.accountType">
								{{accountInfoData.accountType==='COMMON_USER'?'个人':'企业'}}
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">可用余额</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="accountInfoData.availableAmount">
								{{moneyFilter(accountInfoData.availableAmount)}} 元
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">账户状态</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="accountInfoData.status">
								{{accountInfoData.status==='UNACTIVE'?'未激活':'已激活'}}
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</view>
			</view>

			<view class="weui-form" v-if="isEcssInfo">
				<view class="weui-cells__title">
					ECSS账户信息
				</view>
				<view class="weui-cells">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">客户名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="ecssInfoData && ecssInfoData.saleCustName">
								{{ecssInfoData.saleCustName}}
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">客户代码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="ecssInfoData && ecssInfoData.saleCustCode">
								{{ecssInfoData.saleCustCode}}
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">ETC客户代码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="ecssInfoData">
								{{ecssInfoData.custMastId}}
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">可用余额</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="ecssInfoData">
								{{moneyFilter(ecssInfoData.availableAmount)}} 元
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</view>
			</view>
			<view class="weui-form" style="padding-bottom: 160rpx;" v-if='isEcssInfo'>
				<view class="weui-cells__title">
					ECSS账户迁移
				</view>
				<view class="weui-cells">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">客户证件号码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input class="weui-input" v-model="formData.idNo" placeholder="请输入客户证件号码"></input>
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">手机号码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input class="weui-input" v-model="formData.mobile" placeholder="请输入手机号码"></input>
						</view>

					</view>
					<view class="vux-x-input weui-cell code-input ">
						<view class="weui-cell__hd">
							<view class="weui-label ">图形验证码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary ">
							<input class="weui-input" v-model="formData.code" placeholder="请输入图形验证码"></input>

						</view>
						<view class="weui-cell__ft g-flex g-flex-align-center">
							<image :src="codeUrl" class="code-img" @click="getCaptcha">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">短信验证码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary g-flex">
							<input class="weui-input" v-model="formData.mobileCode" placeholder="请输入短信验证码"></input>
						</view>
						<view class="weui-cell__ft g-flex g-flex-align-center">
							<text class="sendSMS" @click="sendSMS">{{smsName}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="ecss-sync__end" v-if="isEcssSync">
			<view class="icon g-flex g-flex-center">
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/safe_warn.png" class="img" @click="getCaptcha">
			</view>

			<view class="title">
				该账号已经完成ECSS数据迁移
			</view>
		</view>
		<view class="ecss-sync__footer" v-if="isEcssInfo">

			<TButton title="数据迁移" @clickButton="onSubmitHandle" />
		</view>
		
		
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import {
		getLoginUserInfo,
		setEtcAccount

	} from '@/common/storageUtil.js';
	import {
		checkIdCard,
		checkPhone,
		checkPosttal
	} from "@/common/util.js";
	import {
		getCurrUserInfo,
		setCurrUserInfo,
		getEtcAccountInfo
	} from "@/common/storageUtil.js";
	import {
		getErrorMessage
	} from '@/common/method/filter.js'
	export default {
		components: {
			tLoading,
			TButton
		},
		data() {
			return {
				smsName: "发送验证码",
				isLoading: false,
				accountInfoData: {},
				ecssInfoData: null,
				isEcssInfo: false, // 
				isEcssSync: false, //是否完成迁移
				captchaId: '', //
				codeUrl: '',
				custInfo: "",
				formData: {
					userNo: '', //
					custMastId: '',
					mobileCode: "", // 短信验证码
					mobile: '', // 手机号码
					idNo: '', // 客户证件号
					code: '' // 图形验证码
				},
			};
		},
		computed: {

		},
		created() {
			this.custInfo = getEtcAccountInfo();
			console.log(getLoginUserInfo())
			this.formData.userNo = getLoginUserInfo().userNo || '';
			this.formData.custMastId = this.custInfo.custMastId || '';
			if (getLoginUserInfo() && getLoginUserInfo().userNo) {
				this.getAccountInfo();
			}
			if (this.custInfo && this.custInfo.custMastId) {
				this.getEcssInfo();
			}
			this.getCaptcha();
		},
		methods: {
			// 获取图形验证码
			getCaptcha() {
				let params = {

				}
				this.$request.post(this.$interfaces.getCaptcha, {
					data: params
				}).then(res => {
					if (res.code == 200) {

						this.codeUrl = res.data.image
						this.captchaId = res.data.captchaId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			validatePhone() {
				if (!this.formData.mobile) {
					uni.showModal({
						title: "提示",
						content: "请输入手机号码",
						showCancel: false,
					});
					return
				}
				if (!(/^1\d{10}$/.test(this.formData.mobile))) {
					uni.showModal({
						title: "提示",
						content: "请填写正确的手机号",
						showCancel: false,
					});
					return;
				}
				return true;
			},
			// 发送短信验证码
			sendSMS() {
				if (!this.validatePhone()) return
				if (!this.formData.code) {
					uni.showModal({
						title: "提示",
						content: "请输入图形验证码",
						showCancel: false,
					});
					return;
				}
				if (this.time) return;
				this.isLoading=true
				let countdown = 60
				let params = {
					mobile: this.formData.mobile,
					mobileCode: this.formData.code,
					captchaId: this.captchaId
				};
				this.$request.post(this.$interfaces.sendaAccountSms, {
					data: params
				}).then(res => {
					this.isLoading=false
					console.log(res);
					if (res.code == '200') {
						this.time = setInterval(() => {
							countdown = countdown - 1
							this.smsName = countdown + "秒后重新发送"
							if (countdown === 0) {
								clearInterval(this.time)
								this.time = null
								this.smsName = "重新发送"
							}
						}, 1000)
					} else {
						uni.showModal({
							title: "提示",
							content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res.code + '】',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									this.getCaptcha();
								}
							}
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})

			},
			onSubmitHandle() {
				if (!this.formData.idNo) {
					uni.showModal({
						title: "提示",
						content: "请输入客户证件号码",
						showCancel: false,
					});
					return;
				}
				if (!this.validatePhone()) return
				if (!this.formData.code) {
					uni.showModal({
						title: "提示",
						content: "请输入图形验证码",
						showCancel: false,
					});
					return;
				}
				if (!this.formData.mobileCode) {
					uni.showModal({
						title: "提示",
						content: "请输入短信验证码",
						showCancel: false,
					});
					return;
				}
				this.sendEcssSync();
			},
			// 同步ecss 数据内容
			sendEcssSync() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = this.formData;
				this.$request.post(this.$interfaces.ecssSync, {
					data: params
				}).then(res => {
					this.isLoading = false;
					this.getAccountInfo();
					this.getEcssInfo();
					if (res.code == 200) {
						
						let item=getEtcAccountInfo()
						item.ecssTransfer=true
						setEtcAccount(item)
						uni.showModal({
							title: "提示",
							content: '迁移成功',
							showCancel: false,
						});
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch((error) => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			// 获取ecss信息
			getEcssInfo() {
				let params = {
					custMastId: this.custInfo.custMastId,
					custType: this.custInfo.custType,
				};
				this.isEcssInfo = false;
				this.$request.post(this.$interfaces.getEcssInfo, {
					data: params
				}).then(res => {
					console.log(res,'getEcssInfo');
					if (res.code == 2002) {
						this.isEcssSync = true;
						return
					}
					if (res.code == 200) {
						this.ecssInfoData = res.data;
						this.isEcssInfo = true;
						return
					}
					uni.showModal({
						title: "提示",
						content: res.msg,
						showCancel: false,
					});

				}).catch((error) => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			// 获取互联网账户信息
			getAccountInfo() {
				let params = {
					userNo: getLoginUserInfo().userNo,
				};
				this.$request.post(this.$interfaces.getAccountInfo, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.accountInfoData = Object.assign({}, res.data);

						if (this.accountInfoData.status === "UNACTIVE") {
							 this.isActivition();
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch((error) => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			isActivition() {
				let _self = this;
				uni.showModal({
					title: '提示',
					content: '该账户未激活,是否激活？',
					success: function(res) {
						if (res.confirm) {
							_self.activitionAccount();
						}
					}
				});
			},
			activitionAccount() {
				let params = {
					userNo: getLoginUserInfo().userNo,
				};
				this.$request.post(this.$interfaces.activitionAccount, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						uni.showModal({
							title: "提示",
							content: '激活账户成功',
							showCancel: false,
						});
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
		onLoad(option) {


		}
	};
</script>

<style scoped lang="scss">
	.sendSMS {
		text-align: right;
		color: #1d82d2;
		width: 240rpx;
		margin-left: 10rpx;
	}

	.code-img {
		width: 240upx;
		height: 70upx;
		margin-left: 10rpx;
	}

	.weui-cell.code-input {
		padding-top: 10rpx;
		padding-bottom: 10rpx;
	}

	.ecss-sync {
		position: relative;
	}

	.ecss-sync__end {
		background-color: #FFFFFF;
		padding-top: 56rpx;
		margin-top: 30rpx;
		padding-bottom: 66rpx;
		width: 100%;
	}

	.ecss-sync__end .img {
		width: 160rpx;
		height: 160rpx;
		margin: 0 auto;
	}

	.ecss-sync__end .icon {
		width: 100%;
	}

	.ecss-sync__end .title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		text-align: center;
		width: 100%;
		margin-top: 40rpx;
	}

	.ecss-sync__footer {
		position: fixed;
		left: 0px;
		bottom: 0px;
		height: 144rpx;
		width: 100%;
		padding: 0 40rpx 24rpx 40rpx;
		background-color: #FFFFFF;
		border-top: 1px solid #E9E9E9;
		z-index: 999;
	}
</style>
