<!--
  * @desc:tab选择
  * @author:zhangys
  * @date:2023-02-10 14:47:16
!-->
<template>
	<view style="height: 100%;overflow:hidden" >
		<scroll-view scroll-x class="bg-white nav">
			<view class="flex text-center">
				<view class="cu-item flex-sub" :class="index==TabCur?'text-cyan cyan  cur':''"
					v-for="(item,index) in tabList" :key="index" @tap="tabSelect" :data-id="index">
					{{item}}
				</view>
			</view>
		</scroll-view>
		<unInvoiceList v-if="TabCur==0" />
		<invoiceList v-if="TabCur==1" />
	</view>
</template>

<script>
	import unInvoiceList from './uninvoiceList/index'
	import invoiceList from './invoiceRecord/index'
	export default {
		name: '',
		props: {

		},
		components: {
			unInvoiceList,
			invoiceList,
		},
		data() {
			return {
				TabCur: 0,
				scrollLeft: 0,
				tabList: ['未开票', '已开票'],
				
			}
		},
		onLoad(obj) {
			if(obj.type=='invoiced'){
				this.TabCur=1
			}else{
				this.TabCur=0
			}
		},
		
		computed: {

		},
		watch: {

		},
		created() {

		},
		methods: {
			tabSelect(e) {
				this.TabCur = e.currentTarget.dataset.id;
				this.scrollLeft = (e.currentTarget.dataset.id - 1) * 60
			}
		},
	}
</script>

<style lang='scss' scoped>

</style>
