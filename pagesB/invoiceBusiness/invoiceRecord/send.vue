<template>
	<view class="invoice-details">
		<view class="invoice-details__box">
			<view class="weui-form">
				<view class="weui-cells__title">
					<view class="">
						接收信息
					</view>
				</view>
				<view class="weui-cells">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">电子邮箱</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="email" :value="formData.email" class="weui-input"
								placeholder="请输入电子邮箱"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="invoiceSendHandle">
						提交
					</button>
				</view>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getEtcAccountInfo
	} from '@/common/storageUtil.js'
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				formData: {
					email: '',
					phone: '',
					customerId: '',
					invOrderId: '',
					sendType: '1', // 发送类型 * 1-邮箱 2-短信
				}
			};
		},
		computed: {

		},
		onLoad(option) {
			this.formData.email = option.email || '';
			this.formData.invOrderId = option.invOrderId || '';
		},
		methods: {
			modifyInput(e) {
				this.formData[e.currentTarget.id] = e.detail.value;
			},
			// 发票重发
			invoiceSendHandle() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					...this.formData,
					customerId: getEtcAccountInfo().custMastId || ''
				}
				this.$request
					.post(this.$interfaces.invoiceSend, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '重发成功',
								showCancel: false
							})
							uni.reLaunch({
								url:'/pagesB/invoiceBusiness/index?type='+'invoiced'
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;

						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			}
		}
	};
</script>

<style scoped lang="scss">
	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 0rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color:#0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}

	.invoice-details__box {
		padding-bottom: 160rpx;
	}
</style>
