<template>
	<view class="invoice-details">
		<view class="invoice-details__box">
			<view class="weui-form">
				<view class="weui-cells__title">
					发票详情
				</view>
				<view class="weui-cells">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">申请类型</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="detailData.invOrder.invoiceApplyTypeStr">
								{{detailData.invOrder.invoiceApplyTypeStr}}
							</view>
						</view>
						<view class="weui-cell__ft">

						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">发票类型</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="detailData.invOrder.invoiceTypeStr">
								{{detailData.invOrder.invoiceTypeStr}}
							</view>
						</view>
						<view class="weui-cell__ft">

						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">公司名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="detailData.invOrder.buyerName">
								{{detailData.invOrder.buyerName}}
							</view>
						</view>
						<view class="weui-cell__ft">

						</view>
					</view>
					<view class="vux-x-input weui-cell" v-if="detailData.invOrder.buyerTaxpayerNum">
						<view class="weui-cell__hd">
							<view class="weui-label">公司税号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value">
								{{detailData.invOrder.buyerTaxpayerNum}}
							</view>
						</view>
						<view class="weui-cell__ft">

						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">发票金额</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value invoice-money" v-if='detailData.invOrder'>
								{{moneyFilter(detailData.invOrder.totalFee)}}元
							</view>
						</view>
						<view class="weui-cell__ft">

						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">申请时间</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if='detailData.invOrder'>
								{{format(new Date(detailData.invOrder.createdTime).getTime(), "yyyy-MM-dd hh:mm:ss")}}
							</view>
						</view>
						<view class="weui-cell__ft">

						</view>
					</view>
				</view>
			</view>
			<view class="weui-form">
				<view class="weui-cells__title">
					接收信息
				</view>
				<view class="weui-cells">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">电子邮箱</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value" v-if="detailData.invOrder.email">
								{{detailData.invOrder.email}}
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell "
						:class="[detailData.invOrder.taxStatus=='2'&&detailData.invOrder.invoiceType == '0'? 'weui-cell_picker' : '']">
						<view class="weui-cell__hd">
							<view class="weui-label">开票状态</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary" @click="onCheckInvoiceHandle">
							<view class="weui-cell__value"
								v-if="detailData.invOrder&&detailData.invOrder.invoiceType == '0'">
								{{_taxStatusFilter(detailData.invOrder.taxStatus)}} <text
									v-if="!(detailData.invOrder.invoiceType == '1'||detailData.invOrder.taxStatus!='2')"
									style="font-size: 26rpx;margin-left: 20rpx;">点击预览发票</text>
							</view>

							<view class="weui-cell__value"
								v-if="detailData.invOrder.invoiceType == '1'&&detailData.invOrder.taxStatus=='2'">
								已红冲
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 	<view class="weui-bottom-fixed" v-if="detailData.invOrder.taxStatus=='2'&&detailData.invOrder.invoiceType=='0'">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="goSendHandle">
						重发发票
					</button>
				</view>
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="open">
						发票红冲
					</button>
				</view>

			</view>
		</view> -->
		<tLoading :isShow="isLoading" />
		<uni-popup ref="popup">
			<view class="half-screen-dialog" style="background: #F9F9F9;border-radius: 20px 20px 0px 0px">
				<view class="half-screen-dialog__hd g-flex g-flex-align-center">
					<view class="half-screen-dialog__hd__main ">
						<view class="title">
							发票红冲
						</view>
					</view>
					<view class="half-screen-dialog__hd__side"
						:class="['half-screen-dialog__hd__side--' + closeIconPos]" @click="closePopup('bottom')">
						<text class="cuIcon-close close"></text>
					</view>

				</view>
				<view class="half-screen-dialog__bd" style="margin: 30rpx 0;">

					<view class="weui-form" style="margin: 30rpx;">
						<view class="weui-cells weui-cells-no__title">
							<view class="vux-x-input weui-cell">
								<view class="weui-cell__hd">
									<view class="weui-label">备注说明</view>
								</view>
								<view class="weui-cell__bd weui-cell__primary">
									<input @input="modifyInput" id="remark" :value="remark" class="weui-input"
										placeholder="请输入备注说明"></input>
								</view>
								<view class="weui-cell__ft">
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="half-screen-dialog__ft">
					<view class="bottom-box">
						<view class="btn-item" style="margin-left: 0rpx;">
							<button class="weui-btn weui-btn_primary" @click="onCloseHandle('bottom','confirm')">
								确定
							</button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		taxStatusFilter,
		invoiceApplyTypeFilter
	} from '@/common/method/filter.js';
	import {
		getEtcAccountInfo
	} from '@/common/storageUtil.js'
	import {
		format
	} from '@/common/format-date.js'
	import float from '@/common/method/float.js'
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				closeIconPos: 'top-right',
				invoiceRedParam: {
					remark: ''
				},
				detailData: {
					invOrderId: ''
				}
			};
		},
		computed: {

		},
		onLoad(option) {
			this.detailData.invOrderId = option.invOrderId || '';
			this.detailData.applyType= option.applyType ||''
			this.invoiceQuery();
		},
		onUnload(){
			uni.reLaunch({
				url: '/pagesB/invoiceBusiness/index?type=' + 'invoiced'
			});
		},
		methods: {
			format,
			moneyFilter(money) {
				return float.div(money, 100)
			},
			open(type) {
				this.invoiceRedParam.remark = '';
				this.$refs.popup.open('bottom')
			},
			onCloseHandle(type, action) {

				if (action == 'confirm') {
					this.invoiceRedHandle();
					return;
				}
				this.closePopup('bottom');

			},
			closePopup(type) {
				this.$refs.popup.close(type)
			},
			modifyInput(e) {
				this.invoiceRedParam[e.currentTarget.id] = e.detail.value;
			},
			// 跳转发票重发
			goSendHandle() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/invoiceRecord/send?invOrderId=' + this.detailData.invOrder
						.invOrderId +
						'&email=' + this.detailData.invOrder.email

				})
			},
			// 发票红冲
			invoiceRedHandle() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					invOrderId: this.detailData.invOrder.invOrderId,
					remark: this.invoiceRedParam.remark,
					custMastId: getEtcAccountInfo().custMastId || '',
					invoiceApplyType:	this.detailData.applyType
				}
				this.$request
					.post(this.$interfaces.invoiceRed, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '发票红冲成功',
								showCancel: false
							})
							uni.redirectTo({
								url: '/pagesB/invoiceBusiness/invoiceRecord/index'
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
							this.closePopup('bottom');
						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;
						this.closePopup('bottom');
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			invoiceQuery() {

				let params = {
					invOrderId: this.detailData.invOrderId,

				}
				this.$request
					.post(this.$interfaces.invoiceQuery, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							if(res.data.applyStatus=='1'){
								uni.showModal({
									title: '提示',
									content: '开票中，请稍后再试',
									showCancel: false,
									success: (res) => {
										if (res) {
											uni.reLaunch({
												url: '/pagesB/invoiceBusiness/index?type=' + 'invoiced'
											});
										}
									}
								})
								return
							}
							this.detailData = res.data;
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})

						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;

						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			// 查看发票
			onCheckInvoiceHandle() {
				if (this.detailData.invOrder.invoiceType == '1' || this.detailData.invOrder.taxStatus != '2') return
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(this.detailData.invOrder
						.url)
				});
			},
			_taxStatusFilter(data) {
				return taxStatusFilter(data) || '';
			}
		}
	};
</script>

<style scoped lang="scss">
	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color:#0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}

	.invoice-details__box {
		padding-bottom: 160rpx;
	}



	.half-screen-dialog__hd {
		height: 100rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;

		position: relative;
	}

	.half-screen-dialog__hd__main {
		width: 100%;
	}

	.half-screen-dialog__hd__main .title {
		color: #333333;
		font-size: 36rpx;
		font-weight: 700;
		padding-left: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__main .desc_title {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__side {
		position: absolute;
		z-index: 3;
	}

	.half-screen-dialog__hd__side .close {
		font-size: 36rpx;
		color: #333333;
	}

	.half-screen-dialog__hd__side--top-left {
		top: 30rpx;
		left: 30rpx;
	}

	.half-screen-dialog__hd__side--top-right {
		top: 30rpx;
		right: 30rpx;
	}

	.half-screen-dialog__bd {
		margin-top: 30rpx;
	}

	.half-screen-dialog__ft {
		padding: 20rpx 56rpx 48rpx 56rpx;
	}

	.invoice-money {
		font-weight: 500;
		color: #FF9D09;
	}
</style>
