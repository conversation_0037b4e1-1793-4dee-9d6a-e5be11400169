<template>
	<view class="invoice-record-list" :style="{'height':windowHeight-45+'px'+';verflow: hidden;'}">
		<view class="invoice-scroll" style="overflow: scroll;">
			<!-- 筛选 -->
			<view class="invoice-record-search">

				<view class="search-form-item" style="display: flex;">
					<view class="title">创建日期</view>
					<view class="picker">
						<view class="pick-date pick-date-one">
							<picker mode="date" @change="startDateChange" :value="formData.startDate">
								<u-cell-item title=" " :arrow="false" @click="show= true">
									<view class="monthData">{{formData.startDate}}</view>
								</u-cell-item>
							</picker>
						</view>
						<view>至</view>
						<view class="pick-date pick-date-two">
							<picker mode="date" @change="endDateChange" :value="formData.endDate">
								<u-cell-item title=" " :arrow="false">
									<view class="monthData">{{formData.endDate}}</view>
								</u-cell-item>
							</picker>
						</view>
					</view>
				</view>
				<view class="search-form-item">
					<u-cell-item title="发票类型" class="car" :title-style="titleStyle" :arrow="true"
						@click="showScene= true" label=" ">
						<u-input v-model="sceneName" :clearable="false" :custom-style="customStyle" disabled
							@click="showScene= true" />
					</u-cell-item>
				</view>
				<view class="search-btn">
					<button class="cu-btn bg-topic" @tap="getInvoiceList">查询</button>
				</view>

			</view>
			<view class="no-vehicle" v-if="invoiceListData.length==0">
				<image src="../../../static/toc/no-data.png" mode="aspectFilt" class="no-vehicle_icon"></image>
				<view class="no-vehicle_des">暂无已开票记录</view>
			</view>
			<view v-else class="invoice-record-list_bd">

				<block>
					<scroll-view :style="'height:100%'" :scroll-top="scrollTop" scroll-y="true" class="scroll-Y"
						:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower"
						@scroll="scroll">
						<view class="weui-form-preview" v-for="(item) in invoiceListData" :key="item.invOrderId">
							<view class="weui-form-preview__hd">
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label g-flex g-flex-start g-flex-align-center">

										<view v-if="formData.invoiceApplyType=='4'">
											{{item.invoiceApplyTypeStr}}发票
										</view>
										<view v-if="formData.invoiceApplyType=='2'">
											线上订单权益费发票
										</view>
									</view>
									<view class="weui-form-preview__value">
										<view class="weui-tag"
											:class="{ 'weui-tag--success': item.taxStatus ==2,'weui-tag--danger':item.taxStatus ==0}">
											{{item.taxStatusStr}}
										</view>
									</view>
								</view>
							</view>
							<view class="weui-form-preview__bd">
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">发票金额：</view>
									<view class="weui-form-preview__value">{{moneyFilter(item.totalFee)}}元</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">发票类型：</view>
									<view class="weui-form-preview__value">普通电子发票</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">开票时间：</view>
									<view class="weui-form-preview__value">{{timeFilter(item.createdTime)}}</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">票据单号：</view>
									<view class="weui-form-preview__value">{{item.invoiceNum }}</view>
								</view>
							</view>

							<view class="weui-form-preview__ft">
								<view class="weui-form-preview__btn">
									<view class="btn-item" v-if='item.taxStatus!="0"&&item.taxStatus'><button
											class="weui-btn_mini  weui-btn_primary"
											@click="searchDetail(item)">查看发票</button>
									</view>
									<view class="btn-item" v-if="item.taxStatus=='2' && item.invoiceType=='0'"><button
											class="weui-btn_mini weui-btn_plain-default"
											@click="open(item)">发票红冲</button>
									</view>
									<view class="btn-item"><button class="weui-btn_mini weui-btn_plain-default"
											@click="goSendHandle(item)">重推发票</button></view>
								</view>
							</view>

						</view>
						<load-more :loadStatus="noticeLoadStatus" v-if="invoiceListData.length!=0" />
					</scroll-view>
				</block>
			</view>
		</view>


		<tLoading :isShow="isLoading" />
		<!-- 发票红冲 -->
		<uni-popup ref="popup" background-color="#F9F9F9">
			<view class="half-screen-dialog" style="background: #F9F9F9;border-radius: 20px 20px 0px 0px">
				<view class="half-screen-dialog__hd g-flex g-flex-align-center">
					<view class="half-screen-dialog__hd__main ">
						<view class="title">
							发票红冲
						</view>
					</view>
					<view class="half-screen-dialog__hd__side"
						:class="['half-screen-dialog__hd__side--' + closeIconPos]" @click="closePopup('bottom')">
						<text class="cuIcon-close close"></text>
					</view>

				</view>
				<view class="half-screen-dialog__bd" style="margin: 30rpx 0;">

					<view class="weui-form" style="margin: 30rpx;">
						<view class="weui-cells weui-cells-no__title">
							<view class="vux-x-input weui-cell">
								<view class="weui-cell__hd">
									<view class="weui-label">备注说明</view>
								</view>
								<view class="weui-cell__bd weui-cell__primary">
									<input @input="modifyInput" id="remark" :value="remark" class="weui-input"
										placeholder="请输入备注说明"></input>
								</view>
								<view class="weui-cell__ft">
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="half-screen-dialog__ft">
					<view class="bottom-box">
						<view class="btn-item" style="margin-left: 0rpx;">
							<button class="weui-btn weui-btn_primary" @click="onCloseHandle('bottom','confirm')">
								确定
							</button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<TModalSide :showModal='dialogVisible' @okModal='getInvoiceList' @cancelModal='dialogVisible=false'
			@resetModal='resetHandle'>
			<view slot='content' class="searh-cotent">
				<view class="content-item">
					<view class="item-title">
						订单日期
					</view>
					<view class="item-value g-flex g-flex-justify g-flex-align-center">
						<view class="value-date">
							<picker mode="date" @change="startDateChange">
								<view class="picker ">
									{{formData.startDate}}
								</view>
							</picker>
						</view>
						<view class="line">

						</view>
						<view class="value-date">
							<picker mode="date" @change="endDateChange">
								<view class="picker ">
									{{formData.endDate}}
								</view>
							</picker>
						</view>

					</view>
				</view>
				<view class="content-item">
					<view class="item-title">
						订单类型
					</view>
					<view class="invoice-type g-flex g-flex-wrap">
						<view :class="selectIndex==index?'invoice-type-item selected':'invoice-type-item'"
							v-for="(item,index) in invoiceType" :key="index" @click="selectInvoiceType(item,index)">
							{{item.label}}
						</view>
					</view>
				</view>
			</view>
		</TModalSide>
		<u-select v-model="showScene" :list="invoiceType" :defaultValue="['0']" @confirm="confirm">
		</u-select>

	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	import loadMore from '../../components/load-more/index.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		format
	} from '@/common/format-date.js'
	import float from '@/common/method/float.js'
	import {
		taxStatusFilter,
	} from '@/common/method/filter.js';
	import {
		invoiceType
	} from '@/common/const/optionData.js'
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	import TModalSide from '@/pagesB/components/t-modal-side/t-modal-side.vue'
	export default {
		data() {
			return {

				invoiceListData: [],
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				cardAmount: {},
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				pageNum: 1,
				pageSize: 10,
				isLoading: false,
				invoiceStatus: [{
						value: '0',
						label: '未开票'
					},
					{
						value: '1',
						label: '开票中'
					},
					{
						value: '2',
						label: '已开票'
					},
				],
				formData: {
					startDate: format(new Date().getTime(), "yyyy-MM-dd"), // 开始时间
					endDate: format(new Date().getTime(), "yyyy-MM-dd"), // 结束时间
					invoiceApplyType: '2' //发票类型
				},
				closeIconPos: 'top-right',
				invoiceRedParam: {
					remark: ''
				},
				detailRow: null,
				invoiceType,
				selectIndex: 0,
				dialogVisible: false,
				flag: false,
				showScene: false,
				sceneName: '权益票',
				titleStyle: {
					color: "#888888",
					fontWeight: "400",
					width: "200rpx",
					display: "inline-block",
					fontSize: "30rpx",
					// marginRight: "50rpx"
				},
				customStyle: {
					paddingLeft: "0",
					color: "#333333",
					minHeight: "54rpx",
					fontSize: '30rpx'
				},
				refreshIfNeeded: false,

			}
		},
		props: {
			refresh: {
				type: Boolean,
				default: false,
			},

		},
		components: {
			loadMore,
			tLoading,
			TModalSide
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},


		},
		onLoad(obj) {},
		created() {
			// this.formData.startDate = format(new Date().getTime(), "yyyy-MM-dd")
			// this.formData.endDate = format(new Date().getTime(), "yyyy-MM-dd")
			this.getInvoiceList()
		},
		methods: {
			format,
			confirm(obj) {
				this.sceneName = obj[0].label;
				this.formData.invoiceApplyType = obj[0].value;
			},
			timeFilter(date) {
				if (!date) return;
				return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
			},
			moneyFilter(val) {
				let value = val
				if (value == 0) return value
				value = value / 100
				return this.toDecimal2(value)
			},
			toDecimal2(x) {
				var f = parseFloat(x)
				if (isNaN(f)) {
					return false
				}
				var f = Math.round(x * 100) / 100
				var s = f.toString()
				var rs = s.indexOf('.')
				if (rs < 0) {
					rs = s.length
					s += '.'
				}
				while (s.length <= rs + 2) {
					s += '0'
				}
				return s
			},
			_taxStatusFilter(data) {
				return taxStatusFilter(data) || '';
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				if (this.flag) return;
				let self = this;


				setTimeout(function() {
					self.pageNum = self.pageNum + 1;
					self.getInvoiceList('page');
				}, 500)

			},
			scroll: function(e) {

				this.old.scrollTop = e.detail.scrollTop;

			},
			startDateChange(e) {
				this.formData.startDate = e.detail.value
			},
			endDateChange(e) {
				this.formData.endDate = e.detail.value
			},
			resetHandle() {
				this.formData.startDate = format(new Date().getTime(), "yyyy-MM-dd")
				this.formData.endDate = format(new Date().getTime(), "yyyy-MM-dd")
				this.formData.invoiceApplyType = ''
				this.selectIndex = null
			},
			selectInvoiceType(item, index) {
				if (index != this.selectIndex) {
					this.selectIndex = index;
					this.formData.invoiceApplyType = item.value
				} else {
					this.selectIndex = null;
					this.formData.invoiceApplyType = ''
				}

			},
			validate() {

				if (!this.formData.invoiceApplyType) {
					uni.showToast({
						title: '请选择订单类型',
						icon: "none"
					})
					return false
				}
				return true
			},
			getInvoiceList(val) {
				if (!this.validate()) return
				this.noticeLoadStatus = 1;
				let data = JSON.parse(JSON.stringify(this.formData))
				// data.startDate = '20230101'
				// data.endDate = '20231210'
				data.startDate = format(new Date(data.startDate).getTime(), "yyyyMMdd")
				data.endDate = format(new Date(data.endDate).getTime(), "yyyyMMdd")

				let params = {
					customerId: this.customerInfo.customer_id,
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					...data

				}
				this.$request.post(this.$interfaces.invoiceQueryInvHistory, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.dialogVisible = false
						this.isLoading = false
						if (val != 'page') {
							this.invoiceListData = []
						}

						let result = res.data.data || []
						if (res.data.data.length) {
							this.invoiceListData = this.invoiceListData.concat(result)
						} else {
							this.noticeLoadStatus = 3
							this.flag = true
						}
						if (this.invoiceListData.length == res.data.total) {
							this.noticeLoadStatus = 3
							this.flag = true
						}
					} else {
						this.noticeLoadStatus = 2;
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,

						});

					}
				}).catch((err) => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,

					});
					this.noticeLoadStatus = 2;
				})
			},
			searchDetail(val) {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/invoiceRecord/details?invOrderId=' + val.invOrderId +
						'&applyType=' + val.invoiceApplyType
				})
			},
			// 查看发票
			onCheckInvoiceHandle(row) {
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(row.url)
				});
			},
			// 跳转发票重发
			goSendHandle(row) {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/invoiceRecord/send?invOrderId=' + row.invOrderId +
						'&email=' + row.email

				})
			},
			open(row) {
				this.invoiceRedParam.remark = '';
				this.detailRow = row;
				this.$refs.popup.open('bottom')
			},
			modifyInput(e) {
				this.invoiceRedParam[e.currentTarget.id] = e.detail.value;
			},
			onCloseHandle(type, action) {
				if (action == 'confirm') {
					this.invoiceRedHandle();
					return;
				}
				this.closePopup('bottom');

			},
			closePopup(type) {
				this.$refs.popup.close(type)
			},
			// 发票红冲
			invoiceRedHandle() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					invOrderId: this.detailRow.invOrderId,
					remark: this.invoiceRedParam.remark,
					custMastId: this.customerInfo.customer_id || ''
				}
				this.$request
					.post(this.$interfaces.invoiceRed, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '发票红冲成功',
								showCancel: false
							})
							this.invoiceListData = [];
							this.page_num = 1;
							this.getInvoiceList()
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/index?type=' + 'unInvoice'
							});
							this.closePopup('bottom');
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
							this.closePopup('bottom');
						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;
						this.closePopup('bottom');
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},


		}
	}
</script>

<style scoped lang="scss">
	.invoice-record-search {
		background-color: #FFFFFF;

		.search-btn {
			display: flex;
			justify-content: center;
			justify-content: space-around;
			padding-top: 20upx;
			padding-bottom: 20upx;

			/deep/.cu-btn {
				width: 690rpx;
				height: 84rpx;
				background: #0066E9;
				border-radius: 10rpx;
				font-size: 34rpx;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 26rpx;
			}
		}

		.search-form-item {
			margin: 0 30rpx;
			border-bottom: 1rpx solid #E9E9E9;
			line-height: 100rpx;
			height: 100rpx;

			.title {
				color: #888;
				font-size: 30rpx;
				width: 200rpx;
			}

			.picker {
				width: calc(100% - 220rpx);
				display: flex;
				height: 100%;
			}

			/deep/.u-border-bottom::after {
				border-bottom: none;
			}

			.pick-date {
				width: 40%;

				/deep/.monthData {
					text-align: right;
				}

				/deep/.u-cell__value {
					font-size: 30rpx !important;
				}
			}

			.pick-date-one {
				/deep/.monthData {
					text-align: left;
				}
			}

			/deep/.u-cell {
				padding: 20rpx 0;
			}

			/deep/.u-cell__value {
				color: #333;
				text-align: left;
				font-size: 30rpx;
			}
		}
	}
	.invoice-scroll {
		height: calc(100% - 168rpx);
	}
	
	.fixed-margin-bottom {
		padding-bottom: 160rpx;
	}

	.fixed-padding-top {
		padding-top: 20rpx;
	}

	.invoice-record-list {
		width: 100%;
		height: 100%;
		// background-color: #f9f9f9;
	}

	.invoice-record-list .invoice-record-list_wrapper {
		padding: 20rpx 0 120rpx 0;
	}

	.invoice-record-list .weui-btn_wrapper {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
	}

	.no-invoice {
		padding-top: 320rpx;
		width: 100%;
		background-color: #f9f9f9;
	}

	.no-invoice .no-invoice_icon {
		width: 240rpx;
		height: 180rpx;
		margin: 0 auto;
		display: block;
	}

	.no-invoice .no-invoice_des {
		font-size: 29rpx;
		color: #333333;
		font-weight: 400;
		text-align: center;
		margin-top: 40rpx;
	}


	.invoice-record-list .invoice-record-list_bd {
		margin: 0 30rpx !important;
	}

	.invoice-record-list .weui-form-preview {
		padding: 20rpx 0rpx;
		margin-bottom: 20rpx;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__hd {
		padding: 10rpx 30rpx !important;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__bd {
		padding: 16rpx 30rpx !important;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__bd .weui-form-preview__item {
		display: flex;
		justify-content: flex-start !important;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__bd .weui-form-preview__label {
		min-width: 180rpx;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__ft {
		padding: 0 30rpx !important;
	}

	.invoice-record-list .weui-form-preview__btn {
		display: flex;
		justify-content: flex-end;
	}

	.invoice-record-list .weui-form-preview__btn .btn-item {
		min-width: 156rpx;
		margin-left: 20rpx;
	}

	.weui-tag {
		background-color: rgba(56, 116, 255, 0.1);
		border-radius: 14rpx;
		display: inline-block;
		height: 48rpx;
		padding: 0 20rpx;
		line-height: 48rpx;
		font-size: 26rpx;
		color: #3874FF;
		border-radius: 8rpx;
		box-sizing: border-box;
		white-space: nowrap;
	}

	.weui-tag.weui-tag--success {
		color: #2BA650;
		background-color: rgba(43, 166, 80, 0.1);
	}

	.weui-tag.weui-tag--danger {
		color: #FF5454;
		background-color: rgba(255, 84, 84, 0.1);
	}

	.half-screen-dialog__hd {
		height: 100rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;

		position: relative;
	}

	.half-screen-dialog__hd__main {
		width: 100%;
	}

	.half-screen-dialog__hd__main .title {
		color: #333333;
		font-size: 36rpx;
		font-weight: 700;
		padding-left: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__main .desc_title {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__side {
		position: absolute;
		z-index: 3;
	}

	.half-screen-dialog__hd__side .close {
		font-size: 36rpx;
		color: #333333;
	}

	.half-screen-dialog__hd__side--top-left {
		top: 30rpx;
		left: 30rpx;
	}

	.half-screen-dialog__hd__side--top-right {
		top: 30rpx;
		right: 30rpx;
	}

	.half-screen-dialog__bd {
		margin-top: 30rpx;
	}

	.half-screen-dialog__ft {
		padding: 20rpx 56rpx 48rpx 56rpx;
	}

	.order-filter {
		background-color: #fff;
		margin: 10rpx 30rpx;
		font-size: 26rpx;
		color: #7f7f7f;
	}

	.searh-cotent {
		margin: 60rpx 30rpx;

		.content-item {
			margin: 20rpx 0;

			.item-title {
				font-weight: bold;
				font-size: 30rpx;
				margin-bottom: 20rpx;

			}

			.item-value {
				margin-left: 10rpx;

				.line {
					width: 100rpx;
					height: 1rpx;
					border-bottom: 1px solid #000;
				}

				.value-date {
					padding: 20rpx;
					border-radius: 10rpx;
					background-color: #f5f6fa;
				}
			}

			.invoice-type {
				.invoice-type-item {
					background-color: #f5f6fa;
					border-radius: 16rpx;
					margin: 10rpx 10rpx;
					border: 1px solid #f5f6fa;
					text-align: center;
					padding: 18rpx 20rpx;
				}

				.selected {
					border: 1px solid #24acf2;
					color: #24acf2;
				}
			}
		}
	}

	.no-vehicle {
		width: 100%;
		background-color: #f3f3f3;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 28rpx;
		color: #999999;
		font-weight: 400;
		text-align: center;
		margin-top: 20rpx;
	}
</style>