<!--
  * @desc:开票详情
  * @author:zhangys
  * @date:2023-02-09 17:40:19
!-->
<template>
	<view class="detail-content">
		<view class="invoice-info g-flex g-flex-column g-flex-horizontal-vertical" style="margin-top: 20rpx;">
			<view class="title">
				发票总金额
			</view>
			<view class="money">
				{{totalMoney}}元
			</view>

		</view>
		<view class="invoice-info">
			<view class=" info g-flex g-flex-column ">
				<view class="list-text g-flex g-flex-justify g-flex-align-center">
					<text>发票内容：</text>
					<text>技术支持服务费</text>
				</view>
				<view class=" g-flex g-flex-justify g-flex-align-center">
					<text>开票方：</text>
					<text>广西接通高速科技有限公司</text>
				</view>
			</view>
		</view>
		<view class="invoice-list">
			<view class="invoice-record-list_bd">
				<block>
					<view class="weui-form-preview" v-for="(item,index) in invoiceListData" :key="index">
						<view class="weui-form-preview__hd">
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label g-flex g-flex-start g-flex-align-center">

									<view v-if="invoiceData.invoiceApplyType=='4'">
										{{item.invoiceApplyTypeStr}}
									</view>
									<view class="" v-if="invoiceData.invoiceApplyType=='2'">
										线上订单权益票
									</view>
								</view>
								<view class="weui-form-preview__value">
									<view class="weui-tag">
										待开票
									</view>
								</view>
							</view>
						</view>
						<view class="weui-form-preview__bd">
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">待开发票金额：</view>
								<view class="weui-form-preview__value" v-if="invoiceData.invoiceApplyType=='4'">
									{{moneyFilter(item.totalFee)}}元</view>
								<view class="weui-form-preview__value" v-if="invoiceData.invoiceApplyType=='2'">
									{{moneyFilter(item.fee)}}元</view>
							</view>
							<view class="" v-if="invoiceData.invoiceApplyType=='4'">
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">发票类型：</view>
									<view class="weui-form-preview__value">普通电子发票</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">入口站点：</view>
									<view class="weui-form-preview__value">{{item.enStation}}</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">出口站点：</view>
									<view class="weui-form-preview__value">{{item.exStation}}</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">通行时间：</view>
									<view class="weui-form-preview__value">
										{{format(new Date(item.transTime).getTime(), "yyyy-MM-dd hh:mm:ss")}}
									</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">滞纳金金额：</view>
									<view class="weui-form-preview__value">{{moneyFilter(item.forfeitFee)}}元</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">服务费金额：</view>
									<view class="weui-form-preview__value">{{moneyFilter(item.serviceFee)}}元</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">通行金额：</view>
									<view class="weui-form-preview__value">{{moneyFilter(item.transFee)}}元</view>
								</view>
							</view>
							<view class="" v-if="invoiceData.invoiceApplyType=='2'">
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">发票类型：</view>
									<view class="weui-form-preview__value">普通电子发票</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">车牌号：</view>
									<view class="weui-form-preview__value">
										{{item.carNo}}【{{getVehicleColor(item.carColor)}}】</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">卡号：</view>
									<view class="weui-form-preview__value">{{item.cardNo}}</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">OBU号：</view>
									<view class="weui-form-preview__value">{{item.obuNo}}</view>
								</view>
								<view class="weui-form-preview__item">
									<view class="weui-form-preview__label">时间：</view>
									<view class="weui-form-preview__value">
										{{format(new Date(item.time).getTime(), "yyyy-MM-dd hh:mm:ss")}}
									</view>
								</view>


							</view>

						</view>
					</view>

				</block>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	import {
		format
	} from '@/common/format-date.js'
	import float from '@/common/method/float.js'
	import {
		getVehicleColor,

	} from '@/common/method/filter.js'
	export default {
		name: '',
		props: {

		},
		components: {

		},
		data() {
			return {
				invoiceData: {

				},
				invoiceListData: [],
				totalMoney: ''

			}
		},
		onLoad(obj) {
			console.log(obj, '------');
			let params = {}
			for (let key in obj) {
				this.invoiceData[key] = obj[key]
			}

			this.invoiceData.selectInvoiceNo = JSON.parse(this.invoiceData.selectInvoiceNo)
			console.log(this.invoiceData);
			this.onSearchHandle()
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
		},
		watch: {

		},
		created() {

		},
		methods: {
			format,
			getVehicleColor,
			moneyFilter(money) {
				if (money ==0 || !money) {
					return money
				}
				return float.div(money, 100)
			},
			onSearchHandle() {
				this.isLoading = true;
				let params = {
					startDate: this.invoiceData.startDate,
					endDate: this.invoiceData.endDate,
					invoiceApplyType: this.invoiceData.invoiceApplyType,
					custMastId: this.customerInfo.customer_id,
					ids: this.invoiceData.selectInvoiceNo

				}
				this.$request.post(this.$interfaces.invoiceQueryBizOrders, {
					data: params
				}).then(res => {
					console.log(res);
					this.isLoading = false;
					if (res.code == 200) {
						this.invoiceListData = res.data.orderList
						let sum = 0
						if (params.invoiceApplyType == '2') {
							this.invoiceListData.map(item => {
								sum += item.fee * 1
								return sum
							})
						}
						if (params.invoiceApplyType == '4') {
							this.invoiceListData.map(item => {
								sum += item.totalFee * 1
								return sum
							})
						}

						this.totalMoney = float.div(sum, 100)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,

						});
					}

				}).catch(() => {

					this.isLoading = false;
				})
			},
		},
	}
</script>

<style lang='scss' scoped>
	.detail-content {

		.invoice-info {
			margin: 0 20rpx;
			background-color: #fff;

			.title {
				font-weight: 500;
				margin: 30rpx 0 20rpx 0;
				font-size: 30rpx
			}

			.money {
				color: #FF9D09;
				font-weight: bold;
				font-size: 32rpx;
			}

			.info {
				padding: 30rpx;

				.list-text {
					margin-bottom: 10rpx;
				}
			}
		}

	}

	.invoice-list {
		margin-top: 30rpx;
	}

	.invoice-record-list_bd {
		margin: 0 30rpx !important;
	}

	.weui-form-preview {
		padding: 20rpx 0rpx;
		margin-bottom: 20rpx;
	}

	.weui-form-preview .weui-form-preview__hd {
		padding: 10rpx 30rpx !important;
	}

	.weui-form-preview .weui-form-preview__bd {
		padding: 12rpx 30rpx !important;

	}

	.weui-form-preview .weui-form-preview__bd .weui-form-preview__item {
		/* padding-bottom: 10rpx !important; */
		display: flex;
		justify-content: flex-start !important;
	}

	.weui-form-preview .weui-form-preview__bd .weui-form-preview__label {
		min-width: 180rpx;
	}

	.weui-form-preview .weui-form-preview__ft {
		padding: 0 30rpx !important;
	}

	.weui-form-preview__btn {
		display: flex;
		justify-content: flex-end;
	}

	.weui-form-preview__btn .btn-item {
		min-width: 156rpx;
		margin-left: 20rpx;
	}

	.weui-tag {
		background-color: rgba(56, 116, 255, 0.1);
		border-radius: 14rpx;
		display: inline-block;
		height: 48rpx;
		padding: 0 20rpx;
		line-height: 48rpx;
		font-size: 26rpx;
		color: #3874FF;
		border-radius: 8rpx;
		box-sizing: border-box;
		white-space: nowrap;
	}

	.weui-tag.weui-tag--success {
		color: #2BA650;
		background-color: rgba(56, 116, 255, 0.1);
	}

	.weui-tag.weui-tag--danger {
		color: #FF5454;
		background-color: rgba(56, 116, 255, 0.1);
	}
</style>