<template>
	<view class="add-invoice">
		<view class="weui-form">
			<view class="weui-cells__title">
				<view class="weui-cells__title__decoration">
					发票详情
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">抬头类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							<radio-group @change="invoiceChange" class="g-flex g-flex-end" style="padding-top: 20upx;">
								<label class="uni-list-cell uni-list-cell-pd " v-for="(item, index) in invoiceOptions"
									:key="item.value">
									<view>
										<radio :value="item.value" class="cyan" style="transform: scale(0.7)"
											:checked="item.value==userTypeIndexSub" />
										<text style="margin: 0 10upx;">{{item.name}}</text>
									</view>

								</label>
							</radio-group>
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">公司名称</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.buyerName" class="weui-input" placeholder="请输入公司名称"></input>
					</view>
					<view class="weui-cell__ft" @click="open">
						<text class="cuIcon-sort" style="font-size: 40rpx;"></text>
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="userTypeIndexSub=='1'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">公司税号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.buyerTaxpayerNum" class="weui-input" placeholder="请输入纳税人识别号"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" @click="isShowMore=!isShowMore" v-if="!isShowMore">
					<view class="weui-cell__hd">
						<view class="weui-label ">更多内容</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							填写备注、地址等（非必填）
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell " v-if="isShowMore&&userTypeIndexSub=='1'">
					<view class="weui-cell__hd">
						<view class="weui-label ">注册地址</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.buyerAddress" class="weui-input" placeholder="请输入公司注册地址"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="isShowMore&&userTypeIndexSub=='1'">
					<view class="weui-cell__hd">
						<view class="weui-label ">注册电话</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.buyerPhone" class="weui-input" placeholder="请输入公司注册电话"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="isShowMore&&userTypeIndexSub=='1'">
					<view class="weui-cell__hd">
						<view class="weui-label ">开户银行</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.buyerBankName" class="weui-input" placeholder="请输入公司开户银行"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="isShowMore&&userTypeIndexSub=='1'">
					<view class="weui-cell__hd">
						<view class="weui-label ">银行账号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.buyerBankAccount" class="weui-input" placeholder="请输入银行账号"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="isShowMore&&userTypeIndexSub=='1'">
					<view class="weui-cell__hd">
						<view class="weui-label ">备注</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.remark" class="weui-input" placeholder="请输入备注"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="invoice-default">

			<view class="invoice-default-bd">
				<view class="title">总金额：<text class="money">{{invoiceApplyData.totalMoney}}元</text> </view>
			</view>
			<view class="invoice-default-value" v-if="invoiceApplyData.invoiceApplyType" @click="invoiceDetail">
				<text style="color: #b3b3b3;">共{{invoiceApplyData.selectInvoiceNo.length}}项，查看详情>></text>
			</view>

		</view>
		<view class="weui-form">
			<view class="weui-cells__title">
				<view class="weui-cells__title__decoration">
					接收方式
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">电子邮箱</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.email" class="weui-input" placeholder="请输入电子邮箱"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed" style="z-index: 99;">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="invoice">
						提交
					</button>
				</view>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<uni-popup ref="popup" background-color="#F9F9F9">
			<view class="half-screen-dialog" style="background: #F9F9F9;border-radius: 20px 20px 0px 0px">
				<view class="half-screen-dialog__hd g-flex g-flex-align-center">
					<view class="half-screen-dialog__hd__main ">
						<view class="title">
							发票抬头
						</view>
					</view>
					<view class="half-screen-dialog__hd__side"
						:class="['half-screen-dialog__hd__side--' + closeIconPos]" @click="closePopup('bottom')">
						<text class="cuIcon-close close"></text>
					</view>


				</view>
				<view class="half-screen-dialog__bd" style="margin: 30rpx 0; max-height: 330rpx; overflow-y: scroll;">
					<view class="weui-media" v-for="(item,index) in titleListData" :key="index">
						<view>
							<radio-group @change="checked=>changeRadio(checked,item,index)">
								<radio :value="index" class="cyan" style="transform: scale(0.7)"
									:checked="index===selectIndex" />
							</radio-group>
						</view>
						<view class="weui-media-bd">
							<view class="title g-flex">
								<view class="">
									{{item.buyerName}}
								</view>
								<view v-if="item.latest"
									style="margin-left: 4px;font-size: 12px;border: 1px solid #0066E9;color: #0066E9;padding: 1px;border-radius: 5px;">
									默认
								</view>
							</view>
							<view class="value" v-if="item.buyerTaxpayerNum">{{item.buyerTaxpayerNum}}</view>
						</view>
					</view>
				</view>
				<view class="half-screen-dialog__ft">
					<view class="bottom-box">
						<view class="btn-item">
							<button class="weui-btn weui-btn_primary" @click="addTitle">
								新增抬头
							</button>
						</view>
						<view class="btn-item">
							<button class="weui-btn weui-btn_primary" @click="onCloseHandle('bottom','confirm')">
								确定
							</button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getLoginUserInfo
	} from "@/common/storageUtil.js";
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				closeIconPos: 'top-right',
				isLoading: false,
				invoiceDefaultChecked: false, // 设置默认抬头
				formData: {
					buyerName: '', //购方名称
					buyerTaxpayerNum: '', //购方纳税人识别号
					buyerAddress: '', //购方地址
					buyerPhone: '', //购方电话
					buyerBankName: '', //购方开户行名称
					buyerBankAccount: '', //购房银行账号
					email: '', //邮箱
					mobilePhone: '', //手机号
					remark: '', //备注
					custMastId: '', //ETC用户ID,
					invoiceApplyType: '', //开票类型 1-设备票2-权益票3-月结账单服务费滞纳金4-次次顺服务费滞纳金
				},
				userTypeIndexSub: '1',
				invoiceOptions: [{
						value: '1',
						name: '企业单位'
					},
					{
						value: '2',
						name: '个人'
					}
				],
				isShowMore: false,
				invoiceApplyData: {},
				titleListData: [],
				selectIndex: null,
				selectInvoiceTitle: {}
			};
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},


		},
		onLoad(obj) {
			console.log(obj, 'obj');
			for (let key in obj) {
				this.invoiceApplyData[key] = obj[key]
			}
			console.log(this.invoiceApplyData);

			// /this.invoiceApplyData.selectInvoiceNo = this.invoiceApplyData.selectInvoiceNo.split(',')
			this.invoiceApplyData.selectInvoiceNo = JSON.parse(this.invoiceApplyData.selectInvoiceNo)
			this.formData.invoiceApplyType = this.invoiceApplyData.invoiceApplyType
			this.formData.custMastId = this.customerInfo.customer_id
			this.getTitleList()
		},

		created() {

		},
		methods: {
			invoiceChange(e) {
				this.userTypeIndexSub = e.detail.value
			},
			open(type) {
				this.$refs.popup.open('bottom')
			},
			onCloseHandle(type, action) {
				if (action == 'confirm') {
					if (Object.keys(this.selectInvoiceTitle).length == 0) {
						this.closePopup('bottom');
						return
					}
					this.matchData(this.selectInvoiceTitle)
				}
				this.closePopup('bottom');

			},
			closePopup(type) {
				this.$refs.popup.close(type)
			},
			validate() {
				if (!this.formData.buyerName) {
					uni.showToast({
						title: '请输入公司名称',
						icon: "none"
					})
					return false
				}
				if (!this.formData.buyerTaxpayerNum && this.userTypeIndexSub == '1') {
					uni.showToast({
						title: '请输入公司税号',
						icon: "none"
					})
					return false
				}
				if (!this.formData.email) {
					uni.showToast({
						title: '请输入电子邮箱',
						icon: "none"
					})
					return false
				}
				return true
			},
			//开票
			invoice() {
				if (!this.validate()) return
				this.isLoading = true;
				let params = {
					orderIds: this.invoiceApplyData.selectInvoiceNo,
					...this.formData
				}
				if (this.userTypeIndexSub == '2') {
					delete params.buyerTaxpayerNum
				}
				console.log(params, '开票入参');
				this.$request.post(this.$interfaces.invoiceBlue, {
					data: params
				}).then(res => {
					console.log(res);
					this.isLoading = false;
					if (res.code == 200) {
						uni.showModal({
							title: "提示",
							content: '开票成功',
							showCancel: false,
							success: (res) => {
								if (res) {
									uni.reLaunch({
										url: '/pagesB/invoiceBusiness/index?type=' + 'invoiced'
									});
								}
							}

						});
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
							success: (res) => {
								if (res) {
									uni.reLaunch({
										url: '/pagesB/invoiceBusiness/index?type=' + 'invoiced'
									});
								}
							}
						});
					}

				}).catch(() => {
					uni.showModal({
						title: "提示",
						content: res.msg,
						showCancel: false,

					});
					this.isLoading = false;
				})
			},
			//获取抬头
			getTitleList() {
				let params = {
					source: 1,
					userNo: getLoginUserInfo().userNo || ''
				}
				this.$request
					.post(this.$interfaces.invoiceQueryTemplate, {
						data: params
					})
					.then((res) => {
						console.log(res, '抬头列表');
						if (res.code == 200) {
							this.titleListData = res.data;
							this.titleListData.filter(item => {
								if (item.latest) {
									this.matchData(item)
								}
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			//开票详情
			invoiceDetail() {
				let params = {
					selectInvoiceNo: this.invoiceApplyData.selectInvoiceNo,
					startDate: this.invoiceApplyData.startDate,
					endDate: this.invoiceApplyData.endDate,
					invoiceApplyType: this.invoiceApplyData.invoiceApplyType
				}
				params.selectInvoiceNo = JSON.stringify(params.selectInvoiceNo)
				uni.navigateTo({
					url: './detail?' + this.objToUrlParam(params)
				})
			},
			//单选框更改事件
			changeRadio(e, item, index) {
				console.log('回填信息', 'item')
				this.selectIndex = e.detail.value * 1
				this.selectInvoiceTitle = item
			},
			//新增抬头
			addTitle() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/invoiceTitle/index'
				})
				this.closePopup('bottom');
			},
			//匹配对象
			matchData(val) {
				this.formData.buyerName = val.buyerName ? val.buyerName : ''
				this.formData.buyerTaxpayerNum = val.buyerTaxpayerNum ? val.buyerTaxpayerNum : ''
				this.formData.buyerAddress = val.buyerAddress ? val.buyerAddress : ''
				this.formData.buyerPhone = val.buyerPhone ? val.buyerPhone : ''
				this.formData.buyerBankName = val.buyerBankName ? val.buyerBankName : ''
				this.formData.buyerBankAccount = val.buyerBankAccount ? val.buyerBankAccount : ''
				this.formData.email = val.email ? val.email : ''
				this.formData.mobilePhone = val.mobilePhone ? val.mobilePhone : ''
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},

		}
	};
</script>

<style scoped lang="scss">
	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		// margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color: #0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}


	.invoice-default {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		padding: 20rpx 30rpx;
		margin-top: 20rpx;

	}

	.invoice-default .invoice-default-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;

	}

	.invoice-default .invoice-default-hd .invoice-default-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.invoice-default .invoice-default-bd {
		flex: 1;
	}

	.invoice-default .invoice-default-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.invoice-default .money {
		font-weight: 500;
		color: #FF9D09;
	}

	.invoice-default .invoice-default-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #999999;
	}

	.invoice-default .invoice-default-value {
		text-align: right;
	}



	.half-screen-dialog__hd {
		height: 100rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;

		position: relative;
	}

	.half-screen-dialog__hd__main {
		width: 100%;
	}

	.half-screen-dialog__hd__main .title {
		color: #333333;
		font-size: 36rpx;
		font-weight: 700;
		padding-left: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__main .desc_title {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__side {
		position: absolute;
		z-index: 3;
	}

	.half-screen-dialog__hd__side .close {
		font-size: 36rpx;
		color: #333333;
	}

	.half-screen-dialog__hd__side--top-left {
		top: 30rpx;
		left: 30rpx;
	}

	.half-screen-dialog__hd__side--top-right {
		top: 30rpx;
		right: 30rpx;
	}

	.half-screen-dialog__bd {
		margin-top: 30rpx;
	}

	.half-screen-dialog__ft {
		padding: 20rpx 56rpx 48rpx 56rpx;
	}

	.fixed-margin-bottom {
		padding-bottom: 160rpx;
	}

	.fixed-padding-top {
		padding-top: 20rpx;
	}

	.vehicle-list {
		width: 100%;
		height: 100%;
		background-color: #f9f9f9;
	}

	.vehicle-list .vehicle-list_wrapper {
		padding: 20rpx 0 120rpx 0;
	}

	.vehicle-list .weui-btn_wrapper {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
	}

	.no-vehicle {
		padding-top: 320rpx;
		width: 100%;
		background-color: #f9f9f9;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 29rpx;
		color: #333333;
		font-weight: 400;
		text-align: center;
		margin-top: 60rpx;
	}

	.weui-media {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #F1F1F1;
		margin: 20rpx 30rpx;
		border-radius: 16rpx;
		padding: 32rpx 30rpx;
		box-shadow: 0px 1px 7px 0px rgba(220, 220, 220, 0.5);

		&:first-child {
			margin-top: 0;
		}
	}

	.weui-media .weui-media-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;
	}

	.weui-media .weui-media-hd .weui-media-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.weui-media .weui-media-bd {
		flex: 1;
		margin-left: 30rpx;
	}

	.weui-media .weui-media-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.weui-media .weui-media-bd .value,
	.desc {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
	}

	.weui-media .weui-media-ft {
		width: 28rpx;
	}

	.weui-media .weui-media-ft .weui-media-ft_icon {
		width: 28rpx;
		height: 28rpx;
		display: block;
	}

	.add-invoice {
		padding-bottom: 170rpx;
	}
</style>