<!--
  * @desc:待开发票原订单查询
  * @author:zhangys
  * @date:2023-01-09 15:46:38
!-->
<template>
	<view class="invoice-record-list" :style="{'height':windowHeight-45+'px'+';verflow: hidden;'}">
		<view class="invoice-scroll" style="overflow: scroll;">
			<!-- 筛选 -->
			<view class="invoice-record-search">

				<view class="search-form-item" style="display: flex;">
					<view class="title">创建日期</view>
					<view class="picker">
						<view class="pick-date pick-date-one">
							<picker mode="date" @change="startDateChange" :value="startDate">
								<u-cell-item title=" " :arrow="false" @click="show= true">
									<view class="monthData">{{startDate}}</view>
								</u-cell-item>
							</picker>
						</view>
						<view>至</view>
						<view class="pick-date pick-date-two">
							<picker mode="date" @change="endDateChange" :value="endDate">
								<u-cell-item title=" " :arrow="false">
									<view class="monthData">{{endDate}}</view>
								</u-cell-item>
							</picker>
						</view>
					</view>
				</view>
				<view class="search-form-item">
					<u-cell-item title="发票类型" class="car" :title-style="titleStyle" :arrow="true"
						@click="showScene= true" label=" ">
						<u-input v-model="sceneName" :clearable="false" :custom-style="customStyle" disabled
							@click="showScene= true" />
					</u-cell-item>
				</view>
				<view class="search-btn">
					<button class="cu-btn bg-topic" @tap="onSearchHandle">查询</button>
				</view>

			</view>
			<view class="invoice-tips" v-if="unInvoiceListData.length!=0">
				专票请前往营业网点申请开具
			</view>
			<view class="no-vehicle" v-if="unInvoiceListData.length==0">
				<image src="../../../static/toc/no-data.png" mode="aspectFilt" class="no-vehicle_icon"></image>
				<view class="no-vehicle_des">暂无待开票记录</view>
			</view>
			<view v-else class="invoice-record-list_bd">
				<block>
					<view class="weui-form-preview" v-for="(item,index) in unInvoiceListData" :key="index">
						<view class="weui-form-preview__hd">
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label g-flex g-flex-start g-flex-align-center">
									<view>
										<!-- checkbox多选 -->
										<checkbox-group @change="checked=>changeCheckbox(checked,item,index)">
											<checkbox class="cyan checked  round" :checked='ischeck'
												style=" left:-4px;transform: scale(0.7)" value="check">
											</checkbox>
										</checkbox-group>

									</view>
									<view v-if="invoiceApplyType=='4'">
										{{item.invoiceApplyTypeStr}}
									</view>
									<view class="" v-if="invoiceApplyType=='2'">
										线上订单权益票
									</view>
								</view>
								<view class="weui-form-preview__value">
									<view class="weui-tag">
										待开票
									</view>
								</view>
							</view>
						</view>
						<view class="weui-form-preview__bd" v-if="invoiceApplyType=='4'">
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">待开发票金额：</view>
								<view class="weui-form-preview__value">{{moneyFilter(item.totalFee)}}元</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">发票类型：</view>
								<view class="weui-form-preview__value">普通电子发票</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">入口站点：</view>
								<view class="weui-form-preview__value">{{item.enStation}}</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">出口站点：</view>
								<view class="weui-form-preview__value">{{item.exStation}}</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">通行时间：</view>
								<view class="weui-form-preview__value">
									{{format(new Date(item.transTime).getTime(), "yyyy-MM-dd hh:mm:ss")}}
								</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">滞纳金金额：</view>
								<view class="weui-form-preview__value">{{moneyFilter(item.forfeitFee)}}元</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">服务费金额：</view>
								<view class="weui-form-preview__value">{{moneyFilter(item.serviceFee)}}元</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">通行金额：</view>
								<view class="weui-form-preview__value">{{moneyFilter(item.transFee)}}元</view>
							</view>

						</view>

						<view class="weui-form-preview__bd" v-if="invoiceApplyType=='2'">
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">待开发票金额：</view>
								<view class="weui-form-preview__value">{{moneyFilter(item.fee)}}元</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">发票类型：</view>
								<view class="weui-form-preview__value">普通电子发票</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">车牌号：</view>
								<view class="weui-form-preview__value">
									{{item.carNo}}【{{getVehicleColor(item.carColor)}}】
								</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">卡号：</view>
								<view class="weui-form-preview__value">{{item.cardNo}}</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">OBU号：</view>
								<view class="weui-form-preview__value">{{item.obuNo}}</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">时间：</view>
								<view class="weui-form-preview__value">
									{{format(new Date(item.time).getTime(), "yyyy-MM-dd hh:mm:ss")}}
								</view>
							</view>


						</view>
					</view>

				</block>
			</view>
		</view>

		<!-- 全选，没有数据不显示 -->
		<view class="weui-bottom-fixed" v-if="unInvoiceListData.length!==0">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item" style="margin-left: -28rpx;">
					<checkbox-group class="g-flex g-flex-align-center" @change="checkAll">
						<checkbox class="cyan checked  round" :checked='isAllcheck' style=" transform: scale(0.8,0.8)"
							value="check">
						</checkbox>
						<view>全选 <text
								style="font-size: 24rpx;margin-left: 6rpx;">共{{selectInvoiceNo.length}}条,合计{{totalMoney}}元</text>
						</view>
					</checkbox-group>
				</view>
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="invoice">
						下一步
					</button>
				</view>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!-- 侧边搜索模态框组件 -->
		<TModalSide :showModal='dialogVisible' @okModal='onSearchHandle' @cancelModal='dialogVisible=false'
			@resetModal='resetHandle'>
			<view slot='content' class="searh-cotent">
				<view class="content-item">
					<view class="item-title">
						订单日期
					</view>
					<view class="item-value g-flex g-flex-justify g-flex-align-center">
						<view class="value-date">
							<picker mode="date" @change="startDateChange">
								<view class="picker ">
									{{startDate}}
								</view>
							</picker>
						</view>
						<view class="line">

						</view>
						<view class="value-date">
							<picker mode="date" @change="endDateChange">
								<view class="picker ">
									{{endDate}}
								</view>
							</picker>
						</view>

					</view>
				</view>
				<view class="content-item">
					<view class="item-title">
						订单类型
					</view>
					<view class="invoice-type g-flex g-flex-wrap">
						<view :class="selectIndex==index?'invoice-type-item selected':'invoice-type-item'"
							v-for="(item,index) in invoiceType" :key="index" @click="selectInvoiceType(item,index)">
							{{item.label}}
						</view>
					</view>
				</view>
			</view>
		</TModalSide>
		<u-select v-model="showScene" :list="invoiceType" :defaultValue="['0']" @confirm="confirm">
		</u-select>

	</view>

</template>
<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	// import item from './p-item.vue'
	import loadMore from '../../components/load-more/index.vue';
	import float from '@/common/method/float.js'
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	import {
		format
	} from '@/common/format-date.js'
	import {
		payTypePartOptions,
		invoiceType
	} from '@/common/const/optionData.js'
	import TModalSide from '@/pagesB/components/t-modal-side/t-modal-side.vue'
	import {
		getVehicleColor,

	} from '@/common/method/filter.js'
	export default {
		components: {
			TButton,
			// item,
			tLoading,
			loadMore,
			TModalSide
		},
		data() {
			return {
				windowHeight: this.windowHeight,
				startDate: format(new Date().getTime(), "yyyy-MM-dd"), // 开始时间
				endDate: format(new Date().getTime(), "yyyy-MM-dd"), // 结束时间
				invoiceApplyType: '2',
				isLoading: false,
				unInvoiceListData: [],
				selectInvoiceNo: [], //选中开票订单号
				selectInvoiceMoney: [], //选中开票金额
				totalMoney: 0,
				ischeck: false,
				isAllcheck: false,
				dialogVisible: false,
				invoiceType,
				selectIndex: 0,
				showScene: false,
				sceneName: '权益票',
				titleStyle: {
					color: "#888888",
					fontWeight: "400",
					width: "200rpx",
					display: "inline-block",
					fontSize: "30rpx",
					// marginRight: "50rpx"
				},
				customStyle: {
					paddingLeft: "0",
					color: "#333333",
					minHeight: "54rpx",
					fontSize: '30rpx'
				},

			};
		},
		onLoad(options) {

		},

		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},


		},
		filters: {

		},
		watch: {
			isAllcheck: {
				immediate: true,
				handler(val) {
					this.isAllcheck = val
				}
			},
			invoiceApplyType(val) {
				if (val) {
					this.unInvoiceListData = []
					this.selectInvoiceNo = []
					this.selectInvoiceMoney = []
					this.totalMoney = 0
					this.isAllcheck = false
				}
			}
		},
		created() {
			// this.startDate = format(new Date().getTime(), "yyyy-MM-dd")
			// this.endDate = format(new Date().getTime(), "yyyy-MM-dd")
			this.onSearchHandle()
		},
		methods: {
			format,
			getVehicleColor,
			confirm(obj) {
				this.sceneName = obj[0].label;
				this.invoiceApplyType = obj[0].value;
			},
			moneyFilter(money) {
				if (!money || money == '0') {
					return money
				}
				return float.div(money, 100)
			},
			startDateChange(e) {
				this.startDate = e.detail.value
			},
			endDateChange(e) {
				this.endDate = e.detail.value
			},
			resetHandle() {
				this.startDate = format(new Date().getTime(), "yyyy-MM-dd")
				this.endDate = format(new Date().getTime(), "yyyy-MM-dd")
				this.invoiceApplyType = ''
				this.selectIndex = null
			},
			// 筛选条件点击选中，再次点击取消
			selectInvoiceType(item, index) {
				if (index != this.selectIndex) {
					this.selectIndex = index;
					this.invoiceApplyType = item.value
				} else {
					this.selectIndex = null;
					this.invoiceApplyType = ''
				}

			},
			validate() {
				if (!this.startDate) {
					uni.showToast({
						title: '请选择开始时间',
						icon: "none"
					})
					return false
				}
				if (!this.endDate) {
					uni.showToast({
						title: '请选择结束时间',
						icon: "none"
					})
					return false
				}
				if (!this.invoiceApplyType) {
					uni.showToast({
						title: '请选择订单类型',
						icon: "none"
					})
					return false
				}
				return true
			},
			onSearchHandle() {
				console.log(this.startDate);
				if (!this.validate()) return
				this.unInvoiceListData = []
				this.isLoading = true;
				let params = {
					// // 测试数据
					//  startDate: '20220210',
					// endDate: '20221220',
					startDate: format(new Date(this.startDate).getTime(), "yyyyMMdd"),
					endDate: format(new Date(this.endDate).getTime(), "yyyyMMdd"),
					invoiceApplyType: this.invoiceApplyType,
					custMastId: this.customerInfo.customer_id

				}
				this.$request.post(this.$interfaces.invoiceQueryBizOrders, {
					data: params
				}).then(res => {
					console.log(res);
					this.isLoading = false;
					if (res.code == 200) {
						this.dialogVisible = false
						this.unInvoiceListData = res.data.orderList
						this.applyType = res.data.invoiceApplyType

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,

						});
					}

				}).catch(() => {

					this.isLoading = false;
				})
			},
			//多选
			changeCheckbox(checked, item, index) {
				//选中数据push到数组里
				if (checked.detail.value == 'check') {
					this.selectInvoiceNo.push({
						bizSource: item.bizSource,
						orderId: item.bizOrderId
					})
					console.log(this.selectInvoiceNo);
					if (this.invoiceApplyType == '4') {
						this.selectInvoiceMoney.push(item.totalFee * 1)
					}
					if (this.invoiceApplyType == '2') {
						this.selectInvoiceMoney.push(item.fee * 1)
					}
				} else {
					//取消选中，找出取消数据项的下标，删除
					let index = this.selectInvoiceNo.map(item => item).indexOf(item.bizOrderId)
					this.selectInvoiceNo.splice(index, 1)
					this.selectInvoiceMoney.splice(index, 1)
				}
				let sum = 0
				this.selectInvoiceMoney.map(item => {
					sum += item
					return sum
				})
				this.totalMoney = float.div(sum, 100)

				// 选中长度和列表长度不一致，取消全选按钮，反之选中全选按钮
				if (this.selectInvoiceNo.length == this.unInvoiceListData.length) {
					this.isAllcheck = true
				} else {
					this.isAllcheck = false
				}
			},
			//全选
			checkAll(e) {
				// 全部选清空数据
				if (e.detail.value.length == 0) {
					this.selectInvoiceNo = []
					this.selectInvoiceMoney = []
					this.ischeck = false
					this.totalMoney = 0
					return
				}
				this.ischeck = true
				this.selectInvoiceNo = this.unInvoiceListData.map(item => {
					return {
						bizSource: item.bizSource,
						orderId: item.bizOrderId
					}
				})
				if (this.invoiceApplyType == '4') {
					this.selectInvoiceMoney = this.unInvoiceListData.map(item => {
						return item.totalFee * 1
					})
				}
				if (this.invoiceApplyType == '2') {
					this.selectInvoiceMoney = this.unInvoiceListData.map(item => {
						return item.fee * 1
					})
				}

				let sum = 0
				this.selectInvoiceMoney.map(item => {
					sum += item
					return sum
				})
				this.totalMoney = float.div(sum, 100)
			},
			//跳转开票界面
			invoice() {
				if (this.selectInvoiceNo.length == 0) {
					uni.showModal({
						title: "提示",
						content: '请选择订单',
						showCancel: false,
					});
					return
				}
				let params = {
					selectInvoiceNo: this.selectInvoiceNo,
					totalMoney: this.totalMoney,
					invoiceApplyType: this.invoiceApplyType,
					startDate: format(new Date(this.startDate).getTime(), "yyyyMMdd"),
					endDate: format(new Date(this.endDate).getTime(), "yyyyMMdd"),
				}
				params.selectInvoiceNo = JSON.stringify(params.selectInvoiceNo)
				console.log(params, this.objToUrlParam(params));
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/uninvoiceList/invoice?' + this.objToUrlParam(params)
				})
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},




		},
		destroyed() {

		}
	};
</script>
<style lang="scss" scoped>
	.invoice-record-search {
		background-color: #FFFFFF;

		.search-btn {
			display: flex;
			justify-content: center;
			justify-content: space-around;
			padding-top: 20upx;
			padding-bottom: 20upx;

			/deep/.cu-btn {
				width: 690rpx;
				height: 84rpx;
				background: #0066E9;
				border-radius: 10rpx;
				font-size: 34rpx;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 26rpx;
			}
		}

		.search-form-item {
			margin: 0 30rpx;
			border-bottom: 1rpx solid #E9E9E9;
			line-height: 100rpx;
			height: 100rpx;

			.title {
				color: #888;
				font-size: 30rpx;
				width: 200rpx;
			}

			.picker {
				width: calc(100% - 220rpx);
				display: flex;
				height: 100%;
			}

			/deep/.u-border-bottom::after {
				border-bottom: none;
			}

			.pick-date {
				width: 40%;

				/deep/.monthData {
					text-align: right;
				}

				/deep/.u-cell__value {
					font-size: 30rpx !important;
				}
			}

			.pick-date-one {
				/deep/.monthData {
					text-align: left;
				}
			}

			/deep/.u-cell {
				padding: 20rpx 0;
			}

			/deep/.u-cell__value {
				color: #333;
				text-align: left;
				font-size: 30rpx;
			}
		}
	}

	.invoice-scroll {
		height: calc(100% - 168rpx);
	}


	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.sellPay-Info {
		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.cu-form-group {
		min-height: 90rpx !important;
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.sellPay-Info .cu-form-group radio-group {

		flex: 1;
		text-align: left;
	}

	.weui-media {
		display: flex;
		// -moz-box-align: center;
		// -webkit-box-align: center;
		// box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		margin: 20rpx 30rpx;
		border-radius: 16rpx;
		padding: 32rpx 30rpx;
		box-shadow: 0px 0px 20rpx 0px rgba(71, 123, 217, 0.12);

	}

	.weui-media .weui-media-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;
	}

	.weui-media .weui-media-hd .weui-media-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.weui-media .weui-media-bd {
		flex: 1;
	}

	.weui-media .weui-media-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.weui-media .weui-media-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
	}

	.weui-media .weui-media-value {
		margin-right: 20rpx;
		text-align: right;
	}

	.weui-media .weui-media-value .money {
		font-weight: 500;
		color: #FF9D09;
		font-size: 30rpx;
	}

	.weui-media .weui-media-value .desc {
		font-weight: 400;
		color: #333333;
		font-size: 26rpx;
		margin-top: 12rpx;
	}

	.weui-media .weui-media-ft {
		min-width: 28rpx;
	}

	.weui-media .weui-media-ft .weui-media-ft_btn {
		width: 158rpx;
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;
		background: #0066E9;
		border-radius: 36rpx;
		font-size: 26rpx;
		color: #fff;
		font-weight: 400;
	}

	.weui-media .weui-media-ft .weui-media-ft_icon {
		width: 28rpx;
		height: 28rpx;
		display: block;
	}

	.content-list {
		padding-bottom: 160rpx;
	}

	.searh-cotent {
		margin: 60rpx 30rpx;

		.content-item {
			margin: 20rpx 0;

			.item-title {
				font-weight: bold;
				font-size: 30rpx;
				margin-bottom: 20rpx;

			}

			.item-value {
				margin-left: 10rpx;

				.line {
					width: 100rpx;
					height: 1rpx;
					border-bottom: 1px solid #000;
				}

				.value-date {
					padding: 20rpx;
					border-radius: 10rpx;
					background-color: #f5f6fa;
				}
			}

			.invoice-type {
				.invoice-type-item {
					background-color: #f5f6fa;
					border-radius: 16rpx;
					margin: 10rpx 10rpx;
					border: 1px solid #f5f6fa;
					text-align: center;
					padding: 18rpx 20rpx;
				}

				.selected {
					border: 1px solid #24acf2;
					color: #24acf2;
				}
			}
		}
	}

	.order-filter {
		background-color: #fff;
		margin: 10rpx 30rpx;
		font-size: 26rpx;
		color: #7f7f7f;
	}

	.no-vehicle {
		width: 100%;
		background-color: #f3f3f3;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 28rpx;
		color: #999999;
		font-weight: 400;
		text-align: center;
		margin-top: 20rpx;
	}

	.invoice-record-list {
		padding-bottom: 160rpx;
	}

	.invoice-record-list .invoice-record-list_bd {
		margin: 0 30rpx !important;
	}

	.invoice-record-list .weui-form-preview {
		padding: 20rpx 0rpx;
		margin-bottom: 20rpx;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__hd {
		padding: 10rpx 30rpx !important;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__bd {
		padding: 16rpx 30rpx !important;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__bd .weui-form-preview__item {
		display: flex;
		justify-content: flex-start !important;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__bd .weui-form-preview__label {
		min-width: 180rpx;
	}

	.invoice-record-list .weui-form-preview .weui-form-preview__ft {
		padding: 0 30rpx !important;
	}

	.invoice-record-list .weui-form-preview__btn {
		display: flex;
		justify-content: flex-end;
	}

	.invoice-record-list .weui-form-preview__btn .btn-item {
		min-width: 156rpx;
		margin-left: 20rpx;
	}

	.weui-tag {
		background-color: rgba(56, 116, 255, 0.1);
		border-radius: 14rpx;
		display: inline-block;
		height: 48rpx;
		padding: 0 20rpx;
		line-height: 48rpx;
		font-size: 26rpx;
		color: #3874FF;
		border-radius: 8rpx;
		box-sizing: border-box;
		white-space: nowrap;
	}

	.weui-tag.weui-tag--success {
		color: #2BA650;
		background-color: rgba(56, 116, 255, 0.1);
	}

	.weui-tag.weui-tag--danger {
		color: #FF5454;
		background-color: rgba(56, 116, 255, 0.1);
	}

	.invoice-tips {
		margin: 8rpx 30rpx;
		font-size: 26rpx;
		color: #a699a6;
	}
</style>