<template>
	<view class="add-invoice" @click="isShowSelect = false">
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify">
				<view class="weui-cells__title__decoration">
					发票抬头信息
				</view>
				<view class="del-btn" @click="delHandle">
					删除
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">抬头类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							<radio-group @change="invoiceChange" class="g-flex g-flex-start">
								<label class="uni-list-cell uni-list-cell-pd " v-for="(item, index) in invoiceOptions"
									:key="item.value">
									<view>
										<radio :value="item.value" class="cyan" style="transform: scale(0.7)"
											:checked="item.value==invoiceType" />
										<text style="margin: 0 10upx;">{{item.name}}</text>
									</view>

								</label>
							</radio-group>
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<block v-if="invoiceType == 2">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">抬头名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerName" placeholder-class="plc"
								:value="formData.buyerName" class="weui-input" placeholder="请输入抬头名称"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</block>
				<block v-if="invoiceType == 1">
					<view class="vux-x-input weui-cell company-name">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input id="name" name="qymc" v-model="companyItem.qymc" class="weui-input"
								placeholder="填写公司名称（必填）" placeholder-class="plc" @confirm="companyQuery"></input>
							<view class="more-list" v-if="isShowSelect">
								<scroll-view scroll-y="true" style="height: 383rpx;">
									<view class="more-list__item" @click="changeCompany(item)"
										v-for="(item,index) in companyList" :key="index">
										<view class="name">
											{{item.qymc}}
										</view>
										<view class="num">
											税号:{{item.nsrsbh}}
										</view>
									</view>
								</scroll-view>
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司税号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerTaxpayerNum" :value="formData.buyerTaxpayerNum"
								class="weui-input" placeholder-class="plc" placeholder="请输入纳税人识别号"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">公司地址</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerAddress" :value="formData.buyerAddress"
								class="weui-input" placeholder-class="plc" placeholder="请输入公司注册地址"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">公司电话</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerPhone" :value="formData.buyerPhone" class="weui-input"
								placeholder-class="plc" placeholder="请输入公司注册电话"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">开户银行</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerBankName" :value="formData.buyerBankName"
								class="weui-input" placeholder-class="plc" placeholder="请输入公司开户银行"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">银行账号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerBankAccount" :value="formData.buyerBankAccount"
								class="weui-input" placeholder-class="plc" placeholder="请输入银行账号"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</block>
			</view>
		</view>
		<view class="invoice-default">
			<view class="invoice-default-bd">
				<view class="title">设置默认抬头</view>
				<view class="value">每次开具其他消费发票时默认填写此抬头信息</view>
			</view>
			<view class="invoice-default-value">
				<switch class="cyan" :checked='invoiceDefaultChecked' @change='onInvoiceDefaultChange'
					style="transform:scale(0.7)" />
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="onSubmitHandle">
						保存
					</button>
				</view>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getLoginUserInfo,
		getCurrUserInfo,
		getEtcAccountInfo
	} from "@/common/storageUtil.js";
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				invoiceDefaultChecked: false, // 设置默认抬头
				isShowSelect: false,
				templateId: '', //发票抬头模版ID
				formData: {
					"id": '',
					"buyerName": "",
					"buyerTaxpayerNum": "",
					"buyerAddress": "",
					"buyerPhone": "",
					"buyerBankName": "",
					"buyerBankAccount": "",
				},
				// otherFormData: {
				// 	"email": "",
				// 	"mobilePhone": "",
				// 	"remark": ""
				// },
				invoiceType: '1',
				invoiceOptions: [{
						value: '1',
						name: '企业单位'
					},
					{
						value: '2',
						name: '个人/非企业单位'
					}
				],
				companyItem: {
					qymc: ''
				}, //选择的内容
				companyList: {},
				routeType: '', //路由类型
				id: '' //id
			};
		},
		computed: {
			commonParams() {
				return {
					custMastId: getEtcAccountInfo().custMastId,
					source: 1,
					userNo: getLoginUserInfo().userNo || ''
				}
			}
		},
		// watch: {
		// 	invoiceType(val) {
		// 		// this.isShowMore = false
		// 		this.companyItem.qymc = ''
		// 		if (val == '2') {
		// 			this.formData.buyerName = ''
		// 		} else if (val == '1') {
		// 			this.formData.buyerName = ''
		// 			this.formData.buyerTaxpayerNum = ''
		// 			this.formData.buyerAddress = ''
		// 			this.formData.buyerPhone = ''
		// 			this.formData.buyerBankName = ''
		// 			this.formData.buyerBankAccount = ''
		// 			this.formData.remark = ''
		// 		}
		// 	}
		// },
		onLoad(option) {
			console.log('option', option)
			if (option.routeType) {
				this.routeType = option.routeType
			}
			// if (option.id) {
			// 	this.id = option.id
			// }
			if (option.templateId) {
				this.templateId = Number(option.templateId)
				this.getDeatils()
			} else {
				for (let key in this.formData) {
					this.formData[key] = '';
				}
			}

			console.log('getCurrUserInfo', getCurrUserInfo())
			console.log('getLoginUserInfo', getLoginUserInfo())
		},
		methods: {
			invoiceChange(e) {
				this.invoiceType = e.detail.value
			},
			onInvoiceDefaultChange(e) {
				this.invoiceDefaultChecked = e.detail.value
			},
			modifyInput(e) {
				this.formData[e.currentTarget.id] = e.detail.value;
			},
			// otherModifyInput(e) {
			// 	this.otherFormData[e.currentTarget.id] = e.detail.value;
			// },
			changeCompany(item) {

				this.companyItem = item

				this.formData.buyerName = item.qymc;
				this.formData.buyerTaxpayerNum = item.nsrsbh;
				this.formData.buyerAddress = item.dz;
				this.formData.buyerPhone = item.dh;
				this.formData.buyerBankName = item.bankAccount;
				this.formData.buyerBankAccount = item.bankNo;

				// this.isShowMore = true
			},
			companyQuery(e) {
				console.log('e.detail.value', e.detail.value)
				let queryStr = e.detail.value
				if (queryStr.length < 2) {
					//不到2个字不搜索
					return
				}
				this.isLoading = true

				let params = {
					// data: {
					key: queryStr
					// },
				}

				let data = {
					routePath: this.$interfaces.getInvoiceCompanyInfo.method,
					bizContent: params
				};

				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('模糊查询', res)
							let result = res.data
							this.isShowSelect = true
							this.companyList = result
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			getDeatils() {
				let params = {
					tId: this.templateId,
					...this.commonParams
				}
				this.$request
					.post(this.$interfaces.otherQueryTemplate, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							if (res.data && res.data.length) {
								let result = res.data[0];
								//回填多选
								this.companyItem.qymc = result.buyerName

								for (let key in this.formData) {
									this.formData[key] = result[key] || this.formData[key];
								}

								if (this.formData.buyerTaxpayerNum) {
									this.invoiceType = '1'
								} else {
									this.invoiceType = '2'
								}
								this.invoiceDefaultChecked = result.latest == 1;
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {

						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			onValidHandle() {
				if (this.invoiceType == 2) {
					if (!this.formData.buyerName) {
						uni.showModal({
							title: '提示',
							content: '请输入抬头名称',
							showCancel: false
						})
						return
					}
					return true;
				}
				if (this.invoiceType == 1) {
					if (!this.formData.buyerName) {
						uni.showModal({
							title: '提示',
							content: '请输入公司名称',
							showCancel: false
						})
						return
					}
					if (!this.formData.buyerTaxpayerNum) {
						uni.showModal({
							title: '提示',
							content: '请输入纳税人识别号',
							showCancel: false
						})
						return
					}
					return true;
				}
			},
			onSubmitHandle() {
				if (this.invoiceType == 1) {
					this.formData.buyerName = this.companyItem.qymc;
				}
				if (!this.onValidHandle()) return;
				// if (this.routeType == 'Bissue') {
				// 	//发行自助开票
				// 	this.invoiceSelfApplyHandle()
				// } else {
				this.invoiceTemplateSave();
				// }
			},
			invoiceTemplateSave() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					...this.commonParams,
					// ...this.otherFormData,
					latest: this.invoiceDefaultChecked ? 1 : 0
				}
				let _formData = JSON.parse(JSON.stringify(this.formData));
				for (let key in _formData) {
					if (this.invoiceType == 2) {
						if (!(key == 'id' || key == 'buyerName')) {
							_formData[key] = '';
						}
					}
				}
				params = Object.assign(params, _formData)
				console.log('新增抬头', params)
				this.$request
					.post(this.$interfaces.otherTemplateSave, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							let msg = !!this.templateId ? '编辑成功' : '添加成功'
							let pages = getCurrentPages(); //获取所有页面栈实例列表
							let prevPage = pages[pages.length - 2]; //上一页页面实例
							console.log('pages', pages, prevPage)
							if (this.routeType == 'confirmInvoice' || this.routeType == 'changeTitle') {
								uni.showModal({
									title: '提示',
									content: msg,
									showCancel: false,
									success() {
										prevPage.$vm.getDefaultTitle('list')
										uni.navigateBack({
											delta: 1
										})
									}
								})
							} else {
								uni.showModal({
									title: '提示',
									content: msg,
									showCancel: false,
									success() {
										prevPage.$vm.getTitleList()
										uni.navigateBack({
											delta: 1
										})
									}
								})
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			delHandle() {
				uni.showModal({
					title: '删除提示',
					content: '确定要删除抬头吗',
					showCancel: true,
					success: (res) => {
						if (res.confirm) {
							this.del()
						}
					}
				})
			},
			del() {
				let params = {
					ids: [this.templateId],
				}
				this.$request
					.post(this.$interfaces.otherDelTemplate, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '删除成功',
								showCancel: false,
								success() {
									// uni.reLaunch({
									// 	url: '/pagesB/invoiceBusiness/otherInvoice/otherInvoiceTitle/index'
									// })
									let pages = getCurrentPages(); //获取所有页面栈实例列表
									let prevPage = pages[pages.length - 2]; //上一页页面实例
									console.log('pages', pages, prevPage)
									prevPage.$vm.getTitleList()
									uni.navigateBack({
										delta: 1
									})
								}
							})

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {

						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			// invoiceSelfApplyHandle() {
			// 	this.isLoading = true
			// 	let detail = {
			// 		...this.formData,
			// 		...this.otherFormData
			// 	}
			// 	let params = {
			// 		custMastId: this.id,
			// 		details: JSON.stringify(detail)
			// 	}
			// 	this.$request
			// 		.post(this.$interfaces.invoiceSelfApply, {
			// 			data: params
			// 		})
			// 		.then((res) => {
			// 			this.isLoading = false;
			// 			if (res.code == 200) {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: '保存成功',
			// 					showCancel: false,
			// 					success() {
			// 						uni.reLaunch({
			// 							url: '/pages/home/<USER>/p-home'
			// 						})
			// 					}
			// 				})

			// 			} else {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				})
			// 			}
			// 		})
			// 		.catch((error) => {
			// 			this.isLoading = false;
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error.msg,
			// 				showCancel: false
			// 			})
			// 		})
			// }
		}
	};
</script>
<style>
	.plc {
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #C6C6C6;
		line-height: 30rpx;
	}
</style>
<style scoped lang="scss">
	.add-invoice {
		margin-bottom: 170rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 190rpx;
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color: #0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}


	.invoice-default {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		padding: 20rpx 30rpx;
		margin-top: 20rpx;

	}

	.invoice-default .invoice-default-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;

	}

	.invoice-default .invoice-default-hd .invoice-default-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.invoice-default .invoice-default-bd {
		flex: 1;
	}

	.invoice-default .invoice-default-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.invoice-default .invoice-default-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #999999;
	}

	.invoice-default .invoice-default-value {
		text-align: right;
	}

	.company-name {
		// width: 100%;
		// position: relative;

		.more-list {
			position: fixed;
			top: 20%;
			left: 20%;
			height: 382rpx;
			background-color: #ffffff;
			border-radius: 12rpx;
			box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
			z-index: 88;
		}

		.more-list__item {
			// margin: 0 20rpx;
			padding: 20rpx 40rpx;
			// height: 120rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			text-align: left;
			border-bottom: 1rpx solid #F2F2F2;

			&:last-of-type {
				border-bottom: none;
			}

			.name {
				margin-bottom: 10rpx;
			}
		}
	}

	/deep/.weui-input,
	.weui-cell__value {
		text-align: left;
	}

	.del-btn {
		font-size: 28rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: rgba(46, 92, 250, 0.82);
		line-height: 40rpx;
	}
</style>