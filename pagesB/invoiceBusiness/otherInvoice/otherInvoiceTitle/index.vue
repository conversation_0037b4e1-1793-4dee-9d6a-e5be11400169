<template>
	<view class="toll-invoice_title">
		<view class="title" v-if="titleList.length > 0 && !isLoading">
			<view class="title__item" v-for="(item,index) in titleList" :key="index" @click="toDetail(item.id)">
				<view class="title__top">
					<view class="right">
						{{item.buyerName}} <text class="tag" v-if="item.latest == 1">默认</text>
					</view>
					<view class="left">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/title_detail_icon.png"
							style="width: 42rpx;height: 40rpx;" mode=""></image>
					</view>
				</view>
				<view class="title__content" v-if="item.buyerTaxpayerNum">
					<view class="title__info">
						<view class="label">
							税号：
						</view>
						<view class="value">
							{{item.buyerTaxpayerNum}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="no-data" v-else>
			<image src="../../../static/no_data.png" mode="" class="no-data-img"></image>
			<view class="no-data-title">暂无发票抬头，点击下方按钮新增</view>
		</view>
		<tButton :buttonList="buttonList" @confirmHandle="confirmHandle"></tButton>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import tButton from '@/pagesB/components/t-button/t-button.vue'
	import {
		getLoginUserInfo,
		getEtcAccountInfo
	} from "@/common/storageUtil.js";
	export default {
		components: {
			tLoading,
			tButton
		},
		data() {
			return {
				isLoading: false,
				buttonList: [{
					title: '新增发票抬头',
					handle: 'confirmHandle',
					type: 'primary',
				}],
				titleList: []
			}
		},
		onLoad() {
			console.log('getLoginUserInfo()', getLoginUserInfo())
			this.getTitleList()
		},
		methods: {
			toDetail(id) {
				uni.navigateTo({
					url: './addTitle?templateId=' + id
				})
			},
			getTitleList() {
				let params = {
					custMastId: getEtcAccountInfo().custMastId,
					source: '1',
					userNo: getLoginUserInfo().userNo,
					tId: ''
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.otherQueryTemplate, {
						data: params
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('抬头列表====>>>', res)
							this.titleList = res.data
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			confirmHandle() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/otherInvoice/otherInvoiceTitle/addTitle'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.toll-invoice_title {
		overflow: hidden;
		padding: 20rpx;
		padding-bottom: 180rpx;

		.title {
			&__item {
				overflow: hidden;
				background-color: $uni-bg-color;
				margin-bottom: 20rpx;
				background: #FFFFFF;
				border-radius: 10rpx;
				border: 2rpx solid #F5F5F5;
			}

			&__top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 25rpx 30rpx;
				border-bottom: 1rpx solid #F1F0F0;

				.right {
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 30rpx;
				}

				.tag {
					margin-left: 20rpx;
					background-color: #E5EFFC;
					border-radius: 12rpx;
					color: #0066E9;
					font-size: 24rpx;
					padding: 8rpx 16rpx;
				}
			}

			&__content {
				padding: 30rpx;
			}

			&__info {
				display: flex;
				margin-bottom: 20rpx;

				.label {
					flex: 0 0 90rpx;
					width: 90rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #999999;
					line-height: 42rpx;
				}

				.value {
					flex: 1;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 42rpx;
				}
			}
		}

		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			margin-top: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}
	}
</style>