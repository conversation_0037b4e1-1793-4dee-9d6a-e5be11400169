<template>
	<view class="toll-history">
		<view class="weui-form">
			<view class="weui-cells" style="padding-top: 0;">
				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label">开票时间</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="picker">
							<view class="pick-date pick-date-one">
								<picker mode="date" @change="startDateChange" :end="nowDate" fields="month"
									:value="staCreateTime" style="width: 100%;">
									<u-cell-item title=" " :arrow="false" icon="date-fill">
										<view class="monthData">{{staCreateTime}}</view>
									</u-cell-item>
								</picker>
							</view>
							<view style="margin: 0 30rpx;">至</view>
							<view class="pick-date pick-date-two">
								<picker mode="date" @change="endDateChange" :end="nowDate" fields="month"
									:value="endCreateTime" style="width: 100%;">
									<u-cell-item title=" " :arrow="false" icon="date-fill">
										<view class="monthData">{{endCreateTime}}</view>
									</u-cell-item>
								</picker>
							</view>
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell" style="width:100%;height: 96rpx;">
					<view class="weui-cell__hd">
						<view class="weui-label">消费类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary" @click="showRecordType = true">
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-input" style="color: #333333;">
								{{typeName}}
							</view>
						</view>
					</view>
					<image src="../../../static/down.png" mode="" style="width: 40rpx;height: 40rpx;"></image>
				</view>
				<view class="vux-x-input weui-cell"
					style="width:100%;height: 162rpx;flex-direction: column;justify-content: center;">
					<view class="btn-search" @click="onSearchHandle">
						查询
					</view>
				</view>
			</view>
		</view>
		<!-- 滚动内容 -->
		<scroll-view v-if="orderList.length > 0 && !isLoading" :style="{height:height}" :scroll-top="scrollTop"
			scroll-y="true" class="scroll-Y" :lower-threshold='lowerThreshold' @scrolltoupper="upper"
			@scrolltolower="scrolltolower" @scroll="scroll">
			<view class="order-list">
				<view class="order-item" v-for="(item,index) in orderList" :key="item.invOrderId">
					<view class="item-title">
						发票总金额<text class="price">￥{{moneyFilter(item.totalFee)}}</text>
					</view>
					<view class="item-container">
						<view class="item-bd">
							<view class="item-label">
								发票类型：
							</view>
							<view class="item-value">
								{{getType(item.invoiceApplyType,otherRecordAllType)}}
							</view>
						</view>
						<view class="item-bd">
							<view class="item-label">
								开票时间：
							</view>
							<view class="item-value">
								{{formatHandle(new Date(item.createdTime).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
							</view>
						</view>
						<view class="item-bd">
							<view class="item-label">
								票据单号：
							</view>
							<view class="item-value">
								{{item.invoiceNum}}
							</view>
						</view>
						<view class="item-bd">
							<view class="item-label">
								申请渠道：
							</view>
							<view class="item-value">
								{{getType(item.channel,channelType)}}
							</view>
						</view>
					</view>
					<view class="item-status" :class="'item-status-' + item.invoiceType">
						<!-- {{orderListData.nodeName}} -->
						<!-- {{getTollInvoiceStatus(item.invoiceStatus)}} -->
						{{item.invoiceType=='0'?'已完成':'已红冲'}}
					</view>
					<view class="btn-container">
						<view class="right">
							<template>
								<view class="item-btn info" @click.stop="invoiceDetail(item)">
									开票详情
								</view>
							</template>
							<template>
								<view class="item-btn primary" :key="item.invOrderId"
									:class="timerData[item.invOrderId].countDown && timerData[item.invOrderId].countDown != 60 ? 'gray-back' : ''"
									@click.stop="reSendHandle(item)" v-if="item.invoiceType == '0'">
									{{!timerData[item.invOrderId].countDown? '重推发票': timerData[item.invOrderId].countDown +'S' }}
								</view>
							</template>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="no-data" v-if="orderList.length == 0 && !isLoading">
			<image src="../../../static/no_data.png" mode="" class="no-data-img"></image>
			<view class="no-data-title">暂无记录</view>
		</view>
		<confirmDialog :show.sync="isShowConfirm" :rightBtnText="'确认提交'" @comfirm="reSend">
			<template v-slot:content>
				<view class="content">
					<view class="tips">
						请确认发票接收邮箱
					</view>
					<view class="title-name">
						<input type="text" v-model="itemList.email" placeholder="请输入发票接收的邮箱">
					</view>
					<view class="tips">
						请留意邮箱信息
					</view>
				</view>
			</template>
		</confirmDialog>
		<u-select v-model="showRecordType" :list="otherRecordAllType" :defaultValue="[defaultValue]"
			@confirm="typeConfirm">
		</u-select>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import confirmDialog from '../../components/confirm-dialog.vue';
	import {
		getEtcAccountInfo
	} from '@/common/storageUtil.js';
	import {
		moneyFilter,
		formatHandle,
		checkEmailReg
	} from '@/common/util.js'
	import {
		mapState
	} from 'vuex';
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')

	function getDate(type) {
		const date = new Date();
		console.log(date)
		let year = date.getFullYear();
		let month = date.getMonth() + 1;
		// let day = date.getDate();

		if (type === 'start') {
			year = year - 1;
		} else if (type === 'end') {
			year = year;
		}
		// if (type === 'start') {
		// 	month = '1';
		// } else if (type === 'end') {
		// 	month = month;
		// }
		month = month > 9 ? month : '0' + month;
		// day = day > 9 ? day : '0' + day;

		return `${year}-${month}`;
	}
	export default {
		components: {
			tLoading,
			confirmDialog
		},
		options: {
			styleIsolation: 'shared'
		},
		data() {
			return {
				isLoading: false,
				showRecordType: false,
				isShowConfirm: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				height: 'calc(100% - 334rpx)',
				otherRecordAllType: [{
						label: '全部',
						value: '',
					},
					{
						label: '权益服务费/设备费',
						value: '5',
					},
					{
						label: '次次顺技术支持服务费',
						value: '4',
					},
					{
						label: '月结服务费/滞纳金',
						value: '3',
					},
					{
						label: '注销违约金',
						value: '6',
					},
				],
				//0 柜面 1 网厅 2 C端 3 B端
				channelType: [{
						label: '网点柜面',
						value: '0',
					},
					{
						label: '电脑端网厅自助',
						value: '1',
					},
					{
						label: '手机端小程序自助',
						value: '2',
					},
					{
						label: '移动业务端',
						value: '3',
					},
				],
				orderList: [],
				typeList: [],
				nowDate: '',
				defaultValue: '0',
				staCreateTime: getDate('start'), //开始提交时间
				endCreateTime: getDate('end'), //结束提交时间
				typeName: '权益服务费/设备费',
				invoiceApplyType: '5',
				pageNum: 1,
				pageSize: 500,
				itemList: {},
				// timerListData: {}
			}
		},
		computed: {
			...mapState({
				timerData: state => state.invoice.timerData
			}),
			etcInfo() {
				return getEtcAccountInfo() || {}
			},
		},
		onLoad(option) {
			this.nowDate = dayjs(new Date()).format('YYYY-MM');
			this.getHistory()
		},
		methods: {
			moneyFilter,
			formatHandle,
			getType(val, typeList) {
				for (let i = 0; i < typeList.length; i++) {
					if (val && typeList[i].value == val) {
						return typeList[i].label;
					}
				}
				return ''
			},
			typeConfirm(obj) {
				this.typeName = obj[0].label;
				this.invoiceApplyType = obj[0].value;
			},
			onSearchHandle() {
				this.getHistory()
			},
			startDateChange(e) {
				console.log('e===>>>>', e)
				this.staCreateTime = e.detail.value
			},
			endDateChange(e) {
				console.log('e===>>>>', e)
				this.endCreateTime = e.detail.value
			},
			formatData(params) {
				//开始年份和开始月份
				let strCurrentYear = dayjs(this.staCreateTime).year();
				let strCurrentMonth = dayjs(this.staCreateTime).month() + 1;
				strCurrentMonth = strCurrentMonth > 9 ? strCurrentMonth : '0' + strCurrentMonth;
				console.log('strCurrentYear', strCurrentYear, strCurrentMonth)
				params.startDate = strCurrentYear + '' + strCurrentMonth + '01'
				// params.startDate = '20240101'
				//结束年份和结束月份，结束月份最大天数
				let maxDaysInMonth = dayjs(this.endCreateTime).daysInMonth();
				let currentYear = dayjs(this.endCreateTime).year();
				let currentMonth = dayjs(this.endCreateTime).month() + 1;
				currentMonth = currentMonth > 9 ? currentMonth : '0' + currentMonth;
				maxDaysInMonth = maxDaysInMonth > 9 ? maxDaysInMonth : '0' + maxDaysInMonth;

				params.endDate = currentYear + '' + currentMonth + '' + maxDaysInMonth
				// params.endDate = '20241231'

				return params
			},
			async getHistory() {
				this.isLoading = true

				let params = {
					customerId: this.etcInfo.custMastId,
					startDate: this.staCreateTime,
					endDate: this.endCreateTime,
					invoiceApplyType: this.invoiceApplyType,
					pageNum: this.pageNum,
					pageSize: this.pageSize,
				}
				params = await this.formatData(params)

				console.log('params==>>', params)
				this.$request
					.post(this.$interfaces.otherInvHistory, {
						data: params
					})
					.then((res) => {
						console.log('res', res)
						this.isLoading = false;
						if (res.code == 200) {
							this.orderList = res.data.data
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			invoiceDetail(item) {
				console.log('item', item)
				// this.$store.dispatch(
				// 	'invoice/setHistoryDetail', {
				// 		...item,
				// 		month: this.formData.applyTime,
				// 	}
				// )
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/otherInvoice/otherHistory/historyDetail?orderList=' +
						encodeURIComponent(JSON
							.stringify(item))
				})
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				// if (this.flag) return;
				// let self = this;
				// setTimeout(function() {
				// 	self.formData.page = self.formData.page + 1;
				// 	self.getList();
				// }, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
			reSendHandle(item) {
				if (this.timerData[item.invOrderId] && this.timerData[item.invOrderId].countDown) {
					return
				}
				// uni.showModal({
				// 	title: '重推提示',
				// 	content: '确定要重推发票吗？',
				// 	success: (res) => {
				// 		if (res.confirm) {
				// 			this.resend(item)
				// 		}
				// 	}
				// })
				this.itemList = item
				this.isShowConfirm = true
			},
			reSend() {
				if (!checkEmailReg(this.itemList.email)) {
					uni.showModal({
						title: '提示',
						content: '请输入正确的邮箱格式!',
						showCancel: false
					})
					return
				}
				this.isShowConfirm = false
				this.isLoading = true
				let params = {
					customerId: this.etcInfo.custMastId,
					invOrderId: this.itemList.invOrderId,
					email: this.itemList.email,
					sendType: '1',
				}
				console.log('params', params)
				this.$request
					.post(this.$interfaces.otherReSend, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							if (res.data.message == '1') {
								uni.showModal({
									title: '提示',
									content: '请勿频繁操作,60s后再进行重推操作',
									showCancel: false
								})
								return
							}
							//定时任务数据
							let id = this.itemList.invOrderId
							let timerListData = getApp().globalData.timerListData
							console.log('timerListData', timerListData)
							timerListData[id] = {
								countDown: 60,
								timer: null
							}
							this.$store.dispatch('invoice/setTimer', {
								timerListData: timerListData,
								id: id
							})
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/otherInvoice/common/resultPage?type=4&descType=reSend'
							})
						} else {
							let errorText = '错误码【' + res.code + '】，' + res.msg
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/otherInvoice/common/resultPage?type=5&descType=reSend&errorText=' +
									errorText
							})
							// uni.showModal({
							// 	title: '提示',
							// 	content: res.msg,
							// 	showCancel: false
							// })
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.gray-back {
		background-color: #999999 !important;
		border: 0 !important;
	}

	.toll-history {
		overflow: hidden;
		// position: ;
		width: 100%;
		height: 100%;
		// height: calc(100% - 134rpx);
		padding: 20rpx 0;

		.line-block {
			height: 20rpx;
			width: 100%;
			background-color: #F6F6F6;
		}

		.weui-form {
			height: 354rpx;
			min-height: 354rpx;
			margin-top: 0rpx;
			box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.06);
		}

		.picker {
			width: 100%;
			display: flex;
			height: 100%;
			align-items: center;
		}

		/deep/.u-border-bottom::after {
			border-bottom: none;
		}

		.pick-date {
			width: 192rpx;
			display: flex;
			align-items: center;

			/deep/.u-cell {
				position: relative;
			}

			/deep/.u-cell__value {
				font-size: 30rpx !important;
			}

			/deep/.u-cell__left-icon-wrap {
				position: absolute;
				right: 0;
				margin-right: 0px !important;
			}

			/deep/.u-icon__icon {
				font-size: 25rpx !important;
				color: #999999;
			}
		}

		.pick-date-two {
			// flex: 1;
		}

		/deep/.u-cell {
			padding: 0 0;
			line-height: 80rpx;
		}

		/deep/.u-cell__value {
			color: #333;
			text-align: left;
			font-size: 30rpx;
		}

		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;
			margin-top: 20rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}

		.weui-cell .tips {
			margin: 16rpx 0 24rpx 0;
			text-align: center;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #FF9038;
			line-height: 33rpx;
		}

		/deep/.status-dialog__content {
			top: calc(50% - 400rpx);
			height: 500rpx;
		}

		.content {
			width: 100%;
			padding: 40rpx;

			.tips {
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #888888;
				line-height: 50rpx;
				text-align: left;
			}

			.title-name {
				// height: 76rpx;
				margin: 35rpx 0;
				padding: 14rpx;
				background: #ffffff;
				border-radius: 8rpx;
				border: 2rpx solid #DDDDDD;
				border-radius: 8rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 50rpx;
			}

			.btn-wrapper {
				display: flex;
				width: 100%;
				margin-top: 50rpx;

				.btn-center {
					width: 232rpx;
					height: 71rpx;
					line-height: 71rpx;
					background: #0066E9;
					border-radius: 14rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					text-align: center;
				}
			}
		}
	}

	.weui-cells {
		padding-top: 0;
	}

	.weui-cell {
		padding: 0rpx 30rpx;
	}

	.weui-cells::before {
		border: 0;
	}

	.weui-label {
		width: 180rpx;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 96rpx;
	}

	.weui-cell_picker .weui-picker-value {
		text-align: left;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 96rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
		height: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 30rpx;
	}

	.weui-cell:before {
		right: 30rpx;
	}

	.btn-search {
		width: 100%;
		height: 79rpx;
		line-height: 79rpx;
		text-align: center;
		background: #0066E9;
		color: #ffffff;
		border-radius: 14rpx;
	}

	.order-list {
		padding: 20rpx;
	}

	.order-item {
		position: relative;
		// margin: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 12rpx;
		background-color: #ffffff;
		font-family: PingFangSC-Medium, PingFang SC;
		// overflow: hidden;
		padding: 6rpx 0rpx 11rpx 0rpx;
	}

	.item-title {
		position: relative;
		margin: 22rpx 40rpx;
		height: 45rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		line-height: 45rpx;

		&:before {
			content: ' ';
			position: absolute;
			left: -39rpx;
			top: 8rpx;
			width: 8rpx;
			height: 30rpx;
			background-color: #333333;
		}
	}

	.item-title .price {
		margin-left: 20rpx;
		font-size: 32rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #FF9100;
		line-height: 45rpx;
	}

	.item-status {
		position: absolute;
		right: 0;
		top: 0;
		width: 169rpx;
		padding: 0 16rpx;
		height: 63rpx;
		border-radius: 0rpx 10rpx 0rpx 30rpx;
		text-align: center;
		line-height: 63rpx;
		font-size: 26rpx;
	}

	// 待支付，已取消，已退货退款，已退货不退款
	.item-status.info {
		background: rgba(133, 134, 134, 0.15);
		color: #6A6969;
	}

	// 设备已发货，审核通过，已完结，已签收，换货审核通过，退货审核通过，设备已寄回
	.item-status-0 {
		background: rgba(0, 189, 50, 0.11);
		color: #00BD32;
	}

	// 换货审核中，退货审核中，待取货
	.item-status-1,
	.item-status-5 {
		background: rgba(255, 145, 0, 0.14);
		color: #FF9100;
	}

	// 后台审核中
	.item-status.primary {
		background: rgba(0, 102, 233, 0.12);
		color: #0066E9;
	}

	// 审核不通过，换货审核不通过，退货审核不通过
	.item-status-1,
	.item-status-4 {
		background: rgba(255, 84, 84, 0.15);
		color: #FF5454;
	}

	.item-container {
		margin: 38rpx 40rpx 30rpx 40rpx;
	}

	.border-line {
		padding-bottom: 20rpx;
		border-bottom: 1rpx dashed #C3C3C3;
	}

	.item-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.item-label {
		flex: 0 0 160rpx;
		width: 160rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.item-value {
		flex: 1;
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
	}

	.btn-container {
		display: flex;
		justify-content: space-between;
		margin: 8rpx 40rpx;
		padding-top: 30rpx;
		border-top: 2rpx dashed #C3C3C3;
	}

	.left {}

	.right {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		flex-wrap: wrap;
	}

	.right .more-btn {
		width: 88rpx;
		position: relative;

		.arrow-top {
			position: absolute;
			top: 60rpx;
			left: 24rpx;

			.triangle {
				position: relative;
				width: 40rpx;
				height: 20rpx;
				overflow: hidden;

				&::before {
					content: "";
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: #FFFFFF;
					transform-origin: left bottom;
					box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
					transform: rotate(45deg);
					z-index: 2;
				}
			}
		}

		.more-list {
			width: 204rpx;
			position: absolute;
			top: 40px;
			left: 0;
			background-color: #ffffff;
			border-radius: 12rpx;
			box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
			z-index: 1;
		}

		.more-list__item {
			margin: 0 20rpx;
			height: 92rpx;
			line-height: 92rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			text-align: left;
			border-bottom: 1rpx solid #F2F2F2;

			&:last-of-type {
				border-bottom: none;
			}
		}
	}


	.right .item-btn {
		margin-left: 20rpx;

		&:nth-child(2n) {
			margin-bottom: 15rpx;
		}
	}

	.right .item-btn:first-child {
		margin-left: 0;
	}

	.item-btn {
		// padding: 12rpx 40rpx;
		width: 190rpx;
		height: 58rpx;
		line-height: 58rpx;
		border-radius: 36rpx;
		font-size: 26rpx;
		text-align: center;
	}

	.item-btn.info {
		border: 2rpx solid #E8E8E8;
		color: #323435;
		background: #FFFFFF;
	}

	.item-btn.primary {
		border: 2rpx solid #0066E9;
		color: #FFFFFF;
		background: #0066E9;
	}

	.calendar-wrapper {
		height: 700rpx;

		.calendar-title {
			justify-content: space-between;
			height: 98rpx;
			line-height: 98rpx;
			padding: 0 36rpx 0 44rpx;
			border-bottom: 1rpx solid #EEEEEE;

			.cancel {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 42rpx;
			}

			.confirm {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #0066E9;
				line-height: 42rpx;
			}
		}

		.calendar-year {
			height: 82rpx;
			line-height: 82rpx;
			font-size: 36rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #0066E9;
			text-align: center;
			background-color: #f8f8f8;
		}

		.calendar-month {

			padding: 0rpx 16rpx 54rpx 16rpx;

			.month {
				display: inline-block;
				height: 45rpx;
				width: 94rpx;
				margin-right: 30rpx;
				margin-top: 50rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: 45rpx;
				text-align: center;
			}

			.month:nth-child(6),
			.month:nth-child(12) {
				margin-right: 0;
			}

			.selector {
				background: #0066E9;
				border-radius: 6rpx;
				color: #FFFFFF !important;
			}
		}

		.calendar-bottom {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 84rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 33rpx;
			background-color: #f8f8f8;


			.bottom-left,
			.bottom-right {
				display: flex;
				align-items: center;
			}

			.bottom-left {
				margin-right: 50rpx;
			}

			.circle {
				width: 15rpx;
				height: 15rpx;
				margin-right: 10rpx;
				background: #0066E9;
				border-radius: 50%;

			}

			.red {
				background: #F65B5B;
			}

		}
	}

	.picker {
		width: 100%;
		display: flex;
		height: 100%;
		align-items: center;
	}

	/deep/.u-border-bottom::after {
		border-bottom: none;
	}

	.pick-date {
		width: 192rpx;
		display: flex;
		align-items: center;

		/deep/.u-cell {
			position: relative;
			padding: 0 !important;
		}

		/deep/.u-cell__value {
			font-size: 30rpx !important;
		}

		/deep/.u-cell__left-icon-wrap {
			position: absolute;
			right: 0;
			margin-right: 0px !important;
		}

		/deep/.u-icon__icon {
			font-size: 25rpx !important;
			color: #999999;
		}
	}

	.pick-date-two {
		// flex: 1;
	}
</style>