<template>
	<view class="history-detail">
		<view class="top-title">
			<view class="title-left">
				已开发票
			</view>
			<view class="title-right">
				<view class="count">
					<text class="count-num"
						v-if="Object.keys(orderList).length > 0">{{orderList.invOrderDetails.length}}</text>张
				</view>
				<view class="count">
					共计<text class="count-num">{{moneyFilter(orderList.totalFee + '')}}</text>元
				</view>
			</view>
		</view>
		<!-- 		<scroll-view :style="{height:height}" :scroll-top="scrollTop" scroll-y="true" class="scroll-Y"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll"> -->
		<view class="order-list">
			<view class="order-item">
				<view class="item-container">
					<view class="item-bd">
						<view class="item-label">
							开票日期：
						</view>
						<view class="item-value">
							{{formatHandle(new Date(orderList.createdTime).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
						</view>
					</view>
					<view class="item-bd">
						<view class="item-label">
							发票金额：
						</view>
						<view class="item-value">
							<text style="color: #FF9100;">{{moneyFilter(orderList.totalFee)}}</text>元
						</view>
					</view>
					<view class="item-bd">
						<view class="item-label">
							发票编号：
						</view>
						<view class="item-value">
							{{orderList.invoiceNum}}
						</view>
					</view>
					<view class="item-bd">
						<view class="item-label">
							发票抬头：
						</view>
						<view class="item-value">
							{{orderList.buyerName}}
						</view>
					</view>
					<view class="item-bd">
						<view class="item-label">
							收款方：
						</view>
						<view class="item-value">
							<!-- {{item.sellerName}} -->
							广西捷通高速科技有限公司
						</view>
					</view>
				</view>
				<!-- 					<view class="more-icon" v-if="index == 1 && dataList.length > 2" @click="toggle">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_more_down.png" mode=""></image>
					</view> -->
				<view class="preview" @click="toPreview()">
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_preview.png" style="width: 40rpx;height:40rpx;"
						mode=""></image>
				</view>
			</view>
		</view>
		<!-- </scroll-view> -->
		<view class="price-wrapper" @click="toRecord">
			<view class="label">
				包含的消费记录
			</view>
			<!-- <view class="price">
				<text>{{moneyFilter(vehicleInfo.totalPrice)}}</text> 元
			</view> -->
			<view class="record">
				<view class="record-text">
					<text style="color: #FF9100;"
						v-if="Object.keys(orderList).length > 0">{{orderList.invOrderDetails.length}}</text>条
					<!-- <text style="color: #FF9100;">2</text>条 -->
				</view>
				<image style="width: 14rpx;height: 26rpx;margin-left: 10rpx;"
					src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_arrow_right.png" mode=""></image>
			</view>
		</view>
		<view class="price-wrapper">
			<view class="label">
				发票接收邮箱
			</view>
			<!-- <view class="price">
				<text>{{moneyFilter(vehicleInfo.totalPrice)}}</text> 元
			</view> -->
			<view class="record">
				<view class="record-text">
					{{orderList.email}}
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed" v-if="orderList.invoiceType == '0'">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary"
						:class="timerData[orderList.invOrderId].countDown && timerData[orderList.invOrderId].countDown != 60 ? 'gray-back' : ''"
						@click="reSendHandle">
						{{!timerData[orderList.invOrderId].countDown? '重推发票': timerData[orderList.invOrderId].countDown +'S' }}
					</button>
				</view>
				<view class="btn-text" @click="menuShow = true">
					发票信息有误？
				</view>
			</view>
		</view>
		<confirmDialog :show.sync="isShowConfirm" :rightBtnText="'确认提交'" @comfirm="reSend">
			<template v-slot:content>
				<view class="content">
					<view class="tips">
						请确认发票接收邮箱
					</view>
					<view class="title-name">
						<input type="text" v-model="invoiceMail" placeholder="请输入发票接收的邮箱">
					</view>
					<view class="tips">
						请留意邮箱信息
					</view>
				</view>
			</template>
		</confirmDialog>
		<u-action-sheet @click="menuClick" :list="menuList" v-model="menuShow"></u-action-sheet>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import confirmDialog from '../../components/confirm-dialog.vue';
	import {
		getTollTicket
	} from '@/common/storageUtil.js';
	import {
		tollInvoiceType,
		tollInvoiceStatus
	} from "@/common/const/optionData.js";
	import {
		getTollInvoiceType,
		getTollInvoiceStatus
	} from '@/common/method/filter.js'
	import {
		moneyFilter,
		formatHandle,
		checkEmailReg
	} from '@/common/util.js'
	import {
		mapState
	} from 'vuex';
	export default {
		components: {
			tLoading,
			confirmDialog
		},
		data() {
			return {
				isLoading: false,
				isShowConfirm: false,
				menuShow: false,
				orderList: {},
				invoiceMail: '',
				menuList: [{
						text: '发票红冲'
					},
					{
						text: '申请换票'
					},
				],
			}
		},
		computed: {
			...mapState({
				timerData: state => state.invoice.timerData
			})
		},
		onLoad(option) {
			// this.getDetail()
			if (option.orderList) {
				const item = JSON.parse(decodeURIComponent(option.orderList));
				console.log('item', item)
				this.orderList = item
				this.invoiceMail = this.orderList.email
			}
		},
		methods: {
			moneyFilter,
			formatHandle,
			toggle() {
				this.isShowMore = !this.isShowMore
			},
			toChangeConfirm() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/otherInvoice/otherHistory/changeConfirm'
				})
			},
			menuClick(index) {
				if (index == 0) {
					//红冲
					uni.navigateTo({
						url: '/pagesB/invoiceBusiness/otherInvoice/otherHistory/changeConfirm?type=red&orderList=' +
							encodeURIComponent(JSON
								.stringify(this.orderList))
					})
				} else {
					//换票
					uni.navigateTo({
						url: '/pagesB/invoiceBusiness/otherInvoice/otherHistory/changeConfirm?type=change&orderList=' +
							encodeURIComponent(JSON
								.stringify(this.orderList))
					})
				}
			},
			toPreview(item) {
				// console.log('item', item)
				// const callcenter = item.invoiceHtmlUrl
				// uni.navigateTo({
				// 	url: '/pages/uni-webview/uni-webview?ownPath=' +
				// 		encodeURIComponent(callcenter)
				// })
				uni.setClipboardData({
					data: this.orderList.url,
					success: () => {
						uni.showModal({
							title: '复制',
							content: '复制发票链接成功，请自行使用手机浏览器预览下载',
							showCancel: false,
						})
					}
				});
			},
			toRecord() {
				let orderInfo = {
					invoiceApplyType: this.orderList.invoiceApplyType,
					totalCount: this.orderList.invOrderDetails.length,
					totalPrice: this.orderList.totalFee,
					orderList: this.orderList.invOrderDetails
				}
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/otherInvoice/invoiceRecord/recordDetail?orderInfo=' +
						encodeURIComponent(JSON
							.stringify(orderInfo))
				})
			},
			reSendHandle(item) {
				this.isShowConfirm = true
			},
			reSend() {
				if (!checkEmailReg(this.invoiceMail)) {
					uni.showModal({
						title: '提示',
						content: '请输入正确的邮箱格式!',
						showCancel: false
					})
					return
				}
				this.isShowConfirm = false
				this.isLoading = true
				let params = {
					customerId: this.orderList.custMastId,
					invOrderId: this.orderList.invOrderId,
					email: this.invoiceMail,
					sendType: '1',
				}
				this.$request
					.post(this.$interfaces.otherReSend, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							if (res.data.message == '1') {
								uni.showModal({
									title: '提示',
									content: '请勿频繁操作,60s后再进行重推操作',
									showCancel: false
								})
								return
							}
							//定时任务数据
							let id = this.orderList.invOrderId
							let timerListData = getApp().globalData.timerListData
							// if (!timerListData.hasOwnProperty(id)) {
							timerListData[id] = {
								countDown: 60,
								timer: null
							}
							// }
							this.$store.dispatch('invoice/setTimer', {
								timerListData: timerListData,
								id: id
							})
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/otherInvoice/common/resultPage?type=4&descType=reSend'
							})
						} else {
							let errorText = '错误码【' + res.code + '】，' + res.msg
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/otherInvoice/common/resultPage?type=5&descType=reSend&errorText=' +
									errorText
							})
							// uni.showModal({
							// 	title: '提示',
							// 	content: res.msg,
							// 	showCancel: false
							// })
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				// if (this.flag) return;
				// let self = this;
				// setTimeout(function() {
				// 	self.formData.page = self.formData.page + 1;
				// 	self.getList();
				// }, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
		},

	}
</script>

<style scoped lang="scss">
	.gray-back {
		background-color: #999999 !important;
	}

	.history-detail {
		overflow: hidden;
		height: 100%;

		.top-title {
			background-color: $uni-bg-color;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 88rpx;
			line-height: 88rpx;
			padding: 0 30rpx;

			.title-left {
				padding-left: 15rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 28rpx;
			}

			.title-right {
				display: flex;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				color: #666666;
				line-height: 28rpx;

				.count {
					margin-left: 20rpx;

					.count-num {
						color: #FF5454;

					}
				}
			}
		}


		.order-list {
			padding: 20rpx;
			background-color: #ffffff;
		}

		.order-item {
			position: relative;
			// margin: 20rpx;
			margin-bottom: 20rpx;
			border-radius: 12rpx;
			background-color: #ffffff;
			font-family: PingFangSC-Medium, PingFang SC;
			border: 2rpx solid #F5F5F5;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.item-title {
			position: relative;
			margin: 22rpx 40rpx;
			height: 45rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
			line-height: 45rpx;

			&:before {
				content: ' ';
				position: absolute;
				left: -39rpx;
				top: 8rpx;
				width: 8rpx;
				height: 30rpx;
				background-color: #333333;
			}
		}

		.item-title .price {
			margin-left: 20rpx;
			font-size: 32rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #FF9100;
			line-height: 45rpx;
		}

		.item-container {
			margin: 30rpx;
			// padding-bottom: 20rpx;
			// border-bottom: 1rpx dashed #C3C3C3;
		}

		.item-bd {
			display: flex;
			margin-bottom: 20rpx;
		}

		.item-label {
			width: 160rpx;
			font-size: 30rpx;
			font-weight: 400;
			color: #999999;
		}

		.item-value {
			flex: 1;
			font-size: 30rpx;
			font-weight: 400;
			color: #333333;
		}

		.preview {
			position: absolute;
			right: 30rpx;
			top: 28rpx;
		}

		.price-wrapper {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 20rpx;
			padding: 26rpx 30rpx;
			background-color: $uni-bg-color;

			.label {
				width: 300rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #666666;
				line-height: 30rpx;
			}

			.record {
				display: flex;
				align-items: center;

				.record-text {
					// margin-right: 10rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #666666;
					line-height: 30rpx;
				}
			}

		}

		.scroll-Y {
			.more-icon {
				background-color: #ffffff;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 83rpx;
				// padding-top: 20rpx;
				border-top: 2rpx solid #F5F5F5;

				&>image {
					width: 39rpx;
					height: 29rpx;
				}
			}
		}

		.content {
			width: 100%;
			padding: 40rpx;

			.tips {
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #888888;
				line-height: 50rpx;
				text-align: left;
			}

			.title-name {
				// height: 76rpx;
				margin: 35rpx 0;
				padding: 14rpx;
				background: #ffffff;
				border-radius: 8rpx;
				border: 2rpx solid #DDDDDD;
				border-radius: 8rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 50rpx;
			}

			.btn-wrapper {
				display: flex;
				width: 100%;
				margin-top: 50rpx;

				.btn-center {
					width: 232rpx;
					height: 71rpx;
					line-height: 71rpx;
					background: #0066E9;
					border-radius: 14rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					text-align: center;
				}
			}
		}

		.weui-bottom-fixed__box {
			display: flex;
			flex-direction: column;
			align-items: normal;
			justify-content: center;
			padding: 20rpx 60rpx 60rpx 60rpx;
		}

		.btn-text {
			margin-top: 15rpx;
			text-align: center;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #F65B5B;
			line-height: 33rpx;
		}

		.bottom-box {
			display: flex;
		}

		.bottom-box .btn-item {
			flex: 1;
		}
	}
</style>