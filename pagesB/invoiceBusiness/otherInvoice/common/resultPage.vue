<template>
	<view class="order-success g-flex g-flex-center g-flex-align-center g-flex-column">
		<!-- <handle-step :current="4" /> -->
		<template v-if="contentList[type].error">
			<image class="img" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/error.png" mode=""></image>
			<view class="desc">
				{{contentList[type].content}}
			</view>
			<view class="desc">
				{{contentList[type].descList[descType] + '：' + errorText}}
			</view>
		</template>
		<template v-else>
			<image class="img" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/suc.png" mode=""></image>
			<view class="success-desc">
				{{contentList[type].content}}
			</view>
			<view class="success-desc" v-if="descType != 'reSend'">
				{{contentList[type].content1}}
			</view>
		</template>
		<tButton :buttonList="buttonList" @toHome="toHome" @toBindTitle="toBindTitle" @toHistory="toHistory"></tButton>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	// import handleStep from "@/components/t-handleStep/handleStep.vue"
	import tButton from '@/pagesB/components/t-button/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import {
		successBtnList
	} from '@/pagesB/invoiceBusiness/otherInvoice/common/optionsData.js';

	export default {
		components: {
			// handleStep,
			tButton,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				type: '',
				applyId: '',
				saleId: '',
				buttonList: [],
				contentList: successBtnList,
				vehicleInfo: {},
				customerId: '',
				descType: '',
				errorText: ''
			}
		},
		onLoad(options) {
			if (options) {
				if (options.type) {
					this.type = options.type
					this.buttonList = this.contentList[this.type].button
					console.log('this.buttonList', this.buttonList)
				}
				// if (options.vehicleInfo) {
				// 	const item = JSON.parse(decodeURIComponent(options.vehicleInfo));
				// 	console.log('item', item)
				// 	this.vehicleInfo = item
				// }

				if (options.customerId) {
					this.customerId = options.customerId
				}

				if (options.descType) {
					this.descType = options.descType
					if (this.descType == 'open') {
						uni.setNavigationBarTitle({
							title: '开票申请结果'
						});
					} else if (this.descType == 'reSend') {
						uni.setNavigationBarTitle({
							title: '发票重推操作结果'
						});
					} else if (this.descType == 'changeTitle') {
						uni.setNavigationBarTitle({
							title: '发票抬头更换申请结果'
						});
					} else if (this.descType == 'red') {
						uni.setNavigationBarTitle({
							title: '发票红冲申请结果'
						});
					}
				}

				if (options.errorText) {
					this.errorText = options.errorText
				}

			}
		},
		methods: {
			toHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>/p-home'
				})
			},
			// toBindTitle() {
			// 	uni.reLaunch({
			// 		url: '/pagesB/invoiceBusiness/tollInvoice/tollInvoiceTitle/addTitle?routeType=bindVehicle&vehicleInfo=' +
			// 			encodeURIComponent(JSON
			// 				.stringify(this.vehicleInfo))
			// 	})
			// },
			toHistory() {
				uni.reLaunch({
					url: '/pagesB/invoiceBusiness/otherInvoice/otherHistory/index'
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.order-success {
		// padding-top: 60rpx;
		background-color: $uni-bg-color;
		margin: 20rpx;
		padding-bottom: 60rpx;

		.success-desc {
			padding: 0 50rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;
			width: 100%;
		}

		.img {
			width: 452rpx;
			height: 437rpx;
		}

		.desc {
			padding: 10rpx 40rpx;
		}
	}
</style>