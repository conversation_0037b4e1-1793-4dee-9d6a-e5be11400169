<template>
	<view class="confirm-invoice" @click="isShowSelect = false">
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="weui-cells__title__decoration">
					发票详情
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">抬头类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							<radio-group @change="invoiceChange" class="g-flex g-flex-end">
								<label class="uni-list-cell uni-list-cell-pd " v-for="(item, index) in invoiceOptions"
									:key="item.value">
									<view>
										<radio :value="item.value" class="cyan" style="transform: scale(0.7)"
											:checked="item.value == userTypeIndexSub" />
										<text>{{item.name}}</text>
									</view>

								</label>
							</radio-group>
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<template v-if="userTypeIndexSub=='2'">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">姓名</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input id="name" v-model="formData.buyerName" class="weui-input" placeholder="请输入姓名"
								placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft" @click="openSelect">
							<text class="cuIcon-sort" style="font-size: 40rpx;"></text>
						</view>
					</view>
				</template>
				<template v-if="userTypeIndexSub=='1'">
					<view class="vux-x-input weui-cell company-name">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input id="name" name="qymc" v-model="companyItem.qymc" class="weui-input"
								placeholder="填写公司名称（必填）" placeholder-class="plc" @input="onKeyInput"></input>
							<view class="more-list" v-if="isShowSelect">
								<scroll-view scroll-y="true" style="height: 383rpx;">
									<view class="more-list__item" @click="changeCompany(item)"
										v-for="(item,index) in companyList" :key="index">
										<view class="name">
											{{item.qymc}}
										</view>
										<view class="num">
											税号:{{item.nsrsbh}}
										</view>
									</view>
								</scroll-view>
							</view>
						</view>
						<view class="weui-cell__ft" @click="openSelect">
							<text class="cuIcon-sort" style="font-size: 40rpx;"></text>
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司税号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input id="taxNum" v-model="formData.buyerTaxpayerNum" class="weui-input"
								placeholder="填写公司税号（必填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell" v-if="!isShowMore">
						<view class="weui-cell__hd">
							<view class="weui-label" style="color: #666666;">更多内容</view>
						</view>
						<view @click="isShowMore = true" class="weui-cell__bd weui-cell__primary"
							style="text-align: right;margin-right: 15rpx;">
							共 <text style="color: #FF9038;">5</text> 项
						</view>
						<image style="width: 14rpx;height: 26rpx;"
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_arrow_right.png" mode=""></image>
					</view>
					<view class="vux-x-input weui-cell" v-if="isShowMore">
						<view class="weui-cell__hd">
							<view class="weui-label ">公司地址</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input id="address" v-model="formData.buyerAddress" class="weui-input"
								placeholder="填写公司地址（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell" v-if="isShowMore">
						<view class="weui-cell__hd">
							<view class="weui-label ">公司电话</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input id="tel" v-model="formData.buyerPhone" class="weui-input" placeholder="填写公司电话（选填）"
								placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell" v-if="isShowMore">
						<view class="weui-cell__hd">
							<view class="weui-label ">开户银行</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input id="bank" v-model="formData.buyerBankName" class="weui-input"
								placeholder="填写公司开户行（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell" v-if="isShowMore">
						<view class="weui-cell__hd">
							<view class="weui-label ">银行账户</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input id="bankAccount" v-model="formData.buyerBankAccount" class="weui-input"
								placeholder="填写公司开户银行账户（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</template>
			</view>
		</view>
		<view class="weui-form" v-if="isShowMore && userTypeIndexSub == '1'">
			<view class="weui-cells__title g-flex g-flex-align-center">
				<view class="weui-cells__title__decoration">
					备注
				</view>
				<view class="desc">
					该内容会打印在发票上
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell" style="padding: 30rpx;">
					<!-- <view class="weui-cell__hd">
						<view class="weui-label weui-label__require">电子邮箱</view>
					</view> -->
					<view class="weui-cell__bd weui-cell__primary">
						<textarea v-model="formData.remark" placeholder="填写备注(选填)" placeholder-class="plc"
							class="textarea" name="" id="" cols="30" rows="10"></textarea>
					</view>
				</view>
			</view>
		</view>
		<view class="price-wrapper" @click="toRecord">
			<view class="label">
				总金额
			</view>
			<view class="price">
				<text>{{moneyFilter(orderInfo.totalPrice)}}</text> 元
			</view>
			<view class="record">
				<view class="record-text">
					共{{orderInfo.totalCount}}条消费记录
				</view>
				<image style="width: 14rpx;height: 26rpx;" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_arrow_right.png"
					mode=""></image>
			</view>
		</view>
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="weui-cells__title__decoration">
					接收方式
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">电子邮箱</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input id="name" v-model="formData.email" class="weui-input" placeholder="用于向您发送电子发票（必填）"
							placeholder-class="plc"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="tips">
			<view class="tips-title">
				温馨提示:
			</view>
			<view class="tips-item">
				当前您开具的发票为普通电子发票，如需开具专用发票，请联系在线客服或拨打广西捷通ETC客服热线0771-5896333。
			</view>
			<!-- 			<view class="tips-item">
				2.ETC发票统一按上高速前设置的默认抬头开票，如需变更默认抬头，请通过[常用发票抬头功能]修改
			</view>
			<view class="tips-item">
				3.如您的车辆曾绑定多个抬头，则有可能会开出多个抬头的发票。
			</view> -->
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="onSubmitHandle">
						提交
					</button>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" background-color="#F9F9F9">
			<view class="half-screen-dialog" style="background: #F9F9F9;border-radius: 20px 20px 0px 0px">
				<view class="half-screen-dialog__hd g-flex g-flex-align-center">
					<view class="half-screen-dialog__hd__main ">
						<view class="title">
							发票抬头
						</view>
					</view>
					<view class="half-screen-dialog__hd__side"
						:class="['half-screen-dialog__hd__side--' + closeIconPos]" @click="closePopup('bottom')">
						<text size="40" class="cuIcon-close close"></text>
					</view>


				</view>
				<view class="half-screen-dialog__bd" style="height: 500rpx; overflow-y: scroll;">
					<view class="select-item g-flex g-flex-align-center g-flex-justify"
						v-for="(item,index) in titleList" :key="index">
						<view class="item-label">
							{{item.buyerName}}
						</view>
						<view class="item-btn" @click="selectTitle(item)">
							选择
						</view>
					</view>
				</view>
				<view class="half-screen-dialog__ft">
					<view class="bottom-box">
						<view class="btn-item">
							<button class="weui-btn weui-btn_primary" @click="addTitle">
								添加常用发票抬头
							</button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<confirmDialog :title="'提示'" :show.sync="isShowConfirm" :leftBtnText="'不需要'" :rightBtnText="'需要'"
			:defaultCancel="false" @comfirm="dialogConfirm" @cancel="cancel">
			<template v-slot:content>
				<view class="content">
					<view class="content-tips" :style="{color:centerBtn?'#333333':''}">
						{{tips}}
					</view>
					<view class="title-name">
						{{formData.buyerName}}
					</view>
				</view>
			</template>
		</confirmDialog>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import confirmDialog from '../../components/confirm-dialog.vue';
	import {
		getEtcAccountInfo,
		getLoginUserInfo
	} from "@/common/storageUtil.js";
	import {
		checkEmailReg,
		moneyFilter
	} from "@/common/util.js";
	import {
		info
	} from '../../../../common/method/log';
	export default {
		components: {
			tLoading,
			confirmDialog
		},
		data() {
			return {
				isLoading: false,
				isShowMore: false,
				isShowSelect: false,
				isShowConfirm: false,
				changeFlag: false,
				invoiceOptions: [{
						value: '1',
						name: '企业单位'
					},
					{
						value: '2',
						name: '个人/非企业单位'
					}
				],
				formData: {
					id: '',
					buyerName: '', //购方名称
					buyerTaxpayerNum: '', //购方纳税人识别号
					buyerAddress: '', //购方地址
					buyerPhone: '', //购方电话
					buyerBankName: '', //购方开户行名称
					buyerBankAccount: '', //购房银行账号
					email: '', //邮箱
					remark: '', //备注
					custMastId: '', //ETC用户ID,
					invoiceApplyType: '', //开票类型 1-设备票2-权益票3-月结账单服务费滞纳金4-次次顺服务费滞纳金
				},
				companyItem: {
					qymc: ''
				}, //选择的内容
				userTypeIndexSub: '1',
				orderInfo: {},
				invoiceMail: '',
				companyList: {},
				titleList: [], //抬头列表
				titleId: '', //抬头id
				tips: '', //弹框提示
				latest: null
			};
		},
		watch: {
			userTypeIndexSub(val) {
				this.isShowMore = false
				this.companyItem.qymc = ''
				if (val == '2') {
					console.log('监听', val)
					if (this.changeFlag) return
					this.formData.buyerName = ''
				} else if (val == '1') {
					this.formData.id = ''
					this.formData.buyerName = ''
					this.formData.buyerTaxpayerNum = ''
					this.formData.buyerAddress = ''
					this.formData.buyerPhone = ''
					this.formData.buyerBankName = ''
					this.formData.buyerBankAccount = ''
					this.formData.remark = ''
				}
			}
		},
		onLoad(option) {
			const item = JSON.parse(decodeURIComponent(option.orderInfo));
			console.log('item', item)
			this.orderInfo = item
			this.formData.invoiceApplyType = item.invoiceApplyType
			this.formData.custMastId = getEtcAccountInfo().custMastId

			this.getDefaultTitle()

			// if (!item.titleType) {
			// 	//没有titleType说明是从新增车辆过来的，拿不到需要查询
			// 	this.getTitleData(item)
			// } else {
			// 	this.vehicleInfo = item
			// }
		},
		methods: {
			moneyFilter,
			onKeyInput(event) {
				this.companyItem.qymc = event.target.value
				let queryStr = event.detail.value
				if (queryStr.length < 2) {
					//不到2个字不搜索
					return
				}
				this.isLoading = true

				let params = {
					// data: {
					key: queryStr
					// },
				}

				let data = {
					routePath: this.$interfaces.getInvoiceCompanyInfo.method,
					bizContent: params
				};

				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('模糊查询', res)
							let result = res.data
							this.isShowSelect = true
							this.companyList = result
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})

			},
			openSelect() {
				this.getDefaultTitle('list')
				this.$refs.popup.open('bottom')
			},
			closePopup(type) {
				this.$refs.popup.close(type)
			},
			invoiceChange(e) {
				this.changeFlag = false
				this.userTypeIndexSub = e.detail.value
			},
			// companyQuery(e) {
			// 	console.log('e.detail.value', e.detail.value)
			// 	let queryStr = e.detail.value
			// 	if (queryStr.length < 2) {
			// 		//不到2个字不搜索
			// 		return
			// 	}
			// 	this.isLoading = true

			// 	let params = {
			// 		// data: {
			// 		key: queryStr
			// 		// },
			// 	}

			// 	let data = {
			// 		routePath: this.$interfaces.getInvoiceCompanyInfo.method,
			// 		bizContent: params
			// 	};

			// 	this.$request
			// 		.post(this.$interfaces.issueRoute, {
			// 			data: data
			// 		})
			// 		.then((res) => {
			// 			this.isLoading = false;
			// 			if (res.code == 200) {
			// 				console.log('模糊查询', res)
			// 				let result = res.data
			// 				this.isShowSelect = true
			// 				this.companyList = result
			// 			} else {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				})
			// 			}
			// 		})
			// 		.catch((error) => {
			// 			this.isLoading = false;
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error.msg,
			// 				showCancel: false
			// 			})
			// 		})

			// },
			changeCompany(item) {

				this.companyItem = item

				this.formData.buyerName = item.qymc;
				this.formData.buyerTaxpayerNum = item.nsrsbh;
				this.formData.buyerAddress = item.dz;
				this.formData.buyerPhone = item.dh;
				this.formData.buyerBankName = item.bankAccount;
				this.formData.buyerBankAccount = item.bankNo;

				this.isShowMore = true
			},
			getDefaultTitle(type = 'default') {
				let params = {
					custMastId: this.formData.custMastId,
					source: '1',
					userNo: getLoginUserInfo().userNo,
					tId: ''
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.otherQueryTemplate, {
						data: params
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('抬头列表====>>>', res)
							if (type == 'default') {
								this.setTitle(res.data)
							}
							this.setSelectTitle(res.data)
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			setTitle(titleList) {
				//默认抬头
				if (titleList.length == 0) return
				let filterTitle = titleList.filter(item => {
					return !!item.latest
				})
				//上一次开票抬头
				let lastFilter = titleList.filter(item => {
					return item.isAgain == '1'
				})
				console.log('默认抬头', filterTitle)
				console.log('上一次开票抬头', lastFilter)

				if (filterTitle.length == 0 && lastFilter.length == 0) {
					return
				}
				let title = {}

				if (filterTitle.length > 0) {
					//优先默认抬头
					title = filterTitle[0]
				} else {
					//无默认抬头时，使用上一次抬头
					title = lastFilter[0]
				}
				if (title.buyerTaxpayerNum) {
					//企业单位
					Object.keys(title).forEach(key => {
						if (this.formData.hasOwnProperty(key)) {
							this.formData[key] = title[key];
						}
					});
					this.companyItem.qymc = title.buyerName
					this.isShowMore = true
				} else {
					//个人单位
					this.userTypeIndexSub = '2'
					this.changeFlag = true
					this.formData.buyerName = title.buyerName
					console.log('默认抬头个人', this.formData.buyerName)
				}

			},
			// setSelectTitle(titleList) {
			// 	//选择抬头列表
			// 	if (titleList.length == 0) return
			// 	let filterTitle = titleList.filter(item => {
			// 		return !!item.buyerTaxpayerNum
			// 	})
			// 	this.titleList = filterTitle
			// },
			setSelectTitle(titleList) {
				//选择抬头列表
				if (titleList.length == 0) return
				let filterTitle = titleList.filter(item => {
					return !!item.buyerTaxpayerNum
				})
				let userTitle = titleList.filter(item => {
					return !item.buyerTaxpayerNum
				})
				console.log('filterTitle', userTitle)
				this.userTypeIndexSub == '1' ? this.titleList = filterTitle : this.titleList = userTitle
			},
			selectTitle(item) {
				Object.keys(item).forEach(key => {
					if (this.formData.hasOwnProperty(key)) {
						this.formData[key] = item[key];
					}
				});
				this.companyItem.qymc = this.formData.buyerName
				this.isShowMore = true
				this.$refs.popup.close('bottom')
			},
			onSubmitHandle() {
				//赋值购方名称
				if (this.userTypeIndexSub == '1') {
					this.formData.buyerName = this.companyItem.qymc;
				}
				if (!this.validData()) return
				this.checkTitle()
			},
			checkTitle() {
				this.isLoading = true;
				let params = {
					buyerName: this.formData.buyerName,
					netUserNo: getLoginUserInfo().userNo,
					userType: 1,
				}
				this.$request
					.post(this.$interfaces.otherIsDefaultTemplate, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						console.log('抬头检查====>>>', res)
						if (res.code == 200) {
							if (res.data.isExist == '1') {
								this.tips = '是否需更新已添加的抬头信息'
								this.formData.id = res.data.id
								this.latest = res.data.latest
								this.isShowConfirm = true
							} else {
								this.formData.id = ''
								this.tips = '此外，是否需将此抬头信息保存为常用抬头'
								this.isShowConfirm = true
							}
						} else {
							uni.showModal({
								title: '提示',
								content: error.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			cancel() {
				//不需要
				if (this.formData.id) {
					//不需要更新抬头，直接调用提交
					this.submit()
				} else {
					//新增抬头，调用updateTitle
					this.updateTitle(false)
				}
			},
			dialogConfirm() {
				// console.log('this.formData.id', this.formData.id)
				//需要
				if (this.formData.id) {
					//存在id，直接取原来信息
					this.updateTitle(this.latest)
				} else {
					//新增抬头，调用updateTitle
					this.updateTitle(true)
				}
			},
			updateTitle(latest) {
				let params = {
					source: 1,
					userNo: getLoginUserInfo().userNo || '',
					latest: latest
				}
				let _formData = JSON.parse(JSON.stringify(this.formData));
				for (let key in _formData) {
					if (this.userTypeIndexSub == '2') {
						if (!(key == 'buyerName' || key == 'id')) {
							_formData[key] = '';
						}
					}
				}
				params = Object.assign(params, _formData)
				this.$request
					.post(this.$interfaces.otherTemplateSave, {
						data: params
					})
					.then((res) => {

					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})

				this.isShowConfirm = false
				this.submit()

			},
			submit() {
				this.isShowConfirm = false
				this.isLoading = true
				let orderIds = []
				if (this.orderInfo && this.orderInfo.orderList.length > 0) {
					this.orderInfo.orderList.forEach((item) => {
						let obj = {};
						obj.orderId = item.bizOrderId;
						obj.bizSource = item.bizSource;
						orderIds.push(obj);
					});
				}
				let params = {
					...this.formData,
					orderIds: orderIds
				}
				delete params.id

				this.$request
					.post(this.$interfaces.otherBlue, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('开票成功===>>>', res)
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/otherInvoice/common/resultPage?type=2&descType=open&customerId=' +
									this.formData.custMastId
							})
						} else {
							console.log('开票失败===>>>', res)
							let errorText = '错误码【' + res.code + '】，' + res.msg
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/otherInvoice/common/resultPage?type=5&descType=open&errorText=' +
									errorText
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			validData() {
				if (this.userTypeIndexSub == '1') {
					if (!this.formData.buyerName) {
						uni.showModal({
							title: '提示',
							content: '请先输入公司名称!',
							showCancel: false
						})
						return false
					}
					if (!this.formData.buyerTaxpayerNum) {
						uni.showModal({
							title: '提示',
							content: '请先输入公司税号!',
							showCancel: false
						})
						return false
					}
				} else if (this.userTypeIndexSub == '2') {
					if (!this.formData.buyerName) {
						uni.showModal({
							title: '提示',
							content: '请先输入姓名!',
							showCancel: false
						})
						return false
					}
				}

				if (!checkEmailReg(this.formData.email)) {
					uni.showModal({
						title: '提示',
						content: '请输入正确的邮箱格式!',
						showCancel: false
					})
					return false
				}
				return true
			},
			toRecord() {
				uni.navigateTo({
					url: './recordDetail?orderInfo=' +
						encodeURIComponent(JSON
							.stringify(this.orderInfo))
				})
			},
			addTitle() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/otherInvoice/otherInvoiceTitle/addTitle?routeType=confirmInvoice'
				})
			}
		}
	}
</script>
<style>
	.plc {
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #C6C6C6;
		line-height: 30rpx;
	}
</style>
<style lang="scss" scoped>
	.confirm-invoice {
		overflow: hidden;
		padding-bottom: 180rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 200rpx;
	}

	.weui-cell {
		&:first-child {
			padding: 26rpx 0 26rpx 30rpx;
		}
	}

	.weui-cell__bd {
		.textarea {
			width: 100%;
			height: 234rpx;
			background-color: #f8f8f8;
			padding: 20rpx 25rpx;
		}
	}

	.weui-cells__title {
		.desc {
			margin-left: 20rpx;
			font-size: 28rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #999999;
			line-height: 28rpx;
		}
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color: #0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}

	.del-btn {
		color: #0066E9;
		font-size: 26rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
	}

	.uni-list-cell {
		flex: 1;

		&:first-child {
			flex: 0 0 190rpx;
			width: 190rpx;
		}


	}

	.weui-form {
		margin: 20rpx 20rpx 0 20rpx;
	}

	.price-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 20rpx;
		padding: 26rpx 30rpx;
		background-color: $uni-bg-color;

		.label {
			width: 160rpx;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 30rpx;
		}

		.price {
			flex: 1;

			&>text {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				color: #FF9038;
				line-height: 30rpx;
				padding-right: 6rpx;
			}
		}

		.record {
			display: flex;
			align-items: center;

			.record-text {
				margin-right: 26rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 30rpx;
			}
		}

	}

	.company-name {
		// width: 100%;
		// position: relative;

		.more-list {
			position: fixed;
			top: 20%;
			left: 20%;
			height: 382rpx;
			background-color: #ffffff;
			border-radius: 12rpx;
			box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
			z-index: 88;
		}

		.more-list__item {
			// margin: 0 20rpx;
			padding: 20rpx 40rpx;
			// height: 120rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			text-align: left;
			border-bottom: 1rpx solid #F2F2F2;

			&:last-of-type {
				border-bottom: none;
			}

			.name {
				margin-bottom: 10rpx;
			}
		}
	}

	.tips {
		padding: 30rpx;
		font-size: 24rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		line-height: 40rpx;

		.tips-title {
			color: #FF9038;
		}

		.tips-item {
			margin-bottom: 6rpx;
			color: #888888;
		}
	}

	.content {
		height: 100%;
		// padding: 50rpx;

		.content-tips {
			margin: 10rpx 0;
			font-size: 28rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #888888;
			line-height: 50rpx;
		}

		.title-name {
			// height: 76rpx;
			// margin: 15rpx 0;
			padding: 15rpx;
			background: #E9E9E9;
			border-radius: 8rpx;
			font-size: 32rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #333333;
			line-height: 50rpx;
		}

		.btn-wrapper {
			display: flex;
			justify-content: center;
			width: 100%;
			margin-top: 50rpx;

			.btn-center {
				width: 232rpx;
				height: 71rpx;
				line-height: 71rpx;
				background: #0066E9;
				border-radius: 14rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				text-align: center;
			}
		}
	}

	/deep/.weui-bottom-fixed {
		z-index: 88;
	}

	.half-screen-dialog {
		height: 718rpx;
	}

	.half-screen-dialog__hd {
		height: 100rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;

		position: relative;
		border-bottom: 1rpx solid #e9e9e9;
	}

	.half-screen-dialog__hd__main {
		width: 100%;
	}

	.half-screen-dialog__hd__main .title {
		color: #333333;
		font-size: 36rpx;
		font-weight: 700;
		padding-left: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__main .desc_title {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__side {
		position: absolute;
		right: 10rpx;
		top: 20rpx;
		z-index: 3;
	}

	.half-screen-dialog__hd__side .close {
		font-size: 44rpx;
		color: #999999;
	}

	.half-screen-dialog__hd__side--top-left {
		top: 30rpx;
		left: 30rpx;
	}

	.half-screen-dialog__hd__side--top-right {
		top: 30rpx;
		right: 30rpx;
	}

	.half-screen-dialog__bd {

		// margin-top: 30rpx;
		.select-item {
			height: 132rpx;
			padding: 0 30rpx;
			border-bottom: 1rpx solid #e9e9e9;

			.item-btn {
				position: relative;
				color: #0066E9;
			}

			.item-btn:after {
				content: "";
				position: absolute;
				left: -20px;
				top: -20px;
				right: -20px;
				bottom: -20px;

			}
		}
	}

	.half-screen-dialog__ft {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 20rpx 45rpx 18rpx 43rpx;
	}

	/deep/.status-dialog__content {
		height: 450rpx !important;

		.content {
			padding: 0 30rpx;
		}
	}
</style>