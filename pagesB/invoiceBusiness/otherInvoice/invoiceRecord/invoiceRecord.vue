<template>
	<view class="toll-record">
		<!-- <view class="line-block"></view> -->
		<view class="weui-form">
			<view class="weui-cells" style="padding-top: 0;">

				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">创建日期</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="picker">
							<view class="pick-date pick-date-one">
								<picker mode="date" @change="startDateChange" :end="nowDate" fields="month"
									:value="staCreateTime" style="width: 100%;">
									<u-cell-item title=" " :arrow="false" icon="date-fill">
										<view class="monthData">{{staCreateTime}}</view>
									</u-cell-item>
								</picker>
							</view>
							<view style="margin: 0 30rpx;">至</view>
							<view class="pick-date pick-date-two">
								<picker mode="date" @change="endDateChange" :end="nowDate" fields="month"
									:value="endCreateTime" style="width: 100%;">
									<u-cell-item title=" " :arrow="false" icon="date-fill">
										<view class="monthData">{{endCreateTime}}</view>
									</u-cell-item>
								</picker>
							</view>
						</view>
					</view>
				</view>

				<view class="vux-x-input weui-cell" style="width:100%;height: 96rpx;">
					<view class="weui-cell__hd">
						<view class="weui-label">消费类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary" @click="showRecordType = true">
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-input" style="color: #333333;">
								{{typeName}}
							</view>
						</view>
					</view>
					<image src="../../../static/down.png" mode="" style="width: 40rpx;height: 40rpx;"></image>
				</view>

				<view class="vux-x-input weui-cell" style="width:100%;height: 96rpx;">
					<view class="weui-cell__hd">
						<view class="weui-label">发票类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-input" style="color: #333333;">
							<!-- {{vehicleInfo.plateNum}}【{{vehicleColorStr}}牌】 -->
							普通电子发票
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell" style="width:100%;height: 150rpx;">
					<view class="btn-search" @click="onSearchHandle">
						查询
					</view>
				</view>
			</view>
		</view>
		<scroll-view v-if="orderList && orderList.length > 0 && !isLoading" :style="{height:height}"
			:scroll-top="scrollTop" scroll-y="true" class="scroll-Y" :lower-threshold='lowerThreshold'
			@scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<view class="order-list">
				<view class="order-item" v-for="(item,index) in orderList" :key="index">
					<!-- 					<checkbox-group class="record-group" @change="checked=>changeCheckbox(checked,item,index)">
						<label class="list-label"> -->
					<view class="item-title">
						<!-- 								<checkbox class="checkbox cyan checked  round" :checked='item.checked'
									style="transform: scale(0.7)" value="check">
								</checkbox> -->
						发票总金额<text
							class="price">￥{{item.invApplyType == '3'? moneyFilter(item.serviceAmount + item.forfeitAmount) : moneyFilter(item.fee)}}</text>
					</view>
					<view class="item-container">
						<view class="item-bd">
							<view class="item-label">
								消费类型：
							</view>
							<view class="item-value">
								{{item.invApplyTypeStr}}
							</view>
						</view>
						<view class="item-bd">
							<view class="item-label">
								{{item.invApplyType == '3' ? '还款时间：' : '交易时间：'}}
							</view>
							<view class="item-value">
								{{ formatHandle(new Date(item.orderTime).getTime(), 'yyyy-MM-dd HH:mm:ss') }}
							</view>
						</view>
						<view class="item-bd" v-if="item.invApplyType == '3'">
							<view class="item-label">
								账单金额：
							</view>
							<view class="item-value">
								{{moneyFilter(item.totalAmount)}}元
							</view>
						</view>
						<view class="item-bd">
							<view class="item-label">
								消费说明：
							</view>
							<view class="item-value">
								<!-- 等字段xxxxxxx -->
								{{item.useType}}
							</view>
						</view>
					</view>
					<view class="item-status item-status-3">
						已开票
					</view>
					<!-- 						</label>
					</checkbox-group> -->
				</view>
			</view>
		</scroll-view>
		<view class="no-data" v-if="orderList && orderList.length == 0 && !isLoading">
			<image src="../../../static/no_data.png" mode="" class="no-data-img"></image>
			<view class="no-data-title">暂无记录</view>
		</view>
		<!-- <view class="weui-bottom-fixed" v-if="orderList && orderList.length > 0">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<checkbox-group class="g-flex g-flex-align-center" @change="checkAll">
						<label class="g-flex g-flex-align-center" style="line-height: 52rpx;">
							<checkbox class="cyan checked  round" :checked='isCheckAll'
								style=" transform: scale(0.8,0.8)" value="check">
							</checkbox>
							<view style="margin-left: 10rpx;">全选可开票记录</view>
						</label>
					</checkbox-group>
				</view>
				<view class="btn-item">
					<view class="count">
						共￥<text>{{moneyFilter(totalPrice)}} </text>
					</view>
					<button class="weui-btn weui-btn_primary" style="background-color: #0066E9;" @click="toConfirm">
						去开票({{totalCount}})
					</button>
				</view>
			</view>
		</view> -->
		<u-select v-model="showRecordType" :list="recordTypeList" :defaultValue="[defaultValue]" @confirm="typeConfirm">
		</u-select>
		<!-- 		<u-select v-model="showType" :list="invoiceType" :defaultValue="['2']" @confirm="confirm">
		</u-select> -->
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import {
		getVehicleColor
	} from '@/common/method/filter.js';
	import {
		moneyFilter,
		formatHandle
	} from '@/common/util.js'
	import {
		getEtcAccountInfo
	} from '@/common/storageUtil.js';
	import {
		otherRecordType,
		otherRecordAllType
	} from '@/common/const/optionData.js'
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')

	function getDate(type) {
		const date = new Date();
		console.log(date)
		let year = date.getFullYear();
		let month = date.getMonth() + 1;
		// let day = date.getDate();

		if (type === 'start') {
			year = year - 1;
		} else if (type === 'end') {
			year = year;
		}
		// if (type === 'start') {
		// 	month = '1';
		// } else if (type === 'end') {
		// 	month = month;
		// }
		month = month > 9 ? month : '0' + month;
		// day = day > 9 ? day : '0' + day;

		return `${year}-${month}`;
	}
	export default {
		components: {
			tLoading
		},
		options: {
			styleIsolation: 'shared'
		},
		computed: {
			vehicleColorStr() {
				return getVehicleColor(this.vehicleInfo.plateColor);
			},
			etcInfo() {
				return getEtcAccountInfo() || {}
			},
		},
		data() {
			return {
				otherRecordType,
				otherRecordAllType,
				isLoading: false,
				showRecordType: false,
				showType: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				height: 'calc(100% - 510rpx)',
				orderList: null,
				orderListData: [],
				vehicleInfo: {},
				pageNum: 1,
				pageSize: 500,
				nowDate: '',
				defaultValue: '0',
				staCreateTime: getDate('start'), //开始提交时间
				endCreateTime: getDate('end'), //结束提交时间
				recordTypeList: otherRecordType,
				typeName: '权益服务费/设备费',
				invoiceApplyType: '5',
				orderInvoiceApplyType: ''
			}
		},
		created() {
			this.nowDate = dayjs(new Date()).format('YYYY-MM');
			console.log('etcInfo', this.etcInfo)
			this.getEtcStatus()
		},
		methods: {
			moneyFilter,
			formatHandle,
			getOtherInvoiceStatus(val) {
				for (let i = 0; i < this.recordTypeList.length; i++) {
					if (val && this.recordTypeList[i].value == val) {
						return this.recordTypeList[i].label;
					}
				}
				return ''
			},
			typeConfirm(obj) {
				this.typeName = obj[0].label;
				this.invoiceApplyType = obj[0].value;
			},
			startDateChange(e) {
				console.log('e===>>>>', e)
				this.staCreateTime = e.detail.value
			},
			endDateChange(e) {
				console.log('e===>>>>', e)
				this.endCreateTime = e.detail.value
			},
			getEtcStatus() {
				this.isLoading = true;
				let params = {
					custId: this.etcInfo.custMastId
				}
				//判断etc用户是否含有月月行
				this.$request
					.post(this.$interfaces.defaultQueryOption, {
						data: params
					})
					.then((res) => {
						console.log('res', res)
						this.isLoading = false;
						if (res.code == 200) {
							this.monthDefault = res.data.monthDefault
							if (res.data.monthDefault == '1') {
								this.recordTypeList = this.otherRecordAllType
								this.defaultValue = '1'
								this.typeName = '月结服务费/滞纳金'
							} else {
								this.recordTypeList = this.otherRecordType
								this.defaultValue = '0'
								this.typeName = '权益服务费/设备费'
							}
							this.getRecordList()
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			onSearchHandle() {
				this.getRecordList()
			},
			formatData(params) {
				//开始年份和开始月份
				let strCurrentYear = dayjs(this.staCreateTime).year();
				let strCurrentMonth = dayjs(this.staCreateTime).month() + 1;
				strCurrentMonth = strCurrentMonth > 9 ? strCurrentMonth : '0' + strCurrentMonth;
				console.log('strCurrentYear', strCurrentYear, strCurrentMonth)
				params.startDate = strCurrentYear + '' + strCurrentMonth + '01'
				// params.startDate = '20240101'
				//结束年份和结束月份，结束月份最大天数
				let maxDaysInMonth = dayjs(this.endCreateTime).daysInMonth();
				let currentYear = dayjs(this.endCreateTime).year();
				let currentMonth = dayjs(this.endCreateTime).month() + 1;
				currentMonth = currentMonth > 9 ? currentMonth : '0' + currentMonth;
				maxDaysInMonth = maxDaysInMonth > 9 ? maxDaysInMonth : '0' + maxDaysInMonth;

				params.endDate = currentYear + '' + currentMonth + '' + maxDaysInMonth
				// params.endDate = '20241231'

				return params
			},
			async getRecordList() {
				this.isLoading = true

				let params = {
					customerId: this.etcInfo.custMastId,
					startDate: this.staCreateTime,
					endDate: this.endCreateTime,
					invoiceApplyType: this.invoiceApplyType,
					pageNum: this.pageNum,
					pageSize: this.pageSize,
				}
				params = await this.formatData(params)

				console.log('params==>>', params)
				this.$request
					.post(this.$interfaces.otherInvHistory, {
						data: params
					})
					.then((res) => {
						console.log('res', res)
						this.isLoading = false;
						if (res.code == 200) {
							// this.orderList = res.data.orderList
							// this.orderInvoiceApplyType = res.data.invoiceApplyType
							this.orderList = []
							let originData = res.data.data
							if (originData.length > 0) {
								this.changeData(originData)
							}

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			changeData(originData) {
				console.log('originData', originData)
				let arrData = []
				originData.forEach(item => {
					item.invOrderDetails.forEach(item1 => {
						if (item.invoiceType == '0') {
							arrData.push(item1)
						}
					})
				})
				console.log('arrData', arrData)
				this.orderList = arrData
				console.log('orderList', this.orderList)
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				// if (this.flag) return;
				// let self = this;
				// setTimeout(function() {
				// 	self.pageNum = self.pageNum + 1;
				// 	self.getRecordList();
				// }, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
			//单选
		}
	}
</script>

<style lang="scss" scoped>
	.toll-record {
		overflow: hidden;
		// position: ;
		width: 100%;
		// height: calc(100% - 134rpx);
		padding: 20rpx 0;
		height: 100%;
		// padding-bottom: 154rpx;

		.line-block {
			height: 20rpx;
			width: 100%;
			background-color: #F6F6F6;
		}

		.weui-form {
			height: 442rpx;
			min-height: 442rpx;
			margin-top: 0rpx;

		}

		.picker {
			width: 100%;
			display: flex;
			height: 100%;
			align-items: center;
		}

		/deep/.u-border-bottom::after {
			border-bottom: none;
		}

		.pick-date {
			width: 192rpx;
			display: flex;
			align-items: center;

			/deep/.u-cell {
				position: relative;
				padding: 0 !important;
			}

			/deep/.u-cell__value {
				font-size: 30rpx !important;
			}

			/deep/.u-cell__left-icon-wrap {
				position: absolute;
				right: 0;
				margin-right: 0px !important;
			}

			/deep/.u-icon__icon {
				font-size: 25rpx !important;
				color: #999999;
			}
		}

		.pick-date-two {
			// flex: 1;
		}

		/deep/.u-cell__value {
			color: #333;
			text-align: left;
			font-size: 30rpx;
		}

		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}
	}

	.activation-page {
		position: relative;
	}

	.weui-cells {
		padding-top: 0;
	}

	.weui-cell {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0rpx 30rpx;
	}

	.weui-cell:before {
		right: 30rpx;
	}

	.btn-search {
		width: 100%;
		height: 79rpx;
		line-height: 79rpx;
		text-align: center;
		background: #0066E9;
		color: #ffffff;
		border-radius: 14rpx;
	}

	.weui-cell__bd {
		display: flex;
		justify-content: flex-end;

		.type {
			width: 226rpx;
			height: 63rpx;
			line-height: 63rpx;
			text-align: center;
			background: #FFFFFF;
			border-radius: 8rpx 0rpx 0rpx 8rpx;
			border: 1rpx solid #0066E9;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #0066E9;
		}

		.type:nth-child(2) {
			border-radius: 0rpx 8rpx 8rpx 0rpx;
			margin-left: 16rpx;
		}

		.selector {
			background: #0066E9;
			color: #FFFFFF;
		}
	}

	.weui-cells::before {
		border: 0;
	}

	.weui-label {
		width: 180rpx;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 96rpx;
	}

	.weui-cell_picker .weui-picker-value {
		text-align: left;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 96rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 96rpx;
	}

	.search-btn {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #ffffff;
		margin-top: 1rpx;
	}

	.weui-btn {
		flex: 1;
		margin-top: 0;
		// margin-right: 20rpx;
		background-color: #0066E9;
	}

	.fixed-top {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		// bottom: 0;
		overflow: hidden;
		z-index: 10;
		background-color: #F3F3F3;
	}

	.scroll-box {
		padding-top: 358rpx;
		padding-bottom: 20rpx;
	}

	.apply-record {

		color: #01C1B2;
		border: 1rpx solid #01C1B2;
		background: transparent;

	}

	.order-list {
		padding: 20rpx;
	}

	.order-item {
		position: relative;
		// margin: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 12rpx;
		background-color: #ffffff;
		font-family: PingFangSC-Medium, PingFang SC;
		// overflow: hidden;
		padding: 6rpx 0rpx 11rpx 0rpx;
	}

	.item-title {
		position: relative;
		margin: 22rpx 30rpx;
		height: 45rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		line-height: 45rpx;

		// &:before {
		// 	content: ' ';
		// 	position: absolute;
		// 	left: -39rpx;
		// 	top: 8rpx;
		// 	width: 8rpx;
		// 	height: 30rpx;
		// 	background-color: #333333;
		// }
	}

	.item-title .price {
		margin-left: 20rpx;
		font-size: 32rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #FF9100;
		line-height: 45rpx;
	}

	.item-status {
		position: absolute;
		right: 0;
		top: 0;
		width: 169rpx;
		padding: 0 16rpx;
		height: 63rpx;
		border-radius: 0rpx 10rpx 0rpx 30rpx;
		text-align: center;
		line-height: 63rpx;
		font-size: 26rpx;
	}

	// 待支付，已取消，已退货退款，已退货不退款
	.item-status.info {
		background: rgba(133, 134, 134, 0.15);
		color: #6A6969;
	}

	// 设备已发货，审核通过，已完结，已签收，换货审核通过，退货审核通过，设备已寄回
	.item-status-2,
	.item-status-3 {
		background: rgba(0, 189, 50, 0.11);
		color: #00BD32;
	}

	// 换货审核中，退货审核中，待取货
	.item-status-1,
	.item-status-5 {
		background: rgba(255, 145, 0, 0.14);
		color: #FF9100;
	}

	// 后台审核中
	.item-status.primary {
		background: rgba(0, 102, 233, 0.12);
		color: #0066E9;
	}

	// 审核不通过，换货审核不通过，退货审核不通过
	.item-status-4 {
		background: rgba(255, 84, 84, 0.15);
		color: #FF5454;
	}

	.item-container {
		margin: 38rpx 40rpx 30rpx 40rpx;
	}

	.border-line {
		padding-bottom: 20rpx;
		border-bottom: 1rpx dashed #C3C3C3;
	}

	.item-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.item-label {
		flex: 0 0 160rpx;
		width: 160rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.item-value {
		flex: 1;
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
		text-align: right;
	}




	.weui-tag {
		// background: rgba(0, 189, 50, 0.1);
		border-radius: 0rpx 0rpx 0rpx 30rpx;
		display: inline-block;
		height: 62rpx;
		padding: 0 20rpx;
		line-height: 62rpx;
		min-width: 170rpx;
		font-size: 26rpx;
		text-align: center;
		// color: #3874FF;
		box-sizing: border-box;
		white-space: nowrap;
	}


	.weui-title__decoration {
		position: relative;

	}

	.weui-title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		background: #333333;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background: #333333;
	}

	.weui-bottom-fixed__box {
		padding: 20rpx 26rpx 48rpx 26rpx;
	}

	.btn-item {
		display: flex;
		align-items: center;

		.count {
			margin-right: 10rpx;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #4F4F4F;
			color: #FF9100;
			line-height: 58rpx;

			&>text {
				font-size: 36rpx;
				font-family: DINCondensed, DINCondensed;
				font-weight: bold;
				color: #FF9100;
				line-height: 58rpx;
			}
		}

		.weui-btn {
			width: 222rpx;
			height: 84rpx;
			background: #0066E9;
			border-radius: 50rpx;
			font-size: 32rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 84rpx;
		}
	}

	/deep/.uni-popup {
		z-index: 1000;
	}

	.calendar-wrapper {
		height: 700rpx;

		.calendar-title {
			justify-content: space-between;
			height: 98rpx;
			line-height: 98rpx;
			padding: 0 36rpx 0 44rpx;
			border-bottom: 1rpx solid #EEEEEE;

			.cancel {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 42rpx;
			}

			.confirm {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #0066E9;
				line-height: 42rpx;
			}
		}

		.calendar-year {
			height: 82rpx;
			line-height: 82rpx;
			font-size: 36rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			// color: #0066E9;
			text-align: center;
			background-color: #f8f8f8;
		}

		.calendar-month {

			padding: 0rpx 16rpx 54rpx 16rpx;

			.month {
				display: inline-block;
				height: 45rpx;
				width: 94rpx;
				margin-right: 30rpx;
				margin-top: 50rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: 45rpx;
				text-align: center;
			}

			.month:nth-child(6),
			.month:nth-child(12) {
				margin-right: 0;
			}

			.selector {
				background: #0066E9;
				border-radius: 6rpx;
				color: #FFFFFF !important;
			}
		}

		.calendar-bottom {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 84rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 33rpx;
			background-color: #f8f8f8;


			.bottom-left,
			.bottom-right {
				display: flex;
				align-items: center;
			}

			.bottom-left {
				margin-right: 50rpx;
			}

			.circle {
				width: 15rpx;
				height: 15rpx;
				margin-right: 10rpx;
				background: #0066E9;
				border-radius: 50%;

			}

			.red {
				background: #F65B5B;
			}
		}
	}

	.no-data {
		width: calc(100% - 40rpx);
		text-align: center;
		background: #fff;
		margin-left: 20rpx;
		margin-top: 20rpx;
		border-radius: 11rpx;
		height: 430rpx;

		.no-data-img {
			width: 248rpx;
			height: 269rpx;
			background-size: 100%;
			margin-top: 62rpx;
		}

		.no-data-title {
			height: 40rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #333333;
			line-height: 40rpx;
		}
	}
</style>