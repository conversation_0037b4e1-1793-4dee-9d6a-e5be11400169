<template>
	<view class="record-detail">
		<view class="top-title">
			<view class="price" style="margin-right: 30rpx;">
				累计金额{{moneyFilter(totalPrice)}}元
			</view>
			<view class="count">
				共{{totalCount}}笔记录
			</view>
		</view>
		<scroll-view :style="{height:height}" :scroll-top="scrollTop" scroll-y="true" class="scroll-Y"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<view class="record-list">
				<view class="record-item" v-for="(item,index) in dataList" :key="index">
					<!-- 					<view class="header">
						<view class="name">
							发票金额: <text style="color: #FF9038;">{{moneyFilter(item.fee)}}元</text>
						</view>
					</view> -->
					<view class="content">
						<view class="fee-item">
							<view class="label">
								发票金额:
							</view>
							<view class="fee">
								￥{{invoiceType == '3'? moneyFilter(item.serviceAmount + item.forfeitAmount) : moneyFilter(item.fee)}}元
							</view>
						</view>
						<view class="info-item">
							<view class="label">
								消费类型:
							</view>
							<view class="info">
								{{getOtherInvoiceStatus(invoiceType)}}
							</view>
						</view>
						<view class="info-item">
							<view class="label">
								{{invoiceType == '3' ? '还款时间：' : '交易时间：'}}
							</view>
							<view class="info">
								{{item.time || formatHandle(new Date(item.orderTime).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
							</view>
						</view>
						<view class="info-item" v-if="invoiceType == '3'">
							<view class="label">
								账单金额:
							</view>
							<view class="info">
								{{moneyFilter(item.totalAmount)}}元
							</view>
						</view>
						<view class="info-item">
							<view class="label">
								消费说明:
							</view>
							<view class="info">
								{{item.useType}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<tLoading :isShow="isLoading" />
</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import {
		moneyFilter,
		formatHandle
	} from '@/common/util.js'
	import {
		getTollTicket
	} from '@/common/storageUtil.js';
	import {
		otherRecordAllType
	} from '@/common/const/optionData.js'
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				otherRecordAllType,
				isLoading: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				height: 'calc(100% - 108rpx)',
				totalPrice: 0,
				totalCount: 0,
				orderInfo: {},
				dataList: [],
				invoiceItem: null,
				titleDetail: null,
				routeType: '',
				invoiceType: ''
			}
		},
		onLoad(option) {
			if (option.orderInfo) {
				const item = JSON.parse(decodeURIComponent(option.orderInfo));
				console.log('item', item)
				this.orderInfo = item
				this.invoiceType = item.invoiceApplyType
				this.totalCount = item.totalCount
				this.totalPrice = item.totalPrice
				this.dataList = item.orderList
			}
		},
		methods: {
			moneyFilter,
			formatHandle,
			getOtherInvoiceStatus(val) {
				for (let i = 0; i < this.otherRecordAllType.length; i++) {
					if (val && this.otherRecordAllType[i].value == val) {
						return this.otherRecordAllType[i].label;
					}
				}
				return ''
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				// if (this.flag) return;
				// let self = this;
				// setTimeout(function() {
				// 	self.searchObj.page = self.searchObj.page + 1;
				// 	self.getList();
				// }, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.record-detail {
		overflow: hidden;
		// height: calc(100% - 20rpx);
		height: 100%;

		.top-title {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			line-height: 88rpx;
			background: rgba(255, 145, 0, 0.1);
		}

		.price,
		.count {
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #FF9038;
			line-height: 42rpx;
		}

		.record-list {
			margin: 0 20rpx;
			background-color: #f3f3f3;
			padding: 20rpx 0;

			.record-item {
				margin-bottom: 20rpx;
				padding-bottom: 30rpx;
				background-color: #ffffff;
			}

			.header {
				display: flex;
				margin-bottom: 16rpx;

				.checkbox {
					margin: 25rpx 0 25rpx 20rpx;
				}

				.checkbox::before {
					margin-top: -28rpx;
				}

				.name {
					flex: 1;
					margin: 25rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 40rpx;
				}

				.status {
					width: 169rpx;
					height: 63rpx;
					line-height: 63rpx;
					text-align: center;
					background: rgba(133, 134, 134, 0.1);
					border-radius: 0rpx 8rpx 0rpx 30rpx;
				}
			}

			.content {
				.fee-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 10rpx 30rpx;

					.label {
						flex: 0 0 150rpx;
						width: 150rpx;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #999999;
						line-height: 42rpx;
					}

					.fee {
						flex: 1;
						text-align: right;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						color: #FF9038;
						line-height: 42rpx;
					}
				}

				.info-item {
					display: flex;
					// align-items: center;
					justify-content: space-between;
					padding: 10rpx 30rpx;

					.label {
						flex: 0 0 160rpx;
						width: 160rpx;
						margin-top: 2rpx;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #999999;
						line-height: 42rpx;
					}

					.info {
						flex: 1;
						text-align: right;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #333333;
						line-height: 42rpx;
					}
				}
			}
		}
	}
</style>