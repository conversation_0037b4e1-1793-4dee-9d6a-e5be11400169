<!--
  * @desc:tab选择
  * @author:dwz
  * @date:2023-1-5 14:47:16
!-->
<template>
	<view style="height: 100%;overflow:hidden">
		<scroll-view scroll-x class="bg-white nav">
			<view class="flex text-center">
				<view class="cu-item flex-sub" :class="index==TabCur?'text-cyan cyan  cur':''"
					v-for="(item,index) in tabList" :key="index" @tap="tabSelect" :data-id="index">
					{{item}}
				</view>
			</view>
		</scroll-view>
		<unInvoiceRecord v-if="TabCur==0" />
		<invoiceRecord v-if="TabCur==1" />
	</view>
</template>

<script>
	import unInvoiceRecord from './unInvoiceRecord.vue'
	import invoiceRecord from './invoiceRecord.vue'
	export default {
		name: '',
		props: {

		},
		components: {
			unInvoiceRecord,
			invoiceRecord
		},
		data() {
			return {
				TabCur: 0,
				scrollLeft: 0,
				tabList: ['未开票', '已开票'],

			}
		},
		onLoad(obj) {
			// if (obj.type == 'invoiced') {
			// 	this.TabCur = 1
			// } else {
			// 	this.TabCur = 0
			// }
		},

		computed: {

		},
		watch: {

		},
		created() {

		},
		methods: {
			tabSelect(e) {
				this.TabCur = e.currentTarget.dataset.id;
				this.scrollLeft = (e.currentTarget.dataset.id - 1) * 60
			}
		},
	}
</script>

<style lang='scss' scoped>
	/deep/.u-border-bottom::after {
		border-bottom: none;
	}

	/deep/.u-cell {
		padding: 0 !important;
	}
</style>