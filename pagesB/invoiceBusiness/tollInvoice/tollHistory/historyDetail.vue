<template>
	<view class="history-detail">
		<view class="top-title">
			<view class="title-left">
				已开发票
			</view>
			<view class="title-right">
				<view class="title-right">
					<view class="count">
						<text class="count-num">{{invoiceItem.sumInvoice}}</text>张{{isChange?'(包含红冲票)':''}}
					</view>
					<view class="count">
						共计<text class="count-num">{{moneyFilter(invoiceItem.sumAmount + '')}}</text>元
					</view>
				</view>
			</view>
		</view>
		<scroll-view v-if="dataList.length > 0" :style="{height:height}" :scroll-top="scrollTop" scroll-y="true"
			class="scroll-Y" :lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower"
			@scroll="scroll">
			<view class="order-list">
				<template v-if="!isShowMore">
					<view class="order-item" v-for="(item,index) in sortList" :key="index">
						<view class="item-container">
							<view class="item-bd">
								<view class="item-label">
									开票日期：
								</view>
								<view class="item-value">
									{{formatHandle(new Date(item.invoiceMakeTime).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
								</view>
							</view>
							<view class="item-bd" v-if="item.totalAmount > 0">
								<view class="item-label">
									发票金额：
								</view>
								<view class="item-value">
									<text style="color: #FF9100;">{{moneyFilter(item.totalAmount)}}</text>元
								</view>
							</view>
							<view class="item-bd">
								<view class="item-label">
									发票编号：
								</view>
								<view class="item-value">
									{{item.invoiceNum}}
								</view>
							</view>
							<view class="item-bd">
								<view class="item-label">
									发票抬头：
								</view>
								<view class="item-value">
									{{item.buyerName}}
								</view>
							</view>
							<view class="item-bd">
								<view class="item-label">
									收款方：
								</view>
								<view class="item-value">
									{{item.sellerName}}
								</view>
							</view>
						</view>
						<view class="more-icon" v-if="index == 1 && dataList.length > 2" @click="toggle">
							<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_more_down.png" mode=""></image>
						</view>
						<view class="preview" @click="toPreview(item)" v-if="item.totalAmount > 0">
							<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_preview.png" style="width: 40rpx;height:40rpx;"
								mode=""></image>
						</view>
						<view class="preview red-tag" v-else>
							已红冲
						</view>
					</view>
				</template>
				<template v-else>
					<view class="order-item" v-for="(item,index) in dataList" :key="index">
						<view class="item-container">
							<view class="item-bd">
								<view class="item-label">
									开票日期：
								</view>
								<view class="item-value">
									{{formatHandle(new Date(item.invoiceMakeTime).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
								</view>
							</view>
							<view class="item-bd" v-if="item.totalAmount > 0">
								<view class="item-label">
									发票金额：
								</view>
								<view class="item-value">
									<text style="color: #FF9100;">{{moneyFilter(item.totalAmount)}}</text>元
								</view>
							</view>
							<view class="item-bd">
								<view class="item-label">
									发票编号：
								</view>
								<view class="item-value">
									{{item.invoiceNum}}
								</view>
							</view>
							<view class="item-bd">
								<view class="item-label">
									发票抬头：
								</view>
								<view class="item-value">
									{{item.buyerName}}
								</view>
							</view>
							<view class="item-bd">
								<view class="item-label">
									收款方：
								</view>
								<view class="item-value">
									{{item.sellerName}}
								</view>
							</view>
						</view>
						<view class="more-icon" v-if="index == dataList.length - 1" @click="toggle">
							<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_more_up.png" mode=""></image>
						</view>
						<view class="preview" @click="toPreview(item)" v-if="item.totalAmount > 0">
							<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_preview.png" style="width: 40rpx;height:40rpx;"
								mode=""></image>
						</view>
						<view class="preview red-tag" v-else>
							已红冲
						</view>
					</view>
				</template>
			</view>
		</scroll-view>
		<view class="price-wrapper" @click="toRecord" v-if="recordList.totalCount > 0">
			<view class="label">
				包含的{{invoiceItem.invoiceType == '1' ? '消费':'充值'}}记录
			</view>
			<!-- <view class="price">
				<text>{{moneyFilter(vehicleInfo.totalPrice)}}</text> 元
			</view> -->
			<view class="record">
				<view class="record-text">
					<text style="color: #FF9100;">{{recordList.totalCount}}</text>条
					<!-- <text style="color: #FF9100;">2</text>条 -->
				</view>
				<image style="width: 14rpx;height: 26rpx;margin-left: 10rpx;"
					src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_arrow_right.png" mode=""></image>
			</view>
		</view>
		<view class="price-wrapper">
			<view class="label">
				发票接收邮箱
			</view>
			<!-- <view class="price">
				<text>{{moneyFilter(vehicleInfo.totalPrice)}}</text> 元
			</view> -->
			<view class="record">
				<view class="record-text">
					{{invoiceItem.invoiceMail}}
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="reSendHandle">
						重推发票
					</button>
				</view>
				<view class="btn-text" @click="toChangeConfirm">
					发票信息有误？
				</view>
			</view>
		</view>
		<confirmDialog :show.sync="isShowConfirm" :rightBtnText="'确认提交'" @comfirm="reSend">
			<template v-slot:content>
				<view class="content">
					<view class="tips">
						请确认发票接收邮箱
					</view>
					<view class="title-name">
						<input type="text" v-model="invoiceMail" placeholder="请输入发票接收的邮箱">
					</view>
					<view class="tips">
						请留意邮箱信息
					</view>
				</view>
			</template>
		</confirmDialog>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import confirmDialog from '../../components/confirm-dialog.vue';
	import {
		getTollTicket
	} from '@/common/storageUtil.js';
	import {
		tollInvoiceType,
		tollInvoiceStatus
	} from "@/common/const/optionData.js";
	import {
		getTollInvoiceType,
		// getTollInvoiceStatus
	} from '@/common/method/filter.js'
	import {
		moneyFilter,
		formatHandle
	} from '@/common/util.js'
	export default {
		components: {
			tLoading,
			confirmDialog
		},
		data() {
			return {
				isLoading: false,
				isShowMore: false,
				isShowConfirm: false,
				invoiceItem: {},
				height: 'calc(100% - 536rpx)',
				dataList: [],
				sortList: [],
				invoiceMail: '',
				totalCount: 0,
				totalPrice: 0,
				recordList: {
					dataList: [],
					totalCount: 0,
					totalPrice: 0
				},
				isChange: false
			}
		},
		onLoad(option) {
			// const item = JSON.parse(decodeURIComponent(option.invoiceItem));
			// console.log('item', item)
			// this.invoiceItem = item
			this.invoiceItem = this.$store.getters['invoice/historyDetail']
			this.invoiceMail = this.invoiceItem.invoiceMail
			if (this.invoiceItem.changeId) {
				this.isChange = true
				//更换的发票，需要查详情里面的invoiceId
				this.getDetail(this.invoiceItem.changeId, 'change')
			}
			this.getDetail(this.invoiceItem.applyId, 'normal')
			// this.getPdf()
		},
		methods: {
			moneyFilter,
			formatHandle,
			toggle() {
				this.isShowMore = !this.isShowMore
			},
			toChangeConfirm() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollHistory/changeConfirm'
				})
			},
			toPreview(item) {
				// console.log('item', item)
				// const callcenter = item.invoiceHtmlUrl
				// uni.navigateTo({
				// 	url: '/pages/uni-webview/uni-webview?ownPath=' +
				// 		encodeURIComponent(callcenter)
				// })
				uni.setClipboardData({
					data: item.invoiceHtmlUrl,
					success: () => {
						uni.showModal({
							title: '复制',
							content: '复制发票链接成功，请自行使用手机浏览器预览下载',
							showCancel: false,
						})
					}
				});
			},
			toRecord() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollSelect/recordDetail?routeType=historyDetail&recordList=' +
						encodeURIComponent(JSON
							.stringify(this.recordList))
				})
			},
			//获取记录
			getDataList(invoiceId) {
				this.isLoading = true
				let params = {
					ticketId: getTollTicket(),
					invoiceId: invoiceId,
					cardId: this.invoiceItem.cardNo
				}
				let url = ''
				if (this.invoiceItem.invoiceType == '1') {
					url = this.$interfaces.tollInvoiceTransInvoiced
				} else {
					url = this.$interfaces.tollInvoiceRechargeInvoiced
				}
				this.$request
					.post(url, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							let result = res.data
							let totalPrice = 0
							//总金额
							result.items.forEach(item => {
								totalPrice += item.fee
							})
							this.recordList = {
								dataList: result.items,
								totalCount: result.items.length,
								totalPrice: totalPrice
							}

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			// changeDataList(dataList) {
			// 	let filterData = dataList.filter(item => {
			// 		return item.totalAmount > 0
			// 	})
			// 	console.log('filterData', filterData)

			// 	if (filterData.length > 0) {
			// 		this.$store.dispatch(
			// 			'invoice/setTitleDetail', filterData[0]
			// 		)
			// 		let totalPrice = 0
			// 		//总金额
			// 		filterData.forEach(item => {
			// 			totalPrice += item.totalAmount
			// 		})

			// 		this.totalCount = filterData.length
			// 		this.totalPrice = totalPrice
			// 		this.dataList = filterData
			// 		console.log('totalCount', this.totalCount, this.totalPrice)
			// 		if (filterData.length >= 2) {
			// 			this.sortList = [filterData[0], filterData[1]]
			// 		} else {
			// 			this.sortList = this.dataList
			// 		}

			// 		if (!this.isChange) {
			// 			this.getDataList(filterData[0].invoiceId)
			// 		}
			// 	}

			// },
			getDetail(applyId, type) {
				if (type == 'normal') {
					this.isLoading = true
				}
				let params = {
					ticketId: getTollTicket(),
					applyId: applyId,
					tradeId: this.invoiceItem.tradeId,
					cardId: this.invoiceItem.cardNo
				}

				this.$request
					.post(this.$interfaces.tollInvoiceInvoiceList, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							if (type == 'normal') {
								console.log('开票详情记录====>>>>res', res)
								// this.changeDataList(res.data.items)
								this.dataList = res.data.items
								if (this.dataList.length > 0) {
									//排序
									this.dataList.sort((last, current) => current.totalAmount - last.totalAmount);
									console.log('this.dataListsort====>>>', this.dataList)
									this.$store.dispatch(
										'invoice/setTitleDetail', this.dataList[0]
									)
								}

								if (this.dataList.length >= 2) {
									this.sortList = [this.dataList[0], this.dataList[1]]
								} else {
									this.sortList = this.dataList
								}

								if (!this.isChange) {
									this.getDataList(this.dataList[0].invoiceId)
								}
							} else {
								//changeId是更换抬头的，需要拿到旧的invoiceId
								let oldDataList = res.data.items
								this.getDataList(oldDataList[0].invoiceId)
							}

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: (resp) => {
									if (resp.confirm) {
										//需要登录
										if (res.msg.includes('请重新登录')) {
											uni.reLaunch({
												url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/index'
											})
											return
										}
									}
								}
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			reSendHandle(item) {
				this.isShowConfirm = true
			},
			reSend() {
				if (!this.invoiceMail) {
					uni.showModal({
						title: '提示',
						content: '发票接收邮箱不能为空！',
						showCancel: false
					})
					return
				}
				this.isShowConfirm = false
				this.isLoading = true
				let params = {
					applyId: this.invoiceItem.applyId,
					cardId: this.invoiceItem.cardNo,
					month: this.invoiceItem.month,
					invoiceMail: this.invoiceMail,
					ticketId: getTollTicket(),
				}
				this.$request
					.post(this.$interfaces.tollInvoicePush, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=4&descType=reSend'
							})
						} else {
							let errorText = '错误码【' + res.code + '】，' + res.msg
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=5&descType=reSend&errorText=' +
									errorText
							})
							// uni.showModal({
							// 	title: '提示',
							// 	content: res.msg,
							// 	showCancel: false
							// })
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				// if (this.flag) return;
				// let self = this;
				// setTimeout(function() {
				// 	self.formData.page = self.formData.page + 1;
				// 	self.getList();
				// }, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
		},

	}
</script>

<style scoped lang="scss">
	.history-detail {
		overflow: hidden;
		height: 100%;

		.top-title {
			background-color: $uni-bg-color;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 88rpx;
			line-height: 88rpx;
			padding: 0 30rpx;

			.title-left {
				padding-left: 15rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 28rpx;
			}

			.title-right {
				display: flex;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				color: #666666;
				line-height: 28rpx;

				.count {
					margin-left: 20rpx;

					.count-num {
						color: #FF5454;

					}
				}
			}
		}


		.order-list {
			padding: 20rpx;
			background-color: #ffffff;
		}

		.order-item {
			position: relative;
			// margin: 20rpx;
			margin-bottom: 20rpx;
			border-radius: 12rpx;
			background-color: #ffffff;
			font-family: PingFangSC-Medium, PingFang SC;
			border: 2rpx solid #F5F5F5;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.item-title {
			position: relative;
			margin: 22rpx 40rpx;
			height: 45rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
			line-height: 45rpx;

			&:before {
				content: ' ';
				position: absolute;
				left: -39rpx;
				top: 8rpx;
				width: 8rpx;
				height: 30rpx;
				background-color: #333333;
			}
		}

		.item-title .price {
			margin-left: 20rpx;
			font-size: 32rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #FF9100;
			line-height: 45rpx;
		}

		.item-container {
			margin: 30rpx;
			// padding-bottom: 20rpx;
			// border-bottom: 1rpx dashed #C3C3C3;
		}

		.item-bd {
			display: flex;
			margin-bottom: 20rpx;
		}

		.item-label {
			width: 160rpx;
			font-size: 30rpx;
			font-weight: 400;
			color: #999999;
		}

		.item-value {
			flex: 1;
			font-size: 30rpx;
			font-weight: 400;
			color: #333333;
		}

		.preview {
			position: absolute;
			right: 30rpx;
			top: 28rpx;
		}

		.red-tag {
			padding: 0 16rpx;
			height: 63rpx;
			border-radius: 10rpx;
			text-align: center;
			line-height: 63rpx;
			font-size: 26rpx;
			background: rgba(255, 84, 84, 0.15);
			color: #FF5454;
		}

		.price-wrapper {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 20rpx;
			padding: 26rpx 30rpx;
			background-color: $uni-bg-color;

			.label {
				width: 300rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #666666;
				line-height: 30rpx;
			}

			.record {
				display: flex;
				align-items: center;

				.record-text {
					// margin-right: 10rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #666666;
					line-height: 30rpx;
				}
			}

		}

		.scroll-Y {
			.more-icon {
				background-color: #ffffff;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 83rpx;
				// padding-top: 20rpx;
				border-top: 2rpx solid #F5F5F5;

				&>image {
					width: 39rpx;
					height: 29rpx;
				}
			}
		}

		.weui-bottom-fixed__box {
			display: flex;
			flex-direction: column;
			align-items: normal;
			justify-content: center;
			padding: 20rpx 60rpx 60rpx 60rpx;
		}

		.btn-text {
			margin-top: 15rpx;
			text-align: center;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #F65B5B;
			line-height: 33rpx;
		}

		.bottom-box {
			display: flex;
		}

		.bottom-box .btn-item {
			flex: 1;
		}
	}
</style>