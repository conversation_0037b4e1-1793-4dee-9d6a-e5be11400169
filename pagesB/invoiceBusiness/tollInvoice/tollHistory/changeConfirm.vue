<template>
	<view class="preview-invoice">
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="weui-cells__title__decoration">
					原开票信息
				</view>
			</view>
			<view class="weui-cells" style="10rpx 0 20rpx 0">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">开票时间：</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :disabled="true" id="name"
							:value="formatHandle(new Date(invoiceItem.invoiceTime).getTime(), 'yyyy-MM-dd HH:mm:ss')"
							class="weui-input"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">发票类型：</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :disabled="true" id="taxNum" :value="getTollInvoiceType(invoiceItem.invoiceType)"
							class="weui-input"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label ">可开票记录：</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :disabled="true" id="address" :value="invoiceItem.sumInvoice + '条'"
							class="weui-input"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label ">发票数量：</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :disabled="true" id="tel" :value="invoiceItem.sumInvoice + '张'"
							class="weui-input"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label ">开票金额：</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :disabled="true" style="color: #FF9100;" id="bank"
							:value="moneyFilter(invoiceItem.sumAmount) + '元'" class="weui-input"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="weui-cells__title__decoration">
					发票抬头
				</view>
				<view class="btn-change" @click="getTitleList">
					<text class="btn-text" style="color: #999999;">
						切换
					</text>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_arrow_right.png"
						style="width: 14rpx;height: 26rpx;margin-left: 26rpx;" mode=""></image>
				</view>
			</view>
			<view class="weui-cells" style="padding: 20rpx 0;">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">开票名称：</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-input">{{selectTitleItem? selectTitleItem.name:oldTitleItem.buyerName}}</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell"
					v-if="(selectTitleItem && selectTitleItem.taxNum) || oldTitleItem.buyerTaxpayerCode">
					<view class="weui-cell__hd">
						<view class="weui-label">开票税号：</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :disabled="true" id="taxNum"
							:value="selectTitleItem? selectTitleItem.taxNum:oldTitleItem.buyerTaxpayerCode"
							class="weui-input"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="tips-wrapper">
			<view class="tips-title">
				温馨提示:
			</view>
			<view class="tips-item">
				1.发票抬头更换申请提交后，系统将在三个工作日内处理你的申请，请耐心等待。
			</view>
			<view class="tips-item">
				2.如您需要更换此辆车的绑定抬头，请通过[通行费发票常用抬头]功能修改。
			</view>
		</view>

		<uni-popup ref="popup" background-color="#F9F9F9">
			<view class="half-screen-dialog" style="background: #F9F9F9;border-radius: 20px 20px 0px 0px">
				<view class="half-screen-dialog__hd g-flex g-flex-align-center">
					<view class="half-screen-dialog__hd__main ">
						<view class="title">
							发票抬头
						</view>
					</view>
					<view class="half-screen-dialog__hd__side"
						:class="['half-screen-dialog__hd__side--' + closeIconPos]" @click="closePopup('bottom')">
						<text size="40" class="cuIcon-close close"></text>
					</view>


				</view>
				<view class="half-screen-dialog__bd" style="height: 500rpx; overflow-y: scroll;">
					<view class="select-item g-flex g-flex-align-center g-flex-justify"
						v-for="(item,index) in titleList" :key="index">
						<view class="item-label">
							{{item.name}}
						</view>
						<view class="item-btn" @click="selectTitle(item)">
							选择
						</view>
					</view>
				</view>
				<view class="half-screen-dialog__ft">
					<view class="bottom-box">
						<view class="btn-item">
							<button class="weui-btn weui-btn_primary" @click="addTitle">
								添加常用发票抬头
							</button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<confirmDialog :title="title" :show.sync="isShowConfirm" :rightBtnText="rightBtnText" :centerBtn="centerBtn"
			@comfirm="changeApply">
			<template v-slot:content>
				<view class="content">
					<view class="tips" :style="{color:centerBtn?'#333333':''}">
						{{tips}}
					</view>
					<view class="title-name" v-if="!centerBtn">
						{{selectTitleItem.name}}
					</view>
					<view class="tips" v-if="!centerBtn">
						{{tips1}}
					</view>
					<view class="btn-wrapper" v-if="centerBtn">
						<view class="btn-center" @click="isShowConfirm = false">
							确定
						</view>
					</view>
				</view>
			</template>
		</confirmDialog>
		<tButton :buttonList="buttonList" @changeApplyHandle="changeApplyHandle"></tButton>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tButton from '@/pagesB/components/t-button/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue'
	import confirmDialog from '../../components/confirm-dialog.vue';
	import {
		getTollTicket
	} from '@/common/storageUtil.js';
	import {
		moneyFilter,
		formatHandle
	} from '@/common/util.js'
	import {
		getTollInvoiceType,
		// getTollInvoiceStatus
	} from '@/common/method/filter.js'
	export default {
		components: {
			tButton,
			tLoading,
			confirmDialog
		},
		data() {
			return {
				isLoading: false,
				isShowConfirm: false,
				centerBtn: false,
				buttonList: [{
					title: '提交申请',
					handle: 'changeApplyHandle',
					type: 'primary',
				}],
				title: '确认信息',
				rightBtnText: '确定提交',
				tips: '',
				tips1: '',
				invoiceItem: {},
				oldTitleItem: {},
				titleItem: {},
				titleList: [],
				selectTitleItem: null, //需要更换成的抬头
			}
		},
		onLoad() {
			this.invoiceItem = this.$store.getters['invoice/historyDetail']
			this.oldTitleItem = this.$store.getters['invoice/titleDetail']

			console.log('oldTitleItem', this.oldTitleItem, this.selectTitleItem)
		},
		methods: {
			moneyFilter,
			formatHandle,
			getTollInvoiceType,
			// getTollInvoiceStatus,
			selectTitle(item) {
				// this.oldTitleItem.buyerName = item.name
				// this.oldTitleItem.buyerTaxpayerCode = item.taxNum
				this.selectTitleItem = item
				this.$refs.popup.close('bottom')
			},
			getTitleList() {
				this.isLoading = true
				let params = {
					ticketId: getTollTicket()
				}

				this.$request
					.post(this.$interfaces.tollTitleSearch, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							let result = res.data
							this.titleList = result.items
							// console.log('发票链接下载====>>>>res', res)
							this.$refs.popup.open('bottom')
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			closePopup(type) {
				this.$refs.popup.close(type)
			},
			changeApplyHandle() {
				if (!this.selectTitleItem) {
					//未更改，提示
					this.title = '提示'
					this.tips = '您未修改发票抬头信息，请先修改'
					this.centerBtn = true
					this.isShowConfirm = true
				} else {
					this.title = '确认信息'
					this.tips = '请确认新抬头'
					this.tips1 = '原发票已失效请勿使用，新发票预计在3个工作日内开出，请留意邮箱信息。'
					this.centerBtn = false
					this.isShowConfirm = true
				}
			},
			changeApply() {
				this.isShowConfirm = false
				this.isLoading = true
				let params = {
					ticketId: getTollTicket(),
					titleId: this.selectTitleItem.titleId,
					applyId: this.invoiceItem.applyId,
					cardId: this.invoiceItem.cardNo
				}

				this.$request
					.post(this.$interfaces.tollInvoiceChangeTitle, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=3&descType=changeTitle&cardNo=' +
									this.invoiceItem.cardNo
							})
						} else {
							let errorText = '错误码【' + res.code + '】，' + res.msg
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=5&descType=changeTitle&errorText=' +
									errorText
							})
							// uni.showModal({
							// 	title: '提示',
							// 	content: res.msg,
							// 	showCancel: false
							// })
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			addTitle() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollInvoiceTitle/addTitle?routeType=changeTitle'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.preview-invoice {
		overflow: hidden;
		padding: 20rpx;

		.weui-input,
		.weui-cell__value {
			text-align: left;
		}

		.weui-cell {
			margin-top: 8rpx;
			padding: 4rpx 30rpx;
		}

		.weui-cell:before {
			border: none;
		}

		.weui-label {
			flex: 0 0 180rpx;
			width: 180rpx;
		}

		.weui-cells__title__decoration {
			position: relative;
			font-weight: 600;
			color: #333333;
			font-size: 30rpx;
			padding-left: 16rpx;
		}

		.weui-cells__title__decoration:before {
			content: ' ';
			position: absolute;
			left: 0rpx;
			top: 50%;
			width: 8rpx;
			height: 30rpx;
			-webkit-transform: translateY(-50%);
			transform: translateY(-50%);
			border-radius: 4rpx;
			background-color: #0066E9;
			border-top-left-radius: 2px;
			border-top-right-radius: 2px;
			border-bottom-right-radius: 2px;
			border-bottom-left-radius: 2px;
		}

		.half-screen-dialog {
			height: 718rpx;
		}

		.half-screen-dialog__hd {
			height: 100rpx;
			display: -webkit-box;
			display: -webkit-flex;
			display: flex;
			-webkit-box-align: center;
			-webkit-align-items: center;
			align-items: center;

			position: relative;
			border-bottom: 1rpx solid #e9e9e9;
		}

		.half-screen-dialog__hd__main {
			width: 100%;
		}

		.half-screen-dialog__hd__main .title {
			color: #333333;
			font-size: 36rpx;
			font-weight: 700;
			padding-left: 12rpx;
			text-align: center;
		}

		.half-screen-dialog__hd__main .desc_title {
			font-size: 26rpx;
			font-weight: 400;
			color: #555555;
			margin-top: 12rpx;
			text-align: center;
		}

		.half-screen-dialog__hd__side {
			position: absolute;
			right: 10rpx;
			top: 20rpx;
			z-index: 3;
		}

		.half-screen-dialog__hd__side .close {
			font-size: 44rpx;
			color: #999999;
		}

		.half-screen-dialog__hd__side--top-left {
			top: 30rpx;
			left: 30rpx;
		}

		.half-screen-dialog__hd__side--top-right {
			top: 30rpx;
			right: 30rpx;
		}

		.half-screen-dialog__bd {

			// margin-top: 30rpx;
			.select-item {
				height: 132rpx;
				padding: 0 30rpx;
				border-bottom: 1rpx solid #e9e9e9;

				.item-btn {
					position: relative;
					color: #0066E9;
				}

				.item-btn:after {
					content: "";
					position: absolute;
					left: -20px;
					top: -20px;
					right: -20px;
					bottom: -20px;

				}
			}
		}



		.half-screen-dialog__ft {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			padding: 20rpx 45rpx 18rpx 43rpx;
		}

		.tips-wrapper {
			position: fixed;
			width: 100%;
			left: 0;
			bottom: 154rpx;
			padding: 30rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			line-height: 40rpx;

			.tips-title {
				color: #FF9038;
			}

			.tips-item {
				margin-bottom: 6rpx;
				color: #888888;
			}
		}

		.content {
			height: 100%;
			padding: 50rpx;

			.tips {
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #888888;
				line-height: 50rpx;
			}

			.title-name {
				// height: 76rpx;
				margin: 15rpx 0;
				padding: 20rpx;
				background: #E9E9E9;
				border-radius: 8rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 50rpx;
			}

			.btn-wrapper {
				display: flex;
				justify-content: center;
				width: 100%;
				margin-top: 50rpx;

				.btn-center {
					width: 232rpx;
					height: 71rpx;
					line-height: 71rpx;
					background: #0066E9;
					border-radius: 14rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					text-align: center;
				}
			}
		}
	}
</style>