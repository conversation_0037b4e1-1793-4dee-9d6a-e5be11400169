<template>
	<view class="preview-invoice">
		<image v-if="!type" @tap="ViewImage()" :src="url" style="width: 100%;" mode="widthFix"></image>
		<view class="change-text">
			更换发票抬头并重新开票
		</view>
		<view class="tips" v-if="type">
			<view class="tips-title">
				温馨提示:
			</view>
			<view class="tips-item">
				1.同步开出红票，如已报销，可提交给财务做原票冲红。
			</view>
			<view class="tips-item">
				2.如因车牌号信息错误无法报销，请联系在线客服或拨打广西ETC客服热线0771-5896333并提供正确车牌，更正信息后再重开。
			</view>
		</view>
		<tButton v-if="!type" :buttonList="buttonList" @saveImage="saveImage"></tButton>
		<tButton v-if="type" :buttonList="buttonList1" @changeInvoice="changeInvoice"></tButton>
	</view>
</template>

<script>
	import tButton from '@/pagesB/components/t-button/t-button.vue'
	export default {
		components: {
			tButton
		},
		data() {
			return {
				pdf: '',
				buttonList: [{
					title: '保存到手机',
					handle: 'saveImage',
					type: 'primary',
				}],
				buttonList1: [{
					title: '申请换票',
					handle: 'changeInvoice',
					type: 'primary',
				}],
				type: '',

			}
		},
		onLoad(option) {
			if (option.type) {
				this.type = option.type
			}
			
		},
		methods: {
			changeInvoice() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollHistory/changeConfirm'
				})
			},
			ViewImage() {
				uni.previewImage({
					urls: [this.url]
				});
			},
			saveImage() {
				if (!this.url) {
					return
				}
				uni.showLoading({
					title: "图片保存中"
				});
				uni.getImageInfo({
					src: this.url,
					success: (image) => {
						uni.saveImageToPhotosAlbum({
							filePath: image.path,
							success(res) {
								uni.hideLoading();
								uni.showToast({
									title: "已保存到相册",
									icon: "success",
									duration: 2000
								});
							},
							fail: err => {
								uni.showToast({
									title: "图片保存失败",
									icon: "fail",
									duration: 2000
								});
								console.log(err);
								uni.hideLoading();
							}
						});
					}
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.preview-invoice {
		overflow: hidden;
		padding: 20rpx;

		.change-text {
			margin-top: 38rpx;
			font-size: 32rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #333333;
			text-align: center;
			line-height: 45rpx;
		}

		.tips {
			position: fixed;
			width: 100%;
			left: 0;
			bottom: 154rpx;
			padding: 30rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			line-height: 40rpx;

			.tips-title {
				color: #FF9038;
			}

			.tips-item {
				margin-bottom: 6rpx;
				color: #888888;
			}
		}
	}
</style>