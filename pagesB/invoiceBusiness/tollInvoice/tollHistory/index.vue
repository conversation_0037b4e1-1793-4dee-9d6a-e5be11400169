<template>
	<view class="toll-history">
		<view class="weui-form">
			<view class="weui-cells" style="padding-top: 0;">
				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label">提交时间</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary" @click="openCalendar">
						<view class="weui-input" style="color: #333333;">
							{{formData.applyTime}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell" style="width:100%;height: 96rpx;">
					<view class="weui-cell__hd">
						<view class="weui-label">消费类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary" style="padding: 30rpx 30rpx 30rpx 0;"
						@click="showOrderType(1)">
						<view class="weui-input" v-if="formData.invoiceType" style="color: #333333;">
							{{invoiceTypeName}}
						</view>
						<view class="weui-input" style="color: #333333;" v-else>全部</view>
					</view>
					<image src="../../../static/down.png" mode="" style="width: 40rpx;height: 40rpx;"></image>
				</view>

				<view class="vux-x-input weui-cell" style="width:100%;height: 96rpx;">
					<view class="weui-cell__hd">
						<view class="weui-label">发票状态</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary" style="padding: 30rpx 30rpx 30rpx 0;"
						@click="showOrderType(2)">
						<view class="weui-input" v-if="formData.invoiceStatus" style="color: #333333;">
							{{invoiceStatusName}}
						</view>
						<view class="weui-input" style="color: #333333;" v-else>全部</view>
					</view>
					<image src="../../../static/down.png" mode="" style="width: 40rpx;height: 40rpx;"></image>
				</view>
				<view class="vux-x-input weui-cell" style="width:100%;height: 228rpx;flex-direction: column;">
					<view class="tips">
						注意:仅展示在捷通端的开票记录,完整记录请通过票根平台查询
					</view>
					<view class="btn-search" @click="onSearchHandle">
						查询
					</view>
				</view>
			</view>
		</view>
		<!-- 滚动内容 -->
		<scroll-view v-if="orderList.length > 0 && !isLoading" :style="{height:height}" :scroll-top="scrollTop"
			scroll-y="true" class="scroll-Y" :lower-threshold='lowerThreshold' @scrolltoupper="upper"
			@scrolltolower="scrolltolower" @scroll="scroll">
			<view class="order-list">
				<view class="order-item" v-for="(item,index) in orderList" :key="index">
					<view class="item-title">
						发票总金额<text class="price">￥{{moneyFilter(item.sumAmount)}}</text>
					</view>
					<view class="item-container"
						:class="item.invoiceStatus == '2' || item.invoiceStatus == '3'?'border-line':''">
						<view class="item-bd">
							<view class="item-label">
								发票类型：
							</view>
							<view class="item-value">
								{{getTollInvoiceType(item.invoiceType)}}
							</view>
						</view>
						<view class="item-bd">
							<view class="item-label">
								开票时间：
							</view>
							<view class="item-value">
								{{formatHandle(new Date(item.createTime).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
							</view>
						</view>
						<view class="item-bd">
							<view class="item-label">
								发票数据：
							</view>
							<view class="item-value">
								{{item.sumInvoice}}张{{item.changeId ? '(包含红冲票)' : ''}}
							</view>
						</view>
						<view class="item-bd" v-if="item.invoiceStatus == '2' || item.invoiceStatus == '3'">
							<view class="item-label">
								票据单号：
							</view>
							<view class="item-value">
								{{item.sumInvoice == 1 ? item.invoiceCode || '' : '具体内容请点击开票详情查看'}}
							</view>
						</view>
					</view>
					<view class="item-status" :class="'item-status-' + item.invoiceStatus">
						<!-- {{orderListData.nodeName}} -->
						{{getTollInvoiceStatus(item.invoiceStatus,item.invoiceReplace)}}
					</view>
					<view class="btn-container">
						<view class="right">
							<template>
								<view class="item-btn info"
									:class="(item.invoiceStatus != '2' && item.invoiceStatus != '3')?'disbaled':''"
									@click.stop="invoiceDetail(item)">
									开票详情
								</view>
							</template>
							<template>
								<view class="item-btn primary" :class="item.invoiceStatus != '3'?'disbaled-1':''"
									@click.stop="reSendHandle(item)">
									重推发票
								</view>
							</template>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="no-data" v-if="orderList.length == 0 && !isLoading">
			<image src="../../../static/no_data.png" mode="" class="no-data-img"></image>
			<view class="no-data-title">暂无记录</view>
		</view>
		<uni-popup ref="popup" background-color="#FFFFFF;">
			<view class="calendar-wrapper">
				<view class="calendar-title g-flex g-flex-align-center">
					<view class="cancel" @click="close">
						取消
					</view>
					<view class="confirm" @click="timeConfirm">
						确认
					</view>
				</view>
				<view class="calendar-year">
					{{currentYear}}
				</view>
				<view class="calendar-month">
					<view class="month" :style="{color:monthRecord.includes(index + 1)?'#F65B5B':''}"
						:class="((index + 1) == selectMonth && selectYear == currentYear)? 'selector':''"
						v-for="index of lastMonth" :key="index" @click="selectMonthHandle('current',index + 1)">
						{{index + 1}}月
					</view>
				</view>
				<view class="calendar-year">
					{{lastYear}}
				</view>
				<view class="calendar-month">
					<view class="month" :style="{color:lastMonthRecord.includes(index + 1)?'#F65B5B':''}"
						:class="((index + 1) == selectMonth && selectYear == lastYear) ? 'selector':''"
						v-for="index of 12" :key="index" @click="selectMonthHandle('last',index + 1)">
						{{index + 1}}月
					</view>
				</view>
				<view class="calendar-bottom">
					<view class="bottom-left">
						<view class="circle"></view>
						<view class="bottom-text">
							已选月份
						</view>
					</view>
					<view class="bottom-right">
						<view class="circle red"></view>
						<view class="bottom-text">
							{{recordDesc}}
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<confirmDialog :show.sync="isShowConfirm" :rightBtnText="'确认提交'" @comfirm="reSend">
			<template v-slot:content>
				<view class="content">
					<view class="tips">
						请确认发票接收邮箱
					</view>
					<view class="title-name">
						<input type="text" v-model="item.invoiceMail" placeholder="请输入发票接收的邮箱">
					</view>
					<view class="tips">
						请留意邮箱信息
					</view>
				</view>
			</template>
		</confirmDialog>
		<u-select v-model="showYear" mode="single-column" :default-value="[yearIndex]" :list="yearList"
			@confirm="confirmYear"></u-select>
		<u-select v-model="showType" :list="tollInvoiceType" @confirm="confirm"></u-select>
		<u-select v-model="showStatus" :list="tollInvoiceStatus" @confirm="confirmStatus">
		</u-select>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import confirmDialog from '../../components/confirm-dialog.vue';
	import {
		getTollTicket
	} from '@/common/storageUtil.js';
	import {
		tollInvoiceType,
		tollInvoiceStatus
	} from "@/common/const/optionData.js";
	import {
		getTollInvoiceType,
		// getTollInvoiceStatus
	} from '@/common/method/filter.js'
	import {
		moneyFilter,
		formatHandle
	} from '@/common/util.js'
	export default {
		components: {
			tLoading,
			confirmDialog
		},
		data() {
			return {
				tollInvoiceType: tollInvoiceType,
				tollInvoiceStatus,
				isLoading: false,
				showType: false,
				showStatus: false,
				showYear: false,
				isShowConfirm: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				height: 'calc(100% - 466rpx)',
				formData: {
					applyTime: '',
					cardNo: '',
					invoiceStatus: '',
					invoiceType: '',
					pageIndex: 1,
					pageSize: 20,
				},
				orderList: [],
				typeList: [],
				invoiceTypeName: '',
				invoiceStatusName: '',
				item: {},
				yearList: [],
				selectYear: '',
				selectMonth: '',
				lastMonth: '',
				currentYear: '',
				currentMonth: '',
				lastYear: '',
				yearIndex: [],
				monthRecord: [], //可通行/消费记录月份
				lastMonthRecord: [],
				cardType: ''
			}
		},
		onLoad(option) {
			if (option.cardNo) {
				this.formData.cardNo = option.cardNo
				//获取卡类型22储值卡23记账卡
				this.cardType = this.formData.cardNo.substring(8, 10) || ''
				if (this.cardType == '23') {
					let newType = [].concat(this.tollInvoiceType)
					newType.pop()
					this.tollInvoiceType = newType
				}
			}
			this.setCalendar()
		},
		computed: {
			recordDesc() {
				if (this.formData.invoiceType == '') {
					return '可选记录'
				} else if (this.formData.invoiceType == '1') {
					return '通行记录'
				} else if (this.formData.invoiceType == '2') {
					return '消费记录'
				}
			}
		},
		methods: {
			getTollInvoiceType,
			moneyFilter,
			formatHandle,
			getTollInvoiceStatus(type, invoiceReplace) {
				if (type == '1' || type == '2') {
					if (invoiceReplace == '1') {
						return "抬头更换中"
					} else {
						return "开票中"
					}
				} else if (type == '3') {
					return "已完成"
				} else if (type == '4') {
					return "开票失败"
				} else if (type == '5') {
					return "抬头更换中"
				}
			},
			getMonthList(type, year) {
				// this.isLoading = true
				let params = {
					cardNo: this.formData.cardNo,
					trafficYear: year,
					ticketId: getTollTicket(),
					queryType: '1'
				}
				this.$request
					.post(this.$interfaces.tollTrafficMonth, {
						data: params
					})
					.then((res) => {
						// this.isLoading = false;
						if (res.code == 200) {
							console.log('月份记录====>>>>res', res)
							//处理月份
							let result = res.data
							if (type == 'current') {
								this.monthRecord = []
								result.forEach(item => {
									if (item.indexOf("0") == 0) {
										item = parseInt(item.replace("0", ""));
									}
									item = parseInt(item);
									this.monthRecord.push(item)
								})
							}
							if (type == 'last') {
								this.lastMonthRecord = []
								result.forEach(item => {
									if (item.indexOf("0") == 0) {
										item = parseInt(item.replace("0", ""));
									}
									item = parseInt(item);
									this.lastMonthRecord.push(item)
								})
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						// this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			close() {
				this.$refs.popup.close()
			},
			openCalendar() {
				this.$refs.popup.open('bottom')
			},
			timeConfirm() {
				this.formData.applyTime = this.selectYear + '-' + this.formatMonth(this.selectMonth)
				this.close()
				// this.onSearchHandle()
			},
			setCalendar() {
				// 获取当前日期
				const currentDate = new Date();
				const currentYear = currentDate.getFullYear();
				const lastYear = currentDate.getFullYear() - 1;
				const currentMonth = currentDate.getMonth() + 1; // 月份从0开始，所以要加1				

				this.currentYear = currentYear
				this.lastYear = lastYear
				this.currentMonth = currentMonth
				this.lastMonth = currentMonth
				this.selectYear = currentYear
				this.selectMonth = currentMonth

				console.log('selectMonth', this.selectMonth)

				this.formData.applyTime = this.selectYear + '-' + this.formatMonth(this.selectMonth)

				// 从1950年开始迭代到当前年份
				for (let year = 1950; year <= currentYear; year++) {
					// console.log(`年份：${year}`);
					this.yearList.push({
						label: year + '年',
						value: year
					})
					// console.log('this.yearList', this.yearList)
				}
				this.yearIndex = [currentYear - 1950]
				console.log('this.yearIndex', this.yearIndex)

				//初始化月份数据
				this.getMonthList('current', this.currentYear)
				this.getMonthList('last', this.lastYear)
				//计算完日期后初始化数据
				this.onSearchHandle()
			},
			selectMonthHandle(type, month) {
				if (type == 'current') {
					this.selectYear = this.currentYear
				} else {
					this.selectYear = this.lastYear
				}
				this.selectMonth = month
			},
			formatMonth(month) {
				return month < 10 ? '0' + month : '' + month;
			},
			onSearchHandle() {
				this.getHistory()
			},
			getHistory() {
				this.isLoading = true
				let params = {
					...this.formData,
					ticketId: getTollTicket(),
				}
				this.$request
					.post(this.$interfaces.tollInvoiceHistory, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('开票历史====>>>>res', res)
							this.orderList = res.data.records
						} else if (res.code == 73300) {
							//无鉴权信息，去绑定登录票根平台
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: (resp) => {
									if (resp.confirm) {
										//需要登录
										uni.reLaunch({
											url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/index'
										})
									}
								}
							})
						} else {
							if (res.msg.includes('请重新登录')) {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false,
									success: (resp) => {
										if (resp.confirm) {
											//需要登录	
											uni.reLaunch({
												url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/index'
											})
											return
										}

									}
								})
							} else {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},

			invoiceDetail(item) {
				if (item.invoiceStatus != '2' && item.invoiceStatus != '3') return
				console.log('item', item)
				this.$store.dispatch(
					'invoice/setHistoryDetail', {
						...item,
						month: this.formData.applyTime,
					}
				)
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollHistory/historyDetail'
				})
			},
			showOrderType(pickerType) {
				if (pickerType == 1) {
					this.showType = true
				} else {
					this.showStatus = true
				}

			},
			showCalendar() {
				console.log('点击事件')
				this.showYear = true
			},
			confirm(options) {
				console.log(options)
				this.invoiceTypeName = options[0].label
				this.formData.invoiceType = options[0].value
				this.showType = false
			},
			confirmStatus(options) {
				console.log(options)
				this.invoiceStatusName = options[0].label
				this.formData.invoiceStatus = options[0].value
				this.showStatus = false
			},
			confirmYear(options) {
				console.log(options)
				this.selectYear = options[0].value
				this.showYear = false
				this.yearIndex = [Math.abs(1950 - this.selectYear)]
				console.log('this.yearIndex', this.yearIndex)
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				// if (this.flag) return;
				// let self = this;
				// setTimeout(function() {
				// 	self.formData.page = self.formData.page + 1;
				// 	self.getList();
				// }, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
			reSendHandle(item) {
				// uni.showModal({
				// 	title: '重推提示',
				// 	content: '确定要重推发票吗？',
				// 	success: (res) => {
				// 		if (res.confirm) {
				// 			this.resend(item)
				// 		}
				// 	}
				// })
				if (item.invoiceStatus != '3') return
				this.item = item
				this.isShowConfirm = true
			},
			reSend() {
				if (!this.item.invoiceMail) {
					uni.showModal({
						title: '提示',
						content: '发票接收邮箱不能为空！',
						showCancel: false
					})
					return
				}
				this.isShowConfirm = false
				this.isLoading = true
				let params = {
					applyId: this.item.applyId,
					cardId: this.item.cardNo,
					month: this.formData.applyTime,
					invoiceMail: this.item.invoiceMail,
					ticketId: getTollTicket(),
				}
				this.$request
					.post(this.$interfaces.tollInvoicePush, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=4&descType=reSend'
							})
						} else {
							let errorText = '错误码【' + res.code + '】，' + res.msg
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=5&descType=reSend&errorText=' +
									errorText
							})
							// uni.showModal({
							// 	title: '提示',
							// 	content: res.msg,
							// 	showCancel: false
							// })
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.disbaled {
		cursor: not-allowed;
		color: #C3C3C3 !important;
	}

	.disbaled-1 {
		cursor: not-allowed;
		color: #FFFFFF !important;
		background-color: #a0cfff !important;
		border-color: #a0cfff !important;
	}

	.toll-history {
		overflow: hidden;
		// position: ;
		width: 100%;
		height: 100%;
		// height: calc(100% - 134rpx);
		padding: 20rpx 0;

		.line-block {
			height: 20rpx;
			width: 100%;
			background-color: #F6F6F6;
		}

		.weui-form {
			height: 486rpx;
			min-height: 486rpx;
			margin-top: 0rpx;
			box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.06);
		}

		.picker {
			width: 100%;
			display: flex;
			height: 100%;
			align-items: center;
		}

		/deep/.u-border-bottom::after {
			border-bottom: none;
		}

		.pick-date {
			width: 192rpx;
			display: flex;
			align-items: center;

			/deep/.u-cell {
				position: relative;
			}

			/deep/.u-cell__value {
				font-size: 30rpx !important;
			}

			/deep/.u-cell__left-icon-wrap {
				position: absolute;
				right: 0;
				margin-right: 0px !important;
			}

			/deep/.u-icon__icon {
				font-size: 25rpx !important;
				color: #999999;
			}
		}

		.pick-date-two {
			// flex: 1;
		}

		/deep/.u-cell {
			padding: 0 0;
			line-height: 80rpx;
		}

		/deep/.u-cell__value {
			color: #333;
			text-align: left;
			font-size: 30rpx;
		}

		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;
			margin-top: 20rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}

		.weui-cell .tips {
			margin: 16rpx 0 24rpx 0;
			text-align: center;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #FF9038;
			line-height: 33rpx;
		}

		/deep/.status-dialog__content {
			top: calc(50% - 400rpx);
			height: 500rpx;
		}

		.content {
			width: 100%;
			padding: 40rpx;

			.tips {
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #888888;
				line-height: 50rpx;
				text-align: left;
			}

			.title-name {
				// height: 76rpx;
				margin: 35rpx 0;
				padding: 14rpx;
				background: #ffffff;
				border-radius: 8rpx;
				border: 2rpx solid #DDDDDD;
				border-radius: 8rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 50rpx;
			}

			.btn-wrapper {
				display: flex;
				width: 100%;
				margin-top: 50rpx;

				.btn-center {
					width: 232rpx;
					height: 71rpx;
					line-height: 71rpx;
					background: #0066E9;
					border-radius: 14rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					text-align: center;
				}
			}
		}
	}

	.weui-cells {
		padding-top: 0;
	}

	.weui-cell {
		padding: 0rpx 30rpx;
	}

	.weui-cells::before {
		border: 0;
	}

	.weui-label {
		width: 180rpx;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 96rpx;
	}

	.weui-cell_picker .weui-picker-value {
		text-align: left;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 96rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
		height: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		// line-height: 30rpx;
	}

	.weui-cell:before {
		right: 30rpx;
	}

	.btn-search {
		width: 100%;
		height: 79rpx;
		line-height: 79rpx;
		text-align: center;
		background: #0066E9;
		color: #ffffff;
		border-radius: 14rpx;
	}

	.order-list {
		padding: 20rpx;
	}

	.order-item {
		position: relative;
		// margin: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 12rpx;
		background-color: #ffffff;
		font-family: PingFangSC-Medium, PingFang SC;
		// overflow: hidden;
		padding: 6rpx 0rpx 11rpx 0rpx;
	}

	.item-title {
		position: relative;
		margin: 22rpx 40rpx;
		height: 45rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		line-height: 45rpx;

		&:before {
			content: ' ';
			position: absolute;
			left: -39rpx;
			top: 8rpx;
			width: 8rpx;
			height: 30rpx;
			background-color: #333333;
		}
	}

	.item-title .price {
		margin-left: 20rpx;
		font-size: 32rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #FF9100;
		line-height: 45rpx;
	}

	.item-status {
		position: absolute;
		right: 0;
		top: 0;
		width: 169rpx;
		padding: 0 16rpx;
		height: 63rpx;
		border-radius: 0rpx 10rpx 0rpx 30rpx;
		text-align: center;
		line-height: 63rpx;
		font-size: 26rpx;
	}

	// 待支付，已取消，已退货退款，已退货不退款
	.item-status.info,
	.item-status-0 {
		background: rgba(133, 134, 134, 0.15);
		color: #6A6969;
	}

	// 设备已发货，审核通过，已完结，已签收，换货审核通过，退货审核通过，设备已寄回
	.item-status-3 {
		background: rgba(0, 189, 50, 0.11);
		color: #00BD32;
	}

	// 换货审核中，退货审核中，待取货
	.item-status-1,
	.item-status-2,
	.item-status-5 {
		background: rgba(255, 145, 0, 0.14);
		color: #FF9100;
	}

	// 后台审核中
	.item-status.primary {
		background: rgba(0, 102, 233, 0.12);
		color: #0066E9;
	}

	// 审核不通过，换货审核不通过，退货审核不通过
	.item-status-4 {
		background: rgba(255, 84, 84, 0.15);
		color: #FF5454;
	}

	.item-container {
		margin: 38rpx 40rpx 30rpx 40rpx;
	}

	.border-line {
		padding-bottom: 20rpx;
		border-bottom: 1rpx dashed #C3C3C3;
	}

	.item-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.item-label {
		flex: 0 0 160rpx;
		width: 160rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.item-value {
		flex: 1;
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
	}

	.btn-container {
		display: flex;
		justify-content: space-between;
		margin: 8rpx 40rpx;
		padding-top: 30rpx;
		// border-top: 2rpx dashed #C3C3C3;
	}

	.left {}

	.right {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		flex-wrap: wrap;
	}

	.right .more-btn {
		width: 88rpx;
		position: relative;

		.arrow-top {
			position: absolute;
			top: 60rpx;
			left: 24rpx;

			.triangle {
				position: relative;
				width: 40rpx;
				height: 20rpx;
				overflow: hidden;

				&::before {
					content: "";
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: #FFFFFF;
					transform-origin: left bottom;
					box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
					transform: rotate(45deg);
					z-index: 2;
				}
			}
		}

		.more-list {
			width: 204rpx;
			position: absolute;
			top: 40px;
			left: 0;
			background-color: #ffffff;
			border-radius: 12rpx;
			box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
			z-index: 1;
		}

		.more-list__item {
			margin: 0 20rpx;
			height: 92rpx;
			line-height: 92rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			text-align: left;
			border-bottom: 1rpx solid #F2F2F2;

			&:last-of-type {
				border-bottom: none;
			}
		}
	}


	.right .item-btn {
		margin-left: 20rpx;

		&:nth-child(2n) {
			margin-bottom: 15rpx;
		}
	}

	.right .item-btn:first-child {
		margin-left: 0;
	}

	.item-btn {
		// padding: 12rpx 40rpx;
		width: 190rpx;
		height: 58rpx;
		line-height: 58rpx;
		border-radius: 36rpx;
		font-size: 26rpx;
		text-align: center;
	}


	.item-btn.info {
		border: 2rpx solid #E8E8E8;
		color: #323435;
		background: #FFFFFF;
	}

	.item-btn.info.disabled {
		border: 2rpx solid #E8E8E8;
		color: #C3C3C3 !important;
	}

	.item-btn.primary {
		border: 2rpx solid #0066E9;
		color: #FFFFFF;
		background: #0066E9;
	}

	.calendar-wrapper {
		height: 700rpx;

		.calendar-title {
			justify-content: space-between;
			height: 98rpx;
			line-height: 98rpx;
			padding: 0 36rpx 0 44rpx;
			border-bottom: 1rpx solid #EEEEEE;

			.cancel {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 42rpx;
			}

			.confirm {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #0066E9;
				line-height: 42rpx;
			}
		}

		.calendar-year {
			height: 82rpx;
			line-height: 82rpx;
			font-size: 36rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #0066E9;
			text-align: center;
			background-color: #f8f8f8;
		}

		.calendar-month {

			padding: 0rpx 16rpx 54rpx 16rpx;

			.month {
				display: inline-block;
				height: 45rpx;
				width: 94rpx;
				margin-right: 30rpx;
				margin-top: 50rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: 45rpx;
				text-align: center;
			}

			.month:nth-child(6),
			.month:nth-child(12) {
				margin-right: 0;
			}

			.selector {
				background: #0066E9;
				border-radius: 6rpx;
				color: #FFFFFF !important;
			}
		}

		.calendar-bottom {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 84rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 33rpx;
			background-color: #f8f8f8;


			.bottom-left,
			.bottom-right {
				display: flex;
				align-items: center;
			}

			.bottom-left {
				margin-right: 50rpx;
			}

			.circle {
				width: 15rpx;
				height: 15rpx;
				margin-right: 10rpx;
				background: #0066E9;
				border-radius: 50%;

			}

			.red {
				background: #F65B5B;
			}

		}
	}
</style>