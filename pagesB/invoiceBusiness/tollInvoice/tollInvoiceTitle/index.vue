<template>
	<view class="toll-invoice_title">
		<view class="title" v-if="titleList.length > 0">
			<view class="title__item" v-for="(item,index) in titleList" :key="index" @click="toDetail(item.titleId)">
				<view class="title__top">
					<view class="right">
						{{item.name}}
					</view>
					<view class="left">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/title_detail_icon.png"
							style="width: 42rpx;height: 40rpx;" mode=""></image>
					</view>
				</view>
				<view class="title__content">
					<view class="title__info" v-if="item.titleType == 2">
						<view class="label">
							税号：
						</view>
						<view class="value">
							{{item.taxNum}}
						</view>
					</view>
					<view class="title__info">
						<view class="label">
							关联ETC卡：
						</view>
						<view class="value">
							{{item.bindCardNum}}张
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="no-data" v-else>
			<image src="../../../static/no_data.png" mode="" class="no-data-img"></image>
			<view class="no-data-title">暂无发票抬头，点击下方按钮新增</view>
		</view>
		<tButton :buttonList="buttonList" @confirmHandle="confirmHandle"></tButton>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import tButton from '@/pagesB/components/t-button/t-button.vue'
	import {
		getTollTicket
	} from "@/common/storageUtil.js";
	export default {
		components: {
			tLoading,
			tButton
		},
		data() {
			return {
				isLoading: false,
				buttonList: [{
					title: '新增发票抬头',
					handle: 'confirmHandle',
					type: 'primary',
				}],
				titleList: []
			}
		},
		onLoad() {
			this.getTitleList()
		},
		methods: {
			toDetail(titleId) {
				uni.navigateTo({
					url: './addTitle?operateType=2&titleId=' + titleId
				})
			},
			getTitleList() {
				let params = {
					ticketId: getTollTicket(),
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.tollTitleSearch, {
						data: params
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('抬头列表====>>>', res)
							this.titleList = res.data.items
						} else if (res.code == 73300) {
							//无鉴权信息，去绑定登录票根平台
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: (resp) => {
									if (resp.confirm) {
										//需要登录
										uni.reLaunch({
											url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/index'
										})
									}
								}
							})
						} else {
							if (res.msg.includes('请重新登录')) {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false,
									success: (resp) => {
										if (resp.confirm) {
											//需要登录	
											uni.reLaunch({
												url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/index'
											})
											return
										}

									}
								})
							} else {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			confirmHandle() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollInvoiceTitle/addTitle'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.toll-invoice_title {
		overflow: hidden;
		padding: 20rpx;
		padding-bottom: 180rpx;

		.title {
			&__item {
				overflow: hidden;
				background-color: $uni-bg-color;
				margin-bottom: 20rpx;
				background: #FFFFFF;
				border-radius: 10rpx;
				border: 2rpx solid #F5F5F5;
			}

			&__top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 25rpx 30rpx;
				border-bottom: 1rpx solid #F1F0F0;

				.right {
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 30rpx;
				}
			}

			&__content {
				padding: 30rpx;
			}

			&__info {
				display: flex;
				margin-bottom: 20rpx;

				.label {
					flex: 0 0 180rpx;
					width: 180rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #999999;
					line-height: 42rpx;
				}

				.value {
					flex: 1;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 42rpx;
				}
			}
		}

		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			margin-top: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}
	}
</style>