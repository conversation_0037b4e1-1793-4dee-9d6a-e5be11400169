<template>
	<view class="bind-vehicle">
		<view class="select-content" v-if="vehicleList.length > 0 ">
			<title title="请确认ETC卡号"></title>
			<view class="select-list" v-for="(item, index) in vehicleList" :key="item.plateColor">
				<checkbox-group class="radio-group" @change="checked=>changeCheckbox(checked,item,index)">
					<label class="list-cell">
						<view style="margin-right: 10rpx;">
							<checkbox class="cyan checked  round" :checked='item.checked'
								style=" left:-4px;transform: scale(0.7)" value="check">
							</checkbox>
						</view>
						<view class="info">
							<view class="vehicle-item g-flex g-flex-horizontal-vertical"
								:class="'license-plate-'+item.plateColor"
								:style="{color:item.plateColor=='0'|| item.plateColor=='2' ? '#fff' : '#000'}">
								{{item.plateNum}}
							</view>
							<view class="card">
								{{noPassByCardNo(item.cardId)}}
							</view>
							<view class="card-status">
								({{getTollCardStatus(item.status)}})
							</view>
						</view>
					</label>
				</checkbox-group>
			</view>
		</view>
		<view class="no-data" v-if="vehicleList.length == 0 && !isLoading">
			<image src="../../../static/no_data.png" mode="" class="no-data-img"></image>
			<view class="no-data-title" @click="addVehicle">暂无已关联的车辆，<text style="color: #0066E9;">去新增车辆</text> </view>
		</view>
		<view class="weui-bottom-fixed" v-if="vehicleList.length > 0">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<checkbox-group class="g-flex g-flex-align-center" @change="checkAll">
						<label class="g-flex g-flex-align-center">
							<checkbox class="cyan checked  round" :checked='isCheckAll'
								style=" transform: scale(0.8,0.8)" value="check">
							</checkbox>
							<view style="margin-left: 10rpx;">全选</view>
						</label>
					</checkbox-group>
				</view>
				<view class="btn-item">
					<view class="count">
						共 <text> {{totalCount}} </text> 辆车
					</view>
					<button class="weui-btn weui-btn_primary" style="background-color: #0066E9;" @click="selectBind">
						{{isCheckAll?'全部添加':'选中添加'}}
					</button>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import {
		getEtcAccountInfo,
		getTollTicket
	} from '@/common/storageUtil.js';
	import {
		getTollCardStatus,
	} from '@/common/method/filter.js'
	import {
		noPassByCardNo,
	} from '@/common/util.js'
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				isCheckAll: false,
				totalCount: 0,
				vehicleList: [],
				filterVehicleList: [],
				eventChannel: null,
				operateType: '', //操作类型1新增2编辑
			}
		},
		computed: {
			etcInfo() {
				return getEtcAccountInfo() || {}
			},
			ticketId() {
				return getTollTicket() || ''
			},
			userType() {
				let userType = ''
				this.etcInfo.custType == '0' ? userType = '1' : userType = '2'
				return userType
			}
		},
		onLoad() {
			this.eventChannel = this.getOpenerEventChannel();

			this.eventChannel.on('sendVehicleList', (data) => {
				console.log('监听到的数据', data)
				this.operateType = data.operateType
				if (data.vehicleList.length == 0) {
					this.getVehicleList()
				} else {
					//判断是新增还是编辑
					if (data.operateType == '1') {
						this.vehicleList = data.vehicleList
					} else if (data.operateType == '2') {
						this.filterVehicleList = data.vehicleList
						this.getVehicleList()
					}

				}
			})
		},
		methods: {
			getTollCardStatus,
			noPassByCardNo,
			addVehicle() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollSelect/addVehicle?routeType=addTitle'
				})
			},
			getVehicleList() {
				let params = {
					ticketId: this.ticketId, //凭证
					plateNumQueryStr: this.plateNumQueryStr, //车牌模糊查询
					userType: this.userType, //客户类型
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.tollCardList, {
						data: params
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('关联的选择车辆res', res)
							let result = res.data

							//拷贝一下数据
							this.vehicleList = [].concat(result.items)
							if (this.vehicleList.length > 0) {
								//设置选中状态
								for (let i = 0; this.vehicleList.length > i; i++) {
									this.$set(this.vehicleList[i], "checked", false);
								}
							}

							//回填选中状态
							if (this.filterVehicleList.length > 0 && this.operateType == '2') {
								this.changeVehicleList()
							}
							console.log('vehicleList', this.vehicleList)
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			changeVehicleList() {
				this.vehicleList.forEach(item => {
					this.filterVehicleList.forEach(filterItem => {
						if (item.cardId == filterItem.cardId) {
							item.checked = filterItem.checked
						}
					})
				})

				console.log('改变后的数据', this.vehicleList)

				//更新全选框状态
				this.isCheckAllFlag();
				//更新选择数量
				this.countTableArrList()
			},
			selectBind() {
				if (this.totalCount == 0) {
					uni.showToast({
						title: '请先选择需要关联的车辆',
						icon: "none"
					})
					return
				}

				this.eventChannel.emit('getVehicleList', {
					vehicleList: this.vehicleList,
					moreFlag: true
				})
				uni.navigateBack({
					delta: 1
				})
			},
			//单选
			changeCheckbox(checked, item, index) {
				//选中数据push到数组里
				if (checked.detail.value.length > 0 && checked.detail.value[0] == 'check') {
					//选中
					this.$set(this.vehicleList[index], "checked", true);
				} else {
					//未选中
					this.$set(this.vehicleList[index], "checked", false);
				}
				//更新全选框状态
				this.isCheckAllFlag();
				//更新选择数量
				this.countTableArrList()
				console.log('this.vehicleList', this.vehicleList)
			},
			checkAll(checked) {
				console.info('check all change is ', checked)
				let checkFlag = false
				if (checked.detail.value.length > 0 && checked.detail.value[0] == 'check') {
					//选中
					checkFlag = true
				} else {
					//未选中
					checkFlag = false
				}
				console.log('checkFlag', checkFlag)
				for (let i = 0; i < this.vehicleList.length; i++) {
					this.$set(this.vehicleList[i], "checked", checkFlag);
				}
				console.log('checkFlagthis.tableData', this.vehicleList)
				//更新全选框状态
				this.isCheckAllFlag();
				//更新选择数量
				this.countTableArrList()
			},
			isCheckAllFlag() {
				if (this.vehicleList.length == 0) {
					this.isCheckAll = false
					return
				}
				console.log("表格数据=========>>>>>", this.vehicleList);
				let filterArr = this.vehicleList.filter((item) => {
					return !item.checked;
				});
				//全选按钮判定
				if (filterArr.length > 0) {
					this.isCheckAll = false;
				} else {
					this.isCheckAll = true;
				}
				console.log("this.isCheckAllFlag", filterArr.length, this.isCheckAll);
			},
			countTableArrList() {
				if (this.vehicleList.length == 0) {
					this.totalCount = 0
					return
				}
				let totalCount = 0;

				this.vehicleList.forEach((item) => {
					if (item.checked) {
						totalCount++;
					}
				});
				this.totalCount = totalCount;
				console.log("this.countTableArrList", this.totalCount);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.bind-vehicle {
		overflow: hidden;
		padding-bottom: 154rpx;
		padding-top: 20rpx;

		.select-content {
			background-color: $uni-bg-color;
			padding: 30rpx;
			// margin: 20rpx;
			border-radius: 12rpx;


			.input-wrapper {
				position: relative;
				margin: 30rpx 0 50rpx 0;
				height: 100rpx;
				line-height: 100rpx;
				background: #F6F6F6;
				border-radius: 11rpx;
				border: 1rpx solid #D4D4D4;

				.input {
					height: 100rpx;
					line-height: 100rpx;
					padding: 0 20rpx;
				}

				.clear-icon {
					position: absolute;
					right: 0px;
					top: 0;
					width: 60rpx;
					height: 100%;
					text-align: center;
					padding-top: 4rpx;
					z-index: 2;
				}
			}

			.select-list {
				.radio-group {
					width: 100%;
				}

				.list-cell {
					margin-top: 20rpx;
					display: flex;
					align-items: center;

					.info {
						position: relative;
						flex: 1;
						display: flex;
						align-items: center;
						height: 120rpx;
						padding: 0 20rpx;
						background: #FFFFFF;
						box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.06);
						border-radius: 16rpx;
						border: 2rpx solid #ECECEC;

						.vehicle-item {
							width: 220rpx;
							height: 80rpx;
						}

						.card {
							margin-top: 10rpx;
							margin-left: 10rpx;
						}

						.card-status {
							position: absolute;
							right: 0;
							top: 0;
							width: 125rpx;
							height: 43rpx;
							background: #ECECEC;
							border-radius: 0rpx 12rpx 0rpx 12rpx;
							font-size: 24rpx;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #333333;
							line-height: 33rpx;
							text-align: center;
						}
					}

					.plateColor {
						width: 202rpx;
						height: 68rpx;
						margin-right: 20rpx;
					}
				}
			}
		}

		.weui-bottom-fixed__box {
			padding: 20rpx 26rpx 48rpx 26rpx;
		}

		.btn-item {
			display: flex;
			align-items: center;

			.count {
				margin-right: 28rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #4F4F4F;
				line-height: 42rpx;

				&>text {
					font-size: 40rpx;
					font-family: DINCondensed, DINCondensed;
					font-weight: bold;
					color: #FF9100;
					line-height: 58rpx;
				}
			}

			.weui-btn {
				width: 242rpx;
				height: 84rpx;
				background: #0066E9;
				border-radius: 50rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 84rpx;
			}
		}

		.license-plate-0 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-1 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_green.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-2 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_black.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-3 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_white.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-4 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_gradient_green.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-5 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_yellow_green.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-6 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue_white_gradient.png') no-repeat;
			background-size: 100% 100%;
		}

		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}
	}
</style>