<template>
	<view class="add-invoice" @click="isShowSelect = false">
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="weui-cells__title__decoration">
					发票抬头信息
				</view>
				<view class="del-btn" v-if="operateType == '2'" @click="delTitleHandle">
					删除
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">抬头类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							<radio-group @change="invoiceChange" class="g-flex g-flex-end">
								<label class="uni-list-cell uni-list-cell-pd " v-for="(item, index) in invoiceOptions"
									:key="item.value">
									<view>
										<radio :value="item.value" class="cyan" style="transform: scale(0.7)"
											:checked="item.value==invoiceType" />
										<text>{{item.name}}</text>
									</view>

								</label>
							</radio-group>
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<block v-if="invoiceType == 1">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">姓名</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="name" :value="formData.name" class="weui-input"
								placeholder="请输入姓名" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</block>
				<block v-if="invoiceType == 2">
					<view class="vux-x-input weui-cell company-name">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="name" :value="formData.name" class="weui-input"
								placeholder="填写公司名称（必填）" placeholder-class="plc" @confirm="companyQuery"></input>
							<view class="more-list" v-if="isShowSelect">
								<scroll-view scroll-y="true" style="height: 383rpx;">
									<view class="more-list__item" @click="changeCompany(item)"
										v-for="(item,index) in companyList" :key="index">
										<view class="name">
											{{item.name}}
										</view>
										<view class="num">
											税号:{{item.taxNum}}
										</view>
									</view>
								</scroll-view>
							</view>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司税号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="taxNum" :value="formData.taxNum" class="weui-input"
								placeholder="填写公司税号（必填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">公司地址</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="address" :value="formData.address" class="weui-input"
								placeholder="填写公司地址（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">公司电话</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="tel" :value="formData.tel" class="weui-input"
								placeholder="填写公司电话（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">开户银行</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="bank" :value="formData.bank" class="weui-input"
								placeholder="填写公司开户行（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">银行账户</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="bankAccount" :value="formData.bankAccount"
								class="weui-input" placeholder="填写公司开户银行账户（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</block>
			</view>
		</view>
		<view class="weui-form" style="padding-bottom: 20rpx;" v-if="routeType != 'changeTitle'">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="weui-cells__title__decoration">
					关联车辆
				</view>
				<view class="bind-car" @click="bindVehicle" v-if="routeType != 'bindVehicle'">
					<view style="margin-right: 20rpx;">
						添加更多车辆
					</view>
					<image style="width: 14rpx;height: 26rpx;" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_arrow_right.png"
						mode=""></image>
				</view>
			</view>
			<view class="vehicle-desc" v-if="filterVehicleList.length == 0 && routeType != 'bindVehicle'">
				添加此抬头关联的ETC车辆，车辆通行后，发票抬头可自动关联此抬头
			</view>
			<view class="vehicle-list" v-if="filterVehicleList.length > 0">
				<template v-if="!isShowMore">
					<view class="select-list" v-for="(item, index) in sortList" :key="item.plateColor">
						<checkbox-group class="radio-group" @change="checked=>changeCheckbox(checked,item,index)">
							<label class="list-cell">
								<!-- <view style="margin-right: 10rpx;">
								<checkbox class="cyan checked  round" :checked='item.checked'
									style=" left:-4px;transform: scale(0.7)" value="check">
								</checkbox>
							</view> -->
								<view class="info">
									<view class="vehicle-item g-flex g-flex-horizontal-vertical"
										:class="'license-plate-'+item.plateColor"
										:style="{color:item.plateColor=='0'|| item.plateColor=='2' ? '#fff' : '#000'}">
										{{item.plateNum}}
									</view>
									<view class="card">
										{{item.cardId}}
									</view>
								</view>
							</label>
						</checkbox-group>
					</view>
				</template>
				<template v-else>
					<view class="select-list" v-for="(item, index) in filterVehicleList" :key="item.plateColor">
						<checkbox-group class="radio-group" @change="checked=>changeCheckbox(checked,item,index)">
							<label class="list-cell">
								<!-- <view style="margin-right: 10rpx;">
								<checkbox class="cyan checked  round" :checked='item.checked'
									style=" left:-4px;transform: scale(0.7)" value="check">
								</checkbox>
							</view> -->
								<view class="info">
									<view class="vehicle-item g-flex g-flex-horizontal-vertical"
										:class="'license-plate-'+item.plateColor"
										:style="{color:item.plateColor=='0'|| item.plateColor=='2' ? '#fff' : '#000'}">
										{{item.plateNum}}
									</view>
									<view class="card">
										{{item.cardId}}
									</view>
								</view>
							</label>
						</checkbox-group>
					</view>
				</template>
				<view class="more-icon" v-if="filterVehicleList.length > 2" @click="toggle">
					<image v-if="!isShowMore" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_more_down.png" mode=""></image>
					<image v-else src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_more_up.png" mode=""></image>
				</view>
			</view>
		</view>
		<view class="tips-wrapper">
			<view class="tips-title">
				温馨提示:
			</view>
			<view class="tips-item">
				1.按照国税局要求，ETC发票统一按上高速或充值前设置的默认抬头开票；
			</view>
			<view class="tips-item">
				2.如此前未设置抬头，则统一按照第一次添加的抬头开票。
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="onSubmitHandle">
						{{routeType == 'bindVehicle' ? '立即绑定' : '保存' }}
					</button>
				</view>
			</view>
		</view>
		<confirmDialog :show.sync="isShowConfirm" :rightBtnText="rightBtnText" :centerBtn="centerBtn"
			@comfirm="addTitle">
			<template v-slot:content>
				<view class="content">
					<view class="tips">
						{{tips}}
					</view>
					<view class="title-name">
						{{formData.name}}
					</view>
					<view class="btn-wrapper" v-if="centerBtn">
						<view class="btn-center" @click="toTollRecord">
							确定
						</view>
					</view>
				</view>
			</template>
		</confirmDialog>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import confirmDialog from '../../components/confirm-dialog.vue';
	import {
		getTollTicket
	} from "@/common/storageUtil.js";
	export default {
		components: {
			tLoading,
			confirmDialog
		},
		data() {
			return {
				isLoading: false,
				isShowMore: false, //显示更多
				isShowConfirm: false, //确认框
				centerBtn: false, //对话框按钮
				isShowSelect: false, //模糊查询
				height: '100%',
				formData: {
					name: '',
					taxNum: '',
					address: '',
					tel: '',
					bank: '',
					bankAccount: ''
				},
				titleId: '',
				invoiceType: '2',
				invoiceOptions: [{
						value: '2',
						name: '企业单位'
					},
					{
						value: '1',
						name: '个人/非企业单位'
					}
				],
				vehicleList: [],
				filterVehicleList: [],
				sortList: [],
				existList: [], //已有抬头数据
				rightBtnText: '',
				tips: '',
				operateType: '1', //操作类型,1是新增2是修改
				routeType: '',
				moreFlag: false,
				existFlag: false,
				//判断是否新增关联
				vehicleInfo: {}, //上个页面车辆信息
				companyList: [] //模糊查询列表
			};
		},
		onLoad(option) {
			if (option.operateType) {
				this.operateType = option.operateType || '1'
			}
			if (option.titleId) {
				this.titleId = option.titleId || ''
			}

			if (this.operateType == '2') {
				this.getDetail()
				this.getCarList()
			}

			if (option.routeType) {
				this.routeType = option.routeType
				uni.setNavigationBarTitle({
					title: '绑定发票抬头'
				})
				if (this.routeType == 'bindVehicle') {
					const item = JSON.parse(decodeURIComponent(option.vehicleInfo));
					this.vehicleInfo = item
					this.titleId = item.titleId || ''

					let obj = {
						plateColor: item.plateColor,
						plateNum: item.plateNum,
						cardId: item.cardId
					}
					this.filterVehicleList.push(obj)
					this.sortList.push(obj)
				}
			}
		},
		methods: {
			getCardStatus(status) {
				switch (status) {
					case 1:
						return '正常'
						break;
					case 2:
						//有卡挂起
						return '有卡挂起'
						break;
					case 3:
						//无卡挂起
						return '无卡挂起'
						break;
					case 4:
						//有卡注销
						return '有卡注销'
						break;
					case 5:
						//无卡注销
						return '无卡注销'
						break;
					case 6:
						return '卡挂失'
						break;
					default:
						break;
				}
			},
			toTollRecord() {
				//绑定成功，去开票
				uni.redirectTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollSelect/tollRecord?vehicleInfo=' +
						encodeURIComponent(JSON
							.stringify({
								...this.vehicleInfo,
								titleName: this.formData.name
							}))
				})
			},
			companyQuery(e) {
				console.log('e.detail.value', e.detail.value)
				let queryStr = e.detail.value
				this.isLoading = true

				let params = {
					ticketId: getTollTicket(),
					queryStr: queryStr,
				}

				this.$request
					.post(this.$interfaces.tollTitleSearch, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('模糊查询', res)
							let result = res.data
							this.isShowSelect = true
							this.companyList = result.items
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})

			},
			changeCompany(item) {
				this.formData.name = item.name
				this.formData.taxNum = item.taxNum
				this.formData.address = item.address
				this.formData.tel = item.tel
				this.formData.bank = item.bank
				this.formData.bankAccount = item.bankAccount

			},
			getDetail() {
				this.isLoading = true
				let params = {
					titleId: this.titleId,
					ticketId: getTollTicket()
				}
				this.$request
					.post(this.$interfaces.tollTitleDetail, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('title详情', res)
							let result = res.data
							this.formData.name = result.name
							this.formData.taxNum = result.taxNum || ''
							this.formData.address = result.address || ''
							this.formData.tel = result.tel || ''
							this.formData.bank = result.bank || ''
							this.formData.bankAccount = result.bankAccount || ''

							this.invoiceType = result.titleType

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			getCarList() {
				this.isLoading = true
				let params = {
					titleId: this.titleId,
					ticketId: getTollTicket(),
					pageIndex: 1,
					pageSize: 500
				}
				this.$request
					.post(this.$interfaces.tollTitleCardList, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('carList列表===>>>>', res)
							let result = res.data
							console.log('existFlag', this.existFlag)
							// if (this.operateType == '2') {
							//不是从校验过来的先加载初始化编辑数据
							if (!this.existFlag) {
								//拷贝一下数据
								this.filterVehicleList = [].concat(result.items)

								if (this.filterVehicleList.length > 0) {
									//设置选中状态
									for (let i = 0; i < this.filterVehicleList.length; i++) {
										// let status = this.getCardStatus(this.filterVehicleList[index].status)

										// this.$set(this.filterVehicleList[i], "status", status);
										this.$set(this.filterVehicleList[i], "checked", true);
									}
									console.log('filterVehicleList', this.filterVehicleList)
									if (this.filterVehicleList.length >= 2) {
										this.sortList = [this.filterVehicleList[0], this.filterVehicleList[1]]
									} else {
										this.sortList = this.filterVehicleList
									}

								}
							} else {
								//已存在抬头，替换
								this.existList = result.items
								this.tips = '此抬头及税号信息，您已添加完成，是否需替换原有抬头信息。'
								this.rightBtnText = '确定替换'
								this.isShowConfirm = true
							}
							// }



						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			delTitleHandle() {
				uni.showModal({
					title: '删除提示',
					content: '确认删除该抬头吗？',
					success: (res) => {
						if (res.confirm) {
							this.delTitle()
						}
					}
				})
			},
			delTitle() {
				this.isLoading = true
				let params = {
					titleId: this.titleId,
					ticketId: getTollTicket()
				}
				this.$request
					.post(this.$interfaces.tollTitleDelete, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '删除成功',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										let pages = getCurrentPages(); //获取所有页面栈实例列表
										let prevPage = pages[pages.length - 2]; //上一页页面实例
										console.log('pages', pages, prevPage)
										prevPage.$vm.getTitleList()
										uni.navigateBack({
											delta: 1
										})
									}
								}
							})

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			toggle() {
				this.isShowMore = !this.isShowMore
			},
			bindVehicle() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollInvoiceTitle/bindVehicle',
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						getVehicleList: (data) => {
							console.log('data=======>>>>>>>', data)
							this.vehicleList = data.vehicleList
							this.moreFlag = data.moreFlag
							let checkedFilter = this.vehicleList.filter(item => {
								return item.checked
							})
							this.filterVehicleList = checkedFilter

							//分割，只显示2个
							if (checkedFilter.length >= 2) {
								this.sortList = [checkedFilter[0], checkedFilter[1]]
							} else {
								this.sortList = checkedFilter
							}
						},
					},
					success: (res) => {
						if (this.operateType == '2') {
							//编辑的数据要回显已经关联的车辆
							res.eventChannel.emit('sendVehicleList', {
								vehicleList: this.filterVehicleList,
								operateType: this.operateType
							})
						} else {
							// 通过eventChannel向被打开页面传送数据
							res.eventChannel.emit('sendVehicleList', {
								vehicleList: this.vehicleList,
								operateType: this.operateType
							})
						}

					}
				})
			},
			invoiceChange(e) {
				this.invoiceType = e.detail.value
				if (this.invoiceType != e.detail.value) {
					for (let prop in this.formData) {
						if (this.formData.hasOwnProperty(prop)) {
							this.formData[prop] = '';
						}
					}
				}
			},
			modifyInput(e) {
				this.formData[e.currentTarget.id] = e.detail.value;
			},
			onValidHandle() {
				//1个人，2单位
				if (this.invoiceType == 1) {
					if (!this.formData.name) {
						uni.showModal({
							title: '提示',
							content: '请输入姓名',
							showCancel: false
						})
						return
					}
					return true;
				}
				if (this.invoiceType == 2) {
					if (!this.formData.name) {
						uni.showModal({
							title: '提示',
							content: '请输入公司名称',
							showCancel: false
						})
						return
					}
					if (!this.formData.taxNum) {
						uni.showModal({
							title: '提示',
							content: '请输入公司税号',
							showCancel: false
						})
						return
					}
					return true;
				}
			},
			//新增抬头
			onSubmitHandle() {
				if (!this.onValidHandle()) return;
				if (this.operateType == '2') {
					this.updateTitleHandle()
				} else {
					this.checkTitleExist()
				}
			},
			updateTitleHandle() {
				this.tips = '修改后，此抬头关联的车辆的通行/充值记录将按照修改后的信息进行开票，请确认是否需进行修改。'
				this.rightBtnText = '确定修改'
				this.isShowConfirm = true
			},
			checkTitleExist() {
				this.isLoading = true
				let params = {
					...this.formData,
					titleType: this.invoiceType,
					ticketId: getTollTicket()
				}
				this.$request
					.post(this.$interfaces.tollTitleExists, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('抬头是否存在校验===>>>', res)
							if (!res.data) {
								//可以新增，进行新增操作
								// this.addTitle()
								if (this.routeType == 'bindVehicle' || this.routeType == 'changeTitle') {
									//从车辆绑定进来
									this.addTitle()
								} else {
									//从抬头管理进来
									this.tips = '保存后，您当前选择添加的车辆，下一次通行/充值所开具的发票抬头以当前添加的抬头为准。'
									this.rightBtnText = '确定保存'
									//显示确认框
									this.isShowConfirm = true
								}

							} else {
								this.existFlag = true //区分原有抬头替换还是编辑修改
								//进行替换操作
								this.titleId = res.data
								this.getCarList()
							}

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			addTitle() {
				this.isShowConfirm = false
				this.isLoading = true
				//获取卡数组
				let cardIdArr = []
				let cardFinalArr = []

				if (this.moreFlag) {
					//如果修改过关联车辆的情况下，需要把旧的卡信息也绑定
					let existArr = this.existList.concat(this.filterVehicleList)
					console.log('existArr', existArr)
					existArr.forEach(item => {
						cardIdArr.push(item.cardId)
					})
					//去重
					const uniqueArr = Array.from(new Set(cardIdArr));
					uniqueArr.forEach(item => {
						cardFinalArr.push({
							cardId: item
						})
					})

				} else {
					this.filterVehicleList.forEach(item => {
						cardFinalArr.push({
							cardId: item.cardId
						})
					})
				}
				let params = {
					title: {
						...this.formData,
						titleType: this.invoiceType,
						ticketId: getTollTicket(),
					},
					infos: cardFinalArr,
					titleId: this.titleId || ''
				}

				console.log('新增抬头params=======>>>>>>>>>', params)
				this.$request
					.post(this.$interfaces.tollTitleAddProcess, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('发票抬头关联用户卡===>>>', res)
							this.isShowConfirm = false
							if (this.routeType == 'bindVehicle') {
								//绑定的弹框
								this.tips = '已将此抬头信息保存为车辆' + this.vehicleInfo.plateNum + '的常用发票抬头。'
								this.centerBtn = true
								this.isShowConfirm = true
							} else {
								//从抬头管理绑定用户卡
								uni.showModal({
									title: '提示',
									content: '保存成功',
									showCancel: false,
									success: (res) => {
										if (res.confirm) {
											let pages = getCurrentPages(); //获取所有页面栈实例列表
											let prevPage = pages[pages.length - 2]; //上一页页面实例
											console.log('pages', pages, prevPage)
											prevPage.$vm.getTitleList()
											uni.navigateBack({
												delta: 1
											})
										}
									}
								})
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			}
		}
	};
</script>

<style>
	.plc {
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #C6C6C6;
		line-height: 30rpx;
	}
</style>

<style scoped lang="scss">
	.add-invoice {
		overflow: hidden;
		padding-bottom: 180rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 200rpx;
	}

	.weui-cell {
		&:first-child {
			padding: 26rpx 0 26rpx 30rpx;
		}
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color: #0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}

	.del-btn {
		color: #0066E9;
		font-size: 26rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
	}

	.uni-list-cell {
		flex: 1;

		&:first-child {
			flex: 0 0 190rpx;
			width: 190rpx;
		}


	}

	.weui-cells__title {
		.bind-car {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #999999;
			line-height: 30rpx;
		}
	}

	.weui-form {
		margin: 20rpx 20rpx 0 20rpx;

		.vehicle-desc {
			height: 282rpx;
			margin: 0 20rpx;
			padding: 24rpx 20rpx 20rpx 20rpx;
			background: rgba(163, 163, 163, 0.1);
			border-radius: 10rpx;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #999999;
			line-height: 42rpx;
		}
	}


	.invoice-default {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		padding: 20rpx 30rpx;
		margin-top: 20rpx;

	}

	.invoice-default .invoice-default-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;

	}

	.invoice-default .invoice-default-hd .invoice-default-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.invoice-default .invoice-default-bd {
		flex: 1;
	}

	.invoice-default .invoice-default-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.invoice-default .invoice-default-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #999999;
	}

	.invoice-default .invoice-default-value {
		text-align: right;
	}

	.select-list {
		.radio-group {
			width: 100%;
		}

		.list-cell {
			margin-top: 20rpx;
			display: flex;
			align-items: center;

			.info {
				flex: 1;
				display: flex;
				align-items: center;
				height: 120rpx;
				padding: 0 20rpx;
				margin: 0 20rpx;
				background: #FFFFFF;
				box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.06);
				border-radius: 16rpx;
				border: 2rpx solid #ECECEC;

				.vehicle-item {
					width: 220rpx;
					height: 80rpx;
				}

				.card {
					margin-left: 20rpx;
				}
			}

			.plateColor {
				width: 202rpx;
				height: 68rpx;
				margin-right: 20rpx;
			}
		}
	}

	.vehicle-list {
		.more-icon {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 62rpx;
			margin-top: 30rpx;
			padding-top: 20rpx;
			border-top: 2rpx solid #F5F5F5;

			&>image {
				width: 39rpx;
				height: 29rpx;
			}
		}
	}

	.content {
		padding: 40rpx;

		.tips {
			font-size: 28rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #888888;
			line-height: 50rpx;
		}

		.title-name {
			// height: 76rpx;
			margin-top: 32rpx;
			padding: 0 20rpx;
			background: #E9E9E9;
			border-radius: 8rpx;
			font-size: 32rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #333333;
			line-height: 50rpx;
		}

		.btn-wrapper {
			display: flex;
			justify-content: center;
			width: 100%;
			margin-top: 50rpx;

			.btn-center {
				width: 232rpx;
				height: 71rpx;
				line-height: 71rpx;
				background: #0066E9;
				border-radius: 14rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				text-align: center;
			}
		}
	}

	.tips-wrapper {
		padding: 30rpx;
		font-size: 24rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		line-height: 40rpx;

		.tips-title {
			color: #FF9038;
		}

		.tips-item {
			color: #888888;
		}
	}

	.company-name {
		width: 100%;
		position: relative;

		.more-list {
			height: 384rpx;
			position: absolute;
			top: 100rpx;
			left: 20%;
			background-color: #ffffff;
			border-radius: 12rpx;
			box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
			z-index: 88;
		}

		.more-list__item {
			// margin: 0 20rpx;
			padding: 20rpx 40rpx;
			// height: 120rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			text-align: left;
			border-bottom: 1rpx solid #F2F2F2;

			&:last-of-type {
				border-bottom: none;
			}

			.name {
				margin-bottom: 10rpx;
			}
		}
	}

	.license-plate-0 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-1 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_green.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-2 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_black.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-3 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_white.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-4 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_gradient_green.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-5 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_yellow_green.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-6 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue_white_gradient.png') no-repeat;
		background-size: 100% 100%;
	}
</style>