<template>
	<view class="stub">
		<view class="card">
			<view class="font">
				开具通行费消费/充值发票、管理ETC车辆绑定的发票抬头、查看已开具的通行费发票等内容。
			</view>
			<view class="img-wrapper">
				<view class="name-wrapper">
					<view class="img">
						<image style="width: 154rpx;height: 154rpx;border-radius: 24rpx;"
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc-logo.png"
							mode=""></image>
						<view style="text-align: center;">
							{{custName}}
						</view>
					</view>
					<!-- 					<view class="custname">
						{{custName}}
					</view> -->
				</view>
				<view class="middle">
					<image class="img-middle" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/turn_icon.png" mode=""></image>
				</view>
				<view class="right">
					<image class="img-stub" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/invoice_icon.png" mode=""></image>
				</view>
			</view>
			<view class="input-wrapper">
				<view class="input-item border">
					<view class="label">
						手机号
					</view>
					<input type="number" v-model="formData.phone">
					<view class="btn" @click="selectEtc">
						切换手机号
					</view>
				</view>
				<view class="input-item">
					<view class="label">
						验证码
					</view>
					<input type="number" placeholder="请输入验证码" placeholder-class="plc" v-model="formData.code">
					<view class="btn" style="width: 220rpx;text-align: right;" @click="sendSMS">
						{{smsName}}
					</view>
				</view>
				<TButton title="立即绑定" @clickButton="bind" />
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getEtcAccountInfo,
		setDefaultUrl,
		setTollTicket,
		getLoginUserInfo
	} from '@/common/storageUtil.js';
	export default {
		components: {
			TButton,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				formData: {
					phone: '',
					code: ''
				},
				realyPhone: '',
				custName: '',
				time: null,
				smsName: '获取验证码',
			}
		},
		computed: {
			etcInfo() {
				return getEtcAccountInfo() || {}
			},
		},
		onLoad() {
			console.log('etcInfo', this.etcInfo)
			this.formData.phone = this.etcInfo.custMobile
			this.realyPhone = this.etcInfo.mobile
			this.custName = this.etcInfo.custName
		},
		methods: {
			bind() {
				if(!this.formData.code){
					uni.showModal({
						title: '提示',
						content: '请先输入验证码',
						showCancel: false,
					})
					return
				}
				this.isLoading = true
				let params = {
					userNo: getLoginUserInfo().userIdStr,
					loginInfo: {
						mobile: this.realyPhone,
						validCode: this.formData.code
					}
				}
				this.$request
					.post(this.$interfaces.tollAuthLogin, {
						data: params
					})
					.then((res) => {
						console.log(res)
						this.isLoading = false
						if (res.code == 200) {
							setTollTicket(res.data.ticketId)
							uni.showModal({
								title: '成功提示',
								content: '绑定票根平台' + res.data.info,
								confirmText: '去开票',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										uni.reLaunch({
											url: '/pagesB/invoiceBusiness/home/<USER>'
										})
									}
								}
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			sendSMS() {
				if (!this.time) {
					this.isLoading = true
					let countdown = 60
					let params = {
						mobile: this.realyPhone,
					}
					this.$request
						.post(this.$interfaces.tollAuthMobile, {
							data: params
						})
						.then((res) => {
							console.log(res)
							this.isLoading = false
							if (res.code == 200) {
								this.time = setInterval(() => {
									countdown = countdown - 1
									this.smsName = countdown + '秒后重新发送'
									if (countdown === 0) {
										clearInterval(this.time)
										this.time = null
										this.smsName = '重新发送'
									}
								}, 1000)
							} else {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false,
								})
							}
						})
						.catch((error) => {
							uni.showModal({
								title: '提示',
								content: error.msg,
								showCancel: false
							})
						})
				}
			},
			selectEtc() {
				//绑定ETC用户并回跳
				setDefaultUrl('/pagesB/invoiceBusiness/tollInvoice/bindStub/index')
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList'
				})
			},
		}
	}
</script>

<style>
	.plc {
		font-size: 28rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #C6C6C6;
		line-height: 30rpx;

	}
</style>

<style lang="scss" scoped>
	.border {
		border-bottom: 1rpx solid #f8f8f8;
	}

	.stub {
		overflow: hidden;
		padding: 20rpx;

		.card {
			padding: 20rpx;
			background: #FFFFFF;
			border-radius: 12rpx;

			.font {
				margin-top: 10rpx;
				margin-bottom: 20rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 42rpx;
			}

			.img-wrapper {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				height: 302rpx;
				background: #F8F8F8;
				border-radius: 12rpx;

				.name-wrapper {
					flex: 1;
					display: flex;
					justify-content: flex-end;
					align-items: center;
					padding-top: 40rpx;

					.custname {
						// flex: 1;
						display: flex;
						justify-content: center;
						align-items: center;
						width: 154rpx;
						height: 154rpx;
						color: #333333;
						font-weight: bold;
						// background: #D8D8D8;
						border-radius: 40rpx;

					}
				}

				.middle {
					width: 150rpx;
					display: flex;
					justify-content: center;
					align-items: center;

					&>image {
						height: 50rpx;
						width: 50rpx;
					}
				}

				.right {
					flex: 1;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&>image {
						height: 154rpx;
						width: 154rpx;
					}

				}
			}

			.input-wrapper {
				margin-top: 20rpx;

				.input-item {
					padding: 0 10rpx;
					height: 96rpx;
					display: flex;
					align-items: center;

					.label {
						width: 90rpx;
						margin-right: 70rpx;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #999999;
						line-height: 30rpx;
					}

					&>input {
						line-height: 96rpx;
						flex: 1;
					}

					.btn {
						width: 140rpx;
						height: 30rpx;
						font-size: 28rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #0066E9;
						line-height: 30rpx;
					}
				}

				/deep/.t-padding {
					padding: 0;
				}

				/deep/.cu-btn {
					margin-top: 90rpx;
					margin-bottom: 20rpx;
					height: 79rpx;
					background: #0066E9;
					border-radius: 14rpx;
					font-size: 32rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
				}
			}
		}


	}
</style>