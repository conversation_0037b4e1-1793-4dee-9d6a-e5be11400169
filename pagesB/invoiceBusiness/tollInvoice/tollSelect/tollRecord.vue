<template>
	<view class="toll-record">
		<!-- <view class="line-block"></view> -->
		<view class="weui-form">
			<view class="weui-cells" style="padding-top: 0;">
				<view class="vux-x-input weui-cell" style="width:100%;height: 96rpx;">
					<view class="weui-cell__hd">
						<view class="weui-label">发票类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="type" :class="selectType == 1 ?'selector' :''" @click="selectType = 1">
							消费发票
						</view>
						<view v-if="cardType == '22'" class="type" :class="selectType == 2 ?'selector' :''"
							@click="selectType = 2">
							充值发票
						</view>
					</view>
				</view>

				<view class="vux-x-input weui-cell" style="width:100%;height: 96rpx;">
					<view class="weui-cell__hd">
						<view class="weui-label">车牌号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-input" style="color: #333333;">
							{{vehicleInfo.plateNum}}【{{vehicleColorStr}}牌】
						</view>
					</view>
				</view>

				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">交易时间</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary" @click="openCalendar">
						<view class="weui-input" style="color: #333333;">
							{{applyTime}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell" style="width:100%;height: 150rpx;">
					<view class="btn-search" @click="onSearchHandle">
						查询
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="line-block"></view> -->
		<scroll-view v-if="vehicleList.length > 0 && !isLoading" :style="{height:height}" :scroll-top="scrollTop"
			scroll-y="true" class="scroll-Y" :lower-threshold='lowerThreshold' @scrolltoupper="upper"
			@scrolltolower="scrolltolower" @scroll="scroll">
			<view class="record-list" v-if="selectType == 1">
				<view class="record-item" v-for="(item,index) in vehicleList" :key="index">
					<checkbox-group class="record-group" @change="checked=>changeCheckbox(checked,item,index)">
						<label class="list-label">
							<view class="header">
								<checkbox class="checkbox cyan checked  round" :checked='item.checked'
									style="transform: scale(0.7)" value="check">
								</checkbox>
								<view class="name">
									{{item.titleName}}
								</view>
								<view class="status">
									{{item.invoiceProgressStatus == '1'?'未开票':'已开票'}}
								</view>
							</view>
							<view class="content">
								<view class="fee-item">
									<view class="label">
										通行金额:
									</view>
									<view class="fee">
										{{moneyFilter(item.fee)}}元
									</view>
								</view>
								<view class="info-item">
									<view class="label">
										入口:
									</view>
									<view class="info">
										{{item.enStationName}}
										{{ formatHandle(new Date(item.enTime).getTime(), 'yyyy-MM-dd HH:mm:ss') }}
									</view>
								</view>
								<view class="info-item">
									<view class="label">
										出口:
									</view>
									<view class="info">
										{{item.exStationName}}
										{{formatHandle(new Date(item.exTime).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
									</view>
								</view>
							</view>
						</label>
					</checkbox-group>
				</view>
			</view>

			<view class="record-list" v-else>
				<view class="record-item" v-for="(item,index) in vehicleList" :key="index">
					<checkbox-group class="record-group" @change="checked=>changeCheckbox(checked,item,index)">
						<label class="list-label">
							<view class="header">
								<checkbox class="checkbox cyan checked  round" :checked='item.checked'
									style="transform: scale(0.7)" value="check">
								</checkbox>
								<view class="name">
									{{item.titleName}}
								</view>
								<view class="status">
									{{item.invoiceProgressStatus == '1'?'未开票':'已开票'}}
								</view>
							</view>
							<view class="content">
								<view class="fee-item">
									<view class="label">
										充值金额:
									</view>
									<view class="fee">
										{{moneyFilter(item.fee)}}元
									</view>
								</view>
								<view class="info-item">
									<view class="label" style="flex:0 0 150rpx;width: 150rpx;">
										充值时间:
									</view>
									<view class="info">
										{{formatHandle(new Date(item.time).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
									</view>
								</view>
							</view>
						</label>
					</checkbox-group>
				</view>
			</view>
		</scroll-view>
		<view class="no-data" v-if="vehicleList.length == 0 && !isLoading">
			<image src="../../../static/no_data.png" mode="" class="no-data-img"></image>
			<view class="no-data-title">暂无记录</view>
		</view>
		<view class="weui-bottom-fixed" v-if="vehicleList.length > 0">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<checkbox-group class="g-flex g-flex-align-center" @change="checkAll">
						<label class="g-flex g-flex-align-center" style="line-height: 52rpx;">
							<checkbox class="cyan checked  round" :checked='isCheckAll'
								style=" transform: scale(0.8,0.8)" value="check">
							</checkbox>
							<view style="margin-left: 10rpx;">全选可开票记录</view>
						</label>
					</checkbox-group>
				</view>
				<view class="btn-item">
					<view class="count">
						共￥<text>{{moneyFilter(totalPrice)}} </text>
					</view>
					<button class="weui-btn weui-btn_primary" style="background-color: #0066E9;" @click="toConfirm">
						去开票({{totalCount}})
					</button>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" background-color="#FFFFFF;">
			<view class="calendar-wrapper">
				<view class="calendar-title g-flex g-flex-align-center">
					<view class="cancel" @click="close">
						取消
					</view>
					<view class="confirm" @click="timeConfirm">
						确认
					</view>
				</view>
				<view class="calendar-year">
					{{currentYear}}
				</view>
				<view class="calendar-month">
					<view class="month" :style="{color:monthRecord.includes(index + 1)?'#F65B5B':''}"
						:class="((index + 1) == selectMonth && selectYear == currentYear)? 'selector':''"
						v-for="index of lastMonth" :key="index" @click="selectMonthHandle('current',index + 1)">
						{{index + 1}}月
					</view>
				</view>
				<view class="calendar-year">
					{{lastYear}}
				</view>
				<view class="calendar-month">
					<view class="month" :style="{color:lastMonthRecord.includes(index + 1)?'#F65B5B':''}"
						:class="((index + 1) == selectMonth && selectYear == lastYear) ? 'selector':''"
						v-for="index of 12" :key="index" @click="selectMonthHandle('last',index + 1)">
						{{index + 1}}月
					</view>
				</view>
				<view class="calendar-bottom">
					<view class="bottom-left">
						<view class="circle"></view>
						<view class="bottom-text">
							已选月份
						</view>
					</view>
					<view class="bottom-right">
						<view class="circle red"></view>
						<view class="bottom-text">
							{{recordDesc}}
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import {
		getVehicleColor
	} from '@/common/method/filter.js';
	import {
		getTollTicket
	} from '@/common/storageUtil.js';
	import {
		moneyFilter,
		formatHandle
	} from '@/common/util.js'
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				height: 'calc(100% - 422rpx)',
				vehicleList: [],
				orderListData: [],
				vehicleInfo: {},
				selectType: 1, //1消费类型，2充值类型
				totalPrice: 0,
				totalCount: 0,
				pageIndex: 1,
				pageSize: 20,
				isCheckAll: false,
				flag: false, //分页flag
				applyTime: '',
				yearList: [],
				selectYear: '',
				selectMonth: '',
				lastMonth: '',
				currentYear: '',
				currentMonth: '',
				lastYear: '',
				yearIndex: [],
				monthRecord: [],
				lastMonthRecord: [],
				cardType: '',

			}
		},
		computed: {
			vehicleColorStr() {
				return getVehicleColor(this.vehicleInfo.plateColor);
			},
			recordDesc() {
				if (this.selectType == 1) {
					return '通行记录'
				} else if (this.selectType == 2) {
					return '消费记录'
				}
			}
		},
		onLoad(option) {
			const item = JSON.parse(decodeURIComponent(option.vehicleInfo));
			console.log('item', item)
			this.vehicleInfo = item
			//获取卡类型22储值卡23记账卡
			this.cardType = this.vehicleInfo.cardId.substring(8, 10) || ''
			// this.getRecordList()
			this.setCalendar()
		},
		watch: {
			selectType(val) {
				if (val) {
					this.pageIndex = 1
					this.vehicleList = []
					this.flag = false
					this.setCalendar()
				}
			},
			// selectYear(val) {
			// 	if (val == this.currentYear) {
			// 		this.lastMonth = this.currentMonth
			// 	} else {
			// 		this.lastMonth = 12
			// 	}
			// }
		},
		methods: {
			moneyFilter,
			formatHandle,
			getMonthList(type, year) {
				// this.isLoading = true
				let params = {
					cardNo: this.vehicleInfo.cardId,
					trafficYear: year,
					ticketId: getTollTicket(),
					queryType: this.selectType == 1 ? '0' : '2'
				}
				this.$request
					.post(this.$interfaces.tollTrafficMonth, {
						data: params
					})
					.then((res) => {
						// this.isLoading = false;
						if (res.code == 200) {
							console.log('月份记录====>>>>res', res)
							//处理月份
							let result = res.data
							if (type == 'current') {
								this.monthRecord = []
								result.forEach(item => {
									if (item.indexOf("0") == 0) {
										item = parseInt(item.replace("0", ""));
									}
									item = parseInt(item);
									this.monthRecord.push(item)
								})
							}
							if (type == 'last') {
								this.lastMonthRecord = []
								result.forEach(item => {
									if (item.indexOf("0") == 0) {
										item = parseInt(item.replace("0", ""));
									}
									item = parseInt(item);
									this.lastMonthRecord.push(item)
								})
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						// this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			close() {
				this.$refs.popup.close()
			},
			openCalendar() {
				this.$refs.popup.open('bottom')
			},
			timeConfirm() {
				this.applyTime = this.selectYear + '-' + this.formatMonth(this.selectMonth)
				this.close()
				// this.onSearchHandle()
			},
			setCalendar() {
				// 获取当前日期
				const currentDate = new Date();
				const currentYear = currentDate.getFullYear();
				const lastYear = currentDate.getFullYear() - 1;
				const currentMonth = currentDate.getMonth() + 1; // 月份从0开始，所以要加1				

				this.currentYear = currentYear
				this.lastYear = lastYear
				this.currentMonth = currentMonth
				this.lastMonth = currentMonth
				this.selectYear = currentYear
				this.selectMonth = currentMonth

				console.log('selectMonth', this.selectMonth)

				this.applyTime = this.selectYear + '-' + this.formatMonth(this.selectMonth)

				// 从1950年开始迭代到当前年份
				for (let year = 1950; year <= currentYear; year++) {
					// console.log(`年份：${year}`);
					this.yearList.push({
						label: year + '年',
						value: year
					})
					// console.log('this.yearList', this.yearList)
				}
				this.yearIndex = [currentYear - 1950]
				console.log('this.yearIndex', this.yearIndex)

				//获取记录月份
				this.getMonthList('current', this.currentYear)
				this.getMonthList('last', this.lastYear)
				//计算完日期后初始化数据
				this.onSearchHandle()
			},
			selectMonthHandle(type, month) {
				if (type == 'current') {
					this.selectYear = this.currentYear
				} else {
					this.selectYear = this.lastYear
				}
				this.selectMonth = month
			},
			formatMonth(month) {
				return month < 10 ? '0' + month : '' + month;
			},
			onSearchHandle() {
				this.pageIndex = 1
				this.vehicleList = []
				this.flag = false
				this.getRecordList()
			},
			getRecordList() {
				this.orderListData = this.pageIndex == 1 ? [] : this.vehicleList;
				this.flag = false
				this.isLoading = true
				let params = {
					ticketId: getTollTicket(), //凭证
					cardId: this.vehicleInfo.cardId, //车牌模糊查询
					month: this.applyTime, //月份
					pageIndex: this.pageIndex,
					pageSize: this.pageSize
				}
				let url = ''
				if (this.selectType == 1) {
					url = this.$interfaces.tollInvoiceTransList
				} else {
					url = this.$interfaces.tollInvoiceRechargeList
				}

				this.$request
					.post(url, {
						data: params
					})
					.then((res) => {
						console.log('res', res)
						if (res.code == 200) {
							let result = res.data
							this.orderListData = result.items ? result.items : []
							this.vehicleList = this.vehicleList.concat(this.orderListData)
							if (result.items.length == 0 && this.pageIndex != 1) {
								this.flag = true
							}

							// 拷贝一下数据
							if (this.vehicleList.length > 0) {
								//设置选中状态
								for (let i = 0; this.vehicleList.length > i; i++) {
									if (!this.vehicleList[i].hasOwnProperty('checked')) {
										this.$set(this.vehicleList[i], "checked", false);
									}
								}
							}

							//更新全选框状态
							this.isCheckAllFlag();
							//更新选择数量
							this.countTableArrList()
							this.isLoading = false;
						} else {
							this.isLoading = false;
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			toConfirm() {
				let vehicleFilter = this.vehicleList.filter(item => {
					return !!item.checked
				})
				if (vehicleFilter.length == 0) {
					uni.showModal({
						title: '提示',
						content: '请先选择需要开票的记录',
						showCancel: false
					})
					return
				}
				let item = {
					...this.vehicleInfo,
					month: this.applyTime,
					totalCount: this.totalCount,
					totalPrice: this.totalPrice,
					vehicleList: vehicleFilter,
					selectType: this.selectType
				}
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollSelect/confirmInvoice?vehicleInfo=' +
						encodeURIComponent(JSON
							.stringify(item))
				})
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				if (this.flag) return;
				let self = this;
				setTimeout(function() {
					self.pageIndex = self.pageIndex + 1;
					self.getRecordList();
				}, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
			//单选
			changeCheckbox(checked, item, index) {
				//选中数据push到数组里
				if (checked.detail.value.length > 0 && checked.detail.value[0] == 'check') {
					//选中
					this.$set(this.vehicleList[index], "checked", true);
				} else {
					//未选中
					this.$set(this.vehicleList[index], "checked", false);
				}
				//更新全选框状态
				this.isCheckAllFlag();
				//更新选择数量
				this.countTableArrList()
				console.log('this.vehicleList', this.vehicleList)
			},
			checkAll(checked) {
				console.info('check all change is ', checked)
				let checkFlag = false
				if (checked.detail.value.length > 0 && checked.detail.value[0] == 'check') {
					//选中
					checkFlag = true
				} else {
					//未选中
					checkFlag = false
				}
				console.log('checkFlag', checkFlag)
				for (let i = 0; i < this.vehicleList.length; i++) {
					this.$set(this.vehicleList[i], "checked", checkFlag);
				}
				console.log('checkFlagthis.tableData', this.vehicleList)
				//更新全选框状态
				this.isCheckAllFlag();
				//更新选择数量
				this.countTableArrList()
			},
			isCheckAllFlag() {
				console.log("表格数据=========>>>>>", this.vehicleList);
				if (this.vehicleList.length == 0) {
					this.isCheckAll = false
					return
				}
				let filterArr = this.vehicleList.filter((item) => {
					return !item.checked;
				});
				//全选按钮判定
				if (filterArr.length > 0) {
					this.isCheckAll = false;
				} else {
					this.isCheckAll = true;
				}
				console.log("this.isCheckAllFlag", filterArr.length, this.isCheckAll);
			},
			countTableArrList() {
				if (this.vehicleList.length == 0) {
					this.totalCount = 0;
					this.totalPrice = 0
					return
				}
				let totalCount = 0;
				let totalPrice = 0;
				this.vehicleList.forEach((item) => {
					if (item.checked) {
						totalCount++;
						totalPrice += item.fee
					}
				});
				this.totalCount = totalCount;
				this.totalPrice = totalPrice
				console.log("this.countTableArrList", this.totalCount, this.totalPrice);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.toll-record {
		overflow: hidden;
		// position: ;
		width: 100%;
		// height: calc(100% - 134rpx);
		padding: 20rpx 0;
		height: calc(100% - 154rpx);
		// padding-bottom: 154rpx;

		.line-block {
			height: 20rpx;
			width: 100%;
			background-color: #F6F6F6;
		}

		.weui-form {
			height: 442rpx;
			min-height: 442rpx;
			margin-top: 0rpx;

		}

		.picker {
			width: 100%;
			display: flex;
			height: 100%;
			align-items: center;
		}

		/deep/.u-border-bottom::after {
			border-bottom: none;
		}

		.pick-date {
			width: 192rpx;
			display: flex;
			align-items: center;

			/deep/.u-cell {
				position: relative;
			}

			/deep/.u-cell__value {
				font-size: 30rpx !important;
			}

			/deep/.u-cell__left-icon-wrap {
				position: absolute;
				right: 0;
				margin-right: 0px !important;
			}

			/deep/.u-icon__icon {
				font-size: 25rpx !important;
				color: #999999;
			}
		}

		.pick-date-two {
			// flex: 1;
		}

		/deep/.u-cell {
			padding: 0 0;
			line-height: 80rpx;
		}

		/deep/.u-cell__value {
			color: #333;
			text-align: left;
			font-size: 30rpx;
		}

		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}
	}

	.activation-page {
		position: relative;
	}

	.weui-cells {
		padding-top: 0;
	}

	.weui-cell {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0rpx 30rpx;
	}

	.weui-cell:before {
		right: 30rpx;
	}

	.btn-search {
		width: 100%;
		height: 79rpx;
		line-height: 79rpx;
		text-align: center;
		background: #0066E9;
		color: #ffffff;
		border-radius: 14rpx;
	}

	.weui-cell__bd {
		display: flex;
		justify-content: flex-end;

		.type {
			width: 226rpx;
			height: 63rpx;
			line-height: 63rpx;
			text-align: center;
			background: #FFFFFF;
			border-radius: 8rpx 0rpx 0rpx 8rpx;
			border: 1rpx solid #0066E9;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #0066E9;
		}

		.type:nth-child(2) {
			border-radius: 0rpx 8rpx 8rpx 0rpx;
			margin-left: 16rpx;
		}

		.selector {
			background: #0066E9;
			color: #FFFFFF;
		}
	}

	.weui-cells::before {
		border: 0;
	}

	.weui-label {
		width: 180rpx;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 96rpx;
	}

	.weui-cell_picker .weui-picker-value {
		text-align: left;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 96rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 96rpx;
	}

	.search-btn {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #ffffff;
		margin-top: 1rpx;
	}

	.weui-btn {
		flex: 1;
		margin-top: 0;
		// margin-right: 20rpx;
		background-color: #0066E9;
	}

	.fixed-top {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		// bottom: 0;
		overflow: hidden;
		z-index: 10;
		background-color: #F3F3F3;
	}

	.scroll-box {
		padding-top: 358rpx;
		padding-bottom: 20rpx;
	}

	.apply-record {

		color: #01C1B2;
		border: 1rpx solid #01C1B2;
		background: transparent;

	}

	.record-list {
		margin: 0 20rpx;
		background-color: #f3f3f3;
		padding: 20rpx 0;

		.record-item {
			margin-bottom: 20rpx;
			padding-bottom: 30rpx;
			background-color: #ffffff;
		}

		.header {
			display: flex;

			.checkbox {
				margin: 25rpx 0 25rpx 20rpx;
			}

			.checkbox::before {
				margin-top: -20rpx;
			}

			.name {
				flex: 1;
				margin: 25rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: 40rpx;
			}

			.status {
				width: 169rpx;
				height: 63rpx;
				line-height: 63rpx;
				text-align: center;
				background: rgba(133, 134, 134, 0.1);
				border-radius: 0rpx 8rpx 0rpx 30rpx;
			}
		}

		.content {
			.fee-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 10rpx 30rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				line-height: 42rpx;

				.label {
					flex: 0 0 150rpx;
					width: 150rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #999999;
					line-height: 42rpx;
				}

				.fee {
					flex: 1;
					text-align: right;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #FF9038;
					line-height: 42rpx;
				}
			}

			.info-item {
				display: flex;
				// align-items: center;
				justify-content: space-between;
				padding: 10rpx 30rpx;

				.label {
					flex: 0 0 90rpx;
					width: 90rpx;
					margin-top: 2rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #999999;
					line-height: 42rpx;
				}

				.info {
					flex: 1;
					text-align: right;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 42rpx;
				}
			}
		}
	}


	.weui-tag {
		// background: rgba(0, 189, 50, 0.1);
		border-radius: 0rpx 0rpx 0rpx 30rpx;
		display: inline-block;
		height: 62rpx;
		padding: 0 20rpx;
		line-height: 62rpx;
		min-width: 170rpx;
		font-size: 26rpx;
		text-align: center;
		// color: #3874FF;
		box-sizing: border-box;
		white-space: nowrap;
	}


	.weui-title__decoration {
		position: relative;

	}

	.weui-title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		background: #333333;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background: #333333;
	}

	.weui-bottom-fixed__box {
		padding: 20rpx 26rpx 48rpx 26rpx;
	}

	.btn-item {
		display: flex;
		align-items: center;

		.count {
			margin-right: 10rpx;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #4F4F4F;
			color: #FF9100;
			line-height: 58rpx;

			&>text {
				font-size: 36rpx;
				font-family: DINCondensed, DINCondensed;
				font-weight: bold;
				color: #FF9100;
				line-height: 58rpx;
			}
		}

		.weui-btn {
			width: 222rpx;
			height: 84rpx;
			background: #0066E9;
			border-radius: 50rpx;
			font-size: 32rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 84rpx;
		}
	}

	/deep/.uni-popup {
		z-index: 1000;
	}

	.calendar-wrapper {
		height: 700rpx;

		.calendar-title {
			justify-content: space-between;
			height: 98rpx;
			line-height: 98rpx;
			padding: 0 36rpx 0 44rpx;
			border-bottom: 1rpx solid #EEEEEE;

			.cancel {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 42rpx;
			}

			.confirm {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #0066E9;
				line-height: 42rpx;
			}
		}

		.calendar-year {
			height: 82rpx;
			line-height: 82rpx;
			font-size: 36rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			// color: #0066E9;
			text-align: center;
			background-color: #f8f8f8;
		}

		.calendar-month {

			padding: 0rpx 16rpx 54rpx 16rpx;

			.month {
				display: inline-block;
				height: 45rpx;
				width: 94rpx;
				margin-right: 30rpx;
				margin-top: 50rpx;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: 45rpx;
				text-align: center;
			}

			.month:nth-child(6),
			.month:nth-child(12) {
				margin-right: 0;
			}

			.selector {
				background: #0066E9;
				border-radius: 6rpx;
				color: #FFFFFF !important;
			}
		}

		.calendar-bottom {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 84rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 33rpx;
			background-color: #f8f8f8;


			.bottom-left,
			.bottom-right {
				display: flex;
				align-items: center;
			}

			.bottom-left {
				margin-right: 50rpx;
			}

			.circle {
				width: 15rpx;
				height: 15rpx;
				margin-right: 10rpx;
				background: #0066E9;
				border-radius: 50%;

			}

			.red {
				background: #F65B5B;
			}
		}
	}

	.no-data {
		width: calc(100% - 40rpx);
		text-align: center;
		background: #fff;
		margin-left: 20rpx;
		margin-top: 20rpx;
		border-radius: 11rpx;
		height: 430rpx;

		.no-data-img {
			width: 248rpx;
			height: 269rpx;
			background-size: 100%;
			margin-top: 62rpx;
		}

		.no-data-title {
			height: 40rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #333333;
			line-height: 40rpx;
		}
	}
</style>