<template>
	<view class="confirm-invoice">
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="weui-cells__title__decoration">
					车辆当前已关联抬头
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">抬头类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							<radio-group class="g-flex g-flex-end">
								<label class="uni-list-cell uni-list-cell-pd " v-for="(item, index) in invoiceOptions"
									:key="item.value">
									<view v-if="item.value == vehicleInfo.titleType">
										<radio :value="item.value" class="cyan" style="transform: scale(0.7)"
											:checked="item.value == vehicleInfo.titleType" />
										<text>{{item.name}}</text>
									</view>

								</label>
							</radio-group>
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<block v-if="vehicleInfo.titleType == 1">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">姓名</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input :disabled="true" id="name" :value="vehicleInfo.vehicleList[0].titleName || vehicleInfo.name"
								class="weui-input" placeholder="请输入姓名" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</block>
				<block v-if="vehicleInfo.titleType == 2">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input :disabled="true" id="name" :value="vehicleInfo.vehicleList[0].titleName || vehicleInfo.name"
								class="weui-input" placeholder="填写公司名称（必填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司税号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input :disabled="true" id="taxNum" :value="vehicleInfo.taxNum" class="weui-input"
								placeholder="填写公司税号（必填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">公司地址</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input :disabled="true" id="address" :value="vehicleInfo.address" class="weui-input"
								placeholder="填写公司地址（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">公司电话</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input :disabled="true" id="tel" :value="vehicleInfo.tel" class="weui-input"
								placeholder="填写公司电话（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">开户银行</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input :disabled="true" id="bank" :value="vehicleInfo.bank" class="weui-input"
								placeholder="填写公司开户行（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">银行账户</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input :disabled="true" id="bankAccount" :value="vehicleInfo.bankAccount" class="weui-input"
								placeholder="填写公司开户银行账户（选填）" placeholder-class="plc"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</block>
			</view>
		</view>
		<view class="price-wrapper" @click="toRecord">
			<view class="label">
				总金额
			</view>
			<view class="price">
				<text>{{moneyFilter(vehicleInfo.totalPrice)}}</text> 元
			</view>
			<view class="record">
				<view class="record-text">
					共{{vehicleInfo.totalCount}}条{{vehicleInfo.selectType == '1' ? '消费':'充值'}}记录
				</view>
				<image style="width: 14rpx;height: 26rpx;" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_arrow_right.png"
					mode=""></image>
			</view>
		</view>
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="weui-cells__title__decoration">
					接收方式
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">电子邮箱</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input id="name" v-model="invoiceMail" class="weui-input" placeholder="用于向您发送电子发票（必填）"
							placeholder-class="plc"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="tips">
			<view class="tips-title">
				温馨提示:
			</view>
			<view class="tips-item">
				1.当您途径的高速路段有多个道路公司共同管辖时，依据税务规定，道路公司将分别出具1张ETC发票，您实际收到的发票张数会大于1张。
			</view>
			<view class="tips-item">
				2.ETC发票统一按上高速前设置的默认抬头开票，如需变更默认抬头，请通过[常用发票抬头功能]修改
			</view>
			<view class="tips-item">
				3.如您的车辆曾绑定多个抬头，则有可能会开出多个抬头的发票。
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="onSubmitHandle">
						提交
					</button>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getTollTicket
	} from "@/common/storageUtil.js";
	import {
		checkEmailReg,
		moneyFilter
	} from "@/common/util.js";
	import {
		info
	} from '../../../../common/method/log';
	export default {
		components: {
			tLoading,
		},
		data() {
			return {
				isLoading: false,
				invoiceOptions: [{
						value: '2',
						name: '企业单位'
					},
					{
						value: '1',
						name: '个人/非企业单位'
					}
				],
				vehicleInfo: {},
				invoiceMail: ''
			};
		},
		onLoad(option) {
			const item = JSON.parse(decodeURIComponent(option.vehicleInfo));
			console.log('item', item)
			if (!item.titleType) {
				//没有titleType说明是从新增车辆过来的，拿不到需要查询
				this.getTitleData(item)
			} else {
				this.vehicleInfo = item
			}
		},
		methods: {
			moneyFilter,
			getTitleData(item) {
				this.isLoading = true

				let params = {
					ticketId: getTollTicket(),
					queryStr: item.titleName,
				}
				this.$request
					.post(this.$interfaces.tollTitleSearch, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						console.log('获取发票确认开票抬头===>>>', res)
						if (res.code == 200) {
							this.vehicleInfo = {
								...res.data.items[0],
								...item
							}
							console.log('this.vehicleInfo===>>>', this.vehicleInfo)
						} else {
							uni.showModal({
								title: '提示',
								content: error.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			onSubmitHandle() {
				if (!this.validData()) return
				this.isLoading = true

				let infos = []
				this.vehicleInfo.vehicleList.forEach(item => {
					infos.push({
						tradeId: item.tradeId
					})
				})

				let params = {
					applyRequest: {
						...this.vehicleInfo,
						ticketId: getTollTicket(),
						invoiceMail: this.invoiceMail,
						month: this.vehicleInfo.month,
						infos: infos
					},
					items: this.vehicleInfo.vehicleList
				}
				delete params.applyRequest.totalCount
				delete params.applyRequest.totalPrice
				delete params.applyRequest.vehicleList
				delete params.applyRequest.selectType

				let url = ''
				if (this.vehicleInfo.selectType == '1') {
					url = this.$interfaces.tollInvoiceTransApply
				} else {
					url = this.$interfaces.tollInvoiceRechargeApply
				}

				this.$request
					.post(url, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=2&descType=open&cardNo=' +
									this.vehicleInfo.cardId
							})
						} else {
							let errorText = '错误码【' + res.code + '】，' + res.msg
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=5&descType=open&errorText=' +
									errorText
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			validData() {
				if (!this.invoiceMail) {
					uni.showModal({
						title: '提示',
						content: '请先输入接收邮箱!',
						showCancel: false
					})
					return false
				}
				if (!checkEmailReg(this.invoiceMail)) {
					uni.showModal({
						title: '提示',
						content: '请输入正确的邮箱格式!',
						showCancel: false
					})
					return false
				}
				return true
			},
			toRecord() {
				uni.navigateTo({
					url: './recordDetail?vehicleInfo=' +
						encodeURIComponent(JSON
							.stringify(this.vehicleInfo))
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.confirm-invoice {
		overflow: hidden;
		padding-bottom: 180rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 200rpx;
	}

	.weui-cell {
		&:first-child {
			padding: 26rpx 0 26rpx 30rpx;
		}
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color: #0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}

	.del-btn {
		color: #0066E9;
		font-size: 26rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
	}

	.uni-list-cell {
		flex: 1;

		&:first-child {
			flex: 0 0 190rpx;
			width: 190rpx;
		}


	}

	.weui-form {
		margin: 20rpx 20rpx 0 20rpx;
	}

	.price-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 20rpx;
		padding: 26rpx 30rpx;
		background-color: $uni-bg-color;

		.label {
			width: 200rpx;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 30rpx;
		}

		.price {
			flex: 1;

			&>text {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				color: #FF9038;
				line-height: 30rpx;
				padding-right: 6rpx;
			}
		}

		.record {
			display: flex;
			align-items: center;

			.record-text {
				margin-right: 26rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 30rpx;
			}
		}

	}

	.tips {
		padding: 30rpx;
		font-size: 24rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		line-height: 40rpx;

		.tips-title {
			color: #FF9038;
		}

		.tips-item {
			margin-bottom: 6rpx;
			color: #888888;
		}
	}
</style>