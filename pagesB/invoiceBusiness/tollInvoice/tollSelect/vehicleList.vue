<template>
	<view class="vechicle-list">
		<view class="content">
			<commonTitle title="请选择需开票的车辆">
				<template #rightBtn v-if="!type">
					<view class="question-wrapper" @click="add">
						<view class="question-text">
							新增开票车辆
						</view>
					</view>
				</template>
			</commonTitle>
			<view class="input-wrapper">
				<input class="input" v-model="plateNumQueryStr" type="text" placeholder="输入车牌号快速定位需开票车辆"
					placeholder-class="plc">
				<view class="clear-icon" v-if="plateNumQueryStr" @click="getBindCardVehicle">
					<icon class="" type="search" size="20" />
				</view>
			</view>
			<template v-if="listData.length > 0">
				<view class="weui-media" v-for="(item,index) in listData" :key="index" @click="checkTitle(item)">
					<view class="weui-media-hd">
						<!-- 	<image v-if="item.vehicleType =='2'" src="../../../static/vehicleIcon/car_icon.png" mode="aspectFilt"
						class="weui-media-hd_icon">
					</image> -->
						<image src="../../../static/vehicleIcon/truck_icon.png" mode="aspectFilt"
							class="weui-media-hd_icon">
						</image>
						<!-- 	<image v-if="item.vehicleType =='3'" src="../../../static/vehicleIcon/priatecar_icon.png" mode="aspectFilt"
						class="weui-media-hd_icon">
					</image> -->
					</view>
					<view class="weui-media-bd">
						<!-- <view class="title">{{item.vehicleCode}}【{{getVehicleColor(item.vehicleColor)}}牌】</view> -->
						<view class="title">{{item.plateNum}}【{{getVehicleColor(item.plateColor)}}牌】</view>
						<view class="value">{{noPassByCardNo(item.cardId)}}
							<text>({{getTollCardStatus(item.status)}})</text>
						</view>
					</view>
					<view class="weui-media-ft">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/right.png" mode="aspectFilt" class="weui-media-ft_icon">
						</image>
					</view>
				</view>
			</template>
			<view class="no-data" v-if="listData.length == 0 && !isLoading">
				<image src="../../../static/no_data.png" mode="" class="no-data-img"></image>
				<view class="no-data-title" v-if="plateNumQueryStr && !type">搜索不到车辆，点击右上角按钮新增</view>
				<view class="no-data-title" v-if="!plateNumQueryStr && !type">暂无绑定开票的车辆，点击右上角按钮新增</view>
				<view class="no-data-title" v-if="plateNumQueryStr && type">搜索不到车辆</view>
			</view>
		</view>
		<confirmDialog class="dialog" :show.sync="isShowConfirm" :rightBtnText="'立即绑定'" @comfirm="addTitle">
			<template v-slot:content>
				<view class="content">
					<view class="tips">
						{{vehicleInfo.plateNum}}当前未绑定发票抬头，按照国税局要求，需绑定发票抬头后才能开具通行费发票，请先绑定。
					</view>
				</view>
			</template>
		</confirmDialog>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import commonTitle from '@/pagesB/components/common-title/common-title.vue'
	import tLoading from '@/components/common/t-loading.vue'
	import confirmDialog from '../../components/confirm-dialog.vue';
	import {
		getVehicleColor,
		getTollCardStatus
	} from '@/common/method/filter.js'
	import {
		noPassByCardNo,
	} from '@/common/util.js'
	import {
		getEtcAccountInfo,
		getTollTicket
	} from '@/common/storageUtil.js';
	export default {
		components: {
			commonTitle,
			tLoading,
			confirmDialog
		},
		data() {
			return {
				isLoading: false,
				isShowConfirm: false,
				plateNumQueryStr: '',
				listData: [],
				searchList: [],
				vehicleInfo: {},
				type: '', //区分历史记录和新增车辆
			}
		},
		computed: {
			etcInfo() {
				return getEtcAccountInfo() || {}
			},
			ticketId() {
				return getTollTicket() || ''
			},
			userType() {
				let userType = ''
				this.etcInfo.custType == '0' ? userType = '1' : userType = '2'
				return userType
			}
		},
		watch: {
			plateNumQueryStr(val) {
				if (!val) {
					this.getBindCardVehicle()
				}
			}
		},
		onLoad(option) {
			// this.getBindCardVehicle()
			console.log('etcInfo', this.etcInfo)
			this.getBindCardVehicle()
			if (option.type) {
				this.type = option.type
			}
		},
		methods: {
			getVehicleColor,
			getTollCardStatus,
			noPassByCardNo,
			checkTitle(item) {
				if (this.type == 'history') {
					uni.navigateTo({
						url: '/pagesB/invoiceBusiness/tollInvoice/tollHistory/index?cardNo=' + item.cardId
					})
					return
				}
				if (!item.titleId) {
					//没有抬头需要去绑定
					this.isShowConfirm = true
					this.vehicleInfo = item

				} else {
					//已经绑定过了，去开票
					uni.navigateTo({
						url: '/pagesB/invoiceBusiness/tollInvoice/tollSelect/tollRecord?vehicleInfo=' +
							encodeURIComponent(JSON
								.stringify(item))
					})
				}
			},
			addTitle() {
				this.isShowConfirm = false
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollInvoiceTitle/addTitle?routeType=bindVehicle&vehicleInfo=' +
						encodeURIComponent(JSON
							.stringify(this.vehicleInfo))
				})
			},
			add() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/tollInvoice/tollSelect/addVehicle'
				})
			},
			getBindCardVehicle() {
				this.isLoading = true
				let params = {
					ticketId: this.ticketId, //凭证
					plateNumQueryStr: this.plateNumQueryStr, //车牌模糊查询
					userType: this.userType, //客户类型
				}
				this.$request
					.post(this.$interfaces.tollCardList, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						console.log('res=========>>>>>>>>>>>', res)
						if (res.code == 200) {
							this.listData = res.data.items

						} else if (res.code == 73300) {
							//无鉴权信息，去绑定登录票根平台
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: (resp) => {
									if (resp.confirm) {
										//需要登录
										uni.reLaunch({
											url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/index'
										})
									}
								}
							})
						} else {
							if (res.msg.includes('请重新登录')) {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false,
									success: (resp) => {
										if (resp.confirm) {
											//需要登录	
											uni.reLaunch({
												url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/index'
											})
											return
										}

									}
								})
							} else {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			}
		}
	}
</script>

<style>
	.plc {
		line-height: 100rpx;
	}
</style>

<style lang="scss" scoped>
	.vechicle-list {
		overflow: hidden;
		// margin-top: 20rpx;
		// padding: 30rpx 30rpx 100rpx 30rpx;
		padding-top: 20rpx;
		// background-color: #ffffff;

		/deep/.title-wrapper {
			align-items: center;
		}

		.question-wrapper {
			display: flex;

			.question-text {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #0081FF;
			}
		}

		.content {
			background-color: #ffffff;
			padding: 30rpx 30rpx 100rpx 30rpx;
		}

		.input-wrapper {
			position: relative;
			margin: 30rpx 0 50rpx 0;
			height: 100rpx;
			line-height: 100rpx;
			background: #F6F6F6;
			border-radius: 11rpx;
			border: 1rpx solid #D4D4D4;

			.input {
				height: 100rpx;
				line-height: 100rpx;
				padding: 0 20rpx;
			}

			.clear-icon {
				position: absolute;
				right: 0px;
				top: 0;
				width: 60rpx;
				height: 100%;
				text-align: center;
				padding-top: 4rpx;
				z-index: 2;
			}
		}

		.weui-media {
			display: flex;
			-moz-box-align: center;
			-webkit-box-align: center;
			box-align: center;
			align-items: center;
			-webkit-align-items: center;
			-moz-align-items: center;
			background: #ffffff;
			border: 1px solid #e9e9e9;
			margin: 20rpx 0;
			border-radius: 16rpx;
			padding: 32rpx 30rpx;
			box-shadow: 0px 0px 20rpx 0px rgba(71, 123, 217, 0.12);

			&:first-child {
				margin-top: 0;
			}
		}

		.weui-media .weui-media-hd {
			width: 90rpx;
			display: flex;
			-moz-box-align: center;
			-webkit-box-align: center;
			box-align: center;
			align-items: center;
			-webkit-align-items: center;
			-moz-align-items: center;
			-moz-box-pack: center;
			-ms-box-pack: center;
			-webkit-box-pack: center;
			-webkit-justify-content: center;
			-moz-justify-content: center;
			justify-content: center;
		}

		.weui-media .weui-media-hd .weui-media-hd_icon {
			display: block;
			width: 84rpx;
			height: 84rpx;
		}

		.weui-media .weui-media-bd {
			flex: 1;
			margin-left: 30rpx;
		}

		.weui-media .weui-media-bd .title {
			font-size: 30rpx;
			font-weight: 500;
			color: #333333;
		}

		.weui-media .weui-media-bd .value {
			font-size: 26rpx;
			font-weight: 400;
			color: #555555;
			margin-top: 12rpx;
		}

		.weui-media .weui-media-ft {
			min-width: 28rpx;
		}

		.weui-media .weui-media-ft .weui-media-ft_btn {
			width: 158rpx;
			height: 60rpx;
			text-align: center;
			line-height: 60rpx;
			background: #0066E9;
			border-radius: 36rpx;
			font-size: 26rpx;
			color: #fff;
			font-weight: 400;
		}

		.weui-media .weui-media-ft .weui-media-ft_icon {
			width: 28rpx;
			height: 28rpx;
			display: block;
		}

		.dialog {
			.content {
				margin-top: 30rpx;

				.tips {
					font-size: 28rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #323435;
					line-height: 50rpx;
				}
			}
		}


		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			margin-top: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}
	}
</style>