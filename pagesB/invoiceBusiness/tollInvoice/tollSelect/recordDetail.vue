<template>
	<view class="record-detail">
		<view class="top-title">
			<view class="price" style="margin-right: 30rpx;">
				累计金额{{moneyFilter(totalPrice)}}元
			</view>
			<view class="count">
				共{{totalCount}}笔记录
			</view>
		</view>
		<scroll-view :style="{height:height}" :scroll-top="scrollTop" scroll-y="true" class="scroll-Y"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<view class="record-list" v-if="vehicleInfo.selectType == '1' || invoiceType == '1'">
				<view class="record-item" v-for="(item,index) in dataList" :key="index">
					<view class="header">
						<view class="name">
							{{titleDetail ? titleDetail.buyerName : item.titleName}}
						</view>
					</view>
					<view class="content">
						<view class="fee-item">
							<view class="label">
								通行金额:
							</view>
							<view class="fee">
								{{moneyFilter(item.fee)}}元
							</view>
						</view>
						<view class="info-item">
							<view class="label">
								入口:
							</view>
							<view class="info">
								{{item.enStationName}}
								{{routeType=='historyDetail'?'': formatHandle(new Date(item.enTime).getTime(), 'yyyy-MM-dd HH:mm:ss') }}
							</view>
						</view>
						<view class="info-item">
							<view class="label">
								出口:
							</view>
							<view class="info">
								{{item.exStationName}}
								{{routeType=='historyDetail'?'': formatHandle(new Date(item.exTime).getTime(), 'yyyy-MM-dd HH:mm:ss') }}
							</view>
						</view>
						<view class="fee-item">
							<view class="label">
								交易时间:
							</view>
							<view class="info">
								{{ formatHandle(new Date(item.exTime).getTime(), 'yyyy-MM-dd HH:mm:ss') }}
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="record-list" v-if="vehicleInfo.selectType == '2' || invoiceType == '2'">
				<view class="record-item" v-for="(item,index) in dataList" :key="index">
					<view class="header">
						<view class="name">
							{{titleDetail ? titleDetail.buyerName : item.titleName}}
						</view>
					</view>
					<view class="content">
						<view class="fee-item">
							<view class="label">
								充值金额:
							</view>
							<view class="fee">
								{{moneyFilter(item.fee)}}元
							</view>
						</view>
						<view class="info-item">
							<view class="label" style="flex:0 0 150rpx;width: 150rpx;">
								充值时间:
							</view>
							<view class="info">
								{{routeType=='historyDetail'? getTransTime(item.transTime)
								: formatHandle(new Date(item.time).getTime(), 'yyyy-MM-dd HH:mm:ss')}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<tLoading :isShow="isLoading" />
</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue'
	import {
		moneyFilter,
		formatHandle
	} from '@/common/util.js'
	import {
		getTollTicket
	} from '@/common/storageUtil.js';
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				height: 'calc(100% - 108rpx)',
				totalPrice: 0,
				totalCount: 0,
				vehicleInfo: {},
				dataList: [],
				invoiceItem: null,
				titleDetail: null,
				routeType: '',
				invoiceType: ''
			}
		},
		onLoad(option) {
			if (option.vehicleInfo) {
				const item = JSON.parse(decodeURIComponent(option.vehicleInfo));
				console.log('item', item)
				this.vehicleInfo = item
				if (item.selectType == '2') {
					uni.setNavigationBarTitle({
						title: '充值明细'
					})
				}
				this.totalCount = item.totalCount
				this.totalPrice = item.totalPrice
				this.dataList = item.vehicleList
			}
			if (option.routeType == 'historyDetail') {
				this.routeType = option.routeType
				this.invoiceItem = this.$store.getters['invoice/historyDetail']
				if (this.invoiceItem.invoiceType == '2') {
					uni.setNavigationBarTitle({
						title: '充值明细'
					})
				}
				this.titleDetail = this.$store.getters['invoice/titleDetail']
				if (option.recordList) {
					const recordList = JSON.parse(decodeURIComponent(option.recordList));
					this.dataList = recordList.dataList
					this.totalCount = recordList.totalCount
					this.totalPrice = recordList.totalPrice
				}

				this.invoiceType = this.invoiceItem.invoiceType || ''
				console.log('invoiceItem', this.invoiceItem.invoiceType, this.invoiceType)
			}
		},
		methods: {
			moneyFilter,
			formatHandle,
			getTransTime(time) {
				return dayjs(time, {
					format: 'YYYYMMDDHHmmss'
				}).format('YYYY-MM-DD HH:mm:ss')
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				// if (this.flag) return;
				// let self = this;
				// setTimeout(function() {
				// 	self.searchObj.page = self.searchObj.page + 1;
				// 	self.getList();
				// }, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.record-detail {
		overflow: hidden;
		// height: calc(100% - 20rpx);
		height: 100%;

		.top-title {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			line-height: 88rpx;
			background: rgba(255, 145, 0, 0.1);
		}

		.price,
		.count {
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #FF9038;
			line-height: 42rpx;
		}

		.record-list {
			margin: 0 20rpx;
			background-color: #f3f3f3;
			padding: 20rpx 0;

			.record-item {
				margin-bottom: 20rpx;
				padding-bottom: 30rpx;
				background-color: #ffffff;
			}

			.header {
				display: flex;
				margin-bottom: 16rpx;

				.checkbox {
					margin: 25rpx 0 25rpx 20rpx;
				}

				.checkbox::before {
					margin-top: -28rpx;
				}

				.name {
					flex: 1;
					margin: 25rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 40rpx;
				}

				.status {
					width: 169rpx;
					height: 63rpx;
					line-height: 63rpx;
					text-align: center;
					background: rgba(133, 134, 134, 0.1);
					border-radius: 0rpx 8rpx 0rpx 30rpx;
				}
			}

			.content {
				.fee-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 10rpx 30rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					line-height: 42rpx;

					.label {
						flex: 0 0 150rpx;
						width: 150rpx;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #999999;
						line-height: 42rpx;
					}

					.fee {
						flex: 1;
						text-align: right;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						color: #FF9038;
						line-height: 42rpx;
					}
				}

				.info-item {
					display: flex;
					// align-items: center;
					justify-content: space-between;
					padding: 10rpx 30rpx;

					.label {
						flex: 0 0 90rpx;
						width: 90rpx;
						margin-top: 2rpx;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #999999;
						line-height: 42rpx;
					}

					.info {
						flex: 1;
						text-align: right;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #333333;
						line-height: 42rpx;
					}
				}
			}
		}
	}
</style>