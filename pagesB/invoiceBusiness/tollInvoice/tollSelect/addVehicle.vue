<template>
	<view class="add-vehicle">
		<view class="car-color">
			<title title="请选择车牌颜色" titleDesc="（非车身颜色）" setPadding="30"></title>
			<view class="plate_color">
				<view v-for="(item,index) in palteColorList" :key="index" @click="selectPlateColor(index)"
					class="plateColorList" v-show="item.isShow">

					<image :src="item.icon" class="plateColor" width="200rpx" height="80rpx" mode='aspectFilt'>
					</image>
					<image
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/icon_select_license_plate_color.png"
						v-show="item.checked" class="checkplateColor" width="40rpx" height="40rpx" mode='aspectFilt'>
					</image>
					<view class="plate-color__text" style="text-align: center;">{{item.label}}</view>
				</view>
				<!-- 				<view v-if="!showMoreCarColor" class="more-color" @click="handleMoreCarColor">
					<image src="/static/toc/more-car-color.png" alt="aspectFilt"></image>
				</view> -->
				<view class="plateColorList" @click="selectPlateColor('more')">
					<!-- 					<picker @change="confirmColor" :value="showMoreColor" :range="otherPalteColorList"
						range-key='label'> -->
					<view class="otherColor" v-if="!otherChecked">其他颜色</view>
					<image v-if="otherChecked && moreColorName == '白色'"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_white.png"
						class="plateColor" width="200rpx" height="80rpx" mode='aspectFilt'>
					</image>

					<image v-if="otherChecked && moreColorName == '蓝白渐变色'"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_blue_white.png"
						class="plateColor" width="200rpx" height="80rpx" mode='aspectFilt'>
					</image>
					<!-- </picker> -->

					<image
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/icon_select_license_plate_color.png"
						v-show="otherChecked" class="checkplateColor" width="40rpx" height="40rpx" mode='aspectFilt'>
					</image>
					<view class="plate-color__text" style="text-align: center;margin-top: 4rpx;">
						{{!otherChecked?'其他':moreColorName}}
					</view>
				</view>
				<!-- 				<view v-if="showMoreCarColor" class="more-color" @click="handleMoreCarColor">
					<image src="/static/toc/more-car-color-hide.png" alt="aspectFilt"></image>
				</view> -->
				<view class="select-more" v-if="showMoreColor">
					<view class="select-more__item" @click="confirmColor(0)">
						<image class="select-more__image"
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_white.png"
							mode="aspectFilt"></image>
						<view class="select-more__text">
							白色
						</view>
					</view>
					<view class="select-more__item" @click="confirmColor(1)">
						<image class="select-more__image"
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_blue_white.png"
							mode="aspectFilt"></image>
						<view class="select-more__text">
							蓝白渐变色
						</view>
					</view>
					<image class="icon-select__color" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/select-color.png"
						mode="">
					</image>
				</view>
			</view>

		</view>
		<view class="select-content" v-if="formData.vehicleColor">
			<title title="输入车牌号查询通行ETC卡"></title>
			<view class="input-wrapper">
				<input class="input" v-model="formData.vehicleCode" @confirm="selectQuery" type="text"
					placeholder="请点击输入车牌号" placeholder-class="plc">
				<view class="clear-icon" v-if="formData.vehicleCode" @click="selectQuery">
					<icon class="" type="search" size="20" />
				</view>
			</view>
			<view style="color: #F65B5B;" v-if="showRedTips">
				{{redTips}}
			</view>
		</view>
		<view class="select-content" v-if="vehicleList.length > 0">
			<title title="请确认ETC卡号"></title>
			<view class="select-list">
				<radio-group class="radio-group" @change="radioChange">
					<label class="list-cell" v-for="(item, index) in vehicleList" :key="item.vehicleColor">
						<view>
							<radio style="transform:scale(0.7)" :color="'#0081FF'" :value="item.cardNo"
								:checked="index === current" />
						</view>
						<view class="info">
							<view class="vehicle-item g-flex g-flex-horizontal-vertical"
								:class="'license-plate-'+item.vehicleColor"
								:style="{color:item.vehicleColor=='0'|| item.vehicleColor=='2' ? '#fff' : '#000'}">
								{{item.vehicleCode}}
							</view>
							<view class="card">
								{{noPassByCardNo(item.cardNo)}}
							</view>
							<view class="card-status">
								({{item.cardStatus}})
							</view>
						</view>
					</label>
				</radio-group>
			</view>
		</view>
		<tButton v-if="formData.cardId" :buttonList="buttonList" @confirmHandle="confirmHandle"></tButton>
		<smsDialog ref="smsDialog" :show.sync="showSmsDialog" :mobile="mobile" @confirm="smsConfirm"
			:cardId="formData.cardId">
		</smsDialog>
		<u-mask :show="showMoreColor" @click="showMoreColor = false"></u-mask>
		<tLoading :isShow="isLoading" />
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import title from '@/pagesB/components/new-title/new-title.vue'
	import smsDialog from '@/pagesB/components/smsDialog/smsDialog.vue'
	import tLoading from '@/components/common/t-loading.vue'
	import tButton from '@/pagesB/components/t-button/t-button.vue'
	import {
		getEtcAccountInfo,
		getTollTicket
	} from '@/common/storageUtil.js';
	import {
		noPassByCardNo,
	} from '@/common/util.js'
	export default {
		components: {
			title,
			smsDialog,
			tLoading,
			tButton
		},
		data() {
			return {
				isLoading: false,
				dataLoading: false,
				showSmsDialog: false,
				otherChecked: false,
				showMoreColor: false,
				showMoreCarColor: false,
				textClass: false,
				moreColorName: "选择其他颜色",
				otherPalteColorList: [{
						value: '3',
						label: '白色',
						icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_white.png'
					},
					{
						value: '6',
						label: '蓝白渐变色',
						icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_blue_white.png'
					}
				],
				palteColorList: [{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_blue.png",
						checked: false,
						isShow: true,
						value: '0',
						label: '蓝色'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_gradient_green.png",
						checked: false,
						isShow: true,
						value: '4',
						label: '渐变绿'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_green.png",
						checked: false,
						isShow: true,
						value: '1',
						label: '黄色'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_yellow_green.png",
						checked: false,
						isShow: true,
						value: '5',
						label: '黄绿色'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_black.png",
						checked: false,
						isShow: true,
						value: '2',
						label: '黑色'
					},
				],
				buttonList: [{
					title: '确定新增',
					handle: 'confirmHandle',
					type: 'primary',
				}],
				current: null,
				formData: {
					vehicleColor: '',
					vehicleCode: '',
					cardId: ''
				},
				mobile: '',
				vehicleList: [],
				showRedTips: false,
				redTips: '您输入的车牌号不在您的名下或未办理广西ETC，请仔细核对',
				routeType: ''
			}
		},
		computed: {
			etcInfo() {
				return getEtcAccountInfo() || {}
			},
			ticketId() {
				return getTollTicket() || ''
			},
			userType() {
				let userType = ''
				this.etcInfo.custType == '0' ? userType = '1' : userType = '2'
				return userType
			}
		},
		onLoad(option) {
			this.mobile = this.etcInfo.mobile
			if (option.routeType) {
				this.routeType = option.routeType
			}
		},
		methods: {
			noPassByCardNo,
			selectQuery() {
				if (!this.formData.vehicleColor || !this.formData.vehicleCode) {
					uni.showModal({
						title: "提示",
						content: '车牌颜色或者车牌号不能为空',
						showCancel: false,
					});
					return
				}
				this.isLoading = true
				let params = {
					customerId: this.etcInfo.custMastId,
					vehicleCode: this.formData.vehicleCode,
					vehicleColor: this.formData.vehicleColor
				}
				this.$request.post(this.$interfaces.tollVehicleQuery, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('车牌查询', res)
						this.vehicleList = res.data
						if (res.data.length == 0) {
							this.showRedTips = true
						} else {
							this.showRedTips = false
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			radioChange(evt) {
				console.log('evt', evt)
				this.formData.cardId = evt.detail.value
				for (let i = 0; i < this.vehicleList.length; i++) {
					if (this.vehicleList[i].vehicleColor === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			},
			// 选择更多车牌颜色
			confirmColor(index) {
				this.showMoreColor = false
				console.log('选择车牌颜色更多', index)
				for (let obj of this.palteColorList) {
					obj.checked = false
				}

				this.otherChecked = true
				// let index = e.detail.value;
				let item = this.otherPalteColorList[index]
				this.moreColorName = item.label
				this.formData.vehicleColor = item.value
			},
			// 选择车牌颜色
			selectPlateColor(index) {
				if (index === "more") {
					this.showMoreColor = true
				} else {
					this.otherChecked = false
					for (let obj of this.palteColorList) {
						obj.checked = false
					}
					this.moreColorName = "选择其他颜色"
					this.palteColorList[index].checked = true
					this.formData.vehicleColor = this.palteColorList[index].value

				}
			},
			confirmHandle() {
				this.isLoading = true
				let params = {
					ticketId: this.ticketId,
					cardId: this.formData.cardId
					// cardId: '20190424171504237005'

				}
				console.log('绑卡验证校验请求', params)
				this.$request.post(this.$interfaces.tollBindCardBindCheck, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('绑卡验证校验', res)
						this.showSmsDialog = true
						this.$refs.smsDialog.getCaptcha()
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			// confirmHandle() {
			// 	//弹出验证码
			// 	this.showSmsDialog = true
			// 	this.$refs.smsDialog.getCaptcha()
			// },
			smsConfirm(mobileCode) {
				this.isLoading = true

				let params = {
					cardId: this.formData.cardId,
					smsCode: mobileCode,
					ticketId: this.ticketId
				}
				this.$request.post(this.$interfaces.tollCardBindNew, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.showSmsDialog = false
						console.log('绑定成功', res)
						if (this.routeType == 'addTitle') {
							//从新增抬头管理新增车辆
							uni.showModal({
								title: '提示',
								content: '新增车辆成功',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										let pages = getCurrentPages(); //获取所有页面栈实例列表
										let prevPage = pages[pages.length - 2]; //上一页页面实例
										console.log('pages', pages, prevPage)
										prevPage.$vm.getVehicleList()
										uni.navigateBack({
											delta: 1
										})
									}
								}
							})
							return
						}
						let item = {
							cardId: params.cardId,
							plateColor: this.formData.vehicleColor,
							plateNum: this.formData.vehicleCode
						}
						uni.reLaunch({
							url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=1&descType=addVehicle&vehicleInfo=' +
								encodeURIComponent(JSON
									.stringify(item))
						})
					} else {
						if (res.msg.includes('验证码不正确') || res.msg.includes('验证码失效')) {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
							return
						} else {
							let errorText = '错误码【' + res.code + '】，' + res.msg
							uni.reLaunch({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/resultPage?type=5&descType=addVehicle&errorText=' +
									errorText
							})
						}

					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
		}
	}
</script>

<style>
	.plc {
		line-height: 100rpx;
	}
</style>

<style lang="scss" scoped>
	.add-vehicle {
		overflow: hidden;
		// padding-top: 20rpx;
		padding-bottom: 180rpx;

		.car-color {
			margin: 20rpx;
			background-color: $uni-bg-color;
			border-radius: 12rpx;

			.title-container {
				padding: 30rpx;

				/deep/.title {
					font-size: 32rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #323435;
					line-height: 45rpx;
				}
			}
		}

		.plate_color {
			position: relative;
			display: flex;
			flex-wrap: wrap;
			background-color: $uni-bg-color;
			// margin-top: -20rpx;
			padding: 10rpx 20rpx 30rpx 30rpx;
			border-bottom-right-radius: 12rpx;
			border-bottom-left-radius: 12rpx;

			.plateColor {
				width: 202rpx;
				height: 68rpx;
			}

			.more-color {
				line-height: 100rpx;
				// margin-left: 30rpx;
				display: flex;
				justify-content: center;
				flex: 1;
				margin-top: 16rpx;

				&>image {
					width: 40rpx;
					height: 44rpx;
				}
			}

			.select-more {
				position: absolute;
				bottom: -106rpx;
				z-index: 100071;

				.select-more__item {
					position: absolute;

					&:first-child {
						left: 85rpx;
						bottom: 30rpx;
						z-index: 100071;
						// z-index: 20;
					}

					&:last-of-type {
						right: 85rpx;
						bottom: 30rpx;
						z-index: 100071;
						// z-index: 20;
					}

					.select-more__image {
						width: 202rpx;
						height: 68rpx;
					}

					.select-more__text {
						text-align: center;
						color: #ffffff;
					}
				}
			}

			.icon-select__color {
				width: 647rpx;
				height: 209rpx;
			}

			.plateColorList {
				position: relative;
				margin-right: 20rpx;
				margin-bottom: 20rpx;

				&:nth-child(3n) {
					margin-right: 0rpx;
				}

				.plate-color__text {
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #888888;
				}

				.checkplateColor {
					position: absolute;
					bottom: 30rpx;
					right: -12rpx;
					width: 40rpx;
					height: 40rpx;
				}
			}


			.otherColor {
				width: 202rpx;
				height: 68rpx;
				background: #ffffff;
				border: 1px dashed #B8B8B8;
				border-radius: 4px;
				line-height: 68rpx;
				text-align: center;
				font-size: 30rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #323435;
			}
		}

		.select-content {
			background-color: $uni-bg-color;
			padding: 30rpx;
			margin: 20rpx;
			border-radius: 12rpx;


			.input-wrapper {
				position: relative;
				margin: 30rpx 0 50rpx 0;
				height: 100rpx;
				line-height: 100rpx;
				background: #F6F6F6;
				border-radius: 11rpx;
				border: 1rpx solid #D4D4D4;

				.input {
					height: 100rpx;
					line-height: 100rpx;
					padding: 0 20rpx;
				}

				.clear-icon {
					position: absolute;
					right: 0px;
					top: 0;
					width: 60rpx;
					height: 100%;
					text-align: center;
					padding-top: 4rpx;
					z-index: 2;
				}
			}

			.select-list {
				.radio-group {
					width: 100%;
				}

				.list-cell {
					margin-top: 20rpx;
					display: flex;
					align-items: center;

					.info {
						position: relative;
						flex: 1;
						display: flex;
						align-items: center;
						height: 120rpx;
						padding: 0 20rpx;
						background: #FFFFFF;
						box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.06);
						border-radius: 16rpx;
						border: 2rpx solid #ECECEC;

						.vehicle-item {
							width: 220rpx;
							height: 80rpx;
						}

						.card {
							margin-top: 10rpx;
							margin-left: 10rpx;
						}

						.card-status {
							position: absolute;
							right: 0;
							top: 0;
							width: 125rpx;
							height: 43rpx;
							background: #ECECEC;
							border-radius: 0rpx 12rpx 0rpx 12rpx;
							font-size: 24rpx;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #333333;
							line-height: 33rpx;
							text-align: center;
						}
					}

					.plateColor {
						width: 202rpx;
						height: 68rpx;
						margin-right: 20rpx;
					}
				}
			}
		}

		.license-plate-0 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-1 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_green.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-2 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_black.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-3 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_white.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-4 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_gradient_green.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-5 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_yellow_green.png') no-repeat;
			background-size: 100% 100%;
		}

		.license-plate-6 {
			background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue_white_gradient.png') no-repeat;
			background-size: 100% 100%;
		}

	}
</style>