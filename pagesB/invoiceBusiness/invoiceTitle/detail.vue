<template>
	<view class="add-invoice">
		<view class="weui-form">
			<view class="weui-cells__title">
				<view class="weui-cells__title__decoration">
					发票抬头信息
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">公司名称</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" v-if="templateDetails.buyerName">
							{{templateDetails.buyerName}}
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="templateDetails.buyerTaxpayerNum" >
					<view class="weui-cell__hd">
						<view class="weui-label">公司税号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" >
							{{templateDetails.buyerTaxpayerNum}}
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="templateDetails.buyerAddress">
					<view class="weui-cell__hd">
						<view class="weui-label ">注册地址</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" >
							{{templateDetails.buyerAddress}}
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="templateDetails.buyerPhone">
					<view class="weui-cell__hd">
						<view class="weui-label ">注册电话</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" >
							{{templateDetails.buyerPhone}}
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="templateDetails.buyerBankName">
					<view class="weui-cell__hd">
						<view class="weui-label ">开户银行</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" >
							{{templateDetails.buyerBankName}}
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="templateDetails.buyerBankAccount">
					<view class="weui-cell__hd">
						<view class="weui-label ">银行账号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" >
							{{templateDetails.buyerBankAccount}}
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="templateDetails.email">
					<view class="weui-cell__hd">
						<view class="weui-label ">邮箱</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" >
							{{templateDetails.email}}
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="templateDetails.mobilePhone">
					<view class="weui-cell__hd">
						<view class="weui-label ">手机号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" >
							{{templateDetails.mobilePhone}}
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="templateDetails.remark">
					<view class="weui-cell__hd">
						<view class="weui-label ">备注</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value" >
							{{templateDetails.remark}}
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="onDelHandle">
						删除
					</button>
				</view>
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="onEditHandle">
						编辑抬头
					</button>
				</view>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getLoginUserInfo
	} from "@/common/storageUtil.js";
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				invoiceDefaultChecked: false, // 设置默认抬头
				formData: {
					"tId": '',
					"source": '1',
					"userNo": ""
				},
				templateDetails: {},
				userTypeIndexSub: '1',
				invoiceOptions: [{
						value: '1',
						name: '企业'
					},
					{
						value: '2',
						name: '个人'
					}
				],
			};
		},
		computed: {

		},
		onLoad(option) {
			if (option.templateId) {
				this.formData.tId = Number(option.templateId)
				this.getDeatils()
			}
		},
		methods: {
			getDeatils() {
				let params = {
					...this.formData,
					userNo: getLoginUserInfo().userNo || ''
				}
				this.$request
					.post(this.$interfaces.invoiceQueryTemplate, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.templateDetails = res.data && res.data.length ? res.data[0] : {};
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			// 删除发票抬头信息
			onDelHandle() {
				let params = {
					ids: [this.formData.tId]
				}
				this.$request
					.post(this.$interfaces.invoiceDelTemplate, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '删除成功',
								showCancel: false
							})
							uni.redirectTo({
								url: '/pagesB/invoiceBusiness/invoiceTitle/index'
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			// 编辑发票抬头信息
			onEditHandle() {
				uni.redirectTo({
					url: '/pagesB/invoiceBusiness/invoiceTitle/addInvoice?templateId=' + this.formData.tId
				})
			}
		}
	};
</script>

<style scoped lang="scss">
	.add-invoice {
		margin-bottom: 170rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color:#0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}


	.invoice-default {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		padding: 20rpx 30rpx;
		margin-top: 20rpx;

	}

	.invoice-default .invoice-default-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;

	}

	.invoice-default .invoice-default-hd .invoice-default-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.invoice-default .invoice-default-bd {
		flex: 1;
	}

	.invoice-default .invoice-default-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.invoice-default .invoice-default-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #999999;
	}

	.invoice-default .invoice-default-value {
		text-align: right;
	}
</style>
