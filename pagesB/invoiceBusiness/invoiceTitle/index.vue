<template>
	<view class="invoice-template" v-if="!isLoading">
		<view class="fixed-margin-bottom fixed-padding-top" v-if="templateList && templateList.length">
			<block>
				<view class="weui-media" v-for='(item,index) in templateList' :key='index'
					@click="goDetailHandle(item)">
					<view class="weui-media-bd">
						<view class="title g-flex">
							<view class="">
								{{item.buyerName}}
							</view>
							<view v-if="item.latest == '1'" class="default-template__tag">
								默认
							</view>
						</view>
						<view class="value" v-if="item.buyerTaxpayerNum">{{item.buyerTaxpayerNum}}</view>
					</view>
					<view class="weui-media-ft">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/right.png" mode="aspectFilt" class="weui-media-ft_icon"></image>
					</view>
				</view>
			</block>
		</view>
		<view class="no-invoice" v-else>
			<image src="../../static/no_invoice_header.png" mode="aspectFilt" class="no-invoice_icon">
			</image>
			<view class="no-invoice_des">
				您未设置发票抬头
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<button class="weui-btn weui-btn_primary" @click="goAddTemplate">
					添加发票抬头
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getLoginUserInfo
	} from "@/common/storageUtil.js";
	export default {
		data() {
			return {
				isLoading: true,
				templateList: []

			}
		},
		components: {

		},
		computed: {

		},
		onLoad(obj) {

		},
		created() {
			this.getList()
		},
		methods: {
			// 跳转添加发票界面
			goAddTemplate() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/invoiceTitle/addInvoice'
				})
			},
			// 跳转详情页面
			goDetailHandle(options) {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/invoiceTitle/detail?templateId=' + options.id
				})
			},
			getList() {
				let params = {
					source: 1,
					userNo: getLoginUserInfo().userNo || ''
				}
				this.$request
					.post(this.$interfaces.invoiceQueryTemplate, {
						data: params
					})
					.then((res) => {

						if (res.code == 200) {
							this.templateList = res.data;
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			}
		}
	}
</script>

<style scoped lang="scss">
	.default-template__tag {
		font-size: 13px;
		color: #fff;
		margin-left: 4px;
		height: 40rpx;
		padding: 0 10px;
		background-color: #0066E9;
		border-radius: 100px;
		display: flex;
		align-items: center;
	
	}

	.fixed-margin-bottom {
		padding-bottom: 160rpx;
	}

	.fixed-padding-top {
		padding-top: 20rpx;
	}

	.invoice-template {
		width: 100%;
		height: 100%;
		background-color: #f9f9f9;
	}

	.invoice-template .invoice-template_wrapper {
		padding: 20rpx 0 120rpx 0;
	}

	.invoice-template .weui-btn_wrapper {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
	}

	.no-invoice {
		padding-top: 320rpx;
		width: 100%;
		background-color: #f9f9f9;
	}

	.no-invoice .no-invoice_icon {
		width: 240rpx;
		height: 180rpx;
		margin: 0 auto;
		display: block;
	}

	.no-invoice .no-invoice_des {
		font-size: 29rpx;
		color: #333333;
		font-weight: 400;
		text-align: center;
		margin-top: 40rpx;
	}

	.weui-media {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		margin: 20rpx 30rpx;
		border-radius: 16rpx;
		padding: 24rpx 30rpx;
		box-shadow: 0px 0px 20rpx 0px rgba(71, 123, 217, 0.12);


		&:first-child {
			margin-top: 0;
		}
	}

	.weui-media .weui-media-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;
	}

	.weui-media .weui-media-hd .weui-media-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.weui-media .weui-media-bd {
		flex: 1;
		min-height: 80rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.weui-media .weui-media-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.weui-media .weui-media-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		height: 30rpx;
		margin-top: 12rpx;
	}

	.weui-media .weui-media-ft {
		min-width: 28rpx;
	}

	.weui-media .weui-media-ft .weui-media-ft_btn {
		width: 158rpx;
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;
		background: #0066E9;
		border-radius: 36rpx;
		font-size: 26rpx;
		color: #fff;
		font-weight: 400;
	}

	.weui-media .weui-media-ft .weui-media-ft_icon {
		width: 28rpx;
		height: 28rpx;
		display: block;
	}
</style>
