<template>
	<view class="add-invoice">
		<view class="weui-form">
			<view class="weui-cells__title">
				<view class="weui-cells__title__decoration">
					发票抬头信息
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">抬头类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							<radio-group @change="invoiceChange" class="g-flex g-flex-end">
								<label class="uni-list-cell uni-list-cell-pd " v-for="(item, index) in invoiceOptions"
									:key="item.value">
									<view>
										<radio :value="item.value" class="cyan" style="transform: scale(0.7)"
											:checked="item.value==invoiceType" />
										<text style="margin: 0 10upx;">{{item.name}}</text>
									</view>

								</label>
							</radio-group>
						</view>

					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<block v-if="invoiceType == 2">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">抬头名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerName" :value="formData.buyerName" class="weui-input"
								placeholder="请输入抬头名称"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</block>
				<block v-if="invoiceType == 1">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerName" :value="formData.buyerName" class="weui-input"
								placeholder="请输入公司名称"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">公司税号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerTaxpayerNum" :value="formData.buyerTaxpayerNum"
								class="weui-input" placeholder="请输入纳税人识别号"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">注册地址</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerAddress" :value="formData.buyerAddress"
								class="weui-input" placeholder="请输入公司注册地址"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">注册电话</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerPhone" :value="formData.buyerPhone" class="weui-input"
								placeholder="请输入公司注册电话"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">开户银行</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerBankName" :value="formData.buyerBankName"
								class="weui-input" placeholder="请输入公司开户银行"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">银行账号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input @input="modifyInput" id="buyerBankAccount" :value="formData.buyerBankAccount"
								class="weui-input" placeholder="请输入银行账号"></input>
						</view>
						<view class="weui-cell__ft">
						</view>
					</view>
				</block>

				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label ">邮箱</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input @input="otherModifyInput" id="email" :value="otherFormData.email" class="weui-input"
							placeholder="请输入邮箱"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label ">手机号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input @input="otherModifyInput" id="mobilePhone" :value="otherFormData.mobilePhone"
							class="weui-input" placeholder="请输入手机号码"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label ">备注</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input @input="otherModifyInput" id="remark" :value="otherFormData.remark" class="weui-input"
							placeholder="请输入备注"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="invoice-default" v-if="!routeType">

			<view class="invoice-default-bd">
				<view class="title">设置默认抬头</view>
				<view class="value">每次开票会默认填写该抬头信息</view>
			</view>
			<view class="invoice-default-value">
				<switch class="cyan" :checked='invoiceDefaultChecked' @change='onInvoiceDefaultChange'
					style="transform:scale(0.7)" />
			</view>

		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="onSubmitHandle">
						保存
					</button>
				</view>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getLoginUserInfo,
		getCurrUserInfo
	} from "@/common/storageUtil.js";
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				invoiceDefaultChecked: false, // 设置默认抬头
				templateId: '', //发票抬头模版ID
				formData: {
					"id": '',
					"buyerName": "",
					"buyerTaxpayerNum": "",
					"buyerAddress": "",
					"buyerPhone": "",
					"buyerBankName": "",
					"buyerBankAccount": "",
				},
				otherFormData: {
					"email": "",
					"mobilePhone": "",
					"remark": ""
				},
				invoiceType: '1',
				invoiceOptions: [{
						value: '1',
						name: '企业'
					},
					{
						value: '2',
						name: '个人'
					}
				],
				routeType: '', //路由类型
				id: '' //id
			};
		},
		computed: {
			commonParams() {
				return {
					source: 1,
					userNo: getLoginUserInfo().userNo || ''
				}
			}
		},
		onLoad(option) {
			console.log('option', option)
			if (option.type) {
				this.routeType = option.type
			}
			if (option.id) {
				this.id = option.id
			}
			if (option.templateId) {
				this.templateId = Number(option.templateId)
				this.getDeatils()
			} else {
				for (let key in this.formData) {
					this.formData[key] = '';
				}
			}

			console.log('getCurrUserInfo', getCurrUserInfo())
			console.log('getLoginUserInfo', getLoginUserInfo())
		},
		methods: {
			invoiceChange(e) {
				this.invoiceType = e.detail.value
			},
			onInvoiceDefaultChange(e) {
				this.invoiceDefaultChecked = e.detail.value
			},
			modifyInput(e) {
				this.formData[e.currentTarget.id] = e.detail.value;
			},
			otherModifyInput(e) {
				this.otherFormData[e.currentTarget.id] = e.detail.value;
			},
			getDeatils() {
				let params = {
					tId: this.templateId,
					...this.commonParams
				}
				this.$request
					.post(this.$interfaces.invoiceQueryTemplate, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							if (res.data && res.data.length) {
								let result = res.data[0];
								for (let key in this.formData) {
									this.formData[key] = result[key] || this.formData[key];
								}
								for (let key in this.otherFormData) {
									this.otherFormData[key] = result[key] || this.otherFormData[key];
								}
								this.invoiceDefaultChecked = result.latest == 1;
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}


					})
					.catch((error) => {

						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			onValidHandle() {
				if (this.invoiceType == 2) {
					if (!this.formData.buyerName) {
						uni.showModal({
							title: '提示',
							content: '请输入抬头名称',
							showCancel: false
						})
						return
					}
					return true;
				}
				if (this.invoiceType == 1) {
					if (!this.formData.buyerName) {
						uni.showModal({
							title: '提示',
							content: '请输入公司名称',
							showCancel: false
						})
						return
					}
					if (!this.formData.buyerTaxpayerNum) {
						uni.showModal({
							title: '提示',
							content: '请输入纳税人识别号',
							showCancel: false
						})
						return
					}
					return true;
				}
			},
			onSubmitHandle() {
				if (!this.onValidHandle()) return;
				if (this.routeType == 'Bissue') {
					//发行自助开票
					this.invoiceSelfApplyHandle()
				} else {
					this.invoiceTemplateSave();
				}
			},
			invoiceTemplateSave() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					...this.commonParams,
					...this.otherFormData,
					latest: this.invoiceDefaultChecked ? 1 : 0
				}
				let _formData = JSON.parse(JSON.stringify(this.formData));
				for (let key in _formData) {
					if (this.invoiceType == 2) {
						if (!(key == 'id' || key == 'buyerName')) {
							_formData[key] = '';
						}
					}
				}
				params = Object.assign(params, _formData)
				this.$request
					.post(this.$interfaces.invoiceTemplateSave, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							let msg = !!this.templateId ? '编辑成功' : '添加成功'
							uni.showModal({
								title: '提示',
								content: msg,
								showCancel: false,
								success() {
									uni.reLaunch({
										url: '/pagesB/invoiceBusiness/invoiceTitle/index'
									})
								}
							})

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
						this.isLoading = false;

					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			invoiceSelfApplyHandle() {
				this.isLoading = true
				let detail = {
					...this.formData,
					...this.otherFormData
				}
				let params = {
					custMastId: this.id,
					details: JSON.stringify(detail)
				}
				this.$request
					.post(this.$interfaces.invoiceSelfApply, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '保存成功',
								showCancel: false,
								success() {
									uni.reLaunch({
										url: '/pages/home/<USER>/p-home'
									})
								}
							})

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			}
		}
	};
</script>

<style scoped lang="scss">
	.add-invoice {
		margin-bottom: 170rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-cells__title__decoration {
		position: relative;
		font-weight: 600;
		color: #333333;
		font-size: 30rpx;
		padding-left: 16rpx;
	}

	.weui-cells__title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background-color: #0066E9;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
		border-bottom-right-radius: 2px;
		border-bottom-left-radius: 2px;
	}


	.invoice-default {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		padding: 20rpx 30rpx;
		margin-top: 20rpx;

	}

	.invoice-default .invoice-default-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;

	}

	.invoice-default .invoice-default-hd .invoice-default-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.invoice-default .invoice-default-bd {
		flex: 1;
	}

	.invoice-default .invoice-default-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.invoice-default .invoice-default-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #999999;
	}

	.invoice-default .invoice-default-value {
		text-align: right;
	}
</style>