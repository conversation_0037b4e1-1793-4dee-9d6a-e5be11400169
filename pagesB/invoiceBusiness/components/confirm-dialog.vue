<template>
	<view class="status-dialog" v-if="show">
		<view class="status-dialog__mask"></view>
		<view class="status-dialog__content" :style="{height:title=='提示'?'400rpx':'575rpx'}">
			<view class="title">{{title}}</view>
			<slot name="content"></slot>
			<view class="interaction-two" v-if="!centerBtn">
				<view class="interaction-two-cancel" @click="clickCancel">{{leftBtnText}}
				</view>
				<view class="interaction-two-comfirm" @click="clickComfirm">
					{{rightBtnText}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'confirm-dialog',
		props: {
			show: { // 弹窗的显示
				type: [Boolean, String],
				default: false
			},
			title: {
				type: String,
				default: '温馨提示'
			},
			leftBtnText: {
				type: String,
				default: '取消'
			},
			rightBtnText: {
				type: String,
				default: '确定'
			},
			centerBtn: {
				type: Boolean,
				default: false
			},
			defaultCancel: {
				type: Boolean,
				default: true
			},
		},
		data() {
			return {

			}
		},

		methods: {
			clickComfirm() {
				this.$emit('comfirm')
			},
			clickCancel() {
				if (this.defaultCancel) {
					this.$emit('update:show', false)
				} else {
					this.$emit('cancel')
				}

			},
		}
	}
</script>

<style lang="scss">
	$bg-color-mask: rgba(0, 0, 0, 0.5); //遮罩颜色
	$bg-color-hover: #f1f1f1; //点击状态颜色

	.status-dialog {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 9998;

		&__mask {
			display: block;
			position: fixed;
			z-index: 1998;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: $bg-color-mask;
		}

		&__content {
			position: relative;
			z-index: 2000;
			top: calc(50% - 500rpx);
			left: 50%;
			margin-left: -310rpx;
			width: 620rpx;
			height: 575rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.status-image {
				margin-top: 44rpx;
			}

			.title {
				width: 100%;
				text-align: center;
				padding: 36rpx 0;
				border-bottom: 2rpx solid #E9E9E9;
				font-size: 36rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: 32rpx;
			}


			.content {
				width: calc(100% - 90rpx);
				height: calc(100% - 330rpx);
				font-size: 28rpx;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				text-align: center;
				color: #666666;
				line-height: 50rpx;
				margin-top: 36rpx;
				overflow-y: scroll;
			}

			.comfirm {
				width: 100%;
				height: 88rpx;
				border-top: .5px solid #f5f5f5;
				text-align: center;
				line-height: 88rpx;
				font-size: 15px;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				position: absolute;
				bottom: 0;
			}

			.interaction-two {
				border-top: .5px solid #f5f5f5;
				width: 100%;
				height: 88rpx;
				position: absolute;
				bottom: 0;
				display: flex;

				.interaction-two-cancel {
					width: 50%;
					height: 88rpx;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					line-height: 88rpx;
					background: #E9E9E9;
					color: #333333;
					border-bottom-left-radius: 24rpx;
				}

				.interaction-two-comfirm {
					width: 50%;
					height: 88rpx;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #fff;
					line-height: 88rpx;
					background-color: #0066E9;
					border-bottom-right-radius: 24rpx;
				}

				.interaction-one-comfirm {
					width: 100%;
					height: 88rpx;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #5591FF;
					line-height: 88rpx;
				}
			}
		}
	}
</style>