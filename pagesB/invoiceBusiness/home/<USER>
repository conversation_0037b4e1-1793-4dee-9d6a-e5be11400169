<template>
	<view class="invoice">
		<view class="invoice_bd">
			<dartCard>
				<view slot="title" class="g-flex g-flex-align-center">
					<view class="">
						开具发票
					</view>

				</view>
				<view class="invoice-content">
					<view class="list g-flex g-flex-justify">
						<view class="list_item" @click="goUninvoiceList">
							<image src="../../static/invoice_add.png" class="icon" mode='aspectFilt' ></image>
							<view class="title">
								开具发票
							</view>
						</view>
						<view class="list_item" @click="goInvoiceTitle">
							<image src="../../static/invoice_title.png" class="icon" mode='aspectFilt'></image>
							<view class="title">
								常用发票抬头
							</view>
						</view>
						<view class="list_item" @click="goInvoiceRecord">
							<image src="../../static/invoice_record.png" class="icon" mode='aspectFilt'></image>
							<view class="title">
								开票历史
							</view>
						</view>
					</view>
				</view>
			</dartCard>
		</view>

	</view>
</template>

<script>
	import dartCard from '@/pagesB/components/dart-card/dart-card.vue';

	export default {
		data() {
			return {

			}
		},
		components: {
			dartCard
		},
		onLoad(option) {

		},
		methods: {
			goInvoiceTitle(){
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/invoiceTitle/index'
				})
			},
			//跳转未开发票列表
			goUninvoiceList(){
				
				uni.navigateTo({
					url:'/pagesB/invoiceBusiness/index?type='+'unInvoice'
				})
				// uni.navigateTo({
				// 	url:'/pagesB/invoiceBusiness/uninvoiceList/index'
				// })
			},
			goInvoiceRecord(){
				uni.navigateTo({
					url:'/pagesB/invoiceBusiness/index?type='+'invoiced'
				})
				// uni.navigateTo({
				// 	url:'/pagesB/invoiceBusiness/invoiceRecord/index'
				// })
				
			}
		}
	}
</script>

<style scoped lang="scss">
	.invoice {
		width: 100%;
		height: 100%;
		background: #F3F3F3;
	}

	.invoice_bd {
		margin: 10rpx;
		background-color: #fff;
	}

	.invoice-content {
		padding: 40rpx 30rpx 30rpx 30rpx;
	}

	.invoice-content {
		width: 100%;

	}

	.invoice-content .list {}

	.invoice-content .list .list_item {
		display: flex;
	
		flex-direction: column;
		align-items: center;
		justify-content: center;

	}

	.invoice-content .list .list_item .icon {
		width: 36rpx;
		height: 36rpx;

	}

	.invoice-content .list .list_item .title {
		font-weight: 500;
		color: #333333;
		font-size: 24rpx;
		margin-top: 10rpx;
		text-align: center;
	}
</style>
