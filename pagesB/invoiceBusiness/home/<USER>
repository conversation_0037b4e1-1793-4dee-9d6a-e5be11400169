<template>
	<view class="invoice-home">
		<view class="card" @click="toTollInvoice">
			<image class="img" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/toll_invoice.png" mode=""></image>
			<view class="font">
				开具通行费消费/充值发票、管理ETC车辆绑定的发票抬头、查看已开具的通行费发票等内容。
			</view>
		</view>
		<view class="card" @click="toOtherInvoice">
			<image class="img" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/other_invoice.png" mode=""></image>
			<view class="font">
				开具其他在捷通公司产生的消费发票（权益服务费/设备费/月结账单服务费等）、管理常用发票抬头、查看已开具的发票等内容。
			</view>
		</view>
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	import {
		setTollTicket,
		getLoginUserInfo
	} from '@/common/storageUtil.js';

	export default {
		data() {
			return {

			}
		},
		methods: {
			toTollInvoice() {
				let param = {
					userNo: getLoginUserInfo().userIdStr
				}
				this.$request
					.post(this.$interfaces.tollAuthCheck, {
						data: param
					})
					.then((res) => {
						this.isLoading = false;
						console.log('res', res)
						if (res.code == 200) {
							setTollTicket(res.data)
							uni.navigateTo({
								url: '/pagesB/invoiceBusiness/home/<USER>'
							})
						} else if (res.code == 73300) {
							//无鉴权信息，去绑定登录票根平台
							uni.navigateTo({
								url: '/pagesB/invoiceBusiness/tollInvoice/bindStub/index'
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			// toTollInvoice() {
			// 	uni.navigateTo({
			// 		url: '/pagesB/invoiceBusiness/index'
			// 	})
			// },
			toOtherInvoice() {
				// uni.navigateTo({
				// 	url: '/pagesB/invoiceBusiness/home/<USER>'
				// })
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/home/<USER>'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.invoice-home {
		overflow: hidden;

		.card {
			margin: 20rpx 20rpx 0 20rpx;
			padding: 20rpx;
			background: #FFFFFF;
			border-radius: 12rpx;

			.img {
				width: 670rpx;
				height: 188rpx;
			}

			.font {
				margin-top: 20rpx;
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #666666;
				line-height: 40rpx;
			}
		}
	}
</style>