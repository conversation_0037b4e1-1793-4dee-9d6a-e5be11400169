<template>
	<view class="toll-select">
		<view class="card-type">
			<view class="card-warpper">
				<view v-for="(item,index) in productList" :key="index" @click="select(index)" class="card-item"
					:style="{backgroundColor:item.backColor}">
					<image class="img" :src="item.src" mode=""></image>
					<view class="card-text">
						<view class="card-name">
							{{item.productName}}
						</view>
						<view class="card-desc">
							{{item.productDesc}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 在线客服功能 -->
		<customerService></customerService>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				productList: [{
					src: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/invoice_apply_icon.png',
					backColor: '#EFF2FE',
					productName: '开票申请',
					productDesc: '选择通行/充值记录并申请开票'
				}, {
					src: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/invoice_history_icon.png',
					backColor: '#FEF9F2',
					productName: '开票历史',
					productDesc: '仅支持查看在桂小通(广西捷通)的记录'
				}, {
					src: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tollInvoice/invoice_title_icon.png',
					backColor: '#F0FCF7',
					productName: '通行费发票常用抬头',
					productDesc: '管理已添加的发票抬头'
				}],
				type: ''
			}
		},
		onLoad(options) {
			if (options.type) {
				this.type = options.type
				if (this.type == 'other') {
					this.productList[2].productName = '常用发票抬头'
					this.productList[0].productDesc = '选择消费记录并申请开票'
					uni.setNavigationBarTitle({
						title: '其他消费发票服务'
					});
				}
			}
		},
		methods: {
			select(index) {
				if (this.type == 'toll') {
					if (index == 0) {
						uni.navigateTo({
							url: '/pagesB/invoiceBusiness/tollInvoice/tollSelect/vehicleList'
						})
					} else if (index == 1) {
						uni.navigateTo({
							url: '/pagesB/invoiceBusiness/tollInvoice/tollSelect/vehicleList?type=history'
						})
					} else if (index == 2) {
						uni.navigateTo({
							url: '/pagesB/invoiceBusiness/tollInvoice/tollInvoiceTitle/index'
						})
					}
				} else {
					if (index == 0) {
						uni.navigateTo({
							url: '/pagesB/invoiceBusiness/otherInvoice/invoiceRecord/index'
						})
					} else if (index == 1) {
						uni.navigateTo({
							url: '/pagesB/invoiceBusiness/otherInvoice/otherHistory/index'
						})
					} else if (index == 2) {
						uni.navigateTo({
							url: '/pagesB/invoiceBusiness/otherInvoice/otherInvoiceTitle/index'
						})
					}
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	.toll-select {
		overflow: hidden;

		.card-type {
			margin: 20rpx;
			// height: 300rpx;
			background: $uni-bg-color;
			border-radius: 12rpx;
			padding: 30rpx;

			.card-warpper {
				margin-top: 20rpx;

				.card-item {
					margin-bottom: 20rpx;
					display: flex;
					align-items: center;
					height: 128rpx;
					background: #F8F8F8;
					border-radius: 12rpx;
					padding: 25rpx 33rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;

					&>image {
						margin-right: 28rpx;
						width: 56rpx;
						height: 56rpx;
					}

					.card-name {
						font-size: 28rpx;
						line-height: 40rpx;

					}

					.card-desc {
						line-height: 33rpx;
						font-size: 24rpx;
					}

				}

				.card-selector {
					background: #E4EFFF;
					border-radius: 12rpx;
					border: 2rpx solid #009ff6;
					// border-image: linear-gradient(180deg, rgba(0, 159, 246, 1), rgba(0, 102, 233, 1)) 2 2;
					// clip-path: inset(0 round 12rpx);

					.card-type,
					.card-desc {
						// color: $uni-bg-color;
					}
				}
			}
		}
	}
</style>