<template>
	<view>
		<web-view ref="webview" :src="ownPath" @message="handlePostMessage" />
	</view>
</template>

<script>
	import {
		uni_decodeURIComponent
	} from '@/common/helper.js';

	export default {
		data() {
			return {
				ownPath: '',
				signKey: '',
			}
		},
		onLoad(options) {
			if (options.ownPath) {
				console.log('options.ownPath', options.ownPath)
				let ownPath = JSON.parse(uni_decodeURIComponent(options.ownPath))
				this.ownPath = ownPath
				if (options.signKey) {
					this.signKey = options.signKey
					uni.setStorageSync('signKey', this.signKey)
				}
			}
		},
		methods: {
			// webview向外部发送消息
			handlePostMessage(evt) {

			},
		}
	}
</script>

<style lang="scss" scoped>
</style>