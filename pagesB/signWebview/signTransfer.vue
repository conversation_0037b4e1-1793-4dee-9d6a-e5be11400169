<template>
	<view class="transfers-box">
		<view class="image-box">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/package/vehicleLoading.gif"
				mode="aspectFill" class="vehicle-loading">
			</image>
		</view>
		<view class="desc">
			电子协议正在签署中...
		</view>

	</view>
</template>

<script>
	import {
		getTicket,
		getLoginUserInfo
	} from '@/common/storageUtil.js';
	export default {

		data() {
			return {
				// redirectPath: '', // 重定向路由地址
				// scene: '',
				signKey: '',
				type: '',
				signTypeObj: {},
				vehicleInfo: ''
			};
		},
		computed: {

		},
		onLoad(option) {
			console.log('option=>>>>>>>>>>>>>>,',
				option)
			this.signKey = uni.getStorageSync('signKey')
			this.signTypeObj = uni.getStorageSync('applyType')
			let vehicleInfo = this.$store.getters['afterSale/afterSaleVehicle']
			this.vehicleInfo = vehicleInfo
			console.log('获取缓存signKey===>>>', this.signKey)
			console.log('获取缓存applyType===>>>', this.signTypeObj)
			//默认B走袁翔B端接口，C走廖宏振新办接口
			//signType区分1-抖音,2-产品转换 判断成功后跳转页面
			if (option.type) {
				this.type = option.type
				if (this.signTypeObj.signType == '2') {
					//产品转换
					this.confirm()
					return
				}
				this.confirmByNewApply()
			} else {
				this.confirm()
			}

		},
		created() {

		},
		methods: {
			confirmByNewApply() {
				let params = {
					signKey: this.signKey,
					netUserNo: getLoginUserInfo().userNo
				}


				let data = {
					data: params,
				};

				this.$request
					.post(this.$interfaces.signConfirm, data)
					.then((res) => {
						if (res.code == 200) {
							console.log('签章确认res', res)
							if (this.signTypeObj.signType == '1') {
								uni.reLaunch({
									url: '/pagesA/newBusiness/douyin/userAgreement/agreementList'
								})
								uni.removeStorageSync('applyType')
							} else {
								uni.reLaunch({
									url: '/pagesA/newBusiness/userAgreement/agreementList'
								})
							}
							uni.removeStorageSync('signKey')
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false,
						})
					})
			},
			confirm() {
				let data = {
					routePath: this.$interfaces.signConfirmByB.method,
					bizContent: {
						signKey: this.signKey,
						netUserNo: getLoginUserInfo().userNo
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						if (res.code == 200) {
							console.log('res', res)
							uni.removeStorageSync('signKey')
							if (this.signTypeObj.signType == '2') {
								//产品转换跳转页面
								this.getCardAccountAmount()
								uni.removeStorageSync('applyType')
								return
							}
							uni.reLaunch({
								url: './signSuccess'
							})

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false,
						})
					})
			},
			getCardAccountAmount() {
				console.log('this.signTypeObj.cardNo', this.signTypeObj.cardNo)
				this.isLoading = true
				let params = {
					cardNo: this.signTypeObj.cardNo
				}
				this.$request.post(this.$interfaces.getCardAccountAmount, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('获取余额', res)
						let amount = res.data.amount
						if (amount > 0) {
							//去到余额页面
							uni.reLaunch({
								url: '/pagesC/productConver/refundAmount?amount=' + amount
							})
						} else {
							uni.reLaunch({
								url: '/pagesC/productConver/converSuccess'
							})
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},

		}
	};
</script>

<style scoped lang="scss">
	.transfers-box {
		width: 100%;
		height: 100%;
		background-color: #fff;

		.image-box {
			width: 100%;
			display: flex;
			justify-content: center;
			padding-top: 180rpx
		}

		.vehicle-loading {
			width: 370rpx;
			height: 278rpx;
		}

		.desc {
			font-size: 28rpx;
			width: 100%;
			font-weight: 400;
			text-align: center;
		}
	}
</style>