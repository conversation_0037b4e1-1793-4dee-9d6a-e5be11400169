<template>
	<view>
		<web-view ref="webview" :src="ownPath" @message="handlePostMessage" />
	</view>
</template>

<script>
	import {
		uni_decodeURIComponent
	} from '@/common/helper.js';

	export default {
		data() {
			return {
				ownPath: '',
				signKey: '',
				// vehicles: {},
			}
		},
		onLoad(options) {
			if (options.ownPath) {
				console.log('options.ownPath', options.ownPath)
				let ownPath = JSON.parse(uni_decodeURIComponent(options.ownPath))
				this.ownPath = ownPath
			}
		},
		methods: {
			// webview向外部发送消息
			handlePostMessage(evt) {

			},	
		}
	}
</script>

<style lang="scss" scoped>
</style>