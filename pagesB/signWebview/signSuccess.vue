<template>
	<view class="order-success g-flex g-flex-center g-flex-align-center g-flex-column">
		<!-- 		<image class="img" v-if="orderStatusFlg!='' && orderStatusFlg == '1'"
			src="../static/productConver/pic_success.png" mode=""></image>
		<image class="img" v-if="orderStatusFlg!='' && orderStatusFlg == '0'" src="../static/productConver/pic_fail.png"
			mode=""></image> -->
		<image class="img" src="../static/pic_success.png" mode=""></image>
		<view class="success-desc">
			<view class="">
				签约成功，可以继续进行其他操作。
			</view>
		</view>
		<!-- <view class="fail-desc" v-if="orderStatusFlg!='' && orderStatusFlg == '0'">
			<view style="margin-top: 30rpx;">
				转换失败，失败原因：
			</view>
			<view class="fail-reson" style="margin-top: 30rpx;">
				状态码:[{{failRes.code}}]{{failRes.msg}}
			</view>
			<view style="margin-top: 30rpx;">
				请截图联系<text class="kf" @click="toWebView">在线客服</text>或拨打客服热线<text class="link"
					@click="call('0771-5896333')">0771-5896333</text>处理，处理完成后，
				您可通过【服务订单】- 选择此车辆的产品转换订单，继续完成转换。
			</view>
		</view> -->
		<!-- 		<tButton v-if="orderStatusFlg!='' && orderStatusFlg == '1'" :buttonList="buttonList" @toHome="toHome"
			@toRecharge="toRecharge">
		</tButton>
		<tButton v-if="orderStatusFlg!='' && orderStatusFlg == '0'" :buttonList="failButtionList" @toHome="toHome">
		</tButton> -->
		<tButton :buttonList="buttonList" @toHome="toHome">
		</tButton>
		<!-- <tLoading :isShow="isLoading" /> -->
	</view>
</template>

<script>
	import tButton from '@/pagesB/components/t-button/t-button.vue'
	export default {
		components: {
			tButton,
		},
		data() {
			return {
				isLoading: false,
				converId: '',
				result: {},
				buttonList: [{
					title: '返回首页',
					handle: 'toHome'
				}],
			}
		},
		onLoad() {

		},
		methods: {
			toHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>/p-home'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-success {
		padding-top: 60rpx;
		margin: 20rpx;
		border-radius: 12rpx;
		background-color: #ffffff;

		.success-desc,
		.fail-desc {
			padding: 50rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;
		}

		// .img {
		// 	width: 452rpx;
		// 	height: 437rpx;
		// }
	}

	.link {
		font-weight: 700;
		margin-top: 30rpx;
		text-align: center;
		color: #3E98FF;
		text-decoration: underline;
		text-underline-offset: 5rpx;
	}

	.kf {
		font-weight: 700;
		margin-top: 30rpx;
		text-align: center;
		color: #3E98FF;
	}
</style>