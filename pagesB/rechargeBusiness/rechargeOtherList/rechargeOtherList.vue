<!-- 新办订单列表 -->
<template>
	<view class="order-list">
		<scroll-view
			:style="'height:' + (windowHeight - 10) + 'px;'"
			:scroll-top="scrollTop"
			scroll-y="true"
			class="scroll-Y"
			:lower-threshold="lowerThreshold"
			@scrolltolower="scrolltolower"
			@scroll="scroll"
		>
			<view class="no-vehicle" v-if="rechargeList.length == 0">
				<image src="../../../static/toc/no-data.png" mode="aspectFilt" class="no-vehicle_icon"></image>
				<view class="no-vehicle_des">暂无代充记录</view>
			</view>
			<view v-else>
				<view class="item" v-for="(item, index) in rechargeList" :key="index">
					<view class="weui-form-preview">
						<view class="weui-form-preview__hd">
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">{{ item.carNo + '【' + vehicleColorStr(item.carColor) + '】' }}</view>

								<view class="weui-form-preview__value chargeMoney">￥{{moneyFilter(item.amount)}}元</view>
							</view>
						</view>
						<view class="weui-form-preview__bd">
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">订单编号</view>
								<view class="weui-form-preview__value">{{ item.orderId }}</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">ETC卡号</view>
								<view class="weui-form-preview__value">{{ item.cardNo }}</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">卡账余额</view>
								<view class="weui-form-preview__value">{{ item.payStatus=='2'?moneyFilter(item.afterAmount):moneyFilter(item.beforeAmount) }}元</view>
							</view>
							
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">支付方式</view>
								<view class="weui-form-preview__value">{{ item.rechargeType=='10000601'?'微信小程序支付':''}}</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">支付状态</view>
								<view class="weui-form-preview__value">{{ getPayStatus(item.payStatus)}}</view>
							</view>
							<view class="weui-form-preview__item">
								<view class="weui-form-preview__label">支付时间</view>
								<view class="weui-form-preview__value">{{item.createTime }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<load-more :loadStatus="noticeLoadStatus" />
	</view>
</template>

<script>
import { getCurrUserInfo, getCurrentCar, getLoginUserInfo } from '@/common/storageUtil.js';
import loadMore from '../../components/load-more/index.vue';
import {moneyFilter} from '@/common/util.js'
import { getVehicleColor, getVehicleClassType, getVehicleType,getPayStatus, getBusinessType, getApplyStatus, getCodeStatus } from '@/common/method/filter.js';
export default {
	name: '',
	components: { loadMore },

	data() {
		return {
			lowerThreshold: 120,
			windowHeight: this.windowHeight,
			cardAmount: {},
			scrollTop: 0,
			noticeLoadStatus: 3,
			old: {
				scrollTop: 0
			},
			rechargeList: [],
			page_num: 0,
			page_size: 10,
			flag: false
		};
	},
	computed: {
		customerInfo() {
			return getCurrUserInfo() || {};
		},
		vehicleInfo() {
			return getCurrentCar() || {};
		},
		
	},
	created() {
		this.getRechargeList();
	},
	methods: {
		moneyFilter,
	getPayStatus,
		vehicleType(val) {
			return getVehicleType(val);
		},
		vehicleColorStr(val) {
			return getVehicleColor(val);
		},
		vehicleClassType(val) {
			return getVehicleClassType(val);
		},
		scrolltolower: function(e) {
			if (this.flag) return;
			let self = this;
			setTimeout(function() {
				self.page_num = self.page_num + 1;
				self.getRechargeList();
			}, 500);
		},
		scroll: function(e) {
			this.old.scrollTop = e.detail.scrollTop;
		},
		getRechargeList() {
			this.noticeLoadStatus = 1;
			let data = {
				routePath: this.$interfaces.rechargeOther.method,
				bizContent: {
					userNo: getLoginUserInfo().userNo,
					pageNum: this.page_num,
					pageSize: this.page_size
				}
			}
			
			this.$request
				.post(this.$interfaces.issueRoute, {
					data: data
				})
				.then(res => {
					console.log(res, '11111111');
					if (res.code == 200) {
						let result = res.data.records || [];
						if (res.data.records.length) {
							this.rechargeList = this.rechargeList.concat(result);
						} else {
							this.noticeLoadStatus = 3;
							this.flag = true;
						}
						if (this.rechargeList.length == res.data.total) {
							this.noticeLoadStatus = 3;
							this.flag = true;
						}
					} else {
						this.noticeLoadStatus = 2;
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				})
				.catch(err => {
					this.noticeLoadStatus = 2;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				});
		},
		
	}
};
</script>

<style scoped lang="scss">
.no-vehicle {
	padding-top: 320rpx;
	width: 100%;
}

.no-vehicle .no-vehicle_icon {
	width: 280rpx;
	height: 280rpx;
	margin: 0 auto;
	display: block;
}

.no-vehicle .no-vehicle_des {
	font-size: 28rpx;
	color: #999999;
	font-weight: 400;
	text-align: center;
	margin-top: 20rpx;
}
.order-list .item {
	margin: 20rpx;

	background-color: #ffffff;
}

.weui-form-preview {
	position: relative;
	border-radius: 10px;
	padding: 30rpx;
	background-color: #ffffff;
}

.weui-form-preview__hd {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1px dashed #c3c3c3;

	.weui-form-preview__label {
		min-width: 200rpx;
		color: #333;
		font-size: 32rpx;
		font-weight: 500;
		text-align: left;
	}

	.weui-form-preview__value {
		font-style: normal;
		font-size: 28rpx;
		font-weight: 400;
	}
	.chargeMoney {
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #ff9d09;
		font-size: 40rpx;
	}
}

.weui-form-preview__bd {
	padding-top: 12rpx;

	.weui-form-preview__item {
		padding-bottom: 12rpx;
	}
}

.weui-form-preview__item {
	display: flex;
	-moz-box-pack: justify;
	-ms-box-pack: justify;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-justify-content: space-between;
	justify-content: space-between;
	-moz-box-align: center;
	-webkit-box-align: center;
	box-align: center;
	align-items: center;
	-webkit-align-items: center;
	-moz-align-items: center;
}

.weui-form-preview__label {
	color: #999999;
	font-size: 26rpx;
	font-weight: 400;
}

.weui-form-preview__value {
	color: #333;
	font-size: 26rpx;
	font-weight: 400;
	display: block;
	overflow: hidden;
	word-break: normal;
	word-wrap: break-word;
}

.weui-form-preview__ft {
	padding-bottom: 10rpx;
}
</style>
