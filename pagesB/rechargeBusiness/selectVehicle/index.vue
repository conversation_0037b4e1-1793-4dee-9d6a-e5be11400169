<!--
  * @desc:优化充值——车辆选择列表
  * @author:zhangys
  * @date:2023-02-06 14:52:56
!-->
<template>
	<view class="vehicle_list_container">
		<view class="vehicle_list_btn g-flex g-flex-end g-flex-align-center " v-if="businessType!='rechargeList'">
			<text class="btns" @click="goRechargeOtherHandle">充值其他车辆</text>| <text class="btns"
				@click="rechargeList">充值记录</text>
			<text style="margin:0 10rpx;font-size: 38rpx;" class="cuIcon-question" @click="showVideo"></text>
		</view>
		<view class="no-vehicle" v-if="isNoVehicle">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/addcar_nocar.png"
				mode="aspectFilt" class="no-vehicle_icon">
			</image>
			<view class="no-vehicle_des">
				暂无车辆
			</view>
			<view class="g-flex g-flex-horizontal-vertical" v-if='isNoVehicle&&businessType!="rechargeList"'>
				<TButton title="前往绑定ETC" @clickButton="bindEtcAccount" style="width: 50%;"></TButton>
			</view>
		</view>
		<!-- 车辆列表 -->
		<view class="" v-if="vehicleList.length>0">
			<view class="vehicle_item g-flex g-flex-column" v-for='(item,index) in vehicleList' :key='index'>
				<view class="item_info g-flex  g-flex-align-center g-flex-justify">
					<view class="g-flex  g-flex-align-center">
						<view class="vehicleInfo_img">
							<img v-if="item.vehicleType =='2'" src="../../static/vehicleIcon/car_icon.png" alt="">
							<img v-if="item.vehicleType =='1'" src="../../static/vehicleIcon/truck_icon.png" alt="">
							<img v-if="item.vehicleType =='3'" src="../../static/vehicleIcon/priatecar_icon.png" alt="">
						</view>
						<view class="vehicleInfo g-flex g-flex-column">
							<view class="vehicleInfo_vehicle">
								{{item.vehicleCode}}【{{getVehicleColor(item.vehicleColor)}}】
							</view>
							<view style="font-size: 28rpx;">{{getVehicleType(item.vehicleType)}}车
								{{gxCardTypeFilter(item.cardProduct)}}
							</view>
						</view>
					</view>

					<view class="">
						<view class='cu-tag light ' :class="item.cardStatus=='1'?'bg-olive':'bg-red'">
							{{item.cardStatus=='1'?'正常':'限制通行'}}
						</view>
					</view>
				</view>
				<view class="item_info">
					<view class="line">

					</view>
				</view>
				<!-- 金额信息 -->
				<view class="item_info g-flex balance g-flex-align-center g-flex-justify"
					:class="item.cardProduct=='0' && item.returnAmount > 0 ? 'g-flex-align-baseline':''">
					<view>
						<view v-if="item.cardProduct=='0'" class="balance_money">
							可圈存余额：{{moneyFilter(item.encircleAmount)}}元
						</view>
						<view style="color: #888888;" v-if="item.cardProduct=='0' && item.returnAmount > 0">
							(包含卡面回补金额{{moneyFilter(item.returnAmount)}}元)
						</view>
					</view>
					<!-- <text class="balance_money">{{item.cardProduct=='0'?'卡片可用余额':'可用余额'}}：<text
							:style="item.cardProduct=='5'&&item.availableAmount<0?'color:red':''">{{moneyFilter(item.availableAmount)}}元</text>
					</text> -->

					<text class="balance_money"
						v-if="item.cardProduct=='0'">卡片可用余额：<text>{{moneyFilter(item.availableAmount)}}元</text>
					</text>
					<text class="balance_money" v-if="item.cardProduct=='5'">账户可用余额：<text
							:style="subMoney(item.availableAmount,item.blackAmount)<0?'color:red':''">{{moneyFilter(subMoney(item.availableAmount,item.blackAmount))}}元</text>
					</text>
					<text v-if="item.cardProduct!='0'"
						class="balance_money">最低预存金标准值：{{moneyFilter(item.blackAmount)}}元</text>
				</view>
				<!-- tips -->
				<view class="item_info" v-if="item.cardProduct=='0'">
					<view class="tips">
						<text style="color: red;">*</text>注:卡片余额仅供参考，如有不符请致电客服或咨询线下网点
					</view>
				</view>
				<view>
					<view class="g-flex g-flex-center" v-if="businessType!='rechargeList'">
						<button class="cu-btn round weui-btn_mini weui-btn_primary"
							style="color: #FFF ;background-color:#0066e9 ;" @click="vehicleInfo(item,'recharge')">
							充值</button>
					</view>
					<view class="g-flex g-flex-center" v-else>
						<button class="cu-btn round weui-btn_mini weui-btn_primary"
							style="color: #FFF ;background-color:#0066e9 ;" @click="vehicleInfo(item,'rechargeList')">
							充值记录</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 客账用户列表 -->
		<view class="" v-if="Object.keys(accountList).length!=0">
			<view class="vehicle_item g-flex g-flex-column">
				<view class="item_info g-flex  g-flex-align-center g-flex-justify">
					<view class="g-flex  g-flex-align-center">
						<view class="vehicleInfo_img">
							<img src="../../static/vehicleIcon/car_icon.png" alt="">
						</view>
						<view class="vehicleInfo g-flex g-flex-column">
							<view class="vehicleInfo_vehicle">{{accountList.custName}}
							</view>
							<view style="font-size: 28rpx;">
								日日通客账模式用户
							</view>
						</view>
					</view>
					<view class="">
						<view class='cu-tag light ' :class="accountList.amount<0?'bg-red':'bg-olive'">
							{{accountList.amount<0?' 限制通行':'正常'}}
						</view>
					</view>
				</view>
				<view class="item_info">
					<view class="line">

					</view>
				</view>
				<!-- 金额信息 -->
				<view class="item_info g-flex balance g-flex-align-center g-flex-justify">
					<view class="balance_money">账户余额：<text
							:style="accountList.amount<0?'color:red':''">{{moneyFilter(accountList.amount)}}</text> 元
					</view>
					<view class="balance_money">最低预存金标准值：{{moneyFilter(accountList.blackAmount)}}元</view>
				</view>

				<view class="g-flex g-flex-center" style="position: relative;">
					<view class="g-flex g-flex-center">
						<button class="cu-btn round weui-btn_mini weui-btn_primary" v-if="businessType!='rechargeList'"
							style="color: #FFF ;background-color:#0066E9 ;" @click="accountRecharge"> 充值</button>

						<button class="cu-btn round weui-btn_mini weui-btn_primary" v-else
							style="color: #FFF ;background-color:#0066E9 ;" @click="accountRechargeList">充值记录</button>
					</view>
					<view class="preview_vehicle g-flex g-flex-align-center " @click="open">
						查看车辆 <img src="../../static/vehicleIcon/arrow_icon.png" mode=""></img>
					</view>

				</view>
			</view>
		</view>
		<uni-popup ref="popup" backgroundColor="#fff">
			<view class="half-screen-dialog" style="background: #fff;border-radius: 20px 20px 0px 0px">
				<view class="half-screen-dialog__hd g-flex g-flex-align-center">
					<view class="half-screen-dialog__hd__main ">
						<view class="title">
							查看车辆
						</view>
					</view>
					<view class="half-screen-dialog__hd__side"
						:class="['half-screen-dialog__hd__side--' + closeIconPos]" @click="closePopup('bottom')">
						<text class="cuIcon-close close"></text>
					</view>
				</view>
				<view class="half-screen-dialog__bd" style="margin: 30rpx 0;">
					<view class="preview_vehicle_container  ">
						<view v-for="(item,index) in accountList.bindVehicleList" :key="index">
							<view class="bind_vehilce g-flex g-flex-align-center g-flex-justify">
								<view class="g-flex g-flex-align-center">
									<img v-if="item.vehicleType =='2'" style="width: 60rpx;height: 60rpx;"
										src="../../static/vehicleIcon/mini_car_icon.png" alt="">
									<img v-if="item.vehicleType =='1'" style="width: 60rpx;height: 60rpx;"
										src="../../static/vehicleIcon/mini_truck_icon.png" alt="">
									<img v-if="item.vehicleType =='3'" style="width: 60rpx;height: 60rpx;"
										src="../../static/vehicleIcon/mini_priatecar_icon.png" alt="">
									<view class="bind_vehicle_info g-flex g-flex-column">
										<view class="bind_vehicle_code">{{getVehicleType(item.vehicleType)}}车
											{{item.carNo}}【{{getVehicleColor(item.carColor)}}】
										</view>
										<view style="font-size: 26rpx;">最低预存金标准值：{{moneyFilter(item.blackAmount)}}元</view>
									</view>
								</view>

								<view class="">
									<view class='cu-tag light ' :class="item.cardStatus=='1'?'bg-olive':'bg-red'">
										{{item.cardStatus=='1'?'正常':'限制通行'}}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- <view class="weui-btn_wrapper" v-if='isNoVehicle&&businessType!="rechargeList"'>
			<TButton title="前往绑定ETC" @clickButton="bindEtcAccount"></TButton>
		</view> -->
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		gxCardTypeFilter
	} from '@/common/method/filter.js'
	import {
		setCurrentCar,
		getAccountId,
		getDefaultUrl,
		setDefaultUrl,
		setRechargeType,
		getCurrUserInfo,
		getLoginUserInfo
	} from '@/common/storageUtil.js'
	import TButton from '@/components/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue'
	import float from '@/common/method/float.js'
	export default {
		name: '',
		props: {

		},
		data() {
			return {
				closeIconPos: 'top-right',
				isNoVehicle: false,
				isLoading: false,
				isNeedFlag: false, //补签弹框
				vehicleList: [],
				accountList: {},
				businessType: '',
				// isOwner: '0' //判断个人承诺书
			}
		},
		components: {
			tLoading,
			TButton
		},
		computed: {},
		onLoad(obj) {
			this.businessType = obj.fontType
			if (obj.fontType == 'rechargeList') {
				uni.setNavigationBarTitle({
					title: '充值记录'
				})
			} else {
				uni.setNavigationBarTitle({
					title: '充值'
				})
			}

		},
		onShow() {
			setRechargeType(' ')
			this.getVehicleList()
		},
		methods: {
			moneyFilter(money) {
				return float.div(money, 100)
			},
			subMoney(a, b) {
				return float.sub(a, b)
			},
			open(type) {
				this.$refs.popup.open('bottom')
			},
			onCloseHandle(type, action) {
				this.closePopup('bottom');

			},
			closePopup(type) {
				this.$refs.popup.close(type)
			},
			getVehicleColor,
			getVehicleClassType,
			getVehicleType,
			gxCardTypeFilter,
			getVehicleList() {
				if (!getAccountId()) {
					this.isNoVehicle = true
					return
				}
				let params = {
					customerId: getAccountId()
				}
				this.$request
					.post(this.$interfaces.rechargeVehicleList, {
						data: params
					})
					.then((res) => {
						console.log(res, '车辆列表');
						if (res.code == 200) {
							if (res.data.vehicleList) {
								this.vehicleList = res.data.vehicleList
							}
							if (res.data.clientAccount) {
								this.accountList = res.data.clientAccount
							}
							this.isNoVehicle = this.vehicleList.length == 0 && Object.keys(this.accountList).length ==
								0
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
			},
			getNeedResign(item, callback) {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.needResign.method,
					bizContent: {
						netUserNo: getLoginUserInfo().userNo,
						customerId: getAccountId(),
						vehicleCode: item.vehicleCode,
						vehicleColor: item.vehicleColor,
						productType: item.cardProduct
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('查询补签', res)
							callback(res)
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						})
					})
			},
			// 查询车辆详情接口
			vehicleInfo(item, type) {
				//补签流程
				this.getNeedResign(item, resp => {
					if (resp.data.isNecessary && !resp.data.conStatus && !this.isNeedFlag) {
						//弹窗标识
						if (this.businessType != 'rechargeList') {
							this.isNeedFlag = true
						}
						//需要签署协议。
						let etcUser = getCurrUserInfo()
						//判断ETC用户是否一致
						if (resp.data.isOwner == '0') {
							uni.showModal({
								title: '提示',
								content: '为给您提供更好的服务，请通知' + item.vehicleCode +
									'客户' + etcUser.customer_name + '登录‘桂小通’微信小程序签署电子协议',
								showCancel: this.businessType != 'rechargeList' ? true : false
							})
							return
						} else {
							uni.showModal({
								title: '提示',
								content: '为给您提供更好的服务，明确双方权利和义务关系，请您签署用户电子协议',
								showCancel: this.businessType != 'rechargeList' ? true : false,
								confirmText: '前往签署',
								success: (res) => {
									if (res.confirm) {
										this.getMarketId(item)
									}
								}
							})
						}
					} else {
						//无需补签
				if (this.isLoading) return
				this.isLoading = true
				setCurrentCar({})
				let data = {
					routePath: this.$interfaces.vehicleBizSearch.method,
					bizContent: {
						vehicle_code: item.vehicleCode,
						vehicle_color: item.vehicleColor
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200 && res.data && res.data.length) {
							setCurrentCar(res.data[0])
							this.goCarBusinessPage(res.data[0], type)
						}
					})
					.catch((error) => {
						this.isLoading = false
					})
					}
				})

			},
			//废弃
			// getContractOwner(item) {
			// 	this.isLoading = true
			// 	//单位用户不需要个人承诺书,直接下一步
			// 	if (getCurrUserInfo().customer_type == '1') {
			// 		//一致就获取营销活动id
			// 		this.getMarketId(item)
			// 		return
			// 	}
			// 	let data = {
			// 		routePath: this.$interfaces.contractOwner.method,
			// 		bizContent: {
			// 			customerId: getCurrUserInfo().customer_id,
			// 			carNo: item.vehicleCode,
			// 			carNoColor: item.vehicleColor
			// 		}
			// 	}
			// 	this.$request
			// 		.post(this.$interfaces.issueRoute, {
			// 			data: data
			// 		})
			// 		.then((res) => {
			// 			this.isLoading = false
			// 			if (res.code == 200) {
			// 				this.isOwner = res.data.isOwner
			// 				this.getMarketId(item)
			// 			} else {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				})
			// 			}
			// 		})
			// 		.catch((error) => {
			// 			this.isLoading = false
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: res.msg,
			// 				showCancel: false
			// 			})
			// 		});
			// },
			getMarketId(item) {
				console.log('获取marktId')
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.resignMarketListV2.method,
					bizContent: {
						customerId: getAccountId(),
						vehicleCode: item.vehicleCode,
						vehicleColor: item.vehicleColor,
						productType: item.cardProduct
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('查询营销活动', res)
							if (res.data.length > 0) {
								let marketList = res.data[0]
								this.createUserSign(item, marketList)
							} else {
								uni.showModal({
									title: "提示",
									content: '获取不到营销方案！',
									showCancel: false,
								});
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						})
					})
			},
			createUserSign(item, marketList) {
				console.log('签约==========>>>>')
				let params = {
					source: '1', //存在etc用户
					customerId: getCurrUserInfo().customer_id,
					// custType: '1', // customerId必填
					vehicles: {
						vehicleCode: item.vehicleCode,
						vehicleColor: item.vehicleColor,
					},
					signName: getCurrUserInfo().customer_name,
					signPhone: getCurrUserInfo().link_mobile,
					signIdNo: getCurrUserInfo().certificates_code,
					marketId: marketList.marketActiveMastId,
					businessType: '7', //7补签
					productType: item.cardProduct,
					isOwner: '1' //默认本人
				}

				// console.log('prams===========>', params)

				// let data = {
				// 	data: params,
				// };
				let data = {
					routePath: this.$interfaces.newSignPreview.method,
					bizContent: params
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('签章返回===>>>', res)
							let signKey = res.data.signKey
							uni.setStorageSync('signKey', signKey)
							// uni.setStorageSync('applyType', '3') //通用签约类型3
							let pdfInfo = res.data.data
							//btnType=B B是使用袁翔的B端接口签约回调
							let signUrl = 'https://portal.gxetc.com.cn/new-agreement?btnType=B&type=sign&signInfo=' +
								encodeURIComponent(JSON.stringify(
									pdfInfo))

							uni.reLaunch({
								url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
									.stringify(
										signUrl))
							})

						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			//客账充值跳转
			accountRecharge() {
				let params = JSON.parse(JSON.stringify(this.accountList))
				params.bindVehicleList = params.bindVehicleList ? JSON.stringify(params.bindVehicleList) : []
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/rechargeAccount/index?' + this.objToUrlParam(params)
				})
			},
			//客账充值记录跳转
			accountRechargeList() {
				let params = JSON.parse(JSON.stringify(this.accountList))
				params.bindVehicleList = params.bindVehicleList ? JSON.stringify(params.bindVehicleList) : []
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/rechargeAccountList/index?' + this.objToUrlParam(params)
				})
			},
			goRechargeOtherHandle() {
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/rechargeType/index?rechargeType=' + 'rechargeOther'
				})
			},
			rechargeList() {
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/selectVehicle/index?fontType=' + 'rechargeList'
				})

			},
			goCarBusinessPage(item, type) {
				if (!item.cpu_card_id) {
					uni.showModal({
						title: '提示',
						content: '您当前车辆' + item.vehicle_code + '未绑定ETC卡'
					})
					return
				}
				if (!item.obu_id) {
					uni.showModal({
						title: '提示',
						content: '您当前车辆' + item.vehicle_code + '未绑定OBU标签'
					})
					return
				}

				if (item.gx_card_type != '5' && item.gx_card_type != '0' && item.gx_card_type != '8') {
					if (type == 'recharge') {
						uni.showModal({
							title: '提示',
							content: '您当前车辆绑定的卡不是捷通日日通记账卡或储值卡，无法充值'
						})
						return
					}
					if (type == 'rechargeList') {
						uni.showModal({
							title: '提示',
							content: '您当前车辆绑定的卡不是捷通日日通记账卡或储值卡，无法查看充值记录'
						})
						return
					}
					return

				}
				if (item.gx_card_type == '5' || item.gx_card_type == '8') {
					if (type == 'rechargeList') {
						uni.navigateTo({
							url: '/pagesB/rechargeBusiness/rechargeList/p-rechargeList'
						})
						return
					}
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/recharge/p-recharge'
					})
					return
				}

				if (item.gx_card_type == '0') {
					if (type == 'rechargeList') {
						uni.navigateTo({
							url: '/pagesB/loadBusiness/recharge/rechargeRecord'
						})
						return
					}
					uni.navigateTo({
						url: '/pagesB/loadBusiness/recharge/recharge'
					})
					return
				}
			},
			//绑定ETC用户并回跳
			bindEtcAccount() {
				setDefaultUrl('/pagesB/rechargeBusiness/selectVehicle/index')
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList'
				})
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},
			//充值教程
			showVideo() {
				let url =
					'https://mp.weixin.qq.com/s?__biz=MzAxNjUxNDYwNg==&mid=**********&idx=1&sn=80a26ab3893aaa610be12eb7da4c6174&chksm=80b4481db7c3c10b46f92cec1e93da3cd32aefd3ea3639d2ac9e96b6cccea928839cc3b60f07&token=*********&lang=zh_CN#rd'
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(url)
				})
			}

		},
	}
</script>

<style lang='scss' scoped>
	.vehicle_list_container {
		padding-bottom: 140rpx;
	}

	.vehicle_list_btn {
		color: #3874FF;
		margin-top: 20rpx;
		padding: 0 20rpx;
		font-weight: bold;
		font-size: 30rpx;

		.btns {

			margin: 0 6rpx;
		}
	}

	.vehicle_item {
		/* width: 100%; */
		padding: 20rpx;
		border: 1px solid #e9e9e9;
		border-radius: 12rpx;
		margin: 20rpx 30rpx;
		box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
		background-color: #fff;

		.item_info {
			margin: 10rpx 0;

			.vehicleInfo_img {
				width: 100rpx;
				height: 100rpx;

				&>img {
					width: 100%;
					height: 100%;
				}
			}

			.vehicleInfo {
				font-size: 30rpx;
				margin-left: 40rpx;

				.vehicleInfo_vehicle {
					margin-bottom: 20rpx;
					font-weight: bold;
				}
			}

			.balance {
				color: rgba(51, 51, 51, 1);
				font-size: 28rpx;

				.balance_money {
					margin: 10rpx 0;
				}
			}

			.tips {
				font-size: 24rpx;
				color: #888888;
				margin: 20rpx 0;
			}

			.line {
				margin: 20rpx 0;
				width: 100%;
				height: 1rpx;
				border-bottom: 1px dashed #C3C3C3;
			}
		}

		.preview_vehicle {
			right: 0;
			top: 24%;
			position: absolute;
			color: rgba(136, 136, 136, 1);
			font-size: 24rpx;

			&>img {
				width: 28rpx;
				height: 28rpx;
			}
		}
	}

	.preview_vehicle_container {
		max-height: 500rpx;
		overflow: hidden;
		overflow-y: scroll;
		border-radius: 20rpx;

		.bind_vehilce {
			border-bottom: 1px solid #E9E9E9;
			padding: 20rpx;
			margin: 0 10rpx;

			.bind_vehicle_info {
				margin-left: 40rpx;

				.bind_vehicle_code {
					font-weight: bold;
					margin-bottom: 20rpx;
				}
			}
		}
	}

	.half-screen-dialog__hd {
		height: 100rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;

		position: relative;
	}

	.half-screen-dialog__hd__main {
		width: 100%;
	}

	.half-screen-dialog__hd__main .title {
		color: #333333;
		font-size: 36rpx;
		font-weight: 700;
		padding-left: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__main .desc_title {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__side {
		position: absolute;
		z-index: 3;
	}

	.half-screen-dialog__hd__side .close {
		font-size: 36rpx;
		color: #333333;
	}

	.half-screen-dialog__hd__side--top-left {
		top: 30rpx;
		left: 30rpx;
	}

	.half-screen-dialog__hd__side--top-right {
		top: 30rpx;
		right: 30rpx;
	}

	.half-screen-dialog__bd {
		margin-top: 30rpx;
	}

	.half-screen-dialog__ft {
		padding: 20rpx 56rpx 48rpx 56rpx;
	}

	.no-vehicle {
		padding-top: 320rpx;
		width: 100%;
		/* background-color: #f9f9f9; */
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 29rpx;
		color: #333333;
		font-weight: 400;
		text-align: center;
		margin-top: 60rpx;
	}

	.weui-btn_mini {
		width: 200rpx !important;
		font-weight: 500;
		font-size: 28rpx
	}

	.weui-btn_wrapper {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
	}
</style>