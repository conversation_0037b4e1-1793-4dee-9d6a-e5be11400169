<!--
  * @desc:代充方式选择
  * @author:zhang<PERSON>
  * @date:2023-02-20 10:39:45
!-->
<template>
	<view class="load-type">
		<view class="content g-flex g-flex-column g-flex-justify">
			<view class="item-img" @click="selectRechargeType('code')">
				<img src="../../static/recharge_by_code.png" alt="">
			</view>
			<view class="item-img" @click="selectRechargeType('equip')">
				<img src="../../static/recharge_by_bt.png" alt="">
			</view>

		</view>
		<view class="load_desc">
			<view class="desc_title">
				充值须知：
			</view>
			<view class="desc_text">
				<text style="color:#F1A633 ">输入车牌号充值：</text>输入车牌号即可充值无需读取ETC设备，推荐日日通产品用户使用。
			</view>

			<view class="desc_text">
				<text style="color:#F65B5B">连接设备快充：</text>读取ETC设备进行圈存无需输入车牌，推荐储值卡产品用户使用。
			</view>


		</view>

	</view>
	</view>
</template>

<script>
	import {
		getTokenId,
		getAccountId,
		setEtcVehicle,
		setRechargeType
	} from '@/common/storageUtil.js';

	export default {
		components: {

		},
		data() {
			return {
			rechargeType:''
			}
		},
		onLoad(val) {
			if (val&&val.rechargeType) {
				this.rechargeType=val.rechargeType
			}
		},
		created() {

		},
		methods: {
			selectRechargeType(val) {
				if (val == 'code') {
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/rechargeOther/rechargeOther'
					})
					return
				}
				if (val == 'equip') {
					if(this.rechargeType=='rechargeOther'){
						setRechargeType('rechargeOther')
					}
					uni.navigateTo({
						url: '/pagesB/loadBusiness/loadType'
					})
				} 
			}
		}

	}
</script>

<style scoped lang="scss">
	.load-type {
		width: 100%;
		height: 100%;

		.content {
			.item-img {
				margin: 60rpx 60rpx 30rpx 60rpx;
				width: 630rpx;
				height: 130rpx;

				&>img {
					width: 100%;
					height: 100%;
				}
			}

		}
	}



	.load_desc {
		margin: 60rpx 36rpx;



		.desc_tips {
			font-size: 24rpx;
			color: #666666;
			margin: 10rpx 0 30rpx 0;
			text-align: center;
		}

		.desc_title {
			font-size: 28rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 500;
			color: #555555;
			margin-bottom: 20rpx;
		}

		.desc_text {
			text-align: left;
			line-height: 50rpx;
			text-indent: 2em;
			padding-bottom: 10rpx;
			font-size: 26rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #aaaaaa;
		}
	}
</style>
