<template>
	<view class="sellPay-container">
		<view class="scroll-box">
			<scroll-view :style="'height:'+(windowHeight-10)+'px;'" :scroll-top="scrollTop" scroll-y="true"
				class="scroll-Y" :lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower"
				@scroll="scroll">
				<view class="sellPay-Info">
					<view class="c-title">用户信息</view>
					<form>
						<view class="cu-form-group">
							<view class="title">用户名称:</view>
							<input placeholder="用户名称" :value="vehicleInfo.customer_name" name="a" disabled></input>
						</view>
						<view class="cu-form-group">
							<view class="title">车牌号码:</view>
							<input placeholder="车牌号码" :value="vehicleInfo.vehicle_code" name="a" disabled></input>
						</view>
						<view class="cu-form-group">
							<view class="title">ETC卡号:</view>
							<input placeholder="ETC卡号" :value="vehicleInfo.cpu_card_id" name="a" disabled></input>
						</view>
						<view class="cu-form-group">
							<view class="title">最低预存金标准值:</view>
							<input placeholder="最低预存金标准值" :value="cardAmount.black_line" name="a" disabled></input>
							<text>元</text>
						</view>
						<view class="cu-form-group">
							<view class="title">账户可用余额:</view>
							<input placeholder="账户可用余额" :value="cardAmount.true_money" name="a" disabled></input>
							<text>元</text>
						</view>
					</form>
				</view>
				<rechargeList style="height: 100px;" v-for="(item,index) in rechargeList" :key='index'
					:rechargeInfo='item' @on-refund='onRefundHandle'>
				</rechargeList>
				<load-more :loadStatus="noticeLoadStatus" />
			</scroll-view>
		</view>

		<tLoading :isShow="isLoading" />

	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import rechargeList from './p-item.vue'
	import loadMore from '../../components/load-more/index.vue';

	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOrderSource,
		setOrderSource
	} from "@/common/storageUtil.js";
	import float from '@/common/method/float.js'
	export default {
		components: {
			TButton,
			rechargeList,
			loadMore,
			tLoading
		},
		data() {
			return {
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				cardAmount: {},
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				rechargeList: [],
				page_num: 1,
				page_size: 10,
				flag: false,
				isLoading: false,
				formData: {
					"card_no": ""
				},
				vehicleInfo: {} // 车辆信息
			};
		},
		onLoad(obj) {
			console.log(obj, 'objojbojboboobbj');
			if (obj && obj.cardNo) {
				this.formData.card_no = obj.cardNo
				this.vehicleBizSearch();
				return
			}
			this.formData.card_no = getCurrentCar() ? getCurrentCar().cpu_card_id : ''
			this.vehicleBizSearch();
		},

		computed: {

		},
		filters: {

		},
		created() {

		},
		methods: {
			// 退款
			onRefundHandle(item) {
				if (item.recharge_type == '55550000') {
					uni.showModal({
						title: '提示',
						content: '是否确认退款？',
						confirmText: '确定',
						success: res => {
							if (res.confirm) {
								this.netPayRefund(item)
							}
						}
					});
					return
				}
			},
			netPayRefund(item) {
				let data = {
					routePath: this.$interfaces.refoundBalanPayCard.method,
					bizContent: {
						order_id: item.order_id,
						remark:''
					}
				}
				this.isLoading = true;
				this.$request.post(this.$interfaces.issueRoute, {
						data: data
					}).then(res => {
						if (res.code == 200) {
							console.log(res);
							this.page_num = 1;
							this.rechargeList = [];
							this.loadRechargeList();
							this.isLoading = false;
							let status = res.data.success;
							let statusVal = {
								4: '退款成功',
								5: '退款失败',
								6: '退款中'
							};
							let msg = statusVal[status] || '退款失败';
							uni.showModal({
								title: '提示',
								content: msg,
								confirmText: '确定'
							});
						} else {

							this.isLoading = false;
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(err => {
						this.isLoading = false;

						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						});
					});
			},
			// 根据卡号查询用户信息
			vehicleBizSearch() {
				let data = {
					routePath: this.$interfaces.vehicleBizSearch.method,
					bizContent: {
						cpu_card_id: this.formData.card_no
					}
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;

						if (res.code == 200 && res.data && res.data.length) {
							this.vehicleInfo = res.data[0]
							this.loadRechargeList();
							this.loadCardAmount()
						}
					})
					.catch(error => {
						this.isLoading = false;
					});
			},
			loadCardAmount() {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.loadCardAmount.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id
					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						this.cardAmount = res.data;
						this.cardAmount.black_line = this.cardAmount.black_line ? this.moneyFilter(this.cardAmount
							.black_line) : '0'
						this.cardAmount.card_money = this.cardAmount.card_money ? this.moneyFilter(this
							.cardAmount
							.card_money) : '0'
						let true_money = float.sub(this.cardAmount.card_money, this.cardAmount.black_line)
						this.cardAmount.true_money = true_money
					}
				}).catch(error => {

				})
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				if (this.flag) return;
				let self = this;


				setTimeout(function() {
					self.page_num = self.page_num + 1;
					self.loadRechargeList();
				}, 500)

			},
			scroll: function(e) {

				this.old.scrollTop = e.detail.scrollTop;

			},
			loadRechargeList() {
				this.noticeLoadStatus = 1;

				let params = {
					routePath: this.$interfaces.loadRechargeList.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.cpu_card_id,
						page_num: this.page_num,
						page_size: this.page_size
					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					console.log(res);
					if (res.code == 200) {
						let result = res.data.records || []
						if (res.data.records.length) {
							this.rechargeList = this.rechargeList.concat(result)
						} else {
							this.noticeLoadStatus = 3;
							this.flag = true
						}
						if (this.rechargeList.length == res.data.total) {
							this.noticeLoadStatus = 3
							this.flag = true
						}
					} else {
						this.noticeLoadStatus = 2;
					}
				}).catch((err) => {

					this.noticeLoadStatus = 2;
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
		destroyed() {

		}
	};
</script>
<style lang="scss">
	.sellPay-Info {
		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.sellPay-Info .cu-form-group radio-group {

		flex: 1;
		text-align: left;
	}
</style>
