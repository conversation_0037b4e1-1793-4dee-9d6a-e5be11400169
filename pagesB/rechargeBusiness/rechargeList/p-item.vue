<template>
	<view class="cu-card article margin-top bg-white order-item">
		<view class="cu-item padding-top item-hd" style="padding-bottom: 0px;">
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text
						class="text-bold">充值金额：{{moneyFilter(rechargeInfo.money)}}元</text></view>

			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>充值方式：</text>{{rechargeInfo.recharge_type_str}}
				</view>
				
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item">
					<text>充值状态：</text>{{getPayStatus(rechargeInfo.pay_status)}}
				</view>
				<view class="item-text padding-left g-flex-item">
					<text>退款状态：</text>{{returnStatusFilter(rechargeInfo.return_status)}}
				</view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>订单编号：</text>{{rechargeInfo.order_id}}</view>

			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>充值时间：</text>{{rechargeInfo.create_time}}</view>

			</view>
		</view>
		<view class="padding-lr  order-item-btn">
			<view class="btn-item">
				<button class="weui-btn_mini weui-btn_primary" v-if="rechargeInfo.recharge_type =='55550000'" :disabled="!isDisableRefund" :class="[!isDisableRefund ? 'weui-btn_disabled' :'']" @click="onRefundHandle(rechargeInfo)">退款</button>
			</view>
		</view>
		<tLoading :isShow="isShowLoding" />

	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getPayStatus,
		returnStatusFilter
	} from '@/common/method/filter'
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		props: {
			rechargeInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {

				isShowLoding: false,
				schoolDetail: {

				},
			}
		},
		watch: {

		},
		components: {
			tLoading
		},
		computed: {
			isDisableRefund() {
				let endTime = dayjs(this.rechargeInfo.create_time).add(24, 'hour').format('YYYY-MM-DD HH:mm:ss');
				let time = dayjs().isBefore(dayjs(endTime))
				return time && ((this.rechargeInfo.return_status==null) &&(this.rechargeInfo.pay_status==='2'))
			}
		},
		created() {

		},
		methods: {
			getPayStatus,
			returnStatusFilter,
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
			onRefundHandle(item){
				this.$emit('on-refund',item)
			}
		}
	}
</script>
<style lang="scss" scoped>
	.order-item-btn {
		display: flex;
		justify-content: flex-end;
		margin-bottom: 20rpx;
		.color-btn {
			padding: 10upx 0;
			width: 100%;
		}

		.btn-item {
			min-width: 140rpx;
		}

	}



	.case-item {
		border-radius: 10upx;
	}

	.case-item .item-hd .item-hd__box {
		padding-bottom: 30rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
	}

	.view-overflow-hide {
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		line-clamp: 3;
		-webkit-box-orient: vertical;
	}



	.cu-card>.cu-item {
		margin: 0;
	}

	.item-text {
		line-height: 46upx;
	}

	.item-text-sub {
		margin-top: 10upx;
		line-height: 35upx;
	}

	.animation {
		transition-property: all;
		transition-duration: 0.5s;
		transition-timing-function: ease;
	}
</style>
