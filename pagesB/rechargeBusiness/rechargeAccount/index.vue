<!--
  * @desc:客账充值
  * @author:zhangys
  * @date:2023-02-07 10:39:18
!-->
<template>
	<view class="sellPay-container">
		<!-- 互联网账户信息 -->
		<!-- <netAccountInfo :accountInfoData='accountInfoData'> </netAccountInfo> -->
		<!-- 客账用户信息 -->
		<view class="weui-form">
			<view class="weui-cells__title g-flex g-flex-justify g-flex-align-center">
				<view class="">
					客账用户信息
				</view>
				<view class="">
					<view class='cu-tag light ' :class="accountData.amount<0?'bg-red':'bg-olive'">
						{{accountData.amount<0?' 限制通行':'正常'}}
					</view>
				</view>
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">账户名称</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{accountData.custName}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">账户可用余额</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{moneyFilter(accountData.amount)}}元
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">最低预存金标准值</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{moneyFilter(accountData.blackAmount)}}元 <text
								style="margin-left: 10rpx; font-size: 26rpx;">请确保卡内余额大于预存金</text>
						</view>
					</view>
				</view>
				<view class="">
					<view class=" g-flex g-flex-align-center g-flex-center search-vehicle" @click="showVehicleList">
						查看车辆<img style="width: 28rpx;height: 28rpx" src="../../static/vehicleIcon/arrow_icon.png"
							mode=""></img>
					</view>
				</view>

			</view>
		</view>
		<view class="sellPay-Info">
			<view class="c-title g-flex g-flex-align-center g-flex-justify">
				<view class="">
					请选择充值金额
				</view>
				<view style="font-weight: normal;color: #1978ec;" @click="refundVisible">
					充值退款说明 <text style="margin-left: 10rpx;" class="cuIcon-question"></text>
				</view>
			</view>
			<view class="amount-box g-flex g-flex-wrap ">
				<view v-for="(item,index) in amountList" :key="index" class="amount-item"
					@click="selectAmout(item,index)"
					:style="(amountMoneyIndex!=null&&amountMoneyIndex==index)?'border:1px solid #1978ec':''">
					<view class="g-flex g-flex-center g-flex-align-center">
						<view class="amount-text">
							{{item.label}}
						</view>
						<view class="amount-item-label">元</view>
					</view>
				</view>
				<view class="amount-item g-flex g-flex-align-center g-flex-justify" style="width: 60%;padding: 0 20upx;"
					:style="isAnyMount?'border:1px solid #1978ec':''">
					<input type="digit" @focus="clickInput" class="amount-custom-input" placeholder="其他支付金额" name="b"
						v-model="otherMoney" @input="(e)=> handleInput('otherMoney',e)"></input>
					<text style="margin-right: 10upx;">元</text>
				</view>
			</view>

		</view>
		<view class="sellPay-Info">
			<view class="c-title">充值信息</view>
			<form>
				<view class="cu-form-group">
					<view class="title">支付方式:</view>
					<picker style="width:100%;" :value="payTypePartOptions[pay_type_index].value"
						@change="bindPayTypeChange" :range="payTypePartOptions" range-key="label"
						:disabled="accountInfoData.availableAmount&&accountInfoData.availableAmount=='0'">
						<view class="weui-picker-value">{{ payTypePartOptions[pay_type_index].label }}</view>
					</picker>
					<text class="cuIcon-right"></text>
				</view>
				<view class="available-money" v-if='formData.recharge_type == "********"'>
					<view class="available-money-item">账户可用余额：
						<text
							v-if="showMoney">{{moneyFilter(accountInfoData.availableAmount)+'元(余额不足，请选择其他支付方式)'}}</text>
						<text v-else>{{moneyFilter(accountInfoData.availableAmount)+'元'}}</text>

					</view>
				</view>
				<view v-if="formData.recharge_type == '********'">
					<view class="cu-form-group" v-if="cuoponList.length!=0">
						<view class="title">选择优惠券</view>
						<picker @change="selectCoupons" :value="code" :range="cuoponList" range-key='coupon_name'>
							<view style="color: #999999;" class="otherColor" v-if="index==-1">请选择优惠券</view>
							<view style="color: #333;" class="otherColor" v-else>{{cuoponList[index].coupon_name}}
							</view>
						</picker>
						<text class="cuIcon-right"></text>
					</view>
					<view class="" v-else>
						<view class="cu-form-group">
							<view class="title">选择优惠券</view>
							<input value="暂无可使用的优惠券" disabled></input>
							<text class="cuIcon-right"></text>

						</view>
					</view>
					<view class="cuopon-tips">
						注：如有优惠券默认使用，如不使用请勾选取消
					</view>
				</view>

				<view class="" v-else>
					<!-- <view class="cu-form-group">
						<view class="title form_label-require">图片验证码:</view>
						<input placeholder="请输入验证码" class="input" name="smsCode" :value='balancePayment.captchaCode'
							@input="changeInput($event,'captchaCode')"></input>
						<image :src="codeUrl" class="code-img" @click="getCaptcha">

					</view> -->
					<view class="cu-form-group">
						<view class="title form_label-require">短信验证码:</view>
						<input placeholder="请输入短信验证码" class="input" name="smsCode" :value='balancePayment.mobileCode'
							@input="changeInput($event,'mobileCode')"></input>
						<button class="codebtn" :style="codebr?'background:#0066E9':''" :disabled="codebr" @tap="toget">
							<text v-if="codebr">重新发送({{count}})s</text>
							<text v-else>获取验证码</text>
						</button>

					</view>
				</view>

			</form>
		</view>


		<view class="certification">
			<TButton title="去充值" @clickButton="goPay" :isLoadding="isBtnLoader" />
		</view>
		<TModal :showModal='dialogVisible' :showCancelFlag='true' @okModal='onPayHandle'
			@cancelModal='dialogVisible=false' okText='去支付'>
			<form slot='content' class="sellPay-Info">
				<view class="cu-form-group">
					<view class="title">充值用户</view>
					<view class="value">{{accountData.custName}}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">充值金额</view>
					<view class="value">{{formData.money}}元</view>
				</view>
				<view class="cu-form-group" v-if="formData.coupon_code&&formData.recharge_type=='********'">
					<view class="title">赠送金额</view>
					<view class="value">{{moneyFilter(coupon_amount)}}元</view>
				</view>
			</form>
		</TModal>

		<TModal :showModal='isShowVehicleList' :showCancelFlag='true' @okModal='isShowVehicleList=false'
			@cancelModal='isShowVehicleList=false'>
			<form slot='content' class="sellPay-Info">
				<view class="" style="max-height: 400rpx;overflow-y: scroll;">
					<view class="bind_vehilce g-flex g-flex-align-center g-flex-justify"
						v-for="(item,index) in accountData.bindVehicleList" :key="index">
						<view class="g-flex g-flex-align-center">
							<img v-if="item.vehicleType =='2'" style="width: 60rpx;height: 60rpx;"
								src="../../static/vehicleIcon/mini_car_icon.png" alt="">
							<img v-if="item.vehicleType =='1'" style="width: 60rpx;height: 60rpx;"
								src="../../static/vehicleIcon/mini_truck_icon.png" alt="">
							<img v-if="item.vehicleType =='3'" style="width: 60rpx;height: 60rpx;"
								src="../../static/vehicleIcon/mini_priatecar_icon.png" alt="">
							<view class="bind_vehicle_info g-flex g-flex-column">
								<view class="bind_vehicle_code">{{getVehicleType(item.vehicleType)}}车
									{{item.carNo}}【{{getVehicleColor(item.carColor)}}】
								</view>
								<view style="font-size: 26rpx;">最低预存金标准值：{{moneyFilter(item.blackAmount)}}元</view>
							</view>
						</view>
						<view class="">
							<view class='cu-tag light ' :class="item.cardStatus=='1'?'bg-olive':'bg-red'">
								{{item.cardStatus=='1'?'正常':'限制通行'}}
							</view>
						</view>
					</view>
				</view>
			</form>
		</TModal>
		<t-captcha id="captcha" app-id="191430362" @verify="handlerVerify" @ready="handlerReady" @close="handlerClose"
			@error="handlerError" />
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import TModal from '@/components/t-modal/t-modal.vue'
	import {
		payTypeOptions,
		amountList,
		payTypePartOptions
	} from '@/common/const/optionData.js'
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		getCoupons
	} from '@/common/method/filter.js';

	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,
		getLoginUserInfo,
		getEtcAccountInfo,
		setOpenid
	} from "@/common/storageUtil.js";
	import {
		twoDecimal,
		moneyFilter
	} from '@/common/util.js'
	import float from '@/common/method/float.js'
	import netAccountInfo from '@/pagesB/components/netAccountInfo/index.vue'
	export default {
		components: {
			TButton,
			TModal,
			tLoading,
			netAccountInfo
		},
		data() {
			return {
				payTypePartOptions,
				moneyFilter,
				isLoading: false,
				isBtnLoader: false,
				isRecycleVisible: false,
				dialogVisible: false,
				cuoponList: [],
				cardAmount: {},
				formData: {
					"recharge_type": "********", //充值方式
					"account_id": "", //客账id
					"money": '', //充值金额
					"source": "3", //充值来源
					"open_id": "",
					"tradeType": 'CLIENT_RECHARGE', //场景
					"coupon_code": '',
					"agent_user": '', //用户编号

				},
				code: "",
				index: -1,
				vehicleInfo: {},
				amountList,
				amountMoneyIndex: null,
				otherMoney: '',
				isAnyMount: false,
				pay_type_index: 0,
				balancePayment: {
					captchaCode: "", // 图形验证码code
					mobileCode: "", // 短信验证码
					mobile: "",
					userNo: '',
					customer_id: ''
				},
				codeUrl: '', //图形验证码url
				timer: null,
				count: 60,
				codebr: false,
				codeid: '', //图形验证码ID，
				accountInfoData: {},
				isShowVehicleList: false,
				accountData: {},
				coupon_amount: '', //优惠券赠送金额
				codeTicket: '', //腾讯验证码

			};
		},
		computed: {
			showMoney() {
				if (this.formData.money) {
					return float.mul(this.formData.money, 100) > float.mul(this.accountInfoData.availableAmount, 1)
				}
			}
		},
		onLoad(obj) {
			console.log(obj, 'obj');
			for (let key in obj) {
				this.accountData[key] = obj[key]
			}
			if (!this.accountData.bindVehicleList || this.accountData.bindVehicleList.length == 0) {
				this.accountData.bindVehicleList = []
			} else {
				this.accountData.bindVehicleList = JSON.parse(this.accountData.bindVehicleList)
			}
			this.formData.account_id = this.accountData.clientId
		},
		created() {
			this.formData.agent_user = getLoginUserInfo().userNo
			this.balancePayment.mobile = getEtcAccountInfo().mobile
			this.formData.open_id = getOpenid() || ''
			this.getAccountInfo()
			this.getCouponList()
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
		},
		methods: {
			getVehicleType,
			getVehicleColor,
			// 验证码验证结果回调
			handlerVerify(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
				if (ev.detail.ret === 0) {
					// 验证成功
					this.codeTicket = ev.detail.ticket
					console.log('ticket:', ev.detail.ticket)
					this.getverification()
				} else {
					// 验证失败
					// 请不要在验证失败中调用refresh，验证码内部会进行相应处理
				}
			},
			// 验证码准备就绪
			handlerReady() {
				console.log('验证码准备就绪')
			},
			// 验证码弹框准备关闭
			handlerClose(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
				if (ev && ev.detail.ret && ev.detail.ret === 2) {
					console.log('点击了关闭按钮，验证码弹框准备关闭');
				} else {
					console.log('验证完成，验证码弹框准备关闭');
				}
			},
			// 验证码出错
			handlerError(ev) {
				console.log(ev.detail.errMsg)
			},
			showVehicleList() {
				this.isShowVehicleList = true
			},
			//以防丢失openID，再获取一下，充值要用
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.formData.open_id = res.data.openid
									setOpenid(res.data.openid)

								}

							}
						})
					}
				})
			},
			//选择金额
			selectAmout(item, index) {
				this.otherMoney = ''
				this.formData.money = ''
				this.amountMoneyIndex = index
				this.formData.money = item.value
				this.isAnyMount = false
			},
			//输入框聚焦事件
			clickInput() {
				this.amountMoneyIndex = null
				if (!this.isAnyMount) {
					this.formData.money = ''
				}
				this.isAnyMount = true
			},
			//计算优惠金额
			couponValue() {
				let params = {
					couponCode: this.formData.coupon_code,
					amount: this.formData.money
				}
				params.amount = params.amount * 100
				this.$request
					.post(this.$interfaces.couponValue, {
						data: params,
					}).then(res => {
						this.coupon_amount = res.data
						console.log(res, '优惠金额');
					})
			},
			//支付方式选择
			bindPayTypeChange(e) {
				this.pay_type_index = e.detail.value;
				this.formData.recharge_type = this.payTypePartOptions[e.detail.value].value || '';
				console.log(this.formData.recharge_type);
				this.balancePayment.mobileCode = ''
				if (this.formData.recharge_type == "********") {
					this.getCaptcha()
					this.index = -1
					this.formData.coupon_code = ''
				}
			},
			changeInput(event, data) {
				this.balancePayment[data] = event.detail.value;
			},
			//获取图形验证码
			getCaptcha() {
				this.balancePayment.captchaCode = '',
					this.$request
					.post(this.$interfaces.getCaptcha).then(res => {
						if (res.code == 200) {
							this.codeUrl = res.data.image
							this.codeid = res.data.captchaId
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
					})
			},
			toget() {
				// if (this.validateCodeImg()) {
				// 	this.getverification()
				// }
				this.selectComponent('#captcha').show()
			},
			// validateCodeImg() {
			// 	if (!this.balancePayment.captchaCode) {
			// 		uni.showModal({
			// 			title: "提示",
			// 			content: "请输入图形证码",
			// 			showCancel: false,
			// 		});
			// 		return false;
			// 	}
			// 	return true;
			// },
			//获取短息验证码
			getverification() {
				this.codebr = true
				let params = {
					// mobileCode: this.balancePayment.captchaCode,
					// captchaId: this.codeid,
					ticket: this.codeTicket,
					mobile: this.accountInfoData.mobile
				}
				//手机号取不到就取userNo
				if (!params.mobile) {
					params.userNo = this.accountInfoData.userNo
				}
				this.$request
					.post(this.$interfaces.sendaAccountSms, {
						data: params,
					}).then(res => {
						if (res.code === 200) {
							this.timer = setInterval(() => {
								if (this.count > 0 && this.count <= 60) {
									this.count--;
								} else {
									this.codebr = false;
									clearInterval(this.timer);
									this.timer = null;
									this.count = 60;
								}
							}, 1000);
						} else {
							this.codebr = false
							let that = this
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
								success: function(res) {
									if (res.confirm) {
										that.getCaptcha()
									}
								}
							});

						}
					}).catch((error) => {
						this.codebr = false
						uni.showModal({
							title: "提示",
							content: "获取失败",
							showCancel: false,
						});
					});
			},
			//获取互联网账户信息
			getAccountInfo() {
				let params = {
					userNo: getLoginUserInfo().userNo,
				};
				this.isLoading = true
				this.$request.post(this.$interfaces.getAccountInfo, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.isLoading = false
						this.accountInfoData = res.data
						if (this.accountInfoData.availableAmount && this.accountInfoData.availableAmount !== '0') {
							this.formData.recharge_type = "********"
							this.getCaptcha()
							this.pay_type_index = 1
						}
						if (this.accountInfoData.status === "UNACTIVE") {
							this.isActivition();
						} else if (res.data.status === "ACTIVE") {

						}
					} else {
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
				})
			},
			//提示激活
			isActivition() {
				let _self = this;
				uni.showModal({
					title: '提示',
					content: '您当前账户未激活，是否激活该账户？激活后可使用账户余额支付',
					success: function(res) {
						if (res.confirm) {
							_self.activitionAccount();
						}
					}
				});
			},
			//激活
			activitionAccount() {
				let params = {
					userNo: getLoginUserInfo().userNo,
				};
				this.$request.post(this.$interfaces.activitionAccount, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.getAccountInfo()
						uni.showModal({
							title: "提示",
							content: '激活账户成功',
							showCancel: false,
						});
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},

			coupons(val) {
				return getCoupons(val) || ''
			},
			//选择优惠券
			selectCoupons(e) {
				this.index = e.detail.value
				this.formData.coupon_code = this.cuoponList[this.index].code
				this.code = this.cuoponList[this.index].code
			},

			//获取优惠券列表
			getCouponList() {
				let params = {
					customer_id: getEtcAccountInfo().custMastId
				};
				this.$request
					.post(this.$interfaces.getCouponList, {
						data: params
					})
					.then(res => {
						if (res.code == 200) {
							//根据条件过滤出优惠券
							this.cuoponList = []
							this.cuoponList = res.data.filter((item) => {
								if (item.is_valid == 0 && item.is_used == 0 && item.type == 3) {
									return item
								}
							})
							if (this.cuoponList.length != 0) {
								let arr = [{
									code: '',
									coupon_name: '不选择优惠券'
								}]
								this.cuoponList = [...arr, ...this.cuoponList]
								this.formData.coupon_code = this.cuoponList[1].code
								this.index = 1
								this.code = this.cuoponList[this.index].code
							}

							console.log(this.cuoponList, '----');
						}

					});
			},

			// 监听输入框,匹配对应的键值对
			handleInput(type, event) {
				if (type == 'otherMoney') {
					this.formData.money = event.target.value
				} else {
					this.formData[type] = event.target.value
				}
			},
			refundVisible() {
				uni.showModal({
					title: "充值退款说明",
					content: '充值后未圈存、未使用的,可在24小时内到网点或联系客服申请原路退款。客服热线:0771-5896333',
					showCancel: false,
				});
			},
			onPayHandle() {
				if (this.formData.recharge_type == '********') {
					this.loadRecharge();
					return
				}
				this.balanceRecharge()
			},
			validate() {
				if (!this.formData.money) {
					uni.showModal({
						title: "提示",
						content: "请输入充值金额",
						showCancel: false,
					});
					return false;
				}
				if (!twoDecimal(this.formData.money)) {
					uni.showModal({
						title: "提示",
						content: "充值金额必须大于零并最多保留小数点后两位！",
						showCancel: false,
					});
					return false;
				}
				// if (float.mul(this.formData.money, 1) > 10000) {
				// 	uni.showModal({
				// 		title: "提示",
				// 		content: "单笔充值金额不得大于1万！",
				// 		showCancel: false,
				// 	});
				// 	return false;
				// }

				return true;
			},
			goPay() {
				if (!this.validate()) return
				if (this.formData.recharge_type == '********' && this.showMoney) {
					uni.showModal({
						title: "提示",
						content: "余额不足，请选择其他支付方式",
						showCancel: false,
					});
					return
				}
				if (!this.balancePayment.mobileCode && this.formData.recharge_type == '********') {
					uni.showModal({
						title: "提示",
						content: "请输入短信验证码",
						showCancel: false,
					});
					return
				}
				console.log(this.formData);
				if (this.formData.coupon_code && this.formData.recharge_type == '********') {
					this.couponValue()
				}
				this.dialogVisible = true;
			},
			//余额支付
			balanceRecharge() {
				let params = {
					user_no: this.formData.agent_user,
					recharge_type: this.formData.recharge_type,
					custmoter_id: getEtcAccountInfo().custMastId,
					money: this.formData.money,
					mobile: this.accountInfoData.mobile,
					mobile_code: this.balancePayment.mobileCode,
					businessType: '1'
				}
				params.money = float.mul(params.money, 100)
				this.isLoading = true;
				this.dialogVisible = false;
				let data = {
					routePath: this.$interfaces.netAccountRecharge.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						this.balancePayment.mobileCode = ''
						this.balancePayment.captchaCode = ''
						this.formData.money = ''
						this.otherMoney = ''
						this.amountMoneyIndex = null
						this.index = -1
						this.getCaptcha()
						this.getVehicleList()
						this.getAccountInfo()
						let statusVal = {
							1: "充值中",
							2: "充值成功",
							3: "充值失败",
						};
						let msg = statusVal[res.data.status]

						uni.showModal({
							title: "提示",
							content: msg + '！（充值到账可能会有延迟）是否查看充值记录',
							confirmText: '查看',
							success: (res) => {
								if (res.confirm) {
									let enParams = JSON.parse(JSON.stringify(this.accountData))
									enParams.bindVehicleList = enParams.bindVehicleList ? JSON
										.stringify(enParams.bindVehicleList) : []
									uni.navigateTo({
										url: '/pagesB/rechargeBusiness/rechargeAccountList/index?' +
											this.objToUrlParam(enParams)
									})
								} else {
									uni.reLaunch({
										url: "/pagesB/rechargeBusiness/selectVehicle/index"
									})
								}
							}

						});
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						this.$logger.info('客账余额支付请求参数：', JSON.stringify(params))
						this.$logger.error('客账余额支付返回结果：', JSON.stringify(res))
					}
				})
			},
			//微信充值
			loadRecharge() {
				let _self = this;
				this.isLoading = true;
				let params = JSON.parse(JSON.stringify(this.formData))
				this.dialogVisible = false;
				params.money = float.mul(params.money, 100)
				if (params.coupon_code && params.recharge_type == '********') {
					params.coupon_amount = this.coupon_amount
				}
				console.log(params, '充值入参');
				let data = {
					routePath: this.$interfaces.accountRecharge.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let data = res.data;
						let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};
						//拉起微信支付
						wx.requestPayment({
							...payMessage,
							"success": function(res) {
								console.log(res);
								_self.isLoading = true;
								//异步调用，确保能查到状态
								setTimeout(() => {
									_self.loadRechargeQuery(data);
								}, 6000)
								/* uni.showModal({
									title: '提示',
									content: '是否完成支付',
									confirmText: '支付完成',
									success: function(res) {
										if (res.confirm) {
											
											

										}
									}
								}); */

							},
							"fail": function(res) {
								let msg = res.errMsg || ''
								if (!msg) return
								uni.showModal({
									title: '提示',
									content: msg
								})
							},
							"complete": function(res) {}
						})
					} else {

						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						this.$logger.info('客账充值请求参数：', JSON.stringify(params))
						this.$logger.error('客账充值返回结果：', JSON.stringify(res))
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//查询支付状态
			loadRechargeQuery(data) {
				let params = {
					routePath: this.$interfaces.accountRechargeSearch.method,
					bizContent: {
						order_Id: data.order_id
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					this.getCouponList()
					this.getVehicleList()
					this.formData.money = ''
					this.otherMoney = ''
					this.amountMoneyIndex = null
					this.index = -1
					this.formData.coupon_code = ''
					this.code = ''
					let status = res.data.status;
					let statusVal = {
						1: "充值中",
						2: "充值成功",
						3: "充值失败",
					};
					let msg = statusVal[status] || "充值失败";
					uni.showModal({
						title: "提示",
						content: msg + '！（充值到账可能会有延迟）是否查看充值记录',
						confirmText: '查看',
						success: (res) => {
							if (res.confirm) {
								let enParams = JSON.parse(JSON.stringify(this.accountData))
								enParams.bindVehicleList = enParams.bindVehicleList ? JSON.stringify(
									enParams.bindVehicleList) : []
								uni.navigateTo({
									url: '/pagesB/rechargeBusiness/rechargeAccountList/index?' +
										this.objToUrlParam(enParams)
								})
							} else {
								uni.redirectTo({
									url: '/pages/home/<USER>/p-home'
								})
							}
						}

					});

				})
			},
			//重新获取下客账信息，防止界面数据不刷新
			getVehicleList() {
				let params = {
					customerId: getAccountId()
				}
				this.$request
					.post(this.$interfaces.rechargeVehicleList, {
						data: params
					})
					.then((res) => {
						console.log(res, '车辆列表');
						if (res.code == 200) {
							this.accountData = null
							if (res.data.clientAccount) {
								this.accountData = JSON.parse(JSON.stringify(res.data.clientAccount))
							}

						} else {}
					})
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},
		}
	};
</script>
<style lang="scss">
	.sellPay-Info {
		background-color: #FFFFFF;

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}

		.amount-box {

			padding: 20rpx;

			.amount-item {
				margin: 10upx 20upx;
				width: 27%;
				height: 110upx;
				background-color: #f8f9fe;
				border-radius: 10px;
				position: relative;

				.amount-text {
					line-height: 100upx;
					color: #1978ec;
					font-size: 34upx;
					font-weight: bold;
				}

				.amount-item-label {
					color: #1978ec;
					font-size: 26upx;
				}


			}
		}



	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.sellPay-Info .cu-form-group radio-group {

		flex: 1;
		text-align: left;
	}

	.code-img {
		width: 210upx;
		height: 90upx;
		margin-right: 10upx;
	}

	.codebtn {
		background: #0066E9;
		min-width: 210upx;
		font-size: 24upx;
		height: 70upx;
		margin-right: 10upx;

	}

	.codebtn text {
		display: inline-block;
		color: #fff;
		line-height: 70upx;
	}


	.bind_vehilce {
		border-bottom: 1px solid #E9E9E9;
		padding: 20rpx;
		margin: 0 10rpx;

		.bind_vehicle_info {
			margin-left: 40rpx;

			.bind_vehicle_code {
				font-weight: bold;
				margin-bottom: 20rpx;
			}
		}
	}

	.cuopon-tips {
		margin: 0 0 10rpx 30rpx;
		padding-bottom: 10rpx;
		font-size: 26rpx;
		color: #e94126;
	}

	.available-money {
		padding: 6rpx 0 6rpx 30rpx;

		.available-money-item {
			color: #adadad;
			font-size: 26rpx;
		}

	}

	.search-vehicle {
		font-size: 26rpx;
		color: #999999;
		margin: 10rpx 0;
	}

	.weui-cells__title {
		font-size: 30rpx;
		font-weight: bold;
	}

	.weui-cell {
		padding: 20rpx 30rpx;
	}
</style>