<!--
  * @desc:客账充值记录
  * @author:zhangys
  * @date:2023-02-16 09:53:39
!-->
<template>
	<view class="sellPay-container">
		<!-- 客账用户信息 -->
		<scroll-view :style="'height:'+(windowHeight-10)+'px;'" :scroll-top="scrollTop" scroll-y="true"
			class="scroll-Y" :lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower"
			@scroll="scroll">
		<view class="user_info g-flex g-flex-column">
			<view class="info_title g-flex g-flex-justify">
				<view class="title">
					客账用户信息
				</view>
				<view class="">
					<view class='cu-tag light ' :class="accountData.amount<0?'bg-red':'bg-olive'">
						{{accountData.amount<0?' 限制通行':'正常'}}
					</view>
				</view>
			</view>
			<view class="info_item g-flex g-flex-justify">
				<view class="item_label">
					账户名称：
				</view>
				<view class="item_value">
					{{accountData.custName}}
				</view>
			</view>
			<view class="info_item g-flex g-flex-justify">
				<view class="item_label">
					账户可用余额：
				</view>
				<view class="item_value">
					{{moneyFilter(accountData.amount)}}元
				</view>
			</view>
			<view class="info_item g-flex g-flex-justify">
				<view class="item_label">
					最低预存金标准值：
				</view>
				<view class="item_value">
					{{moneyFilter(accountData.blackAmount)}}元 <text
						style="margin-left: 10rpx; font-size: 26rpx;">请确保卡内余额大于预存金</text>
				</view>
			</view>

		</view>
			
			<item v-for="(item,index) in rechargeList" :itemInfo='item' :key="index" @getRechargeList='accountRechargeList'></item>
		</scroll-view>
		<load-more :loadStatus="noticeLoadStatus" />
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import TModal from '@/components/t-modal/t-modal.vue'
	import loadMore from '../../components/load-more/index.vue';
	import {
		payTypePartOptions
	} from '@/common/const/optionData.js'
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		getCoupons
	} from '@/common/method/filter.js';

	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,
		getLoginUserInfo,
		getEtcAccountInfo,
		setOpenid
	} from "@/common/storageUtil.js";
	import {
		twoDecimal,
		moneyFilter
	} from '@/common/util.js'
	import float from '@/common/method/float.js'
	import item from './item'
	export default {
		components: {
			TButton,
			TModal,
			tLoading,
			item,
			loadMore
		},
		data() {
			return {
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				payTypePartOptions,
				moneyFilter,
				isLoading: false,
				dialogVisible: false,
				formData: {
					accountId:"",
					businessType:'1002,1003,1008,1000'
				},
				pageNum:1,
				pageSize:10,
				accountInfoData: {},
				isShowVehicleList: false,
				accountData: {},
				rechargeList:[],
				flag:false
			};
		},
		onLoad(obj) {
			console.log(obj, 'obj');
			if(Object.keys(obj).length!=0){
				for (let key in obj) {
					this.accountData[key] = obj[key]
				}
				if(!this.accountData.bindVehicleList||this.accountData.bindVehicleList.length==0){
					this.accountData.bindVehicleList =[]
				}else{
					this.accountData.bindVehicleList = JSON.parse(this.accountData.bindVehicleList)
				}
				this.formData.accountId = this.accountData.clientId
				this.accountRechargeList()
			}
			
		},
		created() {
		},
		methods: {
			upper: function(e) {
			},
			scrolltolower: function(e) {
				console.log('1111111');
				if (this.flag) return;
				let self = this;
				setTimeout(function() {
					self.pageNum = self.pageNum + 1;
					self.accountRechargeList();
				}, 500)
		
			},
			scroll: function(e) {
		
				this.old.scrollTop = e.detail.scrollTop;
		
			},
			accountRechargeList() {
				this.noticeLoadStatus = 1;
				let enData=JSON.parse(JSON.stringify(this.formData))
				let params = {
					routePath: this.$interfaces.accountRechargeList.method,
					bizContent: {
						...enData,
						pageNum: this.pageNum,
						pageSize: this.pageSize
					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
						console.log(res,'-----=');
					if (res.code == 200) {
						
						let result = res.data.records || []
						if (res.data.records.length) {
							this.rechargeList = this.rechargeList.concat(result)
						} else {
							this.noticeLoadStatus = 3;
							this.flag = true
						}
						if (this.rechargeList.length == res.data.total) {
							this.noticeLoadStatus = 3
							this.flag = true
						}
					} else {
						this.noticeLoadStatus = 2;
					}
				}).catch((err) => {
		
					this.noticeLoadStatus = 2;
				})
			},
			
		},
		destroyed() {
		
		}
	};
</script>
<style lang="scss">
	.sellPay-Info {
		background-color: #FFFFFF;

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}

		.amount-box {

			padding: 20rpx;

			.amount-item {
				margin: 10upx 20upx;
				width: 27%;
				height: 110upx;
				background-color: #f8f9fe;
				border-radius: 10px;
				position: relative;

				.amount-text {
					line-height: 100upx;
					color: #1978ec;
					font-size: 34upx;
					font-weight: bold;
				}

				.amount-item-label {
					color: #1978ec;
					font-size: 26upx;
				}


			}
		}



	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.sellPay-Info .cu-form-group radio-group {

		flex: 1;
		text-align: left;
	}

	.code-img {
		width: 210upx;
		height: 90upx;
		margin-right: 10upx;
	}

	.codebtn {
		background: #0066E9;
		width: 210upx;
		font-size: 24upx;
		height: 70upx;
		margin-right: 10upx;
	}

	.codebtn span {
		display: inline-block;
		color: #fff;
		line-height: 70upx;
	}

	// .vehicle_item{
	// 	padding: 20rpx;
	// 			border: 1px solid #e9e9e9;
	// 			border-radius: 12rpx;
	// 			margin: 20rpx;
	// 			box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
	// }
	.user_info {
		padding: 20rpx 40rpx;
		background-color: #fff;

		.info_title {
			margin-bottom: 8rpx;

			.title {
				color: #333333;
				font-weight: bold;
				font-size: 28rpx
			}
		}

		.info_item {
			margin: 10rpx 0;
			font-size: 28rpx;

			.item_label {
				color: #C6C6C6;

				&>img {
					width: 28rpx;
					height: 28rpx;
				}
			}

			.item_value {}
		}
	}

	.bind_vehilce {
		border-bottom: 1px solid #E9E9E9;
		padding: 20rpx;
		margin: 0 10rpx;

		.bind_vehicle_info {
			margin-left: 40rpx;

			.bind_vehicle_code {
				font-weight: bold;
				margin-bottom: 20rpx;
			}
		}
	}
	.cuopon-tips{
		margin: 0 0  10rpx 30rpx;
		padding-bottom: 10rpx;
		font-size: 26rpx;
		color: #e94126;
	}
</style>
