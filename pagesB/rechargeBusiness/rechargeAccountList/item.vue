<template>
	<view class="cu-card article margin-top bg-white order-item">
		<view class="cu-item padding-top item-hd" style="padding-bottom: 0px;">
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text
						class="text-bold">充值金额：{{moneyFilter(itemInfo.amount)}}元</text></view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>充值方式：</text>{{itemInfo.businessName}}
				</view>

			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>充值状态：</text>{{getPayStatus(itemInfo.status)}}
				</view>
			</view>

			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>订单编号：</text>{{itemInfo.id}}</view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>充值时间：</text>{{itemInfo.createTime}}</view>
			</view>
			<view class="g-flex" v-if="itemInfo.refoundStatus!=null">
				<view class="item-text padding-left g-flex-item">
					<text>退款状态：</text>{{returnStatusFilter(itemInfo.refoundStatus)}}</view>
			</view>
			<view class="g-flex" v-if="itemInfo.refoundStatus=='4'">
				<view class="item-text padding-left g-flex-item"><text>退款时间：</text>{{itemInfo.refoundTime}}</view>
			</view>

		</view>
		<view class="padding-lr  order-item-btn">
			<view class="btn-item">
				<button class="weui-btn_mini weui-btn_primary" 
				v-if="itemInfo.businessType=='1000'"
					:disabled="!isDisableRefund" :class="[!isDisableRefund ? 'weui-btn_disabled' :'']"
					@click="refundHandle(itemInfo)">退款</button>
			</view>
		</view>
		<tLoading :isShow="isLoading" />

	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getPayStatus,
		returnStatusFilter
	} from '@/common/method/filter'
	import {
		twoDecimal,
		moneyFilter
	} from '@/common/util.js'
		var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		props: {
			itemInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {
				isLoading: false,
			}
		},
		watch: {

		},
		components: {
			tLoading
		},
		computed: {
           isDisableRefund() {
				let endTime = dayjs(this.itemInfo.createTime).add(24, 'hour').format('YYYY-MM-DD HH:mm:ss');
				let time = dayjs().isBefore(dayjs(endTime))
				return this.itemInfo.status=='2' && this.itemInfo.refoundStatus==null
			}
		},
		created() {

		},
		methods: {
			moneyFilter,
			getPayStatus,
			returnStatusFilter,
			refundHandle(val) {
				uni.showModal({
					title: "提示",
					content: '是否确认退款？',
					confirmText: '确定',
					success: (res) => {
						if (res.confirm) {
							this.onRepayHandle(val)
						}
					}
				})
			},
			onRepayHandle(val) {
				let data = {
					routePath: this.$interfaces.balanPayClientRefund.method,
					bizContent: {
						orderId: val.netId
					}
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						this.isLoading = false
						this.$emit('getRechargeList')
						uni.showModal({
							title: "提示",
							content: "退款成功",
							showCancel: false,
							success: function(res) {
								if (res) {
									uni.redirectTo({
										url: '/pages/home/<USER>/p-home'
									})
								}
							}
						});
					} else {
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,

						});
					}

				}).catch(err => {
					this.isLoading = false
				})
			},
		}
	}
</script>
<style lang="scss" scoped>
	.order-item-btn {
		display: flex;
		justify-content: flex-end;
		margin-bottom: 20rpx;
	
		.color-btn {
			padding: 10upx 0;
			width: 100%;
		}
	
		.btn-item {
			min-width: 140rpx;
		}
	
	}

	.case-item {
		border-radius: 10upx;
	}

	.case-item .item-hd .item-hd__box {
		padding-bottom: 30rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
	}

	.view-overflow-hide {
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		line-clamp: 3;
		-webkit-box-orient: vertical;
	}



	.cu-card>.cu-item {
		margin: 0;
	}

	.item-text {
		line-height: 46upx;
	}

	.item-text-sub {
		margin-top: 10upx;
		line-height: 35upx;
	}

	.animation {
		transition-property: all;
		transition-duration: 0.5s;
		transition-timing-function: ease;
	}

	.refundBtn {
		float: right;
		// margin-bottom: 18upx;
		margin-right: 30upx;
	}
</style>
