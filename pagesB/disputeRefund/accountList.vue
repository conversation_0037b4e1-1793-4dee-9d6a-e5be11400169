<!--
  * @desc:退款账户用户信息-列表
  * @author:zhangys
  * @date:2023-01-12 11:09:49
!-->
<template>
	<view class="account-list">
		<view class="content">
			<view class="content-title g-flex g-flex-align-center g-flex-justify">
				<view class="content-title-decoration">
					收款银行账户
				</view>
				<!-- 集装箱退费新增判断routeType -->
				<view v-if="accountList.length!=0 && !routeType">
					<view class="operator" @click="operation=!operation">{{operation?'完成':'管理'}}</view>
				</view>
			</view>

			<view class="card g-flex g-flex-align-center g-flex-justify" v-for="(item,index) in accountList"
				:key="index">
				<view class="card-bd">
					<view class="card-bd-item ">
						<text class="label">账户名称:</text>
						<text class="value">{{item.bankAccount}}</text>
					</view>
					<view class="card-bd-item">
						<text class="label">开户银行:</text>
						<text class="value">{{item.bankName}}</text>
					</view>
					<view class="card-bd-item">
						<text class="label">收款账号:</text>
						<text class="value">{{item.bankNo}}</text>
					</view>
					<view class="card-bd-item">
						<text class="label">关联车辆:</text>
						<text class="value">{{item.carNos}}</text>
					</view>
					<view class="card-bd-item">
						<text class="label">创建时间:</text>
						<text class="value">{{item.effectiveTime}}</text>
					</view>
					<view class="card-bd-item">
						<text class="label">失效时间:</text>
						<text class="value">{{item.failureTime}} ({{auditStatusFilter(item.auditStatus)}})</text>
					</view>
					<view class="card-bd-item g-flex" v-if="item.attorneyUrl">
						<text class="label">授权材料:</text>
						<view class="attorney-img" @tap="ViewImage(item)"> <img :src="item.attorneyUrl" alt=""></view>
					</view>
					<view class="delete" v-if="!routeType">
						<!--集装箱退费新增判断routeType -->
						<text v-if="operation" class="operator-class" @click="deleteAccountComfirm(item)">删除</text>
						<text v-else class='cuIcon-edit operator-class' style="font-size: 40rpx;"
							@click="showSmsDialog('edit',item)"></text>
					</view>
					<view class="delete" v-if="routeType">
						<text class="operator-class" @click="selectAccount(item)">选择</text>
					</view>
				</view>
			</view>
		</view>

		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="showSmsDialog('add')">
						新增收款银行账户
					</button>
				</view>

			</view>
		</view>


		<TModal :showModal='smsDialogVisible' @cancelModal='smsDialogVisible=false' @okModal='validateSms' okText='提交'>
			<form slot='content' class="bind-Info">

				<view class="des">为了验证您的身份，我们将向手机{{etcAccountInfo.custMobile}}发送短信验证码</view>
				<view class="cu-form-group login-form-group">
					<input placeholder="请输入图形验证码" name="mobileImgCode" v-model='mobileImgCode'></input>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">
				</view>
				<view class="cu-form-group">
					<input placeholder="请输入验证码" v-model="formData.mobileCode" name="mobileCode"></input>
					<button class="codebtn" :style="codebr?'background:#0066E9':''" :disabled="codebr" @tap="sendSMS">
						<span v-if="codebr">重新发送({{count}})s</span>
						<span v-else>发送验证码</span>
					</button>
				</view>
			</form>
		</TModal>

		<neil-modal title="温馨提示" class="modal" :show="isShowPass" @confirm="passConfirm" @cancel="passCancel"
			cancelText="新增其他车辆/账户信息" confirmText="继续使用已有账户">
			<template v-slot:close>
				<image @click="isShowPass = false" style="width: 44rpx;height: 44rpx" src="../../static/etc/close.png"
					mode=""></image>
			</template>
			<view class="dialog">
				{{passTipsText}}
			</view>
		</neil-modal>
	</view>
</template>

<script>
	import {
		getTokenId,
		getAccountId,
		setEtcVehicle,
		getEtcAccountInfo,
	} from '@/common/storageUtil.js';
	import TModal from '@/components/t-modal/t-modal.vue'
	import neilModal from '@/components/neil-modal/neil-modal.vue'
	export default {
		components: {
			TModal,
			neilModal
		},
		data() {
			return {
				isShowPass: false,
				smsDialogVisible: false,
				etcAccountInfo: {},
				timer: null,
				count: 60,
				codebr: false,
				mobileImgCode: '',
				codeUrl: '',
				captchaId: '',
				formData: {
					mobileCode: ''
				},
				smsType: '',
				operation: false,
				accountList: [],
				selectItem: {},
				statusOption: [{
						label: '待审核',
						value: '0',
					},
					{
						label: '生效中',
						value: '1',
					},
					{
						label: '审核不通过',
						value: '2',
					},
					{
						label: '生效中',
						value: '3',
					},
				],
				routeType: '', //注销、集装箱退费路由关联
				applyId: '', //集装箱退费id，
				// custName: '', //注销ETC申请人
				passTipsText: '',
				ids: null
			}
		},
		onLoad(option) {
			if (option && option.type) {
				this.routeType = option.type
				this.applyId = option.applyId
			}
			if (option.custName) {
				this.custName = option.custName
			}
		},
		created() {
			this.etcAccountInfo = getEtcAccountInfo() ? getEtcAccountInfo() : {}
			this.getAccountList()
			this.getAccountPass()
		},
		methods: {
			//集装箱退费选择账户
			selectAccount(item) {
				if (this.routeType == 'containerRefund') {
					//集装箱选择账户
					if (item.auditStatus == '0') {
						uni.showModal({
							title: '提示',
							content: '状态还在审核中，无法选择，请返回填写退款信息，或等待审核通过后再选择。',
							success: res => {
								if (res.confirm) {
									uni.redirectTo({
										url: '/pagesB/containerRefund/car-apply/refund-apply?accountId=' +
											this.applyId
									})
								}
							}

						})
						return
					}
					if (item.auditStatus == '2') {
						uni.showModal({
							title: '提示',
							content: '账户审核未通过，无法选择，请返回填写退款信息。',
							success: res => {
								if (res.confirm) {
									uni.redirectTo({
										url: '/pagesB/containerRefund/car-apply/refund-apply?accountId=' +
											this.applyId
									})
								}
							}
						})
						return
					}

					uni.redirectTo({
						url: '/pagesB/containerRefund/car-apply/refund-apply?accountItem=' + encodeURIComponent(
								JSON
								.stringify(item)) + '&accountId=' +
							this.applyId
					})
				}

				// 线上注销需求不兼容，暂时废弃，过后要优化
				// else if (this.routeType == 'onlineLogout') {
				// 	if (item.auditStatus == '0') {
				// 		uni.showModal({
				// 			title: '提示',
				// 			content: '状态还在审核中，无法选择，请返回填写银行收款信息，或等待审核通过后再选择。',
				// 			success: res => {
				// 				if (res.confirm) {
				// 					// uni.redirectTo({
				// 					// 	url: '/pagesC/logoutBussiness/apply/apply'
				// 					// })
				// 					uni.navigateBack()
				// 				}
				// 			}

				// 		})
				// 		return
				// 	}
				// 	if (item.auditStatus == '2') {
				// 		uni.showModal({
				// 			title: '提示',
				// 			content: '账户审核未通过，无法选择，请返回填写银行收款信息。',
				// 			success: res => {
				// 				if (res.confirm) {
				// 					//添加监听事件选择
				// 					// uni.$emit('setAccountItem', bankAccountItem)
				// 					uni.navigateBack()
				// 				}
				// 			}
				// 		})
				// 		return
				// 	}
				// 	if (this.custName != item.bankAccount) {
				// 		uni.showModal({
				// 			title: '提示',
				// 			content: '注销申请ETC账户与所选银行账户名称不一致，无法选择，请返回填写银行收款信息。',
				// 			success: res => {
				// 				if (res.confirm) {
				// 					//添加监听事件选择
				// 					// uni.$emit('setAccountItem', bankAccountItem)
				// 					uni.navigateBack()
				// 				}
				// 			}
				// 		})
				// 		return
				// 	}
				// 	let bankAccountItem = {
				// 		...item
				// 	}
				// 	//添加监听事件选择
				// 	uni.$emit('setAccountItem', bankAccountItem)
				// 	uni.navigateBack()
				// }

			},
			auditStatusFilter(val) {
				let label = '';
				for (let i = 0; i < this.statusOption.length; i++) {
					if (this.statusOption[i].value == val) {
						label = this.statusOption[i].label
					}
				}
				return label
			},
			deleteAccountComfirm(item) {
				uni.showModal({
					title: '提示',
					content: '确定删除该银行账户吗？',
					cancelText: '取消',
					confirmText: '确定',
					success: res => {
						if (res.confirm) {
							this.deleteAccount(item)
						}
					}
				})
			},
			deleteAccount(item) {
				this.isLoading = true
				let params = {
					id: item.id,
				}
				this.$request
					.post(this.$interfaces.deleteAccount, {
						data: params
					})
					.then((res) => {
						console.log(res, '删除');
						if (res.code == 200) {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: '删除成功'
							})
							this.getAccountList()
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg
						})
						this.isLoading = false
					})
			},
			showSmsDialog(type, item) {
				this.getCaptcha()
				this.smsDialogVisible = true
				this.smsType = type
				if (item) {
					this.selectItem = item
				}
			},

			validateSms() {
				if (!this.mobileImgCode) {
					uni.showToast({
						title: '请输入图形验证码',
						icon: 'none'
					})
					return
				}
				if (!this.formData.mobileCode) {
					uni.showToast({
						title: '请输入短信验证码',
						icon: 'none'
					})
					return
				}
				this.isLoading = true
				let params = {
					mobile: this.etcAccountInfo.mobile,
					mobileCode: this.formData.mobileCode
				}
				this.$request
					.post(this.$interfaces.authentication, {
						data: params
					})
					.then((res) => {
						console.log(res, '短信校验');
						if (res.code == 200) {
							this.isLoading = false
							this.addAccount()
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg
						})
						this.isLoading = false
					})
			},
			addAccount() {
				this.codebr = false
				clearInterval(this.timer)
				this.timer = null
				this.count = 60
				this.smsDialogVisible = false
				this.formData.mobileCode = ''
				this.mobileImgCode = ''
				if (this.smsType == 'edit') {
					uni.navigateTo({
						// url: '/pagesB/disputeRefund/modifyAccount'
						url: '/pagesB/disputeRefund/addAccount?id=' + this.selectItem.id
					})
					return
				}
				if (this.smsType == 'add') {
					//加上集装箱退费路由类型
					uni.navigateTo({
						url: '/pagesB/disputeRefund/addAccount?type=' + this.routeType + '&accountId=' + this
							.applyId
					})
				}

			},
			getAccountList() {
				this.isLoading = true
				let params = {
					custMastId: getEtcAccountInfo().custMastId
				}
				this.$request
					.post(this.$interfaces.accountList, {
						data: params
					})
					.then((res) => {
						console.log(res, 'userList');
						if (res.code == 200) {
							this.isLoading = false
							this.accountList = res.data
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg
						})
					})
			},
			updatePass() {
				this.isLoading = true
				let params = {
					ids: this.ids
				}
				this.$request
					.post(this.$interfaces.updatePass, {
						data: params
					})
					.then((res) => {
						console.log('passudpate', res);
						this.isLoading = false
						this.isShowPass = false
						if (res.code == 200) {
							uni.showModal({
								title: '温馨提示',
								content: res.data.message,
								showCancel: false
							})
							this.getAccountList()
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						this.isShowPass = false
						uni.showModal({
							title: '提示',
							content: error.msg
						})
					})
			},
			getAccountPass() {
				this.isLoading = true
				let params = {
					custMastId: getEtcAccountInfo().custMastId
				}
				this.$request
					.post(this.$interfaces.refundAccountPass, {
						data: params
					})
					.then((res) => {
						console.log('passList', res);
						if (res.code == 200) {
							this.isLoading = false
							if (res.data && res.data.car.length) {
								let carNo = res.data.car[0]
								this.ids = res.data.ids
								let nameStr = res.data.bankAccountList
								console.log('nameStr1', nameStr)
								let len = res.data.bankAccountList.length
								nameStr = nameStr.join()
								console.log('nameStr2', nameStr)
								this.passTipsText = '您的退款银行账户' + nameStr + '等' + len + '个账户已到失效期，如未及时修改可能会导致您的退费失败。'
								this.isShowPass = true
							}
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg
						})
					})
			},
			passConfirm() {
				this.updatePass()
			},
			passCancel() {
				this.isShowPass = false
				this.showSmsDialog('add')
			},
			getCaptcha() {
				let params = {}
				this.$request
					.post(this.$interfaces.getCaptcha, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.codeUrl = res.data.image
							this.captchaId = res.data.captchaId
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					})
					.catch((error) => {})
			},
			//发送短信
			sendSMS() {
				if (!this.mobileImgCode) {
					uni.showToast({
						title: '请输入图形验证码',
						icon: 'none'
					})
					return
				}
				this.codebr = true
				this.isLoading = true
				let params = {
					mobile: this.etcAccountInfo.mobile,
					mobileCode: this.mobileImgCode,
					captchaId: this.captchaId
				}
				this.$request
					.post(this.$interfaces.sendaAccountSms, {
						data: params
					})
					.then((res) => {
						console.log(res)
						this.isLoading = false
						if (res.code == '200') {
							this.timer = setInterval(() => {
								if (this.count > 0 && this.count <= 60) {
									this.count--
								} else {
									this.isLoading = false
									this.codebr = false
									clearInterval(this.timer)
									this.timer = null
									this.count = 60
								}
							}, 1000)
						} else {
							this.codebr = false
							this.timer = null
							this.count = 60
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										this.getCaptcha()
									}
								}
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})

			},
			ViewImage(item) {
				let imgsArray = [];
				imgsArray[0] = item.attorneyUrl;
				uni.previewImage({
					urls: imgsArray,
					current: 0
				});
			},
		}

	}
</script>

<style scoped lang="scss">
	.account-list {
		width: 100%;
		height: 100%;

	}

	.account-list .content {
		padding: 20rpx 30rpx 150rpx 30rpx;

		.card {
			background-color: #fff;
			width: 100%;
			padding: 20rpx;
			border-radius: 12rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
			position: relative;

			.card-bd {
				.card-bd-item {
					font-size: 28rpx;
					font-weight: 400;
					color: #454e50;
					margin: 10rpx 0;

					.label {
						margin-right: 30rpx;
					}

					.value {}
				}

				.delete {
					position: absolute;
					top: 6%;
					right: 4%;

					.operator-class {
						color: #0066E9;
						font-weight: 600;
						font-size: 30rpx;
					}
				}
			}
		}
	}


	.account-list .content .card .card-ft .card-ft_icon {
		width: 14rpx;
		height: 26rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bind-Info {
		.sendSMS {
			padding: 10rpx;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.des {
			font-size: 30upx;
			text-align: left;
			color: #666;
			background: #ffffff;
			padding: 10upx 25upx;
		}

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
		}
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.bind-Info .cu-form-group .title {
		font-size: 32upx;
	}

	.bind-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.bind-Info .cu-form-group input {
		text-align: left;
	}

	.bind-Info .cu-form-group radio-group {
		flex: 1;
		text-align: left;
	}

	.code-img {
		width: 210upx;
		height: 70upx;
		margin-right: 10upx;
	}

	.codebtn {
		background: #0066E9;

		min-width: 210upx;
		font-size: 24upx;
		height: 70upx;
		margin-right: 10upx;
	}

	.codebtn span {
		display: inline-block;
		color: #fff;
		line-height: 70upx;
	}

	.content-title {
		margin-bottom: 20rpx;

		.content-title-decoration {
			position: relative;
			font-weight: 600;
			color: #333333;
			font-size: 30rpx;
			padding-left: 16rpx;
		}

		.content-title-decoration:before {
			content: ' ';
			position: absolute;
			left: 0rpx;
			top: 50%;
			width: 8rpx;
			height: 30rpx;
			-webkit-transform: translateY(-50%);
			transform: translateY(-50%);
			border-radius: 4rpx;
			background-color: #0066E9;
			border-top-left-radius: 2px;
			border-top-right-radius: 2px;
			border-bottom-right-radius: 2px;
			border-bottom-left-radius: 2px;
		}

		.operator {
			color: #0066E9;
			font-weight: 600;

			font-size: 30rpx;

		}
	}

	.attorney-img {
		&>img {
			width: 180rpx;
			height: 180rpx;
		}
	}

	.modal {
		/deep/.neil-modal {
			.neil-modal__container {
				width: 90%;
			}

			.neil-modal__header {
				border-bottom: 1rpx solid #e8e8e8;

				text {
					color: #000000 !important;
					background-color: #fff !important;
				}

				image {
					position: absolute;
					right: 15rpx;
				}
			}

			.dialog {
				padding: 30rpx;
			}

			.neil-modal__footer-left,
			.neil-modal__footer-right {
				color: #0066E9 !important;
				background-color: #FFF;
				border: 1px solid #0066E9;
				border-radius: 10rpx;
				height: 70rpx;
				line-height: 70rpx;
			}

			.neil-modal__footer-right {
				color: #fff !important;
				background-color: #0066E9;
				border: 1px solid #0066E9;
				text-overflow: unset;
				// margin-right: 30rpx;
				margin: 30rpx 20rpx;
			}

			.neil-modal__footer-left {
				// margin-left: 30rpx;
				text-overflow: unset;
				margin: 30rpx 20rpx;
			}
		}

		// .dialog {
		// 	.user-btn {
		// 		/deep/.cu-btn {
		// 			position: relative;
		// 			font-size: 26rpx;
		// 			height: 90rpx;
		// 		}
		// 	}
		// }
	}
</style>