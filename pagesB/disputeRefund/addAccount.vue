<!--
  * @desc:新增&编辑用户
  * @author:zhangys
  * @date:2023-01-12 11:10:27
!-->
<template>
	<view class="sellPay-container">
		<view class="sellPay-Info">
			<view class="c-title">ETC账户信息</view>
			<form>
				<view class="cu-form-group">
					<view class="title ">账户名称:</view>
					<view class="value">
						{{customerInfo.customer_name}}
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">手机号码:</view>
					<view class="value">
						{{customerInfo.link_mobile}}
					</view>
				</view>
			</form>
		</view>

		<view class="sellPay-Info">
			<view class="c-title">退款银行账户</view>
			<form>
				<view class="cu-form-group">
					<view class="title title-require">户名:</view>
					<input v-model="formData.bankAccount" placeholder="请输入户名"></input>
				</view>
				<view class="cu-form-group">
					<view class="title title-require">银行卡号:</view>

					<input type="digit" v-model="formData.bankNo" placeholder="请输入银行卡号">
					<text class='cuIcon-cameraadd' @click="ocrBankImg" style="font-size: 40rpx;"></text>
					</input>
				</view>
				<view class="cu-form-group">
					<view class="title title-require">开户行:</view>
					<view class=" value">
						<picker @change="bindPayTypeChange" class="weui-picker-value" :range="disputeRefundBankName"
							range-key="label">
							<view class="picker" style="text-align: left;" v-if="picker_index==null">
								请选择开户行
							</view>
							<view class="picker" style="text-align: left;" v-else>
								{{ disputeRefundBankName[picker_index].label }}
							</view>
						</picker>
					</view>
				</view>
				<view class="cu-form-group" v-if="picker_index=='8'">
					<view class="title title-require">请输入开户行:</view>
					<input v-model="otherBankName" placeholder="请输入开户行"></input>
				</view>
				<view class="cu-form-group">
					<view class="title title-require">关联车辆:</view>
					<view class="value g-flex g-flex-wrap">
						<text v-if="relativeVehicle.length==0" style="color: #808080;">
							请选择车辆
						</text>
						<text v-else v-for="(item,index) in relativeVehicle" :key="index" class="vechicle-no">{{item}}
							<!-- <text class="cuIcon-close delete-icon"></text> -->
						</text>

					</view>
					<view class="cu-tag radius" @click="showVehicleList" style="font-size: 30rpx;">
						选择
					</view>
				</view>
				<view class="cu-form-group" style="position: relative;">
					<view class="title">授权材料:</view>
					<view class="grid col-3 grid-square flex-sub" style="margin-top: 20rpx;">
						<view class="bg-img" v-for="(item,index) in imgList" :key="index" @tap="ViewImage"
							:data-url="imgList[index].url">
							<image :src="imgList[index].url" mode="aspectFill"></image>
							<view class="cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
								<text class='cuIcon-close'></text>
							</view>
						</view>
						<view class="solids" @tap="ChooseImage()" v-if="imgList.length<1">
							<text class='cuIcon-cameraadd'></text>
						</view>
					</view>

					<view class="g-flex g-flex-align-center operator-btn">
						<view class="fileHandle" @click="authorizationFile('preview')">
							查看样例
						</view>
						<view class="fileHandle" @click="authorizationFile('downLoad')">
							下载模板
						</view>
					</view>
				</view>

			</form>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="submit">
						提交
					</button>
				</view>
			</view>
		</view>


		<!-- ocr识别 -->
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>

		<view class="load_desc">
			<view class="desc_title">
				温馨提示：
			</view>

			<view class="desc_text">
				<view>
					1.退款银行账户用于接收通行费退费等，请仔细核对，并确保银行卡状态正常；
				</view>
				<view>
					2.退款银行账户信息属长期有效材料，如有变更，请及时更改账户信息；
				</view>
				<view>
					3.如账户下办理了多辆车，可批量选择退款至此银行账户的车辆。
				</view>
				<view>
					4.如收款账户名与ETC账户名不一致，需提供ETC开户人的授权材料。
				</view>
				<view style="color: red;">
					5.若您的车辆已注销，请联系客服提交退款账户信息。
				</view>
			</view>
		</view>

		<!-- 关联车辆弹框 -->
		<TModal :showModal='VehicleListDialogVisible' modalTitle='关联车辆' :showCancelFlag='true'
			@cancelModal='VehicleListDialogVisible=false' okText='确定' @okModal='VehicleListDialogVisible=false'
			:modalStyle="'width:80%'">
			<view slot='content' class="bind-Info" style="max-height: 400rpx;overflow-y: scroll;">
				<view class="" v-if="vehicleList.length!=0">
					<view class="cu-form-group" v-for="(item,index) in vehicleList" :key='index'>
						<checkbox-group @change="checked=>changeCheckbox(checked,item,index)">
							<checkbox class="cyan checked  round" :checked='item.isCheck'
								style=" transform: scale(0.8,0.8);margin-right: 8rpx;" value="check">
							</checkbox>
						</checkbox-group>
						<view class="value">
							{{item.carNo}} 【{{vehicleColorStr(item.carColor)}}】
							{{vehicleClassType(item.carType)}}车
							<text v-if="item.cardStatus == '12'" style="color: red;margin-left: 30rpx;font-size: 26rpx;"
								class="status">
								已注销
							</text>
						</view>
					</view>
				</view>
				<view v-else>
					<view class="cu-form-group">
						<view class="value g-flex g-flex-center">
							暂无车辆
						</view>
					</view>
				</view>

			</view>
		</TModal>

		<!-- 信息确认弹框 -->
		<TModal :showModal='confirmDialogVisible' modalTitle='信息确认' :showCancelFlag='true'
			@cancelModal='confirmDialogVisible=false' okText='确认提交' cancelText="返回修改" @okModal='addAccount'
			:modalStyle="'width:80%'">
			<view slot='content'>
				<view class="bind-Info">
					<view class="cu-form-group">
						<view class="title">户名:</view>
						<input v-model="formData.bankAccount" disabled></input>
					</view>

					<view class="cu-form-group">
						<view class="title">开户行:</view>
						<input v-if="picker_index=='8'" v-model="otherBankName" disabled></input>
						<input v-else v-model="formData.bankName" disabled></input>
					</view>

					<view class="cu-form-group">
						<view class="title">收款账号:</view>
						<input v-model="formData.bankNo" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">关联车辆:</view>
						<view class="value g-flex g-flex-wrap">
							<text v-for="(item,index) in relativeVehicle" :key="index"
								style="width: 40%;">{{item}}</text>
						</view>
					</view>
				</view>

				<view class="load_desc" style="margin: 0 20rpx;">
					<view class="desc_title" style="text-align: left;margin-bottom: 10rpx;">
						温馨提示：
					</view>
					<view class="desc_text">
						<view>
							原则上，<text style="color: #0000ff;">{{relativeVehicle}}</text>
							的ETC交易退费均会退回原扣款账户；当原路退款不成功时，会通过本次设定的账户进行退款。
						</view>
					</view>
				</view>
			</view>
		</TModal>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import vue from 'vue';
	import {
		getCurrUserInfo,
		getCurrentCar,
		getEtcAccountInfo,
	} from "@/common/storageUtil.js";
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import TModal from '@/components/t-modal/t-modal.vue'
	import {
		checkPhone
	} from '@/common/util.js'
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType
	} from '@/common/method/filter.js'
	import {
		otherDisputeRefundBankName
	} from '@/common/const/optionData.js'
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	export default {
		name: '',
		components: {
			TButton,
			TModal,
			tLoading,
			cpimg
		},
		data() {
			return {
				vehicleInfo: {},
				customerInfo: {},
				formData: {
					attorneyUrl: '',
					bankAccount: '',
					bankName: '',
					bankNo: '',
					listCar: [],
					id: '',
					userManagerDTO: [],
					bankPhotoUrl: ''
				},
				isLoading: false,
				VehicleListDialogVisible: false, //车辆列表弹框
				confirmDialogVisible: false, //确认信息弹框
				imgList: [], //图片url列表
				isOcr: false, //ocr识别标识
				searchEtcInfo: {}, //查询etc账户信息
				vehicleList: [], //车辆列表
				selectedVehilceList: [], //选中车辆列表
				relativeVehicle: [], //选中车辆列表车牌号，展示用
				id: '',
				disputeRefundBankName: otherDisputeRefundBankName,
				picker_index: null,
				otherBankName: '',
				routeType: '', //集装箱路由类型
				applyId: '' //集装箱退费id
			}
		},
		computed: {},
		onLoad(option) {
			this.vehicleInfo = getCurrentCar() ? getCurrentCar() : {}
			this.customerInfo = getCurrUserInfo() ? getCurrUserInfo() : {}
			this.getVehicleList()
			this.searchAccountInfo()
			console.log(option, 'option');
			if (option.id) {
				this.id = option.id
				// this.getAccountDetail()
			}
			//路由类型赋值
			if (option.type) {
				this.routeType = option.type
			}
			//路由类型赋值
			if (option.accountId) {
				this.applyId = option.accountId
			}
		},
		created() {

		},
		watch: {},
		methods: {
			vehicleColorStr(val) {
				return getVehicleColor(val)
			},
			vehicleClassType(val) {
				return getVehicleType(val)
			},
			bindPayTypeChange(e) {
				this.picker_index = e.detail.value;
				this.formData.bankName = this.disputeRefundBankName[e.detail.value].label || '';
				if (this.picker_index == '8') {
					this.otherBankName = ''
				}
			},
			showVehicleList() {
				this.VehicleListDialogVisible = true
			},
			//表单校验
			validate() {
				if (!this.formData.bankAccount) {
					uni.showModal({
						title: '提示',
						content: '请输入户名',
						showCancel: false
					})
					return false
				}

				if (this.picker_index == null) {
					uni.showModal({
						title: '提示',
						content: '请选择开户行',
						showCancel: false
					})
					return false
				}
				if (this.picker_index == '8' && !this.otherBankName) {
					uni.showModal({
						title: '提示',
						content: '请输入开户行',
						showCancel: false
					})
					return false
				}
				if (!this.formData.bankNo) {
					uni.showModal({
						title: '提示',
						content: '请输入银行卡号',
						showCancel: false
					})
					return false
				}
				if (this.selectedVehilceList.length == 0) {
					uni.showModal({
						title: '提示',
						content: '请选择车辆',
						showCancel: false
					})
					return false
				}
				return true
			},

			ChooseImage() {
				this.isOcr = false
				let sourceType = 0
				this.$refs.cpimg._changImg(sourceType);
			},
			ocrBankImg() {
				this.isOcr = true
				let sourceType = 0
				this.$refs.cpimg._changImg(sourceType);
			},
			cpimgOk(file) {
				console.log(file);
				if (this.isOcr) {
					this.geturl(file, 'ocr')
					this.sendOCR(file);
					return
				}
				this.geturl(file, 'upload')
			},
			cpimgErr(e) {
				console.log(e)
			},
			// ocr识别
			sendOCR(file) {
				let base64Img = file.toString()
				let ocr_type = '';
				let current = {};
				var imgStr = base64Img.split(';')[1].split(",")[1] + '';
				let biz_content = {
					ocr_type: 1,
					file_name: 'file_name',
				}
				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};

				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.isLoading = false;
						let encryptedData = res.data.encryptedData;
						if (encryptedData.bankNo) {
							let trimBankNo = encryptedData.bankNo.replace(/\s*/g, "")
							this.formData.bankNo = trimBankNo
						}
						if (encryptedData.bankName) {
							for (let i = 0; i < this.disputeRefundBankName.length; i++) {
								if (this.disputeRefundBankName[i].label.includes(encryptedData.bankName)) {
									this.picker_index = i
									this.otherBankName = ''
									this.formData.bankName = this.disputeRefundBankName[this.picker_index].label ||
										''
									return
								}
							}
							this.picker_index = '8'
							this.otherBankName = encryptedData.bankName
						}
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//上传档案换取url
			geturl(file, type) {
				this.isLoading = true
				let params = {
					image: file.toString(),
				};
				this.$request.post(this.$interfaces.getImgUrl, {
					data: params
				}).then(res => {
					this.isLoading = false;
					console.log(res, 'ressss');
					if (res.code == 200) {
						if (type == 'upload') {
							this.imgList.push({
								url: res.data.data.fileUrl,
								code: res.data.data.code
							})
						} else {
							this.formData.bankPhotoUrl = res.data.data.code
						}
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg
						})
					}

				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg
					})
				})
			},
			onBlur() {
				if (!this.formData.bankNo) return
				let reg = /^(\d{14,19})$/g
				if (!reg.test(this.formData.bankNo)) {
					uni.showModal({
						title: "提示",
						content: '银行卡号长度不合法，请重新识别或重新输入',
						showCancel: false,
					});
					return
				}
				this.getBankName()
			},
			getBankName() {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.getBankName.method,
					bizContent: {
						cardNo: this.formData.bankNo,
						validSign: '2'
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						if (res.code === 200) {
							this.isLoading = false
							this.formData.bankName = res.data.issueBankName
						} else {
							this.formData.bankName = ''
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					}).catch(err => {
						this.isLoading = false
					})
			},

			ViewImage(e) {
				let viewArr = []
				this.imgList.forEach(item => {
					viewArr.push(item.url)
				})
				uni.previewImage({
					urls: viewArr,
					current: e.currentTarget.dataset.url
				});
			},
			DelImg(e) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该图片吗？',
					cancelText: '取消',
					confirmText: '确定',
					success: res => {
						if (res.confirm) {
							this.imgList.splice(e.currentTarget.dataset.index, 1)
						}
					}
				})
			},
			submit() {
				if (this.customerInfo.customer_name != this.formData.bankAccount && this.imgList.length == 0) {
					uni.showModal({
						title: '提示',
						content: '您的收款账户名与ETC账户名不一致，请上传授权材料',
						showCancel: false
					})
					return
				}
				if (!this.validate()) return
				this.confirmDialogVisible = true
			},

			//新增&编辑账户
			addAccount() {
				//新增接口
				let method = this.$interfaces.addAccount
				this.confirmDialogVisible = false
				this.isLoading = true
				this.formData.attorneyUrl = this.imgList.length == 0 ? '' : this.imgList[0].code
				//如果是修改的话，传入新参数code
				if (this.id) {
					this.formData.attorneyUrlCode = this.imgList.length == 0 ? '' : this.imgList[0].code
				}
				this.formData.id = getEtcAccountInfo().custMastId
				let data = JSON.parse(JSON.stringify(this.formData))
				// if (!data.bankName) {
				// 	data.bankName = this.bank_index_name
				// }
				if (this.picker_index == '8') {
					data.bankName = this.otherBankName
				}
				let params = {
					...data,
					userManagerDTO: this.searchEtcInfo,
					listCar: this.selectedVehilceList
				}
				//编辑
				if (this.id) {
					params.id = this.id
					params.bankNo = params.bankNo
					//编辑接口
					method = this.$interfaces.editAccount
				}
				console.log(params);
				this.$request
					.post(method, {
						data: params
					})
					.then((res) => {

						if (res.code == 200) {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: this.id ? '修改账户成功' : '新增账户成功',
								confirmText: '确定',
								success: res => {
									if (res) {
										uni.reLaunch({
											url: '/pagesB/disputeRefund/accountList?type=' + this
												.routeType + '&applyId=' + this.applyId
										})
									}
								}
							})

							this.imgList = []
							this.selectedVehilceList = []
							for (let key in this.formData) {
								this.formData[key] = ''
							}
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								cancelText: '取消',
								confirmText: '确定',

							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							cancelText: '取消',
							confirmText: '确定',

						})
					})

			},
			// 获取车辆列表
			getVehicleList() {
				let params = {
					custMastId: getEtcAccountInfo().custMastId
				}
				this.$request
					.post(this.$interfaces.selectVehicle, {
						data: params
					})
					.then((res) => {
						console.log(res, 'cheliang');
						if (res.code == 200) {
							this.vehicleList = res.data
							if (this.id) {
								this.getAccountDetail()
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								cancelText: '取消',
								confirmText: '确定',

							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							cancelText: '取消',
							confirmText: '确定',

						})
					})
			},
			//获取用户信息
			searchAccountInfo() {
				let params = {
					custMastId: getEtcAccountInfo().custMastId
				}
				this.$request
					.post(this.$interfaces.accountInfo, {
						data: params
					})
					.then((res) => {
						console.log(res, 'userinfo');
						if (res.code == 200) {
							this.searchEtcInfo = res.data
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								cancelText: '取消',
								confirmText: '确定',

							})
						}
					})
					.catch((error) => {})
			},
			changeCheckbox(checked, item) {
				//选中push到数组
				if (checked.detail.value == 'check') {
					this.selectedVehilceList.push(item)
				} else {
					// 取消选中，找出操作数据项的下标，删除
					let index = this.selectedVehilceList.map(item => item.carNo).indexOf(item.carNo)
					this.selectedVehilceList.splice(index, 1)
				}
				this.relativeVehicle = this.selectedVehilceList.map(item => {
					return item.carNo
				})
			},
			//查询账户详情
			getAccountDetail() {
				let params = {
					id: this.id
				}
				this.$request
					.post(this.$interfaces.accountDetail, {
						data: params
					})
					.then((res) => {
						console.log(res, 'userDetail');
						if (res.code == 200) {
							this.selectedVehilceList = []
							let result = res.data
							if (result.attorneyUrl) {
								this.imgList.push({
									url: result.attorneyUrl
								})
							}

							this.formData.bankAccount = result.bankAccount
							this.otherBankName = result.bankName
							this.picker_index = '8'
							this.formData.bankNo = result.bankNoStr
							this.formData.bankNoStr = result.bankNoStr
							this.id = result.id
							for (let i = 0; i < this.disputeRefundBankName.length; i++) {
								if (this.disputeRefundBankName[i].label.includes('result.bankName')) {
									this.formData.bankName = this.disputeRefundBankName[i].label
									this.picker_index = i
									this.otherBankName = ''
								}
							}
							this.relativeVehicle = result.carList.map(item => {
								return item.carNo
							})
							//修改的时候已选择的车辆不展示，这里把详情里的车辆列表默认选中，push到车辆列表中
							this.selectedVehilceList = result.carList.map((item) => {
								this.$set(item, 'isCheck', true);
								return item
							})
							this.vehicleList = [...this.selectedVehilceList, ...this.vehicleList]
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								cancelText: '取消',
								confirmText: '确定',

							})
						}
					})
					.catch((error) => {})
			},

			// webview跳转
			authorizationFile(val) {
				let previewFile = 'https://portal.gxetc.com.cn/public-static/file/通行费退费委托书样例.png';
				if (val == 'preview') {
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(previewFile)
					});
					return
				}
				if (val == 'downLoad') {
					this.downLoadFile()
					return
				}
			},
			downLoadFile() {
				let url = 'https://portal.gxetc.com.cn/public-static/file/通行费退费委托书模板.doc'
				let fileExt = url.substring(url.lastIndexOf('.') + 1).toLowerCase()
				let fileName = '授权书模板.docx'
				wx.downloadFile({
					url: url,
					filePath: wx.env.USER_DATA_PATH + '/' + fileName,
					success: res => {
						let filePath = res.filePath
						wx.openDocument({
							filePath: filePath,
							fileType: fileExt,
							showMenu: true,
							success: res => {
								console.log('文档打开成功');
							}
						})
					}
				})
			}

		}
	}
</script>

<style lang='scss' scoped>
	.sellPay-container {
		padding-bottom: 180rpx;
	}

	.title-require {
		&:before {
			content: '*';
			color: red;
		}
	}

	.sellPay-Info {
		background-color: #ffffff;

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.cu-form-group {
		min-height: 100rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bind-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.bind-Info .cu-form-group input {
		text-align: left;
	}


	.bankList {
		color: #39b9f2;
		font-size: 28rpx;
		text-decoration: underline;
	}

	.bankImg {
		margin-right: 20rpx;

		&>img {
			width: 70rpx;
			height: 70rpx;
		}
	}

	.load_desc {
		margin: 60rpx 36rpx;

		.desc_title {
			font-size: 28rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 500;
			color: #555555;
			margin-bottom: 20rpx;
		}

		.desc_text {
			text-align: left;
			line-height: 50rpx;
			text-indent: 2em;
			padding-bottom: 10rpx;
			font-size: 26rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #454e50;
		}
	}

	.delete-icon {
		position: absolute;
		top: -16%;
		left: 97%;
	}

	.vechicle-no {
		position: relative;
		width: 33%;
		/* margin: 4rpx 14rpx;
		font-size: 32rpx; */
	}

	.operator-btn {
		position: absolute;
		right: 5%;
		bottom: 75%;

		.fileHandle {
			font-size: 30rpx;
			color: #169bd5;
			margin: 0 8rpx;
		}
	}
</style>