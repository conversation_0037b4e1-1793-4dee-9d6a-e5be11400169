<!--
  * @desc:争议退款入口
  * @author:zhangys
  * @date:2023-01-12 11:08:25
!-->
<template>
	<view class="load-type">
		<view class="content">
			<view class="card" @click="goPageHandle('containerRefund')">
				<view class="card-bd">
					<view class="title">集装箱退费申请</view>
				</view>
				<view class="card-ft">
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unfold.png" class="card-ft_icon" mode='aspectFilt'></image>
				</view>
			</view>

			<view class="card" @click="goPageHandle('account')">
				<view class="card-bd">
					<view class="title">我的退款银行账户</view>
				</view>
				<view class="card-ft">
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unfold.png" class="card-ft_icon" mode='aspectFilt'></image>
				</view>
			</view>
		</view>
		<view class="load_desc">
			<view class="desc_title">
				温馨提示：
			</view>

			<view class="desc_text">

				<view>
					1.集装箱退费申请用于集装箱车主申请退费
				</view>
				<view>
					2.“我的退款银行账户”用于指定接收车辆ETC交易退款的银行账户。
				</view>

			</view>
		</view>

	</view>
	</view>
</template>

<script>
	import {
		getTokenId,
		getAccountId,
		setEtcVehicle,
		getOpenid,
		getLoginUserInfo,
		setOpenid,
	} from '@/common/storageUtil.js';

	export default {
		components: {

		},
		data() {
			return {

			}
		},
		onLoad() {

		},
		created() {
			// this.checkOpenId()
		},
		methods: {
			checkOpenId() {
				if (getOpenid()) {
					this.saveOpenId(getOpenid())
					return
				}
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}
						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.saveOpenId(res.data.openid)
									setOpenid(res.data.openid)
								}

							}
						})
					}
				})

			},
			goPageHandle(type) {
				if (type == 'containerRefund') {
					uni.navigateTo({
						url: '/pagesB/vehicleBusiness/vehicleList?fontType=' + type
					})
					return;
				}
				if (type == 'account') {
					uni.requestSubscribeMessage({
						tmplIds: ['t2I--Uw-tuLME7dhBKLqr2mfCsHGu_4NKfNU3wQi9ow'],
						success: (res) => {
							console.log(res, 'res');
							uni.navigateTo({
								url: '/pagesB/disputeRefund/accountList'
							});
						},
						fail: (err) => {
							console.log(err, 'err');
							uni.navigateTo({
								url: '/pagesB/disputeRefund/accountList'
							});
						}
					})
					this.checkOpenId()
					return;
				}

			},
			//获取用户openId保存订阅
			saveOpenId(openId) {
				this.$request.post(this.$interfaces.saveOpenId, {
					data: {
						netUserId: getLoginUserInfo().userIdStr,
						openId: openId
					}
				}).then(res => {}).catch(err => {})
			},

		}

	}
</script>

<style scoped lang="scss">
	.load-type {
		width: 100%;
		height: 100%;

	}

	.load-type .content {
		padding: 20rpx 30rpx;
	}

	.load-type .content .card {
		width: 100%;
		padding: 30rpx;
		height: 140rpx;
		border: 1px solid #e9e9e9;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(71, 123, 217, 0.12);
		display: flex;
		-moz-box-pack: justify;
		-ms-box-pack: justify;
		-webkit-box-pack: justify;
		-webkit-justify-content: space-between;
		-moz-justify-content: space-between;
		justify-content: space-between;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;

	}

	.load-type .content .card .card-bd .title {
		color: #333333;
		font-size: 30rpx;
		font-weight: bold;
	}

	.load-type .content .card .card-ft .card-ft_icon {
		width: 14rpx;
		height: 26rpx;
	}

	.load_desc {
		margin: 60rpx 36rpx;

		.desc_title {
			font-size: 28rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #555555;
			margin-bottom: 20rpx;
		}

		.desc_text {
			text-align: left;
			line-height: 50rpx;
			text-indent: 2em;
			padding-bottom: 10rpx;
			font-size: 26rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #454e50;
		}
	}
</style>
