<!-- 我的ETC账户 -->
<template>
	<view class="add-car">
		<view class="account-list" v-if="!isLoading && accountList && accountList.length > 0">
			<view class="car-title" v-if="accountList.length>2">您名下存在多个ETC账户,请选择</view>
			<view class="card-list" @click="changeUser(item, 2)" v-for="(item,index) in accountList"
				:key="item.custMastId">
				<view class="card">
					<image class="card-img" v-if='item.custType=="0"' src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/persion-Icon.png">
					</image>
					<image class="card-img" v-if='item.custType=="1"' src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/unit.png"></image>
					<view class="card-text">
						<text class="c-first">{{item.custName}}</text>
						<text class="c-second"
							v-if='item.custType=="0"'>证件类型：{{personalOCRTypeFilter(item.custIdTypeCode)}}</text>
						<text class="c-second"
							v-if='item.custType=="1"'>证件类型：{{enterpriseOCRTypeFilter(item.custIdTypeCode)}}</text>
						<text class="c-second" v-if='item.custType=="0"'>证件号码：{{hideIDCard(item.custIdNo)}}</text>
						<text class="c-second" v-else>证件号码：{{item.custIdNo}}</text>
						<text class="c-second" v-if='item.custType=="1"&& item.dept'>部门：{{item.dept}}</text>
						<text class="c-second">车辆数量：{{item.total > 0 ? item.total + '辆车' : '无车辆'}}</text>
					</view>
					<view class="arrow">
						<image src="../../../static/tab-bar/index/right-ar.png" class="right-arrow"></image>
					</view>
					<view class="bind" v-if='!item.bind'>
						<button class="cu-btn lines-red line-red">未绑定</button>
					</view>
					<view class="bind" v-else>
						<button class="cu-btn lines-green lines-green" @click.stop="unBind(item)">解绑</button>
					</view>
				</view>
			</view>
		</view>

		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<button class="weui-btn weui-btn_primary" @click="goBindOtherHandle">
					绑定ETC用户
				</button>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<TModal :showModal='dialogVisible' @cancelModal='cancelModal' @okModal='onBindHandle' :okText='okText'>
			<form slot='content' class="bind-Info">

				<view class="des">{{dialogTitle}}</view>
<!-- 				<view class="cu-form-group login-form-group">
					<input placeholder="请输入图形验证码" name="mobileCode" v-model='mobileCode'></input>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">
				</view> -->
				<view class="cu-form-group">

					<input placeholder="请输入验证码" @input="(e)=> handleInput('mobileCode',e)" :value="formData.mobileCode"
						name="mobileCode"></input>
					<text class="sendSMS text-cyan" @click="showCodeHandle">{{smsName}}</text>
				</view>
			</form>
		</TModal>
		<t-captcha id="captcha" app-id="191430362" @verify="handlerVerify" @ready="handlerReady" @close="handlerClose"
			@error="handlerError" />
	</view>
</template>

<script>
	import {
		personalOCRTypeFilter,
		enterpriseOCRTypeFilter
	} from '@/common/const/optionData.js'
	import TModal from '@/components/t-modal/t-modal.vue'
	import tLoading from '@/components/common/t-loading.vue'
	import TButton from '@/components/t-button'
	import {
		unitType,
		certificatesTypes
	} from '@/common/systemConstant.js'
	import addCorpUser from '@/pagesB/components/t-add-user.vue'
	import {
		getTokenId,
		setAccountId,
		removeStore,
		setStore,
		getStore,
		getLoginUserInfo,
		setEtcAccount,
		getDefaultUrl
	} from '@/common/storageUtil.js'
	export default {
		components: {
			tLoading,
			TButton,
			addCorpUser,
			TModal
		},
		data() {
			return {
				dialogVisible: false,
				smsName: '发送验证码',
				mobileCode: '', // 图形验证码
				codeUrl: '', // 图形验证码连接
				formData: {
					userNo: '',
					issueUserId: '',
					mobileCode: ''
				},
				time: null,
				accountList: [],
				showBackBtn: true,
				hideBtn: false,
				personalObj: {
					name: '',
					carNumber: 0
				},
				hasPersion: false,
				isLoading: false,
				type: '',
				num: 0,
				navObj: {
					selectUser: '/pages/tab-bar/index/index', // 入口选户
					changeUser: '/pages/tab-bar/index/index' // 用户切换
				},
				captchaId: '',
				dialogTitle: '',
				okText: '',
				bindFlag: '',
				mobile: '', //登录手机号码
				codeTicket: '', //腾讯验证码
			}
		},
		onLoad(param) {
			this.type = param.type
			this.getAccountList()
			this.getLoginMobile()
		},
		methods: {
			personalOCRTypeFilter,
			enterpriseOCRTypeFilter,
			goBindOtherHandle() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/otherBindCheck/otherBindCheck'
				})
			},
			showCodeHandle() {
				this.selectComponent('#captcha').show()
				// 进行业务逻辑，若出现错误需重置验证码，执行以下方法
				// if (error) {
				// this.selectComponent('#captcha').refresh()
				// }
			},
			// 验证码验证结果回调
			handlerVerify(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
				if (ev.detail.ret === 0) {
					// 验证成功
					this.codeTicket = ev.detail.ticket
					console.log('ticket:', ev.detail.ticket)
					this.sendSMS()
				} else {
					// 验证失败
					// 请不要在验证失败中调用refresh，验证码内部会进行相应处理
				}
			},
			// 验证码准备就绪
			handlerReady() {
				console.log('验证码准备就绪')
			},
			// 验证码弹框准备关闭
			handlerClose(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
				if (ev && ev.detail.ret && ev.detail.ret === 2) {
					console.log('点击了关闭按钮，验证码弹框准备关闭');
				} else {
					console.log('验证完成，验证码弹框准备关闭');
				}
			},
			// 验证码出错
			handlerError(ev) {
				console.log(ev.detail.errMsg)
			},
			// getCaptcha() {
			// 	let params = {}
			// 	this.$request
			// 		.post(this.$interfaces.getCaptcha, {
			// 			data: params
			// 		})
			// 		.then((res) => {
			// 			if (res.code == 200) {
			// 				this.codeUrl = res.data.image
			// 				this.captchaId = res.data.captchaId
			// 			} else {
			// 				uni.showToast({
			// 					title: res.msg,
			// 					icon: 'none'
			// 				})
			// 			}
			// 		})
			// 		.catch((error) => {})
			// },
			hideIDCard(id) {
				if (!id) return ''
				return id.replace(/^(\d{6})\d+(\d{4})$/, '$1******$2')
			},
			handleInput(type, event) {
				this.formData[type] = event.target.value
			},
			cancelModal() {
				this.dialogVisible = false
				this.smsName = '发送验证码'
				for (let key in this.formData) {
					this.formData[key] = ''
				}
			},
			//判断绑定解绑
			onBindHandle() {
				if (this.bindFlag) {
					this.unBindHandele()
				} else {
					this.bindHandle()
				}
			},
			//绑定
			bindHandle() {
				this.$request
					.post(this.$interfaces.etcAccountBind, {
						data: this.formData
					})
					.then((res) => {
						this.dialogVisible = false
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '用户绑定成功',
								showCancel: false
							})
							this.getBindRow(this.formData);
							this.getAccountList()
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			//解绑
			unBindHandele() {
				let params = {
					userNo: getLoginUserInfo().userNo,
					issueUserId: this.formData.issueUserId,
					mobileCode: this.formData.mobileCode
				}
				this.$request
					.post(this.$interfaces.unBundling, {
						data: params
					})
					.then((res) => {
						this.dialogVisible = false
						if (res.code == 200) {
							uni.showModal({
								title: '提示',
								content: '解绑成功',
								showCancel: false
							})
							this.getAccountList()
							setEtcAccount({});
							setAccountId();
							uni.reLaunch({
								url:'/pagesD/home/<USER>/p-home'
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			//发送短信
			sendSMS() {
				// if (!this.mobileCode) {
				// 	uni.showModal({
				// 		title: '提示',
				// 		content: '请输入图形验证码',
				// 		showCancel: false
				// 	})
				// 	return
				// }
				if (!this.time) {
					this.isLoading = true
					let countdown = 60
					let params = {
						userNo: getLoginUserInfo().userNo,
						ticket: this.codeTicket,
						// mobileCode: this.mobileCode,
						// captchaId: this.captchaId
					}
					this.$request
						.post(this.$interfaces.sendaAccountSms, {
							data: params
						})
						.then((res) => {
							console.log(res)
							this.isLoading = false
							if (res.code == '200') {
								this.time = setInterval(() => {
									countdown = countdown - 1
									this.smsName = countdown + '秒后重新发送'
									if (countdown === 0) {
										clearInterval(this.time)
										this.time = null
										this.smsName = '重新发送'
									}
								}, 1000)
							} else {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false,
									// success: (res) => {
									// 	if (res.confirm) {
									// 		this.getCaptcha()
									// 	}
									// }
								})
							}
						})
						.catch((error) => {
							uni.showModal({
								title: '提示',
								content: error.msg,
								showCancel: false
							})
						})
				}
			},
			//获取个人账户列表
			getAccountList() {
				let data = {}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.getEtcAccountList, data)
					.then((res) => {
						console.log(res, '11111')
						if (res.code == 200) {
							this.accountList = []
							this.accountList = res.data || []
							let countNum = this.accountList.length
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
						this.isLoading = false
					})
					.catch((error) => {
						console.log(error, '********')
						this.isLoading = false
					})
			},

			//切换用户后切换默认用户
			changeDefaultUser(item) {
				let data = {
					bindingId: item.bindingId + '',
					userNo: getLoginUserInfo().userNo
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.setDefaultUser, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
					})
					.catch((error) => {
						this.isLoading = false
					})
			},
			// 切换用户
			changeUser(item, type) {
				let _self = this
				_self.bindFlag = item.bind
				if (item.bind) {
					setEtcAccount(item)
					setAccountId(item.custMastId)
					this.changeDefaultUser(item)
					let url=getDefaultUrl()?getDefaultUrl():'/pagesD/home/<USER>/p-home'
					uni.reLaunch({
						url: url
					})
				} else {
					this.formData.userNo = getLoginUserInfo().userNo
					this.formData.issueUserId = item.custMastId
					uni.showModal({
						title: '提示',
						content: '当前用户未绑定，是否绑定呢？',
						success: function(res) {
							if (res.confirm) {
								_self.mobileCode = ''
								_self.dialogTitle = `绑定ETC用户需要向预留手机号${item.custMobile}码发送验证码，认证成功后方可绑定`
								_self.dialogVisible = true
								_self.formData.mobileCode = ''
								_self.okText = '绑定'
								clearInterval(_self.time)
								_self.time = null
								_self.smsName = '发送验证码'
								// _self.getCaptcha()
							}
						}
					})
				}
			},
			//获取登录相信信息
			getLoginMobile() {
				let params = {
					userNo: getLoginUserInfo().userNo,
				};
				this.$request.post(this.$interfaces.getAccountInfo, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.mobile = res.data.mobile
						if (this.mobile) {
							this.mobile = this.mobile.replace(
								/(\d{3})\d{4}(\d{4})/,
								"$1****$2"
							);
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch((error) => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//解绑
			unBind(item) {
				console.log(item)
				this.bindFlag = item.bind
				this.mobileCode = ''
				this.formData.issueUserId = item.custMastId
				this.dialogTitle = `解绑ETC用户需要向预留手机号${this.mobile}码发送验证码，认证成功后方可解绑`
				this.dialogVisible = true
				this.formData.mobileCode = ''
				this.okText = '解绑'
				clearInterval(this.time)
				this.time = null
				this.smsName = '发送验证码'
				// this.getCaptcha()
			},
			// 获取当前绑定用户信息
			getBindRow(bindInfo) {
				let data = {}
				this.$request
					.post(this.$interfaces.getEtcAccountList, data)
					.then((res) => {
						if (res.code == 200 && res.data && res.data.length) {
							let result = res.data;
							let row = result.filter(item => {
								return bindInfo.issueUserId == item.custMastId
							})

							if (row && row.length) {
								console.log(bindInfo, row[0], '-----获取当前绑定用户信息')
								this.changeUser(row[0], 2)
							}

						}
					})
					.catch((error) => {

					})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.account-list {
		padding-bottom: 150rpx;
	}

	.bind-Info {
		.login-form-group .code-img {
			width: 240upx;
			height: 90upx;
			margin-left: 20upx;
		}

		.sendSMS {
			padding: 10rpx;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.des {
			font-size: 30upx;
			text-align: left;
			color: #666;
			background: #ffffff;
			padding: 10upx 25upx;
		}

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
		}
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.bind-Info .cu-form-group .title {
		font-size: 32upx;
	}

	.bind-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.bind-Info .cu-form-group input {
		text-align: left;
	}

	.bind-Info .cu-form-group radio-group {
		flex: 1;
		text-align: left;
	}

	.add-car {
		display: flex;
		flex-direction: column;
		padding: 28rpx;

		.corpList {
			position: absolute;
			height: calc(100% - 518rpx);
			overflow: auto;
			top: 300rpx;
		}

		.car-title {
			font-size: 36rpx;
			color: #666;
		}

		.card-list {
			margin-top: 28rpx;

			.card {
				display: flex;
				background-color: #fff;
				width: 700rpx;
				min-height: 180rpx;
				align-items: center;
				border-radius: 10rpx;
				position: relative;
				box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
				margin-bottom: 20rpx;
				opacity: 0.8;
				padding: 20rpx 0;
				position: relative;

				.bind {
					position: absolute;
					top: 3rpx;
					right: 3rpx;
				}

				.arrow {
					position: absolute;
					right: 34rpx;

					.right-arrow {
						width: 30rpx;
						height: 30rpx;
					}
				}

				.card-img {
					width: 108rpx;
					height: 108rpx;
					margin: 0 36rpx;
				}

				.card-text {
					display: flex;
					flex-direction: column;

					.c-first {
						color: #666;
						font-weight: bold;
						font-size: 40rpx;
						width: 500rpx;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
						word-break: break-all;
					}

					.c-second {
						color: #333;
						font-size: 28rpx;
						margin-top: 10rpx;
					}

					.der-value {
						display: inline-block;
						width: 56rpx;
					}
				}
			}
		}

		.goback {
			position: absolute;
			bottom: 20rpx;
			width: 718rpx;

			/deep/.cu-btn.radius {
				border-radius: 10rpx;
			}
		}
	}
</style>
