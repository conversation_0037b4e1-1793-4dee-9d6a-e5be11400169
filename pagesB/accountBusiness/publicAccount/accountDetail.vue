<template>
	<view class="account-detail">
		<block v-if="Object.keys(detail).length > 0">
			<view class="detail-content">
				<view class="input-wrapper g-flex align-center">
					<view class="input-desc">
						对公账户名称：
					</view>
					<input class="input" type="text" disabled :value="detail.accountName || ''">
				</view>
				<view class="input-wrapper g-flex align-center">
					<view class="input-desc">
						专属对公转账充值账号：
					</view>
					<input class="input" type="text" disabled :value="detail.subAccountNo || ''">
				</view>
				<view class="input-wrapper g-flex align-center">
					<view class="input-desc">
						开户行信息：
					</view>
					<input class="input" type="text" disabled :value="detail.openBank">
				</view>
			</view>
			<view class="tips-wrapper">
				<view class="tips-title">
					温馨提示:
				</view>
				<view class="tips">
					1、若需要进行对公转账充值，可通过网上银行、手机银行、ATM或银行人工柜台转账至上述对公充值账户。
				</view>
				<view class="tips">
					2、充值款预计两小时内到账，无需反馈转账回执单。
				</view>
			</view>
		</block>
		<view v-if="!isShowLoding && Object.keys(detail).length == 0"
			class="g-flex g-flex-center g-flex-align-center margin-top-lg">
			<view class="blank margin-top-lg" style="font-size: 36rpx;">
				尚未开通账户
			</view>
		</view>
		<tLoading :isShow="isShowLoding" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getLoginUserInfo,
	} from '@/common/storageUtil.js'
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isShowLoding: true,
				detail: {},
			}
		},
		onLoad() {
			this.getAccountDetail()
		},
		methods: {
			getAccountDetail(closeRemind) {
				this.isShowLoding = true
				let data = {
					routePath: this.$interfaces.getAccountDetail.method,
					bizContent: {
						userNo: getLoginUserInfo().userNo,
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isShowLoding = false
						console.log('res', res)
						if (res.code == 200) {
							this.detail = res.data
							this.blankFlag = true
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isShowLoding = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.account-detail {
		padding-top: 30rpx;

		.detail-content {
			background-color: #ffffff;

			.input-wrapper {
				border-top: 1rpx solid #e8e8e8;
				padding: 20rpx 30rpx;

				.input-desc {
					flex: 0 0 200rpx;
					width: 200rpx;
					color: rgb(119, 119, 119);
					text-align: left;
				}

				.input {
					flex: 1;
					height: 80rpx;
					text-align: right;
					border-radius: 10rpx;
					padding-left: 20rpx;
				}
			}
		}


		.tips-wrapper {
			padding: 40rpx 20rpx 40rpx 30rpx;
			font-size: 28rpx;
			font-family: PingFangSC-regular;
			// background-color: #ffffff;
			color: #0066E9;

			.tips {
				line-height: 42rpx;
				margin-top: 10rpx;
				text-indent: 2em;
			}
		}
	}
</style>
