<template>
	<view class="user-list">
		<view class="weui-form-preview user-list-item" v-for="(item,index) in list" :key='index'>
			<view class="weui-form-preview__hd">
				<view class="cell">
					<view class="cell-hd">
						<image v-if='item.custType=="0"'
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/personal_icon.png"
							mode="aspectFilt" class="cell-hd_icon">
						</image>
						<image v-if='item.custType=="1"'
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/company_icon.png"
							mode="aspectFilt" class="cell-hd_icon">
						</image>
					</view>
					<view class="cell-bd">
						<view class="title">{{item.custName}}</view>
						<view class="value" v-if='!item.total'>无车辆</view>
						<view class="value" v-if='item.total'>{{item.total}}辆车</view>
					</view>
					<view class="cell-ft">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/right.png" mode="aspectFilt" class="cell-ft_icon"></image>
					</view>
				</view>
			</view>
			<view class="weui-form-preview__bd">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">手机号码</view>
					<view class="weui-form-preview__value">{{item.custMobile}}</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">证件类型</view>
					<view class="weui-form-preview__value" v-if='item.custType=="0"'>
						{{personalOCRTypeFilter(item.custIdTypeCode)}}
					</view>
					<view class="weui-form-preview__value" v-if='item.custType=="1"'>
						{{enterpriseOCRTypeFilter(item.custIdTypeCode)}}
					</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">证件号码</view>
					<view class="weui-form-preview__value">{{item.custIdNo}}</view>
				</view>
			</view>
			<view class="weui-form-preview__ft">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label"></view>
					<view class="weui-form-preview__value">
						<view class="btn-item"><button class="weui-btn_mini weui-btn_primary"
								@click="bind(item)">绑定</button></view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		personalOCRTypeFilter,
		enterpriseOCRTypeFilter
	} from "@/common/const/optionData.js";
	import {
		getTokenId,
		setAccountId,
		removeStore,
		setStore,
		getStore,
		getLoginUserInfo,
		setEtcAccount,
		getDefaultUrl
	} from '@/common/storageUtil.js'

	export default {
		data() {
			return {
				formData: {},
				bindTicket: '',
				list: [],
				currnetCustomer: {},
				accountList: []
			}
		},
		onLoad(option) {
			let params = {};

			this.formData = option;
			console.log('option', option)
			this.formData.mobile = JSON.parse(decodeURIComponent(option.mobile));
			this.accountqueryOther();
		},
		methods: {
			personalOCRTypeFilter,
			enterpriseOCRTypeFilter,
			bind(row) {
				this.currnetCustomer = row
				let _self = this;
				let params = {
					userNo: getLoginUserInfo().userNo,
					issueUserId: row.custMastId,
					bindTicket: this.bindTicket,
				};
				this.isLoading = true

				this.$request.post(this.$interfaces.accountbindOther, {
					data: params
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						uni.showModal({
							title: "提示",
							content: '绑定成功',
							showCancel: false,
							success: function(action) {
								if (action.confirm) {
									_self.getBindRow(row)
								}
							}
						});
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
							success: function(action) {
								if (action.confirm) {
									uni.reLaunch({
										url: "/pagesB/accountBusiness/accountList/accountList"
									})
								}
							}
						});
					}


				}).catch(error => {

					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
						success: function(action) {
							if (action.confirm) {
								uni.reLaunch({
									url: "/pagesB/accountBusiness/accountList/accountList"
								})

							}
						}
					});
				})
			},
			accountqueryOther() {

				this.isLoading = true

				this.$request.post(this.$interfaces.accountqueryOther, {
					data: this.formData
				}).then(res => {
					console.log(res);
					this.isLoading = false
					if (res.code == 200) {
						this.bindTicket = res.data.bindTicket;
						this.list = res.data.list;
						this.list = this.list.filter(item => {
							return !item.bind
						});
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(error => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false
					});
				})
			},
			getBindRow(bindRow) {
				let data = {}
				this.$request
					.post(this.$interfaces.getEtcAccountList, data)
					.then((res) => {
						if (res.code == 200 && res.data && res.data.length) {
							let result = res.data;
							let row = result.filter(item => {
								return bindRow.custMastId == item.custMastId
							})
							if (row && row.length) {
								this.onSwitchUserHandle(row[0])
							}

						}
					})
					.catch((error) => {

					})
			},
			onSwitchUserHandle(item) {
				let _self = this
				_self.bindFlag = item.bind
				if (item.bind) {
					setEtcAccount(item)
					setAccountId(item.custMastId)
					this.changeDefaultUser(item)
					let url = getDefaultUrl() ? getDefaultUrl() : '/pages/home/<USER>/p-home'
					uni.reLaunch({
						url: url
					})
				}
			},
			//切换用户后切换默认用户
			changeDefaultUser(item) {
				let data = {
					bindingId: item.bindingId + '',
					userNo: getLoginUserInfo().userNo
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.setDefaultUser, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
					})
					.catch((error) => {
						this.isLoading = false
					})
			},
		}
	}
</script>

<style scoped lang="scss">
	.user-list {
		background-color: #f8f8f8;
		width: 100%;
		height: 100%;
	}

	.user-list .user-list-item {
		margin: 20rpx 30rpx 0 30rpx;
	}

	.cell {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
	}

	.cell .cell-hd {
		width: 90rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;
	}

	.cell .cell-hd .cell-hd_icon {
		display: block;
		width: 84rpx;
		height: 84rpx;
	}

	.cell .cell-bd {
		flex: 1;
		margin-left: 30rpx;
	}

	.cell .cell-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
	}

	.cell .cell-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #555;
		margin-top: 12rpx;
	}

	.cell .cell-ft {
		width: 28rpx;
	}

	.cell .cell-ft .cell-ft_icon {
		width: 28rpx;
		height: 28rpx;
		display: block;
	}
</style>