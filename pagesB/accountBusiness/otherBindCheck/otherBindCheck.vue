<template>
	<view class="">
		<view class="weui-form">
			<view class="weui-cells__title">
				绑定ETC账户
			</view>
			<view class="weui-cells">
				<licensecolor desc='非车身颜色' :palteColor='formData.vehicle_color' @on-change='onLicensecolorChange'>
				</licensecolor>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车牌号码</view>
					</view>
					<!-- <view class="weui-cell__bd weui-cell__primary">
						<input v-model="formData.vehicle_code" class="weui-input" placeholder="请输入车牌号码" />
					</view> -->
					<view class="weui-cell__primary g-flex g-flex-end">
						<!-- <input v-model="vehicleData.vehicle_code" class="weui-input" placeholder="请输入代充车牌号码" /> -->
						<view class="input-wrapper">
							<input placeholder="请点击输入车牌号码" @tap="plateShow=true" disabled="true"
								v-model.trim="formData.vehicle_code" />
							<plate-input style="margin-left: 40upx;" v-if="plateShow" :plate="formData.vehicle_code"
								@export="setPlate" @close="plateShow=false" />
						</view>

					</view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" v-if="false">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车牌颜色</view>
					</view>
					<view class=" weui-cell__primary " style="width: 100%;">
						<picker @change="vehicleColorChange" :range="vehicleColorOptions" range-key="label">
							<view class="weui-picker-value g-flex g-flex-start">
								{{vehicleColorOptions[vehicleColorIndex].label}}
							</view>
						</picker>
					</view>
					<view class=" weui-cell__bd weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">手机号码</view>
					</view>
					<view class=" weui-cell__primary">
						<!-- 						<view class="weui-cell__value" v-if="formData.phone">
							{{formData.phone|hideMiddle}}
						</view> -->
						<view class="weui-cell__value" v-if="formData.phone">
							{{phoneStr}}
						</view>
					</view>

				</view>
				<!-- <view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">图形验证码</view>
					</view>
					<view class="weui-cell__primary">
						<input v-model="formData.captchaCode" class="weui-input" placeholder="图形验证码" />
					</view>
					<view class="weui-cell__bd weui-cell__ft g-flex g-flex-align-center">
						<image :src="codeUrl" class="code-img" @click="getCaptcha">
					</view>
				</view> -->
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">短信验证码</view>
					</view>
					<view class="weui-cell__primary">
						<input v-model="formData.sms" class="weui-input" placeholder="短信验证码" />
					</view>
					<view class="weui-cell__bd weui-cell__ft g-flex g-flex-align-center">
						<text class="sendSMS" @click="showCodeHandle">{{smsName}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_default" @click="goBack">
						返回
					</button>
				</view>
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="accountqueryCheck()">
						查询ETC用户
					</button>
				</view>
			</view>
		</view>
		<t-captcha id="captcha" app-id="*********" @verify="handlerVerify" @ready="handlerReady" @close="handlerClose"
			@error="handlerError" />
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import licensecolor from '@/pagesB/components/licensecolor/licensecolor.vue';
	import tLoading from '@/components/common/t-loading.vue'
	import {
		vehicleColorPicker
	} from '@/common/const/optionData.js'
	import plateInput from '@/components/uni-plate-input/uni-plate-input'
	export default {
		data() {
			return {
				vehicleColorOptions: vehicleColorPicker,
				isLoading: false,
				vehicleColorIndex: 0,
				vehicleReg: /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z0-9]{1}[A-Z0-9]{1}([京津沪渝桂蒙宁新藏冀晋辽吉黑苏浙皖赣闽鲁粤鄂湘豫川云贵陕甘青琼])?[A-NP-Z0-9]{1}[A-NP-Z0-9]{3}[A-NP-Z0-9挂学警港澳领试超外应急]{1}([A-NP-Z0-9外应急])?|^([A-Z0-9]{7}))$/,
				formData: {
					vehicle_color: "",
					vehicle_code: "",
					phone: "",
					captchaCode: "", //图形验证码
					sms: "", //短信验证码
					captchaId: "", // 图形验证码ID
				},
				codeUrl: '',
				smsName: "发送验证码",
				plateShow: false,
				codeTicket: '', //腾讯验证码
				phoneStr: '', //脱敏手机号
			}
		},
		components: {
			tLoading,
			plateInput,
			licensecolor
		},
		watch: {
			"formData.vehicle_color": function(val) {
				if (this.vehicleReg.test(this.formData.vehicle_code)) {
					this.searchVehiclePhone();
				}
			},
			'formData.vehicle_code': function(val) {
				if (this.formData.vehicle_color && val && this.vehicleReg.test(val)) {
					this.searchVehiclePhone();
				}
			}
		},
		created() {
			// this.getCaptcha();
		},
		filters: {
			hideMiddle(val) {
				return `${val.substring(0, 3)}****${val.substring(val.length - 4)}`;
			},
		},
		methods: {
			showCodeHandle() {
				if (!this.formData.vehicle_code) {
					uni.showModal({
						title: "提示",
						content: "请输入车牌号码",
						showCancel: false,
					});
					return;
				}
				if (this.formData.vehicle_code && !this.formData.phone) {
					uni.showModal({
						title: "提示",
						content: "根据车牌查询手机号码不存在",
						showCancel: false,
					});
					return;
				}
				this.selectComponent('#captcha').show()
				// 进行业务逻辑，若出现错误需重置验证码，执行以下方法
				// if (error) {
				// this.selectComponent('#captcha').refresh()
				// }
			},
			// 验证码验证结果回调
			handlerVerify(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
				if (ev.detail.ret === 0) {
					// 验证成功
					this.codeTicket = ev.detail.ticket
					console.log('ticket:', ev.detail.ticket)
					this.sendSMS()
				} else {
					// 验证失败
					// 请不要在验证失败中调用refresh，验证码内部会进行相应处理
				}
			},
			// 验证码准备就绪
			handlerReady() {
				console.log('验证码准备就绪')
			},
			// 验证码弹框准备关闭
			handlerClose(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
				if (ev && ev.detail.ret && ev.detail.ret === 2) {
					console.log('点击了关闭按钮，验证码弹框准备关闭');
				} else {
					console.log('验证完成，验证码弹框准备关闭');
				}
			},
			// 验证码出错
			handlerError(ev) {
				console.log(ev.detail.errMsg)
			},
			onLicensecolorChange(val) {
				this.formData.vehicle_color = val;
			},
			setPlate(plate) {
				if (plate.length >= 7) this.formData.vehicle_code = plate
				this.plateShow = false
			},
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			// getCaptcha() {
			// 	let params = {

			// 	}
			// 	this.$request.post(this.$interfaces.getCaptcha, {
			// 		data: params
			// 	}).then(res => {
			// 		if (res.code == 200) {

			// 			this.codeUrl = res.data.image
			// 			this.formData.captchaId = res.data.captchaId
			// 		} else {
			// 			uni.showToast({
			// 				title: res.msg,
			// 				icon: "none"
			// 			})
			// 		}
			// 	}).catch(error => {
			// 		uni.showToast({
			// 			title: error.msg,
			// 			icon: "none"
			// 		})
			// 	})
			// },
			sendSMS() {
				if (this.time) return;
				this.isLoading = true
				let params = {
					mobile: this.formData.phone,
					ticket: this.codeTicket,
					// mobileCode: this.formData.captchaCode,
					// captchaId: this.formData.captchaId,
					type: 1,
				}
				let countdown = 60;
				this.$request.post(this.$interfaces.sendaAccountSms, {
					data: params
				}).then(res => {
					console.log(res);
					this.isLoading = false
					if (res.code == '200') {
						this.time = setInterval(() => {
							countdown = countdown - 1
							this.smsName = countdown + "秒后重新发送"
							if (countdown === 0) {
								clearInterval(this.time)
								this.time = null
								this.smsName = "重新发送"
							}
						}, 1000)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						// this.getCaptcha();
					}
				}).catch(error => {
					// this.getCaptcha();
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})

			},
			vehicleColorChange(e) {
				this.vehicleColorIndex = e.detail.value;
				this.formData.vehicle_color = this.vehicleColorOptions[e.detail.value].value || "";
			},
			//检验etc用户是否存在
			accountqueryCheck() {
				if (!this.formData.vehicle_color) {
					uni.showModal({
						title: "提示",
						content: "请选择车牌颜色",
						showCancel: false,
					});
					return;
				}
				if (!this.formData.vehicle_code) {
					uni.showModal({
						title: "提示",
						content: "请输入车牌号码",
						showCancel: false,
					});
					return;
				}
				// if (!this.formData.captchaCode) {
				// 	uni.showModal({
				// 		title: "提示",
				// 		content: "请输入图形验证码",
				// 		showCancel: false,
				// 	});
				// 	return;
				// }
				if (!this.formData.sms) {
					uni.showModal({
						title: "提示",
						content: "请输入短信验证码",
						showCancel: false,
					});
					return;
				}
				let params = {
					mobile: this.formData.phone,
					mobileCode: this.formData.sms,
					vehicleCode: this.formData.vehicle_code,
					vehicleColor: this.formData.vehicle_color,
				}
				let str = 'mobile=' + encodeURIComponent(JSON.stringify(params.mobile)) + '&mobileCode=' + params.mobileCode + '&vehicleCode=' + params
					.vehicleCode + '&vehicleColor=' + params.vehicleColor
				uni.redirectTo({
					url: "/pagesB/accountBusiness/otherBind/otherBind?" + str
				})
			},
			// 根据车牌查询手机号码
			searchVehiclePhone() {
				this.isLoading = true

				this.$request.post(this.$interfaces.searchVeh, {
					data: this.formData
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let phoneStr = res.data.mobile_sensitive
						this.phoneStr = phoneStr
						this.formData.phone = res.data.mobile

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			accountqueryOther() {
				this.isLoading = true
				const params = {
					mobile: this.formData.phone,
					mobileCode: this.formData.sms,
					vehicleCode: this.formData.vehicle_code,
					vehicleColor: this.formData.vehicle_color,
				};
				this.$request.post(this.$interfaces.accountqueryOther, {
					data: params
				}).then(res => {
					if (res.code == 200) {

					}
				}).catch(error => {

				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.weui-input,
	.weui-cell__value {
		text-align: left;
	}

	.weui-bottom-fixed {
		z-index: 1;
	}

	.sendSMS {
		text-align: right;
		color: #1d82d2;
		min-width: 200rpx;
		margin-left: 10rpx;
	}

	.code-img {
		width: 240upx;
		height: 70upx;
		margin-left: 10rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}
</style>