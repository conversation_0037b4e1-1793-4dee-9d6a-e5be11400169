<template>
	<view class="personal">
		<handle-step :current="0" />
		<ocrUpload @ocr-change="ocrChange"></ocrUpload>
		<view class="weui-form">
			<view class="weui-cells__title">
				请核对OCR识别信息
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">客户姓名</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input @input="changeInput($event,'customer_name')" :value="formData.customer_name"
							class="weui-input" placeholder="请输入用户名称" />
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">客户类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input @input="changeInput($event,'customer_type')" :value="'个人'" class="weui-input"
							placeholder="请输入客户类型" />
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">手机号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input " @input="changeInput($event,'link_mobile')"
							:value="formData.link_mobile" placeholder-class='placeholder' placeholder="请输入手机号"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">证件类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<picker style="width:100%;" @change="bindUserPickerChange" :range="personalType"
							range-key="label">
							<view class="weui-picker-value">{{personalType[user_type_index].label}}</view>
						</picker>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">证件号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" @input="changeInput($event,'certificates_code')"
							:value="formData.certificates_code" placeholder="请输入证件号码" />
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>
		<view class="tips">
			<text class="tips-text">
				请仔细核对证件信息，如信息有误，请重新拍照上传
			</text>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<!-- <view class="btn-item">
					<button class="weui-btn weui-btn_primary" >
						上一步
					</button>
				</view> -->
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="toNext">
						下一步
					</button>
				</view>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import handleStep from "@/components/t-handleStep/handleStep.vue"
	import ocrUpload from '@/components/ocr-upload/uploadPersonal.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import {
		personalType
	} from "@/common/systemConstant.js";
	import {
		checkIdCard,
		checkPhone,
		checkPosttal
	} from "@/common/util.js";
	import {
		getCurrUserInfo,
		setCurrUserInfo
	} from "@/common/storageUtil.js";
	export default {
		components: {
			handleStep,
			ocrUpload,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				noticeList: [
					"采集的身份证件信息仅用于车辆认证，不会提供给第三方平台。"
				],
				formData: {
					business_type: '1', //1：新办 2：换卡 3：换obu
					customer_type: "0",
					customer_name: "", // 用户名称
					certificates_type: "0", // 证件类型
					certificates_code: "", // 证件号码
					link_mobile: "", // 手机号码，
					registered_type: "1",
				},
				personalType,
				user_type_index: 0,
				redirect_url: '',
				leftImg: false,
				rightImg: false
			};
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			}
		},
		created() {
			this.getDraft()
		},
		methods: {
			bindUserPickerChange(e) {
				this.user_type_index = e.detail.value;
				this.formData.certificates_type =
					this.personalType[e.detail.value].value || "";
			},
			changeInput(event, data) {
				this.formData[data] = event.detail.value;
			},
			modify() {
				this.show = true
				this.isEdit = true
			},

			ocrChange(encryptedData) {
				console.log(encryptedData, 'ocr识别');
				let bizContent = encryptedData.encryptedData
				if (encryptedData.side == 1) {
					this.leftImg = true
					this.formData.certificates_code = bizContent['cardNum'] || ''
					this.formData.customer_name = bizContent['realName'] || ''
				}
				if (encryptedData.side = 2) {
					this.rightImg = true
				}
			},


			validate() {
				if (!(this.leftImg && this.rightImg)) {
					uni.showModal({
						title: "提示",
						content: "请上传证件照片",
						showCancel: false,
					});
					return false;
				}

				if (!this.formData.customer_name) {
					uni.showModal({
						title: "提示",
						content: "请输入用户名称",
						showCancel: false,
					});
					return false;
				}
				if (this.formData.certificates_type === "0") {
					if (!checkIdCard(this.formData.certificates_code)) {
						uni.showModal({
							title: "提示",
							content: "请输入正确的证件号码",
							showCancel: false,
						});
						return false;
					}
				} else {
					if (!this.formData.certificates_code) {
						uni.showModal({
							title: "提示",
							content: "请输入正确的证件号码",
							showCancel: false,
						});
						return false;
					}
				}

				if (!checkPhone(this.formData.link_mobile)) {
					uni.showModal({
						title: "提示",
						content: "请输入正确的手机号码",
						showCancel: false,
					});
					return false;
				}
				return true
			},

			//保存草稿，进行下一步
			toNext() {
				if (this.validate()) {
					this.isLoading = true
					this.$request.post(this.$interfaces.saveDraft, {
						data: this.formData
					}).then(res => {
						if (res.code == 200) {
							this.isLoading = false
							uni.navigateTo({
								url: '/pagesA/newBusiness/vehicleFile/vehicleFile'
							})
						} else {
							this.isLoading = false
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
						console.log(res, '保存个人信息');
					}).catch(err => {
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: err.msg,
							showCancel: false,
						});
					})
				}
			},

			//获取草稿
			getDraft() {
				this.$request.post(this.$interfaces.getDraft).then(res => {
					console.log(res, '获取草稿');
					if (res.code == 200) {
						// this.formData=res.data
					}
				})
			}

		}
	};
</script>

<style scoped lang="scss">
	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.idCard {
		background-color: #f6f6f6;

		.examine {
			position: absolute;
			height: calc(100% - 436rpx);
			background-color: #FFFFFF;
			top: 400rpx;
			width: 100%;
			border-radius: 16rpx 16rpx 0px 0px;

			.examine-content {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-top: 150rpx;

				.text {
					font-size: 32rpx;
					font-weight: 400;
					color: #333333;
					width: 464rpx;
					text-align: center;
					margin-top: 40rpx;
				}
			}
		}

		.idCard-top {
			position: relative;
			z-index: 9;
			padding: 48rpx 30rpx;
			background-color: #fff;
			border-radius: 16rpx 16rpx 0px 0px;

			.title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
			}

			.content {
				display: flex;

				.left,
				.right {
					position: relative;
					margin-top: 40rpx;

					.photo_icon {
						position: absolute;
						top: 55rpx;
						left: 125rpx;
					}

					.text {
						text-align: center;
						font-size: 28rpx;
						color: #333;
						font-weight: 400;
						margin-top: 20rpx;
					}

					.delIcon {
						position: absolute;
						right: 0;
						top: 0;
					}
				}

				.right {
					margin-left: 10rpx;
				}
			}

			.center {
				justify-content: center;
			}
		}

		.idCard-bottom {
			padding: 30rpx 0;
			background-color: #fff;
			margin-top: 20rpx;

			.title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
				margin-bottom: 30rpx;
				margin-left: 30rpx;
			}

			.text_line {
				padding: 0 30rpx;
				height: 100rpx;
				width: 100%;
				font-size: 30rpx;
				color: #999999;
				font-weight: 400;
				line-height: 100rpx;
				border-top: 1px solid #e9e9e9;
				display: flex;
				justify-content: space-between;

				.text {
					display: inline-block;
					width: 250rpx;
					color: #999999;
					font-size: 30rpx;
					font-weight: 400;
				}
			}

			.bottom {
				border-bottom: 1px solid #e9e9e9;
			}
		}

		.tips {
			color: #ff9000;
			font-weight: 400;
			font-size: 22rpx;
			text-align: center;
			margin: 10rpx 0;
		}

		.btn {
			display: flex;
			justify-content: space-around;
			background-color: #FFFFFF;
			padding: 20rpx;
		}
	}

	.slot-content {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
		flex-direction: column;
		align-items: center;

		.text {
			font-size: 26rpx;
			font-weight: 400;
			color: #666666;
			width: 500rpx;
			text-align: center;
			margin-bottom: 10rpx;
		}

		.phone {
			font-size: 36rpx;
			font-weight: 500;
			color: #333333;
		}
	}

	.tips {
		margin-top: 14rpx;
		margin-top: 12rpx;
		text-align: center;

		.tips-text {
			font-size: 11px;
			font-family: PingFangSC, PingFangSC-Regular;
			color: #ff9038;
		}
	}
</style>