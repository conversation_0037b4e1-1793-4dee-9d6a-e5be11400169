<!--
  * @desc:车辆状态信息
  * @author:yangce
  * @date:2023-04-05 14:52:56
!-->
<template>
	<!-- 车辆列表 -->
	<view class="vehicle-state" v-if="vehicleList.length>0">
		<view class="vehicle_item" v-for='(item,index) in vehicleList' :key='index'>
			<view class="vehicle_item-hd">
				<view class="vehicle_item-media-box">
					<view class="vehicle_item-media__hd">
						<img v-if="item.vehicleType =='2'" class='img' src="../../static/vehicleIcon/car_icon.png"
							alt="">
						<img v-if="item.vehicleType =='1'" class='img' src="../../static/vehicleIcon/truck_icon.png"
							alt="">
						<img v-if="item.vehicleType =='3'" class='img' src="../../static/vehicleIcon/priatecar_icon.png"
							alt="">
					</view>
					<view class="vehicle_item-media__bd">
						<view class="vehicle_item-media-title">
							{{item.vehicleCode}}【{{getVehicleColor(item.vehicleColor)}}】
						</view>
						<view class="vehicle_item-media-desc">
							<view style="font-size: 28rpx;">{{getVehicleType(item.vehicleType)}}车
								<text style="margin-left:10rpx"
									v-if="item.cardProduct=='0'||item.cardProduct=='5'||item.cardProduct=='7'||item.cardProduct=='9'||item.cardProduct=='8'||item.cardProduct=='2'">
									{{gxCardTypeFilter(item.cardProduct)}}</text>
								<text style="margin-left:10rpx" v-if="item.cardProduct=='4'||item.cardProduct=='10'">
									{{item.contract.bankCodeName?item.contract.bankCodeName:''}}</text>
								<text style="margin-left:10rpx" v-if="item.cardProduct=='3'"> 建设银行</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="vehicle_item-bd">
				<view class="list">
					<view class="list-item">
						<view class="list-item_label">
							车辆状态
						</view>
						<view :class="item.vehicleStatus=='正常'?'list-item_value':'list-item_value abnormal'">
							{{item.vehicleStatus}}
						</view>
						<view class="list-item_value " v-if="item.vehicleStatus!='正常'">
							<view class="method-btn" @click="methodHandle(item)">处理办法</view>
						</view>
					</view>
					<view class="list-item" v-if="item.vehicleStatus!='正常'">
						<view class="list-item_label">
							开始时间
						</view>
						<view class="list-item_value abnormal">
							{{item.cardStopTime}}
						</view>
					</view>
					<view class="list-item" v-if="item.vehicleStatus!='正常'">
						<view class="list-item_label">
							产生原因
						</view>
						<view class="list-item_value abnormal">
							{{item.cardStopReason}}
						</view>
					</view>
					<!-- <view class="list-item">
						<view class="list-item_label">
							卡片状态
						</view>
						<view :class="item.cardStatus=='正常'?'list-item_value':'list-item_value abnormal'">
							{{item.cardStatus}}
						</view>
					</view> -->
					<view class="list-item">
						<view class="list-item_label">

							<text v-if="item.cardProduct=='0'||item.cardProduct=='2'">可用余额</text>
							<text v-if="item.cardProduct=='5'">卡账余额</text>
							<text v-if="item.cardProduct=='9'">客账余额</text>
							<text v-if="item.cardProduct=='7'">出账日期</text>

						</view>
						<view class="list-item_value ">
							<text v-if="item.cardProduct=='0'">{{moneyFilter(item.cardAmount)}}元</text>
							<text v-if="item.cardProduct=='5'">{{moneyFilter(item.cardAmount)}}元</text>
							<text v-if="item.cardProduct=='9'">{{moneyFilter(item.cardAmount)}}元</text>
							<text v-if="item.cardProduct=='7'">{{item.billingDate}}</text>
							<text v-if="item.cardProduct=='2'">{{moneyFilter(item.cardAmount)}}元</text>

						</view>
					</view>
				</view>
			</view>
			<view class="vehicle_item-ft g-flex g-flex-center">
				<view class="btn g-flex g-flex-horizontal-vertical" @click='detail(item)'>
					详情
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		gxCardTypeFilter
	} from '@/common/method/filter.js'
	import {
		setCurrentCar,
		getAccountId,
		getDefaultUrl,
		setDefaultUrl,
		setRechargeType
	} from '@/common/storageUtil.js'
	import TButton from '@/components/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue'
	import float from '@/common/method/float.js'
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		name: '',
		props: {
			vehicleList: {
				type: Array,
				default () {
					return []
				}
			}
		},
		data() {
			return {
				isLoading: false,
			}
		},
		components: {
			tLoading,
			TButton
		},
		computed: {},
		created() {

		},
		methods: {
			...mapActions(['setVehicleDetail']),
			getVehicleColor,
			moneyFilter(value) {
				if (!value || value == 0) {
					return value
				}
				return float.div(value, 100)
			},
			getVehicleClassType,
			getVehicleType,
			gxCardTypeFilter,
			//处理办法
			methodHandle(val) {
				if(val.solutionStatus=='5'){
					let url ='https://mp.weixin.qq.com/s/_0mvlJbF1BTFrr24s2W4qw'
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(url)
					})
					return
				}
				uni.showModal({
					title:'处理办法',
					content: val.solution,
					showCancel: false,
					confirmText:val.solutionStatus == '1'?'前往补缴':'确定',
					success: (res) => {
						if (res && val.solutionStatus == '0') {
							if (val.cardProduct == '9') {
								this.jumpAccountRecharge(val)
								return
							}
							this.sendVehicleBizSearch(val)
						}
						if(res&&val.solutionStatus=='1'){
							let nextData = {
								vehicle_code: val.vehicleCode,
								vehicle_color: val.vehicleColor,
							}
							uni.navigateTo({
								url: '/pagesC/afterPay/recordDetail/recordDetail?nextData=' + encodeURIComponent(JSON
									.stringify(nextData))
							})
						}
						
					}
				});
			},
			jumpAccountRecharge(val) {
				let params = JSON.parse(JSON.stringify(val.clientAccount))
				params.bindVehicleList = JSON.stringify(params.bindVehicleList)
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/rechargeAccount/index?' + this.objToUrlParam(params)
				})
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},
			sendVehicleBizSearch(val) {
				setCurrentCar({})
				let data = {
					routePath: this.$interfaces.vehicleBizSearch.method,
					bizContent: {
						vehicle_code: val.vehicleCode,
						vehicle_color: val.vehicleColor
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false

						if (res.code == 200 && res.data && res.data.length) {
							let item = res.data[0];
							if (val.vehicleCode == item.vehicle_code && val.cardNo == item
								.cpu_card_id) {

								this.goRechargeBusinessHandle(item);
							}

						}
					})
					.catch((error) => {
						console.log(error);
						this.isLoading = false
					})
			},
			// 根据查询出的卡类型进行不同充值业务
			goRechargeBusinessHandle(item) {
				if (item.gx_card_type == '5' || item.gx_card_type == '8') {
					setCurrentCar(item)
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/recharge/p-recharge'
					})
					return
				}
				if (item.gx_card_type == '0') {
					setCurrentCar(item)
					uni.navigateTo({
						url: '/pagesB/loadBusiness/recharge/recharge'
					})
					return
				}
			},

			//详情
			detail(val) {
				let params = JSON.parse(JSON.stringify(val))
				// if(params.contract){
				// 	params.contract=JSON.stringify(params.contract)
				// }
				this.setVehicleDetail({})
				this.setVehicleDetail(params)
				uni.navigateTo({
					url: '../../vehicleBusiness/vehicleDetail'
				})
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},


		},
	}
</script>

<style lang='scss'>
	.vehicle-state {
		margin: 20rpx 30rpx 0 30rpx;

		.vehicle_item {
			width: 100%;
			border-radius: 16rpx;
			padding: 25rpx 35rpx;
			background: #FFFFFF;
			border: 2rpx solid #ECECEC;
			margin-bottom: 20rpx;

			.vehicle_item-media-box {
				margin-bottom: 34rpx;
				display: flex;
				box-align: center;
				align-items: center;

				.vehicle_item-media__hd {
					margin-right: 30rpx;
					width: 110rpx;
					height: 110rpx;
				}

				.vehicle_item-media__hd .img {
					width: 110rpx;
					height: 110rpx;
					display: block;
				}

				.vehicle_item-media__bd {
					.vehicle_item-media-title {
						font-weight: 600;
						color: #333333;
						margin-bottom: 20rpx;
						font-size: 34rpx;
					}

					.vehicle_item-media-desc {
						font-weight: 400;
						color: #676767;
						font-size: 30rpx;
					}
				}
			}

			.vehicle_item-bd {
				padding-bottom: 26rpx;
				border-bottom: 1px dashed #C3C3C3;

				.list {
					.list-item {
						display: flex;
						margin-bottom: 14rpx;
						box-align: center;
						align-items: center;

						&:last-child {
							margin-bottom: 0;
						}

						.list-item_label {
							font-size: 26rpx;
							width: 160rpx;
							margin-right: 15rpx;
							font-weight: 400;
							color: #999999;
						}

						.list-item_value {
							flex: 1;
							font-size: 26rpx;
							font-weight: 400;
							color: #333333;

							.method-btn {
								height: 47rpx;
								text-align: center;
								line-height: 47rpx;
								padding: 0 10px;
								width: 160rpx;
								font-weight: 400;
								color: #5591FF;
								font-size: 26rpx;
								border-radius: 20rpx;
								border: 2rpx solid #5591FF;
							}
						}

						.list-item_value.abnormal {
							color: #F65B5B;
						}
					}
				}
			}

			.vehicle_item-ft {
				padding-top: 30rpx;

				.btn {
					width: 226rpx;
					height: 60rpx;
					font-weight: 400;
					color: #5591FF;
					font-size: 28rpx;
					border-radius: 30rpx;
					border: 2rpx solid #5591FF;
				}
			}
		}
	}
</style>