<template>
	<view class="black-list">
		<view class="black-list-item" v-for="(item, index) in blackInfo" :key="index">
			<!-- <view class="item-name">
				{{item.customer_name}}
			</view> -->
			<view class="item-status" :style="item.status == 1 ? 'background-color:#FBC02D' : 'background-color:#26DA6F'">{{ item.status == 1 ? '限制消费' : '解除限制' }}</view>
			<view class="item-left">
				<view class="item-content">
					<span>{{ item.type == '1' ? '卡号：' : 'obu号：' }}</span>
					<span>{{ item.card_or_obu_id }}</span>
				</view>
				<view class="" v-if="item.status == 1">
					<view class="item-content">
						开始时间：
						<span>{{ item.start_time }}</span>
					</view>
					<!-- <view class="item-content" >
						解黑时间：
						<span>{{item.end_time}}</span>
					</view> -->
				</view>
				<view class="" v-if="item.status == 2">
					<view class="item-content">
						解除时间：
						<span>{{ item.start_time }}</span>
					</view>
				</view>
			</view>
			<view class="item-right">
				<view class="item-content">
					类型：
					<span>{{ item.type === '1' ? 'ETC卡' : 'OBU' }}</span>
				</view>

				<view class="item-content">
					<span>{{ item.status == 1 ? '限制原因：' : '原限制原因：' }}</span>
					<span>{{ item.reason }}</span>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: '',
	components: {},
	props: {
		blackInfo: {
			type: Array,
			default() {
				return {};
			}
		}
	},
	data() {
		return {};
	},
	computed: {},
	created() {},
	methods: {}
};
</script>

<style lang="scss">
.black-list {
	.black-list-item {
		display: flex;
		align-items: center;
		justify-content: center;
		/* flex-direction: column; */
		position: relative;
		justify-content: space-around;
		width: 700rpx;
		min-height: 240rpx;
		border-radius: 14rpx;
		box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.08);
		background-color: #fff;
		margin: 30rpx auto;

		.item-name {
			font-size: 36rpx;
			position: absolute;
			top: 7%;
			left: 6%;
			font-weight: bold;
		}

		.item-status {
			width: 140rpx;
			height: 50rpx;
			color: #fff;

			border-radius: 10rpx;
			font-size: 32rpx;
			line-height: 50rpx;
			text-align: center;
			position: absolute;
			top: 8%;
			left: 5%;
		}

		.item-left {
			margin-top: 40rpx;
		}

		.item-right {
			margin-top: 40rpx;
		}

		.item-content {
			margin: 10rpx auto;
			font-size: 28rpx;
			color: #666666;
		}
	}
}
</style>
