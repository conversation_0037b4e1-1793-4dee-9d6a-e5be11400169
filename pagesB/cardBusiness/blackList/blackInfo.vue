<template>
	<view class="sellPay-container">
		<view class="scroll-box">
			<scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" :lower-threshold='lowerThreshold'
				@scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
				<blackItem :blackInfo='blackList' />
				<load-more :loadStatus="noticeLoadStatus" />
			</scroll-view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import loadMore from '../../components/load-more/index.vue';
	import blackItem from './blackItem.vue'
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	export default {

		data() {
			return {
				scrollTop: 0,
				old: {
					scrollTop: 0
				},
				lowerThreshold: 120,
				isLoading: false,
				formData: {
					vehicle_color: "",
					vehicle_code: ""
				},
				blackList: [],
				noticeLoadStatus: 3,
			}
		},
		components: {
			tLoading,
			loadMore,
			blackItem
		},
		created() {
			this.formData.vehicle_color = this.vehicleInfo.vehicle_color
			this.formData.vehicle_code = this.vehicleInfo.vehicle_code
			this.getBlackList()
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},
		},
		methods: {
			getBlackList() {
				this.noticeLoadStatus = 1;
				this.isLoading = true;
				let params = {
					routePath: this.$interfaces.queryBlackList.method,
					bizContent: this.formData
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					this.isLoading = false;
					console.log(res, '黑名单');
					if (res.code == 200) {
						if (res.data && res.data.length) {
							this.blackList = res.data
							this.noticeLoadStatus = 3;
						} else {
							this.noticeLoadStatus = 0;
						}
					} else if (res.code == 401) {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.reLaunch({
										url: '/pagesD/login/p-login'
									});
								}
							}
						});
					} else {
						this.noticeLoadStatus = 2;
					}

				}).catch((err) => {
					this.noticeLoadStatus = 2;
				})

			},
			upper: function(e) {

			},
			scrolltolower: function(e) {


			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop;
			},
		}
	}
</script>

<style>
</style>
