<template>
	<view>
		<view class="no-vehicle" v-if="!vehicleList.length">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/addcar_nocar.png" mode="aspectFilt" class="no-vehicle_icon">
			</image>
			<view class="no-vehicle_des">
				暂无车辆
			</view>

		</view>
		<view class="scroll-box" v-else>
			<scroll-view :style="'height:'+(windowHeight-10)+'px;'" :scroll-top="scrollTop" scroll-y="true"
				class="scroll-Y" :lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower"
				@scroll="scroll">
				<blackItem :vehicleList='vehicleList' />
				<load-more :loadStatus="noticeLoadStatus" />
			</scroll-view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import loadMore from '../../components/load-more/index.vue';
	import blackItem from './p-item.vue'
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	export default {

		data() {
			return {
				windowHeight: this.windowHeight,
				scrollTop: 0,
				old: {
					scrollTop: 0
				},
				lowerThreshold: 120,
				isLoading: false,
				formData: {
					vehicle_color: "",
					vehicle_code: ""
				},
				vehicleList: [],
				noticeLoadStatus: 3,
				isNoVehicle: false,
				pageNum: 1,
				pageSize: 10,
				flag: false,
			}
		},
		components: {
			tLoading,
			loadMore,
			blackItem
		},
		created() {

		},
		computed: {

		},
		onShow() {
			this.pageNum = 1
			this.pageSize = 10
			this.vehicleList = []
			this.flag = false
			this.getVehicleList()
		},
		onUnload() {
			
		},
		created() {

		},
		methods: {
			getVehicleList() {
				if (!getAccountId()) {
					this.isNoVehicle = true
					return
				}
				let params = {
					customerId: getAccountId(),
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}

				this.$request
					.post(this.$interfaces.vehicleStatusList, {
						data: params
					})
					.then(res => {

						if (res.code == 200) {
							let result = res.data.records || []
							if (res.data.records.length) {
								this.vehicleList = this.vehicleList.concat(result)
							} else {
								this.noticeLoadStatus = 3;
								this.flag = true
							}
							if (this.vehicleList.length == res.data.total) {
								this.noticeLoadStatus = 3
								this.flag = true
							}
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
							this.noticeLoadStatus = 2;
						}
					}).catch((err) => {

						this.noticeLoadStatus = 2;
					})
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				if (this.flag) return;
				let self = this;


				setTimeout(function() {
					self.pageNum = self.pageNum + 1;
					self.getVehicleList();
				}, 500)

			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop;
			},
		}
	}
</script>

<style lang='scss'>
	.no-vehicle {
		padding-top: 320rpx;
		width: 100%;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 29rpx;
		color: #333333;
		font-weight: 400;
		text-align: center;
		margin-top: 60rpx;
	}
</style>