<template>
	<view class="sellPay-container">
		<view class="block"></view>
		<view class="sellPay-Info search">
			<view class="search-form-group">
				<view class="title">车牌号码</view>
				<view class="value">{{formData.carNo}}（{{getVehicleColor(formData.carColor)}}）</view>
			</view>
			<view class="search-form-group">
				<view class="title">ETC卡号</view>
				<view class="value">{{formData.cardNo}}</view>
			</view>

			<view class="search-form-item">
				<u-cell-item title="交易场景" class="car" :title-style="titleStyle" :arrow="true" @click="showScene= true"
					label=" ">
					<u-input v-model="sceneName" :clearable="false" :custom-style="customStyle" disabled
						@click="showScene= true" />
				</u-cell-item>
			</view>
			<view class="search-form-item" style="display: flex;">
				<view class="title">交易开始时间</view>
				<view class="picker">
					<view class="pick-date pick-date-one">
						<picker mode="date" fields="month" @change="startDateChange" :value="strartTime"
							:start="startDate" :end="endTime" style="width: 100%;">
							<u-cell-item title=" " :arrow="false" icon="date-fill">
								<view class="monthData">{{strartTime}}</view>
							</u-cell-item>
						</picker>
					</view>
					<view style="margin: 0 30rpx;">至</view>
					<view class="pick-date pick-date-two">
						<picker mode="date" fields="month" @change="endDateChange" :value="endTime" :end="endDate"
							style="width: 100%;">
							<u-cell-item title=" " :arrow="false" icon="date-fill">
								<view class="monthData">{{endTime}}</view>
							</u-cell-item>
						</picker>
					</view>
				</view>
			</view>
			<view class="search-btn">
				<button class="cu-btn bg-topic" @tap="searchTable">查询</button>
			</view>
		</view>
		<view class="total">
			<view class="total-recharge">消费总金额：<span>{{moneyFilter(totalAmount)}}元</span></view>
			<view class="frequency">消费次数：<span>{{totalCount}}次</span></view>
		</view>
		<scroll-view :style="{height:height}" :scroll-top="scrollTop" scroll-y="true" class="scroll-Y"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<view class="card-Info">
				<view class="card" v-for="(item, index) in currentConsumeList" :key="index">
					<view class="card-top">
						<image :src="'../../static/pic'+item.scene+'.png'" mode="" class="icon"></image>
						<view class="deduction">扣费金额<span class="unit">￥</span>
							<span class="money">{{moneyFilter(item.deductionAmount)}}</span>
						</view>
						<view class="status" :class="'status'+item.payStatus">
							<view class="name" :class="'name'+item.payStatus">{{filterDict(item.payStatus)}}</view>
						</view>
					</view>
					<!-- 加油站 -->
					<view class="card-middle" v-if="item.scene==3">
						<view>{{item.oilStationName}} {{item.oilTime}}</view>
					</view>
					<!-- 停车场 -->
					<view class="card-middle" v-if="item.scene==2">
						<view>{{item.parkingName}}</view>
					</view>
					<view class="card-middle" v-if="item.scene==2">
						<view>出场时间：{{item.outTime}}</view>
					</view>
					<view class="card-middle" v-if="item.scene==2">
						<view>交易时间：{{item.transactionTime}}</view>
					</view>
					<!-- 服务区 -->
					<view class="card-middle" v-if="item.scene==1">
						<view>入口：{{item.enStation}} {{item.enTime}}</view>
					</view>
					<view class="card-middle" v-if="item.scene==1">
						<view>出口：{{item.exStation}} {{item.exTime}}</view>
					</view>
					<view class="dash-line"></view>
					<view class="card-btn">
						<view class="btn" @click="goDetail(item)">详情</view>
						<!-- #ifdef MP-WEIXIN -->
						<view class="btn btn1" v-if="item.payStatus == 3" @click="supplementaryPayment()">去补缴</view>
						<!-- #endif -->
					</view>
				</view>
			</view>
			<view v-if="totalCount==0" class="no-data">
				<image src="../../static/no_data.png" mode="" class="no-data-img"></image>
				<view class="no-data-title">暂无交易记录</view>
			</view>
		</scroll-view>

		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import loadMore from '../../components/load-more/index.vue';
	import {
		getCurrentCar
	} from "@/common/storageUtil.js";
	import {
		getVehicleColor
	} from '@/common/method/filter.js'
	import {
		sceneType
	} from "@/common/const/optionData.js"
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')

	function getDate(type) {
		const date = new Date();
		let year = date.getFullYear();
		let month = date.getMonth() + 1;
		let day = date.getDate();

		if (type === 'start') {
			year = year - 1;
		} else if (type === 'end') {
			year = year;
		}
		month = month > 9 ? month : '0' + month;
		day = day > 9 ? day : '0' + day;

		return `${year}-${month}`;
	}
	export default {
		components: {
			TButton,
			tLoading,
			loadMore
		},
		data() {
			return {
				isLoading: false,
				titleStyle: {
					color: "#888888",
					fontWeight: "400",
					width: "230rpx",
					display: "inline-block",
					fontSize: "30rpx",
					// marginRight: "50rpx"
				},
				customStyle: {
					paddingLeft: "0",
					color: "#333333",
					minHeight: "54rpx",
					fontSize: '30rpx'
				},
				showScene: false,
				height: 'calc(100% - 540rpx)',
				scrollTop: 0,
				lowerThreshold: 120,
				currentConsumeList: [],
				old: {
					scrollTop: 0
				},
				totalAmount: '22',
				totalCount: 3,
				formData: {
					carNo: '',
					carColor: '',
					cardMastId: '',
					cardNo: '',
					strartTime: '',
					endTime: '',
					scene: '',
					pageNum: 1,
					pageSize: 10
				},
				sceneName: '全部',
				sceneType,
				strartTime: '',
				endTime: '',
				flag: false,
				startDate: getDate('start'),
				endDate: getDate('end'),
				show: true,
				mode: 'multiple'
			};
		},

		computed: {
			vehicleInfo() {
				return getCurrentCar() || {}
			},

		},
		onLoad(options) {},
		created() {
			this.formData.cardNo = this.vehicleInfo.cpu_card_id
			this.formData.carColor = this.vehicleInfo.vehicle_color
			this.formData.carNo = this.vehicleInfo.vehicle_code
			this.strartTime = dayjs(new Date().getTime() - 30 * 24 * 60 * 60 * 1000).format('YYYY-MM')
			this.formData.strartTime = this.strartTime
			this.endTime = dayjs(new Date().getTime()).format('YYYY-MM')
			this.formData.endTime = this.endTime
			this.formData.scene = this.formData.scene
			this.getVehicleConsume();
		},
		methods: {
			confirm(e) {
			},
			getVehicleColor,
			upper: function(e) {

			},
			scrolltolower: function(e) {
				if (this.flag) return;
				let self = this;
				setTimeout(function() {
					self.formData.pageNum = self.formData.pageNum + 1;
					self.getVehicleConsume();
				}, 500)
			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop;
			},
			supplementaryPayment() {
				let nextData = {
					vehicle_code: this.vehicleInfo.vehicle_code,
					vehicle_color: this.vehicleInfo.vehicle_color,
				}
				uni.navigateTo({
					url: '/pagesC/afterPay/recordDetail/recordDetail?nextData=' + encodeURIComponent(JSON
						.stringify(nextData))
				})
				return;
			},
			searchTable() {
				this.formData.pageNum = 1
				this.flag = false
				this.getVehicleConsume()
			},
			getVehicleConsume() {
				this.currentConsumeList = this.formData.pageNum == 1 ? [] : this.currentConsumeList;
				this.$request.post(this.$interfaces.getVehicleConsume, {
					data: this.formData
				}).then(res => {
					if (res.code == 200) {
						if (this.formData.pageNum == 1) {
							this.totalCount = res.data.consumeViewList.total
							this.totalAmount = res.data.totalAmount
						}
						let list = res.data.consumeViewList.records
						list.forEach((item, index) => {
							if (item.scene == 2) { //停车场
								for (let key in item.vehicleParkingView) {
									if (key != 'scene') {
										item[key] = item.vehicleParkingView[key]
									}
								}
							}
							if (item.scene == 3) { //加油站
								for (let key in item.vehicleStationView) {
									if (key != 'scene') {
										item[key] = item.vehicleStationView[key]
									}
								}
							}
							if (item.scene == 1) { //高速
								for (let key in item.vehiclePassView) {
									if (key != 'scene') {
										item[key] = item.vehiclePassView[key]
									}
								}
							}
						})

						this.currentConsumeList = this.currentConsumeList.concat(list)
						if (this.currentConsumeList.length == res.data.consumeViewList.total) {
							this.flag = true
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch(err => {
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			confirm(obj) {
				this.sceneName = obj[0].label;
				this.formData.scene = obj[0].value;
			},
			goDetail(item) {
				uni.navigateTo({
					url: '/pagesB/cardBusiness/expenseRecord/record-detail?id=' + item.transactionId
				})
			},
			startDateChange(e) {
				this.strartTime = e.detail.value
				this.formData.strartTime = e.detail.value
			},
			endDateChange(e) {
				this.endTime = e.detail.value
				this.startDate = dayjs(new Date(this.endTime).getTime() - 365 * 24 * 60 * 60 * 1000).format('YYYY-MM')

				//如果选择结束日期时，结束日期小于开始日期，默认开始日期为前一个月。否则如果开始日期大于结束日期的前一年，默认开始日期为结束日期前一年
				this.strartTime = (this.endTime > this.strartTime) ? (this.startDate > this.strartTime ? this.startDate :
					this.strartTime) : dayjs(new Date(this.endTime).getTime() - 30 * 24 * 60 * 60 * 1000).format(
					'YYYY-MM')
				this.formData.endTime = e.detail.value
			},
			filterDict(val) {
				let payStatusObj = {
					"0": '扣款中',
					"1": '扣款成功',
					"2": '扣款失败',
					"3": '待补缴',
					"4": '补缴成功',
					"9": '月底统一扣款'
				}
				return payStatusObj[val] || ''
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},

		},
		destroyed() {

		}
	};
</script>
<style lang="scss" scoped>
	.sellPay-container {
		// height: calc(100vh - var(--safe-area-inset-bottom));
		width: 100%;
		height: 100%;
		position: relative;

		.search {
			background-color: #FFFFFF;

			.search-btn {
				display: flex;
				justify-content: center;
				justify-content: space-around;
				padding-top: 17rpx;
				padding-bottom: 22rpx;

				/deep/.cu-btn {
					width: 690rpx;
					height: 84rpx;
					background: #0066E9;
					border-radius: 10rpx;
					font-size: 34rpx;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 26rpx;
				}
			}

			.search-form-item {
				margin: 0 30rpx;
				border-bottom: 1rpx solid #E9E9E9;
				line-height: 80rpx;
				height: 80rpx;
				position: relative;

				.title {
					color: #888;
					font-size: 30rpx;
					width: 230rpx;
				}

				.picker {
					width: calc(100% - 230rpx);
					display: flex;
					height: 100%;
				}

				/deep/.u-border-bottom::after {
					border-bottom: none;
				}

				.pick-date {
					width: 142rpx;
					display: flex;

					/deep/.u-cell {
						position: relative;
					}

					/deep/.u-cell__value {
						font-size: 30rpx !important;
					}

					/deep/.u-cell__left-icon-wrap {
						position: absolute;
						right: 0;
						margin-right: 0px !important;
					}

					/deep/.u-icon__icon {
						font-size: 25rpx !important;
						color: #999999;
					}
				}

				.pick-date-two {
					// flex: 1;
				}

				/deep/.u-cell {
					padding: 0 0;
					line-height: 80rpx;
				}

				/deep/.u-cell__value {
					color: #333;
					text-align: left;
					font-size: 30rpx;
				}
			}
		}

		.card-Info {
			// margin-top: 20rpx;
			padding-bottom: 22rpx;

			.card {
				margin: 0 32rpx 21rpx 32rpx;
				border-radius: 10rpx;
				height: 100%;
				background: #FFFFFF;
				border-radius: 10rpx;
				padding: 25rpx 25rpx 22rpx;

				.card-top {
					display: flex;
					line-height: 56rpx;
					margin-bottom: 26rpx;

					.icon {
						width: 56rpx;
						height: 56rpx;
						background-size: 100%;
					}

					.deduction {
						margin-left: 12rpx;
						height: 28rpx;
						font-size: 32rpx;
						font-weight: 500;
						color: #333333;
						flex: 1;

						.unit {
							color: #EA3030;
							font-size: 24rpx;
							margin-left: 7rpx;
						}

						.money {
							color: #EA3030;
						}
					}

					.status {
						min-width: 104rpx;
						padding: 0 9px;
						height: 44rpx;
						background: rgba(56, 116, 255, 0.1);
						border-radius: 7rpx;
						text-align: center;
						line-height: 44rpx;

						.name {
							color: #3874FF;
							font-size: 26rpx;
							font-weight: 400;
						}
					}

					.status0 {
						background: rgba(56, 116, 255, 0.1);

						.name0 {
							color: #3874FF;
						}
					}

					.status1,
					.statu4 {
						background: rgba(43, 166, 80, 0.1);

						.name1,
						.name4 {
							color: #2BA650;
						}
					}

					.status2 {
						background: rgba(255, 84, 84, 0.1);

						.name2 {
							color: #FF5454;
						}
					}

					.status3,
					.status9 {
						background: rgba(106, 105, 105, 0.1);

						.name3,
						.name9 {
							color: #6A6969;
						}
					}
				}

				.card-middle {
					display: flex;
					width: 100%;
					height: 37rpx;
					font-size: 26rpx;
					font-weight: 400;
					color: #666666;
					line-height: 37rpx;
					margin-bottom: 26rpx;
				}

				.dash-line {
					background-image: linear-gradient(to right, #c3c3c3 0%, #c3c3c3 50%, transparent 50%);
					background-size: 9px 1px;
					background-repeat: repeat-x;
					width: 100%;
					height: 1px;
				}

				.card-btn {
					width: 100%;
					margin-top: 22rpx;
					display: flex;
					justify-content: space-around;
					position: relative;

					.btn {
						width: 168rpx;
						border-radius: 30rpx;
						height: 50rpx;
						border: 1px solid #C6C6C6;
						font-size: 26rpx;
						font-weight: 400;
						color: #999999;
						line-height: 50rpx;
						text-align: center;
					}

					.btn1 {
						border: 1px solid #0066E9;
						font-weight: 400;
						color: #0066E9;
						position: absolute;
						right: 0;
					}
				}
			}
		}

		.no-data {
			width: calc(100% - 60rpx);
			text-align: center;
			background: #fff;
			margin-left: 30rpx;
			border-radius: 10rpx;
			height: 264rpx;

			.no-data-img {
				width: 135rpx;
				height: 178rpx;
				background-size: 100%;
				margin-top: 26rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}

		.total {
			width: 100%;
			display: flex;
			padding: 20rpx 30rpx;

			.total-recharge {
				font-size: 30rpx;
				flex: 1;

				span {
					color: #EA3030;
				}
			}

			.frequency {
				font-size: 30rpx;

				span {
					color: #EA3030;
				}
			}
		}
	}
</style>