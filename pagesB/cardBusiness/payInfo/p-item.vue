<template>
	<view class="cu-card article margin-top bg-white order-item">
		<view class="cu-item padding-top item-hd" style="padding-bottom: 0px;">
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text
						class="text-bold">交易金额：{{moneyFilter(info.pay_toll)}}元</text></view>

			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item">
					<text>入口站点：</text>{{info.en_station?info.en_station:''}}
				</view>
				<view class="item-text padding-left g-flex-item">
					<text>出口站点：</text>{{info.ex_station?info.ex_station:''}}</view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>入口时间：</text>{{info.en_time?info.en_time:''}}
				</view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>出口时间：</text>{{info.ex_time?info.ex_time:""}}
				</view>
			</view>
		</view>
		<view class="padding-lr  margin-bottom order-item-btn">
			<view class="color-btn">

			</view>
		</view>
		<tLoading :isShow="isShowLoding" />

	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getPayStatus
	} from '@/common/method/filter'
	export default {
		props: {
			info: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {

				isShowLoding: false,
				schoolDetail: {

				},
			}
		},
		watch: {

		},
		components: {
			tLoading
		},
		computed: {

		},
		created() {

		},
		methods: {
			getPayStatus,
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>
<style lang="scss" scoped>
	.order-item-btn {
		.color-btn {
			padding: 10upx 0;
			width: 100%;

			/deep/.cu-btn {
				min-width: 120rpx;
				height: 56rpx;
				opacity: 1;
				font-size: 26rpx;
			}
		}

	}

	.case-item {
		border-radius: 10upx;
	}

	.case-item .item-hd .item-hd__box {
		padding-bottom: 30rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
	}

	.view-overflow-hide {
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		line-clamp: 3;
		-webkit-box-orient: vertical;
	}



	.cu-card>.cu-item {
		margin: 0;
	}

	.item-text {
		line-height: 46upx;
	}

	.item-text-sub {
		margin-top: 10upx;
		line-height: 35upx;
	}

	.animation {
		transition-property: all;
		transition-duration: 0.5s;
		transition-timing-function: ease;
	}
</style>
