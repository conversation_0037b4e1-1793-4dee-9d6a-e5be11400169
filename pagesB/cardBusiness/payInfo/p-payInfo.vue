<template>
	<view class="sellPay-container">

		<view class="scroll-box">
			<scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" :lower-threshold='lowerThreshold'
				@scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
				<view v-if="cardPayInfoList.length>0">
					<view class="cu-form-group">
						<view class="title">交易总金额:</view>
						<view>{{moneyFilter(sumMoney)+' 元'}}</view>
						<view class="title">交易数量:</view>
						<view class="">
							{{cardPayInfoList.length+' 笔'}}
						</view>
					</view>
				</view>
				<view class="sellPay-Info search">
					<view class="c-title">流水查询</view>
					<view class="cu-form-group">
						<view class="title">开始日期</view>
						<picker mode="date" v-model="start_date" @change="startDateChange">
							<view class="picker">
								{{start_date}}
							</view>
						</picker>
					</view>
					<view class="cu-form-group">
						<view class="title">结束日期</view>
						<picker mode="date" v-model="end_date" @change="endDateChange">
							<view class="picker">
								{{end_date}}
							</view>
						</picker>
					</view>
					<view class="search-btn padding-tb-sm">
						<button class="cu-btn bg-topic" @tap="onSearchHandle">查询</button>
					</view>
				</view>

				<view class="sellPay-Info">
					<view class="c-title">用户信息</view>
					<form>
						<view class="cu-form-group">
							<view class="title">用户名称:</view>
							<input placeholder="用户名称" :value="customerInfo.customer_name" name="a" disabled></input>
						</view>
						<view class="cu-form-group">
							<view class="title">车牌号码:</view>
							<input placeholder="车牌号码" :value="vehicleInfo.vehicle_code" name="a" disabled></input>
						</view>
						<view class="cu-form-group">
							<view class="title">ETC卡号:</view>
							<input placeholder="ETC卡号" :value="vehicleInfo.cpu_card_id" name="a" disabled></input>
						</view>
					</form>
				</view>
				<item style="height: 100px;" v-for="(item,index) in cardPayInfoList" :key='index' :info='item'>
				</item>
				<load-more :loadStatus="noticeLoadStatus" />
			</scroll-view>
		</view>

		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import item from './p-item.vue'
	import loadMore from '../../components/load-more/index.vue';
	import float from '@/common/method/float.js'
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		components: {
			TButton,
			item,
			tLoading,
			loadMore
		},
		data() {
			return {
				isLoading: false,
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				cardAmount: {},
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				cardPayInfoList: [],
				start_date: '',
				end_date: '',
				sumMoney:0,
				formData: {
					"customer_id": "",
					"cpu_card_id": "",
					"vehicle_color": "",
					"vehicle_code": "",
					"pay_start_date": "",
					"pay_end_date": ""
				}
			};
		},
		onLoad(options) {

		},

		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},


		},
		filters: {

		},
		created() {
			 console.log(float.add(10, 100));
			this.formData.customer_id = this.vehicleInfo.customer_id
			this.formData.cpu_card_id = this.vehicleInfo.cpu_card_id
			this.formData.vehicle_color = this.vehicleInfo.vehicle_color
			this.formData.vehicle_code = this.vehicleInfo.vehicle_code
			this.formData.pay_start_date = this.formatHandle(new Date().getTime(), "yyyyMMdd")
			this.formData.pay_end_date = this.formatHandle(new Date().getTime(), "yyyyMMdd")
			this.start_date = this.formatHandle(new Date().getTime(), "yyyy-MM-dd")
			this.end_date = this.formatHandle(new Date().getTime(), "yyyy-MM-dd")
			this.cardPayInfo();
			console.log(dayjs('2019-09-09 20:20:20.0').format('YYYY-MM-DD HH:mm'),'111')
		},
		methods: {
			foramtDate(time, fmt){
				time = time.replace('.0', '');
				  let value = time && time.replace(/-/g, '/');
				  let getDate = new Date(value);
				  let o = {
				   'M+': getDate.getMonth() + 1,
				   'd+': getDate.getDate(),
				   'h+': getDate.getHours(),
				   'm+': getDate.getMinutes(),
				   's+': getDate.getSeconds(),
				   'q+': Math.floor((getDate.getMonth() + 3) / 3),
				   'S': getDate.getMilliseconds()
				  };
				  if (/(y+)/.test(fmt)) {
				   fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))
				  }
				  for (let k in o) {
				   if (new RegExp('(' + k + ')').test(fmt)) {
				    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
				   }
				  }
				  return fmt;
			},
			formatHandle(time, format) {
				var t = new Date(time);
				var tf = function(i) {
					return (i < 10 ? '0' : '') + i
				};
				console.log(t,'formatHandle')
				return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
					switch (a) {
						case 'yyyy':
							return tf(t.getFullYear());
							break;
						case 'MM':
							return tf(t.getMonth() + 1);
							break;
						case 'mm':
							return tf(t.getMinutes());
							break;
						case 'dd':
							return tf(t.getDate());
							break;
						case 'HH':
							return tf(t.getHours());
							break;
						case 'ss':
							return tf(t.getSeconds());
							break;
					}
				})
			},
			onSearchHandle() {
				this.formData.pay_start_date = this.formatHandle(new Date(this.start_date).getTime(), "yyyyMMdd")
				this.formData.pay_end_date = this.formatHandle(new Date(this.end_date).getTime(), "yyyyMMdd")
				this.cardPayInfo();
			},
			startDateChange(e) {
				this.start_date = e.detail.value
			},
			endDateChange(e) {
				this.end_date = e.detail.value
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {


			},
			scroll: function(e) {

				this.old.scrollTop = e.detail.scrollTop;

			},
			cardPayInfo() {
				this.isLoading = true;
				let params = {
					routePath: this.$interfaces.cardPayInfo.method,
					bizContent: this.formData
				}
				this.noticeLoadStatus = 1;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					console.log(res);
					this.isLoading = false;
					if (res.code == 200) {
						this.cardPayInfoList=[]
						this.sumMoney=0
						if (res.data && res.data.length) {
							this.cardPayInfoList = res.data;
							for (let i = 0; i < this.cardPayInfoList.length; i++) {
								if (this.cardPayInfoList[i].en_time) {
									this.cardPayInfoList[i].en_time=dayjs(this.cardPayInfoList[i].en_time).format('YYYY-MM-DD HH:mm')
								}
								if (this.cardPayInfoList[i].ex_time) {
									this.cardPayInfoList[i].ex_time=dayjs(this.cardPayInfoList[i].ex_time).format('YYYY-MM-DD HH:mm')
								}
							}
							 this.cardPayInfoList.map(item=>{
								return this.sumMoney=float.add(item.pay_toll,this.sumMoney)
							})
							this.noticeLoadStatus = 3;
						} else {
							this.noticeLoadStatus = 0;
						}
					} else if (res.code == 401) {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.reLaunch({
										url: '/pagesD/login/p-login'
									});
								}
							}
						});

					} else {
						this.noticeLoadStatus = 2;
					}

				}).catch(() => {
					this.noticeLoadStatus = 2;
					this.isLoading = false;
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
		destroyed() {

		}
	};
</script>
<style lang="scss" scoped>
	.search {
		background-color: #FFFFFF;

		.search-btn {
			display: flex;
			justify-content: center;
			justify-content: space-around;

			/deep/.cu-btn {
				width: 50%;
				height: 64rpx !important;
				border-radius: 8rpx;
				font-size: 28rpx;
			}

		}
	}

	.sellPay-Info {
		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.sellPay-Info .cu-form-group radio-group {

		flex: 1;
		text-align: left;
	}
</style>
