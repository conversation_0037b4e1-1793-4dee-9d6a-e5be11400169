//登录回调跳转
let onLoginCallbackHandle = () => {
	// 判断是否存在重定向地址
	if (uni.getStorageSync('redirectPath')) {
		uni.redirectTo({
			url: uni.getStorageSync('redirectPath')
		})
		uni.removeStorageSync('redirectPath');
		return
	}
	uni.redirectTo({
		url: "/pages/home/<USER>/p-home?type=login"
	})
}
//登录页无需删除storge
let PERMANENTSTORAGE = ['loginAccount','attentionStatus','redirectPath','advertisementShowTime']
export {
	onLoginCallbackHandle,
	PERMANENTSTORAGE
}
