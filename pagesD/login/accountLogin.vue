<template>
	<view class="account-login">

		<view class="GX-logo">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc-logo.png"
				class="home-img"></image>
			<view class="login-title">桂小通(广西捷通)</view>
		</view>
		<view class="login-bd">
			<!-- 验证码登录 -->
			<view class="input-text" v-if='loginType==1'>
				<view class="cu-form-group">
					<input placeholder="请输入手机号码" name="mobile" v-model='identity.mobile'></input>
				</view>
				<view class="cu-form-group" v-if="userTypeIndex=='2'">
					<input placeholder="请输入证件号" name="mobile" v-model='identity.licenceNo'></input>
				</view>
				<!-- 				<view class="cu-form-group login-form-group">
					<input placeholder="请输入图形验证码" name="mobileCode" v-model='mobileCode'></input>
					<image :src="codeUrl" v-show="codeUrl" class="code-img" @click="getCaptcha">
				</view> -->
				<view class="cu-form-group">
					<input placeholder="请输入短信验证码" name="smsCode" v-model='identity.mobileCode'></input>
					<text class="sendSMS" @click="showCodeHandle">{{smsName}}</text>
				</view>
			</view>
			<!-- 账号密码登录 -->
			<view class="input-text" v-if='loginType==0'>
				<view class="" v-if="userTypeIndex=='1'">
					<view class="cu-form-group">
						<input placeholder="请输入手机号码" name="loginName" v-model='formData.loginName'></input>
					</view>
					<view class="cu-form-group">
						<input placeholder="请输入密码" name="password" password v-model='formData.password'></input>
					</view>
				</view>
				<view class="" v-else>
					<view class="cu-form-group">
						<input placeholder="请输入用户名" name="loginName" v-model='formData1.loginName'></input>
					</view>
					<view class="cu-form-group">
						<input placeholder="请输入密码" name="password" password v-model='formData1.password'></input>
					</view>
				</view>
			</view>
			<view class="g-flex g-flex-justify g-flex-align-center" style="margin-top: 30rpx;">
				<view class="g-flex g-flex-align-center remember">
					<checkbox-group @change="checkboxChange" v-if='loginType==0'>
						<checkbox class="cyan checked remember-check" value='checked' :checked='isRemember' />
					</checkbox-group>

					<view class="remember-des" v-if='loginType==0'>
						记住密码
					</view>
				</view>
				<radio-group @change="radioChange" class="g-flex g-flex-align-center" style="padding-top: 20upx;">
					<label class="uni-list-cell uni-list-cell-pd " v-for="(item, index) in userType" :key="item.value">
						<view>
							<radio :value="item.value" class="cyan" style="transform: scale(0.7)"
								:checked="index==userTypeIndexSub" />
							<text style="margin: 0 10upx;">{{item.name}}</text>
						</view>

					</label>
				</radio-group>
			</view>
			<view class='btn' style="margin-top: 30rpx;">
				<TButton title="登录" class="login-btn" @clickButton="loginHandle" />
			</view>


			<view class="resetPwd g-flex g-flex-align-center g-flex-justify">
				<text class="resetPwd-text" @click="goRegisterHandle">
					注册新用户
				</text>
				<text v-if="loginType==0" class="resetPwd-text" @click="resetPwdHandle">
					忘记密码
				</text>
			</view>
		</view>

		<!-- 其他登录方式 -->
		<view class="other-login login-ft">
			<view class="login-title">
				其他登录方式
			</view>
			<view class="other-login_bd g-flex-center g-flex">
				<view class="other-login_item" @click="goWechatLogin">
					<image class="login-img" src="../../static/etc/wxLogin.png" mode=""></image>
					<view class="desc">
						微信登录
					</view>
				</view>
				<view v-if="loginType==0" class="other-login_item" @click="loginTypeHandle('sms')">
					<image class="login-img" src="../../static/etc/login/sms-icon.png" mode=""></image>
					<view class="desc">
						短信验证码登录
					</view>
				</view>
				<view v-else class="other-login_item" @click="loginTypeHandle('account')">
					<image class="login-img" src="../../static/etc/login/pwd-icon.png" mode=""></image>
					<view class="desc">
						密码登录
					</view>
				</view>

			</view>
		</view>
		<TModal :showModal='dialogVisible' @cancelModal='cancelModal' @okModal='onConfirmHandle' okText='绑定'>
			<view slot='content' class="bind-Info">

				<view class="des">绑定微信需要认证您的账号密码</view>
				<view class="cu-form-group login-form-group">
					<input placeholder="请输入密码" name="password" password v-model='formData.password'></input>
				</view>

			</view>
		</TModal>
		<!-- H5公众号绑定弹窗 -->
		<TModal :showModal='publicBindVisible' @cancelModal='publicBindVisible=false' @okModal='goPublicBindHandle'
			okText='绑定'>
			<view slot='content' class="bind-bd">
				<view class="bind-bd__des">绑定微信需要认证您的账号密码</view>
			</view>
		</TModal>

		<t-captcha id="captcha" app-id="*********" @verify="handlerVerify" @ready="handlerReady" @close="handlerClose"
			@error="handlerError" />
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import store from '@/store/index.js';
	import JSEncrypt from '@/js_sdk/jsencrypt/jsencrypt';
	import {
		setLoginUserInfo,
		setTicket,
		setMd5Key,
		setAesKey,
		getTokenId,
		setTokenId,
		setMustFresh,
		setCurrUserInfo,
		setAccountId,
		setPhone,
		setStore,
		removeStore,
		getStore,
		setOpenid,
		getTicket,
		getMd5Key,
		getAesKey
	} from '@/common/storageUtil.js';
	import tLoading from '@/components/common/t-loading.vue';
	import util from "@/common/util.js"
	import TModal from '@/components/t-modal/t-modal.vue'
	import TButton from "@/components/t-button.vue"
	import float from '@/common/method/float.js'
	import {
		getErrorMessage
	} from '@/common/method/filter.js'
	import {
		checkPhone
	} from '@/common/method/validater.js'
	import {
		onLoginCallbackHandle,
		PERMANENTSTORAGE
	} from './login.js';
	export default {
		components: {
			tLoading,
			TButton,
			TModal
		},
		data() {
			return {
				userType: [{
						value: '1',
						name: '个人'
					},
					{
						value: '2',
						name: '单位'
					}
				],
				loginType: 0, // 登录类型 验证码登录0  密码登录1
				time: null,
				smsName: '发送验证码',
				isLoading: false,
				mobileCode: '', // 图形验证码
				codeUrl: '', // 图形验证码连接
				formData: {
					loginName: '',
					password: '',
				},
				formData1: {
					loginName: '',
					password: '',
				},
				identity: {
					mobile: '',
					mobileCode: '',
					userType: '1',
					licenceNo: '',
				},
				tokenId: '',
				captchaId: '',
				opunID: {},
				isRemember: true, // 记住密码.
				wechatInfo: {}, // 获取小程序信息
				publicOpenId: '', // 公众号OpenId
				publicCode: '', // 公众号Code
				wechatCode: '', //微信小程序code
				text: '微信授权登录',
				mobile: '',
				openId: '',
				userTypeIndex: '1',
				userTypeIndexSub: '0',
				dialogVisible: false,
				publicBindVisible: false,
				codeTicket: '' //腾讯验证码
			}
		},
		watch: {
			publicBindVisible(val) {
				if (!val) {
					sessionStorage.setItem('authorizetype', '');
				}
			}
		},
		onLoad(option) {
			this.loginType = option.loginType || '';
			if (this.loginType == 1) {
				this.getCaptcha();
			}
			this.resetStorageHandle();

			// #ifdef H5
			if (this.isWeiXin()) {
				setOpenid(this.getWXOpenId() || '');
				if (this.getWXOpenId() && sessionStorage.getItem('authorizetype') == 'login') {
					this.getBindLogin();
				}
				if (!this.getWXOpenId()) {
					this.gowxauthorize('')
				}
			}
			// #endif
		},
		created() {


		},
		methods: {
			showCodeHandle() {
				if (this.verifyMobile()) {
					uni.showToast({
						title: this.verifyMobile(),
						icon: "none"
					})
					return;
				}
				this.selectComponent('#captcha').show()
				// 进行业务逻辑，若出现错误需重置验证码，执行以下方法
				// if (error) {
				// this.selectComponent('#captcha').refresh()
				// }
			},
			// 验证码验证结果回调
			handlerVerify(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
				if (ev.detail.ret === 0) {
					// 验证成功
					this.codeTicket = ev.detail.ticket
					console.log('ticket:', ev.detail.ticket)
					this.sendSMS()
				} else {
					// 验证失败
					// 请不要在验证失败中调用refresh，验证码内部会进行相应处理
				}
			},
			// 验证码准备就绪
			handlerReady() {
				console.log('验证码准备就绪')
			},
			// 验证码弹框准备关闭
			handlerClose(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
				if (ev && ev.detail.ret && ev.detail.ret === 2) {
					console.log('点击了关闭按钮，验证码弹框准备关闭');
				} else {
					console.log('验证完成，验证码弹框准备关闭');
				}
			},
			// 验证码出错
			handlerError(ev) {
				console.log(ev.detail.errMsg)
			},
			isWeiXin() {
				var ua = window.navigator.userAgent.toLowerCase();
				return ua.match(/MicroMessenger/i) == 'micromessenger';
			},
			gowxauthorize(type) {
				sessionStorage.setItem('authorizetype', type || '');
				if (this.getWXOpenId()) {
					this.getBindLogin();
					return;
				}
				sessionStorage.setItem('redirect_uri', '/pagesD/login/p-login')
				uni.redirectTo({
					url: "/pages/wxauthorize/wxauthorize"
				})
			},
			getWXOpenId() {
				return sessionStorage.getItem('publicOpenId')
			},
			getBindLogin() {
				let _this = this;
				let params = {
					openId: sessionStorage.getItem('publicOpenId')
				}
				this.$request.post(this.$interfaces.bindLogin, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.publicOpenId = res.data.publicOpenId
						if (!res.data.isBind) {
							this.publicBindVisible = true;
						}
						if (res.data.loginRes && res.data.isBind && sessionStorage.getItem('authorizetype') ==
							'login') {
							this.setLoginInfo(res.data.loginRes)
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg + '【错误码：' + res
								.code + '】'
						});
					}
				}).catch(err => {

				})
			},
			// 公众号账号绑定页面
			goPublicBindHandle() {
				uni.navigateTo({
					url: '/pages/bind/bind?openId=' + this.publicOpenId
				});
			},
			resetStorageHandle() {
				let loginAccount = '';
				try {
					loginAccount = uni.getStorageSync('loginAccount');
					if (loginAccount.userType == '2') {
						this.formData1.password = loginAccount ? loginAccount.password : ''
						this.formData1.loginName = loginAccount ? loginAccount.loginName : ''
					} else {
						this.formData.password = loginAccount ? loginAccount.password : ''
						this.formData.loginName = loginAccount ? loginAccount.loginName : ''
					}

				} catch (e) {
					// error
				}
				// 清除本地登录信息
				try {
					const res = uni.getStorageInfoSync();
					if (res && res.keys && res.keys.length) {
						let keys = res.keys;
						for (let i = 0; i < keys.length; i++) {
							if (!PERMANENTSTORAGE.includes(keys[i])) {
								uni.removeStorageSync(keys[i]);
							}

						}
					}
				} catch (e) {

				}
			},
			loginTypeHandle(val) {
				if (val == 'account') {
					this.loginType = 0
				} else {
					this.loginType = 1
					this.getCaptcha();
				}
			},
			radioChange(e) {
				this.userTypeIndex = e.detail.value
				this.userTypeIndexSub = float.sub(e.detail.value, 1)
			},
			// 微信手机号授权登录
			wxLogin(e) {
				let _self = this;
				if (e.detail.errMsg === "getPhoneNumber:ok") {
					_self.wechatInfo = e.detail;
					uni.checkSession({
						fail() {
							console.log('uni.checkSession')
							_self.getWechatCode()
						},
						complete() {
							console.log('uni.checkSessioncomplete')
							_self.getWeChatPhone()
						}
					})
				}
			},
			// 解密手机号
			getWeChatPhone() {

				let _self = this;
				this.isLoading = true;
				let params = {
					"encryptedData": _self.wechatInfo.encryptedData,
					"iv": _self.wechatInfo.iv,
					code: _self.wechatCode
				}

				_self.$request.post(_self.$interfaces.getWeChatPhone, {
					data: params
				}).then((res) => {
					this.isLoading = false;
					this.getWechatCode();
					if (res.code == 200) {
						_self.mobile = res.data.mobile
						_self.openId = res.data.openId
						setOpenid(res.data.openId)
						//未注册提示注册
						let paramsId = {
							mobile: res.data.mobile,
							openId: res.data.openId,
							unionId: res.data.unionId
						}
						if (!res.data.isRegister) {
							uni.showModal({
								title: "提示",
								content: '该手机号未注册，是否去注册',
								showCancel: true,
								success: function(res) {
									if (res.confirm) {
										uni.navigateTo({
											url: "/pagesD/register/p-register?paramsId=" +
												JSON.stringify(paramsId)
										});
									}
								}
							});
							return
						}
						if (res.data.isBind && !res.data.isBindPulic) {
							_self.getOpenPublicId();
						}

						// 未绑定提示绑定
						if (!res.data.isBind) {
							this.dialogVisible = true;
							return
						}
						_self.getWeChatOpenId()
					} else {
						uni.showModal({
							title: "提示",
							content: (getErrorMessage(res.code) || res.msg) + '【错误码：' +
								res.code + '】',
							showCancel: false,
						});
					}
				}).catch(error => {
					this.getWechatCode();
					this.isLoading = false;
				})
			},
			// 获取微信登录code
			getWechatCode() {
				let _self = this;
				wx.login({
					success: (res) => {
						_self.wechatCode = res.code;
						console.log(res.code);
					}
				})
			},
			// 获取微信小程序openID
			getWeChatOpenId() {
				let _self = this;
				_self.isLoading = true;
				let params = {
					openId: this.openId,
					mobile: this.mobile,
				}
				_self.$request.post(_self.$interfaces.wxLogin, {
					data: params
				}).then((res) => {
					console.log(res, '获取小程序openid');
					_self.isLoading = false;
					if (res.code == 200) {
						if (res.data && res.data) {
							_self.loginInfo = res.data;
							_self.setLoginInfo(res.data);

						}
					} else {
						uni.showModal({
							title: "提示",
							content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res
								.code + '】',
							showCancel: false,
						});
					}
				}).catch((e) => {
					_self.isLoading = false;
				})

			},
			// 获取公众号openID
			getOpenPublicId() {
				if (!this.publicCode) {
					return;
				}
				let _self = this;
				let params = {
					code: this.publicCode,
					appId: this.$publicAppId,
					type: '1'
				}
				_self.$request.post(_self.$interfaces.getOpenid, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.publicOpenId = res.data.openid
						_self.relationPublic();
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch((error) => {

				})
			}, // 关联公众号和微信openID
			relationPublic() {
				let _self = this;
				let params = {
					"mobile": _self.mobile,
					"publicOpenId": _self.publicOpenId,
					"unionId": _self.loginInfo.unionId
				}
				_self.$request.post(_self.$interfaces.relationPublic, {
					data: params
				}).then(res => {
					console.log(res);
				}).catch(error => {
					console.log(error)
				})
			},
			// 输入密码绑定
			onConfirmHandle() {
				if (!this.formData.password) {
					uni.showToast({
						title: '请输入密码',
						icon: "none"
					})
					return;
				}
				let _self = this;
				let dealParams = JSON.parse(JSON.stringify(this.formData));
				var encrypt = new JSEncrypt()
				encrypt.setPublicKey(this.$publicKey)
				dealParams.password = encrypt.encrypt(dealParams.password)
				this.isLoading = true;
				let params = {
					mobile: _self.mobile,
					openId: _self.openId,
					...dealParams
				}
				_self.$request.post(_self.$interfaces.bindUser, {
					data: params
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						_self.setLoginInfo(res.data);
						_self.getOpenPublicId();
					} else {
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
				})
			},
			cancelModal() {
				this.dialogVisible = false;
			},

			setLoginInfo(result) {
				setAesKey(result.aesKey)
				setMd5Key(result.md5Key)
				setTicket(result.ticket)
				setLoginUserInfo(result.data);
				// #ifdef H5
				sessionStorage.setItem('authorizetype', '');
				// #endif

				onLoginCallbackHandle()
			},


			checkboxChange(e) {
				this.isRemember = !!e.detail.value.length;
			},
			// 发送图形验证码
			getCaptcha() {
				let params = {
					// mobile:this.identity.mobile
				}
				this.$request.post(this.$interfaces.getCaptcha, {

				}).then(res => {
					if (res.code == 200) {
						this.codeUrl = res.data.image
						this.captchaId = res.data.captchaId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {

				})
			},
			goRegisterHandle() {
				uni.navigateTo({
					url: "/pagesD/register/p-register"
				})
			},
			// 验证手机号码
			verifyMobile() {
				let msg = '';

				if (checkPhone(this.identity.mobile)) {
					msg = '请输入合法手机号码';
					return msg;
				}
				if (this.userTypeIndex == '2') {
					if (!this.identity.licenceNo) {
						msg = '请输入证件号';
						return msg;
					}
					return
				}

				// if (!this.mobileCode) {
				// 	msg = '请输入图形验证码'
				// 	return msg;
				// }
				return msg;
			},
			loginHandle() {
				if (this.loginType == 1) {
					this.smsLoginHandle();
					return;
				}
				this.accountLoginHandle();
			},
			// 账号密码登录
			accountLoginHandle() {
				if (this.userTypeIndex == '2') {
					if (!this.formData1.loginName) {
						uni.showToast({
							title: '请输入用户名',
							icon: "none"
						})
						return;
					}
					if (!this.formData1.password) {
						uni.showToast({
							title: '请输入密码',
							icon: "none"
						})
						return;
					}

				} else {
					if (!this.formData.loginName) {
						uni.showToast({
							title: '请输入手机号码',
							icon: "none"
						})
						return;
					}
					if (checkPhone(this.formData.loginName)) {
						uni.showToast({
							title: '请输入正确手机号码',
							icon: "none"
						})
						return;
					}
					if (!this.formData.password) {
						uni.showToast({
							title: '请输入密码',
							icon: "none"
						})
						return;
					}
				}
				let params = ''
				if (this.userTypeIndex == '2') {
					params = JSON.parse(JSON.stringify(this.formData1));
					params.userType = '2'
				} else {
					params = JSON.parse(JSON.stringify(this.formData));
					params.userType = '1'
				}
				var encrypt = new JSEncrypt()
				encrypt.setPublicKey(this.$publicKey)
				let loginAccount = '';
				if (this.isRemember) {
					loginAccount = this.userTypeIndex == '1' ? this.formData : this.formData1
					loginAccount.userType = this.userTypeIndex == '1' ? '1' : '2'
				}
				try {
					uni.setStorageSync('loginAccount', loginAccount)
				} catch (e) {

				}
				params.password = encrypt.encrypt(params.password)
				this.isLoading = true;
				this.$request.post(this.$interfaces.accountLogin, {
					data: params
				}).then(res => {

					this.isLoading = false;
					if (res.code == 200 && res.data) {
						let result = res.data ? res.data : '';
						if (result) {
							setAesKey(result.aesKey)
							setMd5Key(result.md5Key)
							setTicket(result.ticket)
							setLoginUserInfo(result.data);
							onLoginCallbackHandle()


						}
					} else {
						uni.showModal({
							title: "提示",
							content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res.code + '】',
							showCancel: false,
							success() {
								if (res.code == 7022) {
									uni.navigateTo({
										url: "/pagesC/resetPwd/p-resetPwd?handle=setPwd&mobile=" +
											params.loginName
									})
								}
							}

						});
					}
				}).catch((error) => {
					uni.showModal({
						title: "提示",
						content: (getErrorMessage(error.code) || error.msg) + '【错误码：' + error.code + '】',
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			// 验证验证码
			verifyCode() {
				let msg = '';
				if (!this.identity.mobileCode) {
					msg = '请输入验证码'
				}
				return msg;
			},
			//发送短信
			sendSMS() {
				if (!this.time) {
					this.isLoading = true;
					let countdown = 60
					let params = {
						mobile: this.identity.mobile,
						// mobileCode: this.mobileCode,
						// captchaId: this.captchaId,
						ticket: this.codeTicket,
						userType: this.userTypeIndex == '1' ? '1' : '2',
						autoValidLogin: this.userTypeIndex == '1' ? '2' : '1', // 2023年2月21日 付工提出 验证码无需验证登录

					};
					if (this.userTypeIndex == '2') {
						params.licenceNo = this.identity.licenceNo
					}
					this.$request.post(this.$interfaces.sendaAccountSms, {
						data: params
					}).then(res => {
						console.log(res);
						this.isLoading = false
						if (res.code == '200') {
							this.time = setInterval(() => {
								countdown = countdown - 1
								this.smsName = countdown + "秒后重新发送"
								if (countdown === 0) {
									clearInterval(this.time)
									this.time = null
									this.smsName = "重新发送"
								}
							}, 1000)
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
								// success: (res) => {
								// 	if (res.confirm) {
								// 		this.getCaptcha()
								// 	}
								// }
							});
						}
					})
				}
			},
			// 验证码登录
			smsLoginHandle() {
				if (this.verifyMobile() || this.verifyCode()) {
					uni.showToast({
						title: this.verifyMobile() || this.verifyCode(),
						icon: "none"
					})
					return;
				}
				if (this.userTypeIndex == '2') {
					if (!this.identity.licenceNo) {
						uni.showModal({
							title: "提示",
							content: '请输入证件号！',
							showCancel: false,
						});
						return
					}
					this.identity.userType = '2'
				}
				this.isLoading = true;
				this.$request.post(this.$interfaces.mobileLogin, {
					data: this.identity
				}).then(res => {
					this.isLoading = false;
					if (res.data) {
						let result = res.data ? res.data : '';
						if (result) {
							setAesKey(result.aesKey)
							setMd5Key(result.md5Key)
							setTicket(result.ticket)
							setLoginUserInfo(result.data);
							onLoginCallbackHandle()

						}
					} else {
						uni.showModal({
							title: "提示",
							content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res.code + '】',
							showCancel: false,
						});
						this.getCaptcha()
					}
				}).catch(error => {
					this.getCaptcha()
					this.isLoading = false;
				})
			},
			//忘记密码
			resetPwdHandle() {
				uni.navigateTo({
					url: '/pagesC/resetPwd/p-resetPwd?mobile=' + this.formData.loginName + '&userTypeIndex=' + this.userTypeIndex
				})
			},
			// 跳转微信登录
			goWechatLogin() {

				// #ifdef MP-WEIXIN
				uni.navigateTo({
					url: '/pagesD/login/p-login'
				})
				// #endif
				// #ifdef H5
				this.gowxauthorize('login')
				// #endif

			}

		}
	}
</script>

<style lang="scss" scoped>
	.remember {
		padding-left: 20rpx;
		padding-top: 20rpx;

	}

	.remember .remember-des {
		margin-left: 8rpx;
	}

	.remember .remember-check {
		transform: scale(0.7)
	}

	.GX-logo {
		width: 100%;
		text-align: center;
	}

	.login-form-group .code-img {
		width: 240upx;
		height: 90upx;
		margin-left: 20upx;
	}

	.home-img {
		height: 180rpx;
		width: 170rpx;
	}

	.login-title {
		color: #1D2225;
		font-size: 36rpx;
	}

	.account-login {
		flex-flow: column;
		display: flex;
		padding-top: 40rpx;
		overflow-y: auto;
		height: 100%;

		.login-bd {
			flex: 1;
		}

		.sendSMS {
			padding: 10rpx;
			color: #1D82D2;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.btn {

			width: 100%;


			.login-btn {

				width: 730rpx;
				height: 104rpx;
				font-size: 32rpx;
				line-height: 104rpx;
				text-align: center;

			}
		}


	}

	.resetPwd {
		margin: 20upx 50upx;

		.resetPwd-text {
			// width: 33%;
			height: 36rpx;
			margin-top: 20rpx;
			color: #999;
		}
	}

	.other-login {
		margin: 40rpx 20rpx 20rpx 80rpx;
		height: 220rpx;
		border-top: 1px solid rgba(0, 0, 0, .1);
	}

	.other-login .login-title {
		color: #9c9d9c;
		width: 280rpx;
		height: 40rpx;
		margin: 0 auto;
		background-color: #f3f3f3;
		text-align: center;
		font-size: 24rpx;
		margin-top: -20rpx;
	}

	.other-login .other-login_bd {
		width: 100%;
		margin-top: 20rpx;
	}

	.other-login .other-login_bd .other-login_item {
		width: 180rpx;
		margin: 0 30rpx;
	}

	.other-login .other-login_bd .other-login_item .desc {
		font-size: 24rpx;
		color: #9c9d9c;
		margin-top: 20rpx;
		text-align: center;
	}

	.other-login .other-login_bd .other-login_item .login-img {
		display: block;
		width: 80rpx;
		height: 80rpx;
		margin: 0 auto;
	}

	.bind-bd {
		padding: 48rpx;
	}

	.bind-bd .bind-bd__des {
		font-size: 30rpx;
		color: #999999;
		font-weight: 400;
	}
</style>