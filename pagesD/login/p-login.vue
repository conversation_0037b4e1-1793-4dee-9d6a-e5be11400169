<template>
	<view class="smsLogin">
		<view class="GX-logo">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc-logo.png"
				class="home-img"></image>
			<view class="login-title">桂小通(广西捷通)</view>
		</view>
		<!--  #ifdef MP-WEIXIN -->
		<view class='btn u-f-ajc t-padding' style="margin-top: 60rpx;">
			<button v-if="loginMode =='mobile'" class="cu-btn lg" style="background-color:#0066E9;" open-type="getPhoneNumber"
				@getphonenumber="wxLogin">
				<view class="login-box g-flex g-flex-align-center">
					<view class="login-text">
						手机号快捷登录
					</view>
				</view>
			</button>
			<button v-if="loginMode =='code'" class="cu-btn lg" style="background-color:#0066E9;" @click="codeLoginHandle">
				<view class="login-box g-flex g-flex-align-center">
					<view class="login-text">
						手机号快捷登录
					</view>
				</view>
			</button>
		</view>
		<view class="resetPwd g-flex g-flex-align-center g-flex-justify">
			<text class="resetPwd-text" @click="goRegisterHandle">
				注册新用户
			</text>
			<text class="resetPwd-text" @click="loginTypeHandle('account')">
				密码/单位用户登录
			</text>
			<text class="resetPwd-text" @click="loginTypeHandle('sms')">
				验证码登录
			</text>
		</view>
		<privacyDialog></privacyDialog>
		<!--  #endif -->
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import store from '@/store/index.js';
	import {
		setLoginUserInfo,
		setTicket,
		setMd5Key,
		setAesKey,
		setOpenid
	} from '@/common/storageUtil.js';
	import tLoading from '@/components/common/t-loading.vue';
	import util from "@/common/util.js"
	import {
		getErrorMessage
	} from '@/common/method/filter.js'
	import {
		onLoginCallbackHandle,
		PERMANENTSTORAGE
	} from './login.js';
	export default {
		components: {
			tLoading,

		},
		data() {
			return {
				wechatCode:'',
				mobile: '', // 微信手机号码
				openId: '', // 微信小程序OpenID
				channelType: '', // 登录类型 微信公众号 WEIXIN_OPEN,微信小程序 WEIXIN_APPLET
				isLoading: false,
				loginMode:'mobile' // 登录方式 mobile 微信手机号码登录 code 微信code登录
			}
		},
		onLoad(option) {
			// #ifdef H5
			uni.redirectTo({
				url: '/pagesD/login/accountLogin'
			})
			// #endif
			// #ifdef MP-WEIXIN
			// 微信小程序登录
			this.getWechatCode()
			this.channelType = 'WEIXIN_APPLET'
			// #endif
		},
		created() {
			// 清除本地登录信息
			try {
				const res = uni.getStorageInfoSync();
				if (res && res.keys && res.keys.length) {
					let keys = res.keys;
					for (let i = 0; i < keys.length; i++) {
						if (!PERMANENTSTORAGE.includes(keys[i])) {
							uni.removeStorageSync(keys[i]);
						}

					}
				}
			} catch (e) {

			}

			this.codeButtonValidHandle()
		},
		methods: {
		    getLoginCode(){
			  return new Promise((resolve, reject) => {
				  wx.login({
				    success (res) {
				      resolve(res.code);
				    },
					fail(err){
					  reject(err)
					}
				  })
			  });
		   },
		    async codeButtonValidHandle(){
				try {
				    this.isLoading = true
				    let result = await this.getLoginCode();
				    this.sendCodeButtonValid(result)
				} catch (error) {
					console.log(error);
				} finally {
				    this.isLoading = false; // 无论成功或失败，都应该重置isLoading
				}
				
			},
			async codeLoginHandle(){
				if(this.wechatCode){
					this.sendCodeLogin();
					return
				}
				try {
				    this.wechatCode = await this.getLoginCode();
				    this.sendCodeLogin()
				} catch (error) {
					console.log(error);
				}
			},
			sendCodeLogin(){
				if(this.isLoading) return
				this.isLoading = true
				this.$request.post(this.$interfaces.codeLogin, {
					data: {
						code: this.wechatCode,
						type:this.channelType
					}
				}).then(res => {
					this.isLoading = false
					if(res.code == 200 && res.data){
						this.setLoginInfo(res.data.loginSuccessView);
					} else {
						this.$logger.error('codeLogin接口异常'+ JSON.stringify(res))
						this.loginMode = 'mobile'
						uni.showModal({
							title: "提示",
							content: res.msg + '【错误码：' + res
								.code + '】',
							showCancel: false,
						});
						this.getLoginCode()
						
					}
					
				}).catch(err=>{
					
					this.$logger.error('codeLogin接口异常'+ JSON.stringify(err))
					this.loginMode = 'mobile'
					this.isLoading = false
					this.getLoginCode()
				})
			},
			sendCodeButtonValid(code){
				this.loginMode = 'mobile'
				this.$request.post(this.$interfaces.codeButtonValid, {
					data: {
						code: code,
						type:this.channelType
					}
				}).then(res => {
					this.isLoading = false
					let  validLoginType = 'true'
					if(res.code == 200 && res.data){
						if(res.data.validLoginType == 'true' ||res.data.validLoginType == true ){
							this.loginMode = 'code'
						}
						setOpenid(res.data.openId)
					}else {
					   this.$logger.error('codeButtonValid接口异常'+ JSON.stringify(res))
					}
				}).catch(err=>{
					this.$logger.error('codeButtonValid接口异常'+ JSON.stringify(err))
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			loginTypeHandle(val) {
				let loginType = val == 'account' ? 0 : 1;
				uni.navigateTo({
					url: '/pagesD/login/accountLogin?loginType=' + loginType
				})
			},
			// 微信手机号授权登录
			wxLogin(e) {
				let _self = this;
				if (e.detail.errMsg === "getPhoneNumber:ok") {
					_self.wechatInfo = e.detail;
					uni.checkSession({
						fail() {
							console.log('uni.checkSession')
							_self.getWechatCode()
						},
						complete() {
							console.log('uni.checkSessioncomplete')
							_self.decodeWeChatPhone()
						}
					})
				}
			},
			// 解密手机号
			decodeWeChatPhone() {

				let _self = this;
				this.isLoading = true;
				let params = {
					"encryptedData": _self.wechatInfo.encryptedData,
					"iv": _self.wechatInfo.iv,
					code: _self.wechatCode
				}

				_self.$request.post(_self.$interfaces.decodeWeChatPhone, {
					data: params
				}).then((res) => {
					this.getWechatCode();
					this.isLoading = false;
					if (res.code == 200) {
						console.log(res);
						_self.mobile = res.data.mobile
						_self.openId = res.data.openId
						setOpenid(res.data.openId)
						_self.oauthHandle();
					} else {

						uni.showModal({
							title: "提示",
							content: (getErrorMessage(res.code) || res.msg) + '【错误码：' +
								res.code + '】',
							showCancel: false,
						});
					}
				}).catch(error => {
					this.getWechatCode();
					this.isLoading = false;
				})
			},
			// 获取微信登录code
			async getWechatCode() {
				this.wechatCode = ''
				try {
				   this.wechatCode = await this.getLoginCode();
				} catch (error) {
					
				   console.log(error);
				}
				
			},
			// 获取微信小程序openID
			oauthHandle() {
				let _self = this;
				_self.isLoading = true;
				let params = {
					openId: this.openId,
					mobile: this.mobile,
					channelType: this.channelType
				}
				_self.$request.post(_self.$interfaces.quickLogin, {
					data: params
				}).then((res) => {

					_self.isLoading = false;
					if (res.code == 200) {
						if (res.data && res.data) {
							console.log(JSON.stringify(res.data));
							_self.setLoginInfo(res.data);

						}
					} else {
						uni.showModal({
							title: "提示",
							content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res
								.code + '】',
							showCancel: false,
						});
					}
				}).catch((e) => {
					_self.isLoading = false;
				})

			},

			setLoginInfo(result) {
				setAesKey(result.aesKey)
				setMd5Key(result.md5Key)
				setTicket(result.ticket)
				setLoginUserInfo(result.data);
				onLoginCallbackHandle();
			},



			goRegisterHandle() {
				uni.navigateTo({
					url: "/pagesD/register/p-register"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.GX-logo {
		width: 100%;
		text-align: center;
	}



	.home-img {
		height: 180rpx;
		width: 170rpx;
	}

	.login-title {
		color: #1D2225;
		font-size: 36rpx;
	}

	.smsLogin {
		padding-top: 40rpx;



		.btn {

			width: 100%;


			.login-btn {

				width: 730rpx;
				height: 104rpx;
				font-size: 32rpx;
				line-height: 104rpx;
				text-align: center;

			}
		}

	}

	.resetPwd {
		margin: 20rpx;

		.resetPwd-text {
			padding: 10rpx 0;
			color: #999;
		}
	}

	.login-box {
		.login-img {
			width: 60rpx;
			height: 60rpx;
		}

		.login-text {
			font-size: 34rpx;
			color: #fff;
			font-weight: 400;
			margin-left: 10rpx;
			color: #fff
		}
	}
</style>