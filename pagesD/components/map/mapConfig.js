// 默认聚合配置
export const defaultClusterConfig = {
  enable:false,
  enableDefaultStyle: false,
  gridSize: 60,
  zoomOnClick: true,
};

// 默认聚合样式
export const getDefaultClusterStyle = (cluster) => {
  const count = cluster.markerIds.length;
  return {
    width: 1,
    height: 1,
    alpha: 0,
    label: {
      content: count + '',
      fontSize: 16,
      color: '#fff',
      width: 50,
      height: 50,
      bgColor: '#FF9000',
      borderRadius: 25,
      textAlign: 'center',
      anchorX: -10,
      anchorY: -35
    }
  };
};