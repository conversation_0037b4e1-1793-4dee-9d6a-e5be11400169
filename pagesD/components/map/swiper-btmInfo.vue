<template>
  <view class="info-wrap">
    <swiper
      @animationfinish="change"
      :interval="interval"
      circular
      :duration="duration"
      :autoplay="autoplay"
      :vertical="false"
      class="swiper-content"
      easing-function="linear"
      :disable-touch="true"
      :current="uCurrent"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in bannerList"
        :key="index"
      >
        <!-- 施工 work  @touchmove.stop="touchmove"-->
        <view class="box-wrap" v-if="item.infoType == 'work'">
          <view class="item-wrap" @click="speek(item.description)">
            <image
              class="image"
              src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_4.png"
            ></image>

            <view
              class="text text-info"
              @click="speek(item.description)"
            >
              <view class="warn-tip" style="width:112rpx;">涉路施工</view>
              <!-- <view class="green-tip">S22</view> -->
              <view class="right-block">
                <view class="black-tip">{{ item.highspeedName }}</view>
                <view class="time-tip">{{
                  `${item.startTime}-${item.endTime}`
                }}</view>
              </view>
            </view>
          </view>
          <view class="item-wrap" @tap.stop.prevent="swiperClick(item)">
            <image
              class="image"
              src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vlocation.png"
            ></image>
            <view class="text text-overflow2" style="width:630rpx;">{{
              item.description
            }}</view>
          </view>
          <view class="item-wrap">
            <image
              class="image"
              src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/direction.png"
            ></image>
            <view class="text">方向：{{ item.direction }}</view>
          </view>
          <view class="item-wrap">
            <image
              class="image"
              src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vbus.png"
            ></image>
            <view class="text"
              >桩号：{{ `${item.startZh}-${item.endZh}` }}</view
            >
          </view>
        </view>
        <!-- 路段 road -->
        <view class="box-wrap" v-if="item.infoType == 'road'">
          <view class="item-wrap" @click="speek(item.roadContent)">
            <image
              class="image"
              src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_2.png"
            ></image>
            <view class="text text-info">
              <view class="road-tip">优惠路段</view>
              <view class="black-tip title-flow">{{ item.roadName }}</view>
            </view>
          </view>
          <view class="gray-wrap" @tap.stop.prevent="swiperClick(item)">
            <view class="li-wrap">
              <view class="li-label">实施时间</view>
              <view class="li-value">{{ item.roadTime }}</view>
            </view>
            <view class="li-wrap">
              <view class="li-label">实施内容</view>
              <view class="li-value" :class="isfoldText ? '' : 'textOpen'">{{
                item.roadContent
              }}</view>
            </view>
            <view class="more" @click="foldText">
              <view class="text">{{ isfoldText ? "展开" : "收起" }}</view>
              <image
                class="image"
                :src="
                  isfoldText
                    ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                    : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'
                "
              ></image
            ></view>
          </view>
        </view>
        <!-- 事件 event -->
        <view class="box-wrap" v-if="item.infoType == 'event'">
          <view class="item-wrap" @click="speek(item.description)">
            <image
              class="image"
              src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_1.png"
            ></image>

            <view class="text text-info">
              <view
                class="warn-tip event"
                style="width:112rpx;margin-right:28rpx;"
                >道路事件</view
              >
              <view class="black-tip">{{ item.loadName }}</view>
              <view class="status-tip">{{ statusObj[item.eventStatus] }}</view>
            </view>
          </view>
          <view class="item-wrap" @tap.stop.prevent="swiperClick(item)">
            <!-- <view class="text" style="margin-right:120rpx;">{{
              item.pileNoStart
            }}</view> -->
            <view class="text" style="margin-left:18rpx;"
              >方向：{{ item.directionTraffic }}</view
            >
          </view>
          <view
            class="gray-wrap"
            style="margin-bottom:20rpx;"
            @tap.stop.prevent="swiperClick(item)"
          >
            <view class="li-wrap">
              <view
                class="li-value li-value2 "
                :class="isfoldText ? 'over-2' : 'textOpen'"
                >{{ item.description }}</view
              >
            </view>
            <view class="more" @click="foldText">
              <view class="text">{{ isfoldText ? "展开" : "收起" }}</view>
              <image
                class="image"
                :src="
                  isfoldText
                    ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                    : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'
                "
              ></image
            ></view>
          </view>
          <!-- <view class="item-wrap between" @tap.stop.prevent="swiperClick(item)">
            <view class="text">上报时间：{{ item.eventTime }}</view>
            <view class="text">{{ item.pileNoStart }}</view>
          </view>
          <view class="item-wrap" @tap.stop.prevent="swiperClick(item)">
            <view class="text">事件类型：{{ item.eventTypeName }}</view>
          </view> -->
        </view>
        <!-- 拥堵 jam -->
        <view class="box-wrap" v-if="item.infoType == 'jam'">
          <view
            class="item-wrap"
            @click="speek(item.congestSourceArea)"
            style="margin-bottom:0;"
          >
            <image
              class="image"
              src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_3.png"
            ></image>
            <view class="text text-info">
              <view class="status-tip red">{{
                congestLevel[item.congestLevel]
              }}</view>

              <view class="black-tip" style="margin-right:100rpx;">{{
                item.roadName
              }}</view>
              <view class="black-tip"
                >拥堵时间{{ item.congestDuration }}min</view
              >
            </view>
          </view>
          <view class="item-wrap" @tap.stop.prevent="swiperClick(item)">
            <view
              class="text"
              style="margin-left:174rpx;font-size: 24rpx;color: #999999;"
              >{{ item.startTime }}</view
            >
          </view>
          <view class="gray-wrap" @tap.stop.prevent="swiperClick(item)">
            <view class="li-wrap">
              <view class="li-label">{{ item.direction }}</view>
              <view class="li-value li-value3"
                >拥堵距离 {{ item.congestLength }}km</view
              >
            </view>
            <view class="li-wrap">
              <view class="li-label">平均车速 {{ item.speed }}km/h</view>
              <view class="li-value li-value3">{{
                eventSource[item.eventSource]
              }}</view>
            </view>
            <view class="li-wrap">
              <view class="li-label">{{ item.congestTailDesc }}</view>
              <view class="li-value li-value3"
                >道路类型 {{ roadType[item.roadType] }}</view
              >
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- <view class="close" @click="closeInfo">
      <image class="image" src="@/static/toc/close.png"></image
    ></view> -->
  </view>
</template>

<script>
const plugin = requirePlugin("WechatSI");
const innerAudioContext = uni.createInnerAudioContext();
export default {
  props: {
    bannerList: {
      type: Array,
      default: () => []
    },
    infoType: {
      type: String,
      default: "jam" // event - 事件 work - 施工 jam - 拥堵 road-优惠路段
    },
    infoData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isfoldText: true,
      autoplay: true,
      interval: 5000,
      duration: 800,
      uCurrent: 0,
      timer: null,
      statusObj: {
        0: "刚上报",
        1: "研判否",
        2: "研判确认",
        3: "处理中",
        10: "处理完成"
      },
      eventSource: {
        1: "疑似拥堵",
        2: "异常拥堵",
        3: "常规拥堵"
      },
      roadType: {
        1: "高速",
        2: "环城及快速路",
        3: "主干",
        4: "次干",
        3: "支干",
        101: "省高速",
        102: "国道",
        103: "省道"
      },
      congestLevel: {
        1: "畅通",
        2: "缓行",
        3: "拥堵",
        4: "严重拥堵"
      }
    };
  },
  watch: {
    //加载第一次banner时返回marker的item
    bannerList(banner) {
      if (banner) {
        this.$emit("onmarkerchange", banner[this.uCurrent]);
      }
    }
  },
  methods: {
    touchmove() {
      return false;
    },
    foldText() {
      // this.isfoldText = !this.isfoldText;
    },
    closeInfo() {
      this.$emit("closeInfo");
    },
    speek(content) {
      let _this = this;
      plugin.textToSpeech({
        lang: "zh_CN",
        tts: true,
        content: content,
        success: function(res) {
          console.log("succ tts", res.filename);
          _this.yuyinPlay(res.filename);
        },
        fail: function(res) {
          console.log("fail tts", res);
        }
      });
    },
    yuyinPlay(src) {
      if (src == "") {
        return;
      }
      // if (innerAudioContext) {
      //   try {
      //     innerAudioContext.pause();
      //     innerAudioContext.destroy();
      //     innerAudioContext = null;
      //   } catch (e) {
      //     //TODO handle the exception
      //     return
      //   }
      // }
      innerAudioContext.autoplay = true;
      innerAudioContext.src = src; //设置音频地址
      innerAudioContext.play(); //播放音频
    },
    change(e) {
      let current = e.detail.current;
      this.uCurrent = current;
      this.$emit("onmarkerchange", this.bannerList[this.uCurrent]);
    },
    swiperClick(row) {
      console.log("点击事件跳转数据", row);
      // this.$emit('swiperClick',row)
      uni.navigateTo({
        url: `/pagesD/travelService/jam-info?infoType=${row.infoType}&id=${row.id}&lon=${row.lon}&lat=${row.lat}`
      });
    },
    pickInfo(item) {
      console.log(item);
      let index;
      this.bannerList.forEach((el, idx) => {
        if (el.id == item.id) {
          index = idx;
        }
      });
      this.timer && clearTimeout(this.timer);
      this.uCurrent = index;
      this.autoplay = false;
      this.timer = setTimeout(() => {
        this.autoplay = true;
      }, 5000);
    }
  }
};
</script>

<style lang="scss" scoped>
.info-wrap {
  width: 100%;
  // height: 188rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 0rpx 0rpx;
  // padding: 24rpx 0;
  box-sizing: border-box;
  position: relative;
  .swiper-content {
    margin-top: 10rpx;
    height: 330rpx;
    .box-wrap {
      // padding-top: 24rpx;
      padding: 24rpx 10rpx 0 10rpx;
    }
  }

  .item-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 14rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    &.between {
      justify-content: space-between;
    }
    .image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }
    .text-info {
      display: flex;
      align-items: center;
      position: relative;
      width: 100%;
      .warn-tip {
        color: #ff9e59;
        margin-right: 30rpx;
        &.event {
          color: #ce6134;
        }
      }
      .road-tip {
        color: #08ba81;
        margin-right: 206rpx;
      }
      .green-tip {
        width: 76rpx;
        height: 36rpx;
        background: #08ba81;
        border-radius: 4rpx 4rpx 4rpx 4rpx;
        color: #ffffff;
        font-weight: 500;
        text-align: center;
        line-height: 36rpx;
      }
      .status-tip {
        width: 114rpx;
        height: 44rpx;
        line-height: 44rpx;
        background: #ff9e59;
        border-radius: 2rpx 2rpx 2rpx 2rpx;
        font-size: 24rpx;
        color: #ffffff;
        text-align: center;
        margin-left: 20rpx;
        &.red {
          width: 96rpx;
          height: 40rpx;
          line-height: 40rpx;
          background: #f82a3c;
          margin-left: 0;
          margin-right: 32rpx;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
        }
      }
      .right-block {
        // margin-left: 112rpx;
      }
      .black-tip {
        flex: 1;
        color: #333333;
        font-weight: 500;
        overflow: hidden;
        display: -webkit-box;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        /*要显示的行数*/
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
      }
      .time-tip {
        color: #999;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .gray-wrap {
    background: #f6f6f6;
    padding: 20rpx;
    box-sizing: border-box;
    .li-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      margin-bottom: 16rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .li-label {
        color: #666666;
      }
      .li-value {
        width: 408rpx;
        color: #333333;
        white-space: nowrap; /* 保证文本在一行内显示 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
        &.textOpen {
          white-space: pre-wrap;
        }
        &.li-value2 {
          width: 664rpx;
          color: #666666;
        }
        &.li-value3 {
          width: auto;
        }
        &.over-2 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          white-space: unset;
        }
      }
    }
    .more {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #999999;
      justify-content: flex-end;
      .image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
  .close {
    position: absolute;
    width: 35rpx;
    height: 35rpx;
    top: 24rpx;
    right: 5rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.text-overflow {
  white-space: nowrap; /* 保证文本在一行内显示 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
}
.text-overflow2 {
  overflow: hidden;
  display: -webkit-box;
  text-overflow: ellipsis; //属性规定当文本溢出包含元素时发生的事情  text-overflow: clip|ellipsis|string; (修剪/省略号/指定字符串)
  -webkit-line-clamp: 2;
  /*要显示的行数*/
  /* autoprefixer: off */
  -webkit-box-orient: vertical; //属性规定框的子元素应该被水平或垂直排列
  /* autoprefixer: on */
}
.title-flow {
  position: absolute;
  right: 40rpx;
  width: 400rpx;
}
</style>