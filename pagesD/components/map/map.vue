<template>
  <view class="map-content" :style="{ width: width, height: height }">
    <map class="map" id="map" :ref="'map'" :style="{ width: width, height: '100%' }" :longitude="location.longitude"
      :latitude="location.latitude" :scale="scale" :markers="markers" :include-points="includePoints"
      :polyline="polyline" :polygons="polygons" :circles="circles" :controls="controls" :show-location="showLocation"
      :enable-3D="enable3D" :rotate="rotate" :skew="skew" :show-compass="showCompass"
      :enable-overlooking="enableOverlooking" :enable-zoom="enableZoom" :enable-scroll="enableScroll"
      :enable-rotate="enableRotate" :enable-satellite="enableSatellite" :enable-traffic="enableTraffic"
      :enable-poi="enablePoi" :enable-center-location="enableCenterLocation" @markertap="onmarkertap"
      @callouttap="oncallouttap" @controltap="oncontroltap" @regionchange="onregionchange" @tap="maptap"
      @updated="onupdated" @poitap="onpoitap" @labeltap="onlabeltap" @anchorpointtap="onanchorpointtap"
      @polylinetap="onpolylinetap" @markerClusterCreate="onMarkerClusterCreate"
      @center-location-change="onCenterLocationChange">
      <cover-view slot="callout">
        <slot name="callout"></slot>
      </cover-view>
      <image v-if="showCenterLocation" class="center-location"
        src="/pagesD/static/charging/location.png" />
    </map>
  </view>
</template>

<script>
import { defaultClusterConfig, getDefaultClusterStyle } from './mapConfig';

export default {
  name: "mapCom",
  props: {
    // 地图初始化中心位置
    mapLocation: {
      type: Object,
      default () {
        return {};
      }
    },
    // 控件内容
    controls: {
      type: Object,
      default () {
        return {};
      }
    },
    // 缩放视野以包含所有给定的坐标点
    includePoints: {
      type: Array,
      default () {
        return [];
      }
    },
    // 标记点
    markers: {
      type: Array,
      default () {
        return [];
      }
    },
    // 路线
    polyline: {
      type: Array,
      default () {
        return [];
      }
    },
    // 多边形
    polygons: {
      type: Array,
      default () {
        return [];
      }
    },
    // 圆
    circles: {
      type: Array,
      default () {
        return [];
      }
    },
    // 缩放级别
    scale: {
      type: Number,
      default: 13
    },
    // 旋转角度(范围0-360)地图正北和设备 y 轴角度的夹角
    rotate: {
      type: Number,
      default: 0
    },
    // 倾斜角度，范围 0 ~ 40 , 关于 z 轴的倾角
    skew: {
      type: Number,
      default: 0
    },
    // 是否开启俯视
    enableOverlooking: {
      type: Boolean,
      default: true
    },
    // 是否显示3D楼块
    enable3D: {
      type: Boolean,
      default: true
    },
    // 是否开启实时路况
    enableTraffic: {
      type: Boolean,
      default: false
    },
    // 是否开启卫星图
    enableSatellite: {
      type: Boolean,
      default: false
    },
    // 是否显示指南针
    showCompass: {
      type: Boolean,
      default: false
    },
    // 是否支持缩放
    enableZoom: {
      type: Boolean,
      default: true
    },
    // 是否支持拖动
    enableScroll: {
      type: Boolean,
      default: true
    },
    // 是否支持旋转
    enableRotate: {
      type: Boolean,
      default: false
    },
    // 是否展示 POI 点
    enablePoi: {
      type: Boolean,
      default: true
    },
    // // 显示带有方向的当前定位点
    // showLocation: {
    //   type: Boolean,
    //   default: true
    // },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "600rpx"
    },
    // 是否以当前定位为中心
    isCenter: {
      type: Boolean,
      default: true
    },
    // 是否需要进行逆地址解析
    needGeocoder: {
      type: Boolean,
      default: false
    },
    // 是否显示位置获取失败的弹窗提示
    showLocationErrorModal: {
      type: Boolean,
      default: false
    },
    // 聚合配置
    clusterConfig: {
      type: Object,
      default: () => defaultClusterConfig
    },
    // 自定义聚合样式配置
    customClusterStyles: {
      type: [Object, Function],
      default: null
    },
    // 添加新的prop
    enableCenterLocation: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      location: {
        latitude: 22.811813,
        longitude: 108.424155
      },
      mapCtx: null,
      showLocation: true, // 显示带有方向的当前定位点
      clusters: [],
      showCenterLocation: false,
      isMoving: false,
      moveTimer: null,
      clusterMarkerIds: [], // 用于记录所有聚合点ID
      isScaling: false,     // 用于标记是否正在缩放
      clusterCreateHandler: null, // 存储事件处理函数引用
      clusterCreateHandler1: null, // 存储事件处理函数引用

    };
  },
  methods: {
    // 移动标记点
    handleTranslateMarker (params) {

      if (this.mapCtx) {
        this.mapCtx.translateMarker(params);
      }
    },
    // 沿指定路径移动 marker，用于轨迹回放等场景。动画完成时触发回调事件，若动画进行中，对同一 marker 再次调用 moveAlong 方法，前一次的动画将被打断。
    moveAlong (params) {
      if (this.mapCtx) {
        this.mapCtx.moveAlong(params);
      }
    },
    // 将地图中心移动到当前定位点
    handleMoveToLocation () {
      if (this.mapCtx) {
        this.mapCtx.moveToLocation({
          latitude: this.location.latitude,
          longitude: this.location.longitude
        });
      }
    },
    // 获取缩放程度
    getScale () {
      return this.mapCtx.getScale();
    },
    // 获取当前地图中心的经纬度。
    getCenterLocation () {
      return this.mapCtx.getCenterLocation();
    },
    // 获取地区 ？ 没找到中文Api
    getRegion () {
      return this.mapCtx.getRegion();
    },
    // 去除标记点
    removeMarkers (markerIds, callBack) {
      this.mapCtx.removeMarkers({
        markerIds,
        success: res => {
          callBack && callBack(res);
        }
      });
    },
    openMapApp (loaction) {
      this.mapCtx.openMapApp(loaction);
    },
    maptap (e) {
      this.$emit("maptap", e);
    },
    onmarkertap (e) {
      console.log(e, 123123123);
      this.$emit("onmarkertap", e);
    },
    onpolylinetap (e) {
      console.log(e, "onpolylinetap");
      this.$emit("onpolylinetap", e);
    },
    oncontroltap (e) {
      this.$emit("oncontroltap", e);
    },
    oncallouttap (e) {
      this.$emit("oncallouttap", e);
    },
    onupdated (e) {
      this.$emit("onupdated", e);
    },
    onregionchange (e) {
      // 处理地图视野变化
      if (this.enableCenterLocation) {
        if (e.type === 'begin') {
          this.isMoving = true;
          this.showCenterLocation = true;
          
          // 在缩放开始时记录操作类型
          if (e.causedBy === 'scale') {
            this.isScaling = true;
          }
        } else if (e.type === 'end') {
          this.isMoving = false;
          
          // 防抖处理，避免频繁触发
          if (this.moveTimer) clearTimeout(this.moveTimer);
          this.moveTimer = setTimeout(() => {
            // 获取地图中心点坐标
            this.mapCtx.getCenterLocation({
              success: (res) => {
                // 触发事件，将中心点坐标传给父组件
                this.$emit('centerLocationChange', {
                  latitude: res.latitude,
                  longitude: res.longitude
                });
                
                // 如果是缩放操作结束，重新初始化聚合
                if (this.isScaling && this.clusterConfig.enable) {
                  this.clearCluster();
                  this.initMarkerCluster();
                  this.isScaling = false;
                }
              }
            });
          }, 200);
        }
      }
      this.$emit("onregionchange", e);
    },
    onpoitap (e) {
      this.$emit("onpoitap", e);
    },
    onlabeltap (e) {
      this.$emit("onlabeltap", e);
    },
    onanchorpointtap (e) {
      this.$emit("onanchorpointtap", e);
    },
    // 获取当前位置并处理逆地址解析
    async getCurrentLocation () {
      try {
        const locationInfo = await this._getLocationPromise();
        const location = this._formatLocation(locationInfo);

        this._handleLocationUpdate(location);

        // 如果需要逆地址解析，则获取地址信息
        if (this.needGeocoder) {
          await this._getAddressInfo(location);
        }

        return location;
      } catch (error) {
        console.error('获取位置信息失败：', error);

        // 根据 props 决定是否显示弹窗
        if (this.showLocationErrorModal) {
          uni.showModal({
            title: '提示',
            content: '获取位置信息失败，请检查定位权限是否开启',
            showCancel: false
          });
        }

        throw error;
      }
    },

    // 将获取位置的回调转换为 Promise
    _getLocationPromise () {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: "gcj02",
          geocode: true,
          isHighAccuracy: true,
          success: resolve,
          fail: reject
        });
      });
    },

    // 格式化位置信息
    _formatLocation (res) {
      return {
        latitude: res.latitude,
        longitude: res.longitude
      };
    },

    // 处理位置更新
    _handleLocationUpdate (location) {
      if (this.isCenter) {
        this.location = { ...location };
        this.handleMoveToLocation()
      }
      uni.setStorageSync("location", location);
      this.$emit("updateLocation", location);
    },

    // 获取地址信息（逆地址解析）
    _getAddressInfo(location) {
      // this.locationAddress = "加载中...";
      const key = this.$store.state.mapKey;
      uni.request({
        url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${location.latitude},${location.longitude}&key=${key}&get_poi=1`,
        success: (res) => {
          const { data } = res;
          console.log("逆地址解析结果:", data);
          if (data && data.status === 0 && data.result) {
            // if (data.result.address_component.province != "广西壮族自治区") {
            //   this.showLocation = false;
            // }
            this.$emit("getAddressInfo", data.result);
          } else {
            console.error("腾讯地图API逆地址解析返回错误:", data);
            this.$emit("getAddressInfo", null);
          }
        },
        fail: (err) => {
          console.error("逆地址解析请求失败:", err);
          this.$emit("getAddressInfo", null);
        }
      });
    },

    // 暴露给外部的聚合初始化方法
    async initMarkerCluster () {
      if (!this.mapCtx || !this.clusterConfig.enable) {
        console.log('地图上下文或聚合配置未就绪');
        return;
      }

      try {
        // 先清除可能存在的聚合点
        this.clearCluster();
        
        // 创建一个新的事件处理函数引用
        this.clusterCreateHandler = (res) => {
          // console.log('聚合点创建事件触发:', res);
          this.handleClusterCreate(res);
        };

        this.clusterCreateHandler1 = (res) =>{
          console.log("markerClusterClick", res);  
        }
        
        // 直接使用 on 方法绑定事件，不尝试先解绑
        // 某些平台的实现会自动处理重复绑定的情况
        this.mapCtx.on('markerClusterCreate', this.clusterCreateHandler);

        console.log(this.mapCtx,'initMarkerCluster');
        
        // 初始化聚合配置
        await this.mapCtx.initMarkerCluster({
          enableDefaultStyle: this.clusterConfig.enableDefaultStyle || true,
          gridSize:this.clusterConfig.gridSize,
          zoomOnClick: true,
            success:()=>{
            console.log('聚合初始化完成');
            this.mapCtx.on('markerClusterClick',this.clusterCreateHandler1)  
          }
        },
   
      );

        
      } catch (err) {
        console.error('初始化聚合失败:', err);
        throw err;
      }
    },

    // 处理聚合点创建事件
    handleClusterCreate (res) {
      if (!res || !res.clusters) return;
      
      // 记录当前所有聚合点ID，用于后续清除
      this.clusterMarkerIds = [];
      
      // 使用对象方式自定义样式
      res.clusters.forEach(cluster => {
        const clusterId = cluster.clusterId
        this.clusterMarkerIds.push(clusterId);
        this.updateClusterStyle(cluster);
      });

      // 触发外部事件
      this.$emit('markerClusterCreate', res);
    },

    // 获取聚合点样式
    getClusterStyle (cluster) {
      try {
        if (typeof this.customClusterStyles === 'function') {
          return this.customClusterStyles(cluster);
        }

        // 使用自定义样式或默认样式
        return this.customClusterStyles || getDefaultClusterStyle(cluster);
      } catch (err) {
        console.error('获取聚合样式失败，使用默认样式:', err);
        return getDefaultClusterStyle(cluster);
      }
    },

    // 更新聚合点样式
    updateClusterStyle (cluster) {
      const style = this.getClusterStyle(cluster);
      console.log(style,'style');
      
      const clusterId = cluster.clusterId;

      this.mapCtx.addMarkers({
        markers: [{
          clusterId,
          latitude: cluster.center.latitude,
          longitude: cluster.center.longitude,
          joinCluster:true,
          ...style
        }],
        zIndex:-1,
        clear: false
      });
    },

    // 添加需要聚合的标记点
    async addClusterMarkers (markers) {
      if (!this.mapCtx) return;

      try {
        const clusterMarkers = markers.map(marker => ({
          ...marker,
          joinCluster: true
        }));

        console.log('添加聚合标记点:', clusterMarkers.length);
        await this.mapCtx.addMarkers({
          markers: clusterMarkers,
          clear: false,
          success: (res) => {
            console.log('聚合标记点添加成功', res);
          },
          fail: (err) => {
            console.error('聚合标记点添加失败', err);
          }
        });
      } catch (err) {
        console.error('添加聚合标记点失败:', err);
      }
    },

    // 清除聚合
    clearCluster () {
      if (this.mapCtx) {
        // 清除所有标记点
        if (this.markers && this.markers.length > 0) {
          this.mapCtx.removeMarkers({
            markerIds: this.markers.map(marker => marker.id)
          });
        }
        
        // 清除所有聚合点
        if (this.clusterMarkerIds && this.clusterMarkerIds.length > 0) {
          this.mapCtx.removeMarkers({
            markerIds: this.clusterMarkerIds
          });
        }
        
      }
    },

    // 初始化用户位置信息
    async initUserLoactionInfo () {
      try {
        const location = await this.getCurrentLocation();
        console.log('获取到用户位置:', location);

        // 初始化地图上下文
        this.mapCtx = uni.createMapContext("map", this);

        return location;
      } catch (error) {
        console.error('初始化位置信息失败：', error);
        throw error;
      }
    },

    onCenterLocationChange (location) {
      this.location = location;
      this.handleMoveToLocation();
    }
  },
  watch: {
    mapLocation: {
      deep: true,
      // immediate: true,
      handler (val) {
        console.log("当前mapLocation", val);
        this.location = val;
      }
    },
    // 监听markers变化，重新初始化聚合
    markers: {
      deep: true,
      handler (newVal) {
        if (newVal && newVal.length > 0 && this.clusterConfig.enable) {
          this.initMarkerCluster();
        }
      }
    }
  },
  mounted () {
    this.mapCtx = uni.createMapContext("map", this);

    // 先获取位置信息，再初始化聚合
    this.initUserLoactionInfo().then(() => {
      this.initMarkerCluster();
    }).catch(err => {
      console.error('初始化地图失败:', err);
    });
  },
  beforeDestroy () {
    this.clearCluster();
    
    // 清理事件监听
    if (this.mapCtx && this.clusterCreateHandler) {
      try {
        this.mapCtx.off('markerClusterCreate', this.clusterCreateHandler);
      } catch (error) {
        console.log('清理事件监听失败:', error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.map-content {
  flex: 1;
}

.map {
  background-color: #f0f0f0;
  width: 100%;
  height: 100%;
}

/* 修改 callout 容器样式 */
:deep(.callout-container) {
  position: absolute;
  z-index: 999;
  pointer-events: auto;
}

.cluster-container {
  position: absolute;
  z-index: 99;
}

.default-cluster {
  width: 50rpx;
  height: 50rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .cluster-text {
    color: #FFFFFF;
    font-size: 24rpx;
  }
}

.center-location {
  z-index: 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
}
</style>