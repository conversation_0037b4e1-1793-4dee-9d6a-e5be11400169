<template>
	<view class="top-message g-flex-align-center">
		<view class="notice">
			<view class="message g-flex g-flex-align-center">
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>" class="message-img"></image>
				<view class="message-point">

				</view>
			</view>
			<view class="notice-list">
				<view class="notice-bar">
					<swiper disable-touch @change='onChange' :autoplay="autoplay" :vertical="true" circular
						:interval="interval" class="u-swiper">
						<swiper-item catchtouchmove="stopTouchMove" v-for="(item, index) in noticeList" :key="index"
							class="u-swiper-item">
							<view class="notice-list-item u-line-1" @click="onNoticeHandle(item)">
								{{ item.titleName }}
							</view>
						</swiper-item>
					</swiper>
				</view>

			</view>
			<view class="allNotice" @click="goInfoPage"></view>
		</view>
		<!-- <neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:confirm-text="modal.confirmText" @confirm="onConfirmHandle">
			<view class="message-content-wrapper">
				<view class="title">提示</view>
				<view class="msg_desc">

					<view class="msg_desc-content">
						{{dislogMsg}}
					</view>
				</view>
			</view>
		</neil-modal> -->
	</view>
</template>

<script>
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	import {
		getLoginUserInfo
	} from '@/common/storageUtil.js'
	import {
		throttle
	} from '../../../common/util.js'
	export default {
		data() {
			return {
				disableTouch: true,
				autoplay: true,
				interval: 5000, // 滚动一个周期的时间长，单位ms
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					confirmText: '确认'
				},
				noticeList: [],
				exposureList: [],
				isLoading: false
			}
		},
		components: {

		},
		created() {
			if(process.env.NODE_ENV === "development"){
				this.autoplay = false
			}
			this.getCarouselList();
		},
		methods: {
			goInfoPage(){
				uni.navigateTo({
					url: '/pagesC/infoBusiness/index'
				})
			},
			stopTouchMove() {
				return false;
			},
			// 获取资讯列表
			getCarouselList() {
				this.$request
					.post(this.$interfaces.carouselList).then(res => {
						console.log('res====>>>',res)
						if (res.code == 200 && res.data) {
							this.noticeList = res.data;
							console.log(JSON.stringify(this.noticeList));
						}
					}).catch(err => {

					})
			},
			onChange(events) {

				if (events.detail.source == 'autoplay') {
					let row = this.noticeList[events.detail.current] || {};
					this.acquisitionHandle(row)
				}

			},
			onNoticeHandle(row) {

				if (row.informationId) {
					let params = {
						bizType: '2', //1-广告、2-资讯
						bizId: row.informationId,
						eventType: 2, //1-曝光、2-点击
						eventTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
						userId: getLoginUserInfo().userId
					}
					this.eventUpload([params])
				}
				// articleType 1-富文本页面、2-链接跳转
				if (row.articleType == '2') {
					
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' +
							encodeURIComponent(row.articleUrl)
					})
				}
				if (row.articleType == '1') {
					uni.navigateTo({
						url: '/pagesC/infoBusiness/detail?informationId='+row.informationId
					})
				}
			},
			// 曝光数据采集
			acquisitionHandle(row) {
				if (row.informationId) {
					let obj = {
						bizType: '2', //1-广告、2-资讯
						bizId: row.informationId,
						eventType: 1, //1-曝光、2-点击
						eventTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
						userId: getLoginUserInfo().userId
					}
					this.exposureList.push(obj);
					this.throttleExposure();
				}
			},
			throttleExposure: throttle(function(...args) {
				this.exposureHandle(...args)
			}, 20000),
			exposureHandle() {
				let list = JSON.parse(JSON.stringify(this.exposureList))
				this.exposureList = []
				this.eventUpload(list)
			},
			// 事件统计
			eventUpload(params) {
				if (!(params && params.length)) return;
				if (this.isLoading) return;
				this.isLoading = true
				this.$request
					.post(this.$interfaces.trackUpload, {
						data: {
							list: params
						}
					}).then(res => {
						this.isLoading = false
					}).catch(() => {
						this.isLoading = false
					})

			}
		}
	}
</script>

<style lang="scss" scoped>
	.top-message {
		position: relative;
		height: 80rpx;
		background: linear-gradient(270deg, #FFFFFF 0%, #FFF1ED 100%);
		border-radius: 8rpx;
		margin: 20rpx 0;
		width: 100%;
		display: flex;
		justify-content: center;


		.notice {
			position: relative;
			padding: 0 28rpx;
			z-index: 9;
			width: 100%;
			display: flex;
			height: 80rpx;
			align-items: center;
			border-radius: 10rpx;

			.notice-list {
				width: 100%;
			}

			.allNotice {
				position: relative;
				width: 30rpx;
				height: 30rpx;
			}

			.allNotice:after {
				content: " ";
				display: inline-block;
				height: 6px;
				width: 6px;
				border-width: 2px 2px 0 0;
				border-color: #c8c8cd;
				border-style: solid;
				-webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
				transform: matrix(.71, .71, -.71, .71, 0, 0);
				position: absolute;
				top: 50%;
				margin-top: -4px;
				left: 2px;
			}

			.message-img {
				display: block;
				width: 70rpx;
				height: 30rpx;
			}

			.message-point {
				width: 6rpx;
				height: 6rpx;
				margin-left: 12rpx;
				border-radius: 100%;
				background: #333333;
			}
		}
	}

	.notice-list {
		.notice-bar {
			width: 100%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			flex-wrap: nowrap;
			padding: 9px 0;
			overflow: hidden;
		}

		.u-swiper {
			font-size: 26rpx;
			height: 32rpx;
			display: flex;
			align-items: center;
			flex: 1;
			margin-left: 12rpx;
		}

		.u-swiper-item {
			display: flex;
			align-items: center;
			overflow: hidden;
		}

		.notice-list-item {
			color: #8D7860;
			font-size: 14px;
		}


		.u-line-1 {
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}

	.message-content-wrapper {
		.title {
			text-align: center;
			font-weight: 700;
			font-size: 34rpx;
			padding: 25rpx 50rpx;
			color: rgba(0, 0, 0, 0.9);
		}

		.msg_desc {
			padding: 0 30rpx;
			color: rgba(0, 0, 0, 0.5);
			font-size: 30rpx;
			text-align: left;

			.msg_desc-content {
				text-indent: 2em;
			}
		}
	}
</style>
