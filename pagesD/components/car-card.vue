<template>
	<view class="index-car-card u-f-ajc u-f-dc"> 
	    <!-- u-f-ajc   cu-tag badge -->
		<view class="son-card u-f-ajc" :class="getClass" :style="{color:plateColor=='0'|| plateColor=='2' ? '#fff' : '#000'}">
		   <view>{{plateNum}}</view>
		   <view class="tag" v-if='isProcess'>办理中</view>
		   <view class="tag" v-if="black_type == 2">黑名单</view>
		   <view class="phone-info-bg" v-if="isAgreement === '0'"></view>
		   <view class="phone-info" v-if="isAgreement === '0'">预留手机号码不一致<br/>请确认修改为当前手机号</view>
		</view>
	</view>
</template>

<script>
	import { backColorMap,getVehicleStatus,cardTypeMap,bankCodeToBankNameMap} from '@/common/systemConstant.js';
    import { plateColorToClassMap,plateColorToFirstMap } from '@/common/systemConstant.js'
	export default {
        name: 'neil-modal',
        props: {
			item: {
				type: Object,
				default:{}
			},
            plateNum: { //车牌
                type: String,
                default: ''
            },
			plateColor:{
				type: String,
			},
            status: {//状态
				type: Number,
				default: 0
			}, 
            issuer: { //签约渠道
                type: String,
                default: ''
            },
			isAgreement: {
				type:String,
				default:'1'
			},
			registeredType:{
				type:String,
				default:1
			},
			black_type:{
				type:Number,
				default:1
			}
            // channels: {//
            //     type: Number,
            //     default: 0
            // }
        },
		onLoad:function(e){
			console.log('状态信息'+this.channels)
		},
		computed:{
			getClass(){
				return plateColorToFirstMap.get(this.plateColor+'');
			},
			isProcess(){
				if(this.item && this.item.issueState == '0') {
					return true
				}
				if(this.item && this.item.issueState == '1') {
					if(!(this.item.obustatus == 3 && this.item.obuActiveFlag =='0')) {
						return true
					}
				}
				return false
			}
		},
		data() {
            return {
				cardTypeMap,
				getVehicleStatus,
				bankCodeToBankNameMap,
				plateColorToFirstMap,
                isOpen: false
            }
        },
        methods: {
            
        }
    }
</script>

<style scoped>
	.index-car-card{
		background: #f3f3f3;
		height: 148upx;
		border-radius: 20upx;
		margin:20upx;
		position: relative;
		/* border: 1upx solid #CCCCCC; */
		/* box-shadow: 0px 3px 6px 0px rgba(50,54,63,0.23); */
	}
	.son-card{
		width: 512upx;
		height: 148upx;
		/* color: #FFFFFF; 
		background: #1F77D0; */
		border-radius: 16upx;
		margin-bottom: 20upx;
		font-size: 60upx;
		position: relative;
	}
	.phone-info-bg{
		position: absolute;
		width: 100%;
		font-size: 24rpx;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0 40px;
		background-color: #fff;
		opacity: .6;
	}
	.phone-info{
		color: #000;
		position: absolute;
		background-color: #fff;
		font-size: 12px;
		width: 280rpx;
		text-align: center;
	}
	.tag{
	  background: red;
	  font-size: 20upx;
	  border-radius: 10upx;
	  padding: 4upx 10upx;
	  color: #fff;
	  position: absolute;
	  top: -10upx;
	  right: 0upx;
	 }
	.nav-title{
		width: 80%;
		height: 70upx;
		border-top: 1px dashed #BABABA;
	}
	.car-item{
		padding-top: 10upx;
	}
	.car-item>view{
		font-size: 24upx;
	}
	.car-item text{
		color: #2484E8 ;
		margin-top: 8upx;
	}
	.car-status{
		width: 80upx;
		height: 40upx;
		background-color: #FF0000;
		float: right;
		font-size: 0.6rem;
		right:0px;
		bottom:0px;
	}
	.dot-car{
		width: 14upx;
		height: 14upx;
		border-radius: 100%;
		margin: 0 10upx;
	}
</style>
