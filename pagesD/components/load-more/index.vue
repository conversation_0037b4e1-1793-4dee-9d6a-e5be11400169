<template>
	<view class="u-f-ajc" style="margin-top: 20rpx;" >
		<view v-if="loadStatus==LOAD_NORMAL" class="text-gray">暂无数据</view>
		<view v-if="loadStatus==LOAD_ING" class="cu-load load-cuIcon loading text-blue"></view>
		<view v-if="loadStatus==LOAD_ERROR" class="cuIcon-roundclose text-red">加载失败</view>
		<view v-if="loadStatus==LOAD_NO_MORE" class="text-gray">没有更多了</view>
	</view>
</template>

<script>

	export default {
		props: {
			loadStatus:{
				type:Number,
				default: 0,
			}
		},
		data(){
			return{
				LOAD_NORMAL:0,
				LOAD_ING:1,
				LOAD_ERROR:2,
				LOAD_NO_MORE:3
			}
		},
		methods:{

		}
	}
</script>

<style scoped>
</style>
