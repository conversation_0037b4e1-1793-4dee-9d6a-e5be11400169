<template>
  <view class="cultural-tourism-page">
    <!-- 列表内容 -->
    <scroll-view
      :scroll-top="scrollTop"
      :style="scrollViewHeight"
      scroll-y="true"
      class="scroll-view"
      :lower-threshold="lowerThreshold"
      @scrolltolower="scrolltolower"
      @scroll="scroll"
    >
      <view class="tourism-list">
        <view
          class="tourism-item"
          v-for="(item, idx) in tourismList"
          :key="idx"
          @click="toDetail(item)"
        >
          <image :src="item.coverImage" class="item-image" mode="aspectFill"></image>
        </view>
      </view>
      
      <!-- 加载更多状态 -->
      <load-more :loadStatus="loadStatus" />
    </scroll-view>
  </view>
</template>

<script>
import loadMore from "@/pagesD/components/load-more/index.vue";

// 节流函数
function throttle(fn, delay) {
  let timer = null;
  return function() {
    if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, arguments);
        timer = null;
      }, delay);
    }
  };
}

export default {
  name: 'CulturalTourismList',
  components: {
    loadMore,
  },
  data() {
    return {
      scrollTop: 0,
      lowerThreshold: 120,
      loadStatus: 0,
      flag: false,
      formData: {
        pageNum: 1,
        pageSize: 10
      },
      // 模拟数据 - 基于用户提供的图片内容，简化为纯图片展示
      tourismList: [
        {
          id: 1,
          coverImage: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
          articleType: '1',
          articleUrl: '',
          detailId: 'highway_001'
        },
        {
          id: 2,
          coverImage: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ2.png',
          articleType: '1',
          articleUrl: '',
          detailId: 'highway_002'
        },
        {
          id: 3,
          coverImage: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ3.png',
          articleType: '1',
          articleUrl: '',
          detailId: 'highway_003'
        },
        {
          id: 4,
          coverImage: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
          articleType: '1',
          articleUrl: '',
          detailId: 'highway_004'
        },
        {
          id: 5,
          coverImage: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ2.png',
          articleType: '1',
          articleUrl: '',
          detailId: 'highway_005'
        }
      ]
    };
  },
  computed: {
    scrollViewHeight() {
      return 'height: calc(100vh - 30rpx);'
    }
  },
  methods: {
    // 模拟加载更多数据
    loadMoreData() {
      this.loadStatus = 1;
      
      // 模拟网络请求延迟
      setTimeout(() => {
        const newData = [
          {
            id: this.tourismList.length + 1,
            coverImage: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ3.png',
            articleType: '1',
            articleUrl: '',
            detailId: `highway_${this.tourismList.length + 1}`
          },
          {
            id: this.tourismList.length + 2,
            coverImage: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
            articleType: '1',
            articleUrl: '',
            detailId: `highway_${this.tourismList.length + 2}`
          }
        ];
        
        this.tourismList = this.tourismList.concat(newData);
        
        // 模拟数据加载完成 - 调整阈值为8条
        if (this.tourismList.length >= 8) {
          this.loadStatus = 3; // 没有更多数据
          this.flag = true;
        } else {
          this.loadStatus = 0; // 加载完成
        }
      }, 1000);
    },
    
    scrolltolower() {
      // 防止重复加载：检查flag状态和加载状态
      if (this.flag || this.loadStatus === 1) return;
      this.formData.pageNum += 1;
      this.loadMoreData();
    },
    
    scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
    
    toDetail(item) {
        // 跳转到高速联盟详情页面
        uni.navigateTo({
          url: `/pagesD/highway-alliance/detail?informationId=${item.detailId}&category=highway-alliance`
        });
    }
  },
  
  onLoad() {
    // 页面加载时的初始化操作
    console.log('文化旅游推广页面加载完成');
  }
};
</script>

<style lang="scss" scoped>
.cultural-tourism-page {
  background: #f8f9fa;
  min-height: 100vh;

  .scroll-view {
    background: #f8f9fa;
  }

  .tourism-list {
    padding: 24rpx 32rpx;

    .tourism-item {
      background: #ffffff;
      border-radius: 16rpx;
      margin-bottom: 32rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
      border: 1rpx solid #f0f0f0;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .item-image {
        width: 100%;
        height: 234rpx;
        display: block;
      }
    }
  }
}
</style>
