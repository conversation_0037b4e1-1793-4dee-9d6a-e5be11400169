<template>
  <view class="highway-alliance-detail">
    <view class="highway-alliance-detail-container">
      <!-- 顶部横幅图片 -->
      <view class="banner-section">
        <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png"
          class="banner-image" mode="aspectFill">
        </image>
      </view>

      <!-- 公司介绍区域 -->
      <view class="company-info">
        <view class="company-title">xxxxx公司</view>
        <view class="company-description">
          公司简介：集团公司具有建筑装修装饰工程、地基与工程、
          钢结构工程、市政公用工程施工总承包、环保施工等专业承
          包资质，是一家集建筑、房地产开发、建材经营等为一体的
          大型建筑企业，可承接工业、民用等大体量、高层次、大跨
          度、高标准、精装修的建筑施工业务。
        </view>
      </view>

      <!-- Tab切换区域 -->
      <view class="tab-section">
        <view class="tab-container">
          <view class="custom-tabs">
            <!-- 背景图片 -->
            <image
              src="/static/agent/active_left.png"
              class="tab-background"
              :class="{ 'active': currentTab === 0 }"
              mode="scaleToFill"
            />
            <image
              src="/static/agent/active_right.png"
              class="tab-background"
              :class="{ 'active': currentTab === 1 }"
              mode="scaleToFill"
            />

            <view class="tab-item" :class="{ 'active': currentTab === 0 }" @click="switchTab(0)">
              <text class="tab-text">公司咨询</text>
              <view class="tab-line" v-if="currentTab === 0"></view>
            </view>
            <view class="tab-item" :class="{ 'active': currentTab === 1 }" @click="switchTab(1)">
              <text class="tab-text">优惠活动</text>
              <view class="tab-line" v-if="currentTab === 1"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 文章列表组件 -->
      <ArticleList 
        :currentTab="currentTab"
        ref="articleListRef"
      />
    </view>

  </view>
</template>

<script>
import ArticleList from './components/ArticleList.vue'

export default {
  name: 'HighwayAllianceList',
  components: {
    ArticleList
  },
  data() {
    return {
      currentTab: 0 // 0: 公司咨询, 1: 优惠活动
    }
  },

  onLoad() {
    // 详情页面加载
  },
  onReachBottom() {
    // 通过组件引用调用加载更多
    this.$refs.articleListRef && this.$refs.articleListRef.loadMoreArticles()
  },
  onPullDownRefresh() {
    // 通过组件引用调用刷新
    this.$refs.articleListRef && this.$refs.articleListRef.refreshArticleList()
  },
  methods: {
    // 切换Tab
    switchTab(index) {
      if (this.currentTab !== index) {
        this.currentTab = index
        // tab切换会通过watch自动更新组件
      }
    },




  }
}
</script>

<style lang="scss" scoped>
.highway-alliance-detail {
  min-height: 100vh;
  padding: 20rpx;
  .highway-alliance-detail-container {
    background-color: #ffffff;
    border-radius: 8rpx;
    padding: 24rpx 20rpx;
  }

  .banner-section {
    width: 670rpx;
    height: 234rpx;
    .banner-image {
      width: 100%;
      height: 100%;
    }
  }

  .company-info {
    background: #F1F9FF;
    padding: 16rpx 20rpx;
    margin: 24rpx 0;
    border-radius: 8rpx;
    .company-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 12rpx;
    }

    .company-description {
      font-size: 24rpx;
      color: #666666;
    }
  }

  .tab-section {
    background: #ffffff;
    margin-bottom: 20rpx;

    .tab-container {
      background: #ffffff;
      border-radius: 16rpx 16rpx 0 0;
      
      .custom-tabs {
        display: flex;
        height: 86rpx;
        position: relative;

        .tab-background {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          opacity: 0;
          transition: opacity 0.3s ease;

          &.active {
            opacity: 1;
          }
        }

        .tab-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: relative;
          height: 86rpx;
          z-index: 2;

          .tab-text {
            font-size: 32rpx;
            font-weight: 500;
            color: #979797;
            transition: color 0.3s ease;
          }

          .tab-line {
            position: absolute;
            bottom: 0;
            width: 60rpx;
            height: 6rpx;
            background: linear-gradient(90deg, #4A90E2 0%, #0066E9 100%);
            border-radius: 3rpx;
          }

          &.active {
            .tab-text {
              color: #13234A;
              font-weight: 600;
            }
          }
        }
      }
    }
  }




}
</style>