<template>
  <view class="article-list-container">
    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <image class="search-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/search_icon.png"></image>
        <input type="text" v-model="searchKeyword" placeholder="请输入文章标题" class="search-input" confirm-type="search" @confirm="onSearchConfirm" @input="onSearchInput" />
        <image v-if="searchKeyword" class="clear-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/clear_icon.png" @click="clearInput"></image>
      </view>
    </view>

    <!-- 文章列表 -->
    <view class="article-list">
      <view class="list-item" v-for="(item, index) in displayList" :key="item.informationId"
        @click="goToDetail(item)">
        <view class="item-image">
          <image v-if="item.coverPic" :src="item.coverPic" class="cover-image" mode="aspectFill"></image>
          <view v-else class="default-image">
            <text>暂无图片</text>
          </view>
        </view>
        <view class="item-content">
          <view class="item-title">{{ item.titleName }}</view>
          <view class="item-date">{{ item.publishTime }}</view>
        </view>
      </view>

      <!-- 空状态或推荐文章 -->
      <view v-if="displayList.length === 0 && !isLoading" class="empty-section">
        <!-- 无匹配结果时显示推荐 -->
        <view v-if="searchKeyword.trim()" class="no-match-section">
          <view class="empty-state">
            <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/no_data.png" class="empty-icon"></image>
            <text class="empty-text">无匹配结果，请重新输入</text>
          </view>
          
          <!-- 其他文章推荐 -->
          <view class="recommend-section" v-if="recommendList.length > 0">
            <view class="recommend-title">其他文章推荐</view>
            <view class="recommend-list">
              <view class="list-item" v-for="(item, index) in recommendList" :key="item.informationId"
                @click="goToDetail(item)">
                <view class="item-image">
                  <image v-if="item.coverPic" :src="item.coverPic" class="cover-image" mode="aspectFill"></image>
                  <view v-else class="default-image">
                    <text>暂无图片</text>
                  </view>
                </view>
                <view class="item-content">
                  <view class="item-title">{{ item.titleName }}</view>
                  <view class="item-date">{{ item.publishTime }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 普通空状态 -->
        <view v-else class="empty-state">
          <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/no_data.png" class="empty-icon"></image>
          <text class="empty-text">暂无相关文章</text>
        </view>
      </view>
    </view>

    <!-- 加载遮罩 -->
    <view class="loading-overlay" v-if="isLoading && pageNum === 1">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ArticleList',
  props: {
    currentTab: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 文章列表数据
      articleList: [],
      allArticleList: [], // 存储所有文章，用于本地搜索
      recommendList: [],
      isLoading: false,
      loadStatus: 4, // 1: 加载中, 2: 加载失败, 3: 没有更多, 4: 正常
      pageNum: 1,
      pageSize: 10,
      searchKeyword: '', // 内部管理搜索关键词
      searchTimer: null
    }
  },
  computed: {
    // 显示的列表数据（根据搜索关键词过滤）
    displayList() {
      if (!this.searchKeyword) {
        return this.articleList
      }
      return this.allArticleList.filter(item => 
        item.titleName && item.titleName.includes(this.searchKeyword)
      )
    }
  },
  watch: {
    // 监听tab切换
    currentTab() {
      this.refreshArticleList()
    }
  },
  mounted() {
    this.loadArticleList()
  },
  methods: {
    // 搜索输入事件
    onSearchInput() {
      // 防抖处理
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.performSearch()
      }, 300)
    },

    // 搜索确认事件
    onSearchConfirm() {
      this.performSearch()
    },

    // 清空搜索框
    clearInput() {
      this.searchKeyword = ''
      this.performSearch()
    },

    // 执行搜索（参照policy.vue逻辑）
    performSearch() {
      // 搜索功能通过computed属性自动实现
      console.log('高速联盟搜索:', this.searchKeyword)
      
      // 如果搜索无结果，加载推荐文章
      if (this.displayList.length === 0 && this.searchKeyword.trim()) {
        this.loadRecommendArticles()
      }
    },

    // 加载文章列表
    async loadArticleList() {
      if (this.isLoading) return
      
      this.isLoading = true
      this.loadStatus = 1

      try {
        const params = {
          classType: "21", //资讯类型 
          category: "2", // 一级分类
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }

        if (this.searchKeyword.trim()) {
          params.keyword = this.searchKeyword.trim()
        }

        const res = await this.$request.post(this.$interfaces.informationList, {
          data: params
        })

        if (res.code === 200 && res.data) {
          const newArticles = res.data.data || []
          
          if (this.pageNum === 1) {
            this.articleList = newArticles
            this.allArticleList = newArticles // 同时更新所有文章列表
          } else {
            this.articleList = this.articleList.concat(newArticles)
            this.allArticleList = this.allArticleList.concat(newArticles)
          }

          // 检查是否还有更多数据
          if (newArticles.length < this.pageSize) {
            this.loadStatus = 3 // 没有更多数据
          } else {
            this.loadStatus = 4 // 正常状态
          }
        } else {
          this.loadStatus = 2 // 加载失败
          uni.showToast({
            title: res.msg || '加载失败',
            icon: 'none'
          })
        }
      } catch (error) {
        this.loadStatus = 2
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },

    // 加载推荐文章
    async loadRecommendArticles() {
      try {
        const params = {
          classType: "22", //资讯类型 
          category: "2", // 一级分类
          pageNum: 1,
          pageSize: 10 // 推荐文章数量
        }

        const res = await this.$request.post(this.$interfaces.informationList, {
          data: params
        })

        if (res.code === 200 && res.data) {
          this.recommendList = res.data.data || []
        }
      } catch (error) {
        console.error('加载推荐文章失败:', error)
      }
    },

    // 加载更多文章
    loadMoreArticles() {
      if (this.loadStatus === 1 || this.loadStatus === 3) return
      
      this.pageNum++
      this.loadArticleList()
    },

    // 刷新文章列表
    refreshArticleList() {
      this.pageNum = 1
      this.articleList = []
      this.allArticleList = []
      this.recommendList = []
      this.loadArticleList()
    },

    // 跳转到文章详情
    goToDetail(item) {
      uni.navigateTo({
        url: `/pagesD/highway-alliance/detail?informationId=${item.informationId}&category=highway-alliance`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.article-list-container {
  background: #ffffff;

  .search-section {
    background: #ffffff;
    padding: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 8rpx 8rpx 0 0;

    .search-box {
      position: relative;
      display: flex;
      align-items: center;
      height: 64rpx;
      background: #E7E7E7;
      border-radius: 32rpx;
      padding: 0 24rpx;
      transition: all 0.3s ease;
      
      &:focus-within {
        background: #DCDCDC;
        box-shadow: inset 0 0 4rpx rgba(0, 0, 0, 0.1);
      }
      
      .search-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
      
      .search-input {
        flex: 1;
        height: 64rpx;
        font-size: 28rpx;
        color: #333333;
      }
      
      .clear-icon {
        width: 32rpx;
        height: 32rpx;
        opacity: 0.7;
        transition: opacity 0.2s ease;
        
        &:active {
          opacity: 1;
        }
      }
    }
  }

  .article-list {
    .list-item {
      padding: 26rpx 0;
      display: flex;
      align-items: flex-start;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .item-image {
        width: 160rpx;
        height: 120rpx;
        border-radius: 12rpx;
        overflow: hidden;
        flex-shrink: 0;
        margin-right: 24rpx;

        .cover-image {
          width: 100%;
          height: 100%;
          display: block;
        }

        .default-image {
          width: 100%;
          height: 100%;
          background: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;

          text {
            font-size: 20rpx;
            color: #999999;
          }
        }
      }

      .item-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 120rpx;

        .item-title {
          font-size: 28rpx;
          font-weight: 500;
          color: #333333;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: auto;
        }

        .item-date {
          font-size: 24rpx;
          color: #999999;
          font-weight: 400;
          text-align: right;
          margin-top: 16rpx;
        }
      }
    }
  }

  .empty-section {
    .empty-state {
      text-align: center;
      padding: 80rpx 30rpx;
      display: flex;
      align-items: center;
      flex-direction: column;

      .empty-icon {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 24rpx;
      }

      .empty-text {
        font-size: 28rpx;
        color: #999999;
      }
    }

    .recommend-section {
      .recommend-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 26rpx;
      }

      .recommend-list {
        .list-item {
          padding: 26rpx 0;
          display: flex;
          align-items: flex-start;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .item-image {
            width: 160rpx;
            height: 120rpx;
            border-radius: 12rpx;
            overflow: hidden;
            flex-shrink: 0;
            margin-right: 24rpx;

            .cover-image {
              width: 100%;
              height: 100%;
              display: block;
            }

            .default-image {
              width: 100%;
              height: 100%;
              background: #f5f5f5;
              display: flex;
              align-items: center;
              justify-content: center;

              text {
                font-size: 20rpx;
                color: #999999;
              }
            }
          }

          .item-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 120rpx;

            .item-title {
              font-size: 28rpx;
              font-weight: 500;
              color: #333333;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-bottom: auto;
            }

            .item-date {
              font-size: 24rpx;
              color: #999999;
              font-weight: 400;
              text-align: right;
              margin-top: 16rpx;
            }
          }
        }
      }
    }
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    text {
      font-size: 28rpx;
      color: #666666;
    }
  }
}
</style> 