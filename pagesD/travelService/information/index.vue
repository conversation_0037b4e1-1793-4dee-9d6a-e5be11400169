<template>
  <view class="information-container">
    <!-- TAB切换栏 -->
    <view class="tab-container">
      <view class="custom-tabs">
        <view
          class="tab-item"
          :class="{ 'active': currentTab === index }"
          v-for="(item, index) in tabList"
          :key="index"
          @click="switchTab(index)"
        >
          <text class="tab-text">{{ item.name }}</text>
          <view class="tab-line" v-if="currentTab === index"></view>
        </view>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="custom-search-wrapper">
        <view class="search-box">
          <image class="search-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/search_icon.png"></image>
          <input
            class="search-input"
            type="text"
            v-model="searchKeyword"
            placeholder="请输入文章标题"
            @input="onSearchInput"
            @confirm="onSearchConfirm"
          />
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <info-component
        v-show="currentTab === 0"
        ref="infoRef"
        :searchKeyword="searchKeyword"
      ></info-component>
      <policy-component
        v-show="currentTab === 1"
        ref="policyRef"
        :searchKeyword="searchKeyword"
      ></policy-component>
    </view>
  </view>
</template>

<script>
import InfoComponent from './info.vue'
import PolicyComponent from './policy.vue'
import highSearch from '@/pages/home/<USER>/highSearch.vue'

export default {
  components: {
    InfoComponent,
    PolicyComponent,
    highSearch
  },
  data() {
    return {
      currentTab: 0, // 当前激活的TAB：0-高速资讯，1-政策宣贯
      searchKeyword: '', // 搜索关键词
      searchTimer: null, // 搜索防抖定时器
      tabList: [
        {
          name: '高速资讯'
        },
        {
          name: '政策宣贯'
        }
      ]
    }
  },
  onLoad(options) {
    // 支持从外部传入TAB参数
    if (options.tab) {
      this.currentTab = parseInt(options.tab) || 0
    }
  },
  methods: {
    // 切换TAB
    switchTab(index) {
      if (this.currentTab === index) return
      this.currentTab = index
      // 清空搜索关键词
      this.searchKeyword = ''
      // 清空搜索防抖定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
        this.searchTimer = null
      }
    },
    
    // 搜索输入事件
    onSearchInput(e) {
      this.searchKeyword = e.detail.value
      // 防抖处理
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.triggerSearch()
      }, 300)
    },

    // 搜索确认事件
    onSearchConfirm() {
      this.triggerSearch()
    },

    // 搜索组件点击事件（保留兼容性）
    onSearch(searchData) {
      this.searchKeyword = searchData.name
      this.triggerSearch()
    },

    // 触发搜索
    triggerSearch() {
      if (this.currentTab === 0 && this.$refs.infoRef) {
        this.$refs.infoRef.performSearch(this.searchKeyword)
      } else if (this.currentTab === 1 && this.$refs.policyRef) {
        this.$refs.policyRef.performSearch(this.searchKeyword)
      }
    }
  },
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.information-container {
  padding: 26rpx 20rpx 0 20rpx;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .tab-container {
    flex-shrink: 0;
    background: #ffffff;
    border-radius: 16rpx 16rpx 0 0;
    .custom-tabs {
      display: flex;
      height: 88rpx;

      .tab-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;

        .tab-text {
          font-size: 32rpx;
          font-weight: 500;
          color: #666666;
          transition: color 0.3s ease;
        }

        .tab-line {
          position: absolute;
          bottom: 0;
          width: 60rpx;
          height: 6rpx;
          background: linear-gradient(90deg, #4A90E2 0%, #0066E9 100%);
          border-radius: 3rpx;
        }

        &.active {
          .tab-text {
            color: #0066E9;
            font-weight: 600;
          }
        }
      }
    }
  }

  .search-container {
    flex-shrink: 0;
    background-color: #ffffff;
    padding: 24rpx 32rpx;

    .custom-search-wrapper {
      .search-box {
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        border-radius: 48rpx;
        padding: 0 32rpx;
        height: 80rpx;

        .search-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
          opacity: 0.6;
        }

        .search-input {
          flex: 1;
          font-size: 28rpx;
          color: #333333;
          height: 100%;
          line-height: 80rpx;

          &::placeholder {
            color: #999999;
            font-size: 28rpx;
          }
        }
      }
    }
  }

  .content-wrapper {
    flex: 1;
    overflow: hidden;
    background-color: #f5f5f5;

    // 确保子组件能正常显示
    :deep(.information-page),
    :deep(.policy-page) {
      height: 100%;
      box-sizing: border-box;
    }

    // 调整子组件的滚动视图高度
    :deep(.scroll-Y) {
      height: 100% !important;
    }
  }
}
</style>
