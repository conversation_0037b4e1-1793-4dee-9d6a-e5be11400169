<template>
    <view class="information-page">
      <scroll-view
        :scroll-top="scrollTop"
        :style="scrollViewHeight"
        scroll-y="true"
        class="scroll-Y"
        :lower-threshold="lowerThreshold"
        @scrolltolower="scrolltolower"
        @scroll="scroll"
      >
        <view class="list">
          <view
            class="list-item"
            v-for="(item, idx) in displayList"
            :key="idx"
            @click="toDetail(item)"
          >
            <view class="item-image">
              <image :src="item.coverPic" class="cover-image" mode="aspectFill"></image>
            </view>
            <view class="item-content">
              <view class="item-title">{{ item.titleName }}</view>
              <view class="item-date">{{ item.publishTime }}</view>
            </view>
          </view>
        </view>
        <!-- 加载更多状态 -->
        <load-more :loadStatus="noticeLoadStatus" />
      </scroll-view>
    </view>
  </template>
  
  <script>
  import loadMore from "@/pagesD/components/load-more/index.vue";
  
  export default {
  props: {
    searchKeyword: {
      type: String,
      default: ''
    }
  },
  components: {
    loadMore,
  },
  data() {
    return {
      originalList: [], // 原始数据列表
      filteredList: [], // 过滤后的列表
        flag: false,
        windowHeight: 500, // 设置默认高度
        formData: {
          classType: "21", //资讯类型 
          category: "2", // 一级分类：2-高速资讯
          pageNum: 1,
          pageSize: 10
        },
        noticeLoadStatus: 0,
        scrollTop: 0,
        old: {
          scrollTop: 0
        },
        lowerThreshold: 120,
        informationList: [],
        classTypeObj:{
          11:'路况信息',
          12:'高速政策', 
          13:'出行提醒',
          14:'服务资讯'
        }
      };
    },
    computed: {
      // 显示的列表数据（根据搜索关键词过滤）
      displayList() {
        if (!this.searchKeyword) {
          return this.informationList
        }
        return this.informationList.filter(item => 
          item.titleName && item.titleName.includes(this.searchKeyword)
        )
      },
      // 动态计算滚动视图高度
      scrollViewHeight() {
        return 'height: calc(100vh - 252rpx);' // 减去TAB和搜索框的高度
      }
    },
    methods: {
      getInformationList() {
        this.noticeLoadStatus = 1;
        let params = {
          ...this.formData
        };
        this.$request
          .post(this.$interfaces.informationList, {
            data: params
          })
          .then(res => {
            if (res.code == 200) {
              console.log(res.data.data);
              let result = res.data?.data || [];
              if (res.data && res.data.data.length) {
                this.informationList = this.informationList.concat(result);
              } else {
                // 第一次加载且数据为空，显示暂无数据；否则显示没有更多了
                this.noticeLoadStatus = this.formData.pageNum === 1 ? 0 : 3;
                this.flag = true;
              }
              if (res.data && this.informationList.length == res.data.page.total) {
                this.noticeLoadStatus = 3;
                this.flag = true;
              }
            } else {
              this.noticeLoadStatus = 2;
            }
          })
          .catch(err => {
            console.log(err);
            this.noticeLoadStatus = 2;
          });
      },
      scrolltolower: function(e) {
        console.log(e);
        if (this.flag) return;
        let self = this;
  
        setTimeout(function() {
          self.formData.pageNum = self.formData.pageNum + 1;
          self.getInformationList();
        }, 500);
      },
      scroll: function(e) {
        this.old.scrollTop = e.detail.scrollTop;
      },
      // 执行搜索（由父组件调用）
      performSearch(keyword) {
        // 搜索功能通过computed属性自动实现，这里可以添加额外逻辑
        console.log('高速资讯搜索:', keyword)
      },
      // 格式化日期
      formatDate(dateStr) {
        if (!dateStr) return ''
        // 如果已经是YYYY-MM-DD格式，直接返回
        if (dateStr.includes('-') && dateStr.length === 10) {
          return dateStr
        }
        // 其他格式的日期处理
        const date = new Date(dateStr)
        if (isNaN(date.getTime())) return dateStr
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      },
      toDetail(row) {
        // articleType 1-富文本页面、2-链接跳转
        if (row.articleType == "2") {
          uni.navigateTo({
            url:
              "/pages/uni-webview/uni-webview?ownPath=" +
              encodeURIComponent(row.articleUrl)
          });
        }
        if (row.articleType == "1") {
          console.log(123123123);
          uni.navigateTo({
            url: `/pagesC/infoBusiness/detail?informationId=${row.informationId}&category=1`
          });
        }
      }
    },
    created() {
      this.getInformationList();
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .information-page {
    background: #ffffff;

    .list {
      background: #ffffff;

      .list-item {
        padding: 32rpx;
        display: flex;
        align-items: flex-start;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .item-image {
          width: 160rpx;
          height: 120rpx;
          border-radius: 12rpx;
          overflow: hidden;
          flex-shrink: 0;
          margin-right: 24rpx;

          .cover-image {
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        .item-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 120rpx;

          .item-title {
            font-size: 28rpx;
            font-weight: 500;
            color: #333333;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: auto;
          }

          .item-date {
            font-size: 24rpx;
            color: #999999;
            font-weight: 400;
            text-align: right;
            margin-top: 16rpx;
          }
        }
      }
    }
  }
  </style>