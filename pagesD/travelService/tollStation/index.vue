<template>
  <view class="toll-station">
    <tollSearch @search="search"></tollSearch>
    <!-- <view class="banner-wrapper">
      <travel-banner @bannerClick="handleBannerClick"></travel-banner>
    </view> -->
    <scroll-view class="item-wrapper" :style="{ height: contentHeight }" scroll-y="true" :scroll-top="scrollTop"
      :lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
      <tollStationItem @star="star" v-for="(item, index) in stationList" :item="item" :index="index + 1" :key="index">
      </tollStationItem>
      <load-more class="load-more" :loadStatus="noticeLoadStatus" />
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script>
import tollSearch from './components/tollSearch.vue'
import tollStationItem from './components/tollStationItem.vue'
import loadMore from '@/pages/home/<USER>/load-more/index.vue';
import travelBanner from '@/components/common/travel-banner.vue';
import {
  getLoginUserInfo,
  getTicket,
} from '@/common/storageUtil.js'

export default {
  components: {
    tollSearch,
    tollStationItem,
    loadMore,
    travelBanner
  },
  data() {
    return {
      showStarFlag: false,
      loadFlag: false,
      flag: false, // 分页控制标识
      lowerThreshold: 120,
      scrollTop: 0,
      noticeLoadStatus: 3,
      old: {
        scrollTop: 0
      },
      longitude: '',
      latitude: '',
      stationList: [],
      contentHeight: '100vh', // 默认值
      systemInfo: null,
      pageNo: 1,
      pageSize: 20,
      searchParams: {
        name: '',
        shiCode: ''
      }
    };
  },
  created() {
    this.getSystemInfo();
    this.getLocal();
  },
  methods: {
    // 获取系统信息，计算内容区域高度
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.systemInfo = res;
          // 计算内容区域高度
          // 搜索框高度约为 104rpx (含padding)
          // 转换px为rpx (750rpx = windowWidth px)
          const rpxRatio = 750 / res.windowWidth;
          const searchHeight = 104; // rpx
          const bannerHeight = 106; // banner高度 (86rpx + 20rpx margin)
          const statusBarHeight = res.statusBarHeight * rpxRatio; // 转为rpx
          const navBarHeight = 44 * rpxRatio; // 通常导航栏高度44px，转为rpx
          const safeAreaBottom = res.safeAreaInsets ? (res.screenHeight - res.safeAreaInsets.bottom) * rpxRatio : 0;

          // 计算列表区域可用高度
          let availableHeight = res.windowHeight * rpxRatio - searchHeight - bannerHeight;

          // 确保至少有足够的高度显示几个条目
          availableHeight = Math.max(availableHeight, 600);

          this.contentHeight = availableHeight + 'rpx';
          console.log('计算的内容高度:', this.contentHeight);
        }
      });
    },
    upper: function (e) {
      // 滚动到顶部
    },
    scrolltolower: function (e) {
      if (this.loadFlag || this.flag) return;
      let self = this;
      setTimeout(function () {
        self.pageNo = self.pageNo + 1;
        self.getStationList();
      }, 500)
    },
    scroll: function (e) {
      // 滚动事件
    },
    // 简化数据映射，直接使用接口字段
    mapApiDataToComponent(apiData) {
      return apiData.map(item => {
        return {
          ...item, // 直接使用接口所有字段
          // 只添加必要的计算字段
          entranceFlag: item.inOpenCnt > 0,
          exitFlag: item.outOpenCnt > 0
        };
      });
    },
    getStationList() {
      if (!this.longitude || !this.latitude) {
        this.noticeLoadStatus = 2; // 设置为加载失败状态
        return
      }
      this.noticeLoadStatus = 1;
      this.loadFlag = true

      let params = {
        name: this.searchParams.name || '',
        shiCode: this.searchParams.shiCode || '',
        longitude: this.longitude,
        latitude: this.latitude,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }

      this.$request
        .post(this.$interfaces.tollStationQuery, {
          data: params
        })
        .then((res) => {
          this.loadFlag = false
          console.log('收费站数据=====>>>>>', res.data)
          if (res.code == 200) {
            if (res.data && res.data.length > 0) {
              // 映射数据格式
              const mappedData = this.mapApiDataToComponent(res.data);
              
              if (this.pageNo == 1) {
                this.stationList = mappedData
              } else {
                this.stationList = this.stationList.concat(mappedData)
              }
              
              // 根据返回数据量判断是否还有更多数据
              if (res.data.length < this.pageSize) {
                // 返回的数据少于一页，说明没有更多了
                this.noticeLoadStatus = 3 // 没有更多了
                this.flag = true
              } else {
                // 还可能有更多数据
                this.noticeLoadStatus = 4 // 下滑加载
                this.flag = false
              }
            } else {
              // 无数据情况的处理
              if (this.pageNo == 1) {
                // 第一页无数据：清空列表，显示暂无数据
                this.stationList = []
                this.noticeLoadStatus = 0 // 暂无数据状态
                this.flag = true
              } else {
                // 非第一页无数据：说明没有更多了
                this.noticeLoadStatus = 3 // 没有更多了
                this.flag = true
              }
            }
          } else {
            this.noticeLoadStatus = 2; // 统一设置为加载失败状态
          }
        })
        .catch((error) => {
          this.loadFlag = false
          this.noticeLoadStatus = 2;
          uni.showModal({
            title: '提示',
            content: error.msg || '网络请求失败',
            showCancel: false
          })
        })
    },
    star(type, item) {
      if (!getTicket()) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      if (this.showStarFlag) return
      this.showStarFlag = true

      // 收藏收费站
      let params = {
        netUserId: getLoginUserInfo().userIdStr,
        collectTitle: item.name,
        collectType: 2 // 收费站类型
      }
      let url = ''
      if (type == 'star') {
        url = this.$interfaces.addCollect
      } else {
        url = this.$interfaces.delCollect
        params.id = item.starId
      }
      
      this.$request
        .post(url, {
          data: params
        })
        .then((res) => {
          this.showStarFlag = false
          console.log('收藏结果=====>>>>>', res)
          if (res.code == 200) {
            uni.showToast({
              icon: 'none',
              title: type == 'star' ? '收藏成功，可在我的收藏中查看' : '已取消收藏',
              duration: 2000
            })
            this.setStarStatus(type, item, res.data.id)
          } else {
            uni.showToast({
              icon: 'none',
              title: res.msg,
              duration: 2000
            })
          }
        })
        .catch((error) => {
          this.showStarFlag = false
          uni.showToast({
            icon: 'none',
            title: error.msg,
            duration: 2000
          })
        })
    },
    search(params) {
      console.log('搜索参数==', params)
      this.searchParams = params
      this.pageNo = 1
      this.flag = false // 重置分页标识
      this.getStationList()
    },
    getLocal() {
      uni.getLocation({
        type: 'wgs84',
        success: (res) => {
          console.log('当前位置的经度：' + res.longitude);
          console.log('当前位置的纬度：' + res.latitude);
          this.longitude = res.longitude
          this.latitude = res.latitude
          this.getStationList()
        },
        fail: (fail) => {
          this.noticeLoadStatus = 2; // 设置为加载失败状态
        }
      });
    },
    // 处理banner点击事件
    handleBannerClick(item) {
      console.log('收费站页面 - banner点击:', item);
      // TODO: 根据需求添加具体的跳转逻辑
    },
    // 设置收藏状态
    setStarStatus(type, listItem, starId) {
      this.stationList.forEach((item, index) => {
        if (item.name == listItem.name) {
          this.$set(this.stationList[index], 'starFlag', type == 'star' ? true : false)
          this.$set(this.stationList[index], 'starId', starId)
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.toll-station {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  margin: 20rpx 20rpx 0 20rpx;
  .banner-wrapper {
    padding: 0 20rpx;
  }
  .item-wrapper {
    flex: 1;
    overflow: hidden;
    background-color: #ffffff;
    padding: 0 20rpx;
  }

  ::deep .toll-station-item {

    &:last-child {
      margin-bottom: 30rpx;
      border-bottom: none;
    }
  }

  .safe-area-bottom {
    height: 34rpx; // 兼容iPhone X及以上机型底部安全区域
    background-color: #ffffff;
  }

  .load-more {
    /deep/ .u-f-ajc {
      padding-top: 20rpx;
    }
  }

}
</style>
