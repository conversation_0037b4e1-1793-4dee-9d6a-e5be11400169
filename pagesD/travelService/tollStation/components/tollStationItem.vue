<template>
  <view class="toll-station-item" @click="goDetail(item)">
    <image
      v-if="count"
      class="big-img"
      :src="imageUrl"
      mode=""
      @error="imageError"
    >
    </image>
    <view class="content-wrapper">
      <view class="middle-wrapper">
        <view class="title-wrapper">
          <view class="title">
            {{ item.name }}
          </view>
          <!-- <image
            v-if="!item.starFlag"
            @click.stop="star('star', item)"
            class="star-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/star_icon.png"
            mode=""
          ></image>
          <image
            v-else
            @click.stop="star('noStar', item)"
            class="star-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/star_icon_highlight.png"
            mode=""
          >
          </image> -->
        </view>
        <view class="desc-wrapper">
          <view class="left">
            {{ (item.shiName || '') + (item.xianName || '') }} | 距您{{ item.distance | kmFilter }}km
          </view>
          <view class="nav-btn" @click.stop="navigate(item)">
            <image class="nav-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/loaction.png" mode=""></image>
            <text class="nav-text">导航</text>
          </view>
        </view>
      </view>
      <view class="status-wrapper">
        <view class="status-item">
          <view class="status-label-bg " :class="item.entranceFlag ? 'green' : 'yellow'">
            <text class="status-label">入口</text>
          </view>
          <view class="status-badge" :class="item.entranceFlag ? 'status-normal' : 'status-abnormal'">
            <text class="status-text">{{ item.entranceFlag ? '正常' : '关闭' }}</text>
          </view>
        </view>
        <view class="status-item">
          <view class="status-label-bg" :class="item.exitFlag ? 'green' : 'yellow'">
            <text class="status-label">出口</text>
          </view>
          <view class="status-badge" :class="item.exitFlag ? 'status-normal' : 'status-abnormal'">
            <text class="status-text">{{ item.exitFlag ? '正常' : '关闭' }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    item: {
      type: Object
    },
    index: {
      type: Number
    }
  },
  components: {},
  computed: {
    count() {
      // 使用1-5循环
      return ((this.index % 5) + 1);
    }
  },
  data() {
    return {
      picPre: 'https://portal.gxetc.com.cn/toll-station-pic',
      imageUrl: ''
    };
  },
  created() {
    this.imageUrl = this.item.imgurl && this.item.imgurl.length > 0 ? this.picPre + '' + this.item.imgurl.replace(/\\/g, '/') :
      `https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/tollStation-${this.count}.png`
  },
  methods: {
    imageError(e) {
      this.imageUrl = `https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/tollStation-${this.count}.png`
    },
    star(type, item) {
      this.$emit("star", type, item);
    },
    goDetail(row) {
      uni.navigateTo({
        url: `/pagesD/travelService/tollStation/detail?id=${row.id}&name=${row.name}&distance=${row.distance}&img=${this.imageUrl}`
      });
    },
    navigate(item) {
      // 打开地图导航
      uni.openLocation({
        latitude: Number(item.lat),
        longitude: Number(item.lng),
        name: item.name,
        address: (item.shiName || '') + (item.xianName || '')
      })
    }
  },
  filters: {
    kmFilter(val) {
      return parseFloat(val).toFixed(1);
    }
  }
};
</script>

<style lang="scss" scoped>
.toll-station-item {
  position: relative;
  display: flex;
  font-family: PingFang SC, PingFang SC;
  margin: 20rpx;
  padding:24rpx 0 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;

  .big-img {
    width: 248rpx;
    height: 210rpx;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 24rpx;
    flex: 1;

    .title-wrapper {
      margin-bottom: 10rpx;
      display: flex;
      align-items: center;

      .title {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        // 防止名称过长溢出
        max-width: 300rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .star-icon {
        width: 32rpx;
        height: 32rpx;
        transition: transform 0.2s ease;
        
        &:active {
          transform: scale(1.2);
        }
      }
    }

    .desc-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 36rpx;
      margin-bottom: 20rpx;
      
      .left {
        // 防止地址过长溢出
        max-width: 360rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
      }
      
      .nav-btn {
        position: absolute;
        top: 24rpx;
        right: 0;
        width: 90rpx;
        height: 42rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ECF3FF;
        border-radius: 8rpx;
        color: #4F90FF;
        font-size: 24rpx;
        
        .nav-icon {
          width: 20rpx;
          height: 20rpx;
          margin-right: 8rpx;
        }
      }
    }

    .status-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .status-item {
        display: flex;
        align-items: center;
        
        .status-label-bg {
          width: 90rpx;
          height: 68rpx;
          background: rgba(8,186,129,0.1);
          text-align: center;
          line-height: 68rpx;
          border-radius: 8rpx 0rpx 0rpx 8rpx;
          
          &.green {
            background-color: #E8F5E9;
            color: #4CAF50;
          }
          
          &.yellow {
            background: rgba(255,144,0,0.1);
            color: #FF9800;
          }
          
          .status-label {
            font-size: 28rpx;
            font-weight: 400;
          }
        }
        
        .status-badge {
          width: 90rpx;
          height: 68rpx;
          background: #08BA81;
          border-radius: 0rpx 8rpx 8rpx 0rpx;
          text-align: center;
          line-height: 68rpx;
          
          .status-text {
            font-size: 28rpx;
            font-weight: 500;
            color: #FFFFFF;
          }
          
          &.status-normal {
            background-color: #08BA81;
          }
          
          &.status-abnormal {
            background-color: #FF6320;
          }
        }
      }
    }
  }
  
  &:active {
    background-color: #f9f9f9;
  }
}
</style> 