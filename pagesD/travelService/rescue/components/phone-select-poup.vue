<template>
  <view class="phone-select-poup">
    <u-popup
      v-model="show"
      mode="bottom"
      border-radius="14"
      z-index="9999"
      :closeable="true"
      close-icon-color="#333333"
      class="phone-popup"
    >
      <view class="content-wrapper">
        <view class="phone-list">
          <view class="phone-item" v-for="(phone, index) in phoneList" :key="index" @click="selectPhone(phone)">
            <text class="phone-number">{{ phone }}</text>
            <image class="phone-icon" src="@/static/map/call-blue.png"></image>
          </view>
        </view>
        
        <view class="cancel-btn" @click="closePoup">
          <text>取消拨打</text>
        </view>
        
        <view class="warning-tip">
          若上述电话无法拨通，请拨打0771-96333寻求救援。
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      phoneList: []
    };
  },
  methods: {
    openPoup(phones = []) {
      this.phoneList = phones.length > 0 ? phones : ["13546995622", "13544395477"]; // 使用传入的电话，或默认电话
      this.show = true;
    },
    closePoup() {
      this.show = false;
    },
    selectPhone(phone) {
      this.show = false;
      this.$emit('phoneSelected', phone);
    }
  }
};
</script>

<style lang="scss" scoped>
.phone-select-poup {
  z-index: 9999;
  
  .content-wrapper {
    padding: 30rpx;
    
    .phone-list {
      .phone-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100rpx;
        padding: 0 20rpx;
        border-bottom: 1px solid #f1f1f1;
        
        .phone-number {
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
        }
        
        .phone-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
    
    .cancel-btn {
      margin-top: 30rpx;
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      border-radius: 8rpx;
      background-color: #f5f5f5;
      color: #333;
      font-size: 32rpx;
    }
    
    .warning-tip {
      margin-top: 20rpx;
      text-align: center;
      color: #f82a3c;
      font-size: 26rpx;
      line-height: 1.5;
    }
  }
}
</style> 