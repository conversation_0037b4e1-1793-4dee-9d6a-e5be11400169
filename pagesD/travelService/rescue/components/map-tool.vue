<template>
  <view class="map-tool">
    <view
      class="item"
      @click="toolTap(item)"
      v-for="item in toolList"
      :key="item.id"
    >
      <image class="cover-image" :src="item.src"></image>
      <view class="text">{{ item.name }}</view>
      <view class="line"></view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      toolList: [
        {
          id: 1,
          type: "phone",
          name: "电话",
          src: "../../../static/map/call.png"
        },
        {
          id: 2,
          type: "fee",
          name: "收费",
          src: "../../../static/map/fee.png"
        }
      ]
    };
  },
  methods: {
    toolTap(item) {
      if (item.type == "phone") {
        // uni.makePhoneCall({
        //   phoneNumber: "0771-96333" //仅为示例
        // });
      } else {
        console.log('fee');
      }
      this.$emit("toolTap", item);
    }
  }
};
</script>

<style lang="scss" scoped>
.map-tool {
  width: 80rpx;
  box-sizing: border-box;
  padding: 20rpx 12rpx;
  background: #fff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  .item {
    // width: 44rpx;
    margin-bottom: 14rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    &:last-child {
      margin-bottom: 0;
      .line {
        width: 0;
      }
    }
    .cover-image {
      width: 44rpx;
      height: 44rpx;
      margin-bottom: 4rpx;
    }
    .text {
      width: 54rpx;
      white-space: break-spaces;
      padding-bottom: 12rpx;
      font-size: 20rpx;
      color: #666666;
      // border-bottom: 1rpx solid #e1e1e1;
    }
    .line {
      width: 56rpx;
      height: 1rpx;
      background: #e1e1e1;
    }
  }
}
</style>