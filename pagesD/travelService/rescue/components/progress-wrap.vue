<template>
  <view class="progress-wrap">
    <view class="wrap-title">
      <view class="bar"></view>订单追踪
      <view class="show-more" @click="showMore">{{
        show ? "收起" : "展开"
      }}</view>
    </view>
    <view class="time-line__wrapper">
      <view class="time-line" v-if="show">
        <u-time-line>
          <u-time-line-item nodeTop="6">
            <!-- 此处自定义了左边内容，用一个图标替代 -->
            <template v-slot:node>
              <view class="steps-wrapper">
                <view class="steps-arrow">
                  <view class="steps-hd">
                    <view class="steps-item"> </view>
                  </view>
                </view>
              </view>
            </template>
            <template v-slot:content>
              <view>
                <view class="u-order-title">{{
                  orderStatusObj[timeListFirst[0].orderStatus]
                }}</view>
                <view class="u-order-desc">{{ timeListFirst[0].remarks }}</view>
                <view class="u-order-time">{{
                  timeListFirst[0].createTime
                }}</view>
              </view>
            </template>
          </u-time-line-item>
          <u-time-line-item v-for="(item, index) in timeList" :key="index">
            <template v-slot:content>
              <view>
                <view class="u-order-title">{{
                  orderStatusObj[item.orderStatus]
                }}</view>
                <view class="u-order-desc">{{ item.remarks }}</view>
                <view class="u-order-time">{{ item.createTime }}</view>
              </view>
            </template>
          </u-time-line-item>
        </u-time-line>
      </view>
      <tLoading :isShow="isLoading" />
    </view>
  </view>
</template>

<script>
//订单状态:0.已提交申请;1.救援已出发;2.救援已到达;3.订单已完成，请评价;4.订单已完成，已评价;5.订单已被受理;6.订单已被取消;7.您已取消订单
let orderStatusObj = {
  0: "已提交申请",
  1: "救援已出发",
  2: "救援已到达",
  3: "订单已完成",
  4: "订单已完成",
  5: "订单已被受理",
  6: "订单已被取消",
  7: "您已取消订单"
};
export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    },
    orderId: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      isLoading: false,
      id: "",
      timeList: [],
      timeListFirst: [],
      show: false,
      orderProgress: [],
      orderStatusObj
    };
  },
  mounted() {
    this.getTimeLine();
  },
  methods: {
    getTimeLine() {
      this.isLoading = true;
      let params = {
        id: this.orderId
      };
      this.$request
        .post(this.$interfaces.getOrderStatusById, {
          data: params
        })
        .then(res => {
          this.isLoading = false;
            console.log(res,'orderProgress');
          if (res.code == 200) {
            let orderProgress = res.data.data
            // let orderProgress = [
            //   {
            //     id: 124547834235,
            //     orderStatus: 0,
            //     createTime: "2023-06-01 12:00"
            //   },
            //   {
            //     id: 124547834235,
            //     orderStatus: 5,
            //     createTime: "2023-06-01 12:03"
            //   },
            //   {
            //     id: 124547834235,
            //     orderStatus: 2,
            //     createTime: "2023-06-01 13:00"
            //   }
            // ];
            this.timeListFirst.push(orderProgress[0]);
            let arrList = JSON.parse(JSON.stringify(orderProgress));
            arrList.shift();
            this.timeList = arrList;
            this.show = true;
          } else {
            uni.showModal({
              title: "提示",
              content: res.msg,
              showCancel: false
            });
          }
        })
        .catch(err => {
          this.isLoading = false;
          uni.showModal({
            title: "提示",
            content: err.msg,
            showCancel: false
          });
        });
    },
    showMore() {
      this.show = !this.show;
    }
  }
};
</script>

<style lang="scss" scoped>
.progress-wrap {
  width: 100%;
}
.wrap-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
  // margin-bottom: 10rpx;
  padding-left: 8rpx;
  font-weight: 500;
  background: #e9effe;
  height: 90rpx;
  padding-left: 28rpx;
  .bar {
    width: 6rpx;
    height: 28rpx;
    background: #0066e9;
    border-radius: 20%;
    margin-right: 20rpx;
  }
  .show-more {
    position: absolute;
    right: 30rpx;
    color: #0066e9;
  }
}
.time-line__wrapper {
  // margin: 0 20rpx;
  padding: 20rpx 0 10rpx 28rpx;
  // margin-top: 20rpx;
  width: 100%;
  background: #ffffff;
  border-radius: 12rpx;
}

.time-line {
  padding: 30rpx;
  padding-top: 0;
}

.u-node {
  width: 44rpx;
  height: 44rpx;
  border-radius: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #d0d0d0;
}

.u-order-title {
  margin-bottom: 8rpx;
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
}

.u-order-desc {
  margin-bottom: 8rpx;
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
}

.u-order-time {
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
}

/deep/.u-dot {
  width: 17rpx !important;
  height: 17rpx !important;
  background: #d4d4d4 !important;
}

/deep/.u-time-axis::before {
  width: 3rpx !important;
  background: #d4d4d4 !important;
}

// /deep/.u-time-axis-node {
// 	top: 12rpx !important;
// }

.steps-wrapper {
  position: relative;
  // height: 27rpx;
  // justify-content: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.steps-arrow {
  position: relative;
  width: 27rpx;
  height: 27rpx;
  background: rgba(150, 193, 255, 0.82);
  border-radius: 50%;
  bottom: 0;
}

.steps-hd {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 17rpx;
  height: 17rpx;
  background: #5591ff;
  border-radius: 50rpx;
}

.steps-title {
  color: #323435;
  font-weight: 700;
}
</style>
