<template>
  <view class="info-wrap">
    <view class="wrap-title"><view class="bar"></view>救援详情</view>
    <view class="form-wrap">
      <view class="input-wrapper">
        <view class="detail-title">
          救援车辆
        </view>
        <view class="detail-value">
          {{ rescueDetailVo.isSetOut == 1?((rescueDetailVo.rescueCarNo?rescueDetailVo.rescueCarNo:'')+rescueDetailVo.isSetOutStr): '未出发' }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          出发地点
        </view>
        <view class="detail-value">
          {{ rescueDetailVo.placeRegist || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          预计到达时间
        </view>
        <view class="detail-value">
          {{
            rescueDetailVo.estimatedArrivalTime ? rescueDetailVo.estimatedArrivalTime : rescueCarInfo.etaMinute ? rescueCarInfo.etaMinute + " 分钟" : "-"
          }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          救援联系电话
        </view>
        <view class="detail-value">
          {{ rescueDetailVo.rescuePhone || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          预计费用
        </view>
        <view class="detail-value">
          <view class="input"
            ><text class="price">
              {{ rescueDetailVo.expectedCost || "244" }}元</text
            >
            起</view
          >
        </view>
      </view>
      <view class="input-wrapper" v-if="rescueDetailVo.trailerMileage">
        <view class="detail-title">
          拖车里程
        </view>
        <view class="detail-value">
          {{ rescueDetailVo.trailerMileage }}公里
        </view>
      </view>
      <view class="input-wrapper" v-if="rescueDetailVo.rescueTime">
        <view class="detail-title">
          救援用时
        </view>
        <view class="detail-value"> {{ rescueDetailVo.rescueTime }}小时 </view>
      </view>
      <view class="input-wrapper" v-if="rescueDetailVo.realCost">
        <view class="detail-title">
          实收费用
        </view>
        <view class="detail-value"> {{ rescueDetailVo.realCost }}元 </view>
      </view>
    </view>
    <view class="handle-wrap" v-if="show">
      <button class="weui-btn weui-btn_primary" @click="cancelOrder">
        取消订单
      </button>
      <view class="tips"
        ><text class="red" >{{ countdown }}</text
        ><text>秒后</text
        >取消订单，您将会被收取一定的救援车辆放空费</view
      >
    </view>
  </view>
</template>

<script>
export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    },
    rescueCarInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        carTypeLabel: ""
      },
      countdown: 0,
      intervalId: null,
      show: false
    };
  },
  computed: {
    rescueDetailVo() {
      return this.detailInfo.rescueDetailVo || {};
    },
    orderDetailVo() {
      return this.detailInfo.orderDetailVo || {};
    }
  },
  watch: {
    "detailInfo.countdown": {
      handler(newVal, oldVal) {
        if (newVal > 0 && this.orderDetailVo.orderStatus == 0) {
          this.countdown = newVal;
          this.startCountdown();
        }
      }
    }
  },
  methods: {
    // 倒计时
    startCountdown() {
      this.show = true;
      this.intervalId = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          this.clearCountdown();
          // 倒计时结束时的操作
          this.show = false;
          console.log("Countdown finished");
        }
      }, 1000); // 每秒减少一次
    },
    clearCountdown() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
    async cancelOrder() {
      let params = {
        id: this.orderDetailVo.id
      };
      let res = await this.$request.post(this.$interfaces.travelCancelOrder, {
        data: params
      });
      if (res.code == 200 && res.data.success) {
        console.log(res, "res");
        uni.showModal({
          title: "提示",
          content: "取消成功",
          showCancel: false,
          confirmText: "确认",
          success: res => {
            if (res.confirm) {
              uni.redirectTo({
                url: "/pagesD/travelService/rescue/rescue-order"
              });
            }
          }
        });
      }else{
        uni.showToast({
          title: res.data.desc,
          icon: "none"
        });
      }
      console.log(res, 333);
    }
  },
  mounted() {
    // this.startCountdown();
  },
  unmounted() {
    this.clearCountdown();
  }
};
</script>

<style lang="scss" scoped>
.info-wrap {
  // margin-top: 20rpx;
  background: #fff;
  position: relative;
  .btns-box {
    display: flex;
    .btn {
      height: 58rpx;
      background: rgba(0, 102, 233, 0.13);
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #0066e9;
      line-height: 58rpx;
      margin-right: 16rpx;
      padding: 0 30rpx;
      margin-bottom: 20rpx;
      &.checkd {
        background: #0066e9;
        color: #ffffff;
      }
    }
  }
  .wrap-title {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 10rpx;
    padding-left: 8rpx;
    font-weight: 500;
    background: #e9effe;
    height: 90rpx;
    padding-left: 28rpx;
    .bar {
      width: 6rpx;
      height: 28rpx;
      background: #0066e9;
      border-radius: 20%;
      margin-right: 20rpx;
    }
  }
  .form-wrap {
    padding: 0 28rpx 18rpx 28rpx;
    .input-wrapper {
      // padding: 0 30rpx;
      display: flex;
      // justify-content: space-between;
      align-items: center;
      min-height: 90rpx;
      line-height: 40rpx;
      color: rgba(16, 16, 16, 100);
      font-size: 28rpx;
      // text-align: center;
      font-family: Arial;
      background-color: $uni-bg-color;
      border-bottom: 1rpx solid #ebebeb;
      .detail-title {
        flex: 0 0 260rpx;
        width: 260rpx;
        // text-align: left;
        font-size: 30rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #818181;
      }
      .detail-value {
        color: #333333;
        width: 430rpx;
        word-wrap: break-word;
        white-space: pre-wrap;
      }
      .input {
        flex: 1;
        .price {
          color: rgba(255, 158, 89, 1);
        }
      }

      .auto-input {
        position: relative;
      }

      .autofill {
        position: absolute;
        right: 30rpx;
        height: 120rpx;
        line-height: 120rpx;
        padding: 0 20rpx;
        color: #0081ff;
        z-index: 98;
      }
    }
  }
  .side {
    position: absolute;
    top: 30rpx;
    right: 0;
  }
  .handle-wrap {
    text-align: center;
    padding-bottom: 18rpx;
    .weui-btn {
      width: 640rpx;
      height: 100rpx;
      margin-bottom: 24rpx;
      background: #0066e9;
    }
    .tips {
      color: #666666;
      font-size: 24rpx;
      .red {
        color: #ff0000;
      }
    }
  }
}
</style>