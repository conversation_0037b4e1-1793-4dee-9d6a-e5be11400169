<template>
  <view class="vehicle-type-picker" v-if="show">
    <view class="mask" @click="close"></view>
    <view class="picker-content">
      <view 
        class="picker-item"
        v-for="(item, index) in list" 
        :key="index"
        @click="selectItem(item)"
      >
        <view class="item-label">{{item.label.split('-')[0]}}</view>
        <view class="item-desc" v-if="item.description">{{item.description}}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    selectItem(item) {
      this.$emit('select', item)
      this.$emit('close')
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-type-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  
  .mask {
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
  }
  
  .picker-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 20rpx 0;
    max-height: 40vh;
    overflow-y: auto;
  }
  
  .picker-item {
    padding: 24rpx 40rpx;
    border-bottom: 1rpx solid #f5f5f5;
    text-align: center;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-label {
      font-size: 32rpx;
      color: #0066e9;
      font-weight: 500;
      margin-bottom: 8rpx;
    }
    
    .item-desc {
      font-size: 26rpx;
      color: #999;
    }
  }
}
</style>