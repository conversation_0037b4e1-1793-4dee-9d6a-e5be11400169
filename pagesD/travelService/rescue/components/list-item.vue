<template>
  <view class="rescue-item" @click="goDetail">
    <view class="item-type">
      <view class="block">
        <view class="carId">{{ itemInfo.carNo }}</view>
        <view class="type" :class="{ gray: current == 1 }">{{
          itemInfo.eventTypeName
        }}</view></view
      >
      <view class="block">
        <view class="status">{{ statusObj[itemInfo.orderStatus] }}</view>
        <view class="right-arrow"></view
      ></view>
    </view>
    <view class="item-content">
      <view class="item-wrap">
        <image
          src="~@/pagesD/static/map/rescue-time.png"
          class="enery-img"
          mode="widthFix"
        ></image>
        <text>{{ itemInfo.applyTime }}</text>
      </view>
      <view class="item-wrap">
        <image
          src="~@/pagesD/static/map/loaction-point.png"
          class="enery-img"
          mode="widthFix"
        ></image>
        <text>{{ itemInfo.locationDirection }}</text>
      </view>
    </view>
    <view class="predict-time" v-if="itemInfo.takeTime">
      <view class="msg-text">救援车辆预计</view>
      <view class="msg-time">
        <text>{{ itemInfo.takeTime }}</text>
        <view class="img-box">
          <image
            src="~@/pagesD/static/map/min.png"
            class="enery-img"
            mode="widthFix"
          ></image>
        </view>

        <text>分钟</text>
      </view>
      <view class="msg-text">到达现场</view>
    </view>
  </view>
</template>

<script>
// 订单状态:0.已提交申请;1.救援已出发;2.救援已到达;3.订单已完成，请评价;4.订单已完成，已评价;5.订单已被受理;6.订单已被取消;7.您已取消订单
export default {
  props: {
    itemInfo: {
      type: Object,
      default: () => {}
    },
    current:{
      type:Number,
      default:0
    }
  },
  data() {
    return {
      statusObj: {
        0: "已提交申请",
        1: "救援已出发",
        2: "救援已到达",
        3: "订单已完成",
        4: "订单已完成",
        5: "订单已被受理",
        6: "订单已被取消",
        7: "您已取消订单"
      }
    };
  },
  methods: {
    goDetail() {
      uni.navigateTo({
        url: `/pagesD/travelService/rescue/order-detail?id=${this.itemInfo.id}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.rescue-item {
  width: 710rpx;
  background: #ffffff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  margin-bottom: 20rpx;
  padding-bottom: 14rpx;
  .item-type {
    width: 100%;
    height: 76rpx;
    padding: 20rpx 26rpx 16rpx 18rpx;
    display: flex;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    border-bottom: 2rpx solid #f0f0f0;
    align-items: center;
    justify-content: space-between;
    .block {
      display: flex;
      align-items: center;
      &:last-child {
        justify-content: flex-end;
      }
    }
    .type {
      // width: 92rpx;
      height: 34rpx;
      background: rgba(255, 158, 89, 0.1);
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      font-size: 24rpx;
      color: #ff9e59;
      font-weight: 400;
      font-family: PingFang SC, PingFang SC;
      text-align: center;
      margin-left: 8rpx;
      padding: 0rpx 22rpx;
      &.gray {
        background: rgba(149, 149, 149, 0.1);
        color: #959595;
      }
    }
    .status {
      font-size: 24rpx;
      color: #999999;
      font-weight: 400;
      margin-right: 5rpx;
    }
    .right-arrow {
      width: 24rpx;
      height: 24rpx;
      background-image: url("https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/myAccount/right-arr.png");
      background-repeat: no-repeat;
      background-size: 24rpx;
    }
  }
  .item-content {
    padding: 28rpx 30rpx 5rpx 30rpx;
    .item-wrap {
      display: flex;
      margin-bottom: 20rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
    }
  }
  .predict-time {
    display: flex;
    align-items: center;
    padding-left: 38rpx;
    margin-bottom: 10rpx;
    .msg-time {
      font-weight: 400;
      font-size: 36rpx;
      color: #ff9e59;
      margin-right: 64rpx;
      display: flex;
      align-items: center;
      text {
        font-weight: 400;
        font-size: 28rpx;
        color: #ff9e59;
        &:first-child {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 36rpx;
          color: #ff9e59;
        }
      }
      .img-box {
        position: relative;
        width: 32rpx;
        height: 32rpx;
        image {
          position: absolute;
          top: -15rpx;
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .msg-text {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      &:first-child {
        margin-right: 64rpx;
      }
    }
  }
}
</style>