<template>
  <view class="side-nav">
    <view
      class="nav-item"
      @click="toolTap(item)"
      v-for="item in navList"
      :key="item.id"
    >
      <image class="nav-image" :src="item.src"></image>
      <view class="nav-text">{{ item.name }}</view>
      <view class="line"></view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      navList: [
        {
          id: 1,
          type: "rescueType",
          name: "救援",
          src: "../../../static/map/rescue.png"
        },
        {
          id: 2,
          type: "order",
          name: "订单",
          src: "../../../static/map/order.png"
        }
      ]
    };
  },
    methods: {
    toolTap(item) {
      this.$emit("toolTap", item);
    }
  }
};
</script>

<style lang="scss" scoped>
.side-nav {
  width: 102rpx;
  box-sizing: border-box;
  padding: 20rpx 12rpx;
  background: #ffffff;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.16);
  border-radius: 24rpx 0rpx 0rpx 24rpx;
  border: 1rpx solid #f0f0f0;
  .nav-item {
    // width: 44rpx;
    margin-bottom: 14rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    &:last-child {
      margin-bottom: 0;
      .line {
        width: 0;
      }
    }
    .nav-image {
      width: 44rpx;
      height: 44rpx;
      margin-bottom: 4rpx;
    }
    .nav-text {
      // width: 54rpx;
      white-space: break-spaces;
      padding-bottom: 12rpx;
      font-size: 20rpx;
      color: #666666;
      // border-bottom: 1rpx solid #e1e1e1;
    }
    .line {
      width: 86rpx;
      height: 1rpx;
      background: #e1e1e1;
    }
  }
}
</style>