<template>
  <view class="info-wrap">
    <view class="wrap-title"><view class="bar"></view> 订单信息</view>
    <view class="form-wrap">
      <view class="input-wrapper">
        <view class="detail-title">
          申请时间
        </view>
        <view class="detail-value">
          {{ orderDetailVo.applyTime || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          订单状态
        </view>
        <view class="detail-value">
          {{ orderDetailVo.orderStatusStr || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          事发位置
        </view>
        <view class="detail-value">
          {{ orderDetailVo.position || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          申请类型
        </view>
        <view class="detail-value">
          {{ orderDetailVo.applyType || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          申请人
        </view>
        <view class="detail-value">
          {{ orderDetailVo.customName || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          联系电话
        </view>
        <view class="detail-value">
          {{ orderDetailVo.customPhone || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          车牌号码
        </view>
        <view class="detail-value">
          {{ orderDetailVo.customCarNo || "-" }}
        </view>
      </view>
      <view class="input-wrapper">
        <view class="detail-title">
          车辆类型
        </view>
        <view class="detail-value">
          {{ orderDetailVo.carType || "-" }}
        </view>
      </view>
      <view class="input-wrapper val-img">
        <view class="detail-title">
          上传图片
        </view>
        <view class="detail-value val-img">
          <view
            class="box-img"
            v-for="(item, idx) in imgPathList"
            :key="idx"
            @tap="ViewImage(item)"
          >
            <image :src="item" class="enery-img"></image
          ></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        carTypeLabel: ""
      }
    };
  },
  methods: {
    ViewImage(path) {
      let newArr = [];
      newArr.push(path);
      uni.previewImage({
        urls: newArr
      });
    }
  },
  computed: {
    rescueDetailVo() {
      return this.detailInfo.rescueDetailVo || {};
    },
    orderDetailVo() {
      return this.detailInfo.orderDetailVo || {};
    },
    imgPathList() {
      return this.orderDetailVo.imgPathList || [];
    }
  }
};
</script>

<style lang="scss" scoped>
.info-wrap {
  margin-top: 20rpx;
  background: #fff;
  position: relative;
  .btns-box {
    display: flex;
    .btn {
      height: 58rpx;
      background: rgba(0, 102, 233, 0.13);
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #0066e9;
      line-height: 58rpx;
      margin-right: 16rpx;
      padding: 0 30rpx;
      margin-bottom: 20rpx;
      &.checkd {
        background: #0066e9;
        color: #ffffff;
      }
    }
  }
  .wrap-title {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 10rpx;
    padding-left: 8rpx;
    font-weight: 500;
    background: #e9effe;
    height: 90rpx;
    padding-left: 28rpx;
    .bar {
      width: 6rpx;
      height: 28rpx;
      background: #0066e9;
      border-radius: 20%;
      margin-right: 20rpx;
    }
  }

  .form-wrap {
    padding: 0 28rpx 18rpx 28rpx;
    .input-wrapper {
      // padding: 0 30rpx;
      display: flex;
      // justify-content: space-between;
      align-items: center;
      height: 90rpx;
      color: rgba(16, 16, 16, 100);
      font-size: 28rpx;
      // text-align: center;
      font-family: Arial;
      background-color: $uni-bg-color;
      border-bottom: 1rpx solid #ebebeb;
      &.val-img {
        align-items: unset;
        height: auto;
        padding: 20rpx 0;
      }
      .detail-title {
        flex: 0 0 260rpx;
        width: 260rpx;
        // text-align: left;
        font-size: 30rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #818181;
      }
      .detail-value {
        color: #333333;
        display: flex;
        .box-img {
          width: 134rpx;
          height: 134rpx;
          margin-right: 14rpx;
          .enery-img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .input {
        flex: 1;
        .price {
          color: rgba(255, 158, 89, 1);
        }
      }

      .auto-input {
        position: relative;
      }

      .autofill {
        position: absolute;
        right: 30rpx;
        height: 120rpx;
        line-height: 120rpx;
        padding: 0 20rpx;
        color: #0081ff;
        z-index: 98;
      }
    }
  }
  .side {
    position: absolute;
    top: 30rpx;
    right: 0;
  }
}
</style>