<template>
  <view class="rescue">
    <view class="info-title">
      <image class="warn-img" src="~@/pagesD/static/map/warnImg.png"></image>
      请确保人员已撤离安全地带，做好安全防护！
    </view>
    <view class="location-info" v-if="location.latitude && location.longitude">
      {{ location.latitude }},{{ location.longitude }}
    </view>
    <feePoup ref="feePoup" />
    <guidePoup ref="guidePoup" />
    <phoneSelectPoup ref="phoneSelectPoup" @phoneSelected="handlePhoneSelected" />

    <MapWrapCom :scale="scale" :mapLocation="mapLocation" ref="MapWrapCom" class="MapWrapCom" height="692rpx"
      @updateLocation="getLoaction" @getAddressInfo="getAddressInfo" />

    <!-- 复位定位按钮 -->
    <image class="reset-location" @click="resetLocation" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/orientation.png"></image>

    <!-- 救援引导按钮位置调整 -->
    <image class="rescue-img" @click="showGuide"
      src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/guide.png"></image>

    <!-- 地图工具 -->
    <mapTool @toolTap="toolTap" class="controls-wrap" />

    <!-- 救援信息区域 -->
    <view class="rescue-info">
      <view class="rescue-phone">
        <text>救援电话</text>
        <text class="phone-number">{{ rescuePhoneNumber }}</text>
      </view>
      <view class="rescue-btn" @click="callPhone">一键拨打</view>
    </view>

    <!-- 救援表单 -->
    <rescueForm ref="rescueForm" :recommendAddress="recommendAddress" @toolTap="toolTap" />
    <view class="search-btn">
      <button class="weui-btn weui-btn_primary " :class="{isGray:!isApply}"  @click="onSearchHandle">
        确认提交救援申请
      </button>
    </view>
    <!-- <view class="agreement" @click="goAgreement"
      >申请服务，即标识已阅读并同意
      <text class="txt">《道路救援服务协议》</text></view
    > -->

         <!-- 底部Tab按钮 -->
     <view class="bottom-tabs">
       <view class="tab-btn rescue-tab" :class="{ active: current === 0 }" @click="handleTabChange(0)">
         <image class="tab-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/rescue_icon.png"></image>
         救援
       </view>
       <view class="tab-btn order-tab" :class="{ active: current === 1 }" @click="handleTabChange(1)">
         <image class="tab-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/order_icon.png"></image>
         订单
       </view>
     </view>

    <neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
      :confirm-text="modal.confirmText" @confirm="onConfirmHandle" @close="close">
      <view class="message-content-wrapper">
        <view class="title">提示</view>
        <view class="msg_desc">
          <view class="msg_desc-content">
            <view style="text-align:left;margin-bottom:8rpx">订单收费规则:</view>
            <view style="text-align:left;margin-bottom:8rpx">1、订单提交3分钟内您可在线免费取消。</view>
            <view style="text-align:left;margin-bottom:8rpx">2、订单提交超过3分钟后不支持在线取消。</view>
            <view style="text-align:left;margin-bottom:8rpx">3、如向客服申请取消，救援服务仍将会向您收取一定的救援车辆放空费用，请您知悉。</view>
            <view style="text-align:left;margin-bottom:8rpx">您现在确定要申请救援吗?</view>
          </view>
        </view>
      </view>
    </neil-modal>
  </view>
</template>

<script>
import MapWrapCom from "@/pagesD/components/map/map.vue";
import mapTool from "./components/map-tool.vue";
import rescueForm from "./components/rescue-form.vue";
import feePoup from "./components/fee-poup.vue";
import guidePoup from "./components/guide-poup.vue";
import phoneSelectPoup from "./components/phone-select-poup.vue";
import { getEtcAccountInfo, getLoginUserInfo } from "@/common/storageUtil.js";
import { checkPhone } from "@/common/util.js";
export default {
  components: {
    MapWrapCom,
    mapTool,
    rescueForm,
    feePoup,
    guidePoup,
    phoneSelectPoup
  },
  data() {
    return {
      // 添加Tab相关数据
      tabList: [
        {
          name: "救援",
          value: "0"
        },
        {
          name: "订单",
          value: "1"
        }
      ],
      current: 0,
      scale: 14,
      mapLocation: {},
      recommendAddress: {},
      directionsArr: [],
      modal: {
        show: false,
        close: false,
        showCancel: true,
        align: "left",
        confirmText: "确认"
      },
      address: "",
      location: {},
      isApply: false,
      isSubmitting: false,
      
      // 救援电话相关数据
      rescuePhoneNumber: "0771-96333", // 默认救援电话
      rescuePhones: ["***********", "***********"] // 模拟的两个手机号
    };
  },
  methods: {
    // 添加Tab切换方法
    change(index) {
      this.current = index;
      if (index === 1) {
        // 切换到订单页面
        uni.navigateTo({
          url: "/pagesD/travelService/rescue/rescue-order"
        });
      }
    },
    // 处理底部Tab切换
    handleTabChange(index) {
      this.current = index;
      if (index === 1) {
        // 切换到订单页面
        uni.navigateTo({
          url: "/pagesD/travelService/rescue/rescue-order"
        });
      }
    },
    // 添加复位定位方法
    resetLocation() {
      if (this.$refs.MapWrapCom) {
        this.$refs.MapWrapCom.handleMoveToLocation();
      }
    },
    toolTap(item) {
      console.log(item);
      if (item.type == "fee") {
        this.$refs.feePoup.openPoup();
      } else if (item.type == "phone") {
        this.callPhone();
      }
    },
    showGuide() {
      this.$refs.guidePoup.openPoup();
    },
    // 修改电话呼叫方法
    callPhone() {
      // 显示电话选择弹窗
      // this.$refs.phoneSelectPoup.openPoup(this.rescuePhones);
     this.handlePhoneSelected(this.rescuePhoneNumber);
    },
    // 处理电话选择
    handlePhoneSelected(phone) {
      uni.makePhoneCall({
        phoneNumber: phone
      });
    },
    goAgreement() {
      var callcenter = "https://portal.gxetc.com.cn/agreement/user.png";
      uni.navigateTo({
        url:
          "/pages/uni-webview/h5-webview?ownPath=" +
          encodeURIComponent(callcenter)
      });
    },
    async getLoaction(data) {
      console.log(data, "getLoaction");
      this.location = data;
      // let params = {
      //   pointLat: 23.94017116970486,
      //   pointLon: 109.32941080729167
      // };
      let params = {
        pointLat: data.latitude,
        pointLon: data.longitude
      }
      let res = await this.$request.post(
        this.$interfaces.matchPilePointByLonLat,
        {
          data: params
        }
      );
      console.log(res, "recommendAddress");

      if (res.code == 200 && res.data.data) {
        if (res.data.data.tenantId == 4520) return
        this.recommendAddress = res.data.data;
        let query = {
          expWayId: res.data.data.expressWayId,
          tenantId: res.data.data.tenantId,
          pileUpDownFlag: res.data.data.pileUpDownFlag,
          pileNo: res.data.data.pileNo
        }; 
        this.isApply = true
        this.$refs.rescueForm.getByExpWayId(query);
        
        // 模拟设置是否支持一键救援（实际项目中可能需要从接口获取）
        // 这里假设特定条件下不支持一键救援
        // this.supportOneClickRescue = false; // This line is removed
      }
    },
    onSearchHandle() {
      if (!this.isApply || this.isSubmitting) return
      
      let info = this.$refs.rescueForm.postInfo();
      let { eventContactPhone, eventVehicleDTOS } = info.eventAssitInfoEntity;
      if (!checkPhone(eventContactPhone)) {
        uni.showModal({
          title: "提示",
          content: "手机号码格式不正确",
          showCancel: false
        });
        return false;
      }
      if (!eventVehicleDTOS[0].name) {
        uni.showModal({
          title: "提示",
          content: "请输入车牌号码",
          showCancel: false
        });
        return;
      }

      this.modal.show = true;
    },
    async onConfirmHandle() {
      if (this.isSubmitting) {
        console.log('防重复提交：正在提交中');
        return;
      }
      this.isSubmitting = true;
      this.modal.show = false;
      
      uni.showLoading({
        mask: true,
        title: `正在提交救援申请...`
      });
      
      try {
        let info = this.$refs.rescueForm.postInfo();
        let params = {
          ...info,
          gzcxUserId: getLoginUserInfo().userIdStr,
          expressWayId: this.recommendAddress.expressWayId,
          tenantId: this.recommendAddress.tenantId,
          location: this.address,
          lat: this.location.latitude,
          lon: this.location.longitude
        };
        
        let res = await this.$request.post(this.$interfaces.eventHis, {
          data: params
        });
        uni.hideLoading();
        if (res.code == 200 && res.data.success) {
          
          await new Promise(resolve => {
            uni.showModal({
              title: "提示",
              content: "救援申请成功",
              showCancel: false,
              confirmText: "确认",
              success: res => {
                if (res.confirm) {
                  uni.navigateTo({
                    url: "/pagesD/travelService/rescue/rescue-order"
                  });
                }
                resolve();
              }
            });
          });
        } else {
          uni.showToast({
            title: res.data.desc || '提交失败',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('救援申请提交失败:', error);
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        uni.hideLoading();
        setTimeout(() => {
          this.isSubmitting = false;
        }, 1000);
      }
    },
    close() {
      this.modal.show = false;
    },
    async init() {
      let params = {};
      let res = await this.$request.post(this.$interfaces.faultyVehicleType, {
        data: params
      });

      console.log(res);
    },
    getAddressInfo(data) {
      this.address = data.result.address;
      console.log(this.address, "location");
    }
  },
  onLoad() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.rescue {
  // width: 100%;
  // height: 100vh;
  position: relative;
  padding-bottom: 180rpx; // 为底部按钮和表单提交按钮预留空间

  .info-title {
    font-weight: 600;
    height: 68rpx;
    font-size: 32rpx;
    color: #f82a3c;
    background: rgba(255, 46, 65, 0.1);
    padding: 12rpx 14rpx;
    display: flex;
    align-items: center;

    .warn-img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }

  .location-info {
    position: absolute;
    top: 70rpx;
    left: 10rpx;
    padding: 0 20rpx;
    border-radius: 4rpx;
    background: #fff;
    z-index: 10;
  }

  // 修改救援引导按钮位置
  .rescue-img {
    position: absolute;
    top: 560rpx;
    right: 5rpx;
    width: 110rpx;
    height: 110rpx;
    z-index: 10;
  }

  // 添加复位定位按钮样式
  .reset-location {
    position: absolute;
    top: 657rpx;
    right: 7rpx;
    width: 100rpx;
    height: 100rpx;
    z-index: 10;
  }

  .controls-wrap {
    position: absolute;
    right: 22rpx;
    top: 120rpx;
    z-index: 10;
  }

  .rescue-info {
    width: 100%;
    height: 90rpx;
    background: #cee0ff;
    display: flex;

    .rescue-phone {
      flex: 1;
      padding-left: 30rpx;
      padding-top: 26rpx;
      font-weight: 400;
      font-size: 36rpx;
      color: #3d3d3d;
      display: flex;

      .phone-number {
        margin-left: 22rpx;
      }
    }

    .rescue-btn {
      width: 194rpx;
      height: 90rpx;
      box-sizing: border-box;
      background: #4f90ff;
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;
      font-family: PingFang SC, PingFang SC;
      text-align: center;
      line-height: 90rpx;
      border-radius: 45rpx 0 0 45rpx;
    }
  }

        // 底部Tab样式
   .bottom-tabs {
     width: 100%;
     height: 180rpx;
     padding: 18rpx 0;
     display: flex;
     justify-content: space-around;
     position: fixed;
     bottom: 0;
     background: #fff;
     z-index: 100;

     .tab-btn {
       width: 320rpx;
       height: 88rpx;
       border-radius: 10rpx;
       display: flex;
       align-items: center;
       justify-content: center;
       font-size: 32rpx;
       font-weight: 600;
       border: 2rpx solid transparent;

       .tab-icon {
         width: 32rpx;
         height: 32rpx;
         margin-right: 16rpx;
       }

       &.rescue-tab {
         background: #FF9533;
         color: #ffffff;
         border-color: #FF9533;
         
         &.active {
           background: #FF9533;
           border-color: #FF9533;
         }
       }

       &.order-tab {
         background: #0066E9;
         color: #ffffff;
         border-color: #0066E9;
         
         &.active {
           background: #3A7AE6;
           border-color: #3A7AE6;
         }
       }
     }
   }

   .search-btn {
    display: flex;
    align-items: center;
    padding: 24rpx 54rpx;
  }

  .weui-btn {
    flex: 1;
    margin-top: 0;
    // margin-right: 20rpx;
    background-color: #0066e9;
    &.isGray{
      background-color: #959595;
    }
  }

  .agreement {
    text-align: center;
    padding-bottom: 50rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;

    .txt {
      color: rgba(79, 144, 255, 1);
    }
  }
}

.message-content-wrapper {
  .title {
    text-align: center;
    font-weight: 700;
    font-size: 34rpx;
    padding: 25rpx 50rpx;
    color: rgba(0, 0, 0, 0.9);
  }

  .msg_desc {
    padding: 0 30rpx;
    color: rgba(0, 0, 0, 0.5);
    font-size: 30rpx;
    text-align: left;
  }
}

/deep/ .neil-modal__footer-left {
  border-radius: 12rpx;
  background-color: #ffffff; border-width: 1rpx;
  border-style: solid;
  border-color: #0066e9;
  color: #0066e9 !important;
  height: 70rpx;
  line-height: 70rpx;
}

/deep/ .neil-modal__footer-right {
  border-radius: 12rpx;
  color: #ffffff;
  background-color: #0066e9;
  height: 70rpx;
  line-height: 70rpx;
}
</style>