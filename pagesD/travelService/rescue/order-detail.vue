<template>
  <view class="order-detail">
    <view class="info-title" v-if="shouldShowMap">
      <image class="warn-img" src="~@/pagesD/static/map/warnImg.png"></image>
      请确保人员已撤离安全地带。如情况紧急，请直接拨打电话<text>0771-96333</text>
    </view>
    <feePoup ref="feePoup" />
    <guidePoup ref="guidePoup" />

    <view class="evaluation-container" v-if="shouldShowEvaluation">
      <view v-if="evaluationLoading" class="evaluation-loading">
        <text>正在加载评价数据...</text>
      </view>

      <evaluation v-else businessType="rescue" :initialStatus="evaluationStatus" :initialRate="evaluationRate"
        :initialText="evaluationText" :initialTime="evaluationTime" :initialImgList="evaluationImages"
        @status-change="updateEvaluationStatus" @rate-change="updateEvaluationRate" @submit="handleEvaluationSubmit" />
    </view>

    <template v-if="shouldShowMap">
      <MapWrapCom :scale="scale" :mapLocation="mapLocation" class="MapWrapCom" ref="mapRef" height="780rpx"
        :markers="markers" :includePoints="includePoints">
        <template #callout>
          <cover-view v-if="shouldShowCallout" class="callout" :marker-id="rescueCarInfo.id">
            <cover-view class="callout-t">
              救援车 {{ rescueCarInfo.emergencyVehicleNo }}
            </cover-view>
            <cover-view class="callout-c">
              预计
              <cover-view class="tip">
                {{ rescueCarInfo.distance }}公里 {{ rescueCarInfo.etaMinute }}分钟
              </cover-view>
            </cover-view>
            <cover-view class="callout-b">已从 {{ rescueCarInfo.placeRegist }} 出发</cover-view>
            <cover-image class="callout-icon"
              src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/rescueCar.png"></cover-image>
          </cover-view>
        </template>
      </MapWrapCom>
      <!-- 救援引导 -->
      <image class="order-detail-img" @click="showGuide"
        src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/guide.png"></image>
      <!-- 地图工具 -->
      <mapTool @toolTap="toolTap" class="controls-wrap" />
      <!-- 直接呼叫 -->
      <view class="queue-up" v-if="detailInfo.rescueDetailVo.lineUp > 0">
        <view class="queue-num">前面排队数 <text>{{ detailInfo.rescueDetailVo.lineUp }}</text></view>
        <view class="queue-tip">正在排队中，请耐心等候...</view>
      </view>
    </template>

    <view :class="{ 'order-content-with-evaluation': shouldShowEvaluation }">
      <rescueInfo :detailInfo="detailInfo" :rescueCarInfo="rescueCarInfo" />
      <progress :detailInfo="detailInfo" :orderId="options.id" />
      <orderInfo :detailInfo="detailInfo" />
    </view>

  </view>
</template>

<script>
import MapWrapCom from "@/pagesD/components/map/map.vue";
import mapTool from "./components/map-tool.vue";
import feePoup from "./components/fee-poup.vue";
import rescueInfo from "./components/rescue-info.vue";
import progress from "./components/progress-wrap.vue";
import orderInfo from "./components/order-info.vue";
import guidePoup from './components/guide-poup.vue'
import evaluation from '@/pagesD/components/evaluation/index.vue';
import { getOpenid, getOpenidForRead, getLoginUserInfo } from '@/common/storageUtil.js';

let markersTemp = {
  zIndex: "1",
  rotate: 0,
  width: 60,
  height: 60,
  anchor: {
    x: 0.5,
    y: 1
  },
  customCallout: {
    display: "ALWAYS",
    anchorY: 0, // Y轴偏移量
    anchorX: 35 // X轴偏移量
  }
};
export default {
  components: {
    MapWrapCom,
    mapTool,
    feePoup,
    rescueInfo,
    progress,
    orderInfo,
    guidePoup,
    evaluation
  },
  data() {
    return {
      scale: 14,
      mapLocation: {},
      options: {},
      markers: [],
      modal: {
        show: false,
        close: false,
        showCancel: true,
        align: "left",
        confirmText: "确认"
      },
      detailInfo: {},
      rescueCarInfo: {},
      orderProgress: [],
      includePoints: [],
      showEvaluationFromRoute: false,
      evaluationStatus: 'unevaluated',
      evaluationRate: 0,
      evaluationText: '',
      evaluationTime: '',
      evaluationData: null,
      evaluationLoading: false,
      hasEvaluationData: false,
      evaluationImages: [] // 存储评价图片URL数组
    };
  },
  computed: {
    shouldShowCallout() {
      return Boolean(this.rescueCarInfo.id);
    },
    shouldShowEvaluation() {
      // 路由参数强制显示评价组件
      if (this.showEvaluationFromRoute) {
        return true;
      }

      // 基于评价数据判断：有评价数据或订单已完成且允许评价
      const orderStatus = this.detailInfo?.orderDetailVo?.orderStatus;
      const isOrderCompleted = orderStatus == 3 || orderStatus == 4;

      // 只有订单完成时才可能显示评价组件
      return isOrderCompleted;
    },
    shouldShowMap() {
      return !this.shouldShowEvaluation;
    }
  },
  methods: {
    toolTap(item) {
      console.log(item);
      if (item.type == "fee") {
        this.$refs.feePoup.openPoup();
      } else if (item.type == "phone") {
        this.callPhone();
      }
    },
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: "0771-96333" //仅为示例
      });
    },
    showGuide() {
      this.$refs.guidePoup.openPoup();
    },
    goAgreement() {
      var callcenter = "https://portal.gxetc.com.cn/agreement/user.png";
      // var callcenter = 'http://localhost:3000'
      uni.navigateTo({
        url:
          "/pages/uni-webview/h5-webview?ownPath=" +
          encodeURIComponent(callcenter)
      });
    },
    onSearchHandle() {
      this.modal.show = true;
    },
    onConfirmHandle() {
      this.modal.show = false;
    },
    close() {
      this.modal.show = false;
    },
    // 查询详情
    async initDetail(id) {
      let params = {
        id
      };
      uni.showLoading({
        title: `正在加载数据...`
      });
      let { data } = await this.$request.post(this.$interfaces.getDetailByOrderId, {
        data: params
      });
      uni.hideLoading();

      this.detailInfo = data.data
      // + 初始化评价状态
      this.initEvaluationStatus();


      let { orderDetailVo } = this.detailInfo;
      console.log(this.detailInfo, "this.detailInfo");

      let markers = {
        ...markersTemp,
        id: Number(this.options.id),
        latitude: orderDetailVo.lat,
        longitude: orderDetailVo.lon,
        title: "事发位置",
        type: "rescueCar",
        iconPath:
          "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/event_light.png"
      };
      this.markers = [markers]; // 先清空markers，设置事发位置

      // 等待事发位置标记渲染完成
      await new Promise(resolve => setTimeout(resolve, 300));

      // 获取救援车信息
      await this.getRescueCarInfo(id);
    },
    async getRescueCarInfo(id) {
      let { data } = await this.$request.post(
        this.$interfaces.getRescueCarMsg,
        {
          data: { id }
        }
      );

      let rescueCarInfo = data.data;
      console.log(rescueCarInfo, "rescueCarInfo");

      const mapRef = this.$refs.mapRef;

      if (rescueCarInfo.id) {
        // 有救援车
        let rescueCarMarker = {
          ...markersTemp,
          width: 35,
          height: 35,
          id: Number(rescueCarInfo.id),
          latitude: rescueCarInfo.emergencyVehicleLat,
          longitude: rescueCarInfo.emergencyVehicleLon,
          title: "救援车",
          type: "rescueCar",
          iconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/start-point.png",
        };

        this.markers.push(rescueCarMarker);

        // 等待救援车标记渲染完成
        await new Promise(resolve => setTimeout(resolve, 300));

        if (mapRef) {
          // 先更新地图中心到救援车位置
          this.mapLocation = {
            latitude: Number(rescueCarInfo.emergencyVehicleLat),
            longitude: Number(rescueCarInfo.emergencyVehicleLon)
          };

          // 等待地图中心更新完成
          await new Promise(resolve => setTimeout(resolve, 300));

          // 设置缩放级别以显示整个区域
          this.scale = 11;
        }

        this.rescueCarInfo = rescueCarInfo;
      } else {
        // 没有救援车，聚焦到事发位置
        if (this.markers.length > 0 && mapRef) {
          const accidentPoint = this.markers[0];

          // 更新地图中心到事发位置
          this.mapLocation = {
            latitude: Number(accidentPoint.latitude),
            longitude: Number(accidentPoint.longitude)
          };

          // 等待地图中心更新完成
          await new Promise(resolve => setTimeout(resolve, 300));

          // 设置合适的缩放级别
          this.scale = 13;
        }
      }
    },
    updateEvaluationStatus(status) {
      this.evaluationStatus = status;
    },
    updateEvaluationRate(rate) {
      this.evaluationRate = rate;
    },
    // + 解析图片字符串为URL数组
    parseImageString(imageStr) {
      if (!imageStr || typeof imageStr !== 'string') {
        return [];
      }

      // 按逗号分割并过滤空字符串
      return imageStr.split(',')
        .map(url => url.trim())
        .filter(url => url.length > 0);
    },
    // + 查询救援评价数据
    async getRescueEvaluation(orderNo) {
      if (!orderNo) return;

      try {
        this.evaluationLoading = true;
        const params = {
          orderNo: orderNo
        };

        const response = await this.$request.post(this.$interfaces.getRescueEvaluationList, {
          data: params
        });
        console.log(response, "response");
        if (response.code === 200 && response.data) {
          const evaluationList = response.data;
          if (evaluationList.length > 0) {
            // 取最新的评价记录
            const latestEvaluation = evaluationList[0];
            this.evaluationData = latestEvaluation;
            this.hasEvaluationData = true;

            // 设置评价组件的显示数据
            this.evaluationStatus = 'evaluated';
            this.evaluationRate = latestEvaluation.star || 0;
            this.evaluationText = latestEvaluation.content || '';
            this.evaluationTime = latestEvaluation.reviewsTime || '';
            // + 解析并设置图片数据
            this.evaluationImages = this.parseImageString(latestEvaluation.image || '');
          } else {
            // 没有评价数据
            this.hasEvaluationData = false;
            this.evaluationStatus = 'unevaluated';
            this.evaluationRate = 0;
            this.evaluationText = '';
            this.evaluationTime = '';
            this.evaluationImages = [];
          }
        } else {
          uni.showToast({
            title: response.msg,
            icon: 'none' 
          });
          // 接口返回失败或无数据
          this.hasEvaluationData = false;
          this.evaluationStatus = 'unevaluated';
          this.evaluationImages = [];
        }
      } catch (error) {
        this.hasEvaluationData = false;
        this.evaluationStatus = 'unevaluated';
        this.evaluationImages = [];
      } finally {
        this.evaluationLoading = false;
      }
    },
    initEvaluationStatus() {
      // + 不再基于订单状态初始化，改为基于接口数据
      // 初始化评价状态，具体数据通过getRescueEvaluation方法获取
      this.evaluationStatus = 'unevaluated';
      this.evaluationRate = 0;
      this.evaluationText = '';
      this.evaluationTime = '';
      this.hasEvaluationData = false;
      this.evaluationImages = [];
      this.$nextTick(() => {
        if (this.shouldShowEvaluation) {
          this.getRescueEvaluation(this.options.id)
        }
      })
    },
    // + 处理评价提交
    async handleEvaluationSubmit(evaluationData) {
      try {
        uni.showLoading({
          title: '提交评价中...'
        });

        const orderNo = this.options.id;
        if (!orderNo) {
          uni.showToast({
            title: '订单号获取失败',
            icon: 'error'
          });
          return;
        }

        // 获取用户openid
        const openid = getOpenid() || getOpenidForRead() || '';
        if (!openid) {
          uni.showToast({
            title: '用户信息获取失败，请重新登录',
            icon: 'error'
          });
          return;
        }

        // 构造完整的API参数（根据救援评价API文档）
        const params = {
          openid: openid,
          content: evaluationData.text || '',
          star: evaluationData.rate || 0,
          image: evaluationData.urlList ? evaluationData.urlList.join(',') : '',
          orderNo: orderNo,
          // 车辆信息
          vehiclePlate: this.detailInfo?.orderDetailVo?.customCarNo || '',
          vehicleType: this.detailInfo?.orderDetailVo?.carType || '',
          // 分公司ID
          rescueDeptId: this.detailInfo?.orderDetailVo?.branchCompanyId || '',
          netUserId: getLoginUserInfo().userIdStr,
          // 金额信息（单位：分）
          amount: this.detailInfo?.rescueDetailVo?.estimatedCost ? Math.round(this.detailInfo.rescueDetailVo.estimatedCost * 100) : 0
        };

        console.log('提交救援评价参数:', params);

        const response = await this.$request.post(this.$interfaces.addRescueEvaluation, {
          data: params
        });

        if (response.code === 200) {
          // 提交成功 - 通知评价组件完成评价
          const evaluationComponent = this.$refs.evaluation || this.$children.find(child => child.$options.name === 'Evaluation');
          if (evaluationComponent) {
            evaluationComponent.completeEvaluation();
          }

          uni.showToast({
            title: '评价提交成功',
            icon: 'success'
          });

          // 重新查询评价数据以更新状态
          await this.getRescueEvaluation(orderNo);
        } else {
          uni.showModal({
            title: '评价提交失败',
            content: response.msg,
            showCancel: false
          })
          // API返回失败 - 重置评价组件提交状态
          const evaluationComponent = this.$refs.evaluation || this.$children.find(child => child.$options.name === 'Evaluation');
          if (evaluationComponent) {
            evaluationComponent.resetSubmitStatus();
          }


        }
        uni.hideLoading();
      } catch (error) {
        console.error('提交评价失败:', error);

        // API调用失败 - 重置评价组件提交状态
        const evaluationComponent = this.$refs.evaluation || this.$children.find(child => child.$options.name === 'Evaluation');
        if (evaluationComponent) {
          evaluationComponent.resetSubmitStatus();
        }

        uni.showModal({
          title: '评价提交失败',
          content: response.msg,
          showCancel: false
        })
        uni.hideLoading();
      } finally {
        uni.hideLoading();
      }
    }
  },
  onLoad(options) {
    let { id, showEvaluation } = options;
    this.options = options;
    // + 处理路由参数，设置评价显示标识
    if (showEvaluation == 'true' || showEvaluation === true) {
      this.showEvaluationFromRoute = true;
    }

    this.initDetail(id);
    this.getRescueCarInfo(id);
  }
};
</script>

<style lang="scss" scoped>
.order-detail {
  position: relative;

  .info-title {
    font-weight: 400;
    height: 52rpx;
    font-size: 22rpx;
    background: rgba(255, 46, 65, 0.1);
    padding: 12rpx 10rpx;
    display: flex;
    align-items: center;

    .warn-img {
      width: 28rpx;
      height: 28rpx;
      margin-right: 4rpx;
    }

    text {
      color: #f82a3c;
    }
  }

  .order-detail-img {
    position: absolute;
    top: 102rpx;
    left: 30rpx;
    width: 110rpx;
    height: 110rpx;
    z-index: 10;
  }

  .controls-wrap {
    position: absolute;
    right: 22rpx;
    top: 105rpx;
    z-index: 10;
  }

  .queue-up {
    width: 100%;
    height: 114rpx;
    position: absolute;
    top: 725rpx;
    border-radius: 28rpx 28rpx 0 0;
    background: linear-gradient(180deg,
        #ffffff 0%,
        rgba(255, 255, 255, 0.69) 100%);
    text-align: center;
    padding-top: 16rpx;

    .queue-num {
      font-size: 28rpx;
      color: #333333;

      // margin-bottom: 6rpx;
      text {
        font-weight: 700;
        font-size: 40rpx;
        color: #ff8d12;
        margin: 0 8rpx;
      }
    }

    .queue-tip {
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
    }
  }

  .search-btn {
    display: flex;
    align-items: center;
    padding: 24rpx 54rpx;
    margin-top: 70rpx;
  }

  .weui-btn {
    flex: 1;
    margin-top: 0;
    // margin-right: 20rpx;
    background-color: #0066e9;
  }

  .agreement {
    text-align: center;
    padding-bottom: 50rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;

    .txt {
      color: rgba(79, 144, 255, 1);
    }
  }
}

.callout {
  position: absolute;
  z-index: 999;
  pointer-events: auto;
  transform: translateX(-50%);
  /* 水平居中 */
  bottom: 10rpx;
  /* 调整垂直位置 */

  /* 其他样式保持不变 */
  width: 284rpx;
  background: #e9effe;
  box-sizing: border-box;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(92, 133, 205, 0.42);
  border-radius: 8rpx;
  border: 1rpx solid #fff;
  color: #3d3d3d;
  font-size: 20rpx;
  font-weight: 400;
  padding: 10rpx 10rpx 10rpx 36rpx;

  &-t {
    margin-bottom: 6rpx;
  }

  &-c {
    margin-bottom: 6rpx;
    display: flex;
    align-items: center;

    .tip {
      width: 100%;
      margin-left: 6rpx;
      color: #ff8d12;
      font-weight: 600;
    }
  }

  &-b {
    width: 238rpx;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
  }

  &-icon {
    position: absolute;
    top: 12rpx;
    left: 10rpx;
    width: 18rpx;
    height: 18rpx;
  }
}

// + 评价组件容器样式
.evaluation-container {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 8rpx;
}

// + 当显示评价组件时，订单内容组件的样式兼容
.order-content-with-evaluation {
  margin: 0 20rpx;
}

// + 评价加载状态样式
.evaluation-loading {
  padding: 40rpx;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}
</style>