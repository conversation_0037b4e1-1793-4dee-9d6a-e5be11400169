<template>
  <view class="reviews-container">
    <view class="header">
      <view class="title">{{ serviceName }} - 用户评价</view>
      <view class="summary">
        <view class="rating">
          <text class="score">{{ avgRating || "暂无" }}</text>
          <view class="stars" v-if="avgRating">
            <view 
              class="star" 
              v-for="i in 5" 
              :key="i" 
              :class="i <= Math.round(avgRating) ? 'active' : ''"
            ></view>
          </view>
        </view>
        <view class="total">{{ totalReviews }}条评价</view>
      </view>
    </view>
    
    <view class="rating-tags" v-if="totalReviews > 0">
      <view 
        class="tag" 
        v-for="(tag, index) in ratingTags" 
        :key="index"
        :class="{ active: tag.active }"
        @click="toggleTag(index)"
      >
        {{ tag.name }}({{ tag.count }})
      </view>
    </view>
    
    <view class="reviews-list" v-if="reviews.length > 0">
      <view class="review-item" v-for="(item, index) in reviews" :key="index">
        <view class="user-info">
          <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
          <view class="user-details">
            <view class="username">{{ item.username }}</view>
            <view class="rating-info">
              <view class="rating-stars">
                <view 
                  class="star" 
                  v-for="i in 5" 
                  :key="i" 
                  :class="i <= item.rating ? 'active' : ''"
                ></view>
              </view>
              <view class="date">{{ item.date }}</view>
            </view>
          </view>
        </view>
        <view class="review-content">{{ item.content }}</view>
        <view class="image-list" v-if="item.images && item.images.length > 0">
          <image 
            v-for="(img, imgIndex) in item.images" 
            :key="imgIndex" 
            :src="img" 
            mode="aspectFill"
            class="review-image"
            @click="previewImage(item.images, imgIndex)"
          ></image>
        </view>
        <view class="service-reply" v-if="item.reply">
          <view class="reply-title">商家回复：</view>
          <view class="reply-content">{{ item.reply }}</view>
        </view>
      </view>
    </view>
    
    <view class="empty-reviews" v-if="reviews.length === 0">
      <image class="empty-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/empty-reviews.png"></image>
      <view class="empty-text">暂无评价</view>
    </view>
    
    <view class="load-more" v-if="hasMore && reviews.length > 0" @click="loadMore">
      <text v-if="!loading">加载更多</text>
      <view class="loading" v-else>加载中...</view>
    </view>
    
    <view class="write-review-btn" @click="openReviewPanel">写评价</view>
    
    <!-- 评价面板 -->
    <view class="review-panel" :class="{ 'show-panel': showReviewPanel }">
      <view class="panel-header">
        <view class="close-btn" @click="closeReviewPanel">取消</view>
        <view class="panel-title">发表评价</view>
        <view class="submit-btn" @click="submitReview" :class="{ disabled: !canSubmit }">发布</view>
      </view>
      
      <view class="rating-select">
        <view class="rating-title">服务区评分</view>
        <view class="rating-stars">
          <view 
            class="star-large" 
            v-for="i in 5" 
            :key="i" 
            :class="i <= userRating ? 'active' : ''"
            @click="setRating(i)"
          ></view>
        </view>
        <view class="rating-text">{{ getRatingText() }}</view>
      </view>
      
      <view class="review-textarea-wrap">
        <textarea 
          class="review-textarea" 
          placeholder="请分享您对该服务区的体验，至少5个字" 
          maxlength="200"
          v-model="reviewContent"
        ></textarea>
        <view class="word-count">{{ reviewContent.length }}/200</view>
      </view>
      
      <view class="upload-images">
        <view class="upload-title">上传照片</view>
        <view class="image-upload-list">
          <view 
            class="upload-item" 
            v-for="(img, index) in uploadImages" 
            :key="index"
          >
            <image :src="img" mode="aspectFill" class="preview-img"></image>
            <view class="delete-btn" @click="deleteImage(index)">×</view>
          </view>
          <view class="add-image-btn" @click="chooseImage" v-if="uploadImages.length < 9">
            <view class="add-icon">+</view>
            <view class="add-text">添加图片</view>
          </view>
        </view>
        <view class="upload-tip">最多上传9张图片</view>
      </view>
    </view>
    
    <!-- 蒙层 -->
    <view class="mask" v-if="showReviewPanel" @click="closeReviewPanel"></view>
  </view>
</template>

<script>
import { getLoginUserInfo } from '@/common/storageUtil.js';

export default {
  data() {
    return {
      serviceId: '',
      serviceName: '',
      avgRating: 0,
      totalReviews: 0,
      reviews: [],
      page: 1,
      limit: 10,
      hasMore: true,
      loading: false,
      showReviewPanel: false,
      userRating: 0,
      reviewContent: '',
      uploadImages: [],
      ratingTags: [
        { name: '全部', count: 0, active: true },
        { name: '五星', count: 0, active: false },
        { name: '四星', count: 0, active: false },
        { name: '三星', count: 0, active: false },
        { name: '二星', count: 0, active: false },
        { name: '一星', count: 0, active: false },
        { name: '有图', count: 0, active: false }
      ]
    };
  },
  computed: {
    canSubmit() {
      return this.userRating > 0 && this.reviewContent.length >= 5;
    }
  },
  onLoad(options) {
    this.serviceId = options.id || '';
    this.serviceName = options.name || '服务区';
    this.fetchReviews();
    this.fetchReviewSummary();
  },
  methods: {
    fetchReviewSummary() {
      // 这里应调用实际API
      // 模拟数据
      setTimeout(() => {
        this.avgRating = 4.5;
        this.totalReviews = 35;
        this.ratingTags[0].count = 35;
        this.ratingTags[1].count = 20;
        this.ratingTags[2].count = 10;
        this.ratingTags[3].count = 3;
        this.ratingTags[4].count = 1;
        this.ratingTags[5].count = 1;
        this.ratingTags[6].count = 15;
      }, 500);
    },
    fetchReviews() {
      if (this.loading || !this.hasMore) return;
      
      this.loading = true;
      
      // 这里应调用实际API
      // 模拟数据
      setTimeout(() => {
        const mockData = [
          {
            id: '1',
            username: '用户12345',
            avatar: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/default-avatar.png',
            rating: 5,
            content: '服务区环境很好，设施齐全，洗手间干净，餐饮品种丰富。',
            date: '2023-05-15',
            images: [
              'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/review-img1.jpg',
              'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/review-img2.jpg'
            ],
            reply: '感谢您的评价，我们会继续努力提供更好的服务！'
          },
          {
            id: '2',
            username: '用户67890',
            avatar: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/default-avatar.png',
            rating: 4,
            content: '加油站服务很快，价格合理，休息区也很舒适。',
            date: '2023-05-10',
            images: [],
            reply: ''
          }
        ];
        
        this.reviews = this.page === 1 ? mockData : [...this.reviews, ...mockData];
        
        this.hasMore = this.page < 3; // 假设只有3页数据
        this.loading = false;
        this.page++;
      }, 1000);
    },
    loadMore() {
      this.fetchReviews();
    },
    toggleTag(index) {
      this.ratingTags.forEach((tag, i) => {
        tag.active = i === index;
      });
      
      // 重置并按标签过滤评价
      this.page = 1;
      this.reviews = [];
      this.hasMore = true;
      this.fetchReviews();
    },
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      });
    },
    openReviewPanel() {
      // 检查用户是否已登录
      const userInfo = getLoginUserInfo();
      if (!userInfo || !userInfo.userIdStr) {
        uni.showToast({
          title: '请先登录后再评价',
          icon: 'none'
        });
        return;
      }
      
      this.showReviewPanel = true;
    },
    closeReviewPanel() {
      this.showReviewPanel = false;
    },
    setRating(rating) {
      this.userRating = rating;
    },
    getRatingText() {
      const texts = ['', '非常差', '差', '一般', '好', '非常好'];
      return texts[this.userRating] || '';
    },
    chooseImage() {
      uni.chooseImage({
        count: 9 - this.uploadImages.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadImages = [...this.uploadImages, ...res.tempFilePaths];
        }
      });
    },
    deleteImage(index) {
      this.uploadImages.splice(index, 1);
    },
    submitReview() {
      if (!this.canSubmit) return;
      
      // 显示上传中
      uni.showLoading({
        title: '提交中...'
      });
      
      // 这里应调用实际API
      // 模拟提交
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '评价提交成功',
          icon: 'success'
        });
        
        // 重置评价表单
        this.showReviewPanel = false;
        this.userRating = 0;
        this.reviewContent = '';
        this.uploadImages = [];
        
        // 刷新评价列表
        this.page = 1;
        this.reviews = [];
        this.fetchReviews();
        this.fetchReviewSummary();
      }, 1500);
    }
  }
};
</script>

<style lang="scss" scoped>
.reviews-container {
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background-color: #ffffff;
  padding: 30rpx 20rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .rating {
      display: flex;
      align-items: center;
      
      .score {
        font-size: 48rpx;
        font-weight: bold;
        color: #FF9204;
        margin-right: 16rpx;
      }
      
      .stars {
        display: flex;
        
        .star {
          width: 32rpx;
          height: 32rpx;
          margin-right: 10rpx;
          background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/star_grey.png');
          background-size: cover;
          
          &.active {
            background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/star.png');
          }
        }
      }
    }
    
    .total {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.rating-tags {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  margin-top: 20rpx;
  overflow-x: auto;
  white-space: nowrap;
  
  .tag {
    display: inline-block;
    padding: 10rpx 24rpx;
    font-size: 24rpx;
    color: #666;
    background-color: #f5f5f5;
    border-radius: 30rpx;
    margin-right: 16rpx;
    
    &.active {
      color: #ffffff;
      background-color: #0066e9;
    }
  }
}

.reviews-list {
  margin-top: 20rpx;
  
  .review-item {
    background-color: #ffffff;
    padding: 30rpx 20rpx;
    margin-bottom: 20rpx;
    
    .user-info {
      display: flex;
      margin-bottom: 20rpx;
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }
      
      .user-details {
        flex: 1;
        
        .username {
          font-size: 30rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 10rpx;
        }
        
        .rating-info {
          display: flex;
          align-items: center;
          
          .rating-stars {
            display: flex;
            margin-right: 20rpx;
            
            .star {
              width: 24rpx;
              height: 24rpx;
              margin-right: 6rpx;
              background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/star_grey.png');
              background-size: cover;
              
              &.active {
                background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/star.png');
              }
            }
          }
          
          .date {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
    }
    
    .review-content {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
      margin-bottom: 20rpx;
    }
    
    .image-list {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20rpx;
      
      .review-image {
        width: 220rpx;
        height: 220rpx;
        margin-right: 10rpx;
        margin-bottom: 10rpx;
        border-radius: 8rpx;
      }
    }
    
    .service-reply {
      background-color: #f8f8f8;
      padding: 20rpx;
      border-radius: 8rpx;
      
      .reply-title {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
        margin-bottom: 10rpx;
      }
      
      .reply-content {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }
}

.empty-reviews {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #ffffff;
  margin-top: 20rpx;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  
  .loading {
    color: #999;
  }
}

.write-review-btn {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 300rpx;
  height: 80rpx;
  background-color: #0066e9;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 12rpx rgba(0, 102, 233, 0.2);
}

.review-panel {
  position: fixed;
  bottom: -100%;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  z-index: 1000;
  transition: bottom 0.3s;
  max-height: 90vh;
  overflow-y: auto;
  
  &.show-panel {
    bottom: 0;
  }
  
  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 20rpx;
    border-bottom: 1rpx solid #f1f1f1;
    
    .close-btn, .submit-btn {
      font-size: 32rpx;
      color: #0066e9;
    }
    
    .panel-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
    }
    
    .submit-btn.disabled {
      color: #ccc;
    }
  }
  
  .rating-select {
    padding: 30rpx 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .rating-title {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .rating-stars {
      display: flex;
      margin-bottom: 20rpx;
      
      .star-large {
        width: 60rpx;
        height: 60rpx;
        margin: 0 10rpx;
        background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/star_grey.png');
        background-size: cover;
        
        &.active {
          background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/star.png');
        }
      }
    }
    
    .rating-text {
      font-size: 28rpx;
      color: #FF9204;
    }
  }
  
  .review-textarea-wrap {
    padding: 20rpx;
    position: relative;
    
    .review-textarea {
      width: 100%;
      height: 240rpx;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .word-count {
      position: absolute;
      bottom: 30rpx;
      right: 40rpx;
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .upload-images {
    padding: 0 20rpx 30rpx;
    
    .upload-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .image-upload-list {
      display: flex;
      flex-wrap: wrap;
      
      .upload-item {
        width: 160rpx;
        height: 160rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        position: relative;
        
        .preview-img {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
        }
        
        .delete-btn {
          position: absolute;
          top: -20rpx;
          right: -20rpx;
          width: 40rpx;
          height: 40rpx;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
          color: #fff;
          font-size: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      
      .add-image-btn {
        width: 160rpx;
        height: 160rpx;
        border: 1rpx dashed #ddd;
        border-radius: 8rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        
        .add-icon {
          font-size: 60rpx;
          color: #ccc;
          margin-bottom: 10rpx;
          line-height: 1;
        }
        
        .add-text {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .upload-tip {
      font-size: 24rpx;
      color: #999;
      margin-top: 10rpx;
    }
  }
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
</style> 