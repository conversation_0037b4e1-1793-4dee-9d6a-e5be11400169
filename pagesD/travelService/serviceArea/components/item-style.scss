.item-title {
  margin-bottom: 12rpx;
  display: flex;
  justify-content: space-between;
  .item-name {
    display: flex;
    align-items: center;
    image {
      width: 44rpx;
      height: 44rpx;
      margin-right: 12rpx;
    }
    text {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
    }
  }
  .item-status {
    width: 92rpx;
    height: 44rpx;
    background: #4f90ff;
    border-radius: 4rpx 4rpx 4rpx 4rpx;
    color: #fff;
    line-height: 44rpx;
    text-align: center;
    font-size: 24rpx;
    
    &.normal {
      background: #09BB07;
    }
    
    &.closed {
      background: #F76260;
    }
    
    &.default {
      background: #4f90ff;
    }
  }
}
.tips {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}
.item-info {
  width: 100%;
  background: #f5f9ff;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  padding: 30rpx 20rpx;
}