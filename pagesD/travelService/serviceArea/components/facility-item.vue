<template>
  <view class="parking-item">
    <view class="item-title">
      <view class="item-name">
        <image
          class="phone-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/toilet_icon_highlight.png"
          mode=""
        ></image
        ><text>基础设施</text></view
      >
      <view class="item-status">正常</view>
    </view>
    <view class="tips">卫生间</view>
    <view class="item-info">
      <view class="txt-img">
        <image
          class="phone-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/mancs.png"
          mode=""
        ></image
        ><text>男卫生间</text></view
      >
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <text>蹲便器:</text>{{info.maleStool || info.easyToilet || 0}} 个</view
        >
        <view class="item"> <text>小便器:</text>{{info.maleUrinal || 0}}个</view>
      </view>

      <view class="txt-img">
        <image
          class="phone-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/womancs.png"
          mode=""
        ></image
        ><text>女卫生间</text></view
      >
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <text>蹲便器:</text>{{info.femaleSquatting || 0}}个</view
        >
      </view>
      <view class="line"></view>
      <view class="item-info-line" style="margin-bottom: 10rpx;">
        <view class="txt-img">
          <image
            class="phone-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/lunyi.png"
            mode=""
          ></image
          ><text>无障碍卫生间</text></view
        >
        <view class="txt-img">
          <image
            class="phone-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/washroom.png"
            mode=""
          ></image
          ><text>第三卫生间</text></view
        >
      </view>
      <view class="item-info-line">
        <view class="item"><text>{{info.accessibleBathroom || 0}}个</text></view>
        <view class="item"><text>{{info.thirdToilet || 0}}个</text></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
    props:{
    info:{
      type:Object,
      default:()=> {}
    }
  },
  methods: {
    getStatusClass(status) {
      if (status === 0) return 'closed';
      return 'normal';
    },
    getStatusText(status) {
      if (status === 0) return '关闭';
      return '正常';
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./item-style.scss";
.item-info {
  height: 404rpx;
}
.txt-img {
  flex: 1;
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
  text {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
  }
}
.item-info-line {
  display: flex;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
  .item-title {
    margin-bottom: 20rpx;
  }
  .item {
    flex: 1;
    text {
      color: #666;
      font-weight: 400;
      margin: 0 30rpx 0 40rpx;
    }
  }
}
.line {
  width: 678rpx;
  height: 2rpx;
  border: 2rpx dashed #d4dce8;
  margin-bottom: 24rpx;
}
</style>