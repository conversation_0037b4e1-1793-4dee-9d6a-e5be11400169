<template>
  <view class="">
    <view class="ad-swiper-wrap">
      <swiper
        @change="change"
        :interval="interval"
        circular
        :duration="duration"
        :autoplay="autoplay"
        style="height: 300rpx;background-color: rgb(243, 244, 246);"
      >
        <swiper-item
          class="swiper-item"
          v-for="(item, index) in list"
          :key="index"
        >
          <view
            class="list-image-wrap"
            :class="[uCurrent != index ? 'list-scale' : '']"
          >
            <image class="swiper-image" :src="item.adPic" mode=""> </image>
          </view>
        </swiper-item>
      </swiper>
      <!-- <view
        class="swiper-indicator"
        style="top: auto;bottom: 4px;justify-content: center;padding: 0px 8px;"
      >
        <view
          class="indicator-item-round"
          :class="{ 'indicator-item-round-active': index == uCurrent }"
          v-for="(item, index) in list"
          :key="index"
        ></view>
      </view> -->
    </view>
  </view>
</template>

<script>

export default {
  props: {
    type: {
      type: String,
      default: "1"
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      disableTouch: true,
      autoplay: true,
      interval: 5000,
      duration: 500, // 滚动一个周期的时间长，单位ms
      dislogMsg: "", //弹窗提示内容
      uCurrent: 0,
      bannerList: [], // 广告图列表
      exposureList: [], // 曝光数据列表
      isLoading: false
    };
  },
  components: {},
  created() {
    // this.getAdvBannerList();
  },
  methods: {
    stopTouchMove() {
      return false;
    },
    // 获取广告图列表
    getAdvBannerList() {
      let param = {
        displayPosition: this.type
      };
      this.$request
        .post(this.$interfaces.advBannerList, {
          data: param
        })
        .then(res => {
          console.log("bannerList=======>>>", res);
          if (res.code == 200 && res.data) {
            this.bannerList = res.data;
          }
        })
        .catch(err => {});
    },

    change(e) {
      let current = e.detail.current;
      this.uCurrent = current;
      if (e.detail.source == "autoplay") {
        let row = this.bannerList[current] || {};
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ad-swiper-wrap {
  position: relative;
  overflow: hidden;
  transform: translateY(0);

  .swiper-image {
    width: 100%;
    will-change: transform;
    height: 100%;
    /* #ifndef APP-NVUE */
    display: block;
    /* #endif */
    /* #ifdef H5 */
    pointer-events: none;
    /* #endif */
  }

  .swiper-item {
    display: flex;
    overflow: hidden;
    align-items: center;
  }

  .list-image-wrap {
    width: 100%;
    height: 100%;
    flex: 1;
    transition: all 0.5s;
    overflow: hidden;
    box-sizing: content-box;
    position: relative;
  }

  .swiper-indicator {
    padding: 0 8px;
    position: absolute;
    display: flex;
    flex-direction: row;
    width: 100%;
    z-index: 1;
  }

  .indicator-item-round {
    width: 14rpx;
    height: 14rpx;
    margin: 0 6rpx;
    border-radius: 20rpx;
    transition: all 0.5s;
    background-color: rgba(0, 0, 0, 0.3);
  }

  .indicator-item-round-active {
    width: 34rpx;
    background-color: rgba(255, 255, 255, 0.8);
  }
}

.message-content-wrapper {
  .title {
    text-align: center;
    font-weight: 700;
    font-size: 34rpx;
    padding: 25rpx 50rpx;
    color: rgba(0, 0, 0, 0.9);
  }

  .msg_desc {
    padding: 0 30rpx;
    color: rgba(0, 0, 0, 0.5);
    font-size: 30rpx;
    text-align: left;

    .msg_desc-content {
      text-indent: 2em;
    }
  }
}
</style>