<template>
  <view class="area-detail">
    <view id="tabs">
      <view id="scrollTop">
        <view class="banner">
          <Banner :list="bannerList" />
        </view>
        <view class="area-info">
          <view class="info-top">
            <view class="info-top-title">
              {{ detailInfo.name }}
              <!-- <view 
                class="status-indicator" 
                :class="detailInfo.operationStatus === 0 ? 'closed' : 'normal'"
              >
                {{ detailInfo.operationStatus === 0 ? '暂停营业' : '正常营业' }}
              </view> -->
            </view>
            <view class="info-top-distance"
              >距离{{ options.remark | kmFilter }} km
              <image
                @click="call(detailInfo.supportHotline)"
                class="phone-icon"
                src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/call_icon.png"
                mode=""
              ></image
            ></view>
          </view>
          <view class="area-info-line"
            >{{ detailInfo.highwayGuid || '-' }}
            <text>{{ detailInfo.level || '-' }}</text></view
          >
          <view class="area-info-line"
            >占地面积 <text>{{ detailInfo.area || 0}} ㎡</text></view
          >
          <!-- <view class="area-info-line" >广西壮族自治区南宁市青秀区XXXX </view> -->
          <!-- <view
            v-html="detailInfo.content"
            class="area-info-line"
            style="margin-bottom:0;"
          ></view> -->
        </view>
        
        <!-- 评价入口组件 -->
        <view class="evaluation-section" v-if="detailInfo.guid">
          <evaluation-entry
            businessType="serviceArea"
            :businessId="detailInfo.guid"
            :evaluationCount="evaluationCount"
            :businessName="detailInfo.name"
          />
        </view>
      </view>
      <view class="scroll-content">
        <view
          class="scroll-tab"
          :class="[barshow ? 'scroll-tab-fixed' : 'scroll-tab-static']"
          :style="{ top: barshow ? fixedTop + 'px' : '0' }"
        >
          <view
            class="scroll-tab-list"
            v-for="(item, index) in tabList"
            :style="{
              color: activeTab == index ? '#0066E9' : '#666666',
              fontWeight: activeTab == index ? '500' : '400'
            }"
            @click="changeTab(index)"
            :id="'tabs' + index"
            :key="index"
          >
            <text class="scroll-tab-list-text">{{ item.text }}</text>
          </view>
          <view class="scroll-tab-bar" :style="[tabBarStyle]"></view>
        </view>
      </view>
      <view class="scroll-warp">
        <view id="wrap0" class="wrap wrap0 scroll-warp-list">
          <parkingItem :info="detailInfo" />
        </view>
        <view id="wrap1" class="wrap wrap1 scroll-warp-list">
          <facilityItem :info="detailInfo" />
        </view>
        <view id="wrap2" class="wrap wrap2 scroll-warp-list">
          <refuelItem :info="detailInfo" />
        </view>
        <view id="wrap3" class="wrap wrap3 scroll-warp-list">
          <cateringItem :info="detailInfo" />
        </view>

        <!-- <view
          class="scroll-warp-list"
          :id="'wrap' + index"
          v-for="(item, index) in tabList"
          :key="index"
        >
          <view v-for="(items, index) in 60" :key="index">{{ item.text }}</view>
        </view> -->
      </view>
    </view>
    <view class="detail-bottom">
      <button class="btn light" @click.stop="star('star')">
        <image
          v-if="!detailInfo.starFlag"
          class="star-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/nostar.png"
          mode=""
        ></image>
        <image
          v-else
          class="star-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/star.png"
          mode=""
        >
        </image
        >{{ detailInfo.starFlag ? "取消收藏" : "收藏" }}
      </button>
      <button class="btn pri" open-type="share">
        <image
          class="phone-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/share.png"
          mode=""
        ></image
        >分享
      </button>
    </view>
  </view>
</template>

<script>
import Banner from "./components/arera-swiper.vue";
import parkingItem from "./components/parking-item.vue";
import facilityItem from "./components/facility-item.vue";
import refuelItem from "./components/refuel-item.vue";
import cateringItem from "./components/catering-item.vue";
import EvaluationEntry from "@/components/common/evaluation-entry.vue";
	import {
		getLoginUserInfo,
		getOpenid,
		getOpenidForRead,
	} from '@/common/storageUtil.js'
  
export default {
  name: "area-detail",
  components: {
    Banner,
    parkingItem,
    facilityItem,
    refuelItem,
    cateringItem,
    EvaluationEntry
  },
  data() {
    return {
      detailInfo: {
        operationStatus: 1,
        avgRating: 0,
        ratingCount: 0,
        serviceStatus: {
          parking: 1,
          facility: 1,
          refuel: 1,
          catering: 1
        }
      },
      bannerList: [],
      barshow: false,
      tabTop: 0,
      activeTab: 0,
      scrollInto: 0,
      tabBarStyle: {},
      warpTop: [],
      scrollWarp: [],
      viewId: "",
      tabList: [
        {
          text: "停车场",
          navTarget: "第一列"
        },
        {
          text: "基础设施",
          navTarget: "第二列"
        },
        {
          text: "加油/充电",
          navTarget: "第三列"
        },
        {
          text: "餐饮",
          navTarget: "第四列"
        }
      ],
      options: {},
      fixedTop: 0,
      activeColor: "linear-gradient( 90deg, #0066E9 0%, #7DB6FF 100%)",
      tabChangeScroll: false,
      show: true,
      evaluationCount: 0
    };
  },
  computed: {},
  onLoad(options) {
    let { id, name, img } = options;
    this.options = options;
    // console.log(img, 2222);
    this.bannerList.push({ adPic: img });
    this.getDetail(id);
  },
  mounted() {
    this.$nextTick(async function() {
      let rect = await this.GetRect("#scrollTop");
      let rect0 = await this.GetRect("#wrap0");
      let rect1 = await this.GetRect("#wrap1");

      // console.log(rect, rect0, rect1, "#scrollTop");
      this.tabTop = rect.height;
      this._getTabRect(0);
      this.barInit();
    });
  },
  watch: {},
  onPageScroll: function(e) {
    //nvue暂不支持滚动监听，可用bindingx代替
    // console.log("滚动距离为：" + e.scrollTop);
    this.tabScoll(e.scrollTop);
  },
  filters: {
    kmFilter(val) {
      return parseFloat(val).toFixed(1);
    }
  },
  methods: {
    //获取节点信息
    GetRect(selector) {
      return new Promise((resolve, reject) => {
        let view = uni.createSelectorQuery().in(this);
        view
          .select(selector)
          .boundingClientRect(rect => {
            resolve(rect);
          })
          .exec();
      });
    },
    //tab切换
    changeTab(e) {
      this.activeTab = e;
      this._getTabRect(this.activeTab);
      if (e == 0) {
        this.scrollInto = this.warpTop[e] - 48;
      } else {
        this.scrollInto = this.warpTop[e] - 88;
      }
      this.viewId = `#wrap${e}`;
      uni.pageScrollTo({
        scrollTop: this.scrollInto,
        duration: 300
      });
      this.tabChangeScroll = true;
      setTimeout(() => {
        this.tabChangeScroll = false;
      }, 500);
      // console.log(this.viewId);
    },
    //获取tab宽度
    async _getTabRect(itemIndex) {
      let rect = await this.GetRect("#tabs" + itemIndex);
      let rect1 = await this.GetRect(
        "#tabs" + itemIndex + ">.scroll-tab-list-text"
      );
      let width = rect1.width * 0.67;
      this.tabBarStyle = {
        left: rect.left + (rect.width - width) / 2 + "px",
        width: width + "px",
        background: this.activeColor
      };
    },
    //scroll滚动
    async tabScoll(scrollTop) {
      let rect = await this.GetRect("#scrollTop");
      if (this.tabTop == 0) {
        let rect = await this.GetRect("#scrollTop");
        this.tabTop = rect.height;
      }
      // console.log(scrollTop, this.warpTop, "warpTop");
      this.barshow = scrollTop >= this.tabTop ? true : false;
      let scrollTop1 = scrollTop;
      if (this.tabChangeScroll) return;
      if (scrollTop1 <= this.warpTop[1] - 93) {
        this.activeTab = 0;
        this._getTabRect(0);
      }
      if (
        scrollTop1 > this.warpTop[1] - 93 &&
        scrollTop1 < this.warpTop[2] - 93
      ) {
        this.activeTab = 1;
        this._getTabRect(1);
      }
      if (
        scrollTop1 > this.warpTop[2] - 93 &&
        scrollTop1 < this.warpTop[3] - 93
      ) {
        this.activeTab = 2;
        this._getTabRect(2);
      }
      if (scrollTop1 > this.warpTop[3] - 93) {
        this.activeTab = 3;
        this._getTabRect(3);
      }
    },
    //获取节点距离顶部距离
    barInit: async function(index) {
      let navTargetTop = [];
      let navTargetTop1 = [];
      let randoms = 93; //顶部栏高度+tab高度+兼容值
      for (let i = 0; i < this.tabList.length; i++) {
        this.GetRect("#wrap" + i).then(res => {
          // console.log(res);
          navTargetTop.push(parseInt(res.top));
          navTargetTop1.push(parseInt(res.top) + randoms);
        });
      }
      // console.log(navTargetTop, navTargetTop1, "navTargetTop");
      this.warpTop = navTargetTop;
      this.scrollWarp = navTargetTop1;
    },
    async getDetail(id) {
      try {
        let params = {
          id
        };
        let res = await this.$request.post(this.$interfaces.serviceAreaDetail, {
          data: params
        });
        if (res.code == 200) {
          this.detailInfo = res.data;
          if (this.detailInfo.operationStatus === undefined) {
            this.$set(this.detailInfo, 'operationStatus', 1);
          }
          if (this.detailInfo.avgRating === undefined) {
            this.$set(this.detailInfo, 'avgRating', 0);
          }
          if (this.detailInfo.ratingCount === undefined) {
            this.$set(this.detailInfo, 'ratingCount', 0);
          }
          if (!this.detailInfo.serviceStatus) {
            this.$set(this.detailInfo, 'serviceStatus', {
              parking: 1,
              facility: 1,
              refuel: 1,
              catering: 1
            });
          }
          this.getCollectList();
          this.getEvaluationCount();
        } else {
          uni.showToast({
            title: res.msg || '获取详情失败，请稍后重试',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取服务区详情失败:', error);
        uni.showToast({
          title: '获取详情失败，请稍后重试',
          icon: 'none'
        });
      }
    },
    call(phoneNumber) {
      uni.makePhoneCall({
        phoneNumber: phoneNumber //仅为示例
      });
    },
    getCollectList() {
      if(!getLoginUserInfo().userIdStr) return
      //收藏服务区
      let params = {
        netUserId: getLoginUserInfo().userIdStr,
        collectType: 1
      };
      this.$request
        .post(this.$interfaces.getCollectList, {
          data: params
        })
        .then(res => {
          // console.log("收藏查询=====>>>>>", res);
          if (res.code == 200) {
            let starList = res.data;
            if (starList.length > 0) {
              starList.forEach(item => {
                if (this.detailInfo.name == item.collectTitle) {
                  this.$set(this.detailInfo, "starFlag", true);
                  this.$set(this.detailInfo, "starId", item.id);
                }
              });
            }
          }
        })
        .catch(error => {});
    },
    setStarStatus(type, starId) {
      this.$set(this.detailInfo, "starFlag", type == "star" ? true : false);
      this.$set(this.detailInfo, "starId", starId);
      // this.$set(this.netList[index], 'starFlag', false)
    },
    star() {
      let type = this.detailInfo.starFlag ? "nostar" : "star";
      if (this.showStarFlag) return;
      this.showStarFlag = true;
      //收藏服务区
      let params = {
        netUserId: getLoginUserInfo().userIdStr,
        collectTitle: this.detailInfo.name,
        // id: this.detailInfo.id,
        collectType: 1
      };
      let url = "";
      if (type == "star") {
        url = this.$interfaces.addCollect;
      } else {
        url = this.$interfaces.delCollect;
        params.id = this.detailInfo.starId;
      }
      this.$request
        .post(url, {
          data: params
        })
        .then(res => {
          this.showStarFlag = false;
          // console.log("收藏结果=====>>>>>", res);
          if (res.code == 200) {
            uni.showToast({
              icon: "none",
              title:
                type == "star" ? "收藏成功，可在我的收藏中查看" : "已取消收藏",
              // title: '已取消收藏',
              duration: 2000
            });
            this.setStarStatus(type, res.data.id);
          } else {
            uni.showToast({
              icon: "none",
              title: res.msg,
              // title: '已取消收藏',
              duration: 2000
            });
          }
        })
        .catch(error => {
          this.showStarFlag = false;
          uni.showToast({
            icon: "none",
            title: error.msg,
            // title: '已取消收藏',
            duration: 2000
          });
        });
    },
    getEvaluationCount() {
      // 获取服务区评价数量
      if (!this.detailInfo.id) return;
      
      try {
        let params = {
          facilityId: this.detailInfo.guid,
          openid: this.getUserOpenid()
        };
        
        // 调用获取服务区评价列表接口，通过data数组长度获取评价数量
        this.$request.post(this.$interfaces.getServiceAreaEvaluationList, {
          data: params
        }).then(res => {
          if (res.code == 200) {
            this.evaluationCount = res.data.total;
          } else {
            this.evaluationCount = 0;
          }
        }).catch(error => {
          this.evaluationCount = 0;
        });
      } catch (error) {
        this.evaluationCount = 0;
      }
    },
    // 获取用户openid
    getUserOpenid() {
      // 尝试多种方式获取openid
      let openid = getOpenid();
      if (!openid) {
        openid = getOpenidForRead();
      }

      // 如果还是没有，尝试从用户信息中获取
      if (!openid) {
        const userInfo = getLoginUserInfo();
        openid = userInfo?.openid || '';
      }

      return openid;
    },
  },
  onShareAppMessage() {
    return {
      title: this.detailInfo.name,
      path: `/pagesD/travelService/serviceArea/detail?id=${this.options.id}&remark=${this.options.remark}&img=${this.options.img}`,
      imageUrl: this.options.img
    };
  }
};
</script>

<style lang="scss" scoped>
.area-detail {
  padding-bottom: 216rpx;
  position: relative;
  .banner {
    height: 300rpx;
  }
  .detail-bottom {
    width: 100%;
    height: 180rpx;
    padding: 18rpx 0;
    display: flex;
    justify-content: space-around;
    position: fixed;
    bottom: 0;
    background: #fff;
    .btn {
      width: 320rpx;
      height: 88rpx;
      border-radius: 10rpx 10rpx 10rpx 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid #0066e9;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
      &.light {
        background: #ffffff;
        color: #0066e9;
      }
      &.pri {
        background: #0066e9;
        color: #fff;
      }
    }
  }
}
.flexRowCc {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.scroll-content {
  position: relative;
  .scroll-tab {
    @extend .flexRowCc;
    justify-content: space-between;
    width: 100%;
    height: 44px;
    box-sizing: border-box;
    border-top: 1px solid #f1f1f1;
    border-bottom: 1px solid #f1f1f1;
    background: #f0f0f0;
    position: relative;
    z-index: 999;
    &-static {
      position: relative !important;
    }
    &-fixed {
      position: fixed;
      top: 44px;
      left: 0;
    }
    &-list {
      text-align: center;
      font-size: 32rpx;
      flex: 1 1 auto;
      &-text {
        font-size: 28rpx;
        display: inline-block;
      }
    }
    &-bar {
      width: 64rpx;
      height: 4rpx;
      background: linear-gradient(270deg, #0066e9 0%, #7db6ff 100%);
      position: absolute;
      bottom: 8rpx;
      border-radius: 16rpx;
      transition-duration: 0.5s;
    }
  }
}
.scroll-warp {
  .scroll-warp-list {
    background: #ffffff;
  }
  .wrap {
    width: 100%;
    padding: 28rpx 20rpx;
    background: #fff;
    margin-bottom: 20rpx;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .wrap0 {
    height: 400rpx;
  }
  .wrap1 {
    height: 576rpx;
  }
  .wrap2 {
    height: 284rpx;
  }
  .wrap3 {
    height: 420rpx;
  }
}
.area-info {
  // height: 284rpx;
  width: 100%;
  background: #ffffff;
  box-sizing: border-box;
  padding: 40rpx 40rpx 0 40rpx;
  // padding: 30rpx 20rpx 0 20rpx;
  color: #333;
  font-size: 28rpx;
  .info-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 28rpx;

    &-title {
      font-weight: 600;
      font-size: 36rpx;
      color: #4f90ff;
      display: flex;
      align-items: center;
      
      .status-indicator {
        display: inline-block;
        font-size: 24rpx;
        font-weight: normal;
        padding: 2rpx 14rpx;
        border-radius: 6rpx;
        color: #ffffff;
        margin-left: 16rpx;
        
        &.normal {
          background-color: #09BB07;
        }
        
        &.closed {
          background-color: #F76260;
        }
      }
    }
    &-distance {
      display: flex;
      align-items: center;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-left: 16rpx;
      }
    }
  }
  
  &-line {
    margin-bottom: 16rpx;
    &:last-child {
      margin-bottom: 0;
    }
    &:nth-child(2) {
      margin-bottom: 8rpx;
    }
  }
  text {
    margin-left: 26rpx;
  }
}

</style>
