/**
 * 地图相关常量配置
 */

// 标记模板配置
export const MARKER_TEMPLATE = {
  zIndex: "1",
  rotate: 0,
  width: 30,
  height: 30,
  anchor: {
    x: 0.5,
    y: 1
  }
};

// 图标路径配置
export const ICON_PATHS = {
  event: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/event_light.png",
  jam: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/jam_light.png",
  work: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/sg_light.png",
  highService: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/service_light.png",
  tollStation: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/station_light.png",
  startPoint: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/start-point.png",
  endPoint: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/end-point.png"
};

// 货车配置表 - 国家标准JT/T 489-2019
export const TRUCK_CONFIGS = {
  '一类车': { 
    size: 1, 
    length: 5.8, 
    height: 2.5, 
    width: 2.0, 
    weight: 4.0, 
    axle_count: 2, 
    plateType: '蓝牌', 
    desc: '车长<6米，总质量<4.5吨，2轴' 
  },
  '二类车': { 
    size: 2, 
    length: 6.8, 
    height: 3.0, 
    width: 2.3, 
    weight: 8.0, 
    axle_count: 2, 
    plateType: '黄牌', 
    desc: '车长≥6米或总质量≥4.5吨，2轴' 
  },
  '三类车': { 
    size: 3, 
    length: 9.6, 
    height: 3.5, 
    width: 2.5, 
    weight: 20.0, 
    axle_count: 3, 
    plateType: '黄牌', 
    desc: '车长≤12米，总质量≤25吨，3轴' 
  },
  '四类车': { 
    size: 3, 
    length: 12.0, 
    height: 3.8, 
    width: 2.5, 
    weight: 30.0, 
    axle_count: 4, 
    plateType: '黄牌', 
    desc: '车长≤12米，总质量≤31吨，4轴' 
  },
  '五类车': { 
    size: 4, 
    length: 15.0, 
    height: 4.0, 
    width: 2.6, 
    weight: 40.0, 
    axle_count: 5, 
    plateType: '黄牌', 
    desc: '车长≤15米，总质量≤43吨，5轴' 
  },
  '六类车': { 
    size: 4, 
    length: 17.5, 
    height: 4.0, 
    width: 2.6, 
    weight: 49.0, 
    axle_count: 6, 
    plateType: '黄牌', 
    desc: '车长≤18米，总质量≤49吨，6轴' 
  }
};

// 默认坐标配置（南宁）
export const DEFAULT_LOCATION = {
  latitude: 22.77849,
  longitude: 108.40826
};

// 标记ID偏移量配置
export const ID_OFFSETS = {
  highService: 100000,
  tollStation: 200000,
  startPoint: 9999,
  endPoint: 19999
};

// 默认请求参数
export const DEFAULT_REQUEST_PARAMS = {
  pageNo: 1,
  pageSize: 9999
};

// 路线颜色配置
export const ROUTE_COLORS = {
  primary: "#58c16c",
  secondary: "#a3ddc7",
  truck: "#3366FF",
  truckSecondary: "#a3ddc7",
  selected: "#4F90FF",
  highlight: "#FDD925",
  border: "#2f693c",
  truckBorder: "#2952CC",
  secondaryBorder: "#88b4a3"
};

// 标记大小配置
export const MARKER_SIZES = {
  default: { width: 30, height: 30 },
  enlarged: { width: 60, height: 60 },
  rescueCar: { width: 35, height: 35 }
}; 