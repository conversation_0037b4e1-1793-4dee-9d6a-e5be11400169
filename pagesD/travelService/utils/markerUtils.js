/**
 * 标记工具类
 * 处理地图标记相关的操作
 */

import { MARKER_SIZES, ID_OFFSETS } from './mapConstants.js';

export default class MarkerUtils {
  // 图片前缀常量
  static PIC_PREFIX = 'https://portal.gxetc.com.cn/service-area-pic';
  static DEFAULT_IMG_PREFIX = 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ';

  /**
   * 生成随机图片索引（1到7之间的随机数）
   * @returns {Number} 随机图片索引
   */
  static generateRandomImageIndex() {
    return Math.floor(Math.random() * 7) + 1;
  }
  /**
   * 管理标记显示/隐藏
   * @param {Array} markers 当前显示的标记数组
   * @param {Array} dataMarkers 所有标记数据
   * @param {String} type 标记类型
   * @param {Boolean} show 是否显示
   * @returns {Array} 更新后的标记数组
   */
  static toggleMarkers(markers, dataMarkers, type, show) {
    if (show) {
      const newMarkers = dataMarkers.filter(item => item.type === type);
      return [...markers, ...newMarkers];
    } else {
      return markers.filter(item => item.type !== type);
    }
  }

  /**
   * 处理标记点击事件
   * @param {Array} markers 标记数组
   * @param {Number} markerId 被点击的标记ID
   * @param {Function} clickHandler 点击处理函数
   * @returns {Array} 更新后的标记数组
   */
  static handleMarkerClick(markers, markerId, clickHandler) {
    let clickedMarker = null;
    
    const updatedMarkers = markers.map(item => {
      if (item.id === markerId) {
        // 放大当前标记
        item.width = MARKER_SIZES.enlarged.width;
        item.height = MARKER_SIZES.enlarged.height;
        clickedMarker = item;
      } else {
        // 重置其他标记大小
        item.width = MARKER_SIZES.default.width;
        item.height = MARKER_SIZES.default.height;
      }
      return item;
    });

    // 执行点击处理逻辑
    if (clickedMarker && clickHandler) {
      clickHandler(clickedMarker);
    }

    return updatedMarkers;
  }

  /**
   * 跳转到服务区详情页
   * @param {Object} marker 标记对象
   */
  static navigateToServiceDetail(marker) {
    const realId = marker.originalId || marker.id;
    const name = marker.title || marker.name || '服务区';
    
    // 仿照 serivceItem.vue 的图片处理逻辑
    let img = '';
    // if (marker.mainPic && marker.mainPic.length > 0) {
    //   // 如果有主图片，使用主图片（处理反斜杠）
    //   img = this.PIC_PREFIX + marker.mainPic.replace(/\\/g, '/');
    // } else {
      // 使用1到7的随机图片
      const imageIndex = this.generateRandomImageIndex();
      img = `${this.DEFAULT_IMG_PREFIX}${imageIndex}.png`;
    // }
    
    uni.navigateTo({
      url: `/pagesD/travelService/serviceArea/detail?id=${realId}&name=${encodeURIComponent(name)}&remark=${marker.remark || ''}&img=${img}`
    });
  }

  /**
   * 跳转到收费站详情页
   * @param {Object} marker 标记对象
   */
  static navigateToTollStationDetail(marker) {
    const realId = marker.originalId || marker.id;
    const name = marker.title || marker.name || '收费站';
    const img = marker.iconPath || '';
    
    uni.navigateTo({
      url: `/pagesD/travelService/tollStation/detail?id=${realId}&name=${encodeURIComponent(name)}&distance=${marker.distance || ''}&img=${encodeURIComponent(img)}`
    });
  }

  /**
   * 处理标记类型的点击逻辑
   * @param {Object} marker 标记对象
   * @returns {Object} 处理结果
   */
  static processMarkerClick(marker) {
    console.log('标记点击:', marker);

    // 服务区和收费站直接跳转到详情页面
    if (marker.type === 'highService') {
      this.navigateToServiceDetail(marker);
      return { action: 'navigate', target: 'serviceDetail' };
    } else if (marker.type === 'tollStation') {
      this.navigateToTollStationDetail(marker);
      return { action: 'navigate', target: 'tollStationDetail' };
    } else {
      // 其他类型显示信息弹窗
      return { 
        action: 'showInfo', 
        markerInfoType: marker.type, 
        infoData: marker 
      };
    }
  }

  /**
   * 过滤路线标记（起点终点）
   * @param {Array} markers 标记数组
   * @returns {Array} 过滤后的标记数组
   */
  static filterRouteMarkers(markers) {
    return markers.filter(
      item => item.id !== ID_OFFSETS.startPoint && item.id !== ID_OFFSETS.endPoint
    );
  }

  /**
   * 获取路线标记（起点终点）
   * @param {Array} markers 标记数组
   * @returns {Array} 路线标记数组
   */
  static getRouteMarkers(markers) {
    return markers.filter(
      item => item.id === ID_OFFSETS.startPoint || item.id === ID_OFFSETS.endPoint
    );
  }

  /**
   * 合并标记数组，避免重复
   * @param {Array} existingMarkers 现有标记数组
   * @param {Array} newMarkers 新标记数组
   * @returns {Array} 合并后的标记数组
   */
  static mergeMarkers(existingMarkers, newMarkers) {
    const existingIds = new Set(existingMarkers.map(marker => marker.id));
    const uniqueNewMarkers = newMarkers.filter(marker => !existingIds.has(marker.id));
    
    return [...existingMarkers, ...uniqueNewMarkers];
  }

  /**
   * 根据类型移除标记
   * @param {Array} markers 标记数组
   * @param {String} type 要移除的标记类型
   * @returns {Array} 过滤后的标记数组
   */
  static removeMarkersByType(markers, type) {
    return markers.filter(marker => marker.type !== type);
  }

  /**
   * 根据ID移除标记
   * @param {Array} markers 标记数组
   * @param {Array} idsToRemove 要移除的ID数组
   * @returns {Array} 过滤后的标记数组
   */
  static removeMarkersById(markers, idsToRemove) {
    const idsSet = new Set(idsToRemove);
    return markers.filter(marker => !idsSet.has(marker.id));
  }

  /**
   * 查找标记
   * @param {Array} markers 标记数组
   * @param {Function} predicate 查找条件函数
   * @returns {Object|null} 找到的标记或null
   */
  static findMarker(markers, predicate) {
    return markers.find(predicate) || null;
  }

  /**
   * 根据ID查找标记
   * @param {Array} markers 标记数组
   * @param {Number} id 标记ID
   * @returns {Object|null} 找到的标记或null
   */
  static findMarkerById(markers, id) {
    return this.findMarker(markers, marker => marker.id === id);
  }

  /**
   * 根据类型查找标记
   * @param {Array} markers 标记数组
   * @param {String} type 标记类型
   * @returns {Array} 符合条件的标记数组
   */
  static findMarkersByType(markers, type) {
    return markers.filter(marker => marker.type === type);
  }

  /**
   * 更新标记数据
   * @param {Array} markers 标记数组
   * @param {Number} id 标记ID
   * @param {Object} updates 更新的数据
   * @returns {Array} 更新后的标记数组
   */
  static updateMarker(markers, id, updates) {
    return markers.map(marker => {
      if (marker.id === id) {
        return { ...marker, ...updates };
      }
      return marker;
    });
  }
} 