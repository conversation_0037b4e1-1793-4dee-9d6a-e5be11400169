/**
 * 路线规划工具类
 * 处理路线规划相关的公共逻辑
 */

import { TRUCK_CONFIGS, ICON_PATHS, ID_OFFSETS, ROUTE_COLORS } from './mapConstants.js';

export default class RouteUtils {
  constructor(store) {
    this.mapKey = store.state.mapKey;
  }

  /**
   * 解压坐标数据
   * @param {Array} coors 压缩的坐标数组
   * @param {Number} kr 压缩比率
   * @returns {Array} 解压后的坐标点数组
   */
  decompressCoordinates(coors, kr = 1000000) {
    const points = [];
    
    // 坐标解压（返回的点串坐标，通过前向差分进行压缩）
    for (let i = 2; i < coors.length; i++) {
      coors[i] = Number(coors[i - 2]) + Number(coors[i]) / kr;
    }
    
    // 将解压后的坐标放入点串数组中
    for (let i = 0; i < coors.length; i += 2) {
      points.push({ 
        latitude: coors[i], 
        longitude: coors[i + 1] 
      });
    }
    
    return points;
  }

  /**
   * 格式化距离显示
   * @param {Number} distance 距离（米）
   * @returns {String} 格式化后的距离
   */
  formatDistance(distance) {
    return distance >= 1000000
      ? (distance / 1000).toFixed(0)
      : (distance / 1000).toFixed(1);
  }

  /**
   * 格式化时长显示
   * @param {Number} duration 时长（分钟）
   * @returns {String} 格式化后的时长
   */
  formatDuration(duration) {
    const hours = Math.floor(duration / 60);
    const remainingMinutes = duration % 60;
    return `${hours >= 1 ? hours + "小时" : ""}${remainingMinutes}分钟`;
  }

  /**
   * 创建起点终点标记
   * @param {Object} startPoint 起点坐标
   * @param {Object} endPoint 终点坐标
   * @returns {Array} 起点终点标记数组
   */
  createRouteMarkers(startPoint, endPoint) {
    const startMarker = {
      id: ID_OFFSETS.startPoint,
      latitude: startPoint.latitude,
      longitude: startPoint.longitude,
      iconPath: ICON_PATHS.startPoint,
      width: 30,
      height: 30
    };

    const endMarker = {
      id: ID_OFFSETS.endPoint,
      latitude: endPoint.latitude,
      longitude: endPoint.longitude,
      iconPath: ICON_PATHS.endPoint,
      width: 30,
      height: 30
    };

    return [startMarker, endMarker];
  }

  /**
   * 处理路线数据
   * @param {Array} routes 路线数组
   * @param {Boolean} isTruck 是否为货车路线
   * @param {String} truckType 货车类型
   * @returns {Array} 处理后的路线数组
   */
  processRoutes(routes, isTruck = false, truckType = null) {
    return routes.map((item, idx) => {
      const points = this.decompressCoordinates(item.polyline);
      
      const polyline = {
        points,
        color: this.getRouteColor(idx, isTruck),
        width: 6,
        arrowLine: true,
        borderColor: this.getRouteBorderColor(idx, isTruck),
        borderWidth: 1,
        level: "abovebuildings"
      };

      const distance = this.formatDistance(item.distance);
      const duration = this.formatDuration(item.duration);

      const routeInfo = {
        lineId: idx,
        polyline,
        distance,
        duration,
        tags: item.tags || [],
        toll: item.toll || 0
      };

      // 添加货车特有信息
      if (isTruck && truckType) {
        const truckConfig = TRUCK_CONFIGS[truckType] || TRUCK_CONFIGS['一类车'];
        const restrictions = item.restriction || [];
        
        routeInfo.isTruck = true;
        routeInfo.truckType = truckType;
        routeInfo.plateType = truckConfig.plateType;
        routeInfo.truckDesc = truckConfig.desc;
        routeInfo.restriction = restrictions.length > 0 
          ? restrictions.map(r => r.info).join('；') 
          : '';
      }

      return routeInfo;
    });
  }

  /**
   * 获取路线颜色
   * @param {Number} index 路线索引
   * @param {Boolean} isTruck 是否为货车路线
   * @returns {String} 颜色值
   */
  getRouteColor(index, isTruck) {
    if (isTruck) {
      return index > 0 ? ROUTE_COLORS.truckSecondary : ROUTE_COLORS.truck;
    }
    return index > 0 ? ROUTE_COLORS.secondary : ROUTE_COLORS.primary;
  }

  /**
   * 获取路线边框颜色
   * @param {Number} index 路线索引
   * @param {Boolean} isTruck 是否为货车路线
   * @returns {String} 边框颜色值
   */
  getRouteBorderColor(index, isTruck) {
    if (isTruck) {
      return index > 0 ? ROUTE_COLORS.secondaryBorder : ROUTE_COLORS.truckBorder;
    }
    return index > 0 ? ROUTE_COLORS.secondaryBorder : ROUTE_COLORS.border;
  }

  /**
   * 构建货车路线请求URL
   * @param {Object} startPoint 起点
   * @param {Object} endPoint 终点
   * @param {String} truckType 货车类型
   * @returns {String} 请求URL
   */
  buildTruckRouteUrl(startPoint, endPoint, truckType) {
    const startPointStr = `${startPoint.latitude},${startPoint.longitude}`;
    const endPointStr = `${endPoint.latitude},${endPoint.longitude}`;
    
    let truckingUrl = `https://apis.map.qq.com/ws/direction/v1/trucking?key=${this.mapKey}&from=${startPointStr}&to=${endPointStr}`;
    
    // 获取货车配置
    const truckConfig = TRUCK_CONFIGS[truckType] || TRUCK_CONFIGS['一类车'];
    
    // 添加货车参数到URL
    Object.entries(truckConfig).forEach(([key, value]) => {
      // 仅添加API需要的参数（排除plateType和desc）
      if (key !== 'plateType' && key !== 'desc') {
        truckingUrl += `&${key}=${value}`;
      }
    });
    
    return truckingUrl;
  }

  /**
   * 构建普通路线请求URL
   * @param {Object} startPoint 起点
   * @param {Object} endPoint 终点
   * @returns {String} 请求URL
   */
  buildDrivingRouteUrl(startPoint, endPoint) {
    const startPointStr = `${startPoint.latitude},${startPoint.longitude}`;
    const endPointStr = `${endPoint.latitude},${endPoint.longitude}`;
    
    return `https://apis.map.qq.com/ws/direction/v1/driving?key=${this.mapKey}&from=${startPointStr}&to=${endPointStr}&get_mp=1`;
  }

  /**
   * 路线规划请求
   * @param {Object} params 请求参数
   * @returns {Promise} 路线规划结果
   */
  async planRoute(params) {
    const { startPoint, endPoint, isTruck, truckType } = params;
    
    try {
      const url = isTruck 
        ? this.buildTruckRouteUrl(startPoint, endPoint, truckType)
        : this.buildDrivingRouteUrl(startPoint, endPoint);

      const response = await new Promise((resolve, reject) => {
        uni.request({
          url,
          success: resolve,
          fail: reject
        });
      });

      if (!response.data || !response.data.result) {
        throw new Error(isTruck ? '未找到合适的货车路线' : '路线规划失败');
      }

      const routes = response.data.result.routes;
      const lineArr = this.processRoutes(routes, isTruck, truckType);
      const routeMarkers = this.createRouteMarkers(startPoint, endPoint);

      return {
        success: true,
        lineArr,
        routeMarkers,
        polylines: lineArr.map(item => item.polyline)
      };

    } catch (error) {
      console.error('路线规划失败:', error);
      
      uni.showToast({
        title: error.message || '路线规划失败，请重试',
        icon: 'none'
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 高亮选中的路线 （旧版本优惠路线用的）
   * @param {Array} polylines 路线数组
   * @param {Number} selectedId 选中的路线ID
   * @returns {Array} 更新后的路线数组
   */
  highlightSelectedRoute(polylines, selectedId) {
    return polylines.map(polyline => {
      if (polyline.id === selectedId && polyline.type === "road") {
        polyline.color = ROUTE_COLORS.selected;
      } else if (polyline.type === "road") {
        polyline.color = polyline.lately ? ROUTE_COLORS.highlight : ROUTE_COLORS.primary;
      }
      return polyline;
    });
  }

  /**
   * 选择并高亮路线
   * @param {Array} lineArr 路线信息数组
   * @param {Number} selectedId 选中的路线ID
   * @returns {Array} 更新后的polyline数组
   */
  selectAndHighlightRoute(lineArr, selectedId) {
    const updatedLineArr = JSON.parse(JSON.stringify(lineArr));
    
    // 重新排序，将选中的路线放到最后（最上层显示）
    const selectedRoutes = updatedLineArr.filter(item => item.lineId === selectedId);
    const otherRoutes = updatedLineArr.filter(item => item.lineId !== selectedId);
    
    // 更新颜色
    selectedRoutes.forEach(item => {
      item.polyline.color = ROUTE_COLORS.primary;
      item.polyline.borderColor = ROUTE_COLORS.border;
    });
    
    otherRoutes.forEach(item => {
      item.polyline.color = ROUTE_COLORS.secondary;
      item.polyline.borderColor = ROUTE_COLORS.secondaryBorder;
    });
    
    // 返回重新排序的polyline数组
    return [...otherRoutes, ...selectedRoutes].map(item => item.polyline);
  }
} 