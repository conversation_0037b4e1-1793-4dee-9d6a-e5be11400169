/**
 * 地图数据服务类
 * 统一处理各类地图数据的获取和处理
 */

import { MARKER_TEMPLATE, ICON_PATHS, DEFAULT_REQUEST_PARAMS, ID_OFFSETS } from './mapConstants.js';

export default class MapDataService {
  constructor(request, interfaces) {
    this.request = request;
    this.interfaces = interfaces;
  }

  /**
   * 验证坐标是否有效
   * @param {Number} latitude 纬度
   * @param {Number} longitude 经度
   * @returns {Boolean} 坐标是否有效
   */
  isValidCoordinate(latitude, longitude) {
    const lat = Number(latitude);
    const lng = Number(longitude);
    
    // 检查是否为有效数字
    if (isNaN(lat) || isNaN(lng)) {
      return false;
    }
    
    // 检查纬度范围 [-90, 90]
    if (lat < -90 || lat > 90) {
      return false;
    }
    
    // 检查经度范围 [-180, 180]
    if (lng < -180 || lng > 180) {
      return false;
    }
    
    return true;
  }

  /**
   * 将字符串ID转换为数字ID
   * @param {String} stringId 字符串ID
   * @param {Number} offset 偏移量
   * @returns {Number} 数字ID
   */
  convertStringIdToNumber(stringId, offset = 0) {
    if (typeof stringId === 'number') {
      return stringId + offset;
    }
    
    // 将字符串转换为数字哈希值
    let hash = 0;
    for (let i = 0; i < stringId.length; i++) {
      const char = stringId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    // 确保为正数并加上偏移量
    return Math.abs(hash) + offset;
  }

  /**
   * 创建标记对象
   * @param {Object} item 原始数据
   * @param {String} type 标记类型
   * @param {Object} coordinateMapping 坐标字段映射
   * @param {String} titleField 标题字段名
   * @param {Number} idOffset ID偏移量
   * @returns {Object} 标记对象
   */
  createMarker(item, type, coordinateMapping, titleField, idOffset = 0) {
    const { latField, lngField } = coordinateMapping;
    const latitude = item[latField];
    const longitude = item[lngField];
    
    // 验证坐标有效性
    if (!this.isValidCoordinate(latitude, longitude)) {
      return null;
    }

    const marker = {
      ...MARKER_TEMPLATE,
      ...item,
      id: idOffset > 0 ? this.convertStringIdToNumber(item.id, idOffset) : item.id,
      latitude: Number(latitude),
      longitude: Number(longitude),
      title: item[titleField],
      type,
      iconPath: ICON_PATHS[type]
    };

    // 保存原始ID（用于字符串ID的情况）
    if (idOffset > 0) {
      marker.originalId = item.id;
    }

    return marker;
  }

  /**
   * 通用数据获取方法
   * @param {String} apiName API接口名称
   * @param {Object} location 位置信息
   * @param {Object} config 配置信息
   * @returns {Promise} 数据获取Promise
   */
  async fetchMapData(apiName, location, config) {
    const {
      type,
      coordinateMapping,
      titleField,
      idOffset,
      extraParams = {},
      shouldAddToMarkers = true
    } = config;

    const params = {
      ...DEFAULT_REQUEST_PARAMS,
      latitude: location.latitude,
      longitude: location.longitude,
      ...extraParams
    };

    try {
      const res = await this.request.post(this.interfaces[apiName], {
        data: params
      });

      let data = res.data;
      
      // 处理服务区和收费站的特殊响应格式
      if ((apiName === 'serviceAreaList' || apiName === 'tollStationQuery') && res.code === 200) {
        data = res.data;
      }

      // 过滤并处理有效的数据
      const validMarkers = [];
      let invalidCount = 0;

      data.forEach(item => {
        const marker = this.createMarker(item, type, coordinateMapping, titleField, idOffset);
        
        if (marker) {
          validMarkers.push(marker);
        } else {
          invalidCount++;
          console.warn(`${type}无效坐标数据:`, {
            id: item.id,
            name: item[titleField],
            latitude: item[coordinateMapping.latField],
            longitude: item[coordinateMapping.lngField]
          });
        }
      });

      if (invalidCount > 0) {
        console.log(`过滤掉 ${invalidCount} 个无效坐标的${type}数据`);
      }

      return {
        markers: validMarkers,
        shouldAddToMarkers
      };

    } catch (err) {
      console.error(`获取${type}信息失败:`, err);
      return {
        markers: [],
        shouldAddToMarkers
      };
    }
  }

  /**
   * 获取事件数据
   * @param {Object} location 位置信息
   * @returns {Promise}
   */
  getEvent(location) {
    return this.fetchMapData('getMapEvent', location, {
      type: 'event',
      coordinateMapping: { latField: 'lat', lngField: 'lon' },
      titleField: 'loadName'
    });
  }

  /**
   * 获取拥堵信息
   * @param {Object} location 位置信息
   * @returns {Promise}
   */
  getJamInfo(location) {
    return this.fetchMapData('getMapJam', location, {
      type: 'jam',
      coordinateMapping: { latField: 'lat', lngField: 'lng' },
      titleField: 'congestSourceArea'
    });
  }

  /**
   * 获取施工信息
   * @param {Object} location 位置信息
   * @returns {Promise}
   */
  getWork(location) {
    return this.fetchMapData('getMapWork', location, {
      type: 'work',
      coordinateMapping: { latField: 'startLatitude', lngField: 'startLongitude' },
      titleField: 'highspeedName'
    });
  }

  /**
   * 获取服务区信息
   * @param {Object} location 位置信息
   * @returns {Promise}
   */
  getHighService(location) {
    return this.fetchMapData('serviceAreaList', location, {
      type: 'highService',
      coordinateMapping: { latField: 'latitude', lngField: 'longitude' },
      titleField: 'name',
      idOffset: ID_OFFSETS.highService,
      extraParams: { name: '', cityCode: '' },
      shouldAddToMarkers: false // 服务区默认不显示在地图上
    });
  }

  /**
   * 获取收费站信息
   * @param {Object} location 位置信息
   * @returns {Promise}
   */
  getTollStation(location) {
    return this.fetchMapData('tollStationQuery', location, {
      type: 'tollStation',
      coordinateMapping: { latField: 'lat', lngField: 'lng' },
      titleField: 'name',
      idOffset: ID_OFFSETS.tollStation,
      extraParams: { name: '', shiCode: '' },
      shouldAddToMarkers: false // 收费站默认不显示在地图上
    });
  }

  /**
   * 批量获取所有地图数据
   * @param {Object} location 位置信息
   * @returns {Promise} 包含所有数据的Promise
   */
  async getAllMapData(location) {
    try {
      const results = await Promise.all([
        this.getEvent(location),
        this.getJamInfo(location),
        this.getWork(location),
        this.getHighService(location),
        this.getTollStation(location)
      ]);

      // 分离需要立即显示的标记和仅存储的标记
      const markersToShow = [];
      const markersToStore = [];

      results.forEach(result => {
        if (result.shouldAddToMarkers) {
          markersToShow.push(...result.markers);
        }
        markersToStore.push(...result.markers);
      });

      return {
        markersToShow,
        markersToStore
      };

    } catch (err) {
      console.error('批量获取地图数据失败:', err);
      return {
        markersToShow: [],
        markersToStore: []
      };
    }
  }
} 