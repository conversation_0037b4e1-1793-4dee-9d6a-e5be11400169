<template>
  <view class="city-item">
    <image class="city-image" :src="city.image" mode="aspectFill"></image>
    <view class="city-info">
      <view class="city-name">{{ city.name }}</view>
      <view class="city-action">
        <view class="explore-btn" @click="handleExploreClick">探索城市旅游</view>
      </view>
    </view>
    <view class="city-right">
      <view class="city-weather">
        <image class="weather-icon" :src="getWeatherIconUrl" mode="aspectFit" @error="onWeatherIconError"
          v-if="!weatherIconError"></image>
        <view class="weather-text-box">
          <text class="weather-text">{{ city.temperature }}</text>
          <text class="weather-text">{{ city.weather }}</text>
        </view>
      </view>
      <view class="city-distance">距您{{ city.distance }}</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    city: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 天气图标加载状态
      weatherIconError: false,
      // 天气分类映射，将复杂天气归类为主要类型
      weatherCategories: {
        'sunny': ['晴天', '晴'],
        'cloudy': ['多云', '阴', '云'],
        'rainy': ['阵雨', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨', '小到中雨', '中到大雨', '大到暴雨', '暴雨到大暴雨', '大暴雨到特大暴雨', '雨'],
        'thunderstorm': ['雷阵雨', '雷阵雨伴有冰雹', '雷'],
        'snowy': ['阵雪', '小雪', '中雪', '大雪', '暴雪', '雨夹雪', '小到中雪', '中到大雪', '大到暴雪', '雪'],
        'foggy': ['雾', '浓雾', '强浓雾', '特强浓雾', '大雾'],
        'hazy': ['霾', '中度霾', '重度霾', '严重霾', '强浓雾'],
        'dusty': ['沙尘暴', '强沙尘暴', '浮尘', '扬沙'],
        'freezing': ['冻雨'],
        'clear': ['无']
      }
    };
  },
  computed: {
    // 获取天气对应的图标URL
    getWeatherIconUrl() {
      const weatherType = this.getWeatherType();
      // 使用本地静态资源
      const iconBasePath = 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/';

      // 直接使用天气类型代码匹配本地png文件
      const iconFileName = weatherType === 'default' ? 'cloudy.png' : `${weatherType}.png`;

      return iconBasePath + iconFileName;
    }
  },
  methods: {
    // 处理探索城市旅游点击事件
    handleExploreClick() {
      this.$emit('exploreClick', this.city);
    },

    // 天气图标加载错误处理
    onWeatherIconError() {
      console.log('天气图标加载失败，使用默认图标');
      this.weatherIconError = true;
    },

    // 获取天气类型分类
    getWeatherType() {
      const weatherDesc = this.city.weather;
      if (!weatherDesc) {
        return 'default';
      }

      // 从天气描述中提取天气状态（去掉温度部分）
      const weatherText = weatherDesc.replace(/\d+~\d+°C\s*/, '').trim();

      // 遍历天气分类，找到匹配的类型
      for (const [category, keywords] of Object.entries(this.weatherCategories)) {
        if (keywords.some(keyword => weatherText.includes(keyword))) {
          return category;
        }
      }

      // 如果没有匹配到，返回默认类型
      return 'default';
    }
  }
};
</script>

<style lang="scss" scoped>
.city-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #E7E7E7;

  &:last-child {
    border-bottom: none;
  }

  .city-image {
    width: 142rpx;
    height: 140rpx;
    border-radius: 8rpx;
    margin-right: 20rpx;
  }

  .city-info {
    flex: 1;
    height: 135rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .city-name {
      padding-top: 10rpx;
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }

    .city-action {
      .explore-btn {
        background: #5591FF;
        border-radius: 8rpx;
        color: #ffffff;
        font-size: 24rpx;
        padding: 6rpx 14rpx;
        display: inline-block;
      }
    }
  }

  .city-right {
    height: 135rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;

    .city-weather {
      display: flex;
      align-items: center;
      border-radius: 8rpx;

      .weather-icon {
        width: 60rpx;
        height: 60rpx;
        margin-right: 10rpx;
      }

      .weather-text-box {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: space-between;

        .weather-text {
          font-size: 24rpx;
          color: #666666;
        }
      }

    }

    .city-distance {
      font-size: 24rpx;
      color: #999999;
      margin-bottom: 8rpx;
    }
  }
}
</style>
