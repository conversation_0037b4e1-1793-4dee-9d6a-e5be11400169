<template>
  <view class="info-wrap">
    <!-- 施工 work -->
    <view class="box-wrap" v-if="infoType == 'work'">
      <view class="item-wrap">
        <image
          @click="speek('涉路施工')"
          class="image"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_4.png"
        ></image>

        <view class="text text-info">
          <view class="warn-tip">涉路施工</view>
          <view class="green-tip">S22</view>
          <view class="right-block">
            <view class="black-tip">那安高速</view>
            <view class="time-tip">2024/5/6-2024/5/13</view>
          </view>
        </view>
      </view>
      <view class="item-wrap">
        <image
          class="image"
          @click="speek('方向：贵州往南宁')"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vlocation.png"
        ></image>
        <view class="text">福厚1号和服厚2号隧道之间棚洞应急处置</view>
      </view>
      <view class="item-wrap">
        <image
          class="image"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/direction.png"
        ></image>
        <view class="text">方向：贵州往南宁</view>
        <image
          class="image"
          style="margin-left: 20rpx;"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vbus.png"
        ></image>
        <view class="text">K3+300-K2+345</view>
      </view>
    </view>
    <!-- 路段 road -->
    <view class="box-wrap" v-if="infoType == 'road'">
      <view class="item-wrap">
        <image
          class="image"
          @click="speek('靖西至那坡高速公路')"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_2.png"
        ></image>
        <view class="text text-info">
          <view class="road-tip">优惠路段</view>
          <view class="black-tip">靖西至那坡高速公路</view>
        </view>
      </view>
      <view class="gray-wrap">
        <view class="li-wrap">
          <view class="li-label">实施时间</view>
          <view class="li-value">2023.9.6-2026.12.31</view>
        </view>
        <view class="li-wrap">
          <view class="li-label">实施内容</view>
          <view class="li-value" :class="isfoldText ? '' : 'textOpen'"
            >结合广西高速公路现行差异化收结合广西高速公路现行差异化收结合广西高速公路现行差异化收</view
          >
        </view>
        <view class="more" @click="foldText">
          <view class="text">{{ isfoldText ? "展开" : "收起" }}</view>
          <image
            class="image"
            :src="
              isfoldText
                ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'
            "
          ></image
        ></view>
      </view>
    </view>
    <!-- 事件 event -->
    <view class="box-wrap" v-if="infoType == 'event'">
      <view class="item-wrap">
        <image
          class="image"
          @click="speek('道路事件:S22那安高速,方向：贵州往南宁,桂柳二通道。2024年2月2日9时52分巡查视频发现:S22桂河高=良收费站之间，三睦隧道内)发生小车故障占用行发生小车故障占用行发生小车故障占用行发生小车故障占用行发生小车故障占用行')"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_1.png"
        ></image>

        <view class="text text-info">
          <view class="warn-tip event" style="margin-right:88rpx;"
            >道路事件</view
          >
          <view class="black-tip">S22那安高速</view>
          <view class="status-tip">处理中</view>
        </view>
      </view>
      <view class="item-wrap">
        <view class="text" style="margin-right:120rpx;">桂柳二通道</view>
        <view class="text">方向：贵州往南宁</view>
      </view>
      <view class="gray-wrap" style="margin-bottom:20rpx;">
        <view class="li-wrap">
          <view
            class="li-value li-value2 "
            :class="isfoldText ? 'over-2' : 'textOpen'"
            >2024年2月2日9时52分巡查视频发现:S22桂河高=良收费站之间，三睦隧道内)发生小车故障占用行发生小车故障占用行发生小车故障占用行发生小车故障占用行发生小车故障占用行</view
          >
        </view>
        <view class="more" @click="foldText">
          <view class="text">{{ isfoldText ? "展开" : "收起" }}</view>
          <image
            class="image"
            :src="
              isfoldText
                ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'
            "
          ></image
        ></view>
      </view>
      <view class="item-wrap between">
        <view class="text">上报时间：2024-2-23 17:12:34</view>
        <view class="text">K3+300-K2+345</view>
      </view>
      <view class="item-wrap">
        <view class="text">事件类型：故障</view>
      </view>
    </view>
    <!-- 拥堵 jam -->
    <view class="box-wrap" v-if="infoType == 'jam'">
      <view class="item-wrap" style="margin-bottom:0;">
        <image
          class="image"
          @click="speek"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_3.png"
        ></image>
        <view class="text text-info">
          <view class="status-tip red">严重</view>

          <view class="black-tip" style="margin-right:100rpx;"
            >143-兰海高速</view
          >
          <view class="black-tip">拥堵时间3min</view>
        </view>
      </view>
      <view class="item-wrap">
        <view
          class="text"
          style="margin-left:174rpx;font-size: 24rpx;color: #999999;"
          >2024-2-23 17:12:34</view
        >
      </view>
      <view class="gray-wrap">
        <view class="li-wrap">
          <view class="li-label">北向南</view>
          <view class="li-value li-value3">拥堵距离 3km</view>
        </view>
        <view class="li-wrap">
          <view class="li-label">平均车速 30km/h</view>
          <view class="li-value li-value3">异常拥堵</view>
        </view>
        <view class="li-wrap">
          <view class="li-label">关西隧道附近</view>
          <view class="li-value li-value3">道路类型 高速</view>
        </view>
      </view>
    </view>
    <view class="close" @click="closeInfo">
      <image class="image" src="@/static/toc/close.png"></image
    ></view>
  </view>
</template>

<script>
const plugin = requirePlugin("WechatSI");
const innerAudioContext = uni.createInnerAudioContext();
export default {
  props: {
    infoType: {
      type: String,
      default: "jam" // event - 事件 work - 施工 jam - 拥堵 road-优惠路段
    },
    infoData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isfoldText: true
    };
  },
  methods: {
    foldText() {
      this.isfoldText = !this.isfoldText;
    },
    closeInfo() {
      this.$emit("closeInfo");
    },
    speek(content) {
      let _this = this;
      plugin.textToSpeech({
        lang: "zh_CN",
        tts: true,
        content:content,
        success: function(res) {
          console.log("succ tts", res.filename);
          _this.yuyinPlay(res.filename);
        },
        fail: function(res) {
          console.log("fail tts", res);
        }
      });
    },
    yuyinPlay(src) {
      if (src == "") {
        return;
      }
      // if (innerAudioContext) {
      //   try {
      //     innerAudioContext.pause();
      //     innerAudioContext.destroy();
      //     innerAudioContext = null;
      //   } catch (e) {
      //     //TODO handle the exception
      //     return
      //   }
      // }
      innerAudioContext.autoplay = true;
      innerAudioContext.src = src; //设置音频地址
      innerAudioContext.play(); //播放音频
    }
  }
};
</script>

<style lang="scss" scoped>
.info-wrap {
  width: 100%;
  // height: 188rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 0rpx 0rpx;
  padding: 24rpx 0;
  box-sizing: border-box;
  position: relative;
  .item-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 14rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    &.between {
      justify-content: space-between;
    }
    .image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }
    .text-info {
      display: flex;
      align-items: center;
      .warn-tip {
        color: #ff9e59;
        margin-right: 30rpx;
        &.event {
          color: #ce6134;
        }
      }
      .road-tip {
        color: #08ba81;
        margin-right: 206rpx;
      }
      .green-tip {
        width: 76rpx;
        height: 36rpx;
        background: #08ba81;
        border-radius: 4rpx 4rpx 4rpx 4rpx;
        color: #ffffff;
        font-weight: 500;
        text-align: center;
        line-height: 36rpx;
      }
      .status-tip {
        width: 114rpx;
        height: 44rpx;
        line-height: 44rpx;
        background: #ff9e59;
        border-radius: 2rpx 2rpx 2rpx 2rpx;
        font-size: 24rpx;
        color: #ffffff;
        text-align: center;
        margin-left: 90rpx;
        &.red {
          width: 96rpx;
          height: 40rpx;
          line-height: 40rpx;
          background: #f82a3c;
          margin-left: 0;
          margin-right: 32rpx;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
        }
      }
      .right-block {
        margin-left: 112rpx;
      }
      .black-tip {
        flex: 1;
        color: #333333;
        font-weight: 500;
        margin-bottom: 5rpx;
      }
      .time-tip {
        color: #999;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .gray-wrap {
    background: #f6f6f6;
    padding: 20rpx;
    box-sizing: border-box;
    .li-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      margin-bottom: 16rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .li-label {
        color: #666666;
      }
      .li-value {
        width: 408rpx;
        color: #333333;
        white-space: nowrap; /* 保证文本在一行内显示 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
        &.textOpen {
          white-space: pre-wrap;
        }
        &.li-value2 {
          width: 664rpx;
          color: #666666;
        }
        &.li-value3 {
          width: auto;
        }
        &.over-2 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          white-space: unset;
        }
      }
    }
    .more {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #999999;
      justify-content: flex-end;
      .image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
  .close {
    position: absolute;
    width: 35rpx;
    height: 35rpx;
    top: 24rpx;
    right: 5rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>