<template>
  <view class="map-search-wrap">
    <view class="location-picker">
      <view class="location-wrap">
        <view class="arrow-wrap">
          <view class="circle green"> </view>
          <view class="arrow-line"> </view>
          <view class="circle red"> </view>
        </view>
        <view class="input-container">

          <view class="search-form-map" @click="chooseLocation('startPoint')">
            <!-- <view class="title">ETC卡号</view> -->
            <view class="value" v-if="startPoint.name">{{
              startPoint.name
              }}</view>
            <view class="label" v-else>请输入起点</view>
          </view>
          <view class="search-form-line"></view>

          <view class="search-form-map" @click="chooseLocation('endPoint')">
            <!-- <view class="title">ETC卡号</view> -->
            <view class="value" v-if="endPoint.name">{{
              endPoint.name
              }}</view>
            <view class="label" v-else>请输入终点</view>
          </view>
        </view>
        <view class="interchange-img" @click="swapLocations">
          <image class="cover-image" src="~@/pagesD/static/map/change.png"></image>
        </view>
        <view>
          <view class="btn-item" @click="searchRoute">
            搜索
          </view>
        </view>
      </view>
    </view>
    <view class="truck-options-wrap">
        <view class="truck-checkbox-section">
          <checkbox-group @change="onIsTruckChange">
            <label class="truck-checkbox-label">
              <checkbox class="cyan checked remember-check" value="checked" :checked="isTruck" color="#0066e9" style="transform:scale(0.6)" />
              <view style="margin-left: 5px; font-size: 24rpx;">车辆是否为货车</view>
            </label>
          </checkbox-group>
        </view>
        <view class="truck-type-section" v-if="isTruck">
          <view class="truck-type-label">类型:</view>
          <view class="truck-type-buttons">
            <view v-for="truckType in truckTypes" :key="truckType"
              :class="['truck-type-button', { active: selectedTruckType === truckType }]"
              @click="selectTruckType(truckType)">
              {{ truckType }}
            </view>
          </view>
        </view>
      </view>

  </view>

</template>

<script>
export default {
  data() {
    return {
      startPoint: {},
      endPoint: {
        address: "广西壮族自治区贵港市平南县镇隆镇",
        errMsg: "chooseLocation:ok",
        latitude: 23.417334,
        longitude: 110.43213,
        name: "平南南站"
      },
      isTruck: false,
      truckTypes: ['一类车', '二类车', '三类车', '四类车', '五类车', '六类车'],
      selectedTruckType: '一类车' // 默认选中一类车
    };
  },
  methods: {
    // 起点终点调换
    swapLocations() {
      let temp = this.startPoint;
      this.startPoint = this.endPoint;
      this.endPoint = temp;
    },
    // 选择地点
    chooseLocation(type) {
      uni.chooseLocation({
        success: res => {
          console.log(res, 123);
          this[type] = res;
        },
        fail(err) {
          console.log(err);
        }
      });
    },
    // 处理货车复选框变化
    onIsTruckChange(event) {
      this.isTruck = event.detail.value.length > 0;
      if (this.isTruck && !this.selectedTruckType) {
        this.selectedTruckType = '一类车'; // 如果之前未选择，默认选中一类车
      }
      // 向父组件传递勾选状态和类型
      this.emitTruckInfo();
    },
    //选择货车类型
    selectTruckType(type) {
      this.selectedTruckType = type;
      // 向父组件传递更新后的类型
      this.emitTruckInfo();
    },
    // 向父组件传递货车信息
    emitTruckInfo() {
      const truckInfo = {
        isTruck: this.isTruck,
        truckType: this.isTruck ? this.selectedTruckType : null
      };
      this.$emit("truckInfoChange", truckInfo);
    },
    // 查找路线
    searchRoute() {
      let params = {
        startPoint: this.startPoint,
        endPoint: this.endPoint,
        isTruck: this.isTruck
      };
      if (this.isTruck) {
        params.truckType = this.selectedTruckType;
      }
      console.log(params);
      if (!params.startPoint.latitude || !params.endPoint.latitude) return;

      console.log(this.startPoint, this.endPoint, "666666");
      this.$emit("searchRoute", params);
    },
    updateLocation(location) {
      // let location = uni.getStorageSync("location");
      this.startPoint = {
        ...location,
        name: "当前位置"
      };
    }
  }
};
</script>

<style scoped lang="scss">
.location-picker {
  // height: 193rpx;
  box-sizing: border-box;
  padding: 28rpx 20rpx;
  background-color: #fff;
  box-shadow: 0rpx 8rpx 10rpx 0rpx rgba(0, 0, 0, 0.09);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  padding-bottom: 20rpx;

  .location-wrap {
    width: 100%;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    border-radius: 10rpx;
    height: 137rpx;
    background: #f8f8f8;
    padding: 18rpx;
    border: 1rpx solid #e2e2e2;

    .arrow-wrap {
      width: 40rpx;
      display: flex;
      flex-direction: column;
      align-items: center;

      .circle {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;

        &.green {
          background: #00d17c;
        }

        &.red {
          background: #ff5454;
        }
      }

      .arrow-line {
        width: 4rpx;
        height: 34rpx;
        background: #e2e2e2;
        margin: 10rpx 0;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
      }
    }

    .input-container {
      width: 480rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 8rpx;

      .search-form-map {
        width: 100%;
        font-size: 28rpx;
        font-weight: 400;
        padding-left: 8rpx;

        .value {
          color: #333333;
        }

        .label {
          color: #959595;
        }
      }

      .search-form-line {
        width: 100%;
        height: 1rpx;
        background: #e2e2e2;
        margin: 16rpx 0;
      }

      margin-right: 15rpx;
    }

    .interchange-img {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 24rpx 0 15rpx;

      .cover-image {
        width: 29rpx;
        height: 25rpx;
      }
    }

    .btn-item {
      width: 84rpx;
      height: 46rpx;
      background: #0066e9;
      color: #ffffff;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      line-height: 46rpx;
      text-align: center;
      font-size: 24rpx;
    }
  }
}

.truck-options-wrap {
    width: 100%;
    // height: 76rpx;
    padding: 12rpx;
    box-sizing: border-box;
    background: rgba(255,255,255,0.73);

    .truck-checkbox-section {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #333;

      .truck-checkbox-label {
        display: flex;
        align-items: center;
      }
    }

    .truck-type-section {
      margin-top: 12rpx;
      background: rgba(255,255,255,0.73);

      .truck-type-label {
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        margin-bottom: 12rpx;
      }

      .truck-type-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10rpx;

        .truck-type-button {
          padding: 6rpx 16rpx;
          font-size: 24rpx;
          border-radius: 8rpx;
          background: #FFFFFF;
          color: #666666;
          border: 1rpx solid #E1E1E1;
          box-sizing: border-box;
          text-align: center;

          &.active {
            background-color: #0066e9;
            color: #ffffff;
            border-color: #0052cc;
          }
        }
      }
    }
  }
</style>