<template>
  <view class="service-item">
    <image class="service-image" :src="service.image" mode="aspectFill"></image>
    <view class="service-info">
      <view class="service-header">
        <view class="service-name">{{ service.name }}</view>
        <view class="service-distance">距您{{ service.distance }}</view>
      </view>
      <view class="service-facilities">
        <view
          class="facility-item"
          v-for="(facility, index) in facilityList"
          :key="index"
          @click.stop="handleFacilityClick(service, facility.key)"
        >
          <image
            :src="service[facility.key] ? facility.icon1 : facility.icon"
            mode="aspectFit"
          ></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    service: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      facilityList: [
        {
          name: "卫生间",
          icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/toilet_icon.png",
          icon1: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/toilet_icon_highlight.png",
          key: "toiletFlag"
        },
        {
          name: "停车场",
          icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/park_icon.png",
          icon1: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/park_icon_highlight.png",
          key: "parkFlag"
        },
        {
          name: "加油站",
          icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/tankers_icon.png",
          icon1: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/tankers_icon_highlight.png",
          key: "tankersFlag"
        },
        {
          name: "充电桩",
          icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/charging_icon.png",
          icon1: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/charging_icon_highlight.png",
          key: "chargingFlag"
        },
        {
          name: "便利店",
          icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/store_icon.png",
          icon1: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/store_icon_highlight.png",
          key: "storeFlag"
        },
        {
          name: "餐厅",
          icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/restaurant_icon.png",
          icon1: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/restaurant_icon_highlight.png",
          key: "restaurantFlag"
        }
      ]
    };
  },
  methods: {
    handleFacilityClick(service, facilityKey) {
      // 处理充电桩点击事件
      if (facilityKey === "chargingFlag" && service[facilityKey]) {
        uni.navigateToMiniProgram({
          appId: "wx33901e6f35bd973c",
          success() {}
        });
      }
      // 可以添加其他设施的点击处理逻辑
      this.$emit('facilityClick', { service, facilityKey });
    }
  }
};
</script>

<style lang="scss" scoped>
.service-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #E7E7E7;
  &:last-child {
    border-bottom: none;
  }
  
  .service-image {
    width: 142rpx;
    height: 140rpx;
    border-radius: 8rpx;
    margin-right: 20rpx;
  }
  
  .service-info {
    flex: 1;
    height: 135rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .service-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;
    }
    
    .service-name {
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }
    
    .service-distance {
      font-size: 24rpx;
      color: #999999;
    }
    
    .service-facilities {
      display: flex;
      gap: 12rpx;

      .facility-item {
        display: flex;
        align-items: center;
        justify-content: center;

        & > image {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
}
</style>
