<template>
  <view>
    <!-- 用户信息弹出层 -->
    <view class="user-info-dialog-overlay" v-if="visible" @touchmove.stop.prevent>
      <view class="user-info-dialog" @tap.stop>
        <!-- 关闭按钮 -->
        <view class="dialog-close-btn" @tap="closeDialog">
          <uni-icons type="closeempty" size="22" color="#999999"></uni-icons>
        </view>

        <!-- 弹窗内容区域 -->
        <view class="user-info-dialog-content">
          <!-- 顶部图像区域 -->
          <view class="dialog-header">
            <image class="header-bg" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/evaluation-bg.jpg" mode="aspectFill"></image>
            <view class="header-overlay"></view>
            <view class="dialog-title">欢迎使用评价功能</view>
            <view class="dialog-subtitle">请先完善您的个人信息</view>
          </view>
          
          <!-- 头像选择区域 -->
          <view class="avatar-wrapper">
            <view class="avatar-title">请选择头像</view>
            <button
              class="avatar-btn"
              open-type="chooseAvatar"
              @chooseavatar="onChooseAvatar"
              :disabled="uploading"
            >
              <image class="avatar-image" :src="avatarUrl" mode="aspectFill"></image>

              <!-- 上传进度遮罩 -->
              <view class="upload-progress-overlay" v-if="uploading">
                <view class="upload-progress-circle">
                  <text class="upload-progress-text">{{ uploadProgress }}%</text>
                </view>
              </view>

              <!-- 编辑图标 -->
              <view class="avatar-edit-icon" v-if="!uploading">
                <uni-icons type="camera-filled" size="18" color="#FFFFFF"></uni-icons>
              </view>
            </button>

            <!-- 上传状态提示 -->
            <view class="upload-status" v-if="uploading">
              <text class="upload-status-text">头像上传中...</text>
            </view>
          </view>
          
          <!-- 昵称输入区域 -->
          <view class="nickname-wrapper">
            <view class="nickname-title">请输入昵称</view>
            <input 
              class="nickname-input" 
              type="nickname" 
              placeholder="点击输入您的昵称" 
              maxlength="12"
              v-model="nickname"
              @change="onNicknameChange" 
            />
          </view>
          
          <!-- 底部按钮 -->
          <view class="dialog-footer">
            <button
              class="confirm-btn"
              :disabled="!avatarUrl || nickname.trim() === '' || uploading"
              @tap="confirmUserInfo"
            >
              {{ uploading ? '头像上传中...' : '确认' }}
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue';
import VODUpload from "@/pagesA/ali-video/aliSdk/aliyun-upload-sdk-1.0.1.min.js";
import dayjs from "@/js_sdk/dayjs/dayjs.min.js";
import { getAccountId } from "@/common/storageUtil.js";

export default {
  name: 'TUserInfoDialog',
  components: {
    uniIcons
  },
  props: {
    // 控制弹出层是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 初始头像URL
    initialAvatarUrl: {
      type: String,
      default: ''
    },
    // 初始昵称
    initialNickname: {
      type: String,
      default: ''
    },
    // 默认头像
    // defaultAvatar: {
    //   type: String,
    //   default: ''
    // }
  },
  data() {
    return {
      // 头像URL
      avatarUrl: '',
      // 昵称
      nickname: '',
      // 上传相关
      uploader: null,
      uploading: false,
      uploadProgress: 0,
      uploadedAvatarId: '', // 上传成功后的头像ID
      defaultAvatarUrl: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/guide.png'
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 弹窗显示时初始化数据
        this.avatarUrl = this.initialAvatarUrl || '';
        this.nickname = this.initialNickname || '';
      }
    },
    initialAvatarUrl(newVal) {
      if (newVal) {
        this.avatarUrl = newVal;
      }
    },
    initialNickname(newVal) {
      if (newVal) {
        this.nickname = newVal;
      }
    }
  },
  created() {
    // 初始化默认值
    this.avatarUrl = this.initialAvatarUrl || this.defaultAvatarUrl;
    this.nickname = this.initialNickname || '';
  },
  mounted() {
    // 初始化阿里云上传SDK
    this.initAliUploadSDK();
  },
  methods: {
    // 初始化阿里云上传SDK
    initAliUploadSDK() {
      const that = this;
      this.uploader = new VODUpload({
        timeout: 60000,
        region: "cn-shenzhen",
        addFileSuccess(uploadInfo) {
          console.log('头像文件添加成功:', uploadInfo);
        },
        async onUploadstarted(uploadInfo) {
          that.uploading = true;
          that.uploadProgress = 0;

          const data = {
            type:'avatar',
            title: `用户头像-小程序上传-${getAccountId()}-${dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")}`,
            fileName: `用户头像-小程序上传-${getAccountId()}-${dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")}`
          };

          try {
            const res = await that.$request.post(that.$interfaces.getImageUploadAuth, {
              data
            });
            const akInfo = res.data;

            that.uploader.setUploadAuthAndAddress(
              uploadInfo,
              akInfo.uploadAuth,
              akInfo.uploadAddress,
              akInfo.imageId
            );
          } catch (err) {
            that.uploader.stopUpload();
            that.uploading = false;
            uni.showToast({
              title: '获取上传凭证失败',
              icon: 'none'
            });
          }
        },
        onUploadSucceed(uploadInfo) {
          that.uploading = false;
          that.uploadProgress = 100;
          that.uploadedAvatarId = uploadInfo.videoId;
          console.log('头像上传成功:', uploadInfo);

          uni.showToast({
            title: '头像上传成功',
            icon: 'success'
          });
        },
        onUploadFailed(uploadInfo, code, message) {
          that.uploading = false;
          that.uploadProgress = 0;
          console.error('头像上传失败:', code, message);

          uni.showToast({
            title: '头像上传失败',
            icon: 'none'
          });
        },
        onUploadProgress(uploadInfo, totalSize, progress) {
          that.uploadProgress = Math.ceil(progress);
        },
        onUploadEnd(uploadInfo) {
          that.uploading = false;
        }
      });
    },

    // 关闭弹出层
    closeDialog() {
      this.$emit('close');
    },

    // 选择头像
    onChooseAvatar(e) {
      const { avatarUrl } = e.detail;

      if (!avatarUrl) {
        uni.showToast({
          title: '头像选择失败',
          icon: 'none'
        });
        return;
      }

      // 先设置临时头像显示
      this.avatarUrl = avatarUrl;

      // 开始上传头像到服务器
      this.uploadAvatarToServer(avatarUrl);

      console.log('头像选择:', avatarUrl);
      this.$emit('avatar-change', avatarUrl);
    },

    // 上传头像到服务器
    async uploadAvatarToServer(tempFilePath) {
      try {
        // 检查文件大小（限制5MB）
        const fileInfo = await this.getFileInfo(tempFilePath);
        const fileSizeMB = fileInfo.size / (1024 * 1024);

        if (fileSizeMB > 5) {
          uni.showToast({
            title: '头像大小不能超过5MB',
            icon: 'none'
          });
          return;
        }

        // 检查文件格式
        const ext = tempFilePath.split('.').pop().toLowerCase();
        if (!['jpg', 'jpeg', 'png'].includes(ext)) {
          uni.showToast({
            title: '仅支持JPG/PNG格式头像',
            icon: 'none'
          });
          return;
        }

        // 构造文件对象用于上传
        const fileForUploader = {
          url: tempFilePath,
          file: {
            path: tempFilePath,
            size: fileInfo.size
          }
        };

        // 添加文件到上传队列并开始上传
        this.uploader.addFile(fileForUploader, null, null, null, '{"Vod":{}}');
        this.uploader.startUpload();

      } catch (error) {
        console.error('头像上传准备失败:', error);
        uni.showToast({
          title: '头像处理失败',
          icon: 'none'
        });
      }
    },

    // 获取文件信息
    getFileInfo(filePath) {
      return new Promise((resolve, reject) => {
        uni.getFileInfo({
          filePath: filePath,
          success: resolve,
          fail: reject
        });
      });
    },

    // 修改昵称
    onNicknameChange(e) {
      const value = e.detail.value || this.nickname;
      this.nickname = value;
      this.$emit('nickname-change', value);
    },

    // 确认用户信息
    confirmUserInfo() {
      if (!this.avatarUrl) {
        uni.showToast({
          title: '请选择头像',
          icon: 'none'
        });
        return;
      }

      if (this.nickname.trim() === '') {
        uni.showToast({
          title: '请输入昵称',
          icon: 'none'
        });
        return;
      }

      if (this.uploading) {
        uni.showToast({
          title: '头像正在上传中，请稍候',
          icon: 'none'
        });
        return;
      }

      this.$emit('confirm', {
        avatarUrl: this.uploadedAvatarId || this.avatarUrl, // 优先使用上传后的ID
        nickname: this.nickname,
        uploadedAvatarId: this.uploadedAvatarId // 传递上传后的头像ID
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.user-info-dialog-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.user-info-dialog {
  width: 88%;
  max-width: 640rpx;
  animation: scaleIn 0.3s ease;
  position: relative;
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.dialog-close-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  background-color: #FFFFFF;
  border-radius: 50%;
}

.user-info-dialog-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.dialog-header {
  position: relative;
  height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #FFFFFF;
  overflow: hidden;
  padding: 0 30rpx;
  text-align: center;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 102, 233, 0.8), rgba(0, 102, 233, 0.95));
  z-index: 2;
}

.dialog-title {
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  z-index: 3;
}

.dialog-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  z-index: 3;
}

.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 36rpx 30rpx 20rpx;
}

.avatar-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 24rpx;
  text-align: center;
}

.avatar-btn {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  padding: 0;
  margin: 0;
  background: none;
  position: relative;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.avatar-btn::after {
  border: none;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-edit-icon {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.5));
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 70rpx;
}

.upload-progress-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-progress-text {
  font-size: 20rpx;
  color: #333333;
  font-weight: 500;
}

.upload-status {
  margin-top: 16rpx;
  text-align: center;
}

.upload-status-text {
  font-size: 24rpx;
  color: #0066E9;
}

.nickname-wrapper {
  padding: 20rpx 40rpx 30rpx;
}

.nickname-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  background-color: #F8F8F8;
  border: 1rpx solid #EEEEEE;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}

.dialog-footer {
  padding: 0 40rpx 40rpx;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #0066E9;
  color: #FFFFFF;
  font-size: 30rpx;
  font-weight: 500;
  text-align: center;
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 233, 0.2);
  transition: all 0.3s;
}

.confirm-btn:active {
  transform: scale(0.98);
  background-color: #0055cc;
}

.confirm-btn[disabled] {
  background-color: #CCCCCC;
  color: #FFFFFF;
  box-shadow: none;
}
</style> 