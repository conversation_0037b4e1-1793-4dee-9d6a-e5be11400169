<template>
  <view>
    <!-- 评价详情弹出层 -->
    <view class="comment-dialog-overlay" v-if="visible" @tap="closeDialog">
      <view class="comment-dialog" @tap.stop>
        <!-- 对话框标题 -->
        <view class="comment-dialog-header">
          <!-- <view class="comment-dialog-title">用户评价</view> -->
          <view class="comment-dialog-close" @tap="closeDialog">
            <uni-icons type="close" size="20" color="#333333"></uni-icons>
          </view>
        </view>
        
        <!-- 对话内容区域，可滚动 -->
        <scroll-view class="comment-dialog-content" scroll-y>
          <!-- 主评论区域 -->
          <view class="main-comment" v-if="comment">
            <view class="main-comment-header">
              <view class="main-comment-user">
                <view class="user-avatar">
                  <image :src="comment.avatar || defaultAvatar" mode="aspectFill" />
                </view>
                <view class="user-info">
                  <view class="user-name">{{comment.userName || '匿名用户'}}</view>
                  <view class="comment-time">发布于 {{comment.time}}</view>
                </view>
              </view>
            </view>
            
            <view class="main-comment-content">
              <!-- 星级评分 -->
              <view class="rating-container">
                <t-star-rating 
                  :value="comment.rate" 
                  :readonly="true"
                  :size="24"
                />
                <view class="satisfaction-text">{{satisfactionText}}</view>
              </view>
              
              <!-- 评价文本 -->
              <view class="comment-text">{{comment.text}}</view>
              
              <!-- 标签 -->
              <!-- <t-evaluation-tags 
                v-if="comment.tags && comment.tags.length > 0"
                :tags="comment.tags"
                :value="comment.tags"
                :readonly="true"
              /> -->
              
              <!-- 图片 -->
              <view class="comment-images" v-if="comment.images && comment.images.length > 0">
                <view 
                  class="image-item" 
                  v-for="(image, imgIndex) in comment.images" 
                  :key="imgIndex" 
                  @tap="previewImage(comment.images, image)"
                >
                  <image :src="image" mode="aspectFill" />
                </view>
              </view>
            </view>

          </view>
          
          <!-- 对话列表 -->
          <view class="dialog-message-list">
            <view 
              class="dialog-message-item" 
              :class="[item.isOfficial == 1 ? 'official' : 'user']"
              v-for="(item, index) in sortedDialogList" 
              :key="index"
            >
              <!-- 用户信息和内容 -->
              <view class="dialog-message-wrapper">
                <view class="dialog-user-avatar" :class="{'official-avatar': item.isOfficial == 1}">
                  <image v-if="item.isOfficial == 1" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/gxt_logo.png" mode="aspectFill" />
                  <image v-else :src="item.avatar || defaultAvatar" mode="aspectFill" />
                </view>
                <view class="dialog-message-box">
                  <view class="dialog-user-info">
                    <view class="dialog-user-name">
                      {{item.isOfficial == 1 ? '桂小通' : item.userName}}
                      <text class="user-tag official" v-if="item.isOfficial == 1">平台官方人员</text>
                    </view>
                    <view class="dialog-message-time">
                      <text>{{item.time}}</text>
                      <!-- 回复按钮：只有当整个对话列表的第一条是官方回复时才显示 -->
                      <text class="reply-text-btn" v-if="item.isOfficial == 1 && comment && comment.isCurrentUser && isFirstItemOfficial(index)"
                        @tap.stop="showReplyDialog(item)">
                        回复
                      </text>
                    </view>
                  </view>
                  <view class="dialog-message-content">{{item.content}}</view>
                  
                  <!-- 子回复图片 -->
                  <view class="dialog-message-images" v-if="item.images && item.images.length > 0">
                    <view 
                      class="dialog-image-item" 
                      v-for="(image, imgIndex) in item.images" 
                      :key="imgIndex" 
                      @tap="previewImage(item.images, image)"
                    >
                      <image :src="image" mode="aspectFill" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
        
      </view>
    </view>
    
    <!-- 回复弹出层 -->
    <view class="reply-dialog-overlay" v-if="replyDialogVisible" @tap="closeReplyDialog">
      <view class="reply-dialog" @tap.stop>
        <!-- 回复框标题 -->
        <view class="reply-dialog-header">
          <view class="reply-dialog-close" @tap="closeReplyDialog">
            <uni-icons type="close" size="20" color="#333333"></uni-icons>
          </view>
        </view>
        
        <!-- 回复内容输入区 -->
        <view class="reply-dialog-content">
          <textarea 
            class="reply-textarea" 
            :placeholder="replyPlaceholder" 
            v-model="replyContent"
            maxlength="200"
            :auto-height="false"
            :show-confirm-bar="false"
            :adjust-position="true"
            @input="handleTextareaInput"
          ></textarea>
          <view class="reply-word-count">{{replyContent.length}}/200</view>
          
          <!-- 图片上传区域 -->
          <view class="reply-image-upload">
            <view class="upload-from">
              <view class="upload" v-for="(item, index) in imgList" @tap="viewImage(item)" :key="index">
                <view class="upload-wrap">
                  <view class="upload-wrap-bd">
                    <image :src="item.url" class="upload-wrap__img" mode="aspectFill"></image>
                    <view class="upload-progress" v-if="item.status === 'uploading'">
                      <text class="upload-progress-text">{{ item.progress }}%</text>
                    </view>
                    <view class="upload-wrap__close" @tap.stop="delImgHandle(item, index)">
                      <image src="@/pagesD/static/close.png" mode="" class="close">
                      </image>
                    </view>
                  </view>
                </view>
              </view>
              <view class="upload upload-add" v-if="imgList.length < 3" @tap="chooseImage">
                <view class="upload-wrap">
                  <view class="upload-wrap-bd upload-placeholder">
                    <text class="add-icon">+</text>
                    <text class="add-text">添加图片</text>
                  </view>
                </view>
              </view>
            </view>
            <view class="upload-count">{{imgList.length}}/3</view>
          </view>
        </view>
        
        <!-- 回复按钮 -->
        <view class="reply-dialog-footer">
          <button 
            class="reply-submit-btn" 
            :disabled="replyContent.trim() === '' || isReplySubmitting"
            :loading="isReplySubmitting"
            @tap="submitReply"
          >回复</button>
        </view>
      </view>
    </view>
    
    <!-- 图片上传组件 -->
    <!-- <cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :number="1" :fixOrientation="true" :size="500" :maxWidth="800" :ql="0.9"
      type="base64">
    </cpimg> -->
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue';
// import cpimg from '@/components/uni-yasuo/cpimg.vue';
import TStarRating from '@/components/common/t-star-rating.vue';
import TEvaluationTags from '@/components/common/t-evaluation-tags.vue';
import {
  getLoginUserInfo,
  getOpenid,
  getOpenidForRead,
  getAccountId
} from "@/common/storageUtil.js";
import VODUpload from "@/pagesA/ali-video/aliSdk/aliyun-upload-sdk-1.0.1.min.js";
import dayjs from "@/js_sdk/dayjs/dayjs.min.js";

export default {
  name: 'TCommentDialog',
  components: {
    uniIcons,
    // cpimg,
    TStarRating,
    TEvaluationTags
  },
  props: {
    // 控制弹出层是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 当前评价的详细信息
    comment: {
      type: Object,
      default: () => ({})
    },
    // 评价详情对话列表
    dialogList: {
      type: Array,
      default: () => []
    },
    // 控制是否立即开启回复
    startReply: {
      type: Boolean,
      default: false
    },
    evaluationType: {
      type: String,
      default: ''
    },
    businessId: {
      type: [String, Number],
      default: ''
    },
    // 默认头像
    defaultAvatar: {
      type: String,
      default: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/guide.png'
    }
  },
  data() {
    return {
      // 回复弹出层是否可见
      replyDialogVisible: false,
      // 回复内容
      replyContent: '',
      // 回复目标
      replyTarget: null,
      // 图片列表
      imgList: [],
      // 图片URL列表（上传后）
      urlList: [],
      // 源类型
      sourceType: ['camera', 'album'],
      isReplySubmitting: false,
      uploader: null,
    };
  },
  computed: {
    // 获取满意度文本
    satisfactionText() {
      if (!this.comment || !this.comment.rate) return '';
      
      const rateMap = {
        1: '不满意',
        2: '较差',
        3: '一般',
        4: '满意',
        5: '非常满意'
      };
      
      return rateMap[this.comment.rate] || '';
    },
    replyPlaceholder() {
      const targetName = this.replyTarget ? this.replyTarget.userName : (this.comment ? this.comment.userName : '');
      return `回复 ${targetName}`;
    },
    sortedDialogList() {
      if (!this.dialogList) {
        return [];
      }
      const sortedList = [...this.dialogList].sort((a, b) => {
        try {
          const timeA = new Date(a.time).getTime();
          const timeB = new Date(b.time).getTime();
          if (isNaN(timeA) || isNaN(timeB)) {
            return 0;
          }
          return timeB - timeA;
        } catch (e) {
          return 0;
        }
      });
      
      return sortedList;
    }
  },
  watch: {
    visible(newValue) {
      if (newValue) {
        if (this.startReply) {
          this.$nextTick(() => {
            console.log(this.comment,'showReplyDialog');
            this.showReplyDialog(this.comment.replies[0]);
          });
        }
      } else {
        // 当主弹窗关闭时，确保回复弹窗也关闭
        this.closeReplyDialog();
      }
    }
  },
  mounted() {
    this.aliImageSdkInit();
  },
  methods: {
    // 判断整个对话列表的第一条是否为官方回复
    isFirstItemOfficial(index) {
      if (!this.sortedDialogList || this.sortedDialogList.length === 0 || index != 0) {
        return false;
      }

      // 检查第一条记录是否为官方回复
      return this.sortedDialogList[index].isOfficial == 1;
    },

    aliImageSdkInit() {
      let that = this;
      this.uploader = new VODUpload({
        timeout: 60000,
        region: "cn-shenzhen",
        addFileSuccess(uploadInfo) {
          that.imgList.push({
            url: uploadInfo.url,
            status: 'waiting',
            progress: 0,
            file: uploadInfo.file
          });
        },
        async onUploadstarted(uploadInfo) {
          const img = that.imgList.find(item => item.url === uploadInfo.url);
          if (img) {
            img.status = 'uploading';
          }
          let data = {
            type:'comment',
            title: `回复图片-小程序上传-${getAccountId()}-${dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")}`,
            fileName: `回复图片-小程序上传-${getAccountId()}-${dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")}`,
          };
          try {
            let res = await that.$request.post(that.$interfaces.getImageUploadAuth, {
              data
            });
            let akInfo = res.data;
            that.uploader.setUploadAuthAndAddress(
              uploadInfo,
              akInfo.uploadAuth,
              akInfo.uploadAddress,
              akInfo.imageId
            );
          } catch (err) {
            that.uploader.stopUpload();
            const img = that.imgList.find(item => item.url === uploadInfo.url);
            if (img) {
              img.status = 'failed';
            }
            uni.showToast({
              title: '获取上传凭证失败',
              icon: 'none'
            });

          }
        },
        onUploadSucceed: function(uploadInfo) {
          const img = that.imgList.find(item => item.url === uploadInfo.url);
          if (img) {
            img.status = 'success';
            img.progress = 100;
            img.videoId = uploadInfo.videoId;
          }
          that.urlList.push(uploadInfo.videoId);
        },
        onUploadFailed: function(uploadInfo, code, message) {
          uni.hideLoading();
          const index = that.imgList.findIndex(item => item.url === uploadInfo.url);
          if (index > -1) {
            that.imgList.splice(index, 1);
          }
          uni.showToast({
            title: '图片上传失败',
            icon: 'none'
          });
        },
        onUploadProgress(uploadInfo, totalSize, progress) {
          let progressPercent = Math.ceil(progress);
          const img = that.imgList.find(item => item.url === uploadInfo.url);
          if (img) {
            img.progress = progressPercent;
          }
        },
        onUploadEnd(uploadInfo) {
          uni.hideLoading();
        }
      });
    },
    getUserOpenid() {
      // 尝试多种方式获取openid
      let openid = getOpenid();
      if (!openid) {
        openid = getOpenidForRead();
      }

      // 如果还是没有，尝试从用户信息中获取
      if (!openid) {
        const userInfo = getLoginUserInfo();
        openid = userInfo?.openid || '';
      }

      return openid;
    },
    // 关闭评价详情弹出层
    closeDialog() {
      this.$emit('close');
    },
    
    // 显示回复弹出层
    showReplyDialog(item = null) {
      // 使用nextTick确保DOM完全渲染
      this.$nextTick(() => {
        try {
          this.replyTarget = item || this.comment;
          this.replyDialogVisible = true;
          // 清理输入内容
          this.replyContent = '';
          this.imgList = [];
          this.urlList = [];
        } catch (error) {
          // 降级处理，直接显示
          this.replyDialogVisible = true;
        }
        
      });
    },
    
    // 关闭回复弹出层
    closeReplyDialog() {
      this.replyDialogVisible = false;
      this.replyContent = '';
      this.imgList = [];
      this.urlList = [];
      this.replyTarget = null;
    },
    
    // 提交回复
    async submitReply() {


      if (!this.replyContent.trim()) {
        uni.showToast({
          title: '请输入回复内容',
          icon: 'none'
        });
        return;
      }
      
      if (this.imgList.some(item => item.status === 'uploading')) {
        uni.showToast({
          title: '请等待图片上传完成',
          icon: 'none'
        });
        return;
      }

      const parentId = this.replyTarget ? this.replyTarget.id : this.comment.id;
      if (!parentId) {
        uni.showToast({
          title: '无法确定要回复的评论',
          icon: 'none'
        });
        return;
      }
      this.isReplySubmitting = true;

      try {
        // 根据评价类型选择回复接口
        const apiMethodMap = {
          serviceArea: 'replyServiceAreaEvaluation',
          chargingService: 'replyChargingStationEvaluation',
          tollStation: 'replyTollStationEvaluation'
        };
        const apiMethod = apiMethodMap[this.evaluationType];

        if (!apiMethod) {
          uni.showToast({
            title: '未知的评价类型',
            icon: 'none'
          });
          return;
        }

        const openid = this.getUserOpenid();

        // 构造请求参数
        const params = {
          parentId: parentId,
          facilityId: this.businessId,
          content: this.replyContent,
          openid: openid,
          image: this.urlList.join(','),
        };

        // 调用回复API
        const res = await this.$request.post(this.$interfaces[apiMethod], {
          data: params
        });

        if (res.code == 200) {
          uni.showToast({
            title: '回复成功',
            icon: 'success'
          });

          this.closeReplyDialog();
          this.$emit('reply-successful');

        } else {
          uni.showToast({
            title: res.msg || '回复失败',
            icon: 'none'
          });
        }

      } catch (error) {
        uni.showToast({
          title: '网络异常，回复失败',
          icon: 'none'
        });
      } finally {
        this.isReplySubmitting = false;
      }
    },
    
    // 选择图片
    chooseImage() {
      if (this.imgList.length >= 3) {
        uni.showToast({
          title: '最多上传3张图片',
          icon: 'none'
        });
        return;
      }
      if (this.imgList.some(item => item.status === 'uploading')) {
        uni.showToast({
          title: '请等待当前图片上传完成',
          icon: 'none'
        });
        return;
      }
      uni.chooseImage({
        count: 3 - this.imgList.length,
        sizeType: ['compressed'],
        sourceType: this.sourceType,
        success: (res) => {
          res.tempFiles.forEach(tempFile => {
            const fileForUploader = {
              url: tempFile.path,
              file: tempFile
            };
            this.uploader.addFile(fileForUploader, null, null, null, '{"Vod":{}}');
          });
          this.uploader.startUpload();
        },
        fail: (err) => {
          if (err.errMsg !== "chooseImage:fail cancel") {
            uni.showToast({
              title: '图片选择失败',
              icon: 'none'
            });
          }
        }
      })
    },
    
    // 删除图片
    delImgHandle(item, index) {
      this.imgList.splice(index, 1);
      this.uploader.deleteFile(index);
      if (item.videoId) {
        const videoIdIndex = this.urlList.indexOf(item.videoId);
        if (videoIdIndex > -1) {
          this.urlList.splice(videoIdIndex, 1);
        }
      }
    },
    
    // 预览图片
    viewImage(item) {
      uni.previewImage({
        urls: this.imgList.map(i => i.url),
        current: item.url,
        fail: err => {
          uni.showToast({
            title: "图片预览失败",
            icon: 'none'
          });
        }
      });
    },
    
    // 预览评价图片
    previewImage(urls, current) {
      if (!urls || !Array.isArray(urls) || urls.length === 0) {
        uni.showToast({
          title: "没有可预览的图片",
          icon: 'none'
        });
        return;
      }
      
      uni.previewImage({
        urls: urls,
        current: current,
        fail: err => {
          uni.showToast({
            title: "图片预览失败",
            icon: 'none'
          });
        }
      });
    },
    
    // 处理textarea输入
    handleTextareaInput(event) {
      try {
        // 确保v-model正常工作
        if (event && event.detail && typeof event.detail.value === 'string') {
          this.replyContent = event.detail.value;
        }
      } catch (error) {
        // 忽略textarea输入处理异常
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.comment-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 200;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.comment-dialog {
  width: 100%;
  height: 85vh;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0.8;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.comment-dialog-header {
  height: 84rpx;
  padding: 0 10rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #FFFFFF;
  position: relative;
}

.comment-dialog-header::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1rpx;
  background-color: #EEEEEE;
  transform: scaleY(0.5);
}

.comment-dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.comment-dialog-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.comment-dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx 30rpx;
}

.main-comment {
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
  border-radius: 0;
  margin-bottom: 0;
}

.main-comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.main-comment-user {
  display: flex;
  align-items: center;
  width: 100%;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16rpx;
  border: none;
  background-color: #FFFFFF;
  box-shadow: none;
  
  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
}

.comment-time {
  font-size: 24rpx;
  color: #999999;
}

.main-comment-content {
  padding: 0;
}

.rating-container {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.satisfaction-text {
  font-size: 26rpx;
  color: #333333;
  margin-left: 16rpx;
}

.comment-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 16rpx;
  word-break: break-all;
  text-align: left;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  margin: 16rpx 0;
  gap: 28rpx;
}

.image-item {
  width: 168rpx;
  height: 168rpx;
  border-radius: 4rpx;
  overflow: hidden;
  
  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}


.dialog-message-list {
  display: flex;
  flex-direction: column;
  padding-left: 20rpx;
}

.dialog-message-item {
  margin-bottom: 16rpx;
  display: flex;
  align-items: flex-start;
  padding: 0 30rpx;
  position: relative;
}

.dialog-message-item:last-child {
  margin-bottom: 0;
}

.dialog-message-wrapper {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.dialog-user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.dialog-message-box {
  flex: 1;
}

.dialog-user-info {
  margin-bottom: 16rpx;
}

.dialog-user-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #666666;
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.user-tag.official {
  color: #0066E9;
  font-size: 26rpx;
  font-weight: 400;
  margin-left: 12rpx;
}

.dialog-message-time {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
}

.reply-text-btn {
  color: #999;
  margin-left: 20rpx;
}

.dialog-message-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  padding: 0;
  word-break: break-all;
  text-align: left;
  margin-bottom: 12rpx;
}

.dialog-message-images {
  display: flex;
  flex-wrap: wrap;
  margin: 12rpx 0;
  gap: 16rpx;
}

.dialog-image-item {
  width: 120rpx;
  height: 120rpx;
  border-radius: 4rpx;
  overflow: hidden;
  
  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.comment-dialog-footer {
  height: 100rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1rpx solid #EEEEEE;
  background-color: #FFFFFF;
}

.comment-reply-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: 70rpx;
  background-color: #F8F8F8;
  border-radius: 35rpx;
  padding: 0 30rpx;
  transition: all 0.3s ease;
}

.comment-reply-input-wrapper:active {
  background-color: #EEEEEE;
}

.comment-reply-placeholder {
  font-size: 28rpx;
  color: #999999;
  transition: color 0.3s ease;
}

.comment-reply-input-wrapper:active .comment-reply-placeholder {
  color: #666666;
}

.reply-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 300;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.reply-dialog {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
  max-height: 90vh;
}

.reply-dialog-header {
  height: 80rpx;
  padding: 0 10rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  // border-bottom: 1rpx solid #EEEEEE;
  position: relative;
}

.reply-dialog-close {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.reply-dialog-content {
  padding: 0 30rpx 30rpx;
}

.reply-textarea {
  width: 100%;
  min-height: 180rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333333;
  background-color: #F8F8F8;
  border-radius: 12rpx;
  line-height: 1.6;
  border: 1rpx solid #EEEEEE;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.reply-textarea:focus {
  border-color: #0066E9;
  background-color: #FFFFFF;
}

.reply-word-count {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
  margin: 10rpx 0;
}

.reply-image-upload {
  margin-top: 20rpx;
}

.upload-from {
  display: flex;
  flex-wrap: wrap;
}

.upload {
  width: 180rpx;
  height: 180rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.upload-wrap{
  width: 100%;
  height: 100%;
}

.upload-wrap-bd {
  width: 100%;
  height: 180rpx;
  position: relative;
}

.upload-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.upload-progress-text {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
}

.upload-wrap__img {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.upload-wrap__close {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  .close{
    width: 30rpx;
    height: 30rpx;
    background-size: 100%;
  }
}

.upload-add {
  border: 1px dashed #DDDDDD;
  border-radius: 8rpx;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  transition: background-color 0.3s ease;
  
  .add-icon {
    font-size: 50rpx;
    color: #0066E9;
    margin-bottom: 10rpx;
    line-height: 1;
  }
  
  .add-text {
    font-size: 24rpx;
    color: #666666;
  }
}

.upload-add:active .upload-placeholder {
  background-color: #EEEEEE;
}

.upload-count {
  font-size: 24rpx;
  color: #AAAAAA;
  text-align: right;
}

.reply-dialog-footer {
  padding: 0 30rpx 40rpx;
}

.reply-submit-btn {
  width: 100%;
  height: 80rpx;
  background-color: #0066E9;
  color: #FFFFFF;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 233, 0.2);
}

.reply-submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 102, 233, 0.2);
}

.reply-submit-btn[disabled] {
  opacity: 0.6;
  background-color: #CCCCCC;
  box-shadow: none;
}

.show-more-btn {
  width: 100%;
  height: 80rpx;
  background-color: #FFFFFF;
  color: #0066E9;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.show-more-btn:active {
  background-color: #F8F8F8;
}
</style> 