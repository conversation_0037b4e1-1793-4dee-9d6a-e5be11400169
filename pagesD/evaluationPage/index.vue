<template>
  <view class="evaluation-page">
    <view class="evaluation-content">
      <!-- 评价对象信息 -->
      <view class="business-info" v-if="businessInfo.name">
        <view class="business-name">{{businessInfo.name}}</view>
        <!-- <view class="business-detail" v-if="businessInfo.detail">{{businessInfo.detail}}</view>
        <view class="business-time" v-if="businessInfo.time">{{businessInfo.time}}</view> -->
      </view>
      
      <!-- 评价组件 -->
      <evaluation
        ref="evaluationComponent"
        :businessType="evaluationType"
        :initialStatus="evaluationStatus"
        :initialRate="initialRate"
        :initialText="initialText"
        :initialImgList="initialImgList"
        :initialTime="initialTime"
        :maxImageCount="3"
        :submitButtonText="'发布'"
        @submit="handleSubmit"
        @status-change="handleStatusChange"
        @rate-change="handleRateChange"
      ></evaluation>
      
      <!-- 提交成功提示 -->
      <view class="success-tip" v-if="showSuccessTip">
        <view class="success-icon">
          <uni-icons type="checkmarkempty" size="60" color="#0066E9"></uni-icons>
        </view>
        <view class="success-text">评价提交成功</view>
        <view class="success-subtext">感谢您的反馈，我们将持续提升服务质量</view>
        <view class="back-btn" @tap="goBack">返回</view>
      </view>
    </view>
    
    <!-- 用户信息弹窗 -->
    <t-user-info-dialog
      :visible="userInfoDialogVisible"
      :initialAvatarUrl="userAvatarUrl"
      :initialNickname="userNickname"
      :defaultAvatar="defaultAvatar"
      @close="closeUserInfoDialog"
      @avatar-change="onUserAvatarChange"
      @nickname-change="onUserNicknameChange"
      @confirm="onUserInfoConfirm"
    />
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue';
import evaluation from '@/pagesD/components/evaluation/index.vue';
import TStarRating from '@/components/common/t-star-rating.vue';
import TEvaluationTags from '@/components/common/t-evaluation-tags.vue';
import TEvaluationItems from '@/components/common/t-evaluation-items.vue';
import evaluationUtils from '@/pagesD/utils/evaluationUtils';
import { getLoginUserInfo, setLoginUserInfo, getOpenid, getOpenidForRead } from "@/common/storageUtil.js";
import TUserInfoDialog from './components/t-user-info-dialog.vue';

export default {
  components: {
    uniIcons,
    evaluation,
    TStarRating,
    TEvaluationTags,
    TEvaluationItems,
    TUserInfoDialog
  },
  data() {
    return {
      // 评价类型：serviceArea(服务区), tollStation(收费站), chargingService(充电服务)
      evaluationType: 'serviceArea',
      // 评价状态
      evaluationStatus: 'unevaluated',
      // 初始评分
      initialRate: 0,
      // 初始评价内容
      initialText: '',
      // 初始图片列表
      initialImgList: [],
      // 评价时间
      initialTime: '',
      // 显示成功提示
      showSuccessTip: false,
      // 评价ID（如果是查看已有评价）
      evaluationId: '',
      // 业务ID（如服务区ID，收费站ID等）
      businessId: '',
      // 业务信息
      businessInfo: {
        name: '',
        detail: '',
        time: ''
      },
      // 已选择的标签
      selectedTags: [],
      // 服务区评价项目
      serviceAreaItems: [],
      // 收费站评价项目
      tollStationItems: [],
      // 充电服务评价项目
      chargingItems: [],
      // 用户头像URL
      userAvatarUrl: '',
      // 用户昵称
      userNickname: '',
      // 默认头像
      defaultAvatar: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/guide.png',
      // 用户信息弹窗可见性
      userInfoDialogVisible: false,
      // 待提交的评价结果
      pendingSubmitResult: null,
      // 用户信息是否已确认过
      userInfoConfirmed: false
    };
  },
  computed: {
    // 根据评价类型返回页面标题
    pageTitle() {
      return evaluationUtils.getEvaluationTitle(this.evaluationType);
    },
    
    // 根据评价类型和评分获取评价标签
    evaluationTags() {
      return evaluationUtils.getEvaluationTagsByType(this.evaluationType, this.initialRate);
    }
  },
  created() {
    // 初始化各评价类型的评价项目
    this.serviceAreaItems = evaluationUtils.getInitialItemsByType('serviceArea');
    this.tollStationItems = evaluationUtils.getInitialItemsByType('tollStation');
    this.chargingItems = evaluationUtils.getInitialItemsByType('chargingService');
  },
  onLoad(options) {
    // 初始化页面
    this.initPageSettings();
    this.parseRouteParams(options);
    this.initUserInfo();
    this.checkAndShowUserDialog();
  },
  methods: {
    // 初始化页面基础设置
    initPageSettings() {
      uni.setNavigationBarTitle({
        title: this.pageTitle
      });
    },
    
    // 解析路由参数
    parseRouteParams(options) {
      try {
        // 设置评价类型
        if (options.type) {
          this.evaluationType = options.type;
          // 更新页面标题
          uni.setNavigationBarTitle({
            title: this.pageTitle
          });
        }
        
        // 获取评价ID（如果是查看已有评价）
        if (options.id) {
          this.evaluationId = options.id;
          this.getEvaluationDetail();
        }
        
        // 获取业务ID
        if (options.businessId) {
          this.businessId = options.businessId;
          this.getBusinessInfo();
        }
        
        // 获取业务名称
        if (options.businessName) {
          this.businessInfo.name = decodeURIComponent(options.businessName);
        }
        
        // 获取初始状态
        if (options.status) {
          this.evaluationStatus = options.status;
        }
      } catch (error) {
        // 解析路由参数失败，使用默认值
      }
    },
    
    // 初始化用户信息
    initUserInfo() {
      // 设置默认头像
      this.userAvatarUrl = this.defaultAvatar;
      
      try {
        const userInfo = getLoginUserInfo();
        if (!userInfo) return;
        
        // 获取用户昵称（按优先级）
        this.userNickname = userInfo.nickname || userInfo.nickName || userInfo.userName || '';
        
        // 获取用户头像
        if (userInfo.avatarUrl && userInfo.avatarUrl !== this.defaultAvatar) {
          this.userAvatarUrl = userInfo.avatarUrl;
        }
        
        // 检查用户信息确认状态
        if (userInfo.hasConfirmedUserInfo) {
          this.userInfoConfirmed = true;
        }
      } catch (error) {
        // 初始化用户信息失败，使用默认值
      }
    },
    
    // 检查并显示用户信息弹框
    checkAndShowUserDialog() {
      if (!this.userInfoConfirmed && !this.userNickname) {
        this.$nextTick(() => {
          this.openUserInfoDialog();
        });
      }
    },
    // 返回上一页
    goBack() {
      // 设置全局刷新标记，通知评论列表页面需要刷新
      const app = getApp();
      if (app.globalData) {
        app.globalData.needRefreshCommentList = true;
      } else {
        app.globalData = { needRefreshCommentList: true };
      }
      
      uni.navigateBack();
    },
    
    // 获取业务信息
    getBusinessInfo() {
      // 根据评价类型设置不同的业务信息
      switch (this.evaluationType) {
        case 'serviceArea':
          this.businessInfo = {
            name: this.businessInfo.name || '服务区'
          };
          break;
        case 'tollStation':
          this.businessInfo = {
            name: this.businessInfo.name || '收费站'
          };
          break;
        case 'chargingService':
          this.businessInfo = {
            name: this.businessInfo.name || '充电站'
          };
          break;
        default:
          break;
      }
    },
    
    // 获取评价详情
    getEvaluationDetail() {
      // 这里应该调用API获取评价详情
      // TODO: 实现真实的API调用来获取评价详情
    },
    
    // 处理评价状态变化
    handleStatusChange(status) {
      this.evaluationStatus = status;
      
      // 当状态变为"评价中"时，检查用户信息是否完整，如未完整则稍后弹窗
      if (status === 'evaluating') {
        // 如果还没有头像和昵称，稍后会在提交时弹窗
        // 暂时不弹窗，避免用户刚开始评价就被弹窗打断体验
      }
    },
    
    // 处理评分变化
    handleRateChange(rate) {
      this.initialRate = rate;
    },
    
    // 更新选中的标签
    updateSelectedTags(tags) {
      this.selectedTags = tags;
    },
    
    // 处理服务区评价项目变化
    handleServiceItemChange({ index, value, items }) {
      this.serviceAreaItems = items;
    },
    
    // 处理收费站评价项目变化
    handleTollItemChange({ index, value, items }) {
      this.tollStationItems = items;
    },
    
    // 处理充电服务评价项目变化
    handleChargingItemChange({ index, value, items }) {
      this.chargingItems = items;
    },
    
    
    // 处理评价提交
    handleSubmit(result) {
      // 如果用户已经确认过信息，或者有昵称，直接提交
      if (this.userInfoConfirmed || this.userNickname) {
        this.submitEvaluation(result);
        return;
      }
      
      // 如果用户未确认过信息且没有昵称，弹出用户信息弹窗
      this.openUserInfoDialog();
      
      // 保存评价结果，等待用户信息填写完成后再提交
      this.pendingSubmitResult = result;
    },
    
    // 转换前端数据为API参数格式
    transformToApiParams(result) {
      // 参数验证
      const openid = this.getUserOpenid();
      if (!openid) throw new Error('无法获取用户标识，请重新登录');
      if (!this.businessId) throw new Error('缺少业务ID参数');
      if (!result.text?.trim()) throw new Error('评价内容不能为空');
      if (!result.rate || result.rate < 1 || result.rate > 5) throw new Error('请选择有效的评分');
      
      // 基础参数
      const baseParams = {
        content: result.text.trim(),
        star: parseInt(result.rate),
        image: Array.isArray(result.urlList) ? result.urlList.join(',') : '',
        openid
      };
      
      // 类型参数映射
      const typeParamsMap = {
        serviceArea: { ccgxServiceAreaId: this.businessId, ccgxServiceAreaName: this.businessInfo.name || '' },
        chargingService: { chargeStationId: this.businessId, chargeStationName: this.businessInfo.name || '' },
        tollStation: { ccgxTollStationId: this.businessId, ccgxTollStationName: this.businessInfo.name || '' }
      };
      
      const typeParams = typeParamsMap[this.evaluationType];
      if (!typeParams) throw new Error('未知的评价类型: ' + this.evaluationType);
      
      return { ...baseParams, ...typeParams };
    },
    
    // 获取用户openid
    getUserOpenid() {
      // 尝试多种方式获取openid
      let openid = getOpenid();
      if (!openid) {
        openid = getOpenidForRead();
      }
      
      // 如果还是没有，尝试从用户信息中获取
      if (!openid) {
        const userInfo = getLoginUserInfo();
        openid = userInfo?.openid || '';
      }
      
      return openid;
    },
    
    // 提交评价
    async submitEvaluation(result) {

      let methodMap = {
        serviceArea: 'addServiceAreaEvaluation',
        chargingService: 'addChargingStationEvaluation',
        tollStation: 'addTollStationEvaluation'
      }

      try {
        // 显示加载状态
        uni.showLoading({
          title: '提交评价中...'
        });
        
        // 获取API方法名
        const apiMethod = methodMap[this.evaluationType]
        if (!apiMethod) {
          throw new Error('不支持的评价类型: ' + this.evaluationType);
        }
        
        // 转换参数格式
        const apiParams = this.transformToApiParams(result);
        
        // 调用API提交评价
        const response = await this.$request.post(this.$interfaces[apiMethod], {
          data: apiParams
        });
        
        if (response.code == 200) {
          // 提交成功 - 通知评价组件完成评价
          if (this.$refs.evaluationComponent) {
            this.$refs.evaluationComponent.completeEvaluation();
          }
          
          this.showSuccessTip = true;
          this.pendingSubmitResult = null;
          
          uni.showToast({
            title: '评价提交成功',
            icon: 'success'
          });
        } else {
          uni.hideLoading();
          // API返回失败 - 重置评价组件提交状态
          if (this.$refs.evaluationComponent) {
            this.$refs.evaluationComponent.resetSubmitStatus();
          }
          uni.showToast({
            title: response.msg || '评价提交失败',
            icon: 'none'
          }); 
        }
      } catch (error) {
        // API调用失败 - 重置评价组件提交状态
        if (this.$refs.evaluationComponent) {
          this.$refs.evaluationComponent.resetSubmitStatus();
        }
        uni.hideLoading();
        
        uni.showToast({
          title: error.message,
          icon: 'none',
          duration: 3000
        });
      }
    },

    
    // 打开用户信息弹窗
    openUserInfoDialog() {
      this.userInfoDialogVisible = true;
    },
    
    // 关闭用户信息弹窗
    closeUserInfoDialog() {
      // 允许用户关闭弹窗，不再强制要求填写信息
      this.userInfoDialogVisible = false;
      
      // 如果有待提交的评价，但用户取消了填写信息，则使用默认值
      if (this.pendingSubmitResult) {
        const defaultNickname = this.userNickname || "匿名用户";
        
        // 更新用户信息（使用已有信息或默认值）
        const userInfo = getLoginUserInfo() || {};
        if (!userInfo.nickname) {
          userInfo.nickname = defaultNickname;
        }
        setLoginUserInfo(userInfo);
        
        // 提交评价
        this.submitEvaluation(this.pendingSubmitResult);
      }
    },
    
    // 处理用户头像变化
    onUserAvatarChange(avatarUrl) {
      this.userAvatarUrl = avatarUrl;
    },
    
    // 处理用户昵称变化
    onUserNicknameChange(nickname) {
      this.userNickname = nickname;
    },
    
    // 处理用户信息确认
    onUserInfoConfirm({ avatarUrl, nickname }) {
      // 更新用户信息
      this.userAvatarUrl = avatarUrl;
      this.userNickname = nickname;
      this.userInfoConfirmed = true;
      
      // 持久化存储用户信息
      const userInfo = getLoginUserInfo() || {};
      userInfo.avatarUrl = avatarUrl;
      userInfo.nickname = nickname;
      userInfo.hasConfirmedUserInfo = true; // 添加标记表示用户已确认过信息
      setLoginUserInfo(userInfo);
      
      // 如果有待提交的评价，提交它
      if (this.pendingSubmitResult) {
        this.submitEvaluation(this.pendingSubmitResult);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.evaluation-page {
  min-height: 100vh;
  background-color: #F5F5F5;
  ::v-deep .evaluation-box{
    border-radius: 0rpx 0rpx 8rpx 8rpx ;
  }
}

.evaluation-content {
  padding: 20rpx;
  padding-top: 20rpx;
}

.business-info {
  background-color: #FFFFFF;
  border-radius: 8rpx 8rpx 0rpx 0rpx;
  padding: 24rpx 32rpx;
  margin-top: 20rpx;
}

.business-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  position: relative;
  padding-left: 20rpx;
}

.business-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background-color: #0066E9;
  border-radius: 3rpx;
}

.business-detail {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
}

.business-time {
  font-size: 24rpx;
  color: #999999;
  padding-left: 20rpx;
}

.evaluation-details {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.detail-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 24rpx;
}

.extra-items {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.view-more {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #0066E9;
}

.success-tip {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 50rpx 40rpx;
  margin-top: 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
}

.success-icon {
  margin-bottom: 30rpx;
}

.success-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.success-subtext {
  font-size: 28rpx;
  color: #818181;
  margin-bottom: 40rpx;
}

.back-btn {
  background-color: #0066E9;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  margin-top: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 233, 0.2);
  transition: all 0.3s;
}

.back-btn:active {
  transform: scale(0.98);
  background-color: #0055cc;
}
</style>
