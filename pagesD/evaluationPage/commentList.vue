<template>
  <view class="comment-list-page">
    <template v-if="isDisplayEnabled">
      <!-- 固定头部区域 -->
      <view class="comment-list-header">
        <view class="business-name">{{ businessInfo.name }}</view>
        <!-- <view class="comments-count">{{commentList.length}}条评价</view> -->
      </view>

      <!-- 可滚动的评论列表区域 -->
      <view class="scroll-box">
        <scroll-view scroll-y="true" class="scroll-Y" :scroll-top="scrollTop" :lower-threshold='lowerThreshold'
          @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
          <view class="comment-list">
            <block v-if="commentList.length > 0">
              <view class="comment-item" v-for="(comment, index) in commentList" :key="index">
                <!-- 用户信息 -->
                <view class="comment-user">
                  <view class="user-avatar">
                    <image :src="comment.avatar || defaultAvatar" mode="aspectFill" />
                  </view>
                  <view class="user-info">
                    <view class="user-name">
                      {{ comment.isOfficial == 1 ? '桂小通' : comment.userName || '匿名用户' }}
                      <text class="user-tag top" v-if="comment.isTop">置顶</text>
                      <text class="user-tag self" v-if="comment.isCurrentUser">自己</text>
                      <text class="user-tag official" v-if="comment.isOfficial === 1">平台官方人员</text>
                    </view>
                    <view class="comment-time">
                      <text>发布于 {{ comment.time }}</text>

                      <!-- 回复按钮：只能回复属于自己评论的官方回复 -->
                      <text class="reply-text-btn"
                        v-if="isReviewEnabled && comment.isCurrentUser && hasFirstOfficialReply(comment)"
                        @tap.stop="handleReply(comment)">
                        回复
                      </text>
                    </view>
                  </view>
                </view>

                <!-- 评价内容 -->
                <view class="comment-content">
                  <!-- 评价文本 -->
                  <view class="comment-text">{{ comment.text }}</view>

                  <!-- 星级评分和满意度文本 -->
                  <view class="rating-container">
                    <t-star-rating :value="comment.rate" :readonly="true" :size="24" />
                  </view>

                  <!-- 图片 -->
                  <view class="comment-images" v-if="comment.images && comment.images.length > 0">
                    <view class="image-item" v-for="(image, imgIndex) in comment.images" :key="imgIndex"
                      @tap="previewImage(comment.images, image)">
                      <image :src="image" mode="aspectFill" />
                    </view>
                  </view>

                  <!-- 官方回复 -->
                  <view class="reply-container" v-if="comment.replies && comment.replies.length > 0">
                    <!-- 显示最多3条回复 -->
                    <view v-for="(reply, replyIndex) in getLimitedReplies(comment)" :key="replyIndex"
                      class="reply-item">
                      <view class="reply-user-avatar" :class="{ 'official-avatar': reply.isOfficial == 1 }">
                        <image v-if="reply.isOfficial == 1"
                          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/gxt_logo.png"
                          mode="aspectFill" />
                        <image v-else :src="reply.avatar || defaultAvatar" mode="aspectFill" />
                      </view>
                      <view class="reply-content-wrapper">
                        <view class="reply-header">
                          <view class="reply-title" :class="{ 'official': reply.isOfficial == 1 }">
                            {{ reply.isOfficial == 1 ? '桂小通' : reply.userName }}
                            <text class="user-tag official" v-if="reply.isOfficial == 1">平台官方人员</text>
                          </view>
                          <view class="reply-time" v-if="reply.time">
                            <text>{{ reply.time }}</text>
                          </view>
                        </view>
                        <view class="reply-content">{{ reply.content }}</view>

                        <!-- 子回复图片 -->
                        <view class="reply-images" v-if="reply.images && reply.images.length > 0">
                          <view class="reply-image-item" v-for="(image, imgIndex) in reply.images" :key="imgIndex"
                            @tap="previewImage(reply.images, image)">
                            <image :src="image" mode="aspectFill" />
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>

                  <!-- 展开更多评价内容按钮 -->
                  <view class="actions-container">
                    <view class="expand-btn-wrapper" v-if="hasMoreReplies(comment) || comment.hasDetails"
                      @tap="navigateToDetail(comment.id)">
                      <view class="expand-btn">
                        <text>展开对话</text>
                      </view>
                    </view>

                    <!-- 操作按钮 -->
                    <view class="comment-actions" v-if="isReviewEnabled">
                      <view class="action-btn delete-btn" v-if="comment.isCurrentUser"
                        @tap.stop="confirmDeleteComment(comment)">
                        <text>删除</text>
                        <uni-icons type="trash" size="18" color="#F65B5B"></uni-icons>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </block>

            <!-- 空状态 -->
            <view class="empty-state" v-else>
              <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/no_data.png"
                mode="aspectFill" />
              <view class="empty-text">暂无评价~</view>
            </view>
          </view>
        </scroll-view>
      </view>
    </template>

    <!-- 评价未开启的空状态 -->
    <view class="empty-state" v-else>
      <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/no_data.png"
        mode="aspectFill" />
      <view class="empty-text">该评价暂未开启</view>
    </view>

    <!-- 添加悬浮评价按钮 -->
    <movable-area class="movable-area" v-if="isReviewEnabled">
      <movable-view class="movable-view" :x="x" :y="y" direction="all" @change="onChange">
        <view class="floating-btn" @tap="goToAddEvaluation">
          <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/eva_comm.png"
            mode="aspectFit"></image>
        </view>
      </movable-view>
    </movable-area>

    <!-- 使用新的评价详情与回复组件 -->
    <t-comment-dialog :visible="dialogVisible" :comment="currentComment" :dialogList="commentDialogList"
      :defaultAvatar="defaultAvatar" :start-reply="startReplyFlow" @close="closeCommentDialog"
      @reply-successful="handleReplySuccessful" :evaluation-type="evaluationType" :business-id="businessId" />

    <!-- 用户信息授权弹窗 -->
    <t-user-info-dialog :visible="isUserInfoDialogVisible" @close="closeUserInfoDialog"
      @confirm="handleUserInfoConfirm" />
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue';
import TStarRating from '@/components/common/t-star-rating.vue';
import TEvaluationTags from '@/components/common/t-evaluation-tags.vue';
import TCommentDialog from './components/t-comment-dialog.vue';
import TUserInfoDialog from './components/t-user-info-dialog.vue';
import evaluationUtils from '@/pagesD/utils/evaluationUtils';
import { getLoginUserInfo, getOpenid, getOpenidForRead, getWechatUserInfo, setWechatUserInfo } from "@/common/storageUtil.js";

export default {
  components: {
    uniIcons,
    TStarRating,
    TEvaluationTags,
    TCommentDialog,
    TUserInfoDialog
  },
  data() {
    return {
      evaluationType: 'serviceArea',
      businessId: '',
      businessInfo: {
        name: '高岭服务区',
        detail: '南宁方向',
        time: '2023/05/20 14:30'
      },
      defaultAvatar: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/guide.png',
      commentList: [],
      // 分页相关数据
      page_num: 1, // 当前页码
      page_size: 10, // 每页大小
      flag: false, // 是否已加载所有数据
      windowHeight: 0, // 窗口高度
      scrollTop: 0, // 滚动位置
      lowerThreshold: 120, // 触发滚动到底部的阈值
      // 弹出层相关数据
      dialogVisible: false, // 评价详情弹出层是否可见
      currentComment: null, // 当前查看的评价
      commentDialogList: [], // 评价详情对话列表
      currentUserId: '', // 当前登录用户ID
      businessName: '', // 业务名称
      isDisplayEnabled: false, // 是否开启评价展示
      isReviewEnabled: false, // 是否开启评价功能
      isUserInfoDialogVisible: false, // 控制用户信息弹窗
      wechatUserInfo: null, // 存储微信用户信息
      startReplyFlow: false, // 是否直接启动回复流程
      isReplySubmitting: false,
      x: 300,
      y: 500,
      old: {
        x: 0,
        y: 0,
        scrollTop: 0 // 记录旧的滚动位置
      },
      // 刷新控制相关
      lastRefreshTime: 0, // 上次刷新时间
      pageStackLength: 0, // 页面栈长度
      needRefresh: false // 是否需要刷新
    };
  },
  computed: {
    // 根据评价类型返回页面标题
    pageTitle() {
      return evaluationUtils.getEvaluationTitle(this.evaluationType) + "列表";
    },
    isIos() {
      return uni.getSystemInfoSync().platform === "ios";
    }
  },
  created() {
    // 确保padStart方法可用（兼容性处理）
    this.ensurePadStartPolyfill();
  },
  async onLoad(options) {
    // 设置页面标题（使用小程序自带头部title）
    uni.setNavigationBarTitle({
      title: this.pageTitle
    });

    // 从路由参数获取评价类型和ID
    if (options.type) {
      this.evaluationType = options.type;
      // 更新页面标题
      uni.setNavigationBarTitle({
        title: this.pageTitle
      });
    }

    this.businessId = options.businessId;
    this.businessName = options.businessName;

    this.getBusinessInfo();
  },
  async onShow() {
    // 检查是否需要刷新数据
    if (this.shouldRefreshData()) {
      // 先获取评价配置
      try {
        await this.getEvaluationConfig();

        // 如果允许展示，则获取评论列表
        if (this.isDisplayEnabled) {
          // 重置分页状态
          this.resetPagination();
          this.$nextTick(() => {
            this.getCommentList();
          });
        }
      } catch (error) {
        console.error('获取评价配置失败:', error);
        // 配置获取失败时，设置默认状态
        this.isDisplayEnabled = false;
        this.isReviewEnabled = false;
      }
    }
  },
  mounted() {
    const info = uni.getSystemInfoSync();
    this.x = info.windowWidth - uni.upx2px(34 + 88); // Replicates right: 34rpx, width: 88rpx
    this.y = info.windowHeight - uni.upx2px(344 + 88); // Replicates bottom: 344rpx, height: 88rpx
    this.windowHeight = info.windowHeight;

    // 初始化页面栈长度
    this.pageStackLength = getCurrentPages().length;
  },
  methods: {
    // 判断是否需要刷新数据
    shouldRefreshData() {
      const currentTime = Date.now();
      const currentPageStackLength = getCurrentPages().length;

      // 获取全局刷新标记
      const app = getApp();
      const globalNeedRefresh = app.globalData?.needRefreshCommentList || false;

      // 情况1：页面首次显示（评论列表为空且上次刷新时间为0）
      if (this.commentList.length === 0 && this.lastRefreshTime === 0) {
        this.updateRefreshState(currentTime, currentPageStackLength);
        return true;
      }

      // 情况2：从子页面返回（页面栈长度减少）
      if (currentPageStackLength < this.pageStackLength) {
        this.updateRefreshState(currentTime, currentPageStackLength);
        return true;
      }

      // 情况3：有全局刷新标记
      if (globalNeedRefresh) {
        // 清除全局标记
        if (app.globalData) {
          app.globalData.needRefreshCommentList = false;
        }
        this.updateRefreshState(currentTime, currentPageStackLength);
        return true;
      }

      // 情况4：距离上次刷新超过30秒且页面栈长度变化
      if (currentTime - this.lastRefreshTime > 30000 && currentPageStackLength !== this.pageStackLength) {
        this.updateRefreshState(currentTime, currentPageStackLength);
        return true;
      }

      // 更新页面栈长度但不刷新
      this.pageStackLength = currentPageStackLength;

      // 其他情况不刷新（如图片预览返回）
      return false;
    },

    // 更新刷新状态
    updateRefreshState(currentTime, currentPageStackLength) {
      this.lastRefreshTime = currentTime;
      this.pageStackLength = currentPageStackLength;
    },

    // 手动刷新数据（提供给外部调用或备用）
    async manualRefresh() {
      try {
        await this.getEvaluationConfig();

        if (this.isDisplayEnabled) {
          this.resetPagination();
          this.$nextTick(() => {
            this.getCommentList();
          });
        }

        // 更新刷新状态
        this.updateRefreshState(Date.now(), getCurrentPages().length);
      } catch (error) {
        console.error('手动刷新失败:', error);
        uni.showToast({
          title: '刷新失败，请重试',
          icon: 'none'
        });
      }
    },

    // 确保padStart方法可用的polyfill
    ensurePadStartPolyfill() {
      if (!String.prototype.padStart) {
        String.prototype.padStart = function (targetLength, padString = ' ') {
          const str = String(this);
          if (str.length >= targetLength) {
            return str;
          }
          const pad = String(padString);
          let padLength = targetLength - str.length;
          let result = '';
          while (padLength > 0) {
            if (padLength >= pad.length) {
              result += pad;
              padLength -= pad.length;
            } else {
              result += pad.slice(0, padLength);
              padLength = 0;
            }
          }
          return result + str;
        };
      }
    },

    onChange: function (e) {
      this.old.x = e.detail.x;
      this.old.y = e.detail.y;
    },

    // 滚动相关事件处理
    upper: function (e) {
      // 滚动到顶部时的处理
    },

    scroll: function (e) {
      this.old.scrollTop = e.detail.scrollTop;
    },

    scrolltolower: function (e) {
      // 避免重复触发
      if (this.flag) return;
      let self = this;

      setTimeout(function () {
        self.page_num = self.page_num + 1;
        self.getCommentList();
      }, 200);
    },

    // 重置分页状态
    resetPagination() {
      this.page_num = 1;
      this.flag = false;
      this.commentList = [];
    },
    // 获取用户openid
    getUserOpenid() {
      // 尝试多种方式获取openid
      let openid = getOpenid();
      if (!openid) {
        openid = getOpenidForRead();
      }

      // 如果还是没有，尝试从用户信息中获取
      if (!openid) {
        const userInfo = getLoginUserInfo();
        openid = userInfo?.openid || '';
      }

      return openid;
    },

    // 获取满意度文本
    getSatisfactionText(rate) {
      return evaluationUtils.getSatisfactionText(rate);
    },

    // 获取业务信息
    getBusinessInfo() {
      // 根据评价类型设置不同的业务信息
      switch (this.evaluationType) {
        case 'serviceArea':
          this.businessInfo = {
            name: this.businessName
          };
          break;
        case 'tollStation':
          this.businessInfo = {
            name: this.businessName
          };
          break;
        case 'chargingService':
          this.businessInfo = {
            name: this.businessName
          };
          break;
        default:
          break;
      }
    },

    // 获取评价配置
    async getEvaluationConfig() {
      try {
        const params = {
          facilityId: this.businessId
        };

        // 确保接口存在
        if (!this.$request || !this.$interfaces || !this.$interfaces.getEvaluationConfig) {
          console.warn('评价配置接口不存在，使用默认配置');
          this.isDisplayEnabled = false;
          this.isReviewEnabled = false;
          return;
        }

        const response = await this.$request.post(this.$interfaces.getEvaluationConfig, {
          data: params
        });
        console.log(response, '获取评价配置');
        if (response.code == 200 && response.data) {
          this.isDisplayEnabled = response.data.isDisplayEnabled == 1;
          this.isReviewEnabled = response.data.isReviewEnabled == 1;
        } else {
          // API 异常，默认不显示/不开启
          this.isDisplayEnabled = false;
          this.isReviewEnabled = false;
          uni.showToast({
            title: response.msg || '获取评价配置失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取评价配置网络异常:', error);
        this.isDisplayEnabled = false;
        this.isReviewEnabled = false;
        uni.showToast({
          title: '网络异常，获取评价配置失败',
          icon: 'none'
        });
        // 重新抛出错误，让调用者可以捕获
        throw error;
      }
    },

    // 获取评论列表
    async getCommentList() {
      try {
        // 第一页时显示加载提示
        if (this.page_num === 1) {
          uni.showLoading({
            title: '加载评价中...'
          });
        }

        // 根据评价类型调用不同的API
        const apiMethodMap = {
          serviceArea: 'getServiceAreaEvaluationList',
          chargingService: 'getChargingStationEvaluationList',
          tollStation: 'getTollStationEvaluationList'
        };

        let apiMethod = apiMethodMap[this.evaluationType];
        let params = {
          facilityId: this.businessId,
          openid: this.getUserOpenid(),
          // 添加分页参数
          current: this.page_num,
          size: this.page_size
        };

        if (!apiMethod) {
          // API方法不存在，显示空列表
          if (this.page_num === 1) {
            this.commentList = [];
            uni.showToast({
              title: '评价类型暂不支持',
              icon: 'none'
            });
          }
          this.flag = true;
          if (this.page_num === 1) uni.hideLoading();
          return;
        }

        // 调用API
        let response = await this.$request.post(this.$interfaces[apiMethod], {
          data: params
        });


        if (response.code == 200 && response.data) {
          // 处理分页响应数据
          let result = response.data.records || [];

          if (result.length > 0) {
            // 转换API响应数据为前端格式
            const transformedData = this.transformApiDataToComments(result);

            if (this.page_num === 1) {
              // 第一页，直接赋值
              this.commentList = transformedData;
            } else {
              // 非第一页，追加数据
              this.commentList = this.commentList.concat(transformedData);
            }

            // 检查是否还有更多数据
            if (this.commentList.length >= response.data.total || result.length < this.page_size) {
              this.flag = true;
            }
          } else {
            // 没有数据
            if (this.page_num === 1) {
              this.commentList = [];
            }
            this.flag = true;
          }
        } else {
          // API异常处理
          if (this.page_num === 1) {
            // 第一页加载失败，显示空列表
            this.commentList = [];
            uni.showToast({
              title: response.msg || '加载失败',
              icon: 'none'
            });
          } else {
            // 非第一页加载失败，恢复页码
            this.page_num = this.page_num - 1;
            uni.showToast({
              title: response.msg || '加载失败',
              icon: 'none'
            });
          }
        }

      } catch (error) {
        // 网络错误处理
        if (this.page_num === 1) {
          // 第一页网络错误，显示空列表
          this.commentList = [];
          uni.showToast({
            title: '网络异常',
            icon: 'none'
          });
        } else {
          // 非第一页网络错误，恢复页码
          this.page_num = this.page_num - 1;
          uni.showToast({
            title: '网络异常',
            icon: 'none'
          });
        }
      } finally {
        if (this.page_num === 1) {
          uni.hideLoading();
        }
      }
    },

    // 转换API响应数据为前端评论格式
    transformApiDataToComments(apiData) {
      if (!Array.isArray(apiData)) {
        return [];
      }

      // 获取当前用户openid用于比较
      const currentOpenid = this.getUserOpenid();

      const transformedData = apiData.map(item => {
        // 基础评论信息
        const comment = {
          id: item.id ? item.id.toString() : Math.random().toString(),
          userName: item.nickname || '匿名用户',
          // 增强avatar字段处理，优先使用uploadedAvatarId，然后是avatarUrl，最后是默认头像
          avatar: item.uploadedAvatarId || item.avatarUrl || item.avatar || this.defaultAvatar,
          userId: item.openid || '', // 用于判断是否为当前用户的评论
          time: this.formatApiTime(item.reviewsTime || item.createTime),
          rate: item.star || 0,
          text: item.content || '',
          tags: this.parseImageToTags(item.image), // 将image字段解析为标签（如果有的话）
          images: this.parseImageToArray(item.image),
          isOfficial: item.isOfficial || 0, // 是否为官方人员，1表示官方，0表示非官方
          hasDetails: false,
          replies: [],
          // 根据openid判断是否为当前用户评论
          isCurrentUser: currentOpenid && item.openid === currentOpenid,
          // 添加置顶状态（当前用户评论自动置顶）
          isTop: item.isTop == 1
        };

        // 处理子评论
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          comment.replies = this.flattenReplies(item.children, currentOpenid);

          // 如果有超过3条回复，标记为有更多详情
          if (comment.replies.length > 3) {
            comment.hasDetails = true;
          }
        }

        return comment;
      });

      // 前端排序逻辑已注释 - 现在由后端接口进行排序
      // 对评论进行排序：当前用户评论置顶
      // 注意：在分页情况下，只对当前页数据进行排序，不影响整体分页逻辑
      // return transformedData.sort((a, b) => {
      //   // 仅在第一页时进行用户评论置顶处理
      //   if (this.page_num === 1) {
      //     // 当前用户评论优先显示
      //     if (a.isCurrentUser && !b.isCurrentUser) return -1;
      //     if (!a.isCurrentUser && b.isCurrentUser) return 1;
      //   }

      //   // 其他评论按时间降序排列（新的在前）
      //   const dateA = this.parseCompatibleDate(a.time);
      //   const dateB = this.parseCompatibleDate(b.time);
      //   const timeA = dateA && !isNaN(dateA.getTime()) ? dateA.getTime() : 0;
      //   const timeB = dateB && !isNaN(dateB.getTime()) ? dateB.getTime() : 0;
      //   return timeB - timeA;
      // });

      // 直接返回后端排序后的数据，不进行前端排序
      return transformedData;
    },

    // 递归扁平化所有层级的回复
    flattenReplies(children, currentOpenid, allReplies = []) {
      if (!children || !Array.isArray(children)) {
        return allReplies;
      }

      children.forEach(child => {
        // 添加当前子评论到回复列表
        allReplies.push({
          id: child.id ? child.id.toString() : Math.random().toString(),
          userName: child.nickname || '匿名用户',
          // 增强avatar字段处理，优先使用uploadedAvatarId，然后是avatarUrl，最后是默认头像
          avatar: child.uploadedAvatarId || child.avatarUrl || child.avatar || this.defaultAvatar,
          isOfficial: child.isOfficial || 0, // 是否为官方人员，1表示官方，0表示非官方
          time: this.formatApiTime(child.reviewsTime || child.createTime),
          content: child.content || '',
          images: this.parseImageToArray(child.image), // 添加图片解析
          isCurrentUser: currentOpenid && child.openid === currentOpenid,
          // 保存原始时间用于排序
          originalTime: child.reviewsTime || child.createTime,
        });

        // 如果当前子评论还有子评论，则递归处理
        if (child.children && child.children.length > 0) {
          this.flattenReplies(child.children, currentOpenid, allReplies);
        }
      });

      // 对扁平化后的回复按时间倒序排列（最新的在前面）
      return allReplies.sort((a, b) => {
        const timeA = this.parseCompatibleDate(a.originalTime);
        const timeB = this.parseCompatibleDate(b.originalTime);

        // 如果时间解析失败，使用0作为默认值
        const timestampA = timeA && !isNaN(timeA.getTime()) ? timeA.getTime() : 0;
        const timestampB = timeB && !isNaN(timeB.getTime()) ? timeB.getTime() : 0;

        // 倒序排列：最新的时间在前面
        return timestampB - timestampA;
      });
    },

    // iOS兼容的日期解析函数
    parseCompatibleDate(dateStr) {
      if (!dateStr) return null;

      try {
        // 如果是时间戳，直接使用
        if (typeof dateStr === 'number' || /^\d+$/.test(dateStr)) {
          const timestamp = parseInt(dateStr);
          // 判断是秒还是毫秒时间戳
          const date = new Date(timestamp.toString().length === 10 ? timestamp * 1000 : timestamp);
          return isNaN(date.getTime()) ? null : date;
        }

        let str = dateStr.toString().trim();

        // 处理常见的日期格式，转换为iOS兼容格式
        // 将 "2025-06-11 15:36" 格式转换为 "2025/06/11 15:36"
        if (/^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}(?::\d{2})?$/.test(str)) {
          str = str.replace(/(\d{4})-(\d{2})-(\d{2})/, '$1/$2/$3');
        }

        // 将 "2025-06-11T15:36:00" 格式转换为标准格式
        if (str.includes('T')) {
          const parts = str.split('T');
          if (parts.length === 2) {
            const datePart = parts[0];
            let timePart = parts[1];
            // 移除毫秒和时区信息
            timePart = timePart.replace(/\.\d+/, '').replace(/Z$/, '').replace(/[+-]\d{2}:?\d{2}$/, '');
            str = `${datePart.replace(/-/g, '/')} ${timePart}`;
          }
        }

        // 处理其他可能的格式 - 将所有的 "-" 替换为 "/"，因为iOS Safari对 "-" 格式支持不好
        str = str.replace(/-/g, '/');

        // 创建Date对象
        const date = new Date(str);

        // 验证日期是否有效
        return isNaN(date.getTime()) ? null : date;
      } catch (error) {
        console.warn('日期解析失败:', dateStr, error);
        return null;
      }
    },

    // 格式化API时间为显示格式
    formatApiTime(apiTime) {
      if (!apiTime) return '';

      try {
        // 如果已经是正确格式的时间字符串，直接返回
        if (typeof apiTime === 'string' && /^\d{4}\/\d{2}\/\d{2}\s\d{2}:\d{2}$/.test(apiTime)) {
          return apiTime;
        }

        // 使用兼容的日期解析函数
        const date = this.parseCompatibleDate(apiTime);
        if (!date || isNaN(date.getTime())) {
          console.warn('时间格式化失败:', apiTime);
          return ''; // 返回空字符串而不是原值，避免显示错误格式
        }

        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const hour = date.getHours();
        const minute = date.getMinutes();

        // 验证所有值都是有效数字
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(hour) || isNaN(minute)) {
          console.warn('时间组件包含NaN:', { year, month, day, hour, minute });
          return '';
        }

        // 使用padStart进行格式化，确保两位数显示
        const formattedMonth = month.toString().padStart(2, '0');
        const formattedDay = day.toString().padStart(2, '0');
        const formattedHour = hour.toString().padStart(2, '0');
        const formattedMinute = minute.toString().padStart(2, '0');

        return `${year}/${formattedMonth}/${formattedDay} ${formattedHour}:${formattedMinute}`;
      } catch (error) {
        console.warn('时间格式化异常:', apiTime, error);
        return '';
      }
    },

    // 解析image字段为图片数组
    parseImageToArray(imageStr) {
      if (!imageStr || typeof imageStr !== 'string') {
        return [];
      }

      // 假设图片用逗号分隔
      return imageStr.split(',').filter(img => img.trim()).map(img => img.trim());
    },

    // 解析image字段为标签（临时处理，实际应该有专门的标签字段）
    parseImageToTags(imageStr) {
      // 这里是临时处理，实际项目中标签应该有专门的字段
      // 可以根据实际需求进行调整
      return [];
    },





    // 预览图片
    previewImage(urls, current) {
      uni.previewImage({
        urls: urls,
        current: current
      });
    },

    // 导航到评价详情页
    navigateToDetail(commentId) {
      const comment = this.commentList.find(item => item.id === commentId);
      if (!comment) return;

      // 显示评价详情弹出层
      this.showCommentDialog(comment);
    },

    // 显示评价详情弹出层
    showCommentDialog(comment) {
      // 设置当前评价
      this.currentComment = JSON.parse(JSON.stringify(comment));

      // 获取评价详情和对话历史
      this.getCommentDetail(comment.id);

      // 显示弹出层
      this.dialogVisible = true;
    },

    // 获取评价详情和对话历史
    getCommentDetail(commentId) {
      // 初始化对话列表
      this.commentDialogList = [];

      // 添加所有回复
      if (this.currentComment.replies && this.currentComment.replies.length > 0) {
        // 如果有replies数组，添加所有回复
        this.currentComment.replies.forEach((reply, index) => {
          this.commentDialogList.push({
            id: reply.id || `auto_reply_${index + 1}`,
            userName: reply.userName,
            // 确保avatar字段有值，如果没有则使用默认头像
            avatar: reply.avatar || this.defaultAvatar,
            isOfficial: reply.isOfficial || 0, // 是否为官方人员
            time: reply.time,
            content: reply.content,
            images: reply.images || [], // 添加图片字段
            isCurrentUser: reply.isCurrentUser
          });
        });
      } else if (this.currentComment.reply) {
        // 如果只有单条回复，添加到对话列表
        this.commentDialogList.push({
          id: '1',
          userName: this.currentComment.reply.userName || '官方回复',
          // 官方回复使用默认头像
          avatar: this.defaultAvatar,
          isOfficial: 1, // 官方回复
          time: this.currentComment.reply.time,
          content: this.currentComment.reply.content,
          images: this.currentComment.reply.images || [], // 添加图片字段
          isCurrentUser: false
        });
      }
    },

    // 关闭评价详情弹出层
    closeCommentDialog() {
      this.dialogVisible = false;
      this.currentComment = null;
      this.commentDialogList = [];
      this.startReplyFlow = false; // 关闭时重置状态
    },

    async handleReplySuccessful() {
      // 重新获取主列表数据，从第一页开始
      this.resetPagination();
      await this.getCommentList();

      // 在更新的列表中找到当前评论
      const updatedComment = this.commentList.find(c => c.id === this.currentComment.id);

      if (updatedComment) {
        // 如果找到了，更新弹窗内的数据，保持弹窗开启状态
        this.currentComment = JSON.parse(JSON.stringify(updatedComment));
        this.getCommentDetail(this.currentComment.id);
      } else {
        // 如果评论因某种原因消失（例如被删除），则关闭弹窗
        this.closeCommentDialog();
      }
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hour = date.getHours().toString().padStart(2, '0');
      const minute = date.getMinutes().toString().padStart(2, '0');

      return `${year}/${month}/${day} ${hour}:${minute}`;
    },

    // 跳转到添加评价页面
    async goToAddEvaluation() {
      // 检查是否已缓存用户信息
      const cachedUserInfo = getWechatUserInfo();
      if (cachedUserInfo && cachedUserInfo.nickname) {
        this.wechatUserInfo = cachedUserInfo;
        this.navigateToAddEvaluation();
        return;
      }

      // 如果没有缓存，显示授权弹窗
      this.isUserInfoDialogVisible = true;
    },

    // 处理用户信息确认
    async handleUserInfoConfirm(userInfo) {
      this.isUserInfoDialogVisible = false;
      uni.showLoading({ title: '正在保存...' });

      try {
        const success = await this.maintainUserInfo(userInfo);
        if (success) {
          // 保存成功后，缓存用户信息并跳转
          setWechatUserInfo(userInfo);
          this.wechatUserInfo = userInfo;
          this.navigateToAddEvaluation();
        } else {
          uni.showToast({ title: '信息保存失败，请重试', icon: 'none' });
          // 允许用户再次尝试
          setTimeout(() => {
            this.isUserInfoDialogVisible = true;
          }, 1500);
        }
      } catch (error) {
        uni.showToast({ title: '发生错误，请重试', icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    },

    // 关闭用户信息弹窗
    closeUserInfoDialog() {
      this.isUserInfoDialogVisible = false;
      uni.showToast({
        title: '您需要授权才能发表评价',
        icon: 'none'
      });
    },

    // 调用接口维护用户信息
    async maintainUserInfo(userInfo) {
      const openid = this.getUserOpenid();
      if (!openid) {
        uni.showToast({ title: '无法获取用户标识', icon: 'none' });
        return false;
      }

      const params = {
        openid: openid,
        nickname: userInfo.nickname,
        // 优先使用上传后的头像ID，如果没有则使用头像URL
        avatar: userInfo.uploadedAvatarId || userInfo.avatarUrl,
      };

      try {
        const response = await this.$request.post(this.$interfaces.maintainWechatUserInfo, { data: params });
        if (response.code == 200 && response.data) {
          return true;
        } else {
          console.warn('用户信息维护API返回异常:', response);
          return true; // 即使API异常也允许继续，避免阻塞用户操作
        }
      } catch (error) {
        console.error('用户信息维护失败:', error);
        return true; // 网络异常也允许继续
      }
    },

    // 实际的页面跳转逻辑
    navigateToAddEvaluation() {
      uni.navigateTo({
        url: `/pagesD/evaluationPage/index?type=${this.evaluationType}&businessId=${this.businessId}&businessName=${this.businessInfo.name}`
      });
    },

    // 获取最多3条回复
    getLimitedReplies(comment) {
      return comment.replies ? comment.replies.slice(0, 3) : [];
    },

    // 判断是否有更多回复
    hasMoreReplies(comment) {
      return comment.replies && comment.replies.length > 3;
    },

    // 确认删除评论
    confirmDeleteComment(comment) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除此评论吗？删除后不可恢复。',
        confirmColor: '#FF4D4F',
        success: (res) => {
          if (res.confirm) {
            this.deleteComment(comment);
          }
        }
      });
    },

    // 删除评论
    async deleteComment(comment) {
      try {
        uni.showLoading({
          title: '删除中...'
        });

        // 根据评价类型选择删除接口
        let apiMethod = '';
        switch (this.evaluationType) {
          case 'serviceArea':
            apiMethod = 'deleteServiceAreaEvaluation';
            break;
          case 'chargingService':
            apiMethod = 'deleteChargingStationEvaluation';
            break;
          case 'tollStation':
            apiMethod = 'deleteTollStationEvaluation';
            break;
          default:
            uni.showToast({
              title: '未知的评价类型',
              icon: 'none'
            });
            return;
        }

        // 构造请求参数
        const params = {
          id: comment.id,
          // 添加其他必要参数
          openid: this.getUserOpenid()
        };

        // 调用删除API
        const response = await this.$request.post(this.$interfaces[apiMethod], {
          data: params
        });

        if (response.code == 200) {
          // 删除成功后重新加载列表
          this.resetPagination();
          this.getCommentList();

          uni.showToast({
            title: '评论删除成功',
            icon: 'success'
          });
        } else {
          uni.showModal({
            title: '删除失败',
            content: response.msg,
            showCancel: false
          })
        }

      } catch (error) {
        uni.showModal({
          title: '删除失败',
          content: '网络异常或其他错误，请重试',
          showCancel: false
        })
      } finally {
        uni.hideLoading();
      }
    },

    // 判断评论的第一条回复是否为官方回复
    hasFirstOfficialReply(comment) {

      // 检查新的replies数组结构 - 只检查第一条回复是否为官方
      if (comment.replies && comment.replies.length > 0) {
        return comment.replies[0].isOfficial == 1;
      }

      return false;
    },

    // 点击回复按钮
    handleReply(comment) {
      this.startReplyFlow = true;
      this.showCommentDialog(comment);
    },
  }
};
</script>

<style lang="scss" scoped>
.comment-list-page {
  height: 100vh;
  background-color: #F5F5F5;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.comment-list-header {
  background-color: #FFFFFF;
  padding: 30rpx 30rpx 10rpx 30rpx;
  margin: 0 20rpx;
  margin-top: 20rpx;
  position: relative;
}

.business-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
  position: relative;
  padding-left: 20rpx;
}

.business-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background-color: #0066E9;
  border-radius: 3rpx;
}

.comments-count {
  font-size: 26rpx;
  color: #666666;
  padding-left: 20rpx;
}

.scroll-box {
  flex: 1;
  overflow: hidden;
}

.scroll-Y {
  height: 100%;
}

.comment-list {
  padding: 0 20rpx 30rpx 20rpx;
  border-radius: 0 0 12rpx 12rpx;
}

.comment-item {
  background-color: #FFFFFF;
  padding: 24rpx;
  border-bottom: 1rpx solid #E9E9E9;
  position: relative;

  &:last-child {
    border-bottom: none;
  }
}

/* 置顶评论样式 */
.comment-item.top-comment {
  background-color: #FFF9F5;
  border-left: 4rpx solid #FF6B35;
}

/* 置顶标签样式 */
.top-label {
  position: absolute;
  top: 12rpx;
  right: 24rpx;
  display: flex;
  align-items: center;
  background-color: #FF6B35;
  color: #FFFFFF;
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  z-index: 1;

  text {
    margin-left: 4rpx;
  }
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16rpx;
  background-color: #FFFFFF;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
  display: flex;
  align-items: center;
}

.user-tag {
  display: inline-block;
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  font-weight: normal;
}

.user-tag.top {
  width: 84rpx;
  height: 42rpx;
  text-align: center;
  font-size: 24rpx;
  padding: 0;
  font-weight: 400;
  line-height: 42rpx;
  color: #FF9500;
  background: rgba(255, 157, 9, 0.1);
  border-radius: 8rpx;

}

.user-tag.self {
  width: 84rpx;
  height: 42rpx;
  font-size: 24rpx;
  padding: 0;
  font-weight: 400;
  text-align: center;
  line-height: 42rpx;
  color: #0066E9;
  background: rgba(0, 102, 233, 0.1);
  border-radius: 8rpx;
}


.user-tag.official {
  color: #0066E9;
  font-size: 26rpx;
  font-weight: 400;
}

.comment-time {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
}

.reply-text-btn {
  color: #999;
  margin-left: 20rpx;
}

/* 评论操作按钮样式 */
.comment-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 10rpx;
  gap: 20rpx;
  /* 新增，为按钮之间提供间距 */
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s;
  height: 50rpx;
  box-sizing: border-box;
}

.action-btn.delete-btn {
  color: #999;
  font-size: 24rpx;
}

.action-btn.delete-btn text {
  margin-right: 6rpx;
}

.action-btn:active {
  opacity: 0.7;
  transform: scale(0.96);
}

.comment-content {
  padding-left: 0;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
}

.rating-container {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  text-align: left;
}

.satisfaction-text {
  font-size: 26rpx;
  color: #333333;
  margin: 0 10rpx;
}

.comment-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  margin: 16rpx 0;
  gap: 28rpx;
}

.image-item {
  width: 168rpx;
  height: 168rpx;
  border-radius: 4rpx;
  overflow: hidden;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.reply-container {
  margin-top: 20rpx;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  position: relative;
  border: none;
  box-shadow: none;
  padding-left: 20rpx;
}

.reply-container:before {
  display: none;
}

.reply-item {
  margin-bottom: 16rpx;
  padding-bottom: 0;
  border-bottom: none;
  display: flex;
  align-items: flex-start;
}

.reply-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.reply-user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.reply-content-wrapper {
  flex: 1;
}

.reply-header {
  margin-bottom: 16rpx;
}

.reply-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #666666;
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.reply-time {
  font-size: 24rpx;
  color: #999999;
}

.reply-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  padding: 0;
  word-break: break-all;
  text-align: left;
  margin-bottom: 12rpx;
}

/* 子回复图片样式 */
.reply-images {
  display: flex;
  flex-wrap: wrap;
  margin: 12rpx 0;
  gap: 16rpx;
}

.reply-image-item {
  width: 120rpx;
  height: 120rpx;
  border-radius: 4rpx;
  overflow: hidden;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.expand-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  padding: 12rpx 16rpx;
  background: rgba(0, 102, 233, 0.1);
  border-radius: 8rpx;
  color: #0066E9;
  font-size: 26rpx;
  border: none;
  font-weight: 500;
}

.expand-btn-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-right: 0;
  margin-top: 0;
  /* 调整，因为被外层容器管理 */
}

.expand-btn text {
  color: #0066E9;
}



.floating-btn {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  image {
    width: 88rpx;
    height: 88rpx;
  }
}

.floating-btn:active {
  transform: scale(0.95);
}

.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 60vh;
  width: 100%;
  padding: 40rpx;
  box-sizing: border-box;

  image {
    width: 248rpx;
    height: 269rpx;
  }
}

.empty-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  margin-top: 10rpx;
  font-weight: 400;
}

.action-btn {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
}

.action-btn text {
  margin-left: 4rpx;
}

.movable-area {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.movable-view {
  pointer-events: auto;
  width: 88rpx;
  height: 88rpx;
}
</style>