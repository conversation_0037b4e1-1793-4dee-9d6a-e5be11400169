import {
	getEtcAccountInfo
} from "../../../common/storageUtil";

// 首页默认头像
let defaultAvatar = 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';

// 首页入口菜单配置
/*
关于菜单配置说明
icon 图标字体
name 菜单名称
type 业务类型
url  业务链接 【车辆列表页面 url必须为空】
extensionType 拓展类型 webview H5页面
validateRule 跳转校验规则 visitor 游客模式【无需登录】 login 登录模式 etcAccount etc用户模式 
 */
let menuConfig = [{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/load_iconv1.0.1.png',
		name: '充值',
		type: 'recharge',
		url: '',
		validateRule: 'login',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/rechargeListv1.0.1.png',
		name: '充值记录',
		type: 'rechargeList',
		url: '/pagesB/rechargeBusiness/selectVehicle/index?fontType=rechargeList',
		validateRule: 'etcAccount',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/new-apply.png',
		name: 'ETC新办',
		type: 'newApply',
		url: '/pagesA/newBusiness/productSelect/productSelect',
		validateRule: 'login',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/home/<USER>',
		name: '服务订单',
		type: 'serviceOrder',
		url: '/pagesA/serviceOrder/index',
		validateRule: 'login',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/home/<USER>',
		name: '设备售后',
		type: 'afterSale',
		url: '/pagesC/afterSaleBusiness/vehicleList/index',
		validateRule: 'login',
		extensionType: '',
		isNoVisible: false, //线上发行上线先屏蔽入口
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/coupon_iconv1.0.1.png',
		name: '权益中心',
		type: 'coupon',
		url: '/pagesC/coupons/selectCoupons/selectCoupons',
		validateRule: 'etcAccount',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/load_iconv1.0.1.png',
		name: '储值卡充值',
		type: 'loadBusiness',
		isNoVisible: true,
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unit-car.png',
		name: '消费记录',
		type: 'consumptionRecord',
		url: '/pagesB/vehicleBusiness/vehicleList?fontType=consumptionRecord',
		validateRule: 'etcAccount',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>',
		name: '卡片状态查询',
		type: 'blackList',
		//url: '/pagesB/cardBusiness/blackList/vehicleState',
		url: '/pagesB/vehicleBusiness/vehicleList?fontType=blackList',
		validateRule: 'etcAccount',
		extensionType: '',
		isNoVisible: false, //线上发行上线先屏蔽入口
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bussiness-receipt.png',
		name: '业务记录',
		type: 'receipt',
		isNoVisible: true,
		url: '/pagesC/afterSale/businessReceipt/businessReceipt',
		validateRule: 'etcAccount',
		extensionType: ''
	},

	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/owe.png',
		name: '欠费补缴',
		type: 'afterPay',
		url: '',
		validateRule: 'login',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/refundApplication.png',
		name: '集装箱退费',
		type: 'containerRefund',
		isNoVisible: true,
		url: '/pagesB/vehicleBusiness/vehicleList?fontType=containerRefund',
		validateRule: 'etcAccount',
		extensionType: ''
	},
	{
		icon: '../../../../static/toc/dispute_refund_icon.png',
		name: '退费申请',
		type: 'disputeRefund',
		url: '/pagesB/disputeRefund/index',
		validateRule: 'etcAccount',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/ecss-syncv1.0.1.png',
		name: '余额迁移',
		type: 'ecssSync',
		url: '/pagesB/ecssBusiness/index',
		validateRule: 'etcAccount',
		isNoVisible: true,
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/signaturev1.0.1.png',
		name: '电子协议查询',
		type: 'signature',
		url: '',
		validateRule: 'etcAccount',
		extensionType: '',
		isNoVisible: true
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>',
		name: '在线客服',
		type: 'onlineserver',
		url: '',
		validateRule: 'login',
		isNoVisible: true,
		extensionType: 'webview'
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc_update.png',
		name: '信息修正',
		type: 'updateDevice',
		url: '/pagesB/deviceBusiness/entry/entry',
		validateRule: 'etcAccount',
		extensionType: ''
	},
	{
		icon: '../../../../static/toc/icon_selfcheck.png',
		name: '设备检测',
		type: 'selfCheck',
		url: '/pagesC/selfCheck/index',
		validateRule: 'login',
		extensionType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/ccs_sign.png',
		name: '代扣签约',
		type: 'ccsSign',
		url: '/pagesB/vehicleBusiness/vehicleList?fontType=ccsSign',
		validateRule: 'etcAccount',
		extensionType: '',
		isNoVisible: false, //线上发行上线先屏蔽入口
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/invoice_icon.png',
		name: '发票服务',
		type: 'invoice',
		// url: '/pagesB/invoiceBusiness/home/<USER>',
		url: '/pagesB/invoiceBusiness/home/<USER>',
		validateRule: 'etcAccount',
		extensionType: ''
	},

	// #ifdef MP-WEIXIN
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>',
		name: 'ETC车生活',
		type: 'spread',
		isNoVisible: true,
		url: '',
		validateRule: '',
		extensionType: ''
	},
	// #endif
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/user.png',
		name: '我的账号',
		type: 'myAccount',
		url: '/pagesB/personal/more/p-more',
		validateRule: 'visitor',
		isNoVisible: true,
		extensionType: ''
	}

]
//  首页入口菜单配置
let entranceConfig = [{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/load_icon.png',
		name: '充值',
		type: 'recharge',
		url: '',
		validateType: ''
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/rechargeList.png',
		name: '充值记录',
		type: 'rechargeList'
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/coupon_icon.png',
		name: '权益中心',
		type: 'coupon'
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/load_icon.png',
		name: '储值卡充值',
		type: 'loadBusiness',
		isNoVisible: true
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unit-car.png',
		name: '消费记录',
		type: 'consumptionRecord'
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>',
		name: '卡片状态查询',
		type: 'blackList'
	},

	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bussiness-receipt.png',
		name: '业务记录',
		type: 'receipt',
		isNoVisible: true
	},

	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/owe.png',
		name: '欠费补缴',
		type: 'afterPay'
		// isNoVisible: true
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/huoche.png',
		name: '集装箱退费',
		type: 'containerRefund',
		isNoVisible: true
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/ecss-sync.png',
		name: '余额迁移',
		type: 'ecssSync'
		// isNoVisible: true
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/signature.png',
		name: '电子协议查询',
		type: 'signature',
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>',
		name: '在线客服',
		type: 'onlineserver'
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc_update.png',
		name: '信息修正',
		type: 'updateDevice'
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/invoice_icon.png',
		name: '发票服务',
		type: 'invoice'
	},
	{
		icon: '../../../static/toc/icon_selfcheck.png',
		name: '设备检测',
		type: 'selfCheck'
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/ccs_sign.png',
		name: '代扣签约',
		type: 'ccsSign'
	}, {
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/invoice_icon.png',
		name: '发票服务',
		type: 'invoice'
	},

	// #ifdef MP-WEIXIN
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>',
		name: 'ETC车生活',
		type: 'spread',
		isNoVisible: true
	},
	// #endif
	{
		icon: '../../../static/home/<USER>',
		name: '服务列表',
		type: 'serviceOrder'
	},
	{
		icon: '../../../static/home/<USER>',
		name: '设备售后',
		type: 'afterSale'
	},
	{

		icon: '../../../static/toc/huoche.png',
		name: '退费申请',
		type: 'disputeRefund',
		isNoVisible: false
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/user.png',
		name: '我的账号',
		type: 'myAccount',
		isNoVisible: true
	},
	{
		icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>',
		name: '更多',
		type: 'allBusiness',
		isNoVisible: true
	}
]

let menuConfigS = [
  {
    sortName: '常用应用', sortArr: [
      {
        // menuId: 1,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/load_iconv1.0.1.png',
        name: 'ETC充值',
        type: 'recharge',
        url: '',
        validateRule: 'login',
        extensionType: ''
      },
      {
        menuId: 2,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/rechargeListv1.0.1.png',
        name: '充值记录',
        type: 'rechargeList',
        url: '/pagesB/rechargeBusiness/selectVehicle/index?fontType=rechargeList',
        validateRule: 'etcAccount',
        extensionType: ''
      },
      {
        // menuId: 3,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/new-apply.png',
        name: 'ETC新办',
        type: 'newApply',
        url: '/pagesA/newBusiness/productSelect/productSelect',
        validateRule: 'login',
        extensionType: ''
      },
      {
        // menuId: 4,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/home/<USER>',
        name: '服务订单',
        type: 'serviceOrder',
        url: '/pagesA/serviceOrder/index',
        validateRule: 'login',
        extensionType: ''
      },
      {
        menuId: 5,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/coupon_iconv1.0.1.png',
        name: '权益中心',
        type: 'coupon',
        url: '/pagesC/coupons/selectCoupons/selectCoupons',
        validateRule: 'etcAccount',
        extensionType: ''
      },
      {
        menuId: 6,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/ccs_sign.png',
        name: '代扣签约',
        type: 'ccsSign',
        url: '/pagesB/vehicleBusiness/vehicleList?fontType=ccsSign',
        validateRule: 'etcAccount',
        extensionType: '',
        isNoVisible: false, //线上发行上线先屏蔽入口
      },
      {
        menuId: 9,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/owe.png',
        name: '欠费补缴',
        type: 'afterPay',
        url: '',
        validateRule: 'login',
        extensionType: ''
      },
      {
        // menuId: 17,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/invoice_icon.png',
        name: '开票服务',
        type: 'invoice',
        // url: '/pagesB/invoiceBusiness/home/<USER>',
        url: '/pagesB/invoiceBusiness/home/<USER>',
        validateRule: 'etcAccount',
        extensionType: ''
      },
      {
        // menuId: 101,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unit-car.png',
        name: '消费记录',
        type: 'consumptionRecord',
        url: '/pagesB/vehicleBusiness/vehicleList?fontType=consumptionRecord',
        validateRule: 'etcAccount',
        extensionType: ''
      },

    ]
  },
  {
    sortName: '售后服务', sortArr: [
      {
        // menuId: 7,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/home/<USER>',
        name: '设备售后',
        type: 'afterSale',
        url: '/pagesC/afterSaleBusiness/vehicleList/index',
        validateRule: 'login',
        extensionType: '',
        isNoVisible: false, //线上发行上线先屏蔽入口
      },
      {
        menuId: 8,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/return-goods.png',
        name: '退费申请',
        type: 'disputeRefund',
        url: '/pagesB/disputeRefund/index',
        validateRule: 'etcAccount',
        extensionType: ''
      },
      {
        menuId: 10,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc_update.png',
        name: '信息修正',
        type: 'updateDevice',
        url: '/pagesB/deviceBusiness/entry/entry',
        validateRule: 'etcAccount',
        extensionType: ''
      },
      {
        // menuId: 11,
        icon: '../../../static/toc/icon_selfcheck.png',
        name: '设备检测',
        type: 'selfCheck',
        url: '/pagesC/selfCheck/index',
        validateRule: 'login',
        extensionType: ''
      },
    ]
  },
  {
    sortName: '拓展服务', sortArr: [
      {
        menuId: 14,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/kefu.png',
        name: '在线客服',
        type: 'onlineserver',
        url: '',
        validateRule: 'login',
        isNoVisible: false,
        extensionType: 'webview'
      },
      {
        // menuId: 12,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/jiuyuan.png',
        name: '一键救援',
        type: 'rescue',
				url: '/pagesD/travelService/rescue/index',
        validateRule: 'login',
        extensionType: ''
      },
      {
        // menuId: 13,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/ecss-syncv1.0.1.png',
        name: '充电服务',
        type: 'jtCharge',
				url: '/pagesD/charging/index',
        validateRule: 'login',
        isNoVisible: false,
        extensionType: ''
      },
      {
        // menuId: 15,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/huoche.png',
        name: '货运平台',
        type: 'platform',
        validateRule: 'login',
        extensionType: ''
      },
      {
        menuId: 16,
        icon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/zhengCe.png',
        name: '政策解读',
        type: 'invoice',
        // url: '/pagesB/invoiceBusiness/home/<USER>',
        url: '/pagesD/travelService/policy/index',
        validateRule: 'etcAccount',
        extensionType: ''
      },

    ]
  },
]

export {
	menuConfig,
	entranceConfig,
	defaultAvatar,
	menuConfigS
}