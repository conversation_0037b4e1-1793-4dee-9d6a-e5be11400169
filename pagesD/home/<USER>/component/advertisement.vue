<template>
	<view class="dialog-wrapper">
		<view class="dialog" v-show="showFlag">
			<!-- <view class="mask"></view> -->
			<u-mask :show="showFlag" @click="showFlag = false"></u-mask>
			<view class="dialog-cont">
				<swiper class="swiper" autoplay :interval="5000" :indicator-dots="true" indicator-color="#a09e9e"
					indicator-active-color="#fff" @change="swiperChange">
					<swiper-item v-for="item in dataList" @click="swiHandle(item,3)">
						<image :src="item.adPic" mode="aspectFit" class="s-img"></image>
					</swiper-item>
				</swiper>
				<view class="close-icon" @click="closeAd">
					关闭{{ count == 0 ? '' : `(${count}秒)` }}
				</view>
			</view>
		</view>
		<view v-show="showAd" class="movable-wrapper">
			<movable-area>
				<movable-view :x="x" :y="y" direction="all" out-of-bounds @change="onChange"
					@touchend.prevent.stop="touchend" @click.prevent.stop="tap">
					<view class="customerService">
						<image :src="floatList.adPic" class="customerService-img" mode="aspectFill"></image>
						<image @click.prevent.stop="showAd = false" src="../../../../static/toc/close.png"
							class="icon-img" :class="trimSide?'icon-img_left':''" mode="aspectFill"></image>
					</view>
				</movable-view>
			</movable-area>
		</view>
	</view>
</template>

<script>
	import {
		getOpenid
	} from '@/common/storageUtil.js'
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		data() {
			return {
				trimSide: false,
				showFlag: false,
				showAd: false,
				loading: false,
				floatCount: null,
				floatTimer: null,
				count: null,
				timer: null,
				floatList: {},
				dataList: [],
				x: 0,
				y: 0,
				old: {
					x: 0,
					y: 0
				},
			}
		},
		methods: {
			swiperChange(e) {
				console.log('弹窗change===>>', e.detail.current)
				let index = e.detail.current
				//弹窗传入3，曝光传入1
				this.newTrackUpload(this.dataList[index], 3, 1)
			},
			//新数据上报
			newTrackUpload(row, bizType, eventType) {
				let params = {
					bizType: bizType, //1-广告、2-资讯
					bizId: row.adId,
					eventType: eventType, //1-曝光、2-点击
					eventTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
					userId: getOpenid()
				}
				console.log('prams===>>>>', params)
				if (this.loading) return
				this.loading = true;
				this.$request
					.post(this.$interfaces.trackUpload, {
						data: {
							list: [params]
						}
					})
					.then((res) => {
						this.loading = false;
					})
					.catch((error) => {
						this.loading = false;
					})
			},
			onChange(e) {
				// console.log('变动', e)
				// this.old.x = this.x
				this.old.y = e.detail.y
			},
			tap(e) {
				console.log('点击事件')
				if (!this.trimSide) {
					//弹窗3，浮窗4
					this.swiHandle(this.floatList, 4)
				} else {
					// 解决view层不同步的问题
					this.x = this.old.x
					this.y = this.old.y
					this.$nextTick(() => {
						this.x = this.old.x
						this.y = this.old.y
					})
					this.trimSide = false
				}

			},
			touchend(e) {
				if (this.y == this.old.y) {
					return
				}
				console.log('滑动结束事件', this.trimSide)
				//解决view层不同步的问题
				this.x = this.old.x
				this.y = this.old.y
				this.$nextTick(() => {
					this.x = this.old.x + 25
					this.y = this.old.y
				})
				this.trimSide = true
			},
			// 浮窗组件展示
			startFloatShow() {
				this.showAd = true
				this.floatTimer = setInterval(() => {
					if (this.floatCount == 0) {
						this.trimSide = true
						// 解决view层不同步的问题
						this.x = this.old.x
						this.y = this.old.y
						this.$nextTick(() => {
							this.x = this.old.x + 25
							this.y = this.old.y
						})
						clearInterval(this.floatTimer)
					} else {
						this.floatCount -= 1
					}
				}, 1000)
			},
			// 弹窗组件展示
			startShow() {
				this.showFlag = true
				this.timer = setInterval(() => {
					if (this.count == 0) {
						this.showFlag = false
						clearInterval(this.timer)
					} else {
						this.count -= 1
					}
				}, 1000)
			},
			getAdUrl() {
				this.$request
					.post(this.$interfaces.getDialogList, {
						data: {
							displayId: '0',
							userId: getOpenid()
						}
					})
					.then(res => {
						console.log('广告===>>>', res)
						if (res.code == 200) {
							this.dataList = res.data.popAdList
							this.count = parseInt(res.data.displayTime)
							if (this.dataList.length > 0) {
								this.newTrackUpload(this.dataList[0], 3, 1)
								this.startShow()
							}

						}
					})
					.catch(error => {});
			},
			getFloatUrl() {
				this.$request
					.post(this.$interfaces.getFloatDialog, {
						data: {
							displayId: '0',
							userId: getOpenid()
						}
					})
					.then(res => {
						console.log('floatList===>>>', res)
						if (res.code == 200) {
							if (res.data.length > 0) {
								this.floatList = res.data[0]
								this.floatCount = parseInt(res.data[0].displayTime)
								// this.showAd = true
								//曝光传入1，浮窗传入4
								this.newTrackUpload(this.floatList, 4, 1)
								this.startFloatShow()
							}
						}
					})
					.catch(error => {});
			},
			closeAd() {
				this.showFlag = false
			},
			swiHandle(item, type) {
				//数据点击上报
				this.newTrackUpload(item, type, 2);
				if (item.jumpType == 1) {
					//跳转H5
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' +
							encodeURIComponent(item.jumpUrl)
					})
				} else if (item.jumpType == 2) {
					// 打开商城小程序
					uni.navigateToMiniProgram({
						appId: item.appid,
						path: item.jumpUrl,
						envVersion: 'release', //体验版
						extraData: {},
						success(res) {
							// 打开成功
						}
					})
				} else if (item.jumpType == 3) {
					// 点击文章类型广告
					uni.navigateTo({
						url: '/pagesC/infoBusiness/detail?informationId=' + item.linkId
					})
				}
			},
		},

		mounted() {
			let info = uni.getSystemInfoSync()
			console.log('info', info);
			if (info.safeArea.width > 400) {
				this.x = info.safeArea.width - 85
				this.old.x = info.safeArea.width - 85
			} else if (info.safeArea.width <= 320) {
				this.x = info.safeArea.width - 65
				this.old.x = info.safeArea.width - 65
			} else {
				this.x = info.safeArea.width - 75
				this.old.x = info.safeArea.width - 75
			}
			this.y = info.safeArea.height - 200
			this.old.y = info.safeArea.height - 200

			this.getAdUrl()
			this.getFloatUrl()

		},
		// 销毁时，清除定时器
		destroyed() {
			if (this.timer) {
				clearInterval(this.timer)
			}
			if (this.floatTimer) {
				clearInterval(this.floatTimer)
			}
		}
	}
</script>

<style scoped>
	.mask {
		position: absolute;
		top: 0;
		height: 100vh;
		width: 100vw;
		background-color: #000000;
		opacity: 0.5;
		z-index: 99998;
	}

	.dialog-cont {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		height: 100vw;
		width: 80vw;
		z-index: 99999;
	}

	.swiper {
		height: calc(100% + 80rpx);
		width: 100%;
		z-index: 99999;
	}

	.s-img {
		height: calc(100% - 60rpx);
		width: 100%;
	}

	.close-icon {
		position: absolute;
		top: 0;
		right: 0;
		color: #fff;
		background-color: #a09e9e;
		padding: 5rpx 10rpx;
		border-radius: 5px;
		transform: translate(-15rpx, calc(-100% - 10rpx));
		margin-bottom: 10rpx;
	}

	/* 	movable-view {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 150rpx;
		width: 150rpx;
		background-color: #007AFF;
		color: #fff;
	} */

	.customerService {
		position: relative;
		padding: 10rpx;
		width: 130rpx;
		height: 100rpx;
		border-radius: 12rpx;
		background-color: rgba(0, 0, 0, 0.2);
	}

	.customerService .customerService-img {
		vertical-align: top;
		display: inline-block;
		width: 100%;
		height: 100%;
		z-index: 0;
	}

	.customerService .icon-img {
		position: absolute;
		right: -16rpx;
		top: -10rpx;
		width: 35rpx;
		height: 35rpx;
		/* background-color: #000000; */
		border-radius: 50%;
		/* 		display: inline-block;
		width: 20%;
		height: 20%; */
	}

	.customerService .icon-img_left {
		left: -16rpx;
		right: 0;
	}
</style>