<template>
	<!-- <view class="vehicle-wrap" v-if="checkLoginStatus && customerInfo.customer_name && isLoading"> -->
	<view class="vehicle-wrap" v-if="checkLoginStatus">
		<!-- 是否存在车辆 -->
		<block v-if="vehicleList.length">
			<!-- 个人用户车辆列表 -->
			<view v-if="customerInfo.customer_type == 0" class="vehicle-list">
				<swiper class="vehicle-carousel square-dot" :indicator-dots="true" :circular="true"
					@change='onChangeHandle' previous-margin='60px' next-margin='60px' indicator-color="#666666"
					indicator-active-color="#666666">
					<swiper-item v-for="(item, index) in vehicleList" :key="index">
						<view class="vehicle-card">
							<view class="vehicle-card_item g-flex g-flex-horizontal-vertical"
								:class="'license-plate-'+item.vehicleColor"
								:style="{color:item.vehicleColor=='0'|| item.vehicleColor=='2' ? '#fff' : '#000'}">
								{{item.vehicleCode}}
							</view>
						</view>
					</swiper-item>
				</swiper>
				<view class="vehicle-desc g-flex g-flex-justify g-flex-align-center">
					<view class="vehicle-desc_item vehicle-desc_flex g-flex ">
						<view class="title">
							产品：
						</view>
						<view class="value">
							{{currentCardProduct}}
						</view>
					</view>
					<view class="vehicle-desc_item g-flex " :class="[isVehicleAmount ? '' :'g-flex-end']">
						<view class="title">
							状态：
						</view>
						<view class="value">
							{{currentCpuStatus}}
						</view>
					</view>
					<view class="vehicle-desc_item vehicle-desc_flex g-flex g-flex-end" v-if="isVehicleAmount">
						<view class="title">
							卡内余额：
						</view>
						<view class="value">
							{{currentVehicleAmount}}元
						</view>
					</view>
				</view>
			</view>
			<!-- 单位账单说明 -->
			<view v-else class="company-bill g-flex g-flex-align-center">
				<view class="company-bill_wrap g-flex g-flex-align-center g-flex-justify">
					<view class="company-bill_cont">
						<view class="g-flex label g-flex-align-end">
							月度通行次数汇总：<view class="label_weight">{{unitVehicleInfo.count}}</view>次
						</view>
						<view class="g-flex label g-flex-align-end">
							月度消费金额汇总：<view class="label_weight">{{moneyFilter(unitVehicleInfo.passAmount)}}</view>元
						</view>
					</view>
					<view class="company-bill_ft" @click="goVehicleList">
						<image
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>"
							class="img" mode="aspectFill"></image>
					</view>
				</view>
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>"
					class="company-bill_bg" mode="aspectFill"></image>
			</view>
		</block>
		<block v-else>
			<view class="handle-business">
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>"
					class="home-img" @click='goNewApply'></image>
			</view>
		</block>


	</view>
	</view>
</template>

<script>
	import {
		getTicket,
		getMd5Key,
		getAesKey,
		getLoginUserInfo,
		getCurrUserInfo
	} from '@/common/storageUtil.js'
	import {

		menuConfig
	} from '../home.js';
	import { skipControlHandle } from '@/components/home/<USER>';
	import {
		getCpuStatus
	} from '@/common/method/filter.js'
	export default {
		props: {
			customerInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},

		data() {
			return {
				commonlyList: menuConfig,
				vehicleList: [],
				unitVehicleInfo: {},
				isLoading: false,
				currentIndex: 0, // 当前车辆索引值
				cardTypeOptions: [{
					"label": "捷通日日通记账卡",
					"value": "5"
				}, {
					"label": "后付费绑定记账卡",
					"value": "4"
				}, {
					"label": "捷通月月行记账卡",
					"value": "7"
				}, {
					"label": "预付费绑定记账卡",
					"value": "3"
				}, {
					"label": "预付费记账卡",
					"value": "2"
				}, {
					"label": "储值卡",
					"value": "0"
				}, {
					"label": "运营管理卡",
					"value": "1"
				}, {
					"label": "捷通0元预存记账卡",
					"value": "8"
				}, {
					"label": "集团客户",
					"value": "90"
				}, {
					"label": "捷通日日通记账卡(客账扣款)",
					"value": "9"
				}, {
					"label": "捷通次次顺记账卡",
					"value": "10"
				}]
			}
		},
		components: {

		},
		computed: {

			checkLoginStatus() {
				return !!(getTicket() && getMd5Key() && getAesKey() && this.getUserNo)
			},
			getUserNo() {
				return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length ? getLoginUserInfo().userNo : ''
			},
			currentCustomer() {
				return this.checkLoginStatus ? this.customerInfo : {}
			},
			vehicleAllDataList() {
				return this.vehicleList
			},
			// 当前车辆cpu卡状态
			currentCpuStatus() {
				if (!this.vehicleAllDataList.length) return ''
				let item = this.vehicleAllDataList[this.currentIndex]
				return getCpuStatus(item.cardStatus) || '';
			},
			// 当前车辆金额
			currentVehicleAmount() {
				if (!this.vehicleAllDataList.length) return '0'
				let item = this.vehicleAllDataList[this.currentIndex]
				return this.moneyFilter(item.amount) || '0'
			},
			isVehicleAmount() {
				if (!this.vehicleAllDataList.length) return false;
				let item = this.vehicleAllDataList[this.currentIndex]
				let arr = ['0', '3', '5', '8'] // 展示车辆金额产品类型
				return arr.includes(item.cardProduct);
			},
			currentCardProduct() {
				if (!this.vehicleAllDataList.length) return false;
				let item = this.vehicleAllDataList[this.currentIndex]
				let name = ''
				for (let i = 0; i < this.cardTypeOptions.length; i++) {
					if (item.cardProduct == this.cardTypeOptions[i].value) {
						name = this.cardTypeOptions[i].label
					}
				}
				// 后付费绑定记账卡（按照“渠道名+绑定记账卡”展示）
				if (item.cardProduct == 4) {
					let channelName = item.channelName || ''
					name = channelName + '绑定记账卡';
				}
				return name
			}
		},
		watch: {
			customerInfo(val) {
				this.init();
			}
		},
		mounted() {
			this.init();

		},
		methods: {
			init() {
				if (this.customerInfo && this.customerInfo.customer_id) {
					this.getAllVerhicleList();
					this.getUnitVehicleList();
				}
			},
			goNewApply() {
				this.$emit('goNewApply')
			},
			goVehicleList() {
				uni.navigateTo({
					url: '/pagesB/vehicleBusiness/vehicleList'
				})
			},
			getAllVerhicleList() {
				let params = {
					customerId: this.customerInfo.customer_id
				}
				this.isLoading = false;
				this.$request
					.post(this.$interfaces.vehicleList, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.vehicleList = res.data || []
							this.$emit('on-change', this.vehicleList)
							this.currentIndex = 0;

						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
						this.isLoading = true;
					}).catch(error => {
						this.isLoading = true;
					})
			},
			// 查询单位用户车辆信息
			getUnitVehicleList() {
				let params = {
					customerId: this.customerInfo.customer_id
				}
				this.$request
					.post(this.$interfaces.unitVehicleList, {
						data: params
					})
					.then((res) => {

						if (res.code == 200) {
							this.unitVehicleInfo = res.data;
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}

					}).catch(error => {

					})
			},
			onChangeHandle(e) {
				this.currentIndex = e.detail.current
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
			goLoginHandle() {
				uni.reLaunch({
					url: '/pagesD/login/p-login'
				})
			}
		}
	}
</script>

<style lang="scss">
	.home-img {
		height: 225rpx;
		width: 100%;
	}

	.vehicle-wrap {
		margin: 20rpx;

		.handle-business {
			width: 710rpx;
			height: 225rpx;

			.img {
				display: block;
				width: 100%;
				height: 100%;
			}
		}

		.vehicle-list {
			background-color: #FFFFFF;

			.vehicle-desc {
				padding: 0 10rpx;
				height: 50rpx;
				width: 100%;
				background: #E6EDFA;
				border-radius: 0rpx 0rpx 8rpx 8rpx;

				.vehicle-desc_item {

					min-width: 150rpx;

					.title {
						font-size: 24rpx;
						font-weight: 400;
						color: #062358;
					}

					.value {
						font-size: 24rpx;
						font-weight: 500;
						color: #062358;
					}
				}

				.vehicle-desc_flex {
					flex: 1
				}
			}

			.vehicle-carousel {
				height: 200rpx !important;

				.vehicle-card {
					height: 150rpx;
					border-radius: 20rpx;
					margin: 20rpx;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;

					.vehicle-card_item {
						width: 482rpx;
						height: 122rpx;
						font-size: 56rpx;
						font-weight: 500;
						margin-bottom: 20rpx;
						color: #FFFFFF;
					}

					.license-plate-0 {
						background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue.png') no-repeat;
						background-size: 100% 100%;
					}

					.license-plate-1 {
						background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_green.png') no-repeat;
						background-size: 100% 100%;
					}

					.license-plate-2 {
						background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_black.png') no-repeat;
						background-size: 100% 100%;
					}

					.license-plate-3 {
						background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_white.png') no-repeat;
						background-size: 100% 100%;
					}

					.license-plate-4 {
						background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_gradient_green.png') no-repeat;
						background-size: 100% 100%;
					}

					.license-plate-5 {
						background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_yellow_green.png') no-repeat;
						background-size: 100% 100%;
					}

					.license-plate-6 {
						background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue_white_gradient.png') no-repeat;
						background-size: 100% 100%;
					}
				}
			}
		}

		.company-bill {
			position: relative;
			width: 714rpx;
			height: 199rpx;

			.company-bill_bg {
				display: block;
				width: 100%;
				height: 100%;
				position: absolute;
				left: 0;
				top: 0;
				z-index: 1;
			}

			.company-bill_wrap {
				padding-left: 46rpx;
				padding-right: 32rpx;
				width: 100%;
				position: relative;
				z-index: 2;

				.company-bill_cont {
					flex: 1;

					.label {
						font-weight: 400;
						color: #333333;
						font-size: 28rpx;
					}

					.label_weight {
						font-weight: 500;
						color: #FF9038;
						font-size: 40rpx;
						padding-right: 4rpx;
					}
				}

				.company-bill_ft {
					width: 188rpx;
					height: 64rpx;

					.img {
						display: block;
						width: 100%;
						height: 100%;
					}
				}
			}



		}
	}
</style>