<template>
	<view class="commonly-box">
		<view class="commonly-used">
			<view class="commonly-list">
				<block v-for="(item, index) in commonlyList" :key="index">
					<view class="card" v-if="!item.isNoVisible" @click="onMenuHandle(item)">
						<image :src="item.icon" style="width: 86rpx;height: 86rpx;" mode="monthlyBillt"></image>
						<view class="name">{{ item.name }}</view>
					</view>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getTicket,
		getMd5Key,
		getAesKey,
		getLoginUserInfo,
		getCurrUserInfo
	} from '@/common/storageUtil.js'
	import {

		menuConfig
	} from '../home.js';
	import { skipControlHandle } from '@/components/home/<USER>';
	export default {
		props: {
			vehicleList: {
				type: Array,
				default () {
					return []
				}
			}
		},
		data() {
			return {
				commonlyList: menuConfig
			}
		},
		components: {

		},
		computed: {

			checkLoginStatus() {
				return !!(getTicket() && getMd5Key() && getAesKey() && this.getUserNo)
			},
			getUserNo() {
				return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length ? getLoginUserInfo().userNo : ''
			},
			currentCustomer() {
				return this.checkLoginStatus ? getCurrUserInfo() : {}
			}
		},
		mounted() {


		},
		methods: {
			customerChange() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList'
				})
			},
			goLoginHandle() {
				uni.reLaunch({
					url: '/pagesD/login/p-login'
				})
			},
			onMenuSkipHandle(item) {
				if (item.extensionType && item.extensionType == 'webview') {
					this.goWebviewHandle(item);
					return;
				} 
				this.goPagesHandle(item);
			},
			// 验证登录
			validateLogin() {
				if (!this.checkLoginStatus) {
					uni.showModal({
						title: '提示',
						content: '请先登录',
						success: (res) => {
							if (res.confirm) {
								uni.reLaunch({
									url: '/pagesD/login/p-login'
								})
							}
						}
					})
					return
				}
				return true;
			},
			// 验证绑定ETC用户
			validateBindAccount() {
				if (!(getCurrUserInfo() && getCurrUserInfo().customer_id)) {
					uni.showModal({
						title: '提示',
						content: '请先绑定ETC用户',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pagesB/accountBusiness/accountList/accountList'
								})
							}
						}
					})
					return
				}
				return true;
			},
			// webview 跳转模式
			goWebviewHandle(item) {
				// 在线客服
				if (item.type == 'onlineserver') {
					var callcenter =
						'https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027'
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' +
							encodeURIComponent(callcenter)
					})
				}
			},
			// 小程序页面 跳转模式
			goPagesHandle(item) {
				// 根据菜单配置页面路由地址
				if (item.url) {
					uni.navigateTo({
						url: item.url
					})
					return;
				}
				// 特殊处理路由页面
				const specialType = ['afterPay'];
				
				if (item.type == 'recharge') {
					let flag = !!this.vehicleList.length
					let url = flag ?
						'/pagesB/rechargeBusiness/selectVehicle/index' :
						'/pagesB/rechargeBusiness/selectVehicle/index'
					uni.navigateTo({
						url: url + '?fontType=' + item.type
					})
					return
				}
				if(specialType.includes(item.type) && !this.vehicleList.length){
					uni.navigateTo({
						url: '/pagesB/vehicleBusiness/noVehicleList?fontType=' + item.type
					})
					return;
				}
				
				uni.navigateTo({
					url: '/pagesB/vehicleBusiness/vehicleList?fontType=' + item.type
				})
				
			},
			onMenuHandle(item) {
				// 特勤业务 如车生活
				let type = item.type
				let flag = skipControlHandle(item.type);
				if (!flag) return;
			
				// 游客模式 无需验证登录
				if (item.validateRule == 'visitor') {
					this.onMenuSkipHandle(item)
					return;
				}
				// 登录模式 无需绑定ETC用户
				if (item.validateRule == 'login') {
					if (!this.validateLogin()) return
					this.onMenuSkipHandle(item)
					return;
				}
				// ETC用户模式 需绑定ETC用户
				if (item.validateRule == 'etcAccount') {
					if (!this.validateLogin()) return
					if (!this.validateBindAccount()) return
					this.onMenuSkipHandle(item)
					return;
				}
			},
		}
	}
</script>

<style lang="scss">
	.commonly-box {
		background-color: #fff;
		margin: 0 20rpx;
		padding: 20rpx 0;

		.commonly-used {
			width: 100%;
			display: flex;
			flex-direction: column;
			.commonly-list {
				display: flex;
				flex-wrap: wrap;

				.card {
					display: flex;
					width: 25%;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					margin: 22rpx 0;
					position: relative;

					.name {
						font-weight: 400;
						color: #323435;
						font-size: 26rpx;
						margin-top: 12rpx;
					}

					.editIcon {
						position: absolute;
						right: 50rpx;
						top: -10rpx;
					}
				}
			}
		}
	}
</style>
