<template>
	<view class="top-message g-flex-align-center">
		<view class="notice">
			<view class="message">
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>" class="message-img"></image>
			</view>
			<view class="noticeList">
				<u-notice-bar mode="vertical" padding='9px' color="#8D7860" font-size="26" :more-icon="false"
					:volume-icon="false" :duration="5000" :list="noticeList" type="none" @click='onNoticeHandle'>
				</u-notice-bar>
			</view>
			<!-- <view class="allNotice" >全部</view> -->
		</view>
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:confirm-text="modal.confirmText" @confirm="onConfirmHandle">
			<view class="message-content-wrapper">
				<view class="title">提示</view>
				<view class="msg_desc">

					<view class="msg_desc-content">
						{{dislogMsg}}
					</view>
				</view>
			</view>
		</neil-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					confirmText: '确认'
				},
				msgList: [{
					text: '桂小通(广西捷通)捷通E购商城上线，优惠活动享不停',
					type: 'link',
					url: 'https://mp.weixin.qq.com/s?__biz=MzAxNjUxNDYwNg==&mid=2662548491&idx=1&sn=5b851be89f8ed9d9b4366117ff8e12fe&chksm=80b44f4ab7c3c65c4197ce182d7a8d0a78b5dbdfd9e8912d4034c604a075435c9dfb8f9100b1#rd'
				}],
				noticeList: [],
				dislogMsg: ''
			}
		},
		components: {

		},
		created() {
			this.init();
		},
		methods: {
			init() {
				this.noticeList = [];
				for (let i = 0; i < this.msgList.length; i++) {
					this.noticeList.push(this.msgList[i].text)
				}
			},
			onConfirmHandle() {
				this.modal.show = false
			},
			onNoticeHandle(index) {
				if (!this.msgList.length) return;
				if (this.noticeList[index] != this.msgList[index].text) {
					uni.showToast({
						title: '资讯内容读取异常',
						icon: "none",
						duration: 2000
					});
					return;
				}
				if (this.msgList[index].type == 'link') {
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' +
							encodeURIComponent(this.msgList[index].url)
					})
				}
				if (this.msgList[index].type == 'alert') {
					this.modal.show = true;
					this.dislogMsg = this.msgList[index].text || ''
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.top-message {
		position: relative;
		height: 80rpx;
		margin: 20rpx 0;
		width: 100%;
		display: flex;
		justify-content: center;


		.notice {
			position: relative;
			width: 100%;
			background-color: #fff;
			z-index: 9;
			display: flex;
			height: 80rpx;
			align-items: center;
			border-radius: 10rpx;

			.noticeList {
				width: 100%;
			}

			.allNotice {
				font-size: 26rpx;
				font-weight: 400;
				color: #0066E9;
				width: 100rpx;
			}

			.message {
				margin-left: 30rpx;
			}

			.message-img {
				display: block;
				width: 72rpx;
				height: 34rpx;
			}
		}
	}

	.message-content-wrapper {
		.title {
			text-align: center;
			font-weight: 700;
			font-size: 34rpx;
			padding: 25rpx 50rpx;
			color: rgba(0, 0, 0, 0.9);
		}

		.msg_desc {
			padding: 0 30rpx;
			color: rgba(0, 0, 0, 0.5);
			font-size: 30rpx;
			text-align: left;

			.msg_desc-content {
				text-indent: 2em;
			}
		}
	}
</style>
