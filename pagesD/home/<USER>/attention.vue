<template>
	<view class="container"> 
		<TModal :showModal='dialogVisible' :showTitle='false' @cancelModal='cancelModal' @okModal='onConfirmHandle'
			okText='关注' :footerStyle="'background: #f6f6f6'">
			<view slot='content' class="attention">

				<!-- <view class="attention-hd">
					关注公众号，了解更多资讯
				</view> -->
				<view class="attention-bd">
					<view class="bd-box">
						<img src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/public-logo.png" class='img' alt="">
						<view class="desc">
							关注<text style="color: #2391e6;">广西捷通</text> 微信公众号,了解更多资讯
						</view>
					</view>
				</view>
				<view class="g-flex g-flex-align-center remember">
					<checkbox-group @change="checkboxChange">
						<checkbox class="cyan checked remember-check" value='checked' :checked='isRemember' />
					</checkbox-group>

					<view class="remember-des">
						不再提醒
					</view>
				</view>
			</view>
		</TModal>
	</view>
</template>

<script>
	import TModal from '@/components/t-modal/t-modal.vue'
	import {
		setStore,
		getStore
	} from '@/common/storageUtil.js'
	export default {
		data() {
			return {
				dialogVisible: false,
				isRemember: true
			}
		},
		components: {
			TModal
		},
		mounted() {
			let status = '';
			try {
				status = uni.getStorageSync('attentionStatus');
			} catch (e) {}
			if (!(status && status == '1')) {
				this.init();
			}

		},
		methods: {
			init() {
				let _self = this;
				console.log(getStore('launchGuide'),"getStore('launchGuide')")
				if(!getStore('launchGuide')) {
					return 
				}
				setStore('launchGuide','')
				this.$nextTick(() => {
					_self.dialogVisible = true;
				})
				// wx.login({
				// 	success(res) {
				// 		let params = {
				// 			code: res.code
				// 		}

				// 		_self.$request.post(_self.$interfaces.getOpenid, {
				// 			data: params
				// 		}).then((res) => {
				// 			if (res.code == 200 && res.data) {
				// 				_self.dialogVisible = !res.data.unionid;
				// 			}
				// 		})
				// 	}
				// })
			},
			cancelModal() {
				this.dialogVisible = false
				this.setStorageHandle();
			},
			onConfirmHandle() {
				this.dialogVisible = false
				this.setStorageHandle();
				var callcenter =
					'https://mp.weixin.qq.com/s?__biz=MzAxNjUxNDYwNg==&mid=2662543844&idx=1&sn=0d7c050f9f60c293f14b60bfc8a2b510&chksm=80b43aa5b7c3b3b3031c916d24f79def8d0e3a37c57064009efe76d754dc41d6e51807aabc70&token=1800816094&lang=zh_CN%23rd'
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' +
						encodeURIComponent(callcenter)
				})
			},
			checkboxChange(e) {
				this.isRemember = !!e.detail.value.length;

			},
			setStorageHandle() {
				let status = this.isRemember ? "1" : "0";
				try {
					uni.setStorageSync('attentionStatus', status)
				} catch (e) {

				}
			}
		}
	}
</script>

<style lang="scss" scoped>

	.attention {
		padding: 30rpx 30rpx 0 30rpx;
		background-color: #f6f6f6; 
	}

	.attention-hd {
		font-size: 34rpx; 
		margin-top: 30rpx;
		color: rgba(0, 0, 0, 0.5);
		text-align: center;
	}

	.attention-bd .bd-box .img {
		width: 165rpx;
		height: 180rpx;
		display: block;
		margin: 0 auto;
	}

	.attention-bd .bd-box .desc {
		margin: 30rpx 0 14rpx 0;
		font-size: 32rpx;
		color: #484647;
		font-weight: bold;
		text-align: center;
	}

	.attention .remember {
		padding: 20rpx 0rpx;
	}

	.attention .remember .remember-des {
		margin-left: 8rpx;
		font-size: 26rpx;
		color: rgba(0, 0, 0, 0.4);
	}

	.attention .remember .remember-check {
		transform: scale(0.7)
	}
</style>
