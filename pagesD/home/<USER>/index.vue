<template>
	<view class="all-business">
		<view class="commonly-box" v-for="(item, index) in allBusinessList" :key="index">
			<view class="sort-name">
				<view class="bar"></view> {{ item.sortName }}
			</view>
			<view class="commonly-used">
				<view class="commonly-list">
					<block v-for="(el, idx) in item.sortArr" :key="idx">
						<view class="card" :id="`card-${idx}`" v-if="!el.isNoVisible" @click="onMenuHandle(el)"
							@longtap.stop="longTap(el)" @touchend="touchend">
							<image :src="el.icon" style="width: 86rpx;height: 86rpx;" mode="monthlyBillt"></image>
							<view class="name">{{ el.name }}</view>
							<!-- 浮层 -->
							<view class="popups dark top-center" v-if="isPopupVisible(el)">
								<view class="mask mask-show" @tap.stop="tapMask(el)"></view>
								<!-- <text class="triangle bottom-center" /> -->
								<view class="popups-row" @click.stop="collect(el)">
									收藏
								</view>
							</view>
						</view>
					</block>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getTicket,
		getMd5Key,
		getAesKey,
		getLoginUserInfo,
		getCurrUserInfo
	} from "@/common/storageUtil.js";
	import {
		menuConfig,
		menuConfigS
	} from "@/components/home/<USER>";
	import {
		skipControlHandle
	} from "@/components/home/<USER>";
	import mapAPI from "@/common/api/map.js";

	export default {
		props: {
			vehicleList: {
				type: Array,
				default () {
					return [];
				}
			},
			type:{
				type:String,
				default:''
			}
		},
		data() {
			return {
				// allBusinessList: menuConfig,
				islongPress: false,
				popupStates: {}
			};
		},
		components: {},
		computed: {
			checkLoginStatus() {
				return !!(getTicket() && getMd5Key() && getAesKey() && this.getUserNo);
			},
			getUserNo() {
				return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length ?
					getLoginUserInfo().userNo :
					"";
			},
			currentCustomer() {
				return this.checkLoginStatus ? getCurrUserInfo() : {};
			},
			allBusinessList(){
				return this.type == 'ETC' ? menuConfigS : menuConfig
			}
		},
		mounted() {},
		methods: {
			customerChange() {
				uni.navigateTo({
					url: "/pagesB/accountBusiness/accountList/accountList"
				});
			},
			goLoginHandle() {
				uni.reLaunch({
					url: "/pagesD/login/p-login"
				});
			},
			onMenuSkipHandle(item) {
				if (item.extensionType && item.extensionType == "webview") {
					this.goWebviewHandle(item);
					return;
				}
				this.goPagesHandle(item);
			},
			// 验证登录
			validateLogin() {
				if (!this.checkLoginStatus) {
					uni.showModal({
						title: "提示",
						content: "请先登录",
						success: res => {
							if (res.confirm) {
								uni.reLaunch({
									url: "/pagesD/login/p-login"
								});
							}
						}
					});
					return;
				}
				return true;
			},
			// 验证绑定ETC用户
			validateBindAccount() {
				if (!(getCurrUserInfo() && getCurrUserInfo().customer_id)) {
					uni.showModal({
						title: "提示",
						content: "请先绑定ETC用户",
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: "/pagesB/accountBusiness/accountList/accountList"
								});
							}
						}
					});
					return;
				}
				return true;
			},
			// webview 跳转模式
			goWebviewHandle(item) {
				// 在线客服
				if (item.type == "onlineserver") {
					var callcenter =
						"https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027";
					uni.navigateTo({
						url: "/pages/uni-webview/uni-webview?ownPath=" +
							encodeURIComponent(callcenter)
					});
				}
			},
			toMiniProgram(type) {
				if (type == "platform") {
					// uni.navigateToMiniProgram({
					// 	appId: "wx1b6a17b21753c694",
					// 	path: "/pages/index/auth",
					// 	envVersion: "trial", //正式版
					// 	// extraData: params,
					// 	success(res) {}
					// });
					uni.showModal({
						title: "提示",
						content: '即将上线,敬请期待',
						showCancel: false
					})
				} else if (type == "rescue1") {
					// uni.navigateToMiniProgram({
					//   appId: "wx1b6a17b21753c694",
					//   path: "/pages/index/auth",
					//   envVersion: "trial", //正式版
					//   // extraData: params,
					//   success(res) {}
					// });
					uni.showModal({
						title: "提示",
						content: '一键救援服务即将上线，如需服务请拨打服务热线96333',
						success: (res) => {
							if (res.confirm) {
								uni.makePhoneCall({
									phoneNumber: '0771-96333' //仅为示例
								});
							}
						}
					})
				} 
			},
			// 小程序页面 跳转模式
			goPagesHandle(item) {
				// 根据菜单配置页面路由地址
				if (item.url) {
					uni.navigateTo({
						url: item.url
					});
					return;
				}
				// 特殊处理路由页面
				const specialType = ["afterPay"];

				if (item.type == "recharge") {
					let flag = !!this.vehicleList.length;
					let url = flag ?
						"/pagesB/rechargeBusiness/selectVehicle/index" :
						"/pagesB/rechargeBusiness/selectVehicle/index";
					uni.navigateTo({
						url: url + "?fontType=" + item.type
					});
					return;
				}
				if (specialType.includes(item.type) && !this.vehicleList.length) {
					uni.navigateTo({
						url: "/pagesB/vehicleBusiness/noVehicleList?fontType=" + item.type
					});
					return;
				}

				uni.navigateTo({
					url: "/pagesB/vehicleBusiness/vehicleList?fontType=" + item.type
				});
			},
			onMenuHandle(item) {
				if (this.islongPress) return;
				// 特勤业务 如车生活
				let type = item.type;
				let flag = skipControlHandle(item.type);
				if (!flag) return;
				if (type == "platform" || type == "rescue1") {
					this.toMiniProgram(type);
					return;
				}
				// 游客模式 无需验证登录
				if (item.validateRule == "visitor") {
					this.onMenuSkipHandle(item);
					return;
				}
				// 登录模式 无需绑定ETC用户
				if (item.validateRule == "login") {
					if (!this.validateLogin()) return;
					this.onMenuSkipHandle(item);
					return;
				}
				// ETC用户模式 需绑定ETC用户
				if (item.validateRule == "etcAccount") {
					if (!this.validateLogin()) return;
					if (!this.validateBindAccount()) return;
					this.onMenuSkipHandle(item);
					return;
				}
			},
			longTap(el) {
				console.log(el);
				if (!el.menuId) return;
				this.islongPress = true;
				this.$set(this.popupStates, el.menuId, true);
			},
			tapPopup(e) {
				console.log(e);
			},
			touchend() {
				//延时执行为了防止 click() 还未判断 islongPress 的值就被置为 fasle
				setTimeout(() => {
					this.islongPress = false;
				}, 200);
			},
			async collect(el) {
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					menuId: el.menuId
				};
				let res = await this.$request.post(mapAPI.addMenu, {
					data: params
				});
				if (res.code == 200) {
					console.log(res.data);
					this.tapMask(el);
					uni.showToast({
						title: "收藏成功",
						duration: 1000
					});
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 1000
					});
				}
			},
			tapMask(el) {
				console.log('tapMask called', el.menuId, this.popupStates[el.menuId]);
				if (!el.menuId) {
					console.log('Warning: No menuId found');
					return;
				}
				console.log('Setting popup state to false');
				this.$set(this.popupStates, el.menuId, false);
				// 强制更新组件，以确保状态变化被应用
				this.$forceUpdate();
			},
			isPopupVisible(el) {
				return this.popupStates[el.menuId] || false;
			}
		}
	};
</script>


<style lang="scss" scoped>
	.all-business {
		padding: 20rpx 20rpx 0 20rpx;

		.commonly-box {
			background-color: #fff;
			margin-bottom: 20rpx;
			padding: 20rpx 18rpx;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			position: relative;

			.sort-name {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #333333;
				margin-bottom: 10rpx;
				padding-left: 8rpx;
				font-weight: 500;

				.bar {
					width: 6rpx;
					height: 28rpx;
					background: #0066e9;
					border-radius: 20%;
					margin-right: 20rpx;
				}
			}

			.commonly-used {
				width: 100%;
				display: flex;
				flex-direction: column;

				.commonly-list {
					display: flex;
					flex-wrap: wrap;

					.card {
						display: flex;
						width: 25%;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						margin: 22rpx 0;
						position: relative;

						.name {
							font-weight: 400;
							color: #323435;
							font-size: 26rpx;
							margin-top: 12rpx;
						}

						.editIcon {
							position: absolute;
							right: 50rpx;
							top: -10rpx;
						}
					}
				}
			}
		}

		/** mask：遮罩 */
		.mask {
			position: fixed;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 8888;
			visibility: hidden;
			transition: background 0.3s ease-in-out;

			&.mask-show {
				visibility: visible;
			}
		}

		/** menu：菜单弹窗 */
		.popups {
			position: absolute;
			display: flex;
			align-items: center;
			// height: 45rpx;
			width: 120rpx;
			height: 100%;
			border-radius: 10px;
		}

		.popups-row {
			width: 145rpx;
			text-align: center;
			font-size: 24rpx;
			padding: 20rpx 0;
			z-index: 9999;
		}

		.triangle {
			width: 0px;
			height: 0px;
		}

		.dark {
			background-color: rgba(12, 10, 10, 0.7);
			color: #fff;
			top: 0;
			left: 26rpx;
			border-radius: 16rpx;
			font-weight: 400;

			.top-center:after {
				content: "";
				position: absolute;
				left: 47%;
				border-style: solid;
				border-color: transparent transparent #4c4c4c;
			}
		}
	}
</style>