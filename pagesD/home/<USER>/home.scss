.content {
  background-color: #fff;
  margin: 20rpx 0;
  padding: 20rpx 0;

  .commonlyUsed {
    width: 100%;
    display: flex;
    flex-direction: column;

    .commonly-top {
      display: flex;
      justify-content: space-between;
      padding: 20rpx;
      background-color: #fff;

      .title-icon {
        font-weight: 800;
        background-color: #0066E9;
        width: 8rpx;
        height: 26rpx;
        border-radius: 4rpx;
        margin-left: 10rpx;
      }

      .title {
        color: #666;
        font-size: 32rpx;
        font-weight: 600;
        padding-left: 12rpx;
      }

      .all {
        display: flex;
        color: #999999;
        font-size: 26rpx;
        align-items: center;

        .right-arr {
          margin-left: 10rpx;
        }
      }
    }

    .commonly-two {
      justify-content: normal;
      align-items: center;
    }

    .commonlyList {
      display: flex;
      padding-left: 20rpx;
      flex-wrap: wrap;

      .card {
        display: flex;
        width: 25%;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 22rpx 0;
        position: relative;

        .name {
          color: #666;
          font-weight: 400;
          font-size: 26rpx;
          margin-top: 8rpx;
        }

        .editIcon {
          position: absolute;
          right: 50rpx;
          top: -10rpx;
        }
      }
    }
  }
}

.index-top {
  display: flex;
  width: 100%;
  height: 130rpx;
  background-color: rgb(1, 193, 178);
  color: #fff;
  font-size: 32rpx;
  justify-content: center;
  align-items: center;

  .left-avatar {
    display: flex;
    align-items: center;
    padding-left: 48rpx;
    flex: 8;

    .wx-avatar {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
    }

    .wx-name {
      position: relative;
      margin-left: 15rpx;
    }
  }

  .wx-login {
    flex: 3;
    text-align: center;
    padding-right: 60rpx;
    font-size: 32rpx;
    font-weight: bold;
    text-align: right;
  }

  .authorization-btn-login {
    background-color: rgb(1, 193, 178);
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
    padding-right: 60rpx;

    &:after {
      border: none;
    }
  }
}

.home-img {
  height: 290rpx;
  width: 100%;
}

.modal {
  /deep/.neil-modal {
    .neil-modal__header {
      text {
        color: #000000 !important;
        background-color: #fff !important;
      }
    }

    .neil-modal__footer-left,
    .neil-modal__footer-right {
      color: #47a8ee !important;
      background-color: #fff;
      border: 1px solid #1d82d2;
      border-radius: 10rpx;
      height: 70rpx;
      line-height: 70rpx;
    }

    .neil-modal__footer-right {
      color: #fff !important;
      background-color: #1d82d2;
      border: 1px solid#1D82D2;
    }
  }

  .dialog {
    .user-btn {
      /deep/.cu-btn {
        position: relative;
        font-size: 26rpx;
        height: 90rpx;
      }
    }
  }
}

.start-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  .user-btn {
    width: 718rpx;
    // background-color: #1D82D2;
    color: #fff;
    font-size: 32rpx;

    /deep/.cu-btn {
      position: relative;
    }
  }
}

.index-info {
  height: 320rpx;
}

.bottom-card {
  background-color: #fff;
  width: 100%;
  min-height: 402rpx;
  position: relative;

  .bottom-card-title {
    font-size: 32rpx;
    color: #666;
    padding: 24rpx 0 24rpx 48rpx;
    font-weight: bold;

    &:before {
      content: '|';
      font-weight: bold;
      color: #0066E9;
      position: relative;
      right: 10rpx;
      top: -2rpx;
    }
  }

  .card-list {
    display: flex;
    flex-wrap: wrap;
  }
}

.i-card {
  background-color: #fff;
  width: 100%;
  height: 252rpx;
  margin: 24rpx 0px;

  .bottom-card-title {
    font-size: 32rpx;
    color: #666;
    padding: 24rpx 0 24rpx 48rpx;
    position: relative;
    font-weight: bold;

    &:before {
      content: '|';
      font-weight: bold;
      color: #0066E9;
      position: relative;
      right: 10rpx;
      top: -2rpx;
    }
  }

  .search-more {
    position: absolute;
    right: 60rpx;
    color: #0066E9;
    font-size: 28rpx;
  }

  .screen-swiper {
    height: 176rpx !important;
  }

  .home-card {
    background-color: #fff;

    /deep/.index-car-card {
      margin: 0;
      background-color: #fff;

      .tag {
        top: -4rpx;
      }

      .son-card {
        background-color: #1f77d0;
      }
    }
  }
}

.authorization-btn {
  position: absolute;
  width: 100%;
  height: 260rpx;
  opacity: 0;
  z-index: 9;
}

.authorization-btn:active {
  background: #ffffff;
  color: #0066E9;
  border: 1px solid #0066E9;
}

.nav-icon-index {
  color: #666;
  font-size: 28rpx;
  min-height: 120rpx;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-center;
  align-items: center;
  // justify-content: space-around;
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.nav-icon-index2 {
  color: #666;
  font-size: 28rpx;
  height: 120rpx;
  width: 100%;
  margin-top: 35rpx;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  align-items: center;
  justify-content: flex-start;
  padding: 0 20rpx;

  .nav-icon {
    width: 177rpx;
  }
}

.opacity {
  opacity: 0;
}

.nav-icon {
  border-radius: 5px;
  color: #666;
  font-size: 28rpx;
  height: 150rpx;
  width: 170rpx;
  text-align: center;
}

.nav-icon:active {
  background: #eee;
}

.sort1 {
  order: 3;
}

.sort2 {
  order: 2;
}

.sort3 {
  order: 1;
}

.sort4 {
  order: 4;
}

.nav-icon-image {
  margin-top: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: inline-block;
}

.nav-icon-text {
  text-align: center;
  color: #555555;
}

/deep/.index-car-card {
  margin-top: 0 !important;
}

.a-copyright-bottom,
.copyright-bottom {
  color: #666;
  width: 100%;
  text-align: center;
  font-size: 32rpx;
  position: fixed;
  bottom: 20upx;
}

.copyright-bottom {
  position: relative;
  margin-top: 24rpx;
}

.a-copyright {
  color: #666;
  width: 100%;
  text-align: center;
  font-size: 32rpx;
  margin-top: 20rpx;
}

.content-wrapper {
	.title {
		text-align: left;
		font-size: 30rpx;
		padding: 25rpx 50rpx;
	}

	.desc {
		text-indent: 2em;
		padding: 0 30rpx;
		line-height: 42rpx;
		font-size: 28rpx;
		text-align: left;
	}
}

/deep/ .neil-modal__container {
	width: 80%;
}

/deep/ .neil-modal__footer-left {
	border-radius: 12rpx;
	background-color: #ffffff;
	border-width: 1rpx;
	border-style: solid;
	border-color: #0066E9;
	color: #0066E9 !important;
	height: 60rpx;
	line-height: 60rpx;
}

/deep/ .neil-modal__footer-right {
	border-radius: 12rpx;
	color: #ffffff;
	background-color: #0066E9;
	height: 60rpx;
	line-height: 60rpx;
}

.checkbox-wrapper{
	margin-top: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}