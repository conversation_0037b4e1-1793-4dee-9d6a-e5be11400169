<template>
  <view class="msg-item">
    <view class="msg-type">{{ itemInfo.messageTypeStr || messageTypeStr(itemInfo.messageType) }} </view>
    <view class="msg-content">{{ itemInfo.messageInfo }} </view>
    <view class="msg-time-handle">
      <view class="msg-time">{{ itemInfo.messageTime }}</view>
      <view
        class="msg-handle"
        v-if="itemInfo.messageType != 2"
        @click="swtichPage"
        >{{ messageType[itemInfo.messageType].btn }}</view
      >
    </view>
  </view>
</template>

<script>
export default {
  props: {
    itemInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      messageType: {
        1: {
          name: "限制通行",
          btn: "点击跳转"
        },
        2: {
          name: "限制通行解除"
        },
        3: {
          name: "充值",
          btn: "去充值"
        },
        4: {
          name: "补缴",
          btn: "去补缴"
        },
        5: {
          name: "ETC消费服务评价提醒",
          btn: "去评价"
        },
        6: {
          name: "救援订单评价消息",
          btn: "前往评价"
        }
      }
    };
  },
  methods: {
    messageTypeStr(type) {
      return this.messageType[type].name;
    },
    swtichPage() {
      let type = this.itemInfo.messageType;
      switch (type) {
        case 1:
          //查看解除办法
          //黑名单
          let url = "https://mp.weixin.qq.com/s/xl1xw0JhaNsdmXcQJXE1Pg";
          uni.navigateTo({
            url:
              "/pages/uni-webview/h5-webview?ownPath=" +
              encodeURIComponent(url) +
              "&title=解除黑名单"
          });
          break;
        case 3:
          //前往充值
          uni.navigateTo({
            url:
              "/pagesB/rechargeBusiness/selectVehicle/index?fontType=" +
              "recharge"
          });
          break;
        case 4:
          //前往补缴页面
          //有车辆去车辆列表
          uni.navigateTo({
            url: "/pagesB/vehicleBusiness/vehicleList?fontType=" + "afterPay"
          });
          break;
        case 5:
          //前往消费详情
          uni.navigateTo({
            url: "/pagesB/cardBusiness/expenseRecord/record-detail?id=" + this.itemInfo.otherId + "&evaluationMode=show"
          });
          break;
        case 6:
          //前往救援订单详情
          uni.navigateTo({
            url: "/pagesD/travelService/rescue/order-detail?id=" + this.itemInfo.otherId + "&showEvaluation=true"
          });
          break;
        default:
          break;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.msg-item {
  width: 710rpx;
  background: #ffffff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  margin-bottom: 20rpx;
  padding-bottom: 34rpx;
  .msg-type {
    width: 100%;
    height: 76rpx;
    padding: 20rpx 0 16rpx 18rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    border-bottom: 2rpx solid #f0f0f0;
  }
  .msg-content {
    padding: 28rpx 30rpx 20rpx 30rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
  }
  .msg-time-handle {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 30rpx;
    .msg-time {
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      margin-right: 24rpx;
    }
    .msg-handle {
      height: 46rpx;
      line-height: 46rpx;
      background: rgba(79, 144, 255, 0.1);
      border-radius: 8rpx;
      border: 1rpx solid rgba(79, 144, 255, 0.61);
      padding: 0 12rpx;
      font-size: 24rpx;
      color: #4f90ff;
      text-align: center;
    }
  }
}
</style>