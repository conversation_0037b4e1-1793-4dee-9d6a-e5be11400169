<!-- 主页 -->
<template>
	<view class="tabbarIndex home">
		<!-- 顶部导航栏信息 -->
		<headerBox :customerInfo='customerInfo' @on-style='nextEleStyle'></headerBox>
		<!-- 广告资讯业务 -->
		<view style="padding: 0 20rpx;" :style="[eleStyle]" v-if="isActivate">
			<adColumn type='2'></adColumn>
			<message></message>
		</view>
		<!-- 车辆业务 -->
		<vehicleBox :vehicleList='vehicleAllDataList' :customerInfo='customerInfo' @on-change='vehicleChange'
			@goNewApply="goNewApply"></vehicleBox>
		<!-- 常用功能栏 -->
		<!-- <commonly ref="commonly" :vehicleList='vehicleAllDataList'></commonly> -->
		<allBusiness ref="allBusiness" type="ETC" :vehicleList='vehicleAllDataList'></allBusiness>

		<!-- 在线客服功能 -->
		<customerService></customerService>

		<view :class="isRelative ? 'copyright-bottom' : 'a-copyright-bottom'" v-if='false'>

			<view style="font-size: 28upx;">当前版本：2.6.2</view>
			<view class="">广西捷通高速科技有限公司</view>
		</view>
		<attention></attention>
		<tLoading :isShow="isLoading" />
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:confirm-text="modal.confirmText" @confirm="remindConfirm">
			<view class="content-wrapper">
				<view class="title">提醒</view>
				<view class="desc" style="margin-bottom: 8rpx;">捷通公司对公充值账户已升级为平安银行账户，您的专属账户信息可在“我的账号”-“对公转账充值账号查询”菜单中查询。
				</view>
				<view class="desc">
					可通过网上银行、手机银行、ATM、银行柜台向此账户转账，充值款预计两小时内到账，无需提供转账回执单。
				</view>
				<checkbox-group @change="remindCheckboxChange">
					<label class="checkbox-wrapper">
						<view>
							<checkbox style="transform:scale(0.7)" value="isRemind" />
						</view>
						<view>不再提示</view>
					</label>
				</checkbox-group>
			</view>
		</neil-modal>
		<view class="bottom" style="height:120rpx">
			<tabbar current="etcService" />
		</view>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!-- 广告 -->
		<advertisement></advertisement>
		<!--  #endif -->
	</view>
</template>

<script>
	import {
		menuConfig,
		entranceConfig,
		defaultAvatar
	} from './home.js';
	import {
		skipControlHandle
	} from '@/components/home/<USER>';
	import CarCard from '../../components/car-card.vue'
	import tLoading from '@/components/common/t-loading.vue'
	import neilModal from '@/components/neil-modal/neil-modal.vue'
	import allBusiness from '../all-business/index.vue'
	import {
		getTicket,
		getMd5Key,
		getAesKey,
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getCurrUserInfo,
		setCurrUserInfo,
		getMustFresh,
		setMustFresh,
		setWxAccount,
		setOpenid,
		getOpenid,
		setSessionKey,
		getAccountId,
		setAccountId,
		clearReturnCode,
		setBusinessTypes,
		setChannelId,
		getChannelId,
		setStore,
		getStore,
		getDecodingKey,
		setProductInfo,
		removeStore,
		setEtcAccount,
		getLoginUserInfo,
		setDefaultUrl,
	} from '@/common/storageUtil.js'
	import store from 'store/index.js'
	import {
		uni_encodeURIComponent
	} from '@/common/helper.js'
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import {
		carObuInfo,
		getCarInfo
	} from '@/common/request-1/requestFor4501.js'
	import {
		request,
		getCarList,
		carStatus
	} from '@/common/request-1/requestFunc.js'
	import attention from './attention';
	import tabbar from "@/components/base/tab-bar/index.vue"
	import {
		onlineStoreFn
	} from '@/components/base/redirect/index.js';
	import headerBox from './component/header.vue';
	import commonly from './component/commonly.vue';
	import vehicleBox from './component/vehicle.vue';
	import message from '../../components/adInfo/message.vue'
	import adColumn from '@/components/home/<USER>'
	import advertisement from './component/advertisement.vue'
	export default {
		components: {
			CarCard,
			neilModal,
			attention,
			tLoading,
			tabbar,
			message,
			headerBox,
			commonly,
			vehicleBox,
			advertisement,
			adColumn,
			allBusiness
		},
		data() {
			return {
				isAttention: false,
				isLoading: false,
				topLoading: false,
				isBtnLoader: false,
				isRelative: false,
				isTokenId: '',
				isLoginning: false,
				copyrightPosition: false,
				vehicleNum: 0,
				carInfoList: [],
				vehicleStatusList: [],
				vehicleAllDataList: [],
				accountList: [],
				userInfo: {},
				showDialog: false,
				isGetUserInfo: false,
				persionAccountId: '', //个人账户accountId
				isCorp: false,
				btnClicked: false,
				inputPwd: '',
				payShow: true,
				customerInfo: {},
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					confirmText: '确认'
				},
				remindFlag: [],
				commonlyList: entranceConfig,
				defaultAvatar,
				eleStyle: {},
				isActivate: true
			}
		},
		computed: {
			isLoginStatus() {
				return getTicket() !== '' && getMd5Key() !== '' && getAesKey() !== ''
			},
			...mapGetters('dict', ['gxCardTypeAllOptions'])
		},
		onLoad(query) {
			//平安账户提醒判断
			if (query.type && query.type == 'login') {
				let closeRemind = '0'
				this.getAccountRemind(closeRemind)
			}
		},
		created() {
			if ((getAesKey() && getMd5Key() && getTicket()) && this.gxCardTypeAllOptions.length == 0) {
				this.$store.dispatch(
					'dict/getAllOptions', {
						businessType: 'CARD_TYPE',
						ruleType: "ALL"
					}
				)
			}
			setDefaultUrl('/pages/home/<USER>/p-home')
		},
		onShow() {
			this.isActivate = true
			if (getTicket()) {
				this.getBindUserInfo()
			}
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif

		},
		onHide() {
			this.isActivate = false
		},
		onReady() {},
		onPullDownRefresh() {},
		onShareAppMessage(res) {},

		methods: {
			nextEleStyle(style) {
				this.eleStyle = style;
			},
			vehicleChange(data) {
				this.vehicleAllDataList = data;
			},
			//点击去新办
			goNewApply() {
				let applyIndex = menuConfig.findIndex((item) => {
					return item.type == 'newApply';
				});
				let item = {}
				if (applyIndex != -1) {
					item = menuConfig[applyIndex]
				}
				this.$refs.commonly.onMenuHandle(item)
			},
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}
						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									setOpenid(res.data.openid)

								}

							}
						})
					}
				})
			},

			onlineStoreHandle() {
				onlineStoreFn();
			},
			//获取平安账户列表详情
			getAccountRemind(closeRemind) {
				let data = {
					routePath: this.$interfaces.accountRemind.method,
					bizContent: {
						userNo: getLoginUserInfo().userNo,
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log('res', res)
						if (res.code == 200) {
							if (res.data.isRemind == '0') {
								this.modal.show = true
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			remindCheckboxChange(e) {
				this.remindFlag = e.detail.value
			},
			//平安账户不再提示
			remindConfirm() {
				this.modal.show = false
				if (this.remindFlag.length > 0) {
					//关闭提示
					let data = {
						routePath: this.$interfaces.accountRemind.method,
						bizContent: {
							userNo: getLoginUserInfo().userNo,
							closeRemind: '1'
						}
					}
					this.$request
						.post(this.$interfaces.issueRoute, {
							data: data
						})
						.then((res) => {})
						.catch((error) => {})
				}
			},
			goAllBusinessHandle() {
				uni.navigateTo({
					url: '/pagesA/allBusiness/allBusiness'
				})
			},
			goOtherBusinessHandle(item) {
				let type = item.type
				let flag = skipControlHandle(item.type);
				if (!flag) return;

				console.log('type', type)

				if (type == 'myAccount') {
					uni.navigateTo({
						url: '/pagesB/personal/more/p-more'
					})
					return
				}
				if (!getTicket()) {
					uni.showModal({
						title: '提示',
						content: '请先登录',
						success: (res) => {
							if (res.confirm) {
								uni.reLaunch({
									url: '/pagesD/login/p-login'
								})
							}
						}
					})
					return
				}
				// 读卡圈存
				if (type == 'onlineserver') {
					// var callcenter = "http://ccrm.wengine.cn/online.html?tntInstId=HSYQGXGS&scene=SCE0000027";
					var callcenter =
						'https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027'
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' +
							encodeURIComponent(callcenter)
					})
					return
				}
				if (type == 'loadBusiness') {
					uni.navigateTo({
						url: '/pagesB/loadBusiness/loadType'
					})
					return
				}
				if (type == 'allBusiness') {
					uni.navigateTo({
						url: '/pagesA/allBusiness/allBusiness'
					})
					return
				}

				if (type == 'recharge') {
					let flag = !!this.vehicleAllDataList.length
					let url = flag ?
						'/pagesB/rechargeBusiness/selectVehicle/index' :
						// '/pagesB/vehicleBusiness/noVehicleList'
						'/pagesB/rechargeBusiness/selectVehicle/index'
					uni.navigateTo({
						url: url + '?fontType=' + type
					})
					return
				}
				if (type == 'afterPay') {
					console.log('this.vehicleAllDataList', this.vehicleAllDataList)
					if (this.vehicleAllDataList.length > 0) {
						//有车辆去车辆列表
						uni.navigateTo({
							url: '/pagesB/vehicleBusiness/vehicleList?fontType=' + 'afterPay'
						})
					} else {
						//无车去无车辆列表
						uni.navigateTo({
							url: '/pagesB/vehicleBusiness/noVehicleList?fontType=' + 'afterPay'
						})
					}
					return
				}

				if (!(getCurrUserInfo() && getCurrUserInfo().customer_id)) {
					uni.showModal({
						title: '提示',
						content: '请先绑定ETC用户',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pagesB/accountBusiness/accountList/accountList'
								})
							}
						}
					})
					return
				}
				// ECSS账户迁移
				if (type == 'ecssSync') {
					uni.navigateTo({
						url: '/pagesB/ecssBusiness/index'
					})
					return
				}
				// 服务列表
				if (type == 'serviceOrder') {
					uni.navigateTo({
						url: '/pagesA/serviceOrder/index'
					})
					return
				}
				// 设备售后
				if (type == 'afterSale') {
					uni.navigateTo({
						url: '/pagesC/afterSaleBusiness/vehicleList/index'
					})
					return
				}
				if (type == 'rechargeList') {
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/selectVehicle/index?fontType=' + type
					})
					return
				}
				//签约信息
				// if(type=='signature'){
				// 	uni.navigateTo({
				// 		url:'../../../pagesC/userAgreement/agreementList'
				// 	})
				// 	return
				// }
				//优惠券
				if (type == 'coupon') {
					uni.navigateTo({
						url: '/pagesC/coupons/selectCoupons/selectCoupons'
					})
					return
				}
				if (type == 'receipt') {
					uni.navigateTo({
						url: '/pagesC/afterSale/businessReceipt/businessReceipt'
					})
					return
				}
				if (type == 'updateDevice') {
					uni.navigateTo({
						url: '/pagesB/deviceBusiness/entry/entry'
					})
					return
				}
				if (type) {
					uni.navigateTo({
						// url: '/pagesB/personal/my-car/p-my-car?fontType=' + type

						url: '/pagesB/vehicleBusiness/vehicleList?fontType=' + type
					})
				}
			},
			intervalChange(e) {
				console.log(e.target)
			},
			getVerhicleList() {
				if (!getAccountId()) {
					this.isLoading = false
					return
				}
				this.isLoading = true
				setCurrUserInfo({})
				let data = {
					routePath: this.$interfaces.customerBizView.method,
					bizContent: {
						customer_id: getAccountId()
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log('111111', res.dta)
						this.isAttention = true
						this.isLoading = false
						setCurrUserInfo(res.data)
						this.customerInfo = res.data
						if (!res.data.customer_id) return
						this.getAllVerhicleList(res.data.customer_id)
					})
					.catch((error) => {
						this.isLoading = false
					})
			},

			getAllVerhicleList(id) {
				console.log(id, '-----');
				let params = {
					customerId: id
				}
				this.$request
					.post(this.$interfaces.vehicleList, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.vehicleAllDataList = res.data || []
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
			},
			customerChange() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList'
				})
			},
			// 我的车辆
			goMyCarHandle() {
				uni.navigateTo({
					url: '/pagesB/vehicleBusiness/vehicleList'
				})
			},

			goLoginHandle() {
				uni.navigateTo({
					url: '/pagesD/login/p-login'
				})
			},

			//计算底部高度
			bottomHeight() {
				uni.getSystemInfo({
					success: (res) => {
						// res - 各种参数
						let top = uni.createSelectorQuery().select('.top')
						top
							.boundingClientRect((data) => {
								this.isRelative = false
								if (
									data &&
									data.height &&
									res.windowHeight - data.height - 63 < 30
								) {
									this.isRelative = true
								}
							})
							.exec()
					}
				})
			},

			//设置登录后的默认账户
			getBindUserInfo() {
				let data = {}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.getEtcAccountList, data)
					.then((res) => {
						console.log(res, '11111')
						if (res.code == 200) {
							this.accountList = []
							this.accountList = res.data || []
							setCurrUserInfo({})
							setEtcAccount({})
							setAccountId()
							let bindItem = this.accountList.filter((item) => {
								if (item.isDefault == '2') {
									return item
								}
							})
							if (bindItem.length != 0) {
								setEtcAccount(bindItem[0])
								setAccountId(bindItem[0].custMastId)
							}
							this.getVerhicleList()
							this.accountId = getAccountId() || ''
							let countNum = this.accountList.length
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						console.log(error, '11111')
						this.isLoading = false
					})
			}
		}
	}
</script>
<style lang="scss" scoped>
	.content {
		background-color: #fff;
		margin: 20rpx 0;
		padding: 20rpx 0;

		.commonlyUsed {
			width: 100%;
			display: flex;
			flex-direction: column;

			.commonly-top {
				display: flex;
				justify-content: space-between;
				padding: 20rpx;
				background-color: #fff;

				.title-icon {
					font-weight: 800;
					background-color: #0066E9;
					width: 8rpx;
					height: 26rpx;
					border-radius: 4rpx;
					margin-left: 10rpx;
				}

				.title {
					color: #666;
					font-size: 32rpx;
					font-weight: 600;
					padding-left: 12rpx;
				}

				.all {
					display: flex;
					color: #999999;
					font-size: 26rpx;
					align-items: center;

					.right-arr {
						margin-left: 10rpx;
					}
				}
			}

			.commonly-two {
				justify-content: normal;
				align-items: center;
			}

			.commonlyList {
				display: flex;
				padding-left: 20rpx;
				flex-wrap: wrap;

				.card {
					display: flex;
					width: 25%;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					margin: 22rpx 0;
					position: relative;

					.name {
						color: #666;
						font-weight: 400;
						font-size: 26rpx;
						margin-top: 8rpx;
					}

					.editIcon {
						position: absolute;
						right: 50rpx;
						top: -10rpx;
					}
				}
			}
		}
	}

	.index-top {
		display: flex;
		width: 100%;
		height: 130rpx;
		background-color: rgb(1, 193, 178);
		color: #fff;
		font-size: 32rpx;
		justify-content: center;
		align-items: center;

		.left-avatar {
			display: flex;
			align-items: center;
			padding-left: 48rpx;
			flex: 8;

			.wx-avatar {
				width: 88rpx;
				height: 88rpx;
				border-radius: 50%;
			}

			.wx-name {
				position: relative;
				margin-left: 15rpx;
			}
		}

		.wx-login {
			flex: 3;
			text-align: center;
			padding-right: 60rpx;
			font-size: 32rpx;
			font-weight: bold;
			text-align: right;
		}

		.authorization-btn-login {
			background-color: rgb(1, 193, 178);
			color: #fff;
			font-size: 32rpx;
			font-weight: bold;
			padding-right: 60rpx;

			&:after {
				border: none;
			}
		}
	}

	.home-img {
		height: 290rpx;
		width: 100%;
	}

	.modal {
		/deep/.neil-modal {
			.neil-modal__header {
				text {
					color: #000000 !important;
					background-color: #fff !important;
				}
			}

			.neil-modal__footer-left,
			.neil-modal__footer-right {
				color: #47a8ee !important;
				background-color: #fff;
				border: 1px solid #1d82d2;
				border-radius: 10rpx;
				height: 70rpx;
				line-height: 70rpx;
			}

			.neil-modal__footer-right {
				color: #fff !important;
				background-color: #1d82d2;
				border: 1px solid#1D82D2;
			}
		}

		.dialog {
			.user-btn {
				/deep/.cu-btn {
					position: relative;
					font-size: 26rpx;
					height: 90rpx;
				}
			}
		}
	}

	.start-btn {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;

		.user-btn {
			width: 718rpx;
			// background-color: #1D82D2;
			color: #fff;
			font-size: 32rpx;

			/deep/.cu-btn {
				position: relative;
			}
		}
	}

	.index-info {
		height: 320rpx;
	}

	.bottom-card {
		background-color: #fff;
		width: 100%;
		min-height: 402rpx;
		position: relative;

		.bottom-card-title {
			font-size: 32rpx;
			color: #666;
			padding: 24rpx 0 24rpx 48rpx;
			font-weight: bold;

			&:before {
				content: '|';
				font-weight: bold;
				color: #0066E9;
				position: relative;
				right: 10rpx;
				top: -2rpx;
			}
		}

		.card-list {
			display: flex;
			flex-wrap: wrap;
		}
	}

	.i-card {
		background-color: #fff;
		width: 100%;
		height: 252rpx;
		margin: 24rpx 0px;

		.bottom-card-title {
			font-size: 32rpx;
			color: #666;
			padding: 24rpx 0 24rpx 48rpx;
			position: relative;
			font-weight: bold;

			&:before {
				content: '|';
				font-weight: bold;
				color: #0066E9;
				position: relative;
				right: 10rpx;
				top: -2rpx;
			}
		}

		.search-more {
			position: absolute;
			right: 60rpx;
			color: #0066E9;
			font-size: 28rpx;
		}

		.screen-swiper {
			height: 176rpx !important;
		}

		.home-card {
			background-color: #fff;

			/deep/.index-car-card {
				margin: 0;
				background-color: #fff;

				.tag {
					top: -4rpx;
				}

				.son-card {
					background-color: #1f77d0;
				}
			}
		}
	}

	.authorization-btn {
		position: absolute;
		width: 100%;
		height: 260rpx;
		opacity: 0;
		z-index: 9;
	}

	.authorization-btn:active {
		background: #ffffff;
		color: #0066E9;
		border: 1px solid #0066E9;
	}

	.nav-icon-index {
		color: #666;
		font-size: 28rpx;
		min-height: 120rpx;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-center;
		align-items: center;
		// justify-content: space-around;
		padding: 0 20rpx;
		margin-bottom: 30rpx;
	}

	.nav-icon-index2 {
		color: #666;
		font-size: 28rpx;
		height: 120rpx;
		width: 100%;
		margin-top: 35rpx;
		display: flex;
		flex-wrap: wrap;
		align-content: center;
		align-items: center;
		justify-content: flex-start;
		padding: 0 20rpx;

		.nav-icon {
			width: 177rpx;
		}
	}

	.opacity {
		opacity: 0;
	}

	.nav-icon {
		border-radius: 5px;
		color: #666;
		font-size: 28rpx;
		height: 150rpx;
		width: 170rpx;
		text-align: center;
	}

	.nav-icon:active {
		background: #eee;
	}

	.sort1 {
		order: 3;
	}

	.sort2 {
		order: 2;
	}

	.sort3 {
		order: 1;
	}

	.sort4 {
		order: 4;
	}

	.nav-icon-image {
		margin-top: 20rpx;
		width: 60rpx;
		height: 60rpx;
		display: inline-block;
	}

	.nav-icon-text {
		text-align: center;
		color: #555555;
	}

	/deep/.index-car-card {
		margin-top: 0 !important;
	}

	.a-copyright-bottom,
	.copyright-bottom {
		color: #666;
		width: 100%;
		text-align: center;
		font-size: 32rpx;
		bottom: 20upx;
	}

	.copyright-bottom {
		position: relative;
		margin-top: 24rpx;
	}

	.a-copyright {
		color: #666;
		width: 100%;
		text-align: center;
		font-size: 32rpx;
		margin-top: 20rpx;
	}

	.content-wrapper {
		.title {
			text-align: left;
			font-size: 30rpx;
			padding: 25rpx 50rpx;
		}

		.desc {
			text-indent: 2em;
			padding: 0 30rpx;
			line-height: 42rpx;
			font-size: 28rpx;
			text-align: left;
		}
	}

	/deep/ .neil-modal__container {
		width: 80%;
	}

	/deep/ .neil-modal__footer-left {
		border-radius: 12rpx;
		background-color: #ffffff;
		border-width: 1rpx;
		border-style: solid;
		border-color: #0066E9;
		color: #0066E9 !important;
		height: 60rpx;
		line-height: 60rpx;
	}

	/deep/ .neil-modal__footer-right {
		border-radius: 12rpx;
		color: #ffffff;
		background-color: #0066E9;
		height: 70rpx;
		line-height: 70rpx;
	}

	.checkbox-wrapper {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>