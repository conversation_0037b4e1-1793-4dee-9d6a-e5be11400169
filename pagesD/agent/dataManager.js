/**
 * 分层数据架构管理器
 * 用于精确控制 Vue 响应式数据范围，减少 vdSync 传输量
 */
import TypingStateManager from './TypingStateManager.js'

class DataLayerManager {
  constructor() {
    // 核心业务数据层（保持响应式）
    this.coreData = {
      messages: [],
      inputText: '',
      isRecording: false,
      isAiTyping: false,
      maxMessages: 20
    }
    
    // 渲染层数据（选择性响应式）
    this.renderData = {
      scrollTop: 0,
      keyboardHeight: 0,
      isNearBottom: true,
      lastScrollTop: 0,
      isAutoScrolling: true
    }
    
    // 临时状态层（非响应式）
    this.tempData = {
      // 使用独立的打字状态管理器
      typingManager: TypingStateManager,
      
      // 动画帧管理（非响应式）
      animationFrames: new Set(),
      
      // 定时器管理（非响应式）
      timers: {
        intervals: new Set(),
        timeouts: new Set()
      },
      
      // 组件销毁标记
      _isDestroyed: false,
      
      // 性能监控数据（非响应式）
      performanceStats: {
        startTime: 0,
        typingCount: 0,
        memoryUsage: [],
        lastMemoryCheck: 0,
        memoryCheckInterval: 5000
      }
    }
    
    // 缓存层数据（非响应式）
    this.cacheData = {
      // DOM查询缓存
      domInfo: {
        containerHeight: 0,
        scrollViewHeight: 0,
        lastCacheTime: 0,
        cacheTimeout: 1000
      },
      
      // 消息卡片数据缓存
      messageCards: new Map(),
      
      // 富文本解析缓存
      tokenCache: new Map(),
      
      // 最大缓存条目数
      maxCacheSize: 50
    }
    
    // 配置数据
    this.config = {
      // 打字效果配置
      typing: {
        speed: 50,
        chunkSize: 5,
        maxMemoryUsage: 50 * 1024 * 1024
      },
      
      // 缓存配置
      cache: {
        domTimeout: 1000,
        tokenTimeout: 5000,
        maxCardCache: 20
      },
      
      // 性能配置
      performance: {
        memoryCheckInterval: 5000,
        maxMemoryHistory: 10,
        cleanupThreshold: 0.8
      }
    }
  }
  
  /**
   * 初始化数据管理器
   * @param {Object} vueComponent Vue组件实例
   */
  initialize(vueComponent) {
    this.vueComponent = vueComponent
    
    // 设置响应式数据
    this.setupReactiveData()
    
    // 初始化非响应式管理器
    this.setupNonReactiveManagers()
    
    console.log('数据分层管理器初始化完成')
  }
  
  /**
   * 设置响应式数据
   */
  setupReactiveData() {
    if (!this.vueComponent) return
    
    // 将核心业务数据添加到Vue响应式系统
    Object.keys(this.coreData).forEach(key => {
      if (!(key in this.vueComponent.$data)) {
        this.vueComponent.$set(this.vueComponent, key, this.coreData[key])
      }
    })
    
    // 将渲染层数据添加到Vue响应式系统
    Object.keys(this.renderData).forEach(key => {
      if (!(key in this.vueComponent.$data)) {
        this.vueComponent.$set(this.vueComponent, key, this.renderData[key])
      }
    })
  }
  
  /**
   * 设置非响应式管理器
   */
  setupNonReactiveManagers() {
    // 初始化打字状态管理器
    this.tempData.typingManager.updateConfig(this.config.typing)
    
    // 设置性能监控
    this.startPerformanceMonitoring()
  }
  
  /**
   * 获取核心数据
   * @param {string} key 数据键
   */
  getCoreData(key) {
    if (this.vueComponent && key in this.vueComponent.$data) {
      return this.vueComponent[key]
    }
    return this.coreData[key]
  }
  
  /**
   * 设置核心数据
   * @param {string} key 数据键
   * @param {*} value 数据值
   */
  setCoreData(key, value) {
    if (this.vueComponent && key in this.vueComponent.$data) {
      this.vueComponent[key] = value
    } else {
      this.coreData[key] = value
    }
  }
  
  /**
   * 获取渲染数据
   * @param {string} key 数据键
   */
  getRenderData(key) {
    if (this.vueComponent && key in this.vueComponent.$data) {
      return this.vueComponent[key]
    }
    return this.renderData[key]
  }
  
  /**
   * 设置渲染数据
   * @param {string} key 数据键
   * @param {*} value 数据值
   */
  setRenderData(key, value) {
    if (this.vueComponent && key in this.vueComponent.$data) {
      this.vueComponent[key] = value
    } else {
      this.renderData[key] = value
    }
  }
  
  /**
   * 获取临时数据
   * @param {string} key 数据键
   */
  getTempData(key) {
    return this.tempData[key]
  }
  
  /**
   * 设置临时数据
   * @param {string} key 数据键
   * @param {*} value 数据值
   */
  setTempData(key, value) {
    this.tempData[key] = value
  }
  
  /**
   * 消息管理方法
   */
  
  /**
   * 添加消息
   * @param {Object} message 消息对象
   */
  addMessage(message) {
    const messages = this.getCoreData('messages')
    messages.push(message)
    
    // 限制消息数量
    this.limitMessages()
  }
  
  /**
   * 限制消息数量
   */
  limitMessages() {
    const messages = this.getCoreData('messages')
    const maxMessages = this.getCoreData('maxMessages')
    
    if (messages.length > maxMessages) {
      // 获取要删除的消息
      const messagesToRemove = messages.slice(0, messages.length - maxMessages)
      
      // 清理对应的卡片数据和缓存
      messagesToRemove.forEach(message => {
        this.removeMessageCache(message.id)
      })
      
      // 只保留最新的消息
      messages.splice(0, messages.length - maxMessages)
    }
  }
  
  /**
   * 卡片数据管理
   */
  
  /**
   * 设置消息卡片数据
   * @param {string} messageId 消息ID
   * @param {Array} cards 卡片数组
   */
  setMessageCards(messageId, cards) {
    // 检查缓存大小
    if (this.cacheData.messageCards.size >= this.config.cache.maxCardCache) {
      this.cleanupOldestCardCache()
    }
    
    this.cacheData.messageCards.set(messageId, {
      cards: cards,
      timestamp: Date.now()
    })
  }
  
  /**
   * 获取消息卡片数据
   * @param {string} messageId 消息ID
   */
  getMessageCards(messageId) {
    const cacheItem = this.cacheData.messageCards.get(messageId)
    if (cacheItem) {
      // 更新访问时间
      cacheItem.lastAccess = Date.now()
      return cacheItem.cards
    }
    return []
  }
  
  /**
   * 移除消息缓存数据
   * @param {string} messageId 消息ID
   */
  removeMessageCache(messageId) {
    this.cacheData.messageCards.delete(messageId)
    this.cacheData.tokenCache.delete(messageId)
    this.tempData.typingManager.removeState(messageId)
  }
  
  /**
   * 清理最老的卡片缓存
   */
  cleanupOldestCardCache() {
    let oldestMessageId = null
    let oldestTime = Infinity
    
    for (const [messageId, cacheItem] of this.cacheData.messageCards) {
      const accessTime = cacheItem.lastAccess || cacheItem.timestamp
      if (accessTime < oldestTime) {
        oldestTime = accessTime
        oldestMessageId = messageId
      }
    }
    
    if (oldestMessageId) {
      this.cacheData.messageCards.delete(oldestMessageId)
    }
  }
  
  /**
   * 打字效果管理
   */
  
  /**
   * 开始打字效果
   * @param {Object} message 消息对象
   * @param {Function} onUpdate 更新回调
   * @param {Function} onComplete 完成回调
   */
  startTypingEffect(message, onUpdate, onComplete) {
    const tokens = message.isHtml ? this.parseHtmlTokens(message.content) : null
    
    const config = {
      content: message.content,
      isHtml: message.isHtml,
      tokens: tokens,
      onUpdate: onUpdate,
      onComplete: onComplete
    }
    
    this.tempData.typingManager.addState(message.id, config)
    this.tempData.typingManager.startTyping(message.id)
  }
  
  /**
   * 解析HTML内容为令牌（带缓存）
   * @param {string} htmlContent HTML内容
   */
  parseHtmlTokens(htmlContent) {
    // 创建缓存键
    const cacheKey = this.hashString(htmlContent)
    
    // 检查缓存
    const cached = this.cacheData.tokenCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.config.cache.tokenTimeout) {
      return cached.tokens
    }
    
    // 解析HTML标签和文本
    const tokens = htmlContent.match(/<[^>]+>|[^<]+/g) || []
    const parsedTokens = tokens.map(token => ({
      content: token,
      isTag: token.startsWith('<'),
      processed: false
    }))
    
    // 存入缓存
    this.cacheData.tokenCache.set(cacheKey, {
      tokens: parsedTokens,
      timestamp: Date.now()
    })
    
    // 清理过期缓存
    this.cleanupTokenCache()
    
    return parsedTokens
  }
  
  /**
   * 清理令牌缓存
   */
  cleanupTokenCache() {
    const now = Date.now()
    const timeout = this.config.cache.tokenTimeout
    
    for (const [key, cached] of this.cacheData.tokenCache) {
      if (now - cached.timestamp > timeout) {
        this.cacheData.tokenCache.delete(key)
      }
    }
  }
  
  /**
   * DOM缓存管理
   */
  
  /**
   * 更新DOM缓存
   * @param {Object} domInfo DOM信息
   */
  updateDomCache(domInfo) {
    Object.assign(this.cacheData.domInfo, domInfo, {
      lastCacheTime: Date.now()
    })
  }
  
  /**
   * 获取DOM缓存
   */
  getDomCache() {
    const cache = this.cacheData.domInfo
    const now = Date.now()
    
    if (now - cache.lastCacheTime < cache.cacheTimeout && cache.containerHeight > 0) {
      return cache
    }
    
    return null
  }
  
  /**
   * 性能监控
   */
  
  /**
   * 启动性能监控
   */
  startPerformanceMonitoring() {
    const interval = setInterval(() => {
      if (this.tempData._isDestroyed) {
        clearInterval(interval)
        return
      }
      
      this.collectPerformanceData()
    }, this.config.performance.memoryCheckInterval)
    
    this.tempData.timers.intervals.add(interval)
  }
  
  /**
   * 收集性能数据
   */
  collectPerformanceData() {
    const stats = this.tempData.performanceStats
    
    // 收集内存使用数据
    if (typeof performance !== 'undefined' && performance.memory) {
      const memory = performance.memory
      const memoryInfo = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        timestamp: Date.now()
      }
      
      stats.memoryUsage.push(memoryInfo)
      
      // 只保留最近的记录
      if (stats.memoryUsage.length > this.config.performance.maxMemoryHistory) {
        stats.memoryUsage.shift()
      }
      
      // 检查内存使用
      const memoryUsagePercent = (memoryInfo.used / memoryInfo.limit) * 100
      if (memoryUsagePercent > this.config.performance.cleanupThreshold * 100) {
        console.warn(`内存使用率过高: ${memoryUsagePercent.toFixed(1)}%`)
        this.performCleanup()
      }
    }
    
    // 收集打字管理器统计数据
    const typingStats = this.tempData.typingManager.getStats()
    stats.activeStates = typingStats.activeStates
    stats.activeAnimations = typingStats.activeAnimations
  }
  
  /**
   * 执行清理操作
   */
  performCleanup() {
    // 清理过期缓存
    this.cleanupTokenCache()
    this.cleanupOldestCardCache()
    
    // 清理打字状态管理器
    const typingStats = this.tempData.typingManager.getStats()
    if (typingStats.activeStates > 5) {
      console.log('执行打字状态清理')
      // 这里可以实现更智能的清理策略
    }
  }
  
  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    return {
      core: {
        messagesCount: this.getCoreData('messages').length,
        isTyping: this.getCoreData('isAiTyping')
      },
      cache: {
        cardsCount: this.cacheData.messageCards.size,
        tokensCount: this.cacheData.tokenCache.size,
        domCacheValid: !!this.getDomCache()
      },
      typing: this.tempData.typingManager.getStats(),
      performance: { ...this.tempData.performanceStats }
    }
  }
  
  /**
   * 销毁数据管理器
   */
  destroy() {
    // 标记销毁状态
    this.tempData._isDestroyed = true
    
    // 清理打字状态管理器
    this.tempData.typingManager.clearAll()
    
    // 清理定时器
    this.tempData.timers.intervals.forEach(id => clearInterval(id))
    this.tempData.timers.timeouts.forEach(id => clearTimeout(id))
    
    // 清理缓存
    this.cacheData.messageCards.clear()
    this.cacheData.tokenCache.clear()
    
    console.log('数据分层管理器已销毁')
  }
  
  /**
   * 工具方法
   */
  
  /**
   * 字符串哈希
   * @param {string} str 字符串
   */
  hashString(str) {
    let hash = 0
    if (str.length === 0) return hash
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转为32位整数
    }
    
    return hash.toString(36)
  }
}

// 导出单例实例
export default new DataLayerManager() 