/**
 * 对象池管理器
 * 用于减少对象创建和销毁开销，优化内存性能，进一步减少 vdSync 数据传输
 */

/**
 * 通用对象池基类
 */
class ObjectPool {
  constructor(createFn, resetFn, maxSize = 50) {
    this.createFn = createFn || (() => ({}))
    this.resetFn = resetFn || (() => {})
    this.maxSize = maxSize
    this.pool = []
    this.activeCount = 0
    this.totalCreated = 0
    this.totalReused = 0
  }
  
  /**
   * 从池中获取对象
   */
  acquire() {
    let obj
    
    if (this.pool.length > 0) {
      obj = this.pool.pop()
      this.totalReused++
    } else {
      obj = this.createFn()
      this.totalCreated++
    }
    
    this.activeCount++
    return obj
  }
  
  /**
   * 将对象返回池中
   * @param {Object} obj 要返回的对象
   */
  release(obj) {
    if (!obj) return
    
    this.activeCount--
    
    // 重置对象状态
    this.resetFn(obj)
    
    // 如果池未满，则放入池中
    if (this.pool.length < this.maxSize) {
      this.pool.push(obj)
    }
    // 池已满时，对象会被垃圾回收
  }
  
  /**
   * 清空对象池
   */
  clear() {
    this.pool.length = 0
    this.activeCount = 0
  }
  
  /**
   * 获取池统计信息
   */
  getStats() {
    return {
      poolSize: this.pool.length,
      activeCount: this.activeCount,
      totalCreated: this.totalCreated,
      totalReused: this.totalReused,
      reuseRate: this.totalCreated > 0 ? (this.totalReused / this.totalCreated * 100).toFixed(2) + '%' : '0%'
    }
  }
}

/**
 * Token对象池 - 用于富文本解析
 */
class TokenPool extends ObjectPool {
  constructor() {
    super(
      // 创建函数
      () => ({
        content: '',
        isTag: false,
        processed: false
      }),
      // 重置函数
      (token) => {
        token.content = ''
        token.isTag = false
        token.processed = false
      },
      100 // 最大池大小
    )
  }
  
  /**
   * 创建Token对象
   * @param {string} content 内容
   * @param {boolean} isTag 是否为标签
   */
  createToken(content, isTag = false) {
    const token = this.acquire()
    token.content = content
    token.isTag = isTag
    token.processed = false
    return token
  }
  
  /**
   * 释放Token数组
   * @param {Array} tokens Token数组
   */
  releaseTokens(tokens) {
    if (!Array.isArray(tokens)) return
    
    tokens.forEach(token => {
      if (token && typeof token === 'object') {
        this.release(token)
      }
    })
    
    // 清空数组但保留数组本身的引用
    tokens.length = 0
  }
}

/**
 * 动画状态对象池
 */
class AnimationStatePool extends ObjectPool {
  constructor() {
    super(
      // 创建函数
      () => ({
        messageId: null,
        isTyping: false,
        displayContent: '',
        currentIndex: 0,
        lastUpdateTime: 0,
        animationId: null,
        fullContent: '',
        isHtml: false,
        tokens: null,
        currentTokenIndex: 0,
        currentCharIndex: 0,
        onUpdate: null,
        onComplete: null
      }),
      // 重置函数
      (state) => {
        state.messageId = null
        state.isTyping = false
        state.displayContent = ''
        state.currentIndex = 0
        state.lastUpdateTime = 0
        state.animationId = null
        state.fullContent = ''
        state.isHtml = false
        state.tokens = null
        state.currentTokenIndex = 0
        state.currentCharIndex = 0
        state.onUpdate = null
        state.onComplete = null
      },
      30 // 最大池大小
    )
  }
}

/**
 * 回调函数池 - 用于减少函数对象创建
 */
class CallbackPool extends ObjectPool {
  constructor() {
    super(
      // 创建函数
      () => ({
        fn: null,
        context: null,
        args: null
      }),
      // 重置函数
      (callback) => {
        callback.fn = null
        callback.context = null
        callback.args = null
      },
      20 // 最大池大小
    )
  }
  
  /**
   * 创建回调包装器
   * @param {Function} fn 回调函数
   * @param {Object} context 上下文
   * @param {Array} args 参数数组
   */
  createCallback(fn, context = null, args = null) {
    const callback = this.acquire()
    callback.fn = fn
    callback.context = context
    callback.args = args
    return callback
  }
  
  /**
   * 执行回调
   * @param {Object} callback 回调包装器
   * @param {Array} runtimeArgs 运行时参数
   */
  executeCallback(callback, runtimeArgs = []) {
    if (!callback || typeof callback.fn !== 'function') return
    
    try {
      const args = callback.args ? callback.args.concat(runtimeArgs) : runtimeArgs
      if (callback.context) {
        return callback.fn.apply(callback.context, args)
      } else {
        return callback.fn(...args)
      }
    } catch (error) {
      console.error('回调执行错误:', error)
    }
  }
}

/**
 * DOM事件监听器池
 */
class EventListenerPool extends ObjectPool {
  constructor() {
    super(
      // 创建函数
      () => ({
        element: null,
        eventType: '',
        handler: null,
        options: null,
        isActive: false
      }),
      // 重置函数
      (listener) => {
        // 如果监听器还在活跃状态，先移除
        if (listener.isActive && listener.element && listener.handler) {
          try {
            listener.element.removeEventListener(listener.eventType, listener.handler, listener.options)
          } catch (e) {
            console.warn('移除事件监听器失败:', e)
          }
        }
        
        listener.element = null
        listener.eventType = ''
        listener.handler = null
        listener.options = null
        listener.isActive = false
      },
      15 // 最大池大小
    )
  }
  
  /**
   * 创建事件监听器
   * @param {Element} element DOM元素
   * @param {string} eventType 事件类型
   * @param {Function} handler 处理函数
   * @param {Object} options 选项
   */
  createListener(element, eventType, handler, options = null) {
    const listener = this.acquire()
    listener.element = element
    listener.eventType = eventType
    listener.handler = handler
    listener.options = options
    listener.isActive = true
    
    // 添加事件监听器
    try {
      element.addEventListener(eventType, handler, options)
    } catch (error) {
      console.error('添加事件监听器失败:', error)
      listener.isActive = false
    }
    
    return listener
  }
  
  /**
   * 移除事件监听器
   * @param {Object} listener 监听器对象
   */
  removeListener(listener) {
    if (!listener || !listener.isActive) return
    
    try {
      listener.element.removeEventListener(listener.eventType, listener.handler, listener.options)
      listener.isActive = false
    } catch (error) {
      console.error('移除事件监听器失败:', error)
    }
    
    this.release(listener)
  }
}

/**
 * 内存使用信息对象池
 */
class MemoryInfoPool extends ObjectPool {
  constructor() {
    super(
      // 创建函数
      () => ({
        used: 0,
        total: 0,
        limit: 0,
        timestamp: 0,
        percentage: 0
      }),
      // 重置函数
      (info) => {
        info.used = 0
        info.total = 0
        info.limit = 0
        info.timestamp = 0
        info.percentage = 0
      },
      10 // 最大池大小
    )
  }
  
  /**
   * 创建内存信息对象
   * @param {number} used 已使用内存
   * @param {number} total 总内存
   * @param {number} limit 内存限制
   */
  createMemoryInfo(used, total, limit) {
    const info = this.acquire()
    info.used = used
    info.total = total
    info.limit = limit
    info.timestamp = Date.now()
    info.percentage = limit > 0 ? (used / limit * 100) : 0
    return info
  }
}

/**
 * 对象池管理器 - 统一管理所有对象池
 */
class ObjectPoolManager {
  constructor() {
    // 初始化各种对象池
    this.tokenPool = new TokenPool()
    this.animationStatePool = new AnimationStatePool()
    this.callbackPool = new CallbackPool()
    this.eventListenerPool = new EventListenerPool()
    this.memoryInfoPool = new MemoryInfoPool()
    
    // 性能统计
    this.stats = {
      totalMemorySaved: 0,
      operationCount: 0,
      lastCleanupTime: Date.now()
    }
    
    // 自动清理定时器
    this.cleanupInterval = setInterval(() => {
      this.performMaintenance()
    }, 30000) // 每30秒执行一次维护
  }
  
  /**
   * 获取Token池
   */
  getTokenPool() {
    return this.tokenPool
  }
  
  /**
   * 获取动画状态池
   */
  getAnimationStatePool() {
    return this.animationStatePool
  }
  
  /**
   * 获取回调池
   */
  getCallbackPool() {
    return this.callbackPool
  }
  
  /**
   * 获取事件监听器池
   */
  getEventListenerPool() {
    return this.eventListenerPool
  }
  
  /**
   * 获取内存信息池
   */
  getMemoryInfoPool() {
    return this.memoryInfoPool
  }
  
  /**
   * 执行维护操作
   */
  performMaintenance() {
    const now = Date.now()
    
    // 检查各池的使用情况
    const pools = [
      this.tokenPool,
      this.animationStatePool,
      this.callbackPool,
      this.eventListenerPool,
      this.memoryInfoPool
    ]
    
    pools.forEach(pool => {
      const stats = pool.getStats()
      
      // 如果池的重用率很低且池很大，则缩减池大小
      if (stats.poolSize > 10 && parseFloat(stats.reuseRate) < 20) {
        const targetSize = Math.max(5, Math.floor(stats.poolSize * 0.7))
        pool.pool.length = targetSize
      }
    })
    
    this.stats.lastCleanupTime = now
  }
  
  /**
   * 获取所有池的统计信息
   */
  getAllStats() {
    return {
      tokenPool: this.tokenPool.getStats(),
      animationStatePool: this.animationStatePool.getStats(),
      callbackPool: this.callbackPool.getStats(),
      eventListenerPool: this.eventListenerPool.getStats(),
      memoryInfoPool: this.memoryInfoPool.getStats(),
      manager: {
        ...this.stats,
        totalPools: 5
      }
    }
  }
  
  /**
   * 清空所有对象池
   */
  clearAll() {
    this.tokenPool.clear()
    this.animationStatePool.clear()
    this.callbackPool.clear()
    this.eventListenerPool.clear()
    this.memoryInfoPool.clear()
    
    console.log('所有对象池已清空')
  }
  
  /**
   * 销毁对象池管理器
   */
  destroy() {
    // 清理定时器
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    
    // 清空所有池
    this.clearAll()
    
    console.log('对象池管理器已销毁')
  }
  
  /**
   * 计算内存节省估算
   */
  estimateMemorySavings() {
    const stats = this.getAllStats()
    let totalSavings = 0
    
    // 粗略估算每个重用对象节省的内存（字节）
    const estimates = {
      tokenPool: stats.tokenPool.totalReused * 100, // 每个token约100字节
      animationStatePool: stats.animationStatePool.totalReused * 200, // 每个状态约200字节
      callbackPool: stats.callbackPool.totalReused * 50, // 每个回调约50字节
      eventListenerPool: stats.eventListenerPool.totalReused * 150, // 每个监听器约150字节
      memoryInfoPool: stats.memoryInfoPool.totalReused * 80 // 每个内存信息约80字节
    }
    
    totalSavings = Object.values(estimates).reduce((sum, saving) => sum + saving, 0)
    
    return {
      totalBytes: totalSavings,
      totalKB: (totalSavings / 1024).toFixed(2),
      totalMB: (totalSavings / 1024 / 1024).toFixed(2),
      breakdown: estimates
    }
  }
}

// 导出单例实例
export default new ObjectPoolManager()

// 同时导出各个池类供直接使用
export {
  ObjectPool,
  TokenPool,
  AnimationStatePool,
  CallbackPool,
  EventListenerPool,
  MemoryInfoPool,
  ObjectPoolManager
} 