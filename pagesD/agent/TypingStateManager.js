/**
 * 非响应式打字状态管理器
 * 用于减少 vdSync 数据传输量，提高打字动画性能
 */
class TypingStateManager {
  constructor() {
    // 使用 Map 存储各消息的打字状态，避免 Vue 响应式追踪
    this.states = new Map()
    
    // 动画帧管理
    this.animationFrames = new Set()
    
    // 性能配置
    this.config = {
      speed: 50, // 每个字符的延迟时间（毫秒）
      chunkSize: 5, // 分块大小，每帧处理的字符数
      maxStates: 10 // 最大同时进行的打字状态数量
    }
    
    // 对象池，复用状态对象减少内存分配
    this.statePool = []
    this.maxPoolSize = 20
  }
  
  /**
   * 添加新的打字状态
   * @param {string} messageId 消息ID
   * @param {Object} config 配置参数
   */
  addState(messageId, config = {}) {
    // 检查是否超出最大状态数量限制
    if (this.states.size >= this.config.maxStates) {
      console.warn(`打字状态数量超限，当前: ${this.states.size}`)
      this.cleanupOldestState()
    }
    
    // 从对象池获取状态对象或创建新对象
    const state = this.getStateFromPool()
    
    // 初始化状态
    Object.assign(state, {
      messageId,
      isTyping: true,
      displayContent: '',
      currentIndex: 0,
      lastUpdateTime: Date.now(),
      animationId: null,
      fullContent: config.content || '',
      isHtml: config.isHtml || false,
      tokens: config.tokens || null,
      currentTokenIndex: 0,
      currentCharIndex: 0,
      onUpdate: config.onUpdate || null,
      onComplete: config.onComplete || null
    })
    
    this.states.set(messageId, state)
    return state
  }
  
  /**
   * 更新打字状态
   * @param {string} messageId 消息ID
   * @param {Object} updates 更新内容
   */
  updateState(messageId, updates) {
    const state = this.states.get(messageId)
    if (!state) {
      console.warn(`打字状态不存在: ${messageId}`)
      return false
    }
    
    Object.assign(state, updates)
    
    // 触发更新回调
    if (state.onUpdate && typeof state.onUpdate === 'function') {
      try {
        state.onUpdate(state)
      } catch (error) {
        console.error('打字状态更新回调错误:', error)
      }
    }
    
    return true
  }
  
  /**
   * 获取打字状态
   * @param {string} messageId 消息ID
   */
  getState(messageId) {
    return this.states.get(messageId) || null
  }
  
  /**
   * 移除打字状态
   * @param {string} messageId 消息ID
   */
  removeState(messageId) {
    const state = this.states.get(messageId)
    if (!state) return false
    
    // 取消动画帧
    if (state.animationId) {
      this.cancelAnimationFrame(state.animationId)
      this.animationFrames.delete(state.animationId)
    }
    
    // 触发完成回调
    if (state.onComplete && typeof state.onComplete === 'function') {
      try {
        state.onComplete(state)
      } catch (error) {
        console.error('打字状态完成回调错误:', error)
      }
    }
    
    // 将状态对象返回池中复用
    this.returnStateToPool(state)
    
    // 从Map中移除
    return this.states.delete(messageId)
  }
  
  /**
   * 检查消息是否在打字状态
   * @param {string} messageId 消息ID
   */
  isTyping(messageId) {
    const state = this.states.get(messageId)
    return state ? state.isTyping : false
  }
  
  /**
   * 获取消息显示内容
   * @param {string} messageId 消息ID
   */
  getDisplayContent(messageId) {
    const state = this.states.get(messageId)
    return state ? state.displayContent : ''
  }
  
  /**
   * 启动打字动画
   * @param {string} messageId 消息ID
   */
  startTyping(messageId) {
    const state = this.states.get(messageId)
    if (!state || !state.isTyping) return
    
    if (state.isHtml) {
      this.startRichTyping(messageId)
    } else {
      this.startPlainTyping(messageId)
    }
  }
  
  /**
   * 普通文本打字动画
   * @param {string} messageId 消息ID
   */
  startPlainTyping(messageId) {
    const state = this.states.get(messageId)
    if (!state || !state.isTyping) return
    
    const currentTime = Date.now()
    
    // 检查是否该更新字符
    if (currentTime - state.lastUpdateTime >= this.config.speed) {
      // 分块处理字符，提高性能
      const endIndex = Math.min(
        state.currentIndex + this.config.chunkSize,
        state.fullContent.length
      )
      
      // 更新显示内容
      state.displayContent = state.fullContent.substring(0, endIndex)
      state.currentIndex = endIndex
      state.lastUpdateTime = currentTime
      
      // 触发更新回调
      if (state.onUpdate) {
        state.onUpdate(state)
      }
    }
    
    // 检查是否完成
    if (state.currentIndex >= state.fullContent.length) {
      state.isTyping = false
      this.removeState(messageId)
      return
    }
    
    // 继续下一帧
    const animationId = this.requestAnimationFrame(() => {
      this.startPlainTyping(messageId)
    })
    
    state.animationId = animationId
    this.animationFrames.add(animationId)
  }
  
  /**
   * 富文本打字动画
   * @param {string} messageId 消息ID
   */
  startRichTyping(messageId) {
    const state = this.states.get(messageId)
    if (!state || !state.isTyping || !state.tokens) return
    
    const currentTime = Date.now()
    
    // 检查是否该更新
    if (currentTime - state.lastUpdateTime >= this.config.speed) {
      this.processRichTokens(state)
      state.lastUpdateTime = currentTime
      
      // 触发更新回调
      if (state.onUpdate) {
        state.onUpdate(state)
      }
    }
    
    // 检查是否完成
    if (state.currentTokenIndex >= state.tokens.length) {
      state.isTyping = false
      this.removeState(messageId)
      return
    }
    
    // 继续下一帧
    const animationId = this.requestAnimationFrame(() => {
      this.startRichTyping(messageId)
    })
    
    state.animationId = animationId
    this.animationFrames.add(animationId)
  }
  
  /**
   * 处理富文本令牌
   * @param {Object} state 打字状态
   */
  processRichTokens(state) {
    let processed = 0
    
    while (processed < this.config.chunkSize && state.currentTokenIndex < state.tokens.length) {
      const currentToken = state.tokens[state.currentTokenIndex]
      
      if (currentToken.isTag) {
        // HTML标签直接添加
        if (!currentToken.processed) {
          state.displayContent += currentToken.content
          currentToken.processed = true
          state.currentTokenIndex++
        }
      } else {
        // 文本内容逐字添加
        const textContent = currentToken.content
        if (state.currentCharIndex < textContent.length) {
          const endIndex = Math.min(
            state.currentCharIndex + this.config.chunkSize - processed,
            textContent.length
          )
          
          const chunk = textContent.substring(state.currentCharIndex, endIndex)
          state.displayContent += chunk
          state.currentCharIndex = endIndex
          processed += chunk.length
          
          if (state.currentCharIndex >= textContent.length) {
            currentToken.processed = true
            state.currentTokenIndex++
            state.currentCharIndex = 0
          }
        }
      }
      
      processed++
    }
  }
  
  /**
   * 清空所有状态
   */
  clearAll() {
    // 取消所有动画帧
    this.animationFrames.forEach(id => {
      this.cancelAnimationFrame(id)
    })
    this.animationFrames.clear()
    
    // 将所有状态对象返回池中
    for (const [messageId, state] of this.states) {
      this.returnStateToPool(state)
    }
    
    // 清空状态Map
    this.states.clear()
    
    console.log('所有打字状态已清空')
  }
  
  /**
   * 清理最老的状态（LRU策略）
   */
  cleanupOldestState() {
    if (this.states.size === 0) return
    
    let oldestMessageId = null
    let oldestTime = Infinity
    
    for (const [messageId, state] of this.states) {
      if (state.lastUpdateTime < oldestTime) {
        oldestTime = state.lastUpdateTime
        oldestMessageId = messageId
      }
    }
    
    if (oldestMessageId) {
      console.log(`清理最老的打字状态: ${oldestMessageId}`)
      this.removeState(oldestMessageId)
    }
  }
  
  /**
   * 从对象池获取状态对象
   */
  getStateFromPool() {
    if (this.statePool.length > 0) {
      const state = this.statePool.pop()
      // 重置状态对象
      this.resetState(state)
      return state
    }
    
    // 池中无可用对象，创建新对象
    return this.createNewState()
  }
  
  /**
   * 将状态对象返回池中
   * @param {Object} state 状态对象
   */
  returnStateToPool(state) {
    if (this.statePool.length < this.maxPoolSize) {
      this.resetState(state)
      this.statePool.push(state)
    }
  }
  
  /**
   * 重置状态对象
   * @param {Object} state 状态对象
   */
  resetState(state) {
    state.messageId = null
    state.isTyping = false
    state.displayContent = ''
    state.currentIndex = 0
    state.lastUpdateTime = 0
    state.animationId = null
    state.fullContent = ''
    state.isHtml = false
    state.tokens = null
    state.currentTokenIndex = 0
    state.currentCharIndex = 0
    state.onUpdate = null
    state.onComplete = null
  }
  
  /**
   * 创建新的状态对象
   */
  createNewState() {
    return {
      messageId: null,
      isTyping: false,
      displayContent: '',
      currentIndex: 0,
      lastUpdateTime: 0,
      animationId: null,
      fullContent: '',
      isHtml: false,
      tokens: null,
      currentTokenIndex: 0,
      currentCharIndex: 0,
      onUpdate: null,
      onComplete: null
    }
  }
  
  /**
   * 兼容的 requestAnimationFrame
   */
  requestAnimationFrame(callback) {
    if (typeof requestAnimationFrame !== 'undefined') {
      return requestAnimationFrame(callback)
    } else {
      // uni-app 环境降级使用 setTimeout
      return setTimeout(callback, 16) // 约60fps
    }
  }
  
  /**
   * 兼容的 cancelAnimationFrame
   */
  cancelAnimationFrame(id) {
    if (typeof cancelAnimationFrame !== 'undefined') {
      cancelAnimationFrame(id)
    } else {
      clearTimeout(id)
    }
  }
  
  /**
   * 获取性能统计
   */
  getStats() {
    return {
      activeStates: this.states.size,
      activeAnimations: this.animationFrames.size,
      poolSize: this.statePool.length,
      config: { ...this.config }
    }
  }
  
  /**
   * 更新配置
   * @param {Object} newConfig 新配置
   */
  updateConfig(newConfig) {
    Object.assign(this.config, newConfig)
  }
}

// 导出单例实例
export default new TypingStateManager() 