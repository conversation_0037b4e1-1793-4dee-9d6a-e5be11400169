<template>
  <view class="input-panel">
    <!-- AI生成标记 -->
    <view class="ai-generated-mark">
      <view class="mark-line"></view>
      <text class="mark-text">— 以上内容由AI大模型生成 —</text>
      <view class="mark-line"></view>
    </view>
    <view class="input-container" :class="{ 'voice-mode': !isTextMode }">
      <!-- 左侧：清空聊天按钮 -->
      <view class="clear-btn" @click="handleClearChat">
        <uni-icons type="trash" size="24" color="#999" />
      </view>

      <!-- 中间：输入区域 -->
      <view class="input-wrapper" :class="{ 'voice-wrapper': !isTextMode }">
        <!-- 文本输入模式 -->
        <textarea 
          v-show="isTextMode" 
          v-model="localInputText" 
          @input="handleInputDebounced" 
          class="text-input" 
          placeholder="请输入"
          :maxlength="1000" 
          :max-height="120" 
          :cursor-spacing="20" 
          :show-confirm-bar="false"
          :disable-default-padding="true"
          :auto-height="true"
          ref="textInput" 
        />

        <!-- 语音输入模式 -->
        <view v-if="!isTextMode" class="voice-input-btn" :class="{ 'recording': isRecording }"
          @touchstart="handleVoiceStart" @touchend="handleVoiceEnd">
          <text class="voice-btn-text">{{ isRecording ? '松开结束' : '按住说话' }}</text>
        </view>
      </view>

      <!-- 右侧：智能操作按钮 -->
      <view class="input-actions">
        <view :class="buttonClass" @click="handleButtonClick">
          <uni-icons 
            :type="buttonIcon" 
            size="24" 
            :color="isTextMode && hasContent ? '#fff' : '#8e65de'" 
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 引入uni-icons组件
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  name: 'InputPanel',
  components: {
    uniIcons
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    isRecording: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isTextMode: true, // 是否为文本输入模式
      keyboardHeight: 0, // 键盘高度
      localInputText: '', // 本地输入文本
      inputDebounceTimer: null // 防抖定时器
    }
  },
  computed: {
    // 判断是否有输入内容
    hasContent() {
      return this.localInputText && this.localInputText.trim().length > 0
    },
    // 根据状态返回对应图标
    buttonIcon() {
      if (this.isTextMode && this.hasContent) {
        return 'paperplane-filled' // 发送状态
      } else if (this.isTextMode && !this.hasContent) {
        return 'sound' // 文本输入状态，显示切换到语音的图标
      } else {
        return 'compose' // 语音输入状态，显示切换到文本的图标
      }
    },
    // 根据状态返回对应样式类名
    buttonClass() {
      if (this.isTextMode && this.hasContent) {
        return 'smart-btn send-state' // 发送状态样式
      } else {
        return 'smart-btn toggle-state' // 切换状态样式
      }
    }
  },
  watch: {
    // 监听外部value变化，同步到本地
    value: {
      handler(newVal) {
        if (newVal !== this.localInputText) {
          this.localInputText = newVal
        }
      },
      immediate: true
    }
  },
  created() {
    // 监听键盘高度变化
    uni.onKeyboardHeightChange(res => {
      this.keyboardHeight = res.height
      this.$emit('keyboardHeightChange', res.height)
    })
  },
  methods: {
    // 防抖处理输入事件
    handleInputDebounced() {
      // 清除之前的定时器
      if (this.inputDebounceTimer) {
        clearTimeout(this.inputDebounceTimer)
      }
      
      // 设置新的定时器
      this.inputDebounceTimer = setTimeout(() => {
        this.$emit('input', this.localInputText)
      }, 100) // 100ms防抖延迟
    },
    
    // 处理发送消息
    handleSend() {
      if (!this.localInputText.trim()) return
      
      // 发送消息
      this.$emit('sendMessage', this.localInputText.trim())
      
      // 发送后清空本地输入
      this.localInputText = ''
      // 立即同步给父组件
      this.$emit('input', '')
      
      // 收起键盘
      this.hideKeyboard()
    },
    
    // 收起键盘
    hideKeyboard() {
      try {
        // 方法1：让输入框失去焦点
        if (this.$refs.textInput) {
          this.$refs.textInput.blur()
        }
        
        // 方法2：使用uni-app API收起键盘（兼容性处理）
        if (uni.hideKeyboard) {
          uni.hideKeyboard()
        }
      } catch (error) {
        console.log('收起键盘失败:', error)
      }
    },

    // 处理语音录制开始
    handleVoiceStart() {
      this.$emit('startRecord')
    },

    // 处理语音录制结束
    handleVoiceEnd() {
      this.$emit('stopRecord')
    },

    // 切换输入模式
    toggleInputMode() {
      this.isTextMode = !this.isTextMode
    },

    // 智能按钮点击处理
    handleButtonClick() {
      if (this.isTextMode && this.hasContent) {
        // 发送状态：执行发送操作
        this.handleSend()
      } else {
        // 切换状态：切换输入模式
        this.toggleInputMode()
      }
    },

    // 清空聊天记录
    handleClearChat() {
      this.$emit('clearChat')
    },

    // 处理键盘高度变化
    handleKeyboardHeightChange(height) {
      this.keyboardHeight = height
      this.$emit('keyboardHeightChange', height)
    }
  },
  
  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.inputDebounceTimer) {
      clearTimeout(this.inputDebounceTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.input-panel {
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  height: 200rpx;
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  flex-shrink: 0;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  transition: bottom 0.3s ease;
}

.input-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
  height: 80rpx;

  &.voice-mode {
    .input-wrapper {
      background-color: #8e65de;
      border-color: #8e65de;
    }
  }
}

.clear-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:active {
    background-color: #e0e0e0;
    transform: scale(0.95);
  }
}

.input-wrapper {
  flex: 1;
  position: relative;
  height: 100%;
  border-radius: 36rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e0e0e0;
  overflow: hidden;
  display: flex;
  align-items: center;
  transition: all 0.3s;

  &.voice-wrapper {
    background-color: #8e65de;
    border-color: #8e65de;
  }

  &:focus-within {
    border-color: #8e65de;
    background-color: #fff;
  }
}

.text-input {
  width: 100%;
  height: 100%;
  padding: 16rpx 24rpx;
  font-size: 32rpx;
  line-height: 1.4;
  background-color: transparent;
  resize: none;
  box-sizing: border-box;
  overflow-y: auto;
}

.input-actions {
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.smart-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  &.send-state {
    background: linear-gradient(135deg, #8e65de 0%, #6a49ad 100%);
  }

  &.toggle-state {
    background-color: #f5f5f5;

    &:active {
      background-color: #e0e0e0;
    }
  }
}

.voice-input-btn {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;

  &.recording {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .voice-btn-text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 500;
  }
}

.ai-generated-mark {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  z-index: 99;

  .mark-line {
    width: 80rpx;
    height: 1rpx;
    background-color: #ccc;
  }
  
  .mark-text {
    font-size: 24rpx;
    color: #999;
    margin: 0 16rpx;
  }
}
</style>