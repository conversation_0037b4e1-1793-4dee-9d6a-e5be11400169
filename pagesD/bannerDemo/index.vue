<template>
  <view class="demo-container">
    <!-- 页面标题 -->
    <view class="demo-title">3D轮播图组件Demo</view>
    <view class="demo-desc">EtherealWheat-banner 组件展示 - 支持多种显示模式</view>
    
    <!-- 经典模式示例 -->
    <view class="section">
      <view class="section-title">经典模式 (Classic)</view>
      <view class="section-subtitle">原始的大图轮播样式，适合主要内容展示</view>
      <special-banner 
        :banner-list="bannerList" 
        :swiper-config="classicConfig"
        mode="classic">
      </special-banner>
    </view>
    
    <!-- 卡片模式示例 -->
    <view class="section">
      <view class="section-title">卡片模式 (Card)</view>
      <view class="section-subtitle">适合福利专区、网红打卡等区域展示</view>
      <view class="card-wrapper">
        <special-banner 
          :banner-list="bannerList2" 
          :swiper-config="cardConfig"
          mode="card"
          :hide-description="true">
        </special-banner>
      </view>
    </view>
    
    <!-- 紧凑模式示例 -->
    <view class="section">
      <view class="section-title">紧凑模式 (Compact)</view>
      <view class="section-subtitle">适合活动专区等空间有限的区域</view>
      <view class="compact-wrapper">
        <special-banner 
          :banner-list="bannerList3" 
          :swiper-config="compactConfig"
          mode="compact"
          :hide-description="true">
        </special-banner>
      </view>
    </view>
    
    <!-- 自动播放示例 -->
    <view class="section">
      <view class="section-title">自动播放示例</view>
      <view class="section-subtitle">卡片模式 + 自动播放功能</view>
      <view class="card-wrapper">
        <special-banner 
          :banner-list="bannerList4" 
          :swiper-config="autoplayConfig"
          mode="card"
          :hide-description="true">
        </special-banner>
      </view>
    </view>
    
    <!-- 自定义尺寸示例 -->
    <view class="section">
      <view class="section-title">自定义尺寸示例</view>
      <view class="section-subtitle">自定义容器高度和图片尺寸</view>
      <special-banner 
        :banner-list="bannerList5" 
        :swiper-config="customConfig"
        containerHeight="600rpx"
        mode="classic"
        imageWidth="280rpx"
        imageHeight="580rpx"
        scaleX="1.1"
        scaleY="1.1">
      </special-banner>
    </view>
    
    <!-- 使用说明 -->
    <view class="usage-section">
      <view class="usage-title">使用说明</view>
      <view class="usage-text">
        <text class="usage-item">1. <text class="highlight">mode</text>: 显示模式，支持 'classic'(经典)、'card'(卡片)、'compact'(紧凑)</text>
        <text class="usage-item">2. <text class="highlight">bannerList</text>: 轮播数据数组，至少需要3个项目才能看到完整效果</text>
        <text class="usage-item">3. <text class="highlight">hideDescription</text>: 是否隐藏底部描述文字</text>
        <text class="usage-item">4. <text class="highlight">containerHeight</text>: 自定义容器高度</text>
        <text class="usage-item">5. <text class="highlight">imageWidth/imageHeight</text>: 自定义图片尺寸</text>
        <text class="usage-item">6. <text class="highlight">scaleX/scaleY</text>: 当前项的缩放比例</text>
        <text class="usage-item">7. 每个可视区域都能看到3个图片item，支持触摸滑动切换</text>
      </view>
    </view>
  </view>
</template>

<script>
import specialBanner from '@/components/EtherealWheat-banner/specialBanner.vue'

export default {
  components: {
    specialBanner
  },
  data() {
    return {
      // 经典模式数据
      bannerList: [{
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
            title: '七夕将至：时光足够久，韧性也能炖出味',
            description: '一万年太久，就现在，给你爱',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ2.png',
            title: '新菜上架：无边海洋，找到顺眼的那尾鱼',
            description: '花中樱，鱼乃鲷花中樱，鱼乃鲷',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ3.png',
            title: '在湘西的烟火气里，发现苗族少女的神明',
            description: '取材自湘西苗族传统的烟熏文化',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ4.png',
            title: '福利降临，陪伴独自行走的丰盛旅程',
            description: '在自己的小世界里，日日好日，夜夜好清宵',
            path: ''
        }],
       
       // 卡片模式数据
       bannerList2: [{
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
            title: '福利专区',
            description: '早鸟预售套房，火热抢购中',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ2.png',
            title: '网红打卡',
            description: '"绿通卡"专享优惠活动',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ3.png',
            title: '预定专区',
            description: '三月踏青，遇见美好春天',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ4.png',
            title: '限时优惠',
            description: '最新优惠活动，不容错过',
            path: ''
        }],
       
       // 紧凑模式数据
       bannerList3: [{
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
            title: '活动专区',
            description: '一键游广西旅游资讯',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ2.png',
            title: '活动日历',
            description: '红叶铺了铺满天',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ3.png',
            title: '走进象州',
            description: '古茶芬芳遇见传统技艺',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ4.png',
            title: '文旅资讯',
            description: '最新文旅资讯推送',
            path: ''
        }],
       
       // 自动播放数据
       bannerList4: [{
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
            title: '热门推荐',
            description: '精选热门内容推荐',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ2.png',
            title: '限时特惠',
            description: '限时特惠活动进行中',
            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ3.png',
            title: '新品上线',
            description: '全新产品震撼上线',
            path: ''
        }],

        // 自定义缩放示例数据
        bannerList5: [{
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',

            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ2.png',

            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ3.png',

            path: ''
        }, {
            picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ4.png',

            path: ''
        }],
       
       // 经典模式配置
       classicConfig: {
         indicatorDots: true,
         indicatorColor: 'rgba(255, 255, 255, .4)',
         indicatorActiveColor: 'rgba(255, 255, 255, 1)',
         autoplay: false,
         interval: 3000,
         duration: 300,
         circular: true
       },
       
       // 卡片模式配置
       cardConfig: {
         indicatorDots: true,
         indicatorColor: 'rgba(0, 0, 0, .2)',
         indicatorActiveColor: 'rgba(0, 0, 0, .6)',
         previousMargin: '200rpx',
         nextMargin: '200rpx',
         autoplay: false,
         interval: 3000,
         duration: 400,
         circular: true
       },
       
       // 紧凑模式配置
       compactConfig: {
         indicatorDots: false,
         autoplay: false,
         interval: 3000,
         duration: 300,
         circular: true
       },
       
       // 自动播放配置
       autoplayConfig: {
         indicatorDots: true,
         indicatorColor: 'rgba(0, 0, 0, .2)',
         indicatorActiveColor: '#007AFF',
         autoplay: true,
         interval: 2500,
         duration: 500,
         circular: true
       },
       
       // 自定义配置
       customConfig: {
         indicatorDots: true,
         indicatorColor: 'rgba(255, 255, 255, .3)',
         indicatorActiveColor: '#FF6B6B',
         autoplay: false,
         interval: 3000,
         duration: 400,
         circular: true
       }
    }
  },
  
  onLoad() {
    console.log('3D轮播图Demo页面加载完成')
  },
  
  methods: {
    // 可以在这里添加自定义方法
  }
}
</script>

<style lang="scss" scoped>
.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
}

.demo-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin: 40rpx 0 20rpx;
}

.demo-desc {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.section {
  margin-bottom: 60rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 30rpx 30rpx 10rpx;
  background: #f8f9fa;
}

.section-subtitle {
  font-size: 26rpx;
  color: #666;
  padding: 0 30rpx 20rpx;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
}

.card-wrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx 0;
}

.compact-wrapper {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  padding: 20rpx 0;
}

.usage-section {
  margin-top: 40rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.usage-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.usage-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}

.usage-item {
  display: block;
  margin-bottom: 12rpx;
}

.highlight {
  color: #007AFF;
  font-weight: 600;
}
</style>
