<!-- C端 用户注册 -->
<template>
	<view class="register padding-top">
		<view class="GX-logo padding-bottom">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc-logo.png"
				class="home-img"></image>
			<view class="login-title">桂小通(广西捷通)</view>
		</view>
		<view>

			<form>
				<view class="cu-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">手机号码:</view>
					<input placeholder="请输入手机号码" :disabled="isWxlogin" name="mobile" v-model='formData.mobile'
						@input="changeInput($event,'mobile')"></input>

				</view>
				<view class="cu-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">登录密码:</view>
					<input placeholder="8位以上，包含字母和数字" password name="password" v-model='formData.password'
						@input="changeInput($event,'password')"></input>

				</view>
				<view class="cu-form-group">
					<span style="margin-left: 8rpx;"></span>
					<view class="title form_label-require">电子邮箱:</view>
					<input placeholder="请输入电子邮箱" name="email" v-model='formData.email'
						@input="changeInput($event,'email')"></input>

				</view>
				<!-- <view class="cu-form-group login-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">图形验证码:</view>
					<input placeholder="请输入图形验证码" name="mobileCode" v-model='mobileCode'></input>
					<image :src="codeUrl" v-show="codeUrl" class="code-img" @click="getCaptcha">
				</view> -->
				<view class="cu-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">验证码:</view>
					<input placeholder="请输入验证码" name="mobileCode" v-model='formData.mobileCode'
						@input="changeInput($event,'mobileCode')"></input>
					<text class="sendSMS" @click="showCodeHandle">{{smsName}}</text>
				</view>
			</form>

		</view>
		<view class="certification">

			<TButton title="用户注册" @clickButton="goNext" @click="gotovehicle" :isLoadding="isBtnLoader" />
		</view>
		<view class="load_desc">
			<view class="desc_title">
				温馨提示：
			</view>
			<view class="desc_text">
				<view>
					1.为了您的信息安全，登陆密码建议使用数字、字母 、特殊字符；避免设置过于简单、易破译的密码
				</view>
				<view>
					2.带<text style="color:red">*</text>的为必填项，不带的为选填项，选填项可以不填写
				</view>

			</view>
		</view>
		<!-- app-id：验证码CaptchaAppId, 从腾讯云的验证码控制台中获取, 在验证码控制台页面内【图形验证】>【验证列表】进行查看 -->
		<t-captcha id="captcha" app-id="191430362" @verify="handlerVerify" @ready="handlerReady" @close="handlerClose"
			@error="handlerError" />
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import tLoading from "@/components/common/t-loading.vue";
	import JSEncrypt from '@/js_sdk/jsencrypt/jsencrypt';
	import TButton from "@/components/t-button.vue";
	import {
		setLoginUserInfo,
		getCurrUserInfo,
		setCurrUserInfo,
		setTicket,
		setMd5Key,
		setAesKey,
	} from "@/common/storageUtil.js";
	import {
		checkIdCard,
		checkEmail
	} from "@/common/util.js";
	import {
		checkPhone
	} from '@/common/method/validater.js'
	import {
		getErrorMessage
	} from '@/common/method/filter.js'
	export default {
		components: {

			TButton,

			tLoading,
		},
		data() {
			return {
				smsName: "发送验证码",
				formData: {
					"email": "",
					'loginName': "",
					"mobile": "",
					"mobileCode": "",
					"password": "",
					"userName": "",
					"userType": "1",
					"openId": '',
					"unionId": ''
				},
				num: 0,
				isBtnLoader: false,
				userInfo: {},
				isShowRule: false,
				showInfo: false,
				isLoading: false,
				isLeftUpload: false,
				isRightUplod: false,
				urlType: "",
				isTruck: false,
				isCorp: false,
				applyListNo: "",
				title: "用户信息",
				time: null,
				uploadType: 1, // 1-个人开户 2-车主身份证 3-企业经办人
				user_type_index: 0,
				mobileCode: '', // 图形验证码
				codeUrl: '', // 图形验证码连接
				captchaId: '',
				unionId: {},
				isWxlogin: false,
				opunId: {},
				codeTicket: '', //腾讯验证码
			};
		},
		onLoad(option) {
			if (option.paramsId) {
				let params = JSON.parse(option.paramsId)
				this.formData.mobile = params.mobile
				this.formData.openId = params.openId
				this.formData.unionId = params.unionId
				if (params.mobile) {
					this.isWxlogin = true
				}
			}
		},
		created() {
			// this.getCaptcha();

		},
		methods: {
			showCodeHandle() {
				if (this.sendSmsVal()) {
					uni.showModal({
						title: "提示",
						content: this.sendSmsVal(),
						showCancel: false,
					});
					return;
				}
				this.selectComponent('#captcha').show()
				// 进行业务逻辑，若出现错误需重置验证码，执行以下方法
				// if (error) {
				// this.selectComponent('#captcha').refresh()
				// }
			},
			// 验证码验证结果回调
			handlerVerify(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
				if (ev.detail.ret === 0) {
					// 验证成功
					this.codeTicket = ev.detail.ticket
					console.log('ticket:', ev.detail.ticket)
					this.sendSMS()
				} else {
					// 验证失败
					// 请不要在验证失败中调用refresh，验证码内部会进行相应处理
				}
			},
			// 验证码准备就绪
			handlerReady() {
				console.log('验证码准备就绪')
			},
			// 验证码弹框准备关闭
			handlerClose(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
				if (ev && ev.detail.ret && ev.detail.ret === 2) {
					console.log('点击了关闭按钮，验证码弹框准备关闭');
				} else {
					console.log('验证完成，验证码弹框准备关闭');
				}
			},
			// 验证码出错
			handlerError(ev) {
				console.log(ev.detail.errMsg)
			},
			// 手机号码失去焦点事件
			// onMobileBlurhandle() {

			// },
			// 发送图形验证码
			getCaptcha() {
				let params = {
					// mobile: this.formData.mobile
				}
				this.$request.post(this.$interfaces.getCaptcha, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log(res, 'yanzhegnma');
						this.codeUrl = res.data.image
						this.captchaId = res.data.captchaId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {

				})
			},
			handleNameInput(event, data) {

			},
			changeInput(event, data) {

			},
			bindUserPickerChange(e) {
				this.user_type_index = e.detail.value;
				this.formData.certificates_type =
					this.personalType[e.detail.value].value || "";
			},
			goNext() {
				console.log(this.formData);
				let msg = this.registerVal();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				this.onRegisterHandle();
			},

			onRegisterHandle() {
				let params = JSON.parse(JSON.stringify(this.formData));
				let _self = this;
				var encrypt = new JSEncrypt()
				encrypt.setPublicKey(this.$publicKey)
				params.password = encrypt.encrypt(params.password)
				this.isLoading = true;
				// 个人注册 登录名=手机号
				if (params.userType == '1') {
					params.loginName = params.mobile;
					// 默认登录
					params.autoLogin = 2
				}
				this.$request.post(this.$interfaces.personReg, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log(res.data);
						if (res.data && res.data.hasOwnProperty('loginBody')) {
							uni.showModal({
								title: "提示",
								content: '注册成功',
								showCancel: false,
								success: function() {
									_self.setLoginInfo(res.data.loginBody)
								}
							});
						} else {
							uni.showModal({
								title: "提示",
								content: '注册成功,去登录',
								showCancel: false,
								success: function() {
									uni.redirectTo({
										url: "/pagesD/login/p-login"
									})
								}
							});
						}


					} else {
						this.getCaptcha()
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch((error) => {
					this.getCaptcha()
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//发送短信
			sendSMS() {
				if (!this.time) {
					this.isLoading = true
					let countdown = 60
					let params = {
						mobile: this.formData.mobile,
						// mobileCode: this.mobileCode,
						// captchaId: this.captchaId
						ticket: this.codeTicket
					};
					this.$request.post(this.$interfaces.sendaAccountSms, {
						data: params
					}).then(res => {
						console.log(res);
						this.isLoading = false
						if (res.code == '200') {
							this.time = setInterval(() => {
								countdown = countdown - 1
								this.smsName = countdown + "秒后重新发送"
								if (countdown === 0) {
									clearInterval(this.time)
									this.time = null
									this.smsName = "重新发送"
								}
							}, 1000)
						} else {
							uni.showModal({
								title: "提示",
								content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res.code + '】',
								showCancel: false,
								// success: (res) => {
								// 	if (res.confirm) {
								// 		this.getCaptcha()
								// 	}
								// }
							});
						}
					}).catch(err => {

					})
				}
			},
			sendSmsVal() {
				let rules = {
					mobile: {
						required: '请输入手机号码',
						validator: this.checkPone
					},
					password: {
						required: '请输入登录密码'
					},
					// email: {
					// 	required: '请输入电子邮箱',
					// 	validator: this.checkEmailvalidator
					// },

				}
				return this.validateHandle(rules)
			},
			registerVal() {
				let rules = {
					mobile: {
						required: '请输入手机号码',
						validator: this.checkPone
					},
					password: {
						required: '请输入登录密码'
					},
					// email: {
					// 	required: '请输入电子邮箱',
					// 	validator: this.checkEmailvalidator
					// },

					mobileCode: {
						required: '请输入验证码'
					}
				}
				return this.validateHandle(rules)
			},
			validateHandle(rules) {

				for (let key in rules) {
					if (rules[key].hasOwnProperty('required')) {
						if (!this.formData[key]) {
							return rules[key]['required']
						}
					}
					if (rules[key].hasOwnProperty('validator')) {
						let validator = rules[key].validator && rules[key].validator()
						if (validator) {
							return validator
						}

					}
				}
				return '';

			},

			checkEmailvalidator() {
				return checkEmail(this.formData.email) ? '' : '邮箱格式不正确'
			},
			checkPone() {
				console.log(checkPhone(this.formData.mobile))
				return !checkPhone(this.formData.mobile) ? '' : '请输入合法手机号'
			},
			onInputBlur() {
				if (/[\u4E00-\u9FA5]/g.test(this.formData.loginName)) {
					uni.showModal({
						title: "提示",
						content: '登录名不能含有中文',
						showCancel: false,
					});
				}
			},

			clickLeft() {
				uni.navigateBack({
					delta: 1,
				});
			},
			setLoginInfo(result) {
				setAesKey(result.aesKey)
				setMd5Key(result.md5Key)
				setTicket(result.ticket)
				setLoginUserInfo(result.data);
				uni.redirectTo({
					url: "/pages/home/<USER>/p-home?type=login"
				})
			},
		},
	};
</script>
<style lang="scss" scoped>
	.register {
		padding-top: 40rpx;

		.login-form-group .code-img {
			width: 240upx;
			height: 90upx;
			margin-left: 20upx;
		}

		.cu-form-group .title {
			max-width: 180upx;
			min-width: 140upx;
			color: #333333;
		}

		.sendSMS {
			padding: 10rpx;
			color: #1D82D2;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.btn {
			margin-top: 20rpx;
			width: 100%;
			padding: 10rpx;


			.login-btn {

				width: 730rpx;
				height: 104rpx;
				font-size: 32rpx;
				line-height: 104rpx;
				text-align: center;
				color: #fff;
			}
		}
	}

	.GX-logo {
		width: 100%;
		text-align: center;
	}

	.home-img {
		height: 180rpx;
		width: 170rpx;
	}

	.login-title {
		color: #1D2225;
		font-size: 36rpx;
	}

	.sendSMS {
		padding: 10rpx;
		color: #1d82d2;
	}

	.sendSMS:active {
		background: #ddd;
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.color {
		color: #007aff;
	}

	.c-title {
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #ffffff;
	}

	.tips {
		color: #969696;
	}

	.tips>text {
		color: #007aff;
	}

	.marginBottom {
		margin-bottom: 30upx;
	}

	.img-title {
		/* margin-bottom: 30upx; */
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		margin-top: 30upx;
		background: #fff;
	}

	.consumer .cu-form-group .title {
		min-width: 180upx;
		color: #333333;
	}

	.consumer .cu-form-group input {
		font-size: 28rpx;
		color: #333333;
	}

	.load_desc {
		margin: 60rpx 36rpx;

		.desc_title {
			font-size: 28rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #000;
			margin-bottom: 20rpx;
		}

		.desc_text {
			text-align: left;
			line-height: 50rpx;
			text-indent: 2em;
			padding-bottom: 10rpx;
			font-size: 26rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #555555;
		}
	}
</style>