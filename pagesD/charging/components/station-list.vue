<template>
  <view class="station-list-container" :style="{ height: listHeight }">
    <!-- 筛选遮罩层 -->
    <view v-if="showFilterPage" class="filter-mask" @tap="hideFilter"></view>

    <!-- 筛选页面 -->
    <filter-page 
      v-if="showFilterPage" 
      :listHeight="listHeight" 
      :initialFilters="filterConditions" 
      @close="hideFilter" 
      @confirm="handleFilterConfirm" 
    />

    <!-- 上滑提示栏 -->
    <view class="drag-bar" @touchstart="handleTouchStart"
      @touchmove.stop.prevent="handleDragBarMove"
      @touchend="handleTouchEnd"
      :style="{ height: expanded ? '66rpx' : '86rpx', background: expanded ? 'transparent' : '' }">
      <view class="line"></view>
      <view v-if="!expanded" class="text">向上滑动查看更多充电桩</view>
    </view>

    <!-- 列表头部 -->
    <view class="list-header" @touchmove.stop.prevent>
      <view class="address-info">
        <view class="address-text">{{ currentAddress }}</view>
        <view class="nearby-label">
          <view class="nearby-label-text">附近充电站<view class="distance-summary" v-if="searchDistance">{{ searchDistance }}</view></view>
          
          <view class="filter" v-if="!minimized" @tap="showFilter">筛选</view>
        </view>
      </view>

    </view>



    <!-- 列表内容 -->
    <view class="list-content" :style="{ top: searchDistance ? '216rpx' : '196rpx' }">
      <station-item v-for="(item, index) in stationList" :currentLocation="currentLocation" :key="index" :data="item" :isFirst="index === 0" />
      <view v-if="!stationList || stationList.length === 0" class="no-stations">
        暂无充电站信息
      </view>
    </view>
  </view>
</template>

<script>
import StationItem from './station-item.vue'
import FilterPage from './filter-page.vue'

export default {
  name: 'StationList',
  components: {
    StationItem,
    FilterPage
  },
  props: {
    searchDistance: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      expanded: false,
      minimized: false,
      touchStartY: 0,
      touchMoveY: 0,
      showFilterPage: false,
      listHeight: '684rpx',
      currentLocation: '',
      currentAddress: '',
      distanceToMapCenterNearestStation: '',
      stationList: [],
      filterConditions: { facilities: [], chargeTypes: [] }
    }
  },
  methods: {
    handleTouchStart(e) {
      this.touchStartY = e.touches[0].pageY;
      this.touchMoveY = 0; // Reset moveY on new touch start
    },
    handleDragBarMove(e) {
      if (!this.touchStartY) return;

      this.touchMoveY = e.touches[0].pageY - this.touchStartY;

      // 向上滑动超过30px
      if (this.touchMoveY < -30) {
        if (this.minimized) {
          // 如果是缩略状态，恢复到正常状态
          this.minimized = false;
          this.expanded = false;
        } else if (!this.expanded) {
          // 如果是正常状态，则展开
          this.expanded = true;
        }
        this.touchStartY = 0; // Reset startY after state change to prevent immediate re-triggering
        this.updateListHeight();
      }
      // 向下滑动超过30px
      else if (this.touchMoveY > 30) {
        if (this.expanded) {
          // 如果是展开状态，恢复到正常状态
          this.expanded = false;
          this.minimized = false;
        } else if (!this.minimized) {
          // 如果是正常状态，则缩略
          this.minimized = true;
        }
        this.touchStartY = 0; // Reset startY after state change
        this.updateListHeight();
      }
    },
    handleTouchEnd() {
      // Reset touch start info on touch end
      this.touchStartY = 0;
      this.touchMoveY = 0;
    },
    updateListHeight() {
      let headerHeight = 96; // 基础头部高度
      let summaryHeight = this.searchDistance ? 30 : 0;
      let dragBarHeight = this.expanded ? 66 : 86;

      if (this.minimized) {
        this.listHeight = (headerHeight + dragBarHeight + summaryHeight) + 'rpx' // 仅头部 + 拖动条 + 概要
      } else if (this.expanded) {
        this.listHeight = 'calc(100vh - 16rpx)' // 全高减去一些边距
      } else {
         this.listHeight = '684rpx'; 
      }
      this.$emit('stateChange', {
        expanded: this.expanded,
        minimized: this.minimized
      })
    },
    showFilter() {
      this.showFilterPage = true
    },
    hideFilter() {
      this.showFilterPage = false
    },
    handleFilterConfirm(filterData) {
      console.log('筛选条件：', filterData)
      this.filterConditions = filterData;
      this.$emit('filter-change', this.filterConditions);
      // 筛选条件改变时触发事件通知父组件
      this.hideFilter()
    },
    setMinimized(value) {
      console.log(value, '----------------setMinimized');
      this.minimized = value
      if (value) {
        this.expanded = false
      }
      this.updateListHeight()
    },
    // 更新当前位置信息
    updateCurrentAddress(location, address) {
      this.currentLocation = location;
      this.currentAddress = address;
    },
    // 计算两点之间的距离
    calculateDistance(location1, location2) {
      const lat1 = location1.latitude
      const lon1 = location1.longitude
      const lat2 = parseFloat(location2.stationLat)
      const lon2 = parseFloat(location2.stationLng)
      
      const R = 6371 // 地球半径，单位km
      const dLat = (lat2 - lat1) * Math.PI / 180
      const dLon = (lon2 - lon1) * Math.PI / 180
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
        Math.sin(dLon / 2) * Math.sin(dLon / 2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
      const distance = R * c
      
      // 仅返回数字用于排序，在模板或项目组件中格式化
      return distance // 保留为数字用于排序
    },
    // 更新充电站列表数据
    updateStationList(data, location) {
        this.stationList = data.map(item => {
          
           let distanceValue = Number(item.distance);
           
           // 如果distance已经作为字符串提供，则解析它
           if (distanceValue) {
             distanceValue = parseFloat(distanceValue) / 1000; // 假设distance是以米为单位
           } else {
             // 否则，计算距离
             distanceValue = this.calculateDistance(location, item);
           }
           
          //  // 提取设施信息
          //  const facilities = [];
          //  if (item.isGas === '是') facilities.push('refuel');
          //  if (item.isPark === '是') facilities.push('parking');
          //  if (item.isDine === '是') facilities.push('restaurant');
          //  if (item.isWc === '是') facilities.push('toilet');
          //  if (item.isStore === '是') facilities.push('shop');
          //  if (item.isRepair === '是') facilities.push('repair');
           
           // 返回包含原始数据和处理后数据的组合
           return {
              ...item, // 保留所有原始字段
              distance: distanceValue,
              distanceFormatted: (distanceValue < 1) ? 
                 ((distanceValue * 1000).toFixed(0) + 'm') : 
                 (distanceValue.toFixed(1) + 'km'),
              fastCharge: parseInt(item.connectorFeeNum) || 0,
              totalFast: parseInt(item.connectorNum) || 0,
              slowCharge: parseInt(item.slowFreeNum) || 0,
              totalSlow: parseInt(item.slowNum) || 0,
              // facilities: facilities
            };
        });

      // 按距离升序排序
      this.stationList.sort((a, b) => a.distance - b.distance);

      // Update the distance to the NEAREST station from the MAP CENTER (internal state)
      this.distanceToMapCenterNearestStation = this.stationList.length > 0 ? this.stationList[0].distanceFormatted : '';
    }
  },
  watch: {
    // 监听expanded变化，确保高度更新
    expanded(newVal) {
      this.updateListHeight()
    },
    minimized(newVal) {
        this.updateListHeight();
    },
    searchDistance(newVal, oldVal) {
      console.log('searchDistance 已更改:', newVal);
      this.updateListHeight();
    }
  }
}
</script>

<style lang="scss" scoped>
.station-list-container {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  border-radius: 20rpx 20rpx 0 0;
  transition: height 0.3s ease;
  z-index: 9999;
  background: #FFFFFF;

  .drag-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 16rpx;

    .line {
      width: 146rpx;
      height: 8rpx;
      background: #9B9B9B;
      border-radius: 3rpx;
      margin-bottom: 16rpx;
    }

    .text {
      font-size: 24rpx;
      color: #666666;
    }
  }

  .list-header {
    height: auto;
    min-height: 96rpx;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20rpx 22rpx;

    .address-info {
       flex: 1;
       margin-right: 20rpx;
    }

    .address-text {
       font-size: 28rpx;
       color: #0066E9;
       font-weight: 500;
       margin-bottom: 8rpx;
    }

    .nearby-label {
       display: flex;
       font-size: 26rpx;
       color: #666666;
       line-height: 1.4;
       align-items: center;
       justify-content: space-between;
       .nearby-label-text{
         display: flex;
       }
    }
    
    .filter {
      font-weight: 400;
      font-size: 28rpx;
      color: #0066E9;
    }
  }

  .distance-summary {
     padding-left: 28rpx;
     font-size: 26rpx;
  }

  .list-content {
    width: 100%;
    position: absolute;
    top: 186rpx;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0 20rpx;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
  }

  .filter-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 998;
  }

  .no-stations {
    color: #999;
    font-size: 28rpx;
    text-align: center;
    padding: 40rpx 0;
  }
}
</style>