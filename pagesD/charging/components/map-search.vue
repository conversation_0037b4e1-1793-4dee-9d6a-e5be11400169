<template>
  <cover-view :class="['search-box', { 'fade-out': isHidden }]">
    <cover-view class="search-input" @click="handleSearch">
      <cover-image class="search-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/search.png" />
      <cover-view v-if="!searchValue" class="placeholder">请输入地点进行搜索充电站</cover-view>
      <cover-view v-else class="search-text">{{searchValue}}</cover-view>
    </cover-view>
  </cover-view>
</template>

<script>
export default {
  name: 'MapSearch',
  props: {

  },
  data() {
    return {
      searchValue: '',
      location: {}
    }
  },
  methods: {
    // 处理搜索框点击事件，调用地图选择位置接口
    handleSearch() {
      uni.chooseLocation({
        success: res => {
          this.searchValue = res.name;
          this.location = res;
          this.$emit('searchLocation', {
            location: this.location
          });
        },
        fail(err) {
          console.log(err);
        }
      });
    },
    
    // 更新组件内部存储的当前位置信息（通常由父组件调用）
    updateLocation(location) {
      this.location = location;
    }
  }
}
</script>

<style lang="scss" scoped>
.search-box {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 99999;
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 1;
  transform: translateY(0);
  box-sizing: border-box;
  &.fade-out {
    opacity: 0;
    transform: translateY(-20rpx);
  }

  .search-input {
    width: 710rpx;
    height: 80rpx;
    background: #FFFFFF;
    border-radius: 8rpx;
    padding: 20rpx;
    border: 1rpx solid #E2E2E2;
    display: flex;
    align-items: center;
    box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0,0,0,0.1);
    box-sizing: border-box;
    .search-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }
    
    .placeholder {
      color: #999;
      font-size: 28rpx;
      width: 500rpx;
      display: inline-block;
    }
    
    .search-text {
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>