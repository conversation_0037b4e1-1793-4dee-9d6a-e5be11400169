<template>
  <view class="station-item" :class="{ 'is-first': isFirst }" @click="goToStationDetail">
    <view class="station-header">
      <view class="name">
        <text class="station-name-text">{{ data.stationName }}</text>
        <text class="status" :class="statusClass">{{ statusText }}</text>
      </view>
      <view class="distance">距您{{ data.distanceFormatted }}</view>
    </view>

    <view class="facility-list">
      <!-- <view class="facility-icons">
        <image v-for="(icon, index) in activeFacilityIcons" :key="index" :src="getIconSrc(icon.lightSrc)"
          class="facility-icon cover-image" />
      </view> -->

      <view class="price-btn">当前电价</view>
    </view>

    <view class="charging-info">
      <view class="charge-types">
        <view class="fast-charge">
          <view class="title">
            快充：
          </view>
          <!-- <image class="cover-image" :src="getIconSrc('fast-charge.png')" /> -->
          <view class="text">
            <view class="bold-text" :class="{ 'zero-count': data.quitFreeNum === 0 }">闲{{ data.quitFreeNum }}</view>
            <view>/共{{ data.quitNum }}</view>
          </view>
        </view>
        <view class="slow-charge">
          <view class="title">
            慢充：
          </view>
          <!-- <image class="cover-image" :src="getIconSrc('slow-charge.png')" /> -->
          <view class="text">
            <view class="bold-text" :class="{ 'zero-count': data.slowFreeNum === 0 }">闲{{ data.slowFreeNum }}</view>
            <view>/共{{ data.slowNum }}</view>
          </view>
        </view>
      </view>
      <view class="price-info">
        <view class="price">{{ data.electricityFee || '未知' }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StationItem',
  props: {
    data: {
      type: Object,
      required: true
    },
    currentLocation: {
      type: Object,
      required: true
    },
    isFirst: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      statusMapData: {
        50: { text: '营业中', class: 'operating' },
        6: { text: '维护中', class: 'gray' },
        1: { text: '建设中', class: 'gray' },
        5: { text: '关停', class: 'closed' },
        0: { text: '未知', class: 'unknown' }
      },
      facilityIcons: [
        {
          id: 1,
          name: '充电桩',
          key: 'wifi',
          lightSrc: 'chargingStation-light.png',
          graySrc: 'chargingStation-gray.png'
        },
        {
          id: 2,
          name: '加油站',
          key: 'refuel',
          lightSrc: 'refuel-light.png',
          graySrc: 'refuel-gray.png'
        },
        {
          id: 3,
          name: '停车场',
          key: 'parking',
          lightSrc: 'parking-light.png',
          graySrc: 'parking-gray.png'
        },
        {
          id: 4,
          name: '餐厅',
          key: 'restaurant',
          lightSrc: 'eat-light.png',
          graySrc: 'eat-gray.png'
        },
        {
          id: 5,
          name: '卫生间',
          key: 'toilet',
          lightSrc: 'wc-light.png',
          graySrc: 'wc-gray.png'
        },
        {
          id: 6,
          name: '便利店',
          key: 'shop',
          lightSrc: 'store-light.png',
          graySrc: 'store-gray.png'
        },
        {
          id: 7,
          name: '维修站',
          key: 'repair',
          lightSrc: 'repair-light.png',
          graySrc: 'repair-gray.png'
        }
      ]
    }
  },
  computed: {
    // 根据状态获取对应的文本描述
    statusText() {
      const status = this.data.stationStatus || 0;
      return this.statusMapData[status]?.text || '未知';
    },
    // 根据状态获取对应的 CSS 类名
    statusClass() {
      const status = this.data.stationStatus || 0;
      return this.statusMapData[status]?.class || 'unknown';
    },
    // 获取活跃的设施图标
    activeFacilityIcons() {
      if (!this.data.facilities || !Array.isArray(this.data.facilities)) {
        return [];
      }
      
      return this.facilityIcons.filter(icon => 
        this.data.facilities.includes(icon.key)
      );
    }
  },
  methods: {
    // 根据图标文件名获取完整静态资源路径
    getIconSrc (iconName) {
      return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/' + iconName
    },
    goToStationDetail() {
      let location = uni.getStorageSync('location')
      uni.navigateTo({
        url: `/pagesD/charging/chargingDetail/index?stationId=${this.data.stationId}&lat=${location.latitude}&lng=${location.longitude}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.station-item {
  width: 710rpx;
  min-height: 190rpx;
  box-sizing: border-box;
  background: #FFFFFF;
  padding: 20rpx;
  border: 1rpx solid #E7E7E7;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  margin-bottom: 24rpx;
  
  &.is-first {
    border-color: #78B0F9;
    border-width: 3rpx;
  }

  .station-header {
    width: 666rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 14rpx;
    position: relative;

    .name {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      display: inline-flex;
      align-items: center;

      .station-name-text {
        display: inline-block;
        width: 350rpx;
        overflow-wrap: break-word;
        white-space: normal;
        vertical-align: middle;
      }

      .status {
        margin-left: 14rpx;
        font-size: 24rpx;
        height: 34rpx;
        padding: 0 24rpx;
        line-height: 34rpx;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        margin-left: 14rpx;
        vertical-align: middle;
        flex-shrink: 0;

        &.operating {
          color: #08BA81;
          background: rgba(8, 186, 129, 0.1);
        }
        &.gray {
          color: #999999;
          background: rgba(153, 153, 153, 0.1);
        }
        &.closed {
          color: #F82A3C;
          background: rgba(248, 42, 60, 0.1);
        }
        &.unknown {
          color: #999999;
          background: rgba(153, 153, 153, 0.1);
        }
      }
    }

    .distance {
      width: 160rpx;
      position: absolute;
      right: 0;
      text-align: right;
      font-size: 24rpx;
      color: #999999;
      margin-right: 5rpx;
    }
  }

  .facility-list {
    width: 666rpx;
    display: flex;
    margin-bottom: 12rpx;
    height: 50rpx;
    position: relative;

    .facility-icons {
      display: flex;
      align-items: center;

      .cover-image {
        width: 34rpx;
        height: 34rpx;
        margin-right: 8rpx;
      }

      .facility-icon {
        width: 34rpx;
        height: 34rpx;
        margin-right: 36rpx;
      }
    }

    .price-btn {
      width: 120rpx;
      height: 42rpx;
      background: #FFFFFF;
      position: absolute;
      right: 5rpx;
      border-radius: 10rpx 10rpx 10rpx 10rpx;
      border: 1rpx solid #4F90FF;
      font-size: 24rpx;
      color: #4F90FF;
      text-align: center;
      line-height: 42rpx;
      // margin-left: 190rpx;
    }


  }

  .charging-info {
    display: flex;
    width: 666rpx;
    align-items: center;
    position: relative;

    .charge-types {
      display: flex;
      // width: 430rpx;

      .fast-charge,
      .slow-charge {
        display: flex;
        // width: 160rpx;
        align-items: center;
        margin-right: 32rpx;

        .title {
          font-size: 28rpx;
          color: #666;
          margin-right: 8rpx;
        }

        .cover-image {
          width: 34rpx;
          height: 34rpx;
          margin-right: 8rpx;
        }

        .text {
          display: flex;
          min-width: 120rpx;
          align-items: center;
          font-weight: 400;
          font-size: 28rpx;
          color: #333;

          .bold-text {
            // min-width: 74rpx;
            margin-right: 2rpx;

            &.zero-count {
              color: #333;
              font-weight: 500;
            }
          }
        }
      }
    }

    .price-info {
      width: 180rpx;
      text-align: right;
      position: absolute;
      right: 0;
      font-size: 28rpx;
      color: #666666;

      // margin-right: 10rpx;
      .price {
        color: #333;

        text {
          color: #999999;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>