<template>
  <cover-view class="filter-mask" @tap.stop>
    <cover-view class="filter-page" :style="{ height: pageHeight }">
      <cover-view class="filter-container">
        <!-- 头部 -->
        <cover-view class="header">
          <cover-view class="title">筛选</cover-view>
          <cover-view class="close" @tap="onClose">×</cover-view>
        </cover-view>

        <!-- 提供服务 -->
        <cover-view class="section">
          <cover-view class="section-title">提供服务</cover-view>
          <cover-view class="options-grid">
            <cover-view class="option-item active">充电站</cover-view>
            <cover-view 
              v-for="item in facilities" 
              :key="item.id"
              :class="['option-item', selectedFacilities.includes(item.id) ? 'active' : '']"
              @tap="toggleFacility(item.id)"
            >
              {{ item.name }}
            </cover-view>
          </cover-view>
        </cover-view>

        <!-- 充电类型 -->
        <cover-view class="section">
          <cover-view class="section-title">充电类型</cover-view>
          <cover-view class="options-grid">
            <cover-view 
              v-for="item in chargeTypes" 
              :key="item.id"
              :class="['option-item', selectedChargeTypes.includes(item.id) ? 'active' : '']"
              @tap="toggleChargeType(item.id)"
            >
              {{ item.name }}
            </cover-view>
          </cover-view>
        </cover-view>

        <!-- 底部按钮 -->
        <cover-view class="footer">
          <cover-view class="clear-btn" @tap="clearAll">清除选项</cover-view>
          <cover-view class="confirm-btn" @tap="confirmFilter">确定</cover-view>
        </cover-view>
      </cover-view>
    </cover-view>
  </cover-view>
</template>

<script>
export default {
  name: 'FilterPage',
  props: {
    listHeight: {
      type: String,
      default: '694rpx'
    },
    initialFilters: {
      type: Object,
      default: () => ({ facilities: [], chargeTypes: [] })
    }
  },
  data() {
    return {
      facilities: [
        { id: 1, name: '便利店' },
        { id: 2, name: '公共厕所' },
        { id: 3, name: '加油站' },
        { id: 4, name: '餐厅' },
        // { id: 5, name: '司机之家' },
        { id: 6, name: '维修' },
        { id: 7, name: '停车场' }
      ],
      chargeTypes: [
        { id: 1, name: '慢充' },
        { id: 2, name: '快充' },
        { id: 3, name: '超充' }
      ],
      selectedFacilities: [],
      selectedChargeTypes: []
    }
  },
  computed: {
    pageHeight() {
      return this.listHeight
    }
  },
  mounted() {
    if (this.initialFilters) {
      if (Array.isArray(this.initialFilters.facilities)) {
        this.selectedFacilities = this.initialFilters.facilities.slice();
      }
      if (Array.isArray(this.initialFilters.chargeTypes)) {
        this.selectedChargeTypes = this.initialFilters.chargeTypes.slice();
      }
    }
  },
  methods: {
    toggleFacility(id) {
      const index = this.selectedFacilities.indexOf(id)
      if (index === -1) {
        this.selectedFacilities.push(id)
      } else {
        this.selectedFacilities.splice(index, 1)
      }
    },
    toggleChargeType(id) {
      const index = this.selectedChargeTypes.indexOf(id)
      if (index === -1) {
        this.selectedChargeTypes = [id]
      } else {
        this.selectedChargeTypes = []
      }
    },
    clearAll() {
      this.selectedFacilities = []
      this.selectedChargeTypes = []
    },
    confirmFilter() {
      this.$emit('confirm', {
        facilities: this.selectedFacilities,
        chargeTypes: this.selectedChargeTypes
      })
    },
    onClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-mask {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 998;
}

.filter-page {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #FFFFFF;
  z-index: 999;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;

  .filter-container {
    height: 100%;
    padding: 30rpx;
    padding-bottom: 120rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40rpx;

      .title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333333;
      }

      .close {
        padding: 10rpx;
        font-size: 40rpx;
        color: #999999;
      }
    }

    .section {
      margin-bottom: 40rpx;

      .section-title {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 24rpx;
      }

      .options-grid {
        display: flex;
        flex-wrap: wrap;
        margin: -10rpx;  // 通过负边距实现等间距

        .option-item {
          width: calc(25% - 20rpx); // 四列布局
          height: 64rpx;
          line-height: 64rpx;
          text-align: center;
          font-size: 26rpx;
          color: #666666;
          background: #F5F7FA;
          border-radius: 8rpx;
          margin: 10rpx;  // 统一的外边距

          &.active {
            background: #ECF5FF;
            color: #4A90FA;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 20rpx 30rpx;
      display: flex;
      justify-content: space-between;
      background: #FFFFFF;
      border-top: 1rpx solid #EEEEEE;

      .clear-btn, .confirm-btn {
        width: calc(50% - 10rpx);
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        border-radius: 8rpx;
        font-size: 28rpx;
      }

      .clear-btn {
        background: #F5F7FA;
        color: #333333;
      }

      .confirm-btn {
        background: #4A90FA;
        color: #FFFFFF;
      }
    }
  }
}
</style>
