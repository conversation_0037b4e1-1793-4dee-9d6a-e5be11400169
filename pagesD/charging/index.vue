<template>
  <view class="charging">
    <!-- 地图组件 -->
    <MapWrapCom ref="MapWrapCom" needGeocoder :markers="markers" :includePoints="includePoints" :scale="scale"
      :mapLocation="mapLocation" :clusterConfig="clusterConfig" :customClusterStyles="null" :enableCenterLocation="true"
      height="100vh" @onmarkertap="onmarkertap" @updateLocation="updateLocation"
      @centerLocationChange="handleCenterLocationChange"
      @onregionchange="handleRegionChange" @oncallouttap="calloutTap">
      <!-- 使用与order-detail.vue相似的callout实现 -->
      <template #callout>
        <cover-view class="callout-container">
          <cover-view v-for="(item, idx) in markers" :key="item.id" class="callout" :class="item.id == 1 ? 'callout-big' : ''"
            :marker-id="item.id" v-if="item.showCallout" @click="callOutClick(item)">
            <cover-view class="callout-content">
              <cover-image class="callout-bg" :src="item.id == 1
                  ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/marker-bg.png'
                  : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/marker-bg1.png'
                "></cover-image>
              <cover-view class="callout-text">
                <cover-view class="callout-text-bold">闲{{ item.connectorFeeNum }}</cover-view>
                <cover-view class="callout-text-normal">/{{ item.connectorNum }}</cover-view>
              </cover-view>
            </cover-view>
          </cover-view>
        </cover-view>
      </template>
    </MapWrapCom>

    <!-- 回到视野中心 -->
    <view class="move-center" @click="handleMoveCenter" :style="{ top: listMinimized ? '1158rpx' : '658rpx' }">
      <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/orientation.png" mode=""></image>
    </view>

    <!-- 搜索框组件 -->
    <MapSearch ref="mapSearch" @searchLocation="handleSearchLocation" v-if="!isSearchHidden" />

    <!-- 充电站列表组件，监听 stateChange 和 filter-change 事件 -->
    <StationList 
      ref="StationList" 
      :searchDistance="searchResultDistanceString"
      @stateChange="handleListStateChange"
      @filter-change="handleFilterChange"
      v-show="(markers.length > 0 || hasActiveFilters) && dataLoaded"
    />

    <!-- 无数据提示 -->
    <view class="no-data" v-if="markers.length === 0 && !hasActiveFilters && dataLoaded">
      定位地点周边无充电站，请尝试切换定位。
    </view>
  </view>
</template>

<script>
import MapWrapCom from "@/pagesD/components/map/map.vue";
import MapSearch from "@/pagesD/charging/components/map-search.vue";
import StationList from "@/pagesD/charging/components/station-list.vue";

export default {
  components: {
    MapWrapCom,
    MapSearch,
    StationList
  },
  data() {
    return {
      scale: 15,
      markers: [],
      includePoints: [],
      mapLocation: {},
      currentLocation:{},
      dataLoaded: false, // 新增：标记数据是否加载完成
      searchResultDistanceString: '',
      isSearchHidden: false,
      listMinimized: false,
      stationList: [],
      lastCenterLocation: null, // 添加上一次中心点位置
      programmaticMoveInProgress: false, // 添加：用于跟踪程序化地图移动的标志
      updateTimeout: null, // 用于防抖
      clusterConfig: {
        enable:true,
        enableDefaultStyle: true,
        gridSize: 30,
        zoomOnClick: true,
      },
      visibleMarkers: [],
      deviceInfo: null,
      // 添加标记模板配置
      markerTemplate: {
        zIndex: "1",
        rotate: 0,
        width: 6,
        height: 6,
        iconPath: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/touming.png',
        anchor: {
          x: 0.5,
          y: 0.5
        },
        customCallout: {
          display: "ALWAYS",
          anchorY: 0,
          anchorX: 0
        }
      },
      currentFilters: { facilities: [], chargeTypes: [] }, // 新增：存储当前筛选条件
    };
  },
  computed: {
    hasActiveFilters() {
      return this.currentFilters.facilities.length > 0 || this.currentFilters.chargeTypes.length > 0;
    }
  },
  methods: {
    onLoad() {
      // 获取设备信息
      this.deviceInfo = uni.getSystemInfoSync();
    },
    // 获取充电站数据 (签名修改，增加 filters 参数)
    async getChargingStations(location, filters) {
      
      try {
        this.dataLoaded = false; // 重置加载状态
        // 获取当前筛选条件 (如果未传入则使用 data 中的)
        const currentFiltersToUse = filters || this.currentFilters;
        
        // 直接使用ID数组并转换为逗号分隔的字符串
        const chargeTypeIds = currentFiltersToUse.chargeTypes || [];
        const facilityIds = currentFiltersToUse.facilities || [];
        
        // 格式化为逗号分隔字符串
        const chargeTypeString = chargeTypeIds.length > 0 ? chargeTypeIds.join(',') : '';
        const serviceString = facilityIds.length > 0 ? facilityIds.join(',') : '';

        // 调用正确的接口并传递参数
        const res = await this.$request.post(this.$interfaces.getChargingStations, { 
          data: {
            lat: location.latitude,   
            lng: location.longitude, 
            chargeType: chargeTypeString,
            service: serviceString,
            pageNo: 1,
            pageSize: 9999, 
            distance: 50
          }
        });

        let stations = [];
        if (res && res.data) {
          let markers = res.data.stationInfos.map((item, index) =>
            this.createMarker(item, index)
          );
          // 设置markers
          console.log(markers, 'markers');
          
          this.markers = markers;

          // // // 等待标记渲染完成后再显示callout
          // await this.$nextTick(() => {
          //   this.showCallouts();
          // });

          stations = res.data.stationInfos;
          // 更新列表组件的数据
          if (this.$refs.StationList) {
            console.log(stations, 'stations');
            
            this.$refs.StationList.updateStationList(stations)
          }
        } else {
          // 处理无数据或API错误的情况
          this.markers = [];
          if (this.$refs.StationList) {
            this.$refs.StationList.stationList = [];
          }
        }

        return stations;
      } catch (err) {
        console.error("获取充电站数据失败:", err);
        uni.showToast({
          title: "获取充电站数据失败",
          icon: "none"
        });
        this.markers = []; // 确保出错时 markers 清空
        if (this.$refs.StationList) {
          this.$refs.StationList.stationList = [];
        }
        return [];
      } finally {
        this.dataLoaded = true; // 标记加载完成
      }
    },

    // 显示所有callout
    showCallouts() {
      this.markers.forEach(marker => {
        this.$set(marker, 'showCallout', true);
        // marker.showCallout = true;
      });
    },

    // 创建地图标记点
    createMarker(item, index) {
      // 提取公共属性
      const connectorFeeNum = item.connectorFeeNum || 0;
      const connectorNum = item.connectorNum || 0;

      // 创建marker，使用customCallout而不是原生callout
      return {
        ...this.markerTemplate,
        id:  index+1,
        latitude: item.stationLat,
        longitude: item.stationLng,
        title: item.stationName,
        stationId: item.stationId,
        type: "charging",
        customCallout: {
          display: "ALWAYS",
          anchorY: 13,
          anchorX: 0
        },
        connectorFeeNum: connectorFeeNum,
        connectorNum: connectorNum,
        joinCluster: true,
        showCallout: false
      };
    },

    // 聚合样式
    getCustomClusterStyle(cluster) {
      const count = cluster.markerIds.length;
      if (!this.deviceInfo) {
        this.deviceInfo = uni.getSystemInfoSync();
      }
      const isAndroid = this.deviceInfo.platform.toLowerCase() === "android";
      const anchorX = isAndroid ? -22 : 0;

      // 为安卓平台提供特定的聚合样式
      return {
        iconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/cluster-icon.png",
        width: 43,
        height: 48,
        zIndex: 2, // 添加 zIndex 确保图标在正确的层级
        label: {
          content: count + "",
          fontSize: 18,
          color: "#fff",
          width: 43,
          height: 48,
          textAlign: "center",
          anchorX: anchorX,
          anchorY: -53,
          zIndex: 3 // 确保标签在图标上层
        }
      };
    },

    async updateLocation(location) {
      try {
        this.searchResultDistanceString = ''; 
        uni.setStorageSync('location', location); 
        // 设置一个加载超时保护
        const loadingTimeout = setTimeout(() => {
          uni.hideLoading();
          uni.showToast({
            title: '加载超时，请重试',
            icon: 'none'
          });
        }, 10000); // 10秒超时
        
        this.mapLocation = location;
        // 同步更新搜索框组件的位置信息
        this.$refs.mapSearch.updateLocation(location);

        // 先获取地址信息
        await new Promise((resolve) => {
          this.getAddressInfo({
            ...location,
            callback: () => resolve()
          });
        });

        // 等待地址信息获取完成后，再初始化地图数据
        await this.initMapInfo(location);

        // 清除超时保护
        clearTimeout(loadingTimeout);
      } catch (error) {
        console.error('更新位置信息失败：', error);
        uni.hideLoading();
        uni.showToast({
          title: '更新位置信息失败',
          icon: 'none'
        });
      }
    },

    // 获取地址信息（逆地址解析）
    getAddressInfo(location) {
      const key = this.$store.state.mapKey;
      uni.request({
        url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${location.latitude},${location.longitude}&key=${key}&get_poi=1`,
        success: (res) => {
          const { data } = res;

          if (data.status === 0 && data.result) {
            // 将地址信息存储到本地
            uni.setStorageSync('currentAddress', data.result);
            
            // 更新StationList组件的地址信息
            if (this.$refs.StationList) {
              let address = data.result.formatted_addresses?.recommend || data.result.address;
              this.$refs.StationList.updateCurrentAddress(location,address);
            }
          } else {
            // 处理腾讯地图API返回的错误状态
            console.error('腾讯地图API返回错误:', data);
          }
          // 调用回调函数
          location.callback && location.callback();
        },
        fail: (err) => {
          console.error('获取地址信息请求失败：', err);
          // 即使失败也要调用回调函数
          location.callback && location.callback();
        }
      });
    },

    async initMapInfo(location) {
      uni.showLoading({
        title: `地图数据加载中`
      });
      try {
        // 调用 getChargingStations, 不传 filters 会默认使用 this.currentFilters
        await this.getChargingStations(location);
        
        setTimeout(() => {
          this.onMarkerReady();
          uni.hideLoading();
        }, 100);

      } catch (err) {
        console.error("初始化地图数据失败:", err);
        uni.hideLoading();
        uni.showToast({
          title: "加载地图数据失败",
          icon: "none"
        });
      }
    },
    // 其他必要方法
    onmarkertap(e) {
      console.log("标记点击:", e);

      // 解决安卓点击后callout消失的问题
      if (this.deviceInfo.platform.toLowerCase() === "android") {
        // 获取被点击的marker的id
        const markerId = e.markerId;

        // 找到对应的marker
        const marker = this.markers.find(m => m.id == markerId);
        if (marker) {
          // 确保customCallout始终显示
          marker.customCallout = marker.customCallout || {};
          marker.customCallout.display = "ALWAYS";
          marker.showCallout = true;
          // this.$set(marker, 'showCallout', true);
        }
      }
    },

    handleCenterLocationChange(location) {
      // 处理地图中心点变化
    },

    // handleSearchLocation 中调用 initMapInfo 时使用当前筛选
    async handleSearchLocation(value) {
      try {
        // 读取存储的当前设备位置
        const currentDeviceLocation = uni.getStorageSync('location');

        // 计算距离 - 使用存储的当前设备位置
        if (currentDeviceLocation && currentDeviceLocation.latitude && value.location && value.location.latitude) {
          const distance = this.calculateDistance(
            currentDeviceLocation.latitude,
            currentDeviceLocation.longitude,
            value.location.latitude,
            value.location.longitude
          );
          if (typeof distance === 'number' && !isNaN(distance)) {
            this.searchResultDistanceString = `距离您 ${(distance / 1000).toFixed(1)} 公里`;
            console.log(`选择的位置 "${value.location.name}" ${this.searchResultDistanceString}`);
          } else {
             console.warn("计算出的距离无效。");
          }
        } else {
          console.warn("无法计算距离，当前设备位置或选择位置信息不完整或未存储。");
        }

        this.markers = [];
        // 调用 initMapInfo, 不传 filters 会默认使用 this.currentFilters
        await this.initMapInfo(value.location);
        this.programmaticMoveInProgress = true; // Set flag before changing map location
        this.mapLocation = value.location; // 更新地图中心点为搜索的位置 (保持这个逻辑，让地图移动过去)
        // includePoints 和 stationList 的更新逻辑可能需要调整，取决于 getChargingStations 的返回值
      } catch (err) {
        console.error("搜索充电站失败:", err);
        uni.showToast({
          title: "搜索失败",
          icon: "none"
        });
      }
    },
    handleExpand(expanded) {
      this.isSearchHidden = expanded;
    },
    handleMoveCenter() {
      // Changed: Read location from storage instead of fetching real-time location
      const storedLocation = uni.getStorageSync('location');

      if (storedLocation && typeof storedLocation.latitude === 'number' && typeof storedLocation.longitude === 'number') {
        if (this.$refs.MapWrapCom && this.$refs.MapWrapCom.mapCtx) {
           this.$refs.MapWrapCom.mapCtx.moveToLocation({
            latitude: storedLocation.latitude,
            longitude: storedLocation.longitude,
            fail: (moveErr) => {
              console.error('moveToLocation 调用失败:', moveErr);
              uni.showToast({
                title: '无法移动地图视角',
                icon: 'none'
              });
            }
          });
        } else {
          console.error('地图组件或上下文未准备好');
          uni.showToast({
            title: '地图未准备好',
            icon: 'none'
          });
        }
      } else {
        console.warn('Storage 中无有效位置信息，无法移动地图。');
        uni.showToast({
          title: '无有效位置信息',
          icon: 'none'
        });
      }
    },
    // 处理marker准备完成
    async onMarkerReady() {
      console.log("准备markers，数量:", this.markers.length);
      if (this.markers && this.markers.length > 0) {
        // 先清除所有现有标记
        if (this.$refs.MapWrapCom) {
          try {
            // 清除现有聚合
            await this.$refs.MapWrapCom.clearCluster();

            // 初始化聚合并添加标记点
            await this.$refs.MapWrapCom.addClusterMarkers(this.markers);
            await this.$refs.MapWrapCom.initMarkerCluster();

            // 等待标记渲染完成后再显示callout
            setTimeout(() => {
              this.showCallouts();
            }, 300);
          } catch (error) {
            console.error("标记点准备过程中出错:", error);
          }
        }
      }
    },
    // 计算两点之间的距离（米）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371000; // 地球半径，单位米
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    },

    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI / 180);
    },

    // 处理地图区域变化
    handleRegionChange(e) {
      let { detail } = e;
      console.log(detail, 'handleRegionChange');

      // 清除之前的超时
      if (this.updateTimeout) {
        clearTimeout(this.updateTimeout);
      }

      // 只处理结束事件
      if (detail.type === 'end') {
        // 使用防抖，延迟500ms执行
        this.updateTimeout = setTimeout(() => {
          // 获取地图中心点坐标
          this.$refs.MapWrapCom.mapCtx.getCenterLocation({
            success: (res) => {
              const currentLocation = {
                latitude: res.latitude,
                longitude: res.longitude
              };

              // 检查移动是程序触发的还是手动触发的
              if (this.programmaticMoveInProgress) {
                this.programmaticMoveInProgress = false; // 重置标志
              } else {
                // 假定是手动拖动（如果标志为false）
                this.searchResultDistanceString = ''; // 仅在手动拖动时重置距离字符串
              }

              // 现有位置比较和更新逻辑 
              if (!this.lastCenterLocation) {
                this.lastCenterLocation = currentLocation;
                return;
              }

              // 计算与上一次位置的距离（米）
              const distance = this.calculateDistance(
                this.lastCenterLocation.latitude,
                this.lastCenterLocation.longitude,
                currentLocation.latitude,
                currentLocation.longitude
              );

              // 只有当移动距离超过100米时才更新
              if (distance > 100) {
                console.log('位置变化超过100米，更新数据');
                
                // 更新上一次位置
                this.lastCenterLocation = currentLocation;
                
                // 存储新的位置信息
                uni.setStorageSync('currentLocation', currentLocation);
                
                // 获取地址信息
                this.getAddressInfo(currentLocation);
                
                // 初始化地图数据
                this.$nextTick(() => {
                  this.initMapInfo(currentLocation);
                });
              }
            }
          });
        }, 500); // 500ms 的防抖延迟
      }
    },

    // 处理列表状态变化
    handleListStateChange({ expanded, minimized }) {
      this.isSearchHidden = expanded;
      this.listMinimized = minimized;
      // 可以根据需要处理其他状态变化
    },

    calloutTap(e) {
      console.log('Native callout tapped:', e);
      const markerId = e.detail?.markerId;
      
      if (typeof markerId !== 'number' || markerId <= 0) {
        console.error('Invalid markerId received from calloutTap:', markerId);
        return; // 无效 ID，退出
      }

      // markerId 是从 1 开始的，数组索引是 markerId - 1
      const markerIndex = markerId - 1;
      const marker = this.markers?.[markerIndex];

      if (!marker || !marker.stationId) {
        console.error(`Marker data or stationId not found for markerId: ${markerId}`);
        uni.showToast({ title: '无法获取充电站信息', icon: 'none' });
        return;
      }
      
      const location = uni.getStorageSync('location');
      if (!location || !location.latitude || !location.longitude) {
        console.error('Current location not found in storage.');
        uni.showToast({ title: '无法获取当前位置', icon: 'none' });
        return;
      }

      // 执行导航
      uni.navigateTo({
        url: `/pagesD/charging/chargingDetail/index?stationId=${marker.stationId}&lat=${location.latitude}&lng=${location.longitude}`
      });
    },

    // 新增：处理筛选条件变化的方法
    handleFilterChange(filterConditions) {
      console.log("筛选条件已变化:", filterConditions);
      this.currentFilters = filterConditions;
      // 使用新的筛选条件重新获取数据
      const locationToUse = Object.keys(this.currentLocation).length ? this.currentLocation : this.mapLocation;
      
      this.initMapInfo(locationToUse, this.currentFilters);
    },
    callOutClick(item) {
      console.log('Callout clicked:', item);
      // 之前的导航逻辑已移除
    }
  }
};
</script>

<style lang="scss" scoped>
.charging {
  position: relative;
  height: 100%;

  .move-center {
    position: fixed;
    top: 658rpx;
    right: 14rpx;
    width: 100rpx;
    height: 100rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

.callout-container {
  position: absolute;
  width: 198rpx;
  height: 78rpx;

  left: 0;
  top: 0;
  pointer-events: none;
  z-index: 9999;
}

.callout {
  position: absolute;
  z-index: 9999;
  pointer-events: auto;
  transform: translate(-50%, -100%);
  /* 水平居中并垂直向上偏移 */
  width: 198rpx;
  height: 78rpx;

  .callout-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 198rpx;
    height: 78rpx;
    z-index: -1;
  }

  .callout-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .callout-text {
    // min-width: 130rpx;
    display: flex;
    align-items: center;
    color: #333;
    font-size: 28rpx;
    position: absolute;
    top: 18rpx;
    left: 68rpx;
    font-style: normal;

    .callout-text-bold {
      font-weight: 600;
      min-width: 65rpx;
      text-align: right;
    }

    .callout-text-normal {
      font-weight: 400;
      color: #000;
    }
  }

  &.callout-big {
    width: 232rpx;
    height: 94rpx;

    .callout-bg {
      width: 232rpx;
      height: 94rpx;
    }

    .callout-text {
      top: 22rpx;
      left: 86rpx;
      color: #fff;

      .callout-text-normal {
        color: #d4fff1;
      }
    }
  }
}

// 新增：无数据提示样式
.no-data {
  width: 100%; // 匹配屏幕宽度
  height: 116rpx;
  border-radius: 16rpx;
  background: linear-gradient( 180deg, #FFF4E6 0%, rgba(255,251,247,0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 28rpx;
  color: #FF9000;
  box-sizing: border-box;
  position: fixed;
  bottom: 0; // 定位在底部，留出安全距离或根据 StationList 位置调整
  left: 0;
  right: 0;
  z-index: 10; // 确保在地图之上，但在搜索框等之下（如果需要）
}
</style>
