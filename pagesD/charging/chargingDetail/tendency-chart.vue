<template>
  <view class="charging-price-chart">
    <echarts-uniapp ref="chart" :option="chartOption" :canvas-id="canvasId" />
  </view>
</template>

<script>
import echartsUniapp from '@/pagesD/components/echarts-uniapp/echarts-uniapp.vue'

export default {
  name: 'ChargingPriceChart',
  components: {
    echartsUniapp
  },
  props: {
    priceData: {
      type: Array,
      default: () => []
    },
    width: {
      type: Number,
      default: 750
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data () {
    return {
      canvasId: 'charging-price-chart',
      chartOption: null
    }
  },
  methods: {
    // 根据传入的 priceData 更新 ECharts 图表配置
    updateChartOption () {
      if (!this.priceData || !this.priceData.length) {
        console.warn('No price data available')
        return
      }

      const hours = Array.from({ length: 25 }, (_, i) => `${i}:00`)

      this.chartOption = {
        animation: false,
        grid: {
          left: '4%',
          right: '3%',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line'
          },
          formatter: (params) => {
            const data = params[0];
            return `${data.name} 基础价：¥${data.value.toFixed(2)}`;
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: hours,
          axisLabel: {
            color: '#999',
            fontSize: 12,
            interval: 5
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f5f5f5'
            }
          }
        },
        yAxis: {
          type: 'value',
          position: 'right',
          min: 0,
          max: 2.5,
          interval: 0.5,
          axisLabel: {
            formatter: '{value}元',
            color: '#999',
            fontSize: 12
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f5f5f5'
            }
          },
          axisLine: { show: false },
          axisTick: { show: false }
        },
        series: [
          {
            name: '基础价',
            data: this.priceData,
            type: 'line',
            step: 'start',
            smooth: false,
            symbol: 'circle',
            symbolSize: 4,
            showSymbol: true,
            lineStyle: {
              width: 2,
              color: '#4080FF'
            },
            itemStyle: {
              color: '#4080FF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(64,128,255,0.2)'
                }, {
                  offset: 1,
                  color: 'rgba(64,128,255,0.05)'
                }]
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    // 监听 priceData 的变化，并在有有效数据时更新图表
    priceData: {
      handler (newVal) {
        if (newVal && newVal.length) {
          this.updateChartOption()
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-price-chart {
  width: 100%;
  height: 300px;
  background: #fff;
  border-radius: 8rpx;
  padding: 12rpx;
  box-sizing: border-box;

  :deep(.echarts) {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
