<template>
  <view>
    <!-- 遮罩层 -->
    <view v-if="show" class="mask" @tap="closePopup" @touchmove.prevent></view>

    <!-- 弹窗内容 -->
    <view v-if="show" class="electric-detail" @touchmove.stop>
      <!-- 头部 -->
      <view class="header">
        <text class="title">充电费</text>
        <view class="close" @click="closePopup">
          <image class="image" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/close.png"></image>
        </view>
      </view>

      <!-- 切换标签 -->
      <view class="tab-wrapper">
        <u-tabs :list="tabList" :is-scroll="false" :current="currentTab" activeColor="#fff" inactiveColor="#333"
          :showBar="false" bgColor="#F7F7F7" :bold="false" :active-item-style="{
            color: '#fff',
            backgroundColor: '#0066E9'
          }" @change="handleTabChange" />
      </view>

      <!-- 内容区域 -->
      <view class="content">
        <!-- 趋势图表 -->
        <view class="chart-content" v-show="currentTab === 0">
          <!-- 加载中状态 -->
          <view class="loading-state" v-if="isLoading">
            <u-loading color="#0066E9" size="32"></u-loading>
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 无数据状态 -->
          <view class="empty-state" v-else-if="formattedPriceList.every(price => price === 0)">
            <text class="empty-text">暂无价格数据</text>
          </view>

          <!-- 数据正常显示 -->
          <view v-else>
            <charging-price-chart :priceData="formattedPriceList" :width="750" :height="300" ref="priceChart" />
          </view>
        </view>

        <!-- 明细列表 -->
        <detail-chart v-show="currentTab === 1" :hourlyData="electricData" :isLoading="isLoading" ref="detailChart"
          @tab-change="handleDetailTabChange" />
      </view>
    </view>
  </view>
</template>

<script>
import ChargingPriceChart from './tendency-chart.vue'
import DetailChart from './detail-chart.vue'

export default {
  name: 'ElectricDetail',
  components: {
    ChargingPriceChart,
    DetailChart
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    electricData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentTab: 0,
      tabList: [
        { name: '趋势' },
        { name: '明细' }
      ],
      priceList: [],
      priceRanges: [],
      timeRanges: [
        '00:00-07:00',
        '07:00-11:00',
        '11:00-17:00',
        '17:00-23:00',
        '23:00-24:00'
      ]
    }
  },
  computed: {
    // 判断数据是否正在加载中
    isLoading() {
      return this.electricData && this.electricData.length === 0;
    },
    // 格式化价格列表，确保长度为25（0-24点）
    formattedPriceList() {
      if (this.priceList.length !== 25) {
        return Array(25).fill(0)
      }
      return this.priceList
    }
  },
  watch: {
    // 监听 show prop 的变化，用于调试
    show: {
      handler(newVal) {
        console.log('显示状态变化:', newVal)
      },
      immediate: true
    },
    // 监听电费数据变化，处理数据格式
    electricData: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.processElectricData(newVal)
        }
      },
      immediate: true
    }
  },
  methods: {
    // 关闭弹窗并重置标签页
    closePopup() {
      console.log('关闭弹窗触发')
      this.$emit('close')
      this.currentTab = 0
    },
    // 处理标签页切换事件
    handleTabChange(index) {
      this.currentTab = index
    },
    // 处理明细标签页事件传递
    handleDetailTabChange(index) {
      // 如果有需要，可以处理明细标签页的变化
    },
    // 处理电费数据，转换为组件需要的格式
    processElectricData(data) {
      // 处理priceList（每小时价格列表）
      this.processPriceList(data)

      // 处理priceRanges（保留每小时的价格时段）
      this.processPriceRanges(data)

      // 将数据传递给明细组件
      this.$nextTick(() => {
        const detailChartRef = this.$refs.detailChart
        if (detailChartRef) {
          detailChartRef.updatePriceDetails(data)
        }
      })
    },
    // 处理priceList，转换为25个点（0-24点）的价格数组
    processPriceList(data) {
      const priceList = []

      // 确保数据按时间排序
      const sortedData = [...data].sort((a, b) => {
        const timeA = parseInt(a.feeTimes.split(':')[0])
        const timeB = parseInt(b.feeTimes.split(':')[0])
        return timeA - timeB
      })

      // 填充priceList数组（25个点，0-24点）
      for (let i = 0; i <= 24; i++) {
        const hour = i === 24 ? 0 : i  // 24点等同于下一天的0点
        const hourData = sortedData.find(item => {
          const timeRange = item.feeTimes.split('-')
          const startHour = parseInt(timeRange[0].split(':')[0])
          return startHour === hour
        })

        if (hourData) {
          priceList.push(parseFloat(hourData.totalFee))
        } else {
          // 如果没有对应小时的数据，使用上一小时的价格或默认值
          const prevPrice = i > 0 ? priceList[i - 1] : 0
          priceList.push(prevPrice)
        }
      }

      this.priceList = priceList
    },
    // 处理priceRanges，使用固定的时间区间
    processPriceRanges(data) {
      if (!data || data.length === 0) return

      const priceRanges = []

      // 按时间排序
      const sortedData = [...data].sort((a, b) => {
        const timeA = parseInt(a.feeTimes.split(':')[0])
        const timeB = parseInt(b.feeTimes.split(':')[0])
        return timeA - timeB
      })

      // 使用预定义的时间区间
      this.timeRanges.forEach(timeRange => {
        const [startStr, endStr] = timeRange.split('-')
        const startHour = parseInt(startStr.split(':')[0])
        const endHour = parseInt(endStr.split(':')[0])

        // 找出该时间区间内的所有数据
        const itemsInRange = sortedData.filter(item => {
          const itemHour = parseInt(item.feeTimes.split(':')[0])
          return itemHour >= startHour && (endHour === 24 ? itemHour < 24 : itemHour < endHour)
        })

        if (itemsInRange.length === 0) {
          priceRanges.push({
            timeRange: timeRange,
            price: '0.00'
          })
          return
        }

        // 计算该时间段的平均价格
        const totalFees = itemsInRange.map(item => parseFloat(item.totalFee))
        const sum = totalFees.reduce((total, fee) => total + fee, 0)
        const avgPrice = totalFees.length > 0 ? (sum / totalFees.length).toFixed(2) : '0.00'

        priceRanges.push({
          timeRange: timeRange,
          price: avgPrice
        })
      })

      this.priceRanges = priceRanges
    },
    // 获取当前时间所属的价格区间
    getCurrentTimeRange() {
      const now = new Date()
      const hour = now.getHours()
      const range = this.priceRanges.find(item => {
        const [start, end] = item.timeRange.split('-')
        const startHour = parseInt(start.split(':')[0])
        const endHour = parseInt(end.split(':')[0])
        return hour >= startHour && hour < endHour
      })
      return range ? range.timeRange : ''
    },
    // 获取当前小时的基础电价
    getCurrentPrice() {
      const now = new Date()
      const hour = now.getHours()
      return this.priceList[hour]?.toFixed(2) || '0.00'
    },
    // 公开方法：用于父组件直接更新电费数据
    updateElectricData(data) {
      if (data && Array.isArray(data) && data.length > 0) {
        this.processElectricData(data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 998;
}

.close {
  width: 32rpx;
  height: 32rpx;
  image {
    width: 100%;
    height: 100%;
  }
}

.electric-detail {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  min-height: 60vh;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 999;

  .header {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #F5F5F5;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }
  }

  .tab-wrapper {
    padding: 20rpx 30rpx 0;
  }

  .content {
    padding: 30rpx;

    .loading-state,
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 300rpx;

      .loading-text,
      .empty-text {
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #999;
      }
    }

    .chart-content {
      .chart-wrapper {
        height: 400rpx;
        margin-bottom: 30rpx;
      }

      .current-price-tip {
        background: #F8F8F8;
        padding: 20rpx;
        border-radius: 8rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .time {
          color: #666666;
          font-size: 28rpx;
        }

        .price {
          color: #FF4D4F;
          font-size: 28rpx;
          font-weight: 500;
        }
      }
    }

    .detail-content {
      .price-item {
        padding: 30rpx 0;
        border-bottom: 1rpx solid #F5F5F5;

        &:last-child {
          border-bottom: none;
        }

        .time-range {
          color: #333333;
          font-size: 28rpx;
          margin-bottom: 16rpx;
        }

        .price-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .label {
            color: #666666;
            font-size: 26rpx;
          }

          .value {
            color: #FF4D4F;
            font-size: 28rpx;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>