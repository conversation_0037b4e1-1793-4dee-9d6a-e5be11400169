<template>
  <view class="bottom-button">
    <button class="primary-btn" @tap="handleClick">{{ text }}</button>
  </view>
</template>

<script>
export default {
  name: 'ActionButton',
  props: {
    text: {
      type: String,
      default: '前往充电'
    }
  },
  methods: {
    handleClick() {
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 40rpx 40rpx 50rpx;
  background: #fff;
  font-weight: 400;
  font-size: 32rpx;
  color: #FFFFFF;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);

  .primary-btn {
    background: #0066E9;
    color: #fff;
    border-radius: 10rpx;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;

    &:active {
      opacity: 0.9;
    }
  }
}
</style> 