<template>
  <view class="station-info">
    <view class="contact" @tap="makePhoneCall">
      <text>联系电话：{{ info.stationTel }}</text>
      <image class="phone-icon"
        src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/call_icon.png" mode=""></image>
    </view>

    <view class="station-info-content">
      <view class="distance">{{ distanceText }}</view>
      <view class="address">
        {{ addressText }}
      </view>
      <view class="navigate-btn" @tap="onNavigate">导航</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StationInfo',
  props: {
    info: {
      type: Object,
      default: () => null
    },
    navigationData: {
      type: Object,
      default: () => null
    }
  },
  computed: {
    distanceText() {
      const distanceM = this.info?.distance;
      if (distanceM === null || distanceM === undefined) {
        return '距离未知';
      }
      const distanceNum = parseFloat(distanceM);
      if (isNaN(distanceNum)) {
        return `距离${distanceM}`;
      }
      if (distanceNum >= 1000) {
        // 转换为公里并保留一位小数
        return `距离当前位置${(distanceNum / 1000).toFixed(1)}km`;
      } else {
        // 保持为米并取整数
        return `距离当前位置${Math.round(distanceNum)}m`;
      }
    },
    addressText() {
      return this.info?.address || '地址未知';
    }
  },
  methods: {
    makePhoneCall() {
      let phoneNumber = this.info.stationTel;
				if (phoneNumber.includes('暂无')) {
					uni.makePhoneCall({
						phoneNumber: '0771-5896333' //仅为示例
					});
				} else {
					uni.makePhoneCall({
						phoneNumber: phoneNumber //仅为示例
					});
				}
    },

    onNavigate() {
      if (this.navigationData) {
        this.$emit('navigate', this.navigationData);
      } else {
        console.warn('导航数据不可用');
        uni.showToast({ title: '位置信息不完整', icon: 'none' });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.station-info {
  background: #fff;
  padding: 24rpx 20rpx 0 20rpx;
  box-sizing: border-box;
  background: linear-gradient(180deg, #FFF5E7 0%, #FFFCF8 27%, #FFFEFE 48%, #FFFFFF 100%);

  .contact {
    width: 670rpx;
    border-bottom: 1rpx solid #F3EFE9;
    padding-bottom: 20rpx;
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #333333;
    font-weight: 400;
    .phone-icon {
      width: 32rpx;
      height: 32rpx;
      margin-left: 16rpx;
    }
  }

  .station-info-content {
    position: relative;
    width: 100%;
    padding-top: 24rpx;
    padding-bottom: 40rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 104rpx;
    border-bottom: 1rpx solid #F3EFE9;
  }

  .distance {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
  }

  .address {
    margin-top: 10rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    white-space: normal;
    word-break: break-all;
    max-width: calc(100% - 140rpx);
  }

  .navigate-btn {
    color: #fff;
    font-size: 24rpx;
    width: 120rpx;
    height: 54rpx;
    text-align: center;
    line-height: 54rpx;
    background: linear-gradient(270deg, #4F7AF4 0%, #33AFFF 100%);
    border-radius: 24rpx;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>