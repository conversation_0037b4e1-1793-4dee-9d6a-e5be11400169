<template>
  <view class="header">
    <view class="line-block"></view>
    <view class="station-name">
      <text class="station-name-text">{{ stationName }}</text>
      <text class="status" :class="statusClass">{{ statusText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StationHeader',
  props: {
    name: {
      type: String,
      required: true
    },
    status: {
      type: String,
      required: true
    },
    isFast: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      statusMapData: {
        operating: { text: '营业中', class: 'operating' },
        maintenance: { text: '维护中', class: 'gray' },
        constructing: { text: '建设中', class: 'gray' },
        closed: { text: '关停', class: 'closed' },
        unknown: { text: '未知', class: 'gray' }
      }
    }
  },
  computed: {
    stationName() {
      return this.name;
    },
    statusText() {
      return this.statusMapData[this.status]?.text || '未知'
    },
    statusClass() {
      return this.statusMapData[this.status]?.class || 'unknown'
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  background: #fff;
  padding: 52rpx 18rpx 18rpx 20rpx;
  margin-top: 28rpx;
  display: flex;
  align-items: center;
  border-radius: 8rpx 8rpx 0rpx 0rpx;

  .line-block {
    display: inline-block;
    margin-right: 20rpx;
    width: 6rpx;
    height: 28rpx;
    background: #0066E9;
    border-radius: 34rpx 34rpx 34rpx 34rpx;
  }

  .station-name {
    display: inline-flex;
    align-items: center;
    font-size: 32rpx;
    font-weight: bold;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    .station-name-text {
      display: inline-block;
      max-width: 500rpx;
    }
    .status {
      margin-left: 20rpx;
      font-size: 24rpx;
      height: 34rpx;
      padding: 0 24rpx;
      line-height: 34rpx;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      margin-left: 14rpx;

      &.operating {
        color: #08BA81;
        background: rgba(8, 186, 129, 0.1);
      }
      &.gray {
        color: #999999;
        background: rgba(153, 153, 153, 0.1);
      }
      &.closed {
        color: #F82A3C;
        background: rgba(248, 42, 60, 0.1);
      }
    }
  }
}
</style> 