<template>
  <view class="charger-list">
    <!-- <view class="list-header" v-if="list && list.length > 0">充电枪列表</view> -->
    <view class="charger-item" v-for="(item, index) in list" :key="item.equipmentId" :class="item.typeClass" @click="getChargingFee(item)">
      <text class="type-label">{{ item.type || '未知' }}</text>
      <text class="device-no">{{ item.name || item.equipmentId || '未知设备' }}</text>
      <text class="status-tag" :class="item.statusClass">{{ item.statusText || '未知' }}</text>
    </view>
    <view v-if="!list || list.length === 0" class="no-chargers">
      暂无充电枪信息
    </view>
  </view>
</template>

<script>
export default {
  name: 'ChargerList',
  props: {
    list: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  methods: {
    getChargingFee(charger) {
      // console.log(charger, 'charger');
      
      // // 立即触发父组件事件，传递充电桩信息
      // this.$emit('showElectricDetail', charger);
      
      // 不再在这里调用接口，由父组件负责缓存和数据请求
    }
  }
}
</script>

<style lang="scss" scoped>
.charger-list {
  background: #fff;
  border-radius: 0 0 8rpx 8rpx;
  margin-bottom: 20rpx;
  padding: 0 0 1rpx 0;
  overflow: hidden;

  .list-header {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    padding: 20rpx 24rpx;
    border-bottom: 1rpx solid #E7E7E7;
  }

  .charger-item {
    background: #fff;
    padding: 28rpx 24rpx;
    display: flex;
    min-height: 104rpx;
    align-items: center;
    border-bottom: 1rpx solid #E7E7E7;

    &:last-child {
      border-bottom: none;
    }

    .type-label {
      color: #fff;
      padding: 4rpx 12rpx;
      font-size: 24rpx;
      line-height: 36rpx;
      border-radius: 8rpx;
      margin-right: 22rpx;
      white-space: nowrap;
    }

    // 样式类名应与父组件生成的 class 一致
    &.super .type-label {
      background: #FF9900;
    }

    &.fast .type-label {
      background: #08BA81;
    }

    &.slow .type-label {
      background: #4F90FF;
    }

    &.unknown .type-label {
      background: #999999;
    }

    .device-no {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      margin-right: 16rpx;
      word-break: break-all;
    }

    .status-tag {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      text-align: center;
      line-height: 60rpx;
      border: 1rpx solid transparent;
      font-size: 20rpx;
      white-space: nowrap;

      // 样式类名应与父组件生成的 class 一致
      &.idle {
        background: #ECFFF9;
        color: #08BA81;
        border-color: #08BA81;
      }

      &.occupied {
        background: #FFF4E5;
        color: #FF9900;
        border-color: #FF9900;
      }

      &.fault {
        background: #FEEEEE;
        color: #F82A3C;
        border-color: #F82A3C;
      }

      &.offline {
        background: #F4F4F4;
        color: #999999;
        border-color: #999999;
      }

      &.unknown {
        background: #F4F4F4;
        color: #999999;
        border-color: #999999;
      }
    }
  }

  .no-chargers {
    color: #999;
    font-size: 28rpx;
    text-align: center;
    padding: 40rpx 0;
  }
}
</style>