<template>
  <view class="charging-fee">
    <!-- 折叠状态 - 只显示当前时段价格 -->
    <view v-if="collapsed" class="fee-current" @tap="showPriceDetail">
      <view class="fee-current-left">
        <text class="fee-label">充电费</text>
      </view>
      <view class="fee-current-right">
        <text class="fee-period">当前时段</text>
        <text class="fee-value">{{ currentPeriodText }}</text>
        <image class="fee-icon" src="/pagesD/static/<EMAIL>" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 展开状态 - 显示完整费用信息 -->
    <view v-else class="fee-expanded">
      <view class="fee-item">
        <text class="fee-title">电费</text>
        <text class="fee-value">{{ formattedElectricityFee }}</text>
      </view>
      <view class="fee-item">
        <text class="fee-title">服务费</text>
        <text class="fee-value">{{ formattedServiceFee }}</text>
      </view>
      <view class="fee-item">
        <text class="fee-title">停车费</text>
        <text class="fee-value">{{ formattedParkFee }}</text>
      </view>
      <view class="actions">
        <!-- <view class="fee-detail-button" @tap="showPriceDetail">
          <text>查看电费详情</text>
          <image class="right-icon" src="/pagesD/static/<EMAIL>" mode="aspectFit"></image>
        </view> -->
        <view class="fee-detail-button" @tap="toggleCollapse">
          <text>收起</text>
          <image class="right-icon rotate-up" src="/pagesD/static/<EMAIL>" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ChargingFee',
  props: {
    fees: {
      type: Object,
      required: true,
      default: () => ({
        electricityFee: null,
        serviceFee: null,
        parkFee: null
      })
    }
  },
  data() {
    return {
      collapsed: true // 默认为折叠状态
    }
  },
  computed: {
    // 当前时段电费显示
    currentPeriodText() {
      // 假设默认使用电费作为当前时段的费用
      return this.formatFeeForCurrentPeriod(this.fees.electricityFee);
    },
    // 格式化电费
    formattedElectricityFee() {
      return this.formatFee(this.fees.electricityFee);
    },
    // 格式化服务费
    formattedServiceFee() {
      return this.formatFee(this.fees.serviceFee);
    },
    // 格式化停车费
    formattedParkFee() {
      return this.fees.parkFee || '免费';
    }
  },
  methods: {
    // 折叠/展开切换
    toggleCollapse() {
      this.collapsed = !this.collapsed;
    },

    showPriceDetail() {
      this.$emit('show-price-detail');
    },

    // 格式化当前时段费用显示
    formatFeeForCurrentPeriod(feeString) {
      if (feeString === null || feeString === undefined || feeString === '') {
        return '¥--元/度';
      }

      // 处理"X元/度"或"X元/小时"格式
      if (feeString.includes('元/')) {
        // 将"1.45元/度"转换为"¥1.20元/度"格式
        return `¥ ${feeString}`;
      }

      // 如果是数字，添加默认单位
      const feeNum = parseFloat(feeString);
      if (!isNaN(feeNum)) {
        return `¥ ${feeNum.toFixed(2)}元/度`;
      }

      return feeString;
    },

    // 格式化费用显示的辅助函数
    formatFee(feeString) {
      if (feeString === null || feeString === undefined || feeString === '') {
        return '¥-- / --';
      }

      // 处理"X元/度"或"X元/小时"格式
      if (feeString.includes('元/')) {
        // 将"1.45元/度"转换为"¥1.20元/度"格式
        return `¥ ${feeString}`;
      }

      // 检查是否为数字字符串
      const feeNum = parseFloat(feeString);
      if (!isNaN(feeNum)) {
        return `¥ ${feeNum.toFixed(2)} /度`; // 默认单位为度
      }

      // 若格式不符合预期，返回原始字符串
      return feeString;
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-fee {
  border-radius: 0 0 8rpx 8rpx;
  overflow: hidden;

  .fee-current {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .fee-current-left {
      display: flex;
      flex-direction: column;

      .fee-label {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 8rpx;
      }
    }

    .fee-period {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      margin-right: 14rpx;
    }

    .fee-current-right {
      display: flex;
      align-items: center;

      .fee-value {
        font-weight: 500;
  font-size: 28rpx;
  color: #666666;
      }

      .fee-icon {
        width: 28rpx;
        height: 28rpx;
        margin-left: 8rpx;
      }
    }
  }

  .fee-expanded {
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .fee-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .fee-title {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
      }

      .fee-value {
        font-weight: 500;
        font-size: 28rpx;
        color: #666666;
      }
    }

    .actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 10rpx;
    }

    .fee-detail-button {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 400;
      font-size: 26rpx;

      .right-icon {
        width: 28rpx;
        height: 28rpx;
        margin-left: 8rpx;

        &.rotate-up {
          transform: rotate(90deg);
        }
      }
    }
  }
}
</style>