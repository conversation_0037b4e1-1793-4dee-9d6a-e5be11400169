<template>
  <view class="facilities">
    <view class="facility-item" v-for="(item, index) in displayFacilities" :key="index"
      :class="{ 'disabled': !item.available }">
      <image :src="item.available ? item.activeIcon : item.inactiveIcon" mode="aspectFit" />
      <text>{{ item.name }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FacilityIcons',
  props: {
    facilities: {
      type: Object,
      required: true,
      default: () => ({
          isDine: false,
          isDriverhome: false,
          isGas: false,
          isPark: false,
          isRepair: false,
          isStore: false,
          isWc: false
        })
    },
    isFast: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      baseFacilities: [
        {
          activeIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/chargingStation-light.png',
          inactiveIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/chargingStation-gray.png',
          name: '充电站',
          type: 'charging'
        },
        {
          activeIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/refuel-light.png',
          inactiveIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/refuel-gray.png',
          name: '加油站',
          type: 'isGas'
        },
        { activeIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/parking-light.png', inactiveIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/parking-gray.png', name: '停车场', type: 'isPark' },
        { activeIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/wc-light.png', inactiveIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/wc-gray.png', name: '公共厕所', type: 'isWc' },
        { activeIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/eat-light.png', inactiveIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/eat-gray.png', name: '餐厅', type: 'isDine' },
        { activeIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/store-light.png', inactiveIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/store-gray.png', name: '便利店', type: 'isStore' },
        { activeIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/repair-light.png', inactiveIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/repair-gray.png', name: '维修', type: 'isRepair' },
        { activeIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/driverHome-light.png', inactiveIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/driverHome-gray.png', name: '司机之家', type: 'isDriverhome' }
      ]
    }
  },
  computed: {
    displayFacilities() {
      return this.baseFacilities.map(facility => {
        let available = false;
        let name = facility.name;
        let activeIcon = facility.activeIcon;

        if (facility.type === 'charging') {
          available = true;
          if (this.isFast) {
            name = '超充站'
            activeIcon = 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/charging/chao-charg.png'
          }
        } else {
          available = !!this.facilities[facility.type];
        }
        
        return {
          ...facility,
          name: name,
          activeIcon: activeIcon,
          available: available
        };
      }).sort((a, b) => {
        if (a.available && !b.available) return -1;
        if (!a.available && b.available) return 1;
        return 0;
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.facilities {
  background: #fff;
  padding: 0 20rpx 10rpx 20rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;

  .facility-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx 0 20rpx 0;

    image {
      width: 72rpx;
      height: 72rpx;
    }

    text {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      margin-top: 20rpx;
    }
  }
}
</style> 