<template>
  <view class="charging-spots">
    <view class="spots-header">充电车位</view>
    <view class="spots-status">
      <view class="status-item fast" v-if="isFast">
        <view class="status-item-title">超充</view>
        <view class="item-line">闲<text class="status-item-number">{{ counts.superFreeNum || counts.quitFreeNum ||0 }}</text>/总{{ counts.superNum || 0 }}</view>
        <view class="line-bar"></view>
      </view>
      <view class="status-item normal" v-if="!isFast">
        <view class="status-item-title">快充</view>
        <view class="item-line">闲<text class="status-item-number">{{ counts.quitFreeNum || 0 }}</text>/总{{ counts.quitNum || 0 }}</view>
      </view>
      <view class="status-item slow" v-if="!isFast">
        <view class="status-item-title">慢充</view>
        <view class="item-line">闲<text class="status-item-number">{{ counts.slowFreeNum || 0 }}</text>/总{{ counts.slowNum || 0 }}</view>
      </view>
      <view v-if="!counts || (counts.superNum === 0 && counts.quitNum === 0 && counts.slowNum === 0)" class="no-spots-placeholder">
        <!--  暂无车位信息 或 保留空白 -->
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ChargingSpots',
  props: {
    isFast: {
      type: Boolean,
      default: false
    },
    counts: {
      type: Object,
      required: true,
      default: () => ({
        connectorNum: 0,
        connectorFeeNum: 0,
        quitNum: 0,
        quitFreeNum: 0,
        slowNum: 0,
        slowFreeNum: 0,
        superNum: 0,
        superFreeNum: 0
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-spots {
  background: #fff;
  margin-top: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx 8rpx 0rpx 0rpx;

  .spots-header {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
  }

  .spots-status {
    display: flex;
    gap: 20rpx;
    margin-top: 20rpx;

    .status-item {
      flex: 1;
      padding-left: 50rpx;
      font-weight: 500;
      font-size: 28rpx;
      color: #FFFFFF;
      height: 112rpx;
      display: flex;
      line-height: 112rpx;

      .status-item-title {
        margin-right: 56rpx;
      }

      .item-line {
        line-height: 90rpx;
      }

      .status-item-number {
        font-size: 60rpx;
      }
      .line-bar{
        width: 238rpx;
        height: 16rpx;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.72) 40%, rgba(255, 255, 255, 0) 120%);
        border-radius: 34rpx 34rpx 34rpx 34rpx;
        margin-left: 98rpx;
        margin-top: 48rpx;
      }
      &.fast {
        border-radius: 8rpx 0rpx 0rpx 8rpx;
        background: linear-gradient(180deg, #FF9000 0%, #FCBA7D 100%);
      }

      &.normal{
        background: linear-gradient( 180deg, #4DBB8B 0%, #6CD4AA 100%);
        border-radius: 8rpx 0rpx 0rpx 8rpx;
      }

      &.slow {
        background: linear-gradient( 180deg, #4F90FF 0%, #6CA2FF 100%);
        border-radius: 0rpx 8rpx 8rpx 0rpx;
      }
    }
  }
}
</style> 