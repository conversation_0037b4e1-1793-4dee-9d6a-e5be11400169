<template>
  <view class="charging-detail">
    <!-- 头部信息 -->
    <station-header :name="stationName" :status="formattedStationStatus" :is-fast="isFast" />

    <!-- 服务设施图标区 -->
    <facility-icons :facilities="facilities" :is-fast="isFast" />

    <!-- 站点信息 -->
    <view class="station-info-container">
      <station-info :info="stationInfo" :navigation-data="navigationData" @navigate="handleNavigate" />

      <!-- 充电费用区域 -->
      <charging-fee :fees="fees" @show-price-detail="showPriceDetail" />
    </view>

    <!-- 充电车位状态 -->
    <charging-spots :counts="spotCounts" :is-fast="isFast" />

    <!-- 充电桩列表 -->
    <charger-list :list="formattedConnectorList" @showElectricDetail="showPriceDetail" />

            <!-- 评价入口组件 -->
    <view class="evaluation-section" v-if="stationId">
          <evaluation-entry
            businessType="chargingService"
            :businessId="stationId"
            :evaluationCount="evaluationCount"
            :businessName="stationName"
          />
        </view>
    <!-- 底部按钮 -->
    <action-button text="前往充电" @click="handleStartCharging" />

    <!-- 充电费用弹窗，只在主页面保留一个实例 -->
    <electric-detail 
      :show="showPricePopup" 
      :electricData="electricData" 
      ref="electricDetail"
      @close="hidePriceDetail" 
    />
  </view>
</template>

<script>
import StationHeader from './components/StationHeader.vue'
import FacilityIcons from './components/FacilityIcons.vue'
import StationInfo from './components/StationInfo.vue'
import ChargingFee from './components/ChargingFee.vue'
import ChargingSpots from './components/ChargingSpots.vue'
import ChargerList from './components/ChargerList.vue'
import ActionButton from './components/ActionButton.vue'
import ElectricDetail from './electric-detail.vue'
import EvaluationEntry from '@/components/common/evaluation-entry.vue'
import { getLoginUserInfo, getOpenid, getOpenidForRead } from "@/common/storageUtil.js";


export default {
  components: {
    StationHeader,
    FacilityIcons,
    StationInfo,
    ChargingFee,
    ChargingSpots,
    ChargerList,
    ActionButton,
    ElectricDetail,
    EvaluationEntry
  },
  data() {
    return {
      stationId: null,
      stationInfo: null,
      connectorList: [],
      showPricePopup: false,
      electricData: [], // 存储电费数据
      electricDataCache: {}, // 缓存各充电枪的电费数据
      currentEquipmentId: '', // 当前选中的充电枪ID
      location: {
        address: '南宁市青秀区民族大道146号三祺G层'
      },
      evaluationCount: 0
    }
  },
  computed: {
    // 站点名称
    stationName() {
      return this.stationInfo ? this.stationInfo.stationName : '加载中...'
    },
    // 判断站点是否包含超充桩
    isFast() {
      return this.connectorList.some(connector => connector.type === '超充');
    },
    // 根据状态码格式化站点状态文本和样式类
    formattedStationStatus() {
      if (!this.stationInfo) return 'unknown';
      switch (this.stationInfo.stationStatus) {
        case 50: return 'operating';
        case 6: return 'maintenance';
        case 5: return 'closed';
        case 1: return 'constructing';
        case 0: return 'unknown';
        default: return 'unknown';
      }
    },
    // 格式化站点提供的服务设施信息
    facilities() {
      if (!this.stationInfo) return {};
      return {
        isDine: this.stationInfo.isDine == '是',
        isDriverhome: this.stationInfo.isDriverhome == '是',
        isGas: this.stationInfo.isGas == '是',
        isPark: this.stationInfo.isPark == '是',
        isRepair: this.stationInfo.isRepair == '是',
        isStore: this.stationInfo.isStore == '是',
        isWc: this.stationInfo.isWc == '是'
      };
    },
    // 格式化站点的费用信息
    fees() {
      if (!this.stationInfo) return {};
      return {
        electricityFee: this.stationInfo.electricityFee,
        serviceFee: this.stationInfo.serviceFee,
        parkFee: this.stationInfo.parkFee,
      }
    },
    // 计算并格式化不同类型充电车位的数量
    spotCounts() {
      if (!this.stationInfo) return {};
      return {
        connectorNum: this.stationInfo.connectorNum,
        connectorFeeNum: this.stationInfo.connectorFeeNum,
        quitNum: this.stationInfo.quitNum,
        quitFreeNum: this.stationInfo.quitFreeNum,
        slowNum: this.stationInfo.slowNum,
        slowFreeNum: this.stationInfo.slowFreeNum,
        superNum: this.connectorList.filter(c => c.type === '超充').length,
        superFreeNum: this.connectorList.filter(c => c.type === '超充' && c.status === 1).length,
      }
    },
    // 格式化充电枪列表，添加状态和类型文本及样式类
    formattedConnectorList() {
      return this.connectorList.map(connector => {
        let statusText = '未知';
        let statusClass = 'unknown';
        console.log(connector.status, 'connector.status');
        switch (connector.status) {
          case '0': statusText = '离线'; statusClass = 'offline'; break;
          case '1': statusText = '空闲'; statusClass = 'idle'; break;
          case '2': statusText = '占用'; statusClass = 'occupied'; break;
          case '3': statusText = '占用'; statusClass = 'occupied'; break;
          case '4': statusText = '占用'; statusClass = 'occupied'; break;
          case '255': statusText = '故障'; statusClass = 'fault'; break;
          default: statusText = '未知'; statusClass = 'unknown';
        }

        let typeClass = 'unknown';
        switch (connector.type) {
            case '超充': typeClass = 'super'; break;
            case '快充': typeClass = 'fast'; break;
            case '慢充': typeClass = 'slow'; break;
            default: typeClass = 'unknown';
        }

        return {
          ...connector,
          statusText: statusText,
          statusClass: statusClass,
          typeClass: typeClass
        };
      });
    },
    // 准备用于地图导航的数据
    navigationData() {
      if (!this.stationInfo) return null;
      return {
        latitude: parseFloat(this.stationInfo.stationLat),
        longitude: parseFloat(this.stationInfo.stationLng),
        name: this.stationInfo.stationName,
        address: this.stationInfo.address
      }
    }
  },
  onLoad(options) {
    if (options && options.stationId) {
      this.stationId = options.stationId;
      this.currentLocation = {
        latitude: options.lat,
        longitude: options.lng
      }
      this.fetchStationDetails();
    } else {
      console.error('stationId is missing');
      uni.showToast({
        title: '缺少充电站ID',
        icon: 'none'
      });
    }
  },
  methods: {
    // 异步获取充电站详细信息
    async fetchStationDetails() {
      if (!this.stationId) return;
      uni.showLoading({ title: '加载中...' });
      try {

        const res = await this.$request.post(this.$interfaces.getChargingStationDetail, {
          data: {
            stationId: this.stationId,
            lat: this.currentLocation.latitude,
            lng: this.currentLocation.longitude
          }
        });

        if (res && res.code == 200 && res.data) {
          this.stationInfo = res.data
          this.connectorList = res.data.connectorInfos || [];
          // 获取评价数量
          this.getEvaluationCount();
        } else {
          uni.showToast({ title: res.msg || '加载失败', icon: 'none' });
          this.stationInfo = null;
          this.connectorList = [];
        }
      } catch (error) {
        uni.showToast({ title: '网络错误', icon: 'none' });
        this.stationInfo = null;
        this.connectorList = [];
      } finally {
        uni.hideLoading();
      }
    },
    // 处理导航按钮点击事件，调用地图应用进行导航
    handleNavigate() {
      const navData = this.navigationData;
      if (!navData || navData.latitude == null || navData.longitude == null) {
        uni.showToast({ title: '位置信息不完整', icon: 'none' });
        return;
      }

      uni.openLocation({
        latitude: navData.latitude,
        longitude: navData.longitude,
        name: navData.name,
        address: navData.address,
        success: () => {
          console.log('导航打开成功')
        },
        fail: (err) => {
          console.error('导航打开失败', err)
          uni.showToast({
            title: '导航打开失败',
            icon: 'none'
          })
        }
      })
    },
    // 获取充电费用数据
    async fetchElectricPriceData() {
      if (!this.stationId) return;
      
      uni.showLoading({ title: '加载中...' });
      try {
        const res = await this.$request.post(this.$interfaces.getElectricPriceData, {
          data: {
            stationId: this.stationId,
            equipmentId: this.currentEquipmentId || ''
          }
        });

        if (res && res.code == 200 && res.data) {
          this.electricData = res.data.electricPrices || [];
          console.log('获取电费数据成功', this.electricData);
          return true;
        } else {
          uni.showToast({ title: res.msg || '加载电费数据失败', icon: 'none' });
          this.electricData = [];
          return false;
        }
      } catch (error) {
        console.error('获取电费数据错误', error);
        uni.showToast({ title: '网络错误', icon: 'none' });
        this.electricData = [];
        return false;
      } finally {
        uni.hideLoading();
      }
    },
    
    // 显示充电费用详情弹窗
    async showPriceDetail(data) {
      console.log('显示价格详情, 传入 data:', data);
      
      let equipmentId = null;
      // 优先从传入的 data 中获取 equipmentId
      if (data && data.equipmentId) {
        equipmentId = data.equipmentId;
      } 
      // 如果 data 中没有，尝试从 formattedConnectorList 的第一个元素获取
      else if (this.formattedConnectorList && this.formattedConnectorList.length > 0 && this.formattedConnectorList[0].equipmentId) {
        console.log('Data missing equipmentId, using equipmentId from formattedConnectorList[0]');
        equipmentId = this.formattedConnectorList[0].equipmentId;
      } 

      // 如果最终没有有效的 equipmentId，则报错并退出
      if (!equipmentId) {
        console.error('无法确定有效的 equipmentId 来显示价格详情。');
        uni.showToast({ title: '无法获取充电枪ID', icon: 'none' });
        return;
      }

      // 记录当前设备ID
      this.currentEquipmentId = equipmentId;
      
      // 先显示弹窗和加载状态
      this.showPricePopup = true;
      
      // 检查缓存中是否有数据
      if (this.electricDataCache[equipmentId] && this.electricDataCache[equipmentId].length > 0) {
        // 使用缓存数据
        this.electricData = this.electricDataCache[equipmentId];
        console.log('使用缓存的电费数据', this.electricData);
        
        // 异步更新缓存数据
        this.updateElectricDataCache(equipmentId);
      } else {
        // 缓存中没有数据，直接使用传入的数据（如果有）
        if (data && Array.isArray(data) && data.length > 0) {
          this.electricData = data;
          // 更新缓存
          this.electricDataCache[equipmentId] = [...data];
        } else {
          // 没有数据，显示加载中状态
          this.electricData = [];
          // 异步获取数据
          this.updateElectricDataCache(equipmentId);
        }
      }
    },
    
    // 异步更新电费数据缓存
    async updateElectricDataCache(equipmentId) {
      if (!equipmentId) return;
      
      try {
        const res = await this.$request.post(this.$interfaces.electricDetail, {
          data: { equipmentId }
        });
        
        if (res && res.code === 200 && res.data) {
          // 更新缓存
          this.electricDataCache[equipmentId] = res.data;
          
          // 如果当前查看的就是这个设备，更新显示数据
          if (this.currentEquipmentId === equipmentId && this.showPricePopup) {
            this.electricData = res.data;
            console.log('异步更新电费数据成功', this.electricData);
          }
        }
      } catch (error) {
        console.error('更新电费数据缓存失败', error);
      }
    },
    
    // 隐藏充电费用详情弹窗
    hidePriceDetail() {
      console.log('隐藏价格详情');
      this.showPricePopup = false;
    },
    // 前往充电
    handleStartCharging() {
      uni.navigateToMiniProgram({
        appId: 'wx33901e6f35bd973c',
        path: '',
        envVersion: 'release', //正式版
        extraData: '',
        success(res) {
          console.log('跳转成功', res)
        }
      })
      },
    onEvaluationTap() {
      // 处理评价入口点击事件
      console.log('充电桩评价被点击:', {
        businessType: 'chargingService',
        businessId: this.stationId,
        evaluationCount: this.evaluationCount
      });
      // 组件会自动处理跳转，这里可以添加埋点统计等额外逻辑
    },
    async getEvaluationCount() {
      // 获取充电桩评价数量
      if (!this.stationId) return;  
      try {
        const res = await this.$request.post(this.$interfaces.getChargingStationEvaluationList, {
          data: {
            facilityId: this.stationId,
            openid: this.getUserOpenid()
          } 
        });
        if (res && res.code == 200 && res.data) {
          this.evaluationCount = res.data.total;
        } else {
          this.evaluationCount = 0;
        } 
      } catch (error) {
        console.error('获取评价数量失败', error);
        this.evaluationCount = 0;
      }
    },
    // 获取用户openid
    getUserOpenid() {
      // 尝试多种方式获取openid
      let openid = getOpenid();
      if (!openid) {
        openid = getOpenidForRead();
      }

      // 如果还是没有，尝试从用户信息中获取
      if (!openid) {
        const userInfo = getLoginUserInfo();
        openid = userInfo?.openid || '';
      }

      return openid;
    },
  }
}
</script>

<style lang="scss" scoped>
.charging-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0 20rpx;
  padding-bottom: 143rpx;

  .station-info-container {
    background: #fff;
    box-sizing: border-box;
    background: linear-gradient(180deg, #FFF5E7 0%, #FFFCF8 27%, #FFFEFE 48%, #FFFFFF 100%);
  }

  .evaluation-section {
    background: #fff;
    margin: 20rpx 0;
    padding: 0 20rpx;
  }
}
</style>
