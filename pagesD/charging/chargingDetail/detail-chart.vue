<template>
  <view class="detail-chart">
    <!-- 加载中状态 -->
    <view class="loading-state" v-if="isLoading">
      <u-loading color="#0066E9" size="32"></u-loading>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 无数据状态 -->
    <view class="empty-state" v-else-if="!priceDetails || priceDetails.length === 0">
      <text class="empty-text">暂无价格数据</text>
    </view>
    
    <!-- 数据正常显示 -->
    <view v-else>
      <view class="detail-content">
        <view class="table-header">
          <text class="col time-col">收费时段</text>
          <text class="col">充电费</text>
          <text class="col">电费</text>
          <text class="col">服务费</text>
        </view>
        
        <view class="table-body">
          <view 
            class="table-row" 
            v-for="(item, index) in priceDetails" 
            :key="index"
            :class="{'current-period': item.isCurrent}"
          >
            <text class="col time-col">
              {{ item.timeRange }}
              <text v-if="item.isCurrent" class="current-tag">(当前时段)</text>
            </text>
            <text class="col">{{ item.totalPrice }}</text>
            <text class="col">{{ item.electricityPrice }}</text>
            <text class="col">{{ item.servicePrice }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DetailChart',
  props: {
    hourlyData: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      priceDetails: [],
      timeRanges: [
        '00:00-07:00',
        '07:00-11:00',
        '11:00-17:00',
        '17:00-23:00',
        '23:00-24:00'
      ],
      timer: null
    }
  },
  watch: {
    hourlyData: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.updatePriceDetails(newVal)
        }
      },
      immediate: true
    }
  },
  // 组件创建时初始化当前时段并设置定时器
  created() {
    // 组件创建时立即初始化当前时段
    this.initCurrentPeriod()
    
    // 每分钟更新一次当前时段
    this.timer = setInterval(() => {
      this.initCurrentPeriod()
    }, 60000)
  },
  methods: {
    // 接收并处理电费数据
    updatePriceDetails(data) {
      if (!data || data.length === 0) return
      
      // 排序确保按时间顺序处理
      const sortedData = [...data].sort((a, b) => {
        const timeA = parseInt(a.feeTimes.split(':')[0])
        const timeB = parseInt(b.feeTimes.split(':')[0])
        return timeA - timeB
      })
      
      // 使用预定义的时间区间
      this.priceDetails = this.timeRanges.map(timeRange => {
        const [startStr, endStr] = timeRange.split('-')
        const startHour = parseInt(startStr.split(':')[0])
        const endHour = parseInt(endStr.split(':')[0])
        
        // 找出该时间区间内的所有数据
        const itemsInRange = sortedData.filter(item => {
          const itemHour = parseInt(item.feeTimes.split(':')[0])
          return itemHour >= startHour && (endHour === 24 ? itemHour < 24 : itemHour < endHour)
        })
        
        if (itemsInRange.length === 0) {
          return {
            timeRange,
            totalPrice: '0.00',
            electricityPrice: '0.00',
            servicePrice: '0.00',
            isCurrent: false
          }
        }
        
        // 计算该时间段的平均价格
        const totalFee = this.calculateAverage(itemsInRange.map(item => parseFloat(item.totalFee)))
        const electricityFee = this.calculateAverage(itemsInRange.map(item => parseFloat(item.electricityFee)))
        const serviceFee = this.calculateAverage(itemsInRange.map(item => parseFloat(item.serviceFee)))
        
        return {
          timeRange,
          totalPrice: totalFee.toFixed(2),
          electricityPrice: electricityFee.toFixed(2),
          servicePrice: serviceFee.toFixed(2),
          isCurrent: false // 将在initCurrentPeriod中更新
        }
      })
      
      // 更新当前时段标记
      this.initCurrentPeriod()
    },
    
    // 计算平均值
    calculateAverage(values) {
      if (!values || values.length === 0) return 0
      const sum = values.reduce((total, value) => total + value, 0)
      return sum / values.length
    },
    
    // 初始化或更新价格详情中的当前时段标记
    initCurrentPeriod() {
      const now = new Date()
      const currentHour = now.getHours()
      const currentMinute = now.getMinutes()
      const currentTime = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`
      
      // 更新每个时段的 isCurrent 状态
      this.priceDetails = this.priceDetails.map(item => {
        const [start, end] = item.timeRange.split('-')
        const startTime = this.normalizeTimeFormat(start)
        const endTime = this.normalizeTimeFormat(end)
        
        // 判断当前时间是否在该时段内
        const isCurrent = this.isTimeInRange(currentTime, startTime, endTime)
        return { ...item, isCurrent }
      })
      
      // 触发父组件的事件，告知当前时段变化
      this.$emit('tab-change')
    },
    
    // 规范化时间字符串格式（移除空格）
    normalizeTimeFormat(time) {
      return time.trim().replace(/\s+/g, '')
    },
    
    // 判断当前时间是否在给定的时间段内（支持跨天）
    isTimeInRange(current, start, end) {
      const currentMinutes = this.convertToMinutes(current)
      const startMinutes = this.convertToMinutes(start)
      let endMinutes = this.convertToMinutes(end)
      
      // 处理跨天的特殊情况
      if (endMinutes === 0) {
        endMinutes = 24 * 60 // 将 "24:00" 转换为分钟数
      }
      
      if (startMinutes <= endMinutes) {
        // 普通情况：时间段在同一天内
        return currentMinutes >= startMinutes && currentMinutes < endMinutes
      } else {
        // 特殊情况：时间段跨天
        return currentMinutes >= startMinutes || currentMinutes < endMinutes
      }
    },
    
    // 将 HH:MM 格式的时间转换为当天的分钟数
    convertToMinutes(time) {
      const [hours, minutes] = time.split(':').map(Number)
      return hours * 60 + minutes
    }
  },
  // 组件销毁前清除定时器
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-chart {
  width: 100%;
  background: #fff;
  border-radius: 8rpx;
  
  .loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300rpx;
    
    .loading-text, .empty-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999;
    }
  }
  
  .detail-content {
    width: 100%;
    
    .table-header {
      display: flex;
      padding: 16rpx 0;
      background: #F7F7F7;
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      .col {
        flex: 1;
        text-align: center;
        
        &.time-col {
          flex: 1.5;
        }
      }
    }
    
    .table-body {
      .table-row {
        display: flex;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #f5f5f5;
        font-weight: 400;
        font-size: 28rpx;
        color: #3D3D3D;
        // background: #E7E7E7;
        // &:nth-child(odd) {
        //   background: #f8f8f8;
        // }
        
        &.current-period {
          background:  #EAF6FF !important;
          // border: 2rpx solid #4080FF;
          border-radius: 8rpx;
          // margin: 0 20rpx;
          
          .time-col {
            position: relative;
          }
        }
      }
      
      .col {
        flex: 1;
        text-align: center;
        
        &.time-col {
          flex: 1.5;
        }
      }
      
      .current-tag {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-left: 8rpx;
      }
    }
  }
}
</style>
