<template>
	<view class="high-service">
		<view v-if="text" class="tips-text">
			{{text}}
		</view>
		<seriveItem @star="star" v-for="(item,index) in starList" :item="item" :index="index + 1" :key="index">
		</seriveItem>
	</view>
</template>

<script>
	import seriveItem from './serivceItem.vue'
	import {
		getLoginUserInfo,
	} from '@/common/storageUtil.js'
	export default {
		components: {
			seriveItem
		},
		data() {
			return {
				showStarFlag: false,
				text: '',
				longitude: '',
				latitude: '',
				starList: [],
			}
		},
		created() {
			this.getCollectList()
		},
		methods: {
			// getLocal() {
			// 	uni.getLocation({
			// 		type: 'wgs84',
			// 		success: (res) => {
			// 			console.log('当前位置的经度：' + res.longitude);
			// 			console.log('当前位置的纬度：' + res.latitude);
			// 			this.longitude = res.longitude
			// 			this.latitude = res.latitude
			// 			this.getCollectList()
			// 		},
			// 		fail: (fail) => {
			// 			this.text = '获取不到当前位置信息，无法加载服务区列表'
			// 		}
			// 	});
			// },
			getCollectList() {
				this.text = '加载中...'
				//收藏服务区
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					collectType: 1
				}
				this.$request
					.post(this.$interfaces.getCollectList, {
						data: params
					})
					.then((res) => {
						console.log('收藏查询=====>>>>>', res)
						this.text = ''
						if (res.code == 200) {
							let starList = res.data
							this.starList = starList
							if (starList.length == 0) {
								this.text = '暂无数据'
								return
							}
							this.setListFlag(res.data)
							// if (starList.length > 0) {
							// 	this.netList.forEach((item, index) => {
							// 		starList.forEach((item2) => {
							// 			if (item.id == item2.id) {
							// 				this.$set(this.netList[index], 'starFlag', true)
							// 			}
							// 		})
							// 	})
							// }
						} else {
							this.text = '加载失败'
						}
					})
					.catch((error) => {
						this.text = '加载失败'
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			//设置服务区条目的显示状态
			setListFlag(list) {

				// let newList = list.filter(item => {
				// 	return item.areaInfo
				// })
				// console.log('newList', newList)

				list.forEach((item, index) => {
					// console.log('item', item.areaInfo)

					// if (item.areaInfo.accessibleBathroom > 0 || item.areaInfo.thirdToilet > 0 || item.areaInfo
					// 	.easyToilet > 0) {
					// 	this.$set(this.starList[index].areaInfo, 'toiletFlag', true)
					// 	console.log('111', this.starList[index].areaInfo)

					// }
					if (item.areaInfo.accessibleBathroom > 0 || item.areaInfo.thirdToilet > 0 || item.areaInfo
						.easyToilet > 0) {
						this.$set(item.areaInfo, 'toiletFlag', true)
					}
					if (item.areaInfo.parkingArea > 0) {
						this.$set(item.areaInfo, 'parkFlag', true)
					}
					if (item.areaInfo.tankers > 0) {
						this.$set(item.areaInfo, 'tankersFlag', true)
					}
					if (item.areaInfo.chargingPile > 0) {
						this.$set(item.areaInfo, 'chargingFlag', true)
					}
					if (item.areaInfo.storeArea > 0) {
						this.$set(item.areaInfo, 'storeFlag', true)
					}
					if (item.areaInfo.restaurantCount > 0) {
						this.$set(item.areaInfo, 'restaurantFlag', true)
					}
				})
				this.starList = list
				console.log('this.starList ', this.starList)
			},
			star(type, item) {
				if (this.showStarFlag) return
				this.showStarFlag = true
				//收藏服务区
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					collectTitle: item.name,
					// id: item.id,
					collectType: 1
				}
				let url = ''
				if (type == 'star') {
					url = this.$interfaces.addCollect
				} else {
					url = this.$interfaces.delCollect
					params.id = item.id
				}
				this.$request
					.post(url, {
						data: params
					})
					.then((res) => {
						this.showStarFlag = false
						console.log('收藏结果=====>>>>>', res)
						if (res.code == 200) {
							uni.showToast({
								icon: 'none',
								title: type == 'star' ? '收藏成功，可在我的收藏中查看' : '已取消收藏',
								// title: '已取消收藏',
								duration: 2000
							})
							// this.setStarStatus(type, item)
							this.getCollectList()
						}
					})
					.catch((error) => {
						this.showStarFlag = false
						uni.showToast({
							icon: 'none',
							title: error.msg,
							// title: '已取消收藏',
							duration: 2000
						})
					})
			},
		}

	}
</script>

<style lang="scss">
	.high-service {
		font-family: PingFang SC, PingFang SC;
		// padding: 20rpx 0;
		// margin: 20rpx;
		margin-top: 102rpx;

		/deep/.service-item {
			margin: 20rpx;
			padding: 20rpx;
			background: #ffffff;
			border: 0;
			border-radius: 12rpx;
		}

		.tips-text {
			margin-bottom: 20rpx;
			height: 250rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #999999;
		}
	}
</style>