<template>
	<view class="star">
		<u-tabs class="tab" :list="highList" :is-scroll="false" inactive-color='#666666' :current="current"
			activeColor='#0066E9' @change="change">
		</u-tabs>
		<highService v-if="current == 0"></highService>
		<highPlay v-if="current == 1"></highPlay>
	</view>
</template>

<script>
	import highService from './highService.vue'
	import highPlay from './highPlay.vue'
	import {
		getLoginUserInfo,
	} from '@/common/storageUtil.js'
	export default {
		components: {
			// seriveItem
			highService,
			highPlay
		},
		data() {
			return {
				current: 0,
				highList: [{
						name: '服务区'
					},
					{
						name: '嗨玩八桂'
					},
				],
			}
		},
		onLoad() {
			// this.getLocal()
		},
		methods: {
			change(index) {
				this.current = index;
			},
			getLocal() {
				uni.getLocation({
					type: 'wgs84',
					success: (res) => {
						console.log('当前位置的经度：' + res.longitude);
						console.log('当前位置的纬度：' + res.latitude);
						this.longitude = res.longitude
						this.latitude = res.latitude
						this.getNetList()
					},
					fail: (fail) => {
						this.text = '获取不到当前位置信息，无法加载服务区列表'
					}
				});
			},
			getNetList() {
				if (!this.longitude || !this.latitude) {
					this.text = '获取不到当前位置信息，无法加载收藏列表'
					return
				}
				this.text = '加载列表中...' //
				// console.log('转换前====>>>>', this.longitude, this.latitude)
				// let changeObj = this.qqMapTransBMap(this.longitude, this.latitude)
				// console.log('转换后====>>>>', changeObj.longitude, changeObj.latitude)
				let params = {
					name: '',
					longitude: this.longitude,
					latitude: this.latitude,
					pageNo: 1,
					pageSize: 999
				}
				this.$request
					.post(this.$interfaces.serviceAreaList, {
						data: params
					})
					.then((res) => {
						console.log('服务区数据=====>>>>>', res)
						if (res.code == 200) {
							if (res.data.length > 0) {
								this.netList = res.data
								// this.calculateDistance(res.data)
								//设置条目状态
								this.setListFlag(res.data)
								//设置收藏状态
								this.getCollectList()
								this.text = '' //重置错误信息
								if (this.netList.length == 1) {
									//计算少量数据时滑动高度
									this.height = '280rpx'
								} else if (this.netList.length == 2) {
									this.height = '560rpx'
								} else {
									this.height = '840rpx'
								}
							} else {
								this.netList = res.data || []
								this.text = '暂无数据'
							}
						} else {
							this.text = '抱歉，加载列表失败，请稍后重试'
						}
					})
					.catch((error) => {
						this.text = '抱歉，加载列表失败，请稍后重试'
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			//设置服务区条目的显示状态
			setListFlag(list) {
				list.forEach((item, index) => {
					if (item.accessibleBathroom > 0 || item.thirdToilet > 0 || item.easyToilet > 0) {
						this.$set(this.netList[index], 'toiletFlag', true)
					}
					if (item.parkingArea > 0) {
						this.$set(this.netList[index], 'parkFlag', true)
					}
					if (item.tankers > 0) {
						this.$set(this.netList[index], 'tankersFlag', true)
					}
					if (item.chargingPile > 0) {
						this.$set(this.netList[index], 'chargingFlag', true)
					}
					if (item.storeArea > 0) {
						this.$set(this.netList[index], 'storeFlag', true)
					}
					if (item.restaurantCount > 0) {
						this.$set(this.netList[index], 'restaurantFlag', true)
					}
				})
				console.log('this.netcc===>>>', list, this.netList)
			},
			getCollectList() {
				//收藏服务区
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					collectType: 1
				}
				this.$request
					.post(this.$interfaces.getCollectList, {
						data: params
					})
					.then((res) => {
						console.log('收藏查询=====>>>>>', res)
						if (res.code == 200) {
							let starList = res.data
							if (starList.length > 0) {
								this.netList.forEach((item, index) => {
									starList.forEach((item2) => {
										if (item.id == item2.id) {
											this.$set(this.netList[index], 'starFlag', true)
										}
									})
								})
							}
						}
					})
					.catch((error) => {})
			},
			star(type, item) {
				if (this.showStarFlag) return
				this.showStarFlag = true
				//收藏服务区
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					collectTitle: item.name,
					id: item.id,
					collectType: 1
				}
				let url = ''
				if (type == 'star') {
					url = this.$interfaces.addCollect
				} else {
					url = this.$interfaces.delCollect
				}
				this.$request
					.post(url, {
						data: params
					})
					.then((res) => {
						this.showStarFlag = false
						console.log('收藏结果=====>>>>>', res)
						if (res.code == 200) {
							uni.showToast({
								icon: 'none',
								title: type == 'star' ? '收藏成功，可在我的收藏中查看' : '已取消收藏',
								// title: '已取消收藏',
								duration: 2000
							})
							this.setStarStatus(type, item)
						}
					})
					.catch((error) => {
						this.showStarFlag = false
					})
			},
			setStarStatus(type, listItem) {
				this.netList.forEach((item, index) => {
					if (item.id == listItem.id) {
						this.$set(this.netList[index], 'starFlag', type == 'star' ? true : false)
						// this.$set(this.netList[index], 'starFlag', false)
					}
				})
			},
		}
	}
</script>
<style>
	page {
		height: calc(100vh - 106rpx);
	}
</style>
<style lang="scss" scoped>
	.tab {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		z-index: 9999;
	}
</style>