<template>
	<view class="high-play">
		<!-- <highSearch :placeholder="'请输入景点名称'" @click="search"></highSearch> -->
		<view v-if="text" class="tips-text">
			{{text}}
		</view>
		<scroll-view class="item-wrapper" :style="{height:height}" scroll-y="true" :scroll-top="scrollTop"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<playItem v-for="(item,index) in playList" :item="item" :key="index" @star="star">
			</playItem>
			<load-more :loadStatus="noticeLoadStatus" />
		</scroll-view>
	</view>
</template>
<script>
	// import highSearch from '../component/highSearch'
	import playItem from './playItem.vue'
	import loadMore from '@/pages/home/<USER>/load-more/index.vue';
	import {
		getLoginUserInfo,
	} from '@/common/storageUtil.js'
	export default {
		components: {
			// highSearch,
			playItem,
			loadMore
		},
		data() {
			return {
				showStarFlag: false,
				loadFlag: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 1,
				old: {
					scrollTop: 0
				},
				text: '',
				longitude: '',
				latitude: '',
				jqList: [{
					name: '德天跨国瀑布',
					address: '广西壮族自治区崇左市大新县硕龙镇德天跨国大瀑布风景区',
					level: '国家5A级景区',
					phone: '0771-3690199',
					icon: '1'
				}],
				height: 'calc(100vh - 106rpx)',
				// showDistance: true
				playList: [],
				pageNum: 1,
				pageSize: 20,
			};
		},
		created() {
			this.getLocal()
		},
		methods: {
			upper: function(e) {

			},
			scrolltolower: function(e) {
				console.log('this.noticeLoadStatus ', this.noticeLoadStatus)
				if (this.noticeLoadStatus == 3 || this.loadFlag) return;
				let self = this;
				setTimeout(function() {
					if (self.noticeLoadStatus != 2 && self.noticeLoadStatus != 0) {
						self.pageNum = self.pageNum + 1;
					}
					self.getPlayList();
				}, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
			// search(name) {
			// 	console.log('name==', name)
			// 	this.pageNum = 1
			// 	this.getPlayList(name)
			// },
			getLocal() {
				uni.getLocation({
					type: 'wgs84',
					success: (res) => {
						console.log('当前位置的经度：' + res.longitude);
						console.log('当前位置的纬度：' + res.latitude);
						this.longitude = res.longitude
						this.latitude = res.latitude
						this.getPlayList()
					},
					fail: (fail) => {
						this.text = '获取不到当前位置信息，无法加载服务区列表'
					}
				});
			},
			getPlayList(keyword) {
				if (!this.longitude || !this.latitude) {
					this.text = '获取不到当前位置信息，无法加载服务区列表'
					return
				}
				this.noticeLoadStatus = 1;
				this.loadFlag = true
				// this.text = '加载列表中...' //
				// console.log('转换前====>>>>', this.longitude, this.latitude)
				// let changeObj = this.qqMapTransBMap(this.longitude, this.latitude)
				// console.log('转换后====>>>>', changeObj.longitude, changeObj.latitude)
				let params = {
					keyword: keyword || '',
					longitude: this.longitude,
					latitude: this.latitude,
					collectFlag: 1,
					coordinate: 2,
					pageNum: this.pageNum,
					pageSize: 50,
					userId: getLoginUserInfo().userIdStr,
				}
				this.$request
					.post(this.$interfaces.getAttractionList, {
						data: params
					})
					.then((res) => {
						this.loadFlag = false
						console.log('景区数据=====>>>>>', res.data.data)
						if (res.code == 200) {
							// this.playList = res.data.data
							if (res.data.data.length > 0) {
								this.noticeLoadStatus = 4
								if (this.pageNum == 1) {
									this.playList = res.data.data
								} else {
									this.playList = this.playList.concat(res.data.data)
								}
								this.text = '' //重置错误信息
								// if (this.playList.length == 1) {
								// 	//计算少量数据时滑动高度
								// 	this.height = '280rpx'
								// } else if (this.playList.length == 2) {
								// 	this.height = '560rpx'
								// } else {
								// 	this.height = '840rpx'
								// }
							} else {
								// this.playList = res.data.data || []
								if (this.pageNum == 1) {
									this.playList = res.data.data
									this.noticeLoadStatus = 0;
									return
								}
								this.noticeLoadStatus = 3;
								// }
							}
						} else {
							if (this.pageNum != 1) {
								this.noticeLoadStatus = 2;
							}
							// this.text = '抱歉，加载列表失败，请稍后重试'

						}
					})
					.catch((error) => {
						this.loadFlag = false
						this.noticeLoadStatus = 2;
						// this.text = '抱歉，加载列表失败，请稍后重试'
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			star(item) {
				console.log('attractionNo', item)
				let param = {
					userId: getLoginUserInfo().userIdStr,
					attractionNo: item.attractionNo,
					collectFlag: 0
				}
				// if (type == 'star') {
				// 	param.collectFlag = 1
				// } else {
				// 	param.collectFlag = 0
				// }
				this.$request
					.post(this.$interfaces.attractionModify, {
						data: param
					}).then(res => {
						console.log('景区list=======>>>', res)
						if (res.code == 200) {
							uni.showToast({
								icon: 'none',
								title: '已取消收藏',
								// title: '已取消收藏',
								duration: 2000
							})
							this.pageNum = 1
							this.getPlayList()
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								// title: '已取消收藏',
								duration: 2000
							})
						}
					}).catch(err => {
						uni.showToast({
							icon: 'none',
							title: err.msg,
							// title: '已取消收藏',
							duration: 2000
						})
					})
			}
			// getPlayList(keyword) {
			// 	if (!this.longitude || !this.latitude) {
			// 		this.text = '获取不到当前位置信息，无法加载服务区列表'
			// 		return
			// 	}
			// 	this.text = '加载列表中...' //
			// 	let param = {
			// 		keyword: keyword || '',
			// 		longitude: this.longitude,
			// 		latitude: this.latitude,
			// 		collectFlag: 0,
			// 		coordinate: 2,
			// 		pageNum: 1,
			// 		pageSize: 20,
			// 	}
			// 	this.$request
			// 		.post(this.$interfaces.getAttractionList, {
			// 			data: param
			// 		}).then(res => {
			// 			console.log('景区list=======>>>', res)
			// 			if (res.code == 200) {
			// 				if (res.data.data.length > 0) {
			// 					this.playList = res.data.data
			// 					this.text = '' //重置错误信息
			// 				} else {
			// 					this.playList = res.data.data || []
			// 					this.text = '暂无数据'
			// 				}
			// 			} else {
			// 				this.text = '抱歉，加载列表失败，请稍后重试'
			// 			}
			// 		}).catch(err => {
			// 			this.text = '抱歉，加载列表失败，请稍后重试'
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error.msg,
			// 				showCancel: false
			// 			})
			// 		})
			// }
		}
	};
</script>

<style lang="scss" scoped>
	.high-play {
		font-family: PingFang SC, PingFang SC;
		margin-top: 82rpx;
		// .right-item {
		// 	display: flex;
		// 	align-items: center;
		// 	font-weight: 400;
		// 	font-size: 26rpx;
		// 	line-height: 36rpx;
		// 	text-align: left;
		// 	font-style: normal;

		// 	.right-icon {
		// 		width: 32rpx;
		// 		height: 32rpx;
		// 		margin: 0 20rpx 0 16rpx;
		// 	}
		// }

		.item-wrapper {
			overflow: hidden;
			height: 840rpx;
			margin-bottom: 20rpx;
		}

		.tips-text {
			margin-bottom: 20rpx;
			height: 250rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #999999;
		}
	}
</style>