<template>
	<view class="service-item">
		<image class="big-img" :src="item.coverPic" mode="">
		</image>
		<view class="content-wrapper">
			<view class="middle-wrapper">
				<view class="title-wrapper">
					<view class="title">
						{{item.titleName}}
					</view>
					<!-- 					<image v-if="item.collectFlag == 0" @click="star('star',item)" class="star-icon"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/star_icon.png"
						mode=""></image> -->
					<image v-if="item.collectFlag == 1" @click="star(item)" class="star-icon"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/star_icon_highlight.png"
						mode="">
					</image>
				</view>
				<view class="desc-wrapper">
					<view class="left" style="color: #0075FC;">
						{{item.tags}}
						<!-- <text style="color: blue;">5A景区</text>|风景区 -->
					</view>
					<!-- 	<view class="right" v-if="item.distance < 1000">
						距您{{item.distance}}m
					</view>
					<view class="right" v-if="item.distance > 1000">
						距您{{parseInt(item.distance / 1000)}}km
					</view>
					<view class="right" v-if="item.distance == '未知'">
						{{item.distance}}
					</view> -->
					<view class="right">
						<!-- 距您{{item.remark | kmFilter}}km -->
						<image @click="call(item.contactPhone)" class="phone-icon"
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/call_icon.png"
							mode=""></image>
					</view>
				</view>
			</view>
			<view class="list-wrapper">
				{{item.address}}
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		props: {
			item: {
				type: Object
			},
			index: {
				type: Number
			}
		},
		components: {

		},
		computed: {

		},
		data() {
			return {

			};
		},
		created() {

		},
		methods: {
			star(item) {
				this.$emit('star',item)
			},
			call(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber: phoneNumber //仅为示例
				});

			},
		},
	};
</script>

<style lang="scss" scoped>
	.service-item {
		position: relative;
		display: flex;
		// align-items: center;
		font-family: PingFang SC, PingFang SC;
		// height: 205rpx;
		padding: 20rpx;
		margin: 20rpx 20rpx 20rpx 20rpx;
		// border-bottom: 2rpx solid #E7E7E7;
		background-color: #ffffff;

		.big-img {
			margin-bottom: 32rpx;
			width: 248rpx;
			height: 172rpx;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
		}

		.content-wrapper {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			margin-left: 20rpx;
			flex: 1;
			margin-bottom: 32rpx;

			.title-wrapper {
				margin-bottom: 10rpx;
				display: flex;
				align-items: center;

				.title {
					margin-right: 20rpx;
					font-weight: 600;
					font-size: 30rpx;
					color: #333333;
					line-height: 40rpx;
				}

				.star-icon {
					width: 28rpx;
					height: 27rpx;
				}
			}

			.desc-wrapper {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				line-height: 34rpx;

				.phone-icon {
					margin-left: 14rpx;
					width: 32rpx;
					height: 32rpx;
				}
			}

			.list-wrapper {
				display: flex;
				flex-wrap: wrap;
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
				line-height: 34rpx;

				.list {
					display: flex;
					align-items: center;
					width: 26%;
					margin-right: 40rpx;
					margin-bottom: 20rpx;

					&>image {
						margin-right: 4rpx;
						width: 32rpx;
						height: 32rpx;
					}

					.label {
						width: 84rpx;
					}

					&:nth-child(3n) {
						margin-right: 0;
					}
				}
			}
		}


	}
</style>