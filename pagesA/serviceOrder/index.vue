<template>
	<view class="home">
		<view class="g-flex" v-if="isTabShow">
			<scroll-view ref="tabbar1" id="tab-bar" class="tab-bar" :scroll="false" :scroll-x="true"
				:show-scrollbar="false" :scroll-into-view="scrollInto">
				<view style="height: 100%;position: relative;">
					<view style="display: flex;height: 100%;">
						<view class="uni-tab-item" v-for="(tab,index) in tabList" :key="tab.id" :id="tab.id"
							:ref="'tabitem'+index" :data-id="index" :data-current="index" @click="ontabtap">
							<text class="uni-tab-item-title" :class="['uni-tab-item-title-'+index]">
								<text :class="tabIndex==index ? 'uni-tab-item-title-active' : ''">
									{{tab.name}}
								</text></text>
						</view>
						<!-- ['uni-tab-item-title-'+index]' -->
					</view>
					<view class="scroll-line" :style="{
					left: pillsLeft + 'px',
					transform: `translateX(-${lineWidth / 2}px)`
				  }">
					</view>
				</view>
			</scroll-view>
		</view>

		<newBusiness :isTabShow="isTabShow" v-if="tabIndex==0"></newBusiness>
		<afterSale v-if="tabIndex==1" :alone="false"> </afterSale>
		<other v-if="tabIndex==2" :alone="false"> </other>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import newBusiness from './newBusiness/index.vue'
	import afterSale from './afterSale/index.vue'
	import other from './other/index.vue'
	export default {
		components: {
			tLoading,
			afterSale,
			newBusiness,
			other
		},
		data() {
			return {
				isTabShow: true, //线上发行灰度先不展示头部tab,上线先不展示售后
				tabIndex: 0,
				tabList: [{
					id: "tab01",
					name: 'ETC新办',
					newsid: 0
				}, {
					id: "tab02",
					name: '设备售后',
					newsid: 2
				},{
					id: "tab03",
					name: '其他',
					newsid: 3
				}],
				pillsLeft: 0, //// 胶囊距离左侧的位置
				lineWidth: 30,
			};
		},
		computed: {

		},
		onLoad(option) {
			var _this = this
			setTimeout(function() {
				_this.getTabItemWidth(); //获取位置
			});

			if (option && option.routeType == 'productSelect') {
				//新办需求从产品列表过来的话，隐藏顶部tab
				this.isTabShow = false
			}
		},
		onShow(){
			let pages = getCurrentPages(); //获取所有页面栈实例列表
			let prevPage = pages[pages.length - 1]; 
			if(prevPage && prevPage.$vm.$children[0].onSearchHandle){
				prevPage.$vm.$children[0].onSearchHandle()//当回到这个页面，tab是1的时候刷新售后单列表
			}
		},
		created() {

		},
		methods: {
			// 切换tab
			ontabtap(e) {
				let index = e.target.dataset.current || e.currentTarget.dataset.current;
				if (this.tabIndex === index) {
					return;
				}
				this.isCheckAll = false
				this.tabIndex = index;
				console.log(this.tabIndex)
				this.getTabItemWidth()
			},
			// 获取左移动位置
			getTabItemWidth() {
				//微信小程序获取方式
				let query = uni
					.createSelectorQuery()
					.in(this)
				// 获取所有的 tab-item 的宽度
				query
					.selectAll(`.uni-tab-item-title`)
					.boundingClientRect((data) => {
						if (!data) {
							return
						}
						console.log('data', data)
						let lineLeft = 0;
						let currentWidth = 0
						for (let i = 0; i < data.length; i++) {
							if (i == this.tabIndex) {
								currentWidth = data[i].width
								// if (this.tabIndex == 1) {
								// 	lineLeft = data[i].left + data[i].width / 3
								// } else {
								lineLeft = data[i].left + data[i].width / 5
								// }
							}
						}
						this.lineWidth = currentWidth * 0.5
						this.pillsLeft = lineLeft
					})
					.exec()
			},
		}
	};
</script>

<style lang="scss" scoped>
	.home {
		width: 100%;
		height: 100%;
	}

	.fixed-top {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		overflow: hidden;
		z-index: 999;
	}

	.tab-bar {
		background: #ffffff;
		width: 100%;
		height: 88rpx;
		white-space: nowrap;
		line-height: 88rpx;
	}

	.tab-bar ::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}

	.uni-tab-item {
		text-align: center;
		width: 50%;
		font-size: 28rpx;
		font-weight: 600;
		font-family: PingFangSC-Semibold, PingFang SC;
		color: #666666;
	}

	.uni-tab-item-title-active {
		color: #0066E9;
	}

	.scroll-line {
		background-color: #0066E9;
		width: 122rpx;
		height: 4rpx;
		position: absolute;
		bottom: 0;
		transition: all 0.3s linear;
		border-radius: 3rpx;
	}
</style>