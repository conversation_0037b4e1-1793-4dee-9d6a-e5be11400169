<template>
	<view class="order-item_wrapper">
		<view class="order-item" @click="toDetail">
			<view class="item-title">
				{{orderListData.applyRecordVO.vehicleCode}}【{{vehicleColorStr}}牌】【{{vehicleType}}车】<text
					class="is-trunk"></text>
			</view>
			<view class="item-container">
				<view class="item-bd" v-if="orderListData.sourceType == '1'">
					<view class="item-label">
						平台渠道：
					</view>
					<view class="item-value">
						抖音
					</view>
				</view>
				<view class="item-bd">
					<view class="item-label">
						订单类型：
					</view>
					<view class="item-value">
						ETC新办
					</view>
				</view>
				<view class="item-bd">
					<view class="item-label">
						订单编号：
					</view>
					<view class="item-value">
						{{orderListData.id}}
					</view>
				</view>
				<view class="item-bd">
					<view class="item-label">
						提交时间：
					</view>
					<view class="item-value">
						{{ formatHandle(new Date(orderListData.createTime).getTime(), 'yyyy-MM-dd HH:mm:ss') }}
					</view>
				</view>
			</view>
			<view v-if="orderListData.sourceType == '1'" class="item-status"
				:class="dyStatusInfoList[orderListData.nodeCode].status">
				{{orderListData.nodeName}}
			</view>
			<view v-else class="item-status" :class="statusInfoList[orderListData.hsApplyStatusId].status">
				{{orderListData.nodeName}}
			</view>
			<view class="btn-container">
				<view class="left">
					<view @click.stop="toDetail" class="item-btn info" style="width: 140rpx;">
						详情
					</view>
				</view>
				<view class="right" v-if="btnListLen < 3">
					<view class="item-btn" :class="btnItem.type" v-for="(btnItem,index) in orderListData.buttonStatuses"
						@click.stop="fun(btnItem)" :key="index">
						{{btnItem.title}}
					</view>
				</view>
				<view class="right" v-if="btnListLen > 2">
					<view class="item-btn info more-btn" @click.stop="showMore">
						<image style="width: 46rpx;height: 46rpx;margin-top: 6rpx;"
							src="../../static/new-apply/order/icon_etcapply_more.png" mode=""></image>
						<block v-if="showMoreBtn">
							<view class="arrow-top">
								<view class="triangle">

								</view>
							</view>
							<view class="more-list">
								<view class="more-list__item" v-for="(btnItem,index) in orderListData.buttonStatuses"
									@click.stop="fun(btnItem)" :key="index"
									v-if="index != orderListData.buttonStatuses.length - 1">
									{{btnItem.title}}
								</view>
							</view>
						</block>
					</view>
					<view class="item-btn" :class="lastBtnItem.type" @click.stop="fun(lastBtnItem)">
						{{ lastBtnItem.title}}
					</view>
				</view>
			</view>
		</view>
		<!-- <u-mask :show="showMoreBtn" @click="showMoreBtn = false"></u-mask> -->
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		getBusinessType,
		getApplyStatus,
		getCodeStatus
	} from '@/common/method/filter.js';
	import {
		statusInfoList,
		dyStatusInfoList
	} from '@/pagesA/common/optionsData.js'
	export default {
		components: {
			tLoading
		},
		props: {
			orderListData: {
				type: Object,
				default () {
					return {};
				}
			},
		},
		data() {
			return {
				statusInfoList,
				dyStatusInfoList,
				isLoading: false,
				showMoreBtn: false,
			}
		},
		computed: {
			applyStatus() {
				return getApplyStatus(this.orderListData.nodeStatus);
			},
			codeStatus() {
				return getCodeStatus(this.orderListData.nextNodeCode);
			},
			vehicleType() {
				return getVehicleType(this.orderListData.applyRecordVO.vehicleType);
			},
			vehicleColorStr() {
				return getVehicleColor(this.orderListData.applyRecordVO.vehicleColor);
			},
			vehicleClassType() {
				return getVehicleClassType(this.orderListData.applyRecordVO.vehicleNationalType);
			},
			btnListLen() {
				return this.orderListData.buttonStatuses.length
			},
			lastBtnItem() {
				return this.orderListData.buttonStatuses[this.btnListLen - 1] ? this.orderListData.buttonStatuses[this
					.btnListLen - 1] : {}
			}
		},
		methods: {
			fun(btnItem) {
				this.showMoreBtn = false
				console.log('btnItem', btnItem)
				switch (btnItem.handle) {
					case 'toPay':
						// 去支付
						this.toPay()
						break;
					case 'toSign':
						// 去签署
						this.toSign()
						break;
					case 'handleCancelOrder':
						// 取消订单
						this.handleCancelOrder()
						break;
					case 'handleAfterConfirm':
						// 售后确认收货
						this.handleAfterConfirm(this.orderListData.returnInfoDTO.record.id)
						break;
					case 'handleConfirm':
						// 确认收货
						this.handleConfirm()
						break;
					case 'toAfterApply':
						// 申请退换
						this.toAfter('afterApply')
						break;
					case 'toWl':
						// 查看物流
						this.toWl()
						break;
					case 'toAfterList':
						// 售后记录
						this.toAfter('afterList')
						break;
					case 'toIssue':
						// 前往激活
						this.toIssue()
						break;
					case 'toAfterDetail':
						//查看售后  传入售后id
						this.toAfterDetail(this.orderListData.returnInfoDTO.record.id)
						break;
					case 'cancelExchange':
						// 撤销退换货
						this.cancelExchange(this.orderListData.returnInfoDTO.record.id, btnItem.title)
						break;
					case 'sendDevice':
						//寄回设备  进入售后详情， 传入售后id
						this.toAfterDetail(this.orderListData.returnInfoDTO.record.id)
						break;
					case 'invoice':
						//开票按钮
						this.toInvoice()
						break;
					default:
						this.toDetail()
						break;
				}
			},
			setShowMoreBtn() {
				this.showMoreBtn = false
			},
			showMore() {
				this.showMoreBtn = !this.showMoreBtn
			},
			toIssue() {
				console.log('this.orderListData', this.orderListData)
				this.beforeToIssue(res => {
					if (res.checkSilkyFlag == 1) {
						//签约过了
						this.$store.dispatch('setIssueVehicleInfo', {
							businessSource: 1, //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
							orderId: this.orderListData.id,
							gxCardType: this.orderListData.applyRecordVO.productType,
							customerId: this.orderListData.applyRecordVO.customerId,
							vehicleCode: this.orderListData.applyRecordVO.vehicleCode,
							vehicleColor: this.orderListData.applyRecordVO.vehicleColor,
							sourceType: this.orderListData.sourceType
						})
						uni.navigateTo({
							url: '/pagesA/newBusiness/issue/issue-install'
						})
					} else {
						uni.showModal({
							title: '提示',
							content: '您已解约，如要继续激活请先进行签约？',
							cancelText: '取消',
							confirmText: '去签约',
							success: res => {
								if (res.confirm) {
									//没签约去C端代扣签约
									uni.reLaunch({
										url: '/pagesB/vehicleBusiness/vehicleList?fontType=ccsSign'
									})
								}
							}
						})
					}
				})

			},
			toDetail() {
				this.showMoreBtn = false
				//抖音详情跳转
				if (this.orderListData.sourceType == '1') {
					uni.navigateTo({
						url: '/pagesA/newBusiness/douyin/order/orderDetail?applyId=' + this.orderListData.id
					})
					return
				}
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderDetail?applyId=' + this.orderListData.id
				})
			},
			toWl() {
				console.log('wl')
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderWl/orderWlDetail?applyId=' + this.orderListData.id
				})
			},
			toPay() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/order?applyId=' + this.orderListData.id
				})
			},
			//签署去车辆信息表
			toSign() {
				this.$store.dispatch('setApplyId', this.orderListData.id)
				//抖音详情跳转
				if (this.orderListData.sourceType == '1') {
					uni.navigateTo({
						url: '/pagesA/newBusiness/douyin/order/orderDetail?applyId=' + this.orderListData.id
					})
					return
				}
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderDetail?applyId=' + this.orderListData.id
				})
			},
			updateOrder() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/productSelect/productSelect?applyId=' + this.orderListData.id
				})
			},
			toAfter(type) {
				let data = this.orderListData
				let params = {
					applyId: data.id,
					productType: data.applyRecordVO.productType,
					customerName: data.applyRecordVO.customerName,
					vehicleCode: data.applyRecordVO.vehicleCode,
					vehicleColor: data.applyRecordVO.vehicleColor

				}
				if (type == 'afterApply') {
					uni.navigateTo({
						url: '/pagesA/newBusiness/afterSale/select-type?applyData=' + encodeURIComponent(JSON
							.stringify(params))
					})
				} else if (type == 'afterList') {
					if (this.orderListData.returnInfoDTO.orderSize == 1) {
						let saleId = this.orderListData.returnInfoDTO.record.id
						//只有1条的直接去详情里
						uni.navigateTo({
							url: '/pagesA/newBusiness/afterSale/apply-detail?saleId=' + saleId
						})
					} else {
						console.log('执行了吗')
						uni.navigateTo({
							url: '/pagesA/newBusiness/afterSale/apply-list?applyId=' +
								this.orderListData.id
						})
					}
				}
			},
			toAfterDetail(id) {
				uni.navigateTo({
					url: '/pagesA/newBusiness/afterSale/apply-detail?saleId=' + id
				})
			},
			toInvoice() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/home/<USER>'
				})
			},
			//激活前校验
			beforeToIssue(callback) {
				this.isLoading = true
				this.$request.post(this.$interfaces.newSilkyActivationCheck, {
					data: {
						hsApplyRecordId: this.orderListData.id,
						sourceType: this.orderListData.sourceType
					}
				}).then(res => {
					console.log(res, '激活前校验');
					this.isLoading = false
					if (res.code == 200) {
						callback(res.data)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			// toAfterList() {
			// 	uni.navigateTo({
			// 		url: '/pagesA/newBusiness/afterSale/apply-list?applyId=' + this.orderListData.id
			// 	})
			// },
			confirmProduct() {
				this.isLoading = true
				this.$request.post(this.$interfaces.confirmProduct, {
					data: {
						id: this.orderListData.id,
					}
				}).then(res => {
					console.log(res, '获取申请单列表');
					this.isLoading = false
					if (res.code == 200) {

						this.$emit('handleUpdate', this.orderListData.id)

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			confirmAfterProduct(id) {
				this.isLoading = true
				this.$request.post(this.$interfaces.confirmRefundProduct, {
					data: {
						id: id,
					}
				}).then(res => {
					console.log(res, '获取申请单列表');
					this.isLoading = false
					if (res.code == 200) {

						this.$emit('handleUpdate', this.orderListData.id)

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			handleConfirm() {
				uni.showModal({
					title: "提示",
					content: '确定收货?',
					success: (res) => {
						if (res.confirm) {
							this.confirmProduct(this.orderListData.id)
							// console.log('确认取消')
						}
					}

				});
			},
			handleAfterConfirm(saleId) {
				uni.showModal({
					title: "提示",
					content: '确定收货?',
					success: (res) => {
						if (res.confirm) {
							this.confirmAfterProduct(saleId)
							// console.log('确认取消')
						}
					}

				});
			},
			handleCancelOrder() {
				if (this.orderListData.sourceType == '1') {
					uni.showModal({
						title: "取消订单",
						confirmText: '确定取消',
						// placeholderText: '请输入原因',
						cancelText: '我再想想',
						// editable: true,
						title: '提示',
						content: '我们正在为您审核订单，确定要取消吗',
						// showCancel: false
						success: (res) => {
							if (res.confirm) {
								let reason = res.content || '无'
								this.cancelApplyOrder(this.orderListData.id, reason)
								// console.log('确认取消')
							}
						}

					});
					return
				}
				uni.showModal({
					title: "取消订单原因",
					confirmText: '确定',
					placeholderText: '请输入原因',
					editable: true,
					success: (res) => {
						if (res.confirm) {
							let reason = res.content || '无'
							this.cancelApplyOrder(this.orderListData.id, reason)
							// console.log('确认取消')
						}
					}

				});

			},
			cancelApplyOrder(id, reason) {
				this.isLoading = true
				//兼容抖音取消订单
				let url = this.$interfaces.cancelApplyOrder
				if (this.orderListData.sourceType == '1') {
					url = this.$interfaces.dyDancelApplyOrder
				}
				this.$request.post(url, {
					data: {
						id: id,
						reason: reason
					}
				}).then(res => {
					console.log(res, '获取申请单列表');
					this.isLoading = false
					if (res.code == 200) {

						this.$emit('handleCancel', id, this.orderListData.sourceType)

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			cancelExchange(id, title) {
				uni.showModal({
					title: '操作提示',
					content: '确定要撤销售后申请吗',
					success: (res) => {
						if (res.confirm) {
							this.doCancel(id, title)
						}
					}
				});
			},
			doCancel(id, title) {
				this.isLoading = true
				let params = {
					id: id
				}

				this.$request.post(this.$interfaces.returnCancel, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('取消操作', res)
						// uni.navigateBack()
						this.$emit('handleCancelExchange', id, title)

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			formatHandle(time, format) {
				var t = new Date(time);
				var tf = function(i) {
					return (i < 10 ? '0' : '') + i;
				};
				return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
					switch (a) {
						case 'yyyy':
							return tf(t.getFullYear());
							break;
						case 'MM':
							return tf(t.getMonth() + 1);
							break;
						case 'mm':
							return tf(t.getMinutes());
							break;
						case 'dd':
							return tf(t.getDate());
							break;
						case 'HH':
							return tf(t.getHours());
							break;
						case 'ss':
							return tf(t.getSeconds());
							break;
					}
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-item {
		position: relative;
		// margin: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 12rpx;
		background-color: #ffffff;
		font-family: PingFangSC-Medium, PingFang SC;
		// overflow: hidden;
		padding: 6rpx 0rpx 11rpx 0rpx;
	}

	.item-title {
		position: relative;
		margin: 22rpx 40rpx;
		height: 45rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		line-height: 45rpx;

		&:before {
			content: ' ';
			position: absolute;
			left: -39rpx;
			top: 8rpx;
			width: 8rpx;
			height: 30rpx;
			background-color: #333333;
		}
	}

	.item-title .is-trunk {
		font-size: 28rpx;
	}

	.item-status {
		position: absolute;
		right: 0;
		top: 0;
		// width: 169rpx;
		padding: 0 16rpx;
		height: 63rpx;
		border-radius: 0rpx 10rpx 0rpx 30rpx;
		text-align: center;
		line-height: 63rpx;
		font-size: 26rpx;
	}

	// 待支付，已取消，已退货退款，已退货不退款
	.item-status.info {
		background: rgba(133, 134, 134, 0.15);
		color: #6A6969;
	}

	// 设备已发货，审核通过，已完结，已签收，换货审核通过，退货审核通过，设备已寄回
	.item-status.success {
		background: rgba(0, 189, 50, 0.11);
		color: #00BD32;
	}

	// 换货审核中，退货审核中，待取货
	.item-status.warnning {
		background: rgba(255, 145, 0, 0.14);
		color: #FF9100;
	}

	// 后台审核中
	.item-status.primary {
		background: rgba(0, 102, 233, 0.12);
		color: #0066E9;
	}

	// 审核不通过，换货审核不通过，退货审核不通过
	.item-status.error {
		background: rgba(255, 84, 84, 0.15);
		color: #FF5454;
	}

	.item-container {
		margin: 38rpx 40rpx 30rpx 40rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx dashed #C3C3C3;
	}

	.item-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.item-label {
		width: 160rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.item-value {
		flex: 1;
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
	}

	.btn-container {
		display: flex;
		justify-content: space-between;
		margin: 20rpx 40rpx;
	}

	.left {}

	.right {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		flex-wrap: wrap;
	}

	.right .more-btn {
		width: 88rpx;
		position: relative;

		.arrow-top {
			position: absolute;
			top: 60rpx;
			left: 24rpx;

			.triangle {
				position: relative;
				width: 40rpx;
				height: 20rpx;
				overflow: hidden;

				&::before {
					content: "";
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: #FFFFFF;
					transform-origin: left bottom;
					box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
					transform: rotate(45deg);
					z-index: 2;
				}
			}
		}

		.more-list {
			width: 204rpx;
			position: absolute;
			top: 40px;
			left: 0;
			background-color: #ffffff;
			border-radius: 12rpx;
			box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
			z-index: 1;
		}

		.more-list__item {
			margin: 0 20rpx;
			height: 92rpx;
			line-height: 92rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			text-align: left;
			border-bottom: 1rpx solid #F2F2F2;

			&:last-of-type {
				border-bottom: none;
			}
		}
	}


	.right .item-btn {
		margin-left: 20rpx;

		&:nth-child(2n) {
			margin-bottom: 15rpx;
		}
	}

	.right .item-btn:first-child {
		margin-left: 0;
	}

	.item-btn {
		// padding: 12rpx 40rpx;
		width: 190rpx;
		height: 58rpx;
		line-height: 58rpx;
		border-radius: 36rpx;
		font-size: 26rpx;
		text-align: center;
	}

	.item-btn.info {
		border: 2rpx solid #E8E8E8;
		color: #323435;
		background: #FFFFFF;
	}

	.item-btn.primary {
		border: 2rpx solid #0066E9;
		color: #FFFFFF;
		background: #0066E9;
	}
</style>