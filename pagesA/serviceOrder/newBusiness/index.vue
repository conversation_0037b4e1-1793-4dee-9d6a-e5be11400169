<template>
	<view class="order-detail_list" :class="{'no-height':!isTabShow}">
		<view class="fixed-top1">
			<view class="line-block"></view>
			<view class="weui-form">
				<!-- 				<view class="weui-cells__title">
					通行记录查询
				</view> -->
				<!-- 				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label">订单类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<picker :model="formData.handleMode" range-key="label" :value="index" :range="handleModeList"
							@change="pickerChange">
							<view class="weui-picker-value">
								{{handleModeList[index].label}}
							</view>
						</picker>
					</view>
					<image style="width: 40rpx;height: 40rpx" src="../../static/new-apply/order/arrow-down.png" mode="">
					</image>
				</view> -->
				<view class="weui-cells">
					<view class="vux-x-input weui-cell weui-cell_picker">
						<view class="weui-cell__hd">
							<view class="weui-label">提交时间</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="picker">
								<view class="pick-date pick-date-one">
									<picker mode="date" @change="startDateChange" :end="nowDate" :value="staCreateTime"
										style="width: 100%;">
										<u-cell-item title=" " :arrow="false" icon="date-fill">
											<view class="monthData">{{staCreateTime}}</view>
										</u-cell-item>
									</picker>
								</view>
								<view style="margin: 0 30rpx;">至</view>
								<view class="pick-date pick-date-two">
									<picker mode="date" @change="endDateChange" :end="nowDate" :value="endCreateTime"
										style="width: 100%;">
										<u-cell-item title=" " :arrow="false" icon="date-fill">
											<view class="monthData">{{endCreateTime}}</view>
										</u-cell-item>
									</picker>
								</view>
							</view>
						</view>
					</view>


					<view class="vux-x-input weui-cell weui-cell_picker" style="width:100%;height: 96rpx;">
						<view class="weui-cell__hd">
							<view class="weui-label">车牌号码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary" style="padding: 30rpx 30rpx 30rpx 0;"
							@click="showPlate">
							<view class="weui-input" v-if="vehicleNo" style="color: #333333;">
								{{vehicleNo}}
							</view>
							<view class="weui-input" v-else>请选择</view>
						</view>
						<view style="width:60rpx">
							<uni-icons @click="clearCarNo" type="clear" color="#c0c4cc" size="24" v-if="vehicleNo">
							</uni-icons>
						</view>
					</view>
				</view>
			</view>
			<view class="search-btn">
				<button class="weui-btn weui-btn_primary " @click="onSearchHandle()">
					搜索
				</button>
			</view>
		</view>
		<view class="scroll-box" v-if="orderListData.length > 0">
			<orderItem style="height: 100px;" v-for="(item,index) in orderListData" :key="index" :orderListData='item'
				@handleCancel="handleCancel" @handleUpdate="handleUpdate" @handleCancelExchange="handleCancelExchange">
			</orderItem>
			<!-- <load-more :loadStatus="noticeLoadStatus" /> -->
		</view>
		<view class="no-data" v-if="orderListData.length == 0">
			<image src="../../static/no_data.png" mode="" class="no-data-img"></image>
			<view class="no-data-title">暂无记录</view>
			<view class="no-data-title">请检查登录账号是否正确</view>
		</view>
		<plate-input v-if="plateShow" :plate="vehicleNo" @export="setPlate" @close="plateShow=false" />
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import orderItem from './orderItem.vue'
	// import loadMore from '../../components/load-more/index.vue';
	import float from '@/common/method/float.js'
	import plateInput from '@/components/uni-plate-input/uni-plate-input.vue';
	import uniDataTimePicker from '@/pagesA/components/uni-datetime-picker/uni-datetime-picker.vue'
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar
	} from "@/common/storageUtil.js";
	import {
		handleModeList
	} from '@/common/const/optionData.js'
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')

	function getDate(type) {
		const date = new Date();
		console.log(date)
		let year = date.getFullYear();
		let month = date.getMonth() + 1;
		let day = date.getDate();

		if (type === 'start') {
			year = year - 1;
		} else if (type === 'end') {
			year = year;
		}
		month = month > 9 ? month : '0' + month;
		day = day > 9 ? day : '0' + day;

		return `${year}-${month}-${day}`;
	}
	export default {
		props: {
			isTabShow: {
				type: Boolean,
				default: true
			}
		},
		components: {
			TButton,
			orderItem,
			tLoading,
			// loadMore
			plateInput,
			uniDataTimePicker
		},
		options: {
			styleIsolation: 'shared'
		},
		data() {
			return {
				isLoading: false,
				plateShow: false,
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				// cardAmount: {},
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				orderListData: [],
				vehicleNo: '', //车牌号码
				staCreateTime: getDate('start'), //开始提交时间
				endCreateTime: getDate('end'), //结束提交时间
				sumMoney: 0,
				index: 0,
				nowDate: ''
			};
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},
		},
		watch: {
			// staCreateTime(val) {
			// 	if (val != '') {
			// 		this.formData.startTime = this.formatHandle(new Date(val).getTime(), "yyyy-MM-dd")
			// 	} else {
			// 		this.formData.startTime = ''
			// 	}
			// },
			// end_date(val) {
			// 	if (val != '') {
			// 		this.formData.endTime = this.formatHandle(new Date(val).getTime(), "yyyy-MM-dd") +
			// 			' 23:59:59'
			// 	} else {
			// 		this.formData.endTime = ''
			// 	}
			// },
		},
		// onLoad() {
		// 	this.formData.carColor = this.vehicleInfo.vehicle_color
		// 	this.formData.carNo = this.vehicleInfo.vehicle_code
		// 	// this.start_date = this.formatHandle(new Date().getTime(), "yyyy-MM-dd")
		// 	// this.end_date = this.formatHandle(new Date().getTime(), "yyyy-MM-dd")
		// 	this.getRecord('refresh')
		// },
		created() {
			this.nowDate = dayjs(new Date()).format('YYYY-MM-DD');
			// this.getApplyList()
		},
		// onShow() {
		// 	this.nowDate = dayjs(new Date()).format('YYYY-MM-DD');
		// 	this.getApplyList()
		// },
		// //加载更多
		// onReachBottom() {
		// 	console.log('触底', this.formData)
		// 	this.formData.pageNo += 1;
		// 	this.getRecord('add');
		// },
		methods: {
			startDateChange(e) {
				this.staCreateTime = e.detail.value
			},
			endDateChange(e) {
				this.endCreateTime = e.detail.value
			},
			handleCancel(id, sourceType) {
				// this.getApplyList()
				if (sourceType == '1') {
					uni.reLaunch({
						url: '/pagesA/newBusiness/douyin/order/orderSuccess?type=2&sourceType=1&applyId=' +
							id
					})
					return
				}
				uni.reLaunch({
					url: '/pagesA/newBusiness/order/orderSuccess?type=2&applyId=' + id
				})
			},
			handleUpdate(id) {
				uni.reLaunch({
					url: '/pagesA/newBusiness/order/orderSuccess?type=5&applyId=' + id
				})
			},
			handleCancelExchange(id, title) {
				if (title == '撤销换货') {
					uni.reLaunch({
						url: '/pagesA/newBusiness/order/orderSuccess?type=4&saleId=' + id
					})
				} else if (title == '撤销退货') {
					uni.reLaunch({
						url: '/pagesA/newBusiness/order/orderSuccess?type=8&saleId=' + id
					})
				}

			},
			getApplyList() {
				let params = {
					businessType: '1', //业务类型1： 新办 2： 撤单 3： 退货 4: 换货	
					staCreateTime: this.staCreateTime,
					endCreateTime: this.endCreateTime,
					vehicleNo: this.vehicleNo
				}

				this.isLoading = true

				this.$request.post(this.$interfaces.applyList, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res, '获取申请单列表');
					if (res.code == 200) {
						// this.formData=res.data
						this.orderListData = res.data
						this.changeBtnData()
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			changeBtnData() {
				//按钮排序
				this.orderListData.forEach(item => {
					let btnArr = item.buttonStatuses
					if (btnArr.length > 0) {
						for (let i = 0; i < btnArr.length - 1; i++) {
							for (let j = 0; j < btnArr.length - 1 - i; j++) {
								if (btnArr[j].type == 'primary' && btnArr[j + 1].type == 'info') {
									//比较前后，交换位置
									let temp = btnArr[j]
									btnArr[j] = btnArr[j + 1];
									btnArr[j + 1] = temp
								}
							}
						}
					}
				})
			},
			clearTime() {
				this.staCreateTime = ''
			},
			clearCarNo() {
				this.vehicleNo = ''
			},
			showPlate() {
				this.plateShow = true
			},
			setPlate(plate) {
				if (plate.length >= 7) this.vehicleNo = plate
				this.plateShow = false
			},
			foramtDate(time, fmt) {
				time = time.replace('.0', '');
				let value = time && time.replace(/-/g, '/');
				let getDate = new Date(value);
				let o = {
					'M+': getDate.getMonth() + 1,
					'd+': getDate.getDate(),
					'h+': getDate.getHours(),
					'm+': getDate.getMinutes(),
					's+': getDate.getSeconds(),
					'q+': Math.floor((getDate.getMonth() + 3) / 3),
					'S': getDate.getMilliseconds()
				};
				if (/(y+)/.test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))
				}
				for (let k in o) {
					if (new RegExp('(' + k + ')').test(fmt)) {
						fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k])
							.length)))
					}
				}
				return fmt;
			},
			formatHandle(time, format) {
				var t = new Date(time);
				var tf = function(i) {
					return (i < 10 ? '0' : '') + i
				};
				console.log(t, 'formatHandle')
				return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
					switch (a) {
						case 'yyyy':
							return tf(t.getFullYear());
							break;
						case 'MM':
							return tf(t.getMonth() + 1);
							break;
						case 'mm':
							return tf(t.getMinutes());
							break;
						case 'dd':
							return tf(t.getDate());
							break;
						case 'HH':
							return tf(t.getHours());
							break;
						case 'ss':
							return tf(t.getSeconds());
							break;
					}
				})
			},
			onSearchHandle() {
				this.getApplyList();
			},
			startDateChange(e) {
				this.staCreateTime = e.detail.value
				console.log('this.formData.staCreateTime', this.staCreateTime)
			},
			// endDateChange(e) {
			// 	this.end_date = e.detail.value
			// },
			// upper: function(e) {
			// 	console.log('upper')
			// },
			// scrolltolower: function(e) {
			// 	console.log('scrolltolower')

			// },
			// scroll: function(e) {

			// 	this.old.scrollTop = e.detail.scrollTop;

			// },
			// getRecord(type) {
			// 	//没有更多直接返回
			// 	if (type === 'add') {
			// 		if (this.noticeLoadStatus == 3) {
			// 			return;
			// 		}
			// 	} else {
			// 		this.formData.pageNo = 1;
			// 	}
			// 	//加载中
			// 	this.noticeLoadStatus = 1;
			// 	if (this.formData.pageNo == 1) {
			// 		this.isLoading = true
			// 	}
			// 	let params = JSON.parse(JSON.stringify(this.formData))
			// 	let data = {

			// 		'data': params
			// 	}
			// 	console.log('入参', params)
			// 	this.noticeLoadStatus = 1;
			// 	this.$request.post(this.$interfaces.applyList, data).then(res => {
			// 		console.log('通行记录', res);
			// 		this.isLoading = false;
			// 		if (res.code == 200) {
			// 			let resData = res.data.data

			// 			if (this.formData.pageNo == 1) {
			// 				this.orderListData = resData;
			// 				if (resData.length < 1) {
			// 					this.noticeLoadStatus = 0
			// 					return;
			// 				}
			// 			} else {
			// 				this.orderListData = this.orderListData.concat(resData);
			// 			}

			// 			if (resData.length < 1 || resData.length < this.formData.pageSize) {
			// 				//没数据了
			// 				this.noticeLoadStatus = 3
			// 				return
			// 			}

			// 			// 	// this.orderListData.map(item => {
			// 			// 	// 	return this.sumMoney = float.add(item.fee, this.sumMoney)
			// 			// 	// })
			// 			// 	this.noticeLoadStatus = 3;
			// 			// } else {
			// 			// 	this.noticeLoadStatus = 0;
			// 			// }
			// 		} else if (res.code == 401) {
			// 			uni.showModal({
			// 				title: "提示",
			// 				content: res.msg,
			// 				showCancel: false,
			// 				success: (res) => {
			// 					if (res.confirm) {
			// 						uni.reLaunch({
			// 							url: '/pages/login/p-login'
			// 						});
			// 					}
			// 				}
			// 			});
			// 			this.isLoading = false;

			// 		} else {
			// 			this.noticeLoadStatus = 2;
			// 			this.isLoading = false;
			// 		}

			// 	}).catch(() => {
			// 		this.noticeLoadStatus = 2;
			// 		this.isLoading = false;
			// 	})
			// },
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.order-detail_list {
		width: 100%;
		height: calc(100% - 88rpx);
	}

	.no-height {
		height: 100%;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-form {
		margin-top: 0px;
	}

	.picker {
		width: 100%;
		display: flex;
		height: 100%;
		align-items: center;
	}

	/deep/.u-border-bottom::after {
		border-bottom: none;
	}

	.pick-date {
		width: 192rpx;
		display: flex;
		align-items: center;

		/deep/.u-cell {
			position: relative;
		}

		/deep/.u-cell__value {
			font-size: 30rpx !important;
		}

		/deep/.u-cell__left-icon-wrap {
			position: absolute;
			right: 0;
			margin-right: 0px !important;
		}

		/deep/.u-icon__icon {
			font-size: 25rpx !important;
			color: #999999;
		}
	}

	.pick-date-two {
		// flex: 1;
	}

	/deep/.u-cell {
		padding: 0 0 !important;
		line-height: 80rpx !important;
	}

	/deep/.u-cell__value {
		color: #333 !important;
		text-align: left !important;
		font-size: 30rpx !important;
	}

	.weui-cells {
		padding-top: 0;
	}

	.weui-cell {
		padding: 0rpx 30rpx;
	}

	.weui-cells::before {
		border: 0;
	}

	.weui-label {
		width: 180rpx;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 96rpx;
	}

	.weui-cell_picker .weui-picker-value {
		text-align: left;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 96rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
		height: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 30rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
		height: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 30rpx;
	}

	.search-btn {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #ffffff;
		margin-top: 1rpx;
	}

	.weui-btn {
		flex: 1;
		margin-top: 0;
		// margin-right: 20rpx;
		background-color: #0066E9;
	}

	// .weui-btn:last-child {
	// 	margin-right: 0;
	// }

	.fixed-top {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		// bottom: 0;
		overflow: hidden;
		z-index: 10;
		background-color: #F3F3F3;
	}

	.scroll-box {
		// padding-top: 358rpx;
		// padding-bottom: 20rpx;
		padding: 20rpx 20rpx 0;
		height: calc(100% - 370rpx);
		overflow-y: scroll;
	}

	.apply-record {
		// 	width: 150rpx;
		// 	margin-left: 10rpx;
		color: #01C1B2;
		border: 1rpx solid #01C1B2;
		background: transparent;
		// 	padding: 0 30rpx;
		// 	font-size: 28rpx;
		// 	height: 84rpx;
		// 	line-height: 1;
		// 	text-align: center;
		// 	text-decoration: none;
	}

	.weui-title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		background: #333333;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background: #333333;
	}

	.no-data {
		width: calc(100% - 40rpx);
		text-align: center;
		background: #fff;
		margin-left: 20rpx;
		margin-top: 20rpx;
		border-radius: 11rpx;
		height: 430rpx;

		.no-data-img {
			width: 248rpx;
			height: 269rpx;
			background-size: 100%;
			margin-top: 62rpx;
		}

		.no-data-title {
			height: 40rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #333333;
			line-height: 40rpx;
		}
	}
</style>