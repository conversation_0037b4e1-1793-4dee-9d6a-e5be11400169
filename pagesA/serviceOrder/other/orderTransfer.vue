<template>
  <view class="transfers-box">
    <!-- <view class="no-vehicle" v-if="isNoVehicle">
      <image
        src="../../../static/toc/addcar_nocar.png"
        mode="aspectFilt"
        class="no-vehicle_icon"
      >
      </image>
      <view class="no-vehicle_des">
        暂无车辆
      </view>
      <view class="g-flex g-flex-horizontal-vertical" v-if="isNoVehicle">
        <TButton
          title="前往绑定ETC"
          @clickButton="bindEtcAccount"
          style="width: 50%;"
        ></TButton>
      </view>
    </view> -->
    <view>
      <view class="image-box">
        <image
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/package/vehicleLoading.gif"
          mode="aspectFill"
          class="vehicle-loading"
        >
        </image>
      </view>
      <view class="desc">
        系统拼命加载中...
      </view>
    </view>
  </view>
</template>

<script>
import {
  getTicket,
  getOpenid,
  getEtcAccountInfo,
  getLoginUserInfo,
  setOpenid,
  getAccountId,
  setDefaultUrl
} from "@/common/storageUtil.js";
	import TButton from '@/components/t-button.vue'

export default {
  components:{
    TButton
  },
  data() {
    return {
      redirectPath: "pagesA/serviceOrder/other/orderTransfer",
      redirectCallcenter: "", // 重定向路由地址
      targetPage: "",
      workflowId: "",
      isNoVehicle: false
      // callcenterObj: {
      //   aa: "https://ccrm.wengine.cn/chatui/#/app/online"
      // }
    };
  },
  computed: {},
  async onLoad(option) {
    this.targetPage = option.targetPage;
    this.workflowId = option.workflowId;
    // console.log(encodeURIComponent(option),'encodeURIComponent(option)')
    if (!getTicket()) {
      uni.showModal({
        title: "提示",
        content: "请先登录",
        success: res => {
          if (res.confirm) {
            this.goLoginHandle();
          }
        }
      });
      return;
    }
    // if (!getAccountId()) {
    //   this.isNoVehicle = true;
    //   return;
    // }
    let redirectCallcenter = await this.getRedirectUrl();
    // if(!redirectCallcenter) {
    //   uni.showModal({
    // 			title: '提示',
    // 			content: '未获取到跳转链接,请截图后联系在线客服处理',
    // 			showCancel: false
    // 		})
    //   return
    // }
    this.redirectCallcenter = redirectCallcenter;
    this.toRedirectCallcenter();
  },

  methods: {
    // 跳转
    toRedirectCallcenter() {
      // let path = this.callcenterObj[this.scene];
      let callcenter;
      // if (this.scene == "aa") {
      //   callcenter = `${path}?tntInstId=HSYQGXGS&scene=SCE0000027`;
      // }
      callcenter = this.redirectCallcenter;

      uni.navigateTo({
        url:
          "/pages/uni-webview/uni-webview?ownPath=" +
          encodeURIComponent(callcenter)
      });
    },
    // 跳转登录页面
    goLoginHandle() {
      uni.setStorageSync("redirectPath", this.redirectPath);
      uni.reLaunch({
        url: "/pagesD/login/p-login"
      });
    },
    // 获取跳转链接
    async getRedirectUrl() {
      let openId = await this.getOpenIdHandle();
      let params = {
        custMastId: getEtcAccountInfo().custMastId,
        netUserId: getLoginUserInfo().userIdStr,
        openId: openId,
        ticket: getTicket(),
        targetPage: this.targetPage || "gxEtcWorkOrder"
      };
      if (this.workflowId) {
        params.workflowId = this.workflowId;
      }
      let res = await this.$request.post(this.$interfaces.customerServiceUrl, {
        data: params
      });
      console.log(res, "callcenter");
      if (res.code != 200) {
        uni.showModal({
          title: "提示",
          content: "跳转失败：" + res.msg,
          showCancel: false
        });
        return false;
      }
      return new Promise((resolve, reject) => {
        resolve(res.data);
      });
    },
    // 获取openId操作
    async getOpenIdHandle() {
      if (getOpenid()) {
        console.log(111);
        return new Promise((resolve, reject) => {
          resolve(getOpenid());
        });
      }
      let code = await this.getWxUserCode();
      let params = {
        code
      };
      let res = await this.$request.post(this.$interfaces.getOpenid, {
        data: params
      });
      if (res.code == 200) {
        if (res.data && res.data.openid) {
          setOpenid(res.data.openid);
          this.openid = res.data.openid;
        }
      }
      return new Promise((resolve, reject) => {
        resolve(this.openid);
      });
    },
    //获取code
    getWxUserCode() {
      return new Promise((resolve, reject) => {
        wx.login({
          success: res => {
            resolve(res.code);
          },
          fail: err => {
            reject(err);
          }
        });
      });
    },
    //绑定ETC用户并回跳
    bindEtcAccount() {
      setDefaultUrl("/pagesA/serviceOrder/other/orderTransfer");
      uni.navigateTo({
        url: "/pagesB/accountBusiness/accountList/accountList"
      });
    }
  }
};
</script>

<style scoped lang="scss">
.transfers-box {
  width: 100%;
  height: 100%;
  background-color: #fff;

  .image-box {
    width: 100%;
    display: flex;
    justify-content: center;
    padding-top: 180rpx;
  }

  .vehicle-loading {
    width: 370rpx;
    height: 278rpx;
  }

  .desc {
    font-size: 28rpx;
    width: 100%;
    font-weight: 400;
    text-align: center;
  }

  	.no-vehicle {
		padding-top: 320rpx;
		width: 100%;
		/* background-color: #f9f9f9; */
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 29rpx;
		color: #333333;
		font-weight: 400;
		text-align: center;
		margin-top: 60rpx;
	}
}
</style>
