<template>
  <view class="update-success">
    <view class="protocol">
      <view class="msg">
        <view class="msg-icon">
          <image
            src="../../static/new-apply/order/pic_success.png"
            class="img"
          ></image>
        </view>
        <view class="msg-text">
          <view class="msg-title"
            >提交成功，后续您可通过八桂行小程序首页-服务订单-其他模块，查询此申请订单的处理进度。</view
          >
        </view>
      </view>
    </view>
    <view class="bottom-box g-flex justify-center">
      <button class="weui-btn weui-btn_primary" @click="toHome">
        返回首页
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    toHome() {
      this.$subscriptionMethod(() => {
        uni.reLaunch({
          url: "/pages/home/<USER>/p-home"
        });
      });
    }
  },
  onLoad() {}
};
</script>

<style>
.protocol {
  width: 710rpx;
  margin: 0 auto;
  margin-top: 20rpx;
  background: #fff;
  padding: 20rpx 40rpx 34rpx 40rpx;
}
.msg .msg-icon {
  width: 452rpx;
  height: 437rpx;
  display: flex;
  justify-content: center;
  margin: 0 auto;
  /* margin-bottom: 24rpx; */
}

.msg-title {
  font-weight: 400;
  font-size: 32rpx;
  text-align: center;
  color: #323435;
}

.bottom-box {
  width: 100%;
  position: fixed;
  bottom: 0;
  height: 166rpx;
  background: #fff;
}

.weui-btn {
  border-radius: 12rpx;
  width: 662rpx;
  height: 100rpx;
  margin-top: 18rpx;
}
</style>
