<template>
  <view class="order-detail_list" :class="{ 'no-height': !isTabShow }">
    <view class="fixed-top1">
      <view class="line-block"></view>
      <view class="weui-form">
        <!-- 				<view class="weui-cells__title">
					通行记录查询
				</view> -->
        <view class="vux-x-input weui-cell weui-cell_picker">
          <view class="weui-cell__hd">
            <view class="weui-label">订单类型</view>
          </view>
          <view class="weui-cell__bd weui-cell__primary">
            <picker
              :model="formData.handleMode"
              range-key="label"
              :value="index"
              :range="handleModeList"
              @change="pickerChange"
            >
              <view class="weui-picker-value" style="color: #333333;">
                {{ handleModeList[index].label }}
              </view>
            </picker>
          </view>
          <image
            style="width: 40rpx;height: 40rpx"
            src="../../static/new-apply/order/arrow-down.png"
            mode=""
          >
          </image>
        </view>
        <view class="weui-cells">
          <view class="vux-x-input weui-cell weui-cell_picker">
            <view class="weui-cell__hd">
              <view class="weui-label">提交时间</view>
            </view>
            <view class="weui-cell__bd weui-cell__primary">
              <view class="picker">
                <view class="pick-date pick-date-one">
                  <picker
                    mode="date"
                    @change="startDateChange"
                    :end="nowDate"
                    :value="staCreateTime"
                    style="width: 100%;"
                  >
                    <u-cell-item title=" " :arrow="false" icon="date-fill">
                      <view class="monthData">{{ staCreateTime }}</view>
                    </u-cell-item>
                  </picker>
                </view>
                <view style="margin: 0 30rpx;">至</view>
                <view class="pick-date pick-date-two">
                  <picker
                    mode="date"
                    @change="endDateChange"
                    :end="nowDate"
                    :value="endCreateTime"
                    style="width: 100%;"
                  >
                    <u-cell-item title=" " :arrow="false" icon="date-fill">
                      <view class="monthData">{{ endCreateTime }}</view>
                    </u-cell-item>
                  </picker>
                </view>
              </view>
            </view>
          </view>

          <view
            class="vux-x-input weui-cell weui-cell_picker"
            style="width:100%;height: 96rpx;"
          >
            <view class="weui-cell__hd">
              <view class="weui-label">车牌号码</view>
            </view>
            <view
              class="weui-cell__bd weui-cell__primary"
              style="padding: 30rpx 30rpx 30rpx 0;"
              @click="showPlate"
            >
              <view class="weui-input" v-if="vehicleNo" style="color: #333333;">
                {{ vehicleNo }}
              </view>
              <view class="weui-input" v-else>请选择</view>
            </view>
            <view style="width:60rpx">
              <uni-icons
                @click="clearCarNo"
                type="clear"
                color="#c0c4cc"
                size="24"
                v-if="vehicleNo"
              >
              </uni-icons>
            </view>
          </view>
        </view>
      </view>
      <view class="search-btn">
        <button class="weui-btn weui-btn_primary " @click="onSearchHandle()">
          搜索
        </button>
      </view>
    </view>
    <scroll-view
      :style="{ height: height }"
      :scroll-top="scrollTop"
      scroll-y="true"
      class="scroll-Y"
      :lower-threshold="lowerThreshold"
      @scrolltolower="scrolltolower"
      @scroll="scroll"
    >
      <view class="scroll-box" v-if="orderListData.length > 0">
        <orderItem
          v-for="(item, index) in orderListData"
          :key="index"
          :orderListData="item"
          @handleCancel="handleCancel"
          @handleUpdate="handleUpdate"
          @handleCancelExchange="handleCancelExchange"
        >
        </orderItem>
        <!-- <load-more :loadStatus="noticeLoadStatus" /> -->
      </view>
      <view class="no-data" v-if="orderListData.length == 0">
        <image
          src="../../static/no_data.png"
          mode=""
          class="no-data-img"
        ></image>
        <view class="no-data-title">暂无记录</view>
        <view class="no-data-title">请检查登录账号是否正确</view>
      </view>
    </scroll-view>

    <plate-input
      v-if="plateShow"
      :plate="vehicleNo"
      @export="setPlate"
      @close="plateShow = false"
    />
    <tLoading :isShow="isLoading" />
  </view>
</template>

<script>
import TButton from "@/components/t-button.vue";
import tLoading from "@/components/common/t-loading.vue";
import orderItem from "./orderItem.vue";
// import loadMore from '../../components/load-more/index.vue';
import float from "@/common/method/float.js";
import plateInput from "@/components/uni-plate-input/uni-plate-input.vue";
import uniDataTimePicker from "@/pagesA/components/uni-datetime-picker/uni-datetime-picker.vue";
import {
  getTicket,
  getOpenid,
  getEtcAccountInfo,
  getLoginUserInfo,
  setOpenid,
  getCurrUserInfo,
  getCurrentCar
} from "@/common/storageUtil.js";
import { handleModeList } from "@/common/const/optionData.js";
var dayjs = require("@/js_sdk/dayjs/dayjs.min.js");

function getDate(type) {
  const date = new Date();
  console.log(date);
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();

  if (type === "start") {
    year = year - 1;
  } else if (type === "end") {
    year = year;
  }
  month = month > 9 ? month : "0" + month;
  day = day > 9 ? day : "0" + day;

  return `${year}-${month}-${day}`;
}
export default {
  props: {
    isTabShow: {
      type: Boolean,
      default: true
    }
  },
  components: {
    TButton,
    orderItem,
    tLoading,
    // loadMore
    plateInput,
    uniDataTimePicker
  },
  options: {
    styleIsolation: "shared"
  },
  data() {
    return {
      isLoading: false,
      plateShow: false,
      lowerThreshold: 120,
      windowHeight: this.windowHeight,
      // cardAmount: {},
      scrollTop: 0,
      noticeLoadStatus: 3,
      old: {
        scrollTop: 0
      },
      orderListData: [
        // {
        //   carNumber: "浙G5E41B",
        //   modelName: "C端自助提交",
        //   workflowId: "123",
        //   carColor: 0,
        //   status: "处理中"
        // }
      ],
      vehicleNo: "", //车牌号码
      staCreateTime: getDate("start"), //开始提交时间
      endCreateTime: getDate("end"), //结束提交时间
      sumMoney: 0,
      index: 0,
      nowDate: "",
      handleModeList,
      height: "calc(100% - 480rpx)",
      searchObj: {
        page: 1,
        pageSize: 10
      },
      flag: false
    };
  },
  computed: {
    customerInfo() {
      return getCurrUserInfo() || {};
    },
    vehicleInfo() {
      return getCurrentCar() || {};
    }
  },

  created() {
    this.nowDate = dayjs(new Date()).format("YYYY-MM-DD");
    this.getWorkerList();
  },

  methods: {
    pickerChange(e) {
      console.log("tasdas", e.target);
      this.index = e.target.value;
      // this.formData.handleMode = this.handleModeList[e.target.value].value
    },
    startDateChange(e) {
      this.staCreateTime = e.detail.value;
    },
    endDateChange(e) {
      this.endCreateTime = e.detail.value;
    },
    handleCancel(id) {
      // this.async ()
      uni.reLaunch({
        url: "/pagesA/newBusiness/order/orderSuccess?type=2&applyId=" + id
      });
    },
    handleUpdate(id) {
      uni.reLaunch({
        url: "/pagesA/newBusiness/order/orderSuccess?type=5&applyId=" + id
      });
    },
    handleCancelExchange(id, title) {
      if (title == "撤销换货") {
        uni.reLaunch({
          url: "/pagesA/newBusiness/order/orderSuccess?type=4&saleId=" + id
        });
      } else if (title == "撤销退货") {
        uni.reLaunch({
          url: "/pagesA/newBusiness/order/orderSuccess?type=8&saleId=" + id
        });
      }
    },
    async getWorkerList() {
      let openId = await this.getOpenIdHandle();
      let params = {
        startTime: `${this.staCreateTime} 00:00:00`,
        endTime: `${this.endCreateTime} 23:59:59`,
        carNumber: this.vehicleNo,
        page: this.searchObj.page,
        pageSize: this.searchObj.pageSize,
        custMastId: getEtcAccountInfo().custMastId,
        netUserId: getLoginUserInfo().userIdStr,
        ticket: getTicket(),
        openId
      };

      this.isLoading = true
      this.flag = true;

      this.$request
        .post(this.$interfaces.selfWorkflowList, {
          data: params
        })
        .then(res => {
          this.isLoading = false
          this.flag = false;
          console.log(res, "获取申请单列表");
          if (res.code == 200) {
            let list = res.data.data ? res.data.data : [];
            this.orderListData = this.orderListData.concat(list);
            console.log(this.orderListData.length == res.data.total)
            if (this.orderListData.length == res.data.total) {
              this.flag = true;
            }
          } else {
            uni.showModal({
              title: "提示",
              content: res.msg,
              showCancel: false
            });
          }
        })
        .catch(err => {
          this.isLoading = false
          this.flag = false;
          uni.showModal({
            title: "提示",
            content: err.msg,
            showCancel: false
          });
        });
    },

    clearTime() {
      this.staCreateTime = "";
    },
    clearCarNo() {
      this.vehicleNo = "";
    },
    showPlate() {
      this.plateShow = true;
    },
    setPlate(plate) {
      if (plate.length >= 7) this.vehicleNo = plate;
      this.plateShow = false;
    },
    foramtDate(time, fmt) {
      time = time.replace(".0", "");
      let value = time && time.replace(/-/g, "/");
      let getDate = new Date(value);
      let o = {
        "M+": getDate.getMonth() + 1,
        "d+": getDate.getDate(),
        "h+": getDate.getHours(),
        "m+": getDate.getMinutes(),
        "s+": getDate.getSeconds(),
        "q+": Math.floor((getDate.getMonth() + 3) / 3),
        S: getDate.getMilliseconds()
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (getDate.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      }
      for (let k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length === 1
              ? o[k]
              : ("00" + o[k]).substr(("" + o[k]).length)
          );
        }
      }
      return fmt;
    },
    formatHandle(time, format) {
      var t = new Date(time);
      var tf = function(i) {
        return (i < 10 ? "0" : "") + i;
      };
      console.log(t, "formatHandle");
      return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
        switch (a) {
          case "yyyy":
            return tf(t.getFullYear());
            break;
          case "MM":
            return tf(t.getMonth() + 1);
            break;
          case "mm":
            return tf(t.getMinutes());
            break;
          case "dd":
            return tf(t.getDate());
            break;
          case "HH":
            return tf(t.getHours());
            break;
          case "ss":
            return tf(t.getSeconds());
            break;
        }
      });
    },
    onSearchHandle() {
      this.orderListData = []
      this.getWorkerList();
    },
    startDateChange(e) {
      this.staCreateTime = e.detail.value;
      console.log("this.formData.staCreateTime", this.staCreateTime);
    },
    scrolltolower: function(e) {
      if (this.flag) return;
      let self = this;
      console.log(123123);
      setTimeout(function() {
        self.searchObj.page = self.searchObj.page + 1;
        self.getWorkerList();
      }, 500);
    },
    scroll: function(e) {
      this.old.scrollTop = e.detail.scrollTop;
    },
    // 获取openId操作
    async getOpenIdHandle() {
      if (getOpenid()) {
        console.log(111);
        return new Promise((resolve, reject) => {
          resolve(getOpenid());
        });
      }
      let code = await this.getWxUserCode();
      let params = {
        code
      };
      let res = await this.$request.post(this.$interfaces.getOpenid, {
        data: params
      });
      if (res.code == 200) {
        if (res.data && res.data.openid) {
          setOpenid(res.data.openid);
          this.openid = res.data.openid;
        }
      }
      return new Promise((resolve, reject) => {
        resolve(this.openid);
      });
    },
    //获取code
    getWxUserCode() {
      return new Promise((resolve, reject) => {
        uni.login({
          success: res => {
            resolve(res.code);
          },
          fail: err => {
            reject(err);
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.order-detail_list {
  width: 100%;
  height: calc(100% - 88rpx);
}

.no-height {
  height: 100%;
}

.bottom-box {
  display: flex;
}

.bottom-box .btn-item {
  flex: 1;
}

.bottom-box .btn-item:last-child {
  margin-left: 32rpx;
}

.activation-page {
  position: relative;
}

.weui-form {
  margin-top: 0px;
}

.picker {
  width: 100%;
  display: flex;
  height: 100%;
  align-items: center;
}

/deep/.u-border-bottom::after {
  border-bottom: none;
}

.pick-date {
  width: 192rpx;
  display: flex;
  align-items: center;

  /deep/.u-cell {
    position: relative;
  }

  /deep/.u-cell__value {
    font-size: 30rpx !important;
  }

  /deep/.u-cell__left-icon-wrap {
    position: absolute;
    right: 0;
    margin-right: 0px !important;
  }

  /deep/.u-icon__icon {
    font-size: 25rpx !important;
    color: #999999;
  }
}

.pick-date-two {
  // flex: 1;
}

/deep/.u-cell {
  padding: 0 0 !important;
  line-height: 80rpx !important;
}

/deep/.u-cell__value {
  color: #333 !important;
  text-align: left !important;
  font-size: 30rpx !important;
}

.weui-cells {
  padding-top: 0;
}

.weui-cell {
  padding: 0rpx 30rpx;
}

.weui-cells::before {
  border: 0;
}

.weui-label {
  width: 180rpx;
  height: 96rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 96rpx;
}

.weui-cell_picker .weui-picker-value {
  text-align: left;
  height: 96rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #cacaca;
  line-height: 96rpx;
}

.weui-input,
.weui-cell__value {
  text-align: left;
  height: 30rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #cacaca;
  line-height: 30rpx;
}

.weui-input,
.weui-cell__value {
  text-align: left;
  height: 30rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #cacaca;
  line-height: 30rpx;
}

.search-btn {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  margin-top: 1rpx;
}

.weui-btn {
  flex: 1;
  margin-top: 0;
  // margin-right: 20rpx;
  background-color: #0066e9;
}

// .weui-btn:last-child {
// 	margin-right: 0;
// }

.fixed-top {
  position: fixed;
  width: 100%;
  left: 0;
  right: 0;
  top: 0;
  // bottom: 0;
  overflow: hidden;
  z-index: 10;
  background-color: #f3f3f3;
}

.scroll-box {
  // padding-top: 358rpx;
  // padding-bottom: 20rpx;
  padding: 20rpx 20rpx 0;
  // height: calc(100% - 370rpx);
  // overflow-y: scroll;
}

.apply-record {
  // 	width: 150rpx;
  // 	margin-left: 10rpx;
  color: #01c1b2;
  border: 1rpx solid #01c1b2;
  background: transparent;
}

.weui-title__decoration:before {
  content: " ";
  position: absolute;
  left: 0rpx;
  top: 50%;
  width: 8rpx;
  height: 30rpx;
  background: #333333;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-radius: 4rpx;
  background: #333333;
}

.no-data {
  width: calc(100% - 40rpx);
  text-align: center;
  background: #fff;
  margin-left: 20rpx;
  margin-top: 20rpx;
  border-radius: 11rpx;
  height: 430rpx;

  .no-data-img {
    width: 248rpx;
    height: 269rpx;
    background-size: 100%;
    margin-top: 62rpx;
  }

  .no-data-title {
    height: 40rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #333333;
    line-height: 40rpx;
  }
}
</style>