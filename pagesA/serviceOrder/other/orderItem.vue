<template>
  <view class="order-item_wrapper">
    <view class="order-item" @click="getRedirectUrl">
      <view class="item-title">
        {{ orderListData.carNumber }}【{{ vehicleColorStr }}牌】<text
          class="is-trunk"
        ></text>
      </view>
      <view class="item-container">
        <view class="item-bd">
          <view class="item-label">
            订单类型：
          </view>
          <view class="item-value">
            {{ modelNameObj[orderListData.modelKey] }}
          </view>
        </view>
        <view class="item-bd">
          <view class="item-label">
            订单编号：
          </view>
          <view class="item-value">
            {{ orderListData.workflowId }}
          </view>
        </view>
        <view class="item-bd">
          <view class="item-label">
            提交时间：
          </view>
          <view class="item-value">
            {{ orderListData.createTime }}
          </view>
        </view>
      </view>
      <view
        class="item-status"
        :class="orderListData.status == '完结' ? 'success' : 'warnning'"
      >
        {{ orderListData.status }}
      </view>
      <view class="btn-container">
        <view class="left">
          <view
            @click.stop="getRedirectUrl"
            class="item-btn info"
            style="width: 140rpx;"
          >
            详情
          </view>
        </view>
      </view>
    </view>

    <tLoading :isShow="isLoading" />
  </view>
</template>

<script>
import tLoading from "@/components/common/t-loading.vue";
import {
  getVehicleColor,
  getVehicleType,
  getApplyStatus,
  getCodeStatus
} from "@/common/method/filter.js";
import { statusInfoList } from "@/pagesA/common/optionsData.js";
import {
  getTicket,
  getOpenid,
  getEtcAccountInfo,
  getLoginUserInfo
} from "@/common/storageUtil.js";
export default {
  components: {
    tLoading
  },
  props: {
    orderListData: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      statusInfoList,
      isLoading: false,
      btnListLen: 4,
      modelNameObj: {
        gxetcautosubmitbyc: "提交材料"
      }
    };
  },
  computed: {
    applyStatus() {
      return getApplyStatus(this.orderListData.nodeStatus);
    },
    codeStatus() {
      return getCodeStatus(this.orderListData.nextNodeCode);
    },
    vehicleType() {
      return getVehicleType(this.orderListData.vehicleType);
    },
    vehicleColorStr() {
      console.log(
        this.orderListData.carColor,
        getVehicleColor(this.orderListData.carColor)
      );
      return getVehicleColor(this.orderListData.carColor);
    }
  },
  methods: {
    // 获取跳转链接
    async getRedirectUrl() {
      let params = {
        custMastId: getEtcAccountInfo().custMastId,
        netUserId: getLoginUserInfo().userIdStr,
        openId: getOpenid(),
        ticket: getTicket(),
        targetPage: "gxEtcWorkOrderDetails",
        workflowId: this.orderListData.workflowId
      };
      let res = await this.$request.post(this.$interfaces.customerServiceUrl, {
        data: params
      });
      console.log(res, "gxEtcWorkOrderDetails");
      if (res.code != 200) {
        uni.showModal({
          title: "提示",
          content: "跳转失败：" + res.msg,
          showCancel: false
        });
        return false;
      }
      this.toDetail(res.data);
    },
    toDetail(callcenterVal) {
      this.showMoreBtn = false;
      if (!getTicket()) {
        uni.showModal({
          title: "提示",
          content: "请先登录",
          success: res => {
            if (res.confirm) {
              uni.reLaunch({
                url: "/pagesD/login/p-login"
              });
            }
          }
        });
        return;
      }
      let callcenter = callcenterVal;
      uni.navigateTo({
        url:
          "/pages/uni-webview/uni-webview?ownPath=" +
          encodeURIComponent(callcenter)
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.order-item {
  position: relative;
  // margin: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  font-family: PingFangSC-Medium, PingFang SC;
  // overflow: hidden;
  padding: 6rpx 0rpx 11rpx 0rpx;
}

.item-title {
  position: relative;
  margin: 22rpx 40rpx;
  height: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  line-height: 45rpx;

  &:before {
    content: " ";
    position: absolute;
    left: -39rpx;
    top: 8rpx;
    width: 8rpx;
    height: 30rpx;
    background-color: #333333;
  }
}

.item-title .is-trunk {
  font-size: 28rpx;
}

.item-status {
  position: absolute;
  right: 0;
  top: 0;
  // width: 169rpx;
  padding: 0 16rpx;
  height: 63rpx;
  border-radius: 0rpx 10rpx 0rpx 30rpx;
  text-align: center;
  line-height: 63rpx;
  font-size: 26rpx;
}

// 待支付，已取消，已退货退款，已退货不退款
.item-status.info {
  background: rgba(133, 134, 134, 0.15);
  color: #6a6969;
}

// 设备已发货，审核通过，已完结，已签收，换货审核通过，退货审核通过，设备已寄回
.item-status.success {
  background: rgba(0, 189, 50, 0.11);
  color: #00bd32;
}

// 换货审核中，退货审核中，待取货
.item-status.warnning {
  background: rgba(255, 145, 0, 0.14);
  color: #ff9100;
}

// 后台审核中
.item-status.primary {
  background: rgba(0, 102, 233, 0.12);
  color: #0066e9;
}

// 审核不通过，换货审核不通过，退货审核不通过
.item-status.error {
  background: rgba(255, 84, 84, 0.15);
  color: #ff5454;
}

.item-container {
  margin: 38rpx 40rpx 30rpx 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx dashed #c3c3c3;
}

.item-bd {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-label {
  width: 160rpx;
  font-size: 30rpx;
  font-weight: 400;
  color: #999999;
}

.item-value {
  flex: 1;
  font-size: 30rpx;
  font-weight: 400;
  color: #333333;
}

.btn-container {
  display: flex;
  justify-content: space-between;
  margin: 20rpx 40rpx;
}

.left {
}

.right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.right .more-btn {
  width: 88rpx;
  position: relative;

  .arrow-top {
    position: absolute;
    top: 60rpx;
    left: 24rpx;

    .triangle {
      position: relative;
      width: 40rpx;
      height: 20rpx;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #ffffff;
        transform-origin: left bottom;
        box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
        transform: rotate(45deg);
        z-index: 2;
      }
    }
  }

  .more-list {
    width: 204rpx;
    position: absolute;
    top: 40px;
    left: 0;
    background-color: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  .more-list__item {
    margin: 0 20rpx;
    height: 92rpx;
    line-height: 92rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    text-align: left;
    border-bottom: 1rpx solid #f2f2f2;

    &:last-of-type {
      border-bottom: none;
    }
  }
}

.right .item-btn {
  margin-left: 20rpx;

  &:nth-child(2n) {
    margin-bottom: 15rpx;
  }
}

.right .item-btn:first-child {
  margin-left: 0;
}

.item-btn {
  // padding: 12rpx 40rpx;
  width: 190rpx;
  height: 58rpx;
  line-height: 58rpx;
  border-radius: 36rpx;
  font-size: 26rpx;
  text-align: center;
}

.item-btn.info {
  border: 2rpx solid #e8e8e8;
  color: #323435;
  background: #ffffff;
}

.item-btn.primary {
  border: 2rpx solid #0066e9;
  color: #ffffff;
  background: #0066e9;
}
</style>