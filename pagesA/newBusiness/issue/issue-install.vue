<template>
	<view class="issue-install">
		<handle-step :current="0" />
		<view class="section">
			<view class="padding-30 title"  v-if="businessSource == 2">
				一、请将设备背面的残留背胶更换为新的背胶（推荐3M双面胶），并撕开背胶保护膜。
			</view>
			<view class="padding-30 title" v-else>
				一、请撕开ETC设备的背胶保护膜。
			</view>
			<view class="img">
				<image style="width: 650rpx;height: 367rpx;"
					src="../../static/new-apply/issue/pic_obu_install_step1.png" mode=""></image>
			</view>
		</view>

		<view class="section" style="padding-bottom: 20rpx;">
			<view class="padding-30 title">
				二、将设备紧贴在车前挡风玻璃并确保设备粘贴牢固
			</view>
			<view class="content">
				小型客车、小型货车<text class="text">粘贴在挡风玻璃</text>上沿
			</view>
			<view class="img" style="padding-bottom: 40rpx;border-bottom: 1rpx dashed #DAD7D7;margin: 0 30rpx;">
				<image style="width: 650rpx;height: 367rpx;"
					src="../../static/new-apply/issue/pic_obu_install_step2.png" mode=""></image>
			</view>
			<view class="content" style="margin-top: 32rpx;">
				大型客车、大型货车<text class="text">粘贴在挡风玻璃</text>下沿
			</view>
			<view class="img">
				<image style="width: 650rpx;height: 367rpx;"
					src="../../static/new-apply/issue/pic_obu_install_step3.png" mode=""></image>
			</view>
			<view class="padding-30 title" style="text-align: center;font-weight: bold;color:#F65B5B ;">
				禁止将设备安装在其他车辆上
			</view>
		</view>
		<view class="agree-wrapper" @click="agree">
			<image v-if="!checked" style="width: 35rpx;height: 35rpx;" src="../../static/new-apply/vehicle/check.png"
				mode=""></image>
			<image v-if="checked" style="width: 35rpx;height: 35rpx;" src="../../static/new-apply/vehicle/checked.png"
				mode="">
			</image>
			<view class="desc">
				已将设备紧贴在挡风玻璃上
			</view>
		</view>
		<tButton :buttonList="buttonList" @toNext="toNext"></tButton>
		<u-modal v-model="uShow" :content="content" :show-cancel-button="false"></u-modal>
	</view>
</template>

<script>
	import handleStep from "@/pagesA/components/new-handle-step/issue-handle-step.vue"
	import tButton from "@/pagesA/components/t-button/t-button.vue"

	export default {
		components: {
			handleStep,
			tButton
		},
		data() {
			return {
				uShow: false,
				checked: false,
				content: '请先勾选已将设备紧贴在挡风玻璃上',
				buttonList: [{
					title: '下一步',
					handle: 'toNext'
				}],
				businessSource: null //办理类型
			}
		},
		onLoad() {
			this.init();
		},
		methods: {
			init() {
				const vehicleInfo = this.$store.getters.issueVehicleInfo

				this.businessSource = vehicleInfo.businessSource || 1
			},
			agree() {
				this.checked = !this.checked
			},
			toNext() {
				if (!this.checked) {
					this.uShow = true
					return;
				}
				// uni.navigateTo({
				// 	url: '/pagesA/newBusiness/issue/issue-confirm'
				// })
				uni.navigateTo({
					url: '/pagesA/newBusiness/issue/issue-upload'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.padding-30 {
		padding: 30rpx;
	}

	.issue-install {
		padding-top: 200rpx;
		padding-bottom: 160rpx;
	}

	.section {
		margin: 0 20rpx 20rpx 20rpx;
		padding-bottom: 58rpx;
		background: #FFFFFF;
		border-radius: 10rpx;
	}


	.title {
		padding-bottom: 20rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 50rpx;

	}

	.img {
		text-align: center;
	}

	.content {
		padding: 0 30rpx 30rpx 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #F65B5B;
		line-height: 40rpx;
	}

	.text {
		color: #333333;
	}

	.agree-wrapper {
		margin-top: 38rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		.desc {
			margin-left: 25rpx;
			font-size: 30rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #787878;
			line-height: 42rpx;
		}
	}
</style>