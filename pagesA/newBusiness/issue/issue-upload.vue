<template>
  <view class="issue-upload">
    <handle-step :current="1" />
    <tLoading :isShow="isLoading" />
    <view class="section padding-30">
      <view class="title-wrapper">
        <view class="title">
          请核对车辆信息
        </view>
        <!-- 上线先屏蔽 -->
        <!-- <view class="question-wrapper">
					<view class="question-text" style="margin-right: 6rpx;">
						激活教程
					</view>
					<image style="width: 28rpx;height: 28rpx;margin-top: 4rpx;"
						src="../../static/new-apply/order/icon-question.png" mode=""></image>
				</view> -->
      </view>
      <view class="car-wrapper">
        <view class="car-bd" v-if="issueVehicleInfo.vehicleCode">
          <view class="car-wrapper__label">
            车牌号码
          </view>
          <view class="car-wrapper__value">
            {{ issueVehicleInfo.vehicleCode }}【{{ vehicleColorStr }}色】
          </view>
        </view>
        <view class="car-bd" v-if="issueVehicleInfo.cardNo">
          <view class="car-wrapper__label">
            ETC卡号
          </view>
          <view class="car-wrapper__value">
            {{ issueVehicleInfo.cardNo }}
          </view>
        </view>
        <view class="car-bd" v-if="issueVehicleInfo.obuNo">
          <view class="car-wrapper__label">
            OBU号
          </view>
          <view class="car-wrapper__value">
            {{ issueVehicleInfo.obuNo }}
          </view>
        </view>
      </view>
    </view>

    <view
      class="section"
      v-if="!(formData.businessSource == 1 || formData.businessSource == 2)"
    >
      <view class="title-wrapper padding-30">
        <view class="title">
          请拍照上传行驶证
        </view>
        <view class="question-wrapper">
          <view class="question-text">
            查看样例
          </view>
        </view>
      </view>
      <view class="upload-car">
        <uploadCar :vehicle="comParams" @on-change="imgChange" />
      </view>
    </view>

    <!-- 请拍照上传ETC设备安装视频 -->
    <view class="section">
      <videoUpload
        title="请拍照上传ETC设备安装视频"
        @uploadSucceed="uploadSucceed"
        @removeVideo="removeVideo"
        :exampUrl="exampUrl"
      >
        <view class="upload-tips">
          <view class="tip-content">
            <text class="tip"> *注意 </text><br />
            <text>
              1.请连贯拍摄已固定安装好的ETC设备、车头(且需看清车牌)、车身信息;
              2.拍摄期间镜头全程不离开车辆、不中断；
              3.视频拍摄时长最长不超过40秒
              </text>
              <br/>
              <text class="tip-red">4.拍摄车身信息时注意周边车辆，确保自身安全</text> 
          </view>
        </view>
      </videoUpload>
    </view>

    <view class="section">
      <view class="title-wrapper padding-30">
        <view class="title">
          请拍照上传45°车头照
        </view>
        <view class="question-wrapper">
          <view class="question-text" @click="preview">
            查看样例
          </view>
        </view>
      </view>
      <view class="upload-car">
        <uploadHead
          ref="upload"
          :vehicle="comParams"
          @on-change="imgChange"
          @ocr-change="orcChange"
          :sourceType="sourceType"
        />
        <view class="tips-wrapper">
          <view class="tips-title">
            *注意
          </view>
          <view class="tips">
            <view class="">
              1.照片必须清楚识别车牌号及车牌颜色
            </view>
            <view class="">
              2.必须看清一侧车轮数量
            </view>
            <view class="">
              3.请确认上传的图片信息真实有效
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- <view class="section">
      <view class="title-wrapper padding-30">
        <view class="title">
          请拍照上传车身照片
        </view>
        <view class="question-wrapper">
          <view class="question-text" @click="previewBody">
            查看样例
          </view>
        </view>
      </view>
      <view class="upload-car">
        <uploadBody
          ref="upload"
          :vehicle="comParams"
          @on-change="imgChange"
          :sourceType="sourceType"
        />
        <view class="tips-wrapper">
          <view class="tips-title">
            *注意
          </view>
          <view class="tips">
            <view class="">
              1.必须看清一侧车轮数量
            </view>
            <view class="">
              2.请确认上传的图片信息真实有效
            </view>
          </view>
        </view>
      </view>
    </view> -->
    <tButton :buttonList="buttonList" @toNext="toNext"></tButton>
    <tLoading :isLoading="isLoading"></tLoading>
    <!--  #ifdef MP-WEIXIN -->
    <privacyDialog></privacyDialog>
    <!--  #endif -->
  </view>
</template>

<script>
import handleStep from "@/pagesA/components/new-handle-step/issue-handle-step.vue";
import tLoading from "@/components/common/t-loading.vue";
import uploadCar from "@/pagesA/components/upload/uploadCar";
import uploadHead from "@/pagesA/components/upload/uploadHead";
import uploadBody from "@/pagesA/components/upload/uploadBody";
import tButton from "@/pagesA/components/t-button/t-button.vue";
import { getVehicleColor } from "@/common/method/filter.js";
import { getLoginUserInfo } from "@/common/storageUtil.js";
import { previewFile } from "@/pagesA/common/method.js";
import videoUpload from "@/pagesA/ali-video/videoUpload";

export default {
  components: {
    handleStep,
    tLoading,
    uploadCar,
    uploadHead,
    uploadBody,
    tButton,
    videoUpload
  },
  data() {
    return {
      isLoading: false,
      buttonList: [
        {
          title: "下一步",
          handle: "toNext"
        }
      ],
      comParams: {
        customer_id: "",
        vehicle_code: "",
        vehicle_color: ""
      },
      sourceType: 2, // 0:相册和摄像机 1:本地相册  2:摄像机
      fileList: [
        {
          photo_code: "3",
          label: "行驶证正页",
          file_url: "",
          file_serial: "",
          key: "drivingLicenseFrontUrl"
        },
        {
          photo_code: "12",
          label: "行驶证副页",
          file_url: "",
          file_serial: "",
          key: "drivingLicenseSubpageUrl"
        },
        {
          photo_code: "15",
          label: "车头外观",
          file_url: "",
          file_serial: "",
          key: "frontPhotoUrl"
        },
        {
          photo_code: "16",
          label: "车身照片",
          file_url: "",
          file_serial: "",
          key: "bodyPhotoUrl"
        }
      ],
      formData: {
        activateStatus: 0, //0-未激活，1-激活中，2-激活成功，3-激活失败
        businessSource: 1, //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
        carColor: "",
        carNo: "",
        cardNo: "",
        custMastId: 0,
        drivingLicenseFrontUrl: "",
        drivingLicenseSubpageUrl: "",
        failureReason: "",
        frontPhotoUrl: "",
        bodyPhotoUrl: "",
        netUserNo: "",
        obuNo: "",
        orderId: ""
      },
      exampUrl:
        "https://portal.gxetc.com.cn/public-static/file/activate_video_template.mp4"
    };
  },
  computed: {
    vehicleColorStr() {
      return getVehicleColor(this.issueVehicleInfo.vehicleColor);
    },
    issueVehicleInfo() {
      let vehicleInfo = this.$store.getters.issueVehicleInfo;
      return Object.keys(vehicleInfo).length ? vehicleInfo : {};
    },
    opBusinessType() {
      // 1-失效重新激活，2-遗失/损毁补办，3-设备故障更换，4-设备注销
      return this.$store.getters.afterSaleBusinessType || 1;
    }
  },
  onLoad(options) {
    this.activationType = options.activationType || this.activationType;
    this.init();
  },
  created() {
    if (process.env.NODE_ENV === "development") {
      this.sourceType = 0;
    }
  },
  methods: {
    init() {
      const vehicleInfo = this.$store.getters.issueVehicleInfo;
	  if(!vehicleInfo.orderId){
		  uni.showModal({
		  	title: '提示',
		  	content: '未获取到订单id，返回首页重新进入订单',
		  	confirmText: '确定',
		  	showCancel: false,
		  	success: function(res) {
		  		if (res.confirm) {
		  			uni.reLaunch({
		  				url:'/pages/home/<USER>/p-home'
		  			})
		  		}
		  	}
		  });
		  return;
	  }
      this.comParams.customer_id = vehicleInfo.customerId;
      this.comParams.vehicle_code = vehicleInfo.vehicleCode;
      this.comParams.vehicle_color = vehicleInfo.vehicleColor;
      this.comParams.vehicle_cbusinessSourceolor = vehicleInfo.vehicleColor;
      this.comParams.businessSource = vehicleInfo.businessSource || 1;
      this.formData.businessSource = vehicleInfo.businessSource || 1;
      this.formData.carColor = vehicleInfo.vehicleColor;
      this.formData.carNo = vehicleInfo.vehicleCode;
      this.formData.cardNo = vehicleInfo.cardNo || "";
      this.formData.obuNo = vehicleInfo.obuNo || "";
      this.formData.orderId = vehicleInfo.orderId || "";
      this.formData.custMastId = vehicleInfo.customerId || "";
      this.formData.netUserNo = getLoginUserInfo().userNo;
    },
    imgChange(item) {
      console.log(item,'------imgChange')
      for (let i = 0; i < this.fileList.length; i++) {
        if (item.photo_code == this.fileList[i].photo_code) {
          this.fileList[i].file_url = item.file_url;
          this.fileList[i].code = item.code;
          //照片id
          this.fileList[i].file_serial = item.file_serial;
          let key = this.fileList[i].key;
          if (key in this.formData) {
            console.log(key,'key')
            // this.formData[key] = this.fileList[i].file_url;
            this.formData[key] = item.code;
            console.log(this.formData,'this.formData')

          }
        }
      }
    },
    orcChange(ocrData) {},
    // 验证参数
    vaildForm() {
      // 更换补办行驶证必传
      if (
        this.formData.businessSource == 3 ||
        this.formData.businessSource == 4
      ) {
        if (
          !this.formData.drivingLicenseFrontUrl ||
          !this.formData.drivingLicenseSubpageUrl
        ) {
          uni.showModal({
            title: "提示",
            content: "请拍照上传行驶证",
            showCancel: false
          });
          return;
        }
      }
      if (!this.formData.videoId) {
        uni.showModal({
          title: "提示",
          content: "请拍照上传ETC设备安装视频",
          showCancel: false
        });
        return;
      }
      // 新办发行和二次激活校验 车头照片上传
      if (!this.formData.frontPhotoUrl) {
        uni.showModal({
          title: "提示",
          content: "请拍照上传45°车头照",
          showCancel: false
        });
        return;
      }
      // if (!this.formData.bodyPhotoUrl) {
      //   uni.showModal({
      //     title: "提示",
      //     content: "请拍照车身照片",
      //     showCancel: false
      //   });
      //   return;
      // }

      return true;
    },
    toNext() {
      console.log(this.formData);
      if (!this.vaildForm()) return;
      if (this.isLoading) return;
      this.isLoading = true;
      this.$request
        .post(this.$interfaces.addActivateRecord, {
          data: this.formData
        })
        .then(res => {
          this.isLoading = false;
          if (res.code == 200 && res.data) {
            const vehicleInfo = this.$store.getters.issueVehicleInfo;

            let fileSerial = this.formatSeiral();

            this.$store.dispatch("setIssueVehicleInfo", {
              ...vehicleInfo,
              issueApplyId: res.data.id,
              ...fileSerial //车头照与车身照行驶证id
            });

            uni.navigateTo({
              url: "/pagesA/newBusiness/issue/issue-confirm"
            });
          } else {
            uni.showModal({
              title: "提示",
              content: res.msg,
              showCancel: false
            });
          }
        })
        .catch(error => {
          this.isLoading = false;
          uni.showModal({
            title: "提示",
            content: error.msg,
            showCancel: false
          });
        });
    },
    formatSeiral() {
      let fileSerial = {
        headFileSerial: "",
        bodyFileSerial: "",
        drivingLicenseFrontSerial: "",
        drivingLicenseSubpageSerial: ""
      };
      console.log("this.fileList", this.fileList);
      for (let i = 0; i < this.fileList.length; i++) {
        if (this.fileList[i].photo_code == "15") {
          //车头照
          fileSerial.headFileSerial = this.fileList[i].file_serial;
        } else if (this.fileList[i].photo_code == "16") {
          //车身照
          fileSerial.bodyFileSerial = this.fileList[i].file_serial;
        } else if (this.fileList[i].photo_code == "3") {
          //行驶证正页
          fileSerial.drivingLicenseFrontSerial = this.fileList[i].file_serial;
        } else if (this.fileList[i].photo_code == "12") {
          //行驶证副页
          fileSerial.drivingLicenseSubpageSerial = this.fileList[i].file_serial;
        }
      }

      return fileSerial;
    },
    uploadSucceed(info) {
      console.log(info, "page");
      this.formData.videoId = info.videoId;
    },
    removeVideo() {
      console.log("removeVideo", "page");
      this.formData.videoId = "";
    },
    // webview跳转
    preview() {
      let url =
        "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/etc/车头照样例.png";
      previewFile(url);
    },
    previewBody() {
      let url =
        "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/etc/车身样例.png";
      previewFile(url);
    }
  }
};
</script>

<style lang="scss" scoped>
.padding-30 {
  padding: 30rpx;
}

.issue-upload {
  padding-top: 200rpx;
  padding-bottom: 160rpx;
}

.section {
  background: #ffffff;
  border-radius: 10rpx;
  margin: 0 20rpx 20rpx 20rpx;
}

.title-wrapper {
  display: flex;
  justify-content: space-between;

  .title {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: bold;
    color: #333333;
  }

  .question-wrapper {
    .question-text {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #0081ff;
    }
  }
}

.question-wrapper {
  display: flex;
}

.car-wrapper {
  margin-top: 35rpx;
  overflow: hidden;
}

.car-bd {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.car-wrapper__label {
  flex: 0 0 175rpx;
  width: 175rpx;
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
}

.car-wrapper__value {
  flex: 1;
}

.upload-car {
  display: flex;

  .tips-wrapper {
    margin-right: 30rpx;
    padding: 10rpx 18rpx;
    width: 324rpx;
    height: 183rpx;
    background: #f8f8f8;
    border-radius: 8rpx;

    .tips-title {
      font-size: 18rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ff9100;
      // line-height: 38rpx;
    }

    .tips {
      font-size: 18rpx;
      line-height: 34rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #888888;
      // line-height: 38rpx;
    }
  }
}

.upload-tips {
  padding: 0 30rpx 30rpx 30rpx;
  background: #fff;
  color: #888888;
  .tip-content {
    line-height: 48rpx;
    padding: 18rpx;
    border-radius: 8rpx;
    background: #f8f8f8;
    .tip {
      color: #ff9100;
    }
    .tip-red{
      color: #FF5454;
    }
  }
}
</style>