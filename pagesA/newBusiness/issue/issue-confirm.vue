<template>
	<view class="issue-install">
		<handle-step :current="2" />
		<view class="section">
			<view class="padding-30 title">
				一、打开手机蓝牙，并关闭其他蓝牙设备确保手机连接蓝牙时不受干扰
			</view>
			<view class="img">
				<image style="width: 650rpx;height: 367rpx;" src="../../static/new-apply/issue/blue-device1.png"
					mode=""></image>
			</view>
		</view>

		<view class="section" style="padding-bottom: 20rpx;">
			<view class="padding-30 title" v-if="businessSource == 1">
				二、快速连续拔插ETC卡两次，更多蓝牙打开方式请点击蓝牙打开方式
			</view>
			<view class="padding-30 title" v-else>
				二、快速连续拔插ETC卡两次，更多蓝牙打开方式请点击蓝牙打开方式 (部分品牌设备需按压侧边按钮)
			</view>
			<view class="question-wrapper">
				<view class="question-text" @click="checkBluetooth">
					蓝牙打开方式
				</view>
				<image class="question-img" src="../../static/new-apply/order/icon-question.png" mode=""></image>
			</view>
			<view class="img">
				<image style="width: 650rpx;height: 367rpx;" src="../../static/new-apply/issue/blue-device2.png"
					mode=""></image>
			</view>
			<view class="title" style="padding: 30rpx 30rpx 0 30rpx;">
				1、设备屏幕显示"蓝牙开启"，并且指示灯开始闪
			</view>
			<view class="title" style="padding: 0rpx 30rpx 10rpx 30rpx;">
				2、请确保ETC卡已完全插入设备中
			</view>
		</view>
		<view class="agree-wrapper" @click="agree">
			<image v-if="!checked" style="width: 35rpx;height: 35rpx;" src="../../static/new-apply/vehicle/check.png"
				mode=""></image>
			<image v-if="checked" style="width: 35rpx;height: 35rpx;" src="../../static/new-apply/vehicle/checked.png"
				mode="">
			</image>
			<view class="desc">
				已打开手机及ETC设备蓝牙
			</view>
		</view>
		<tButton :buttonList="buttonList" @toNext="toNext"></tButton>
		<u-modal v-model="uShow" :content="content" :show-cancel-button="false"></u-modal>
	</view>
</template>

<script>
	import handleStep from "@/pagesA/components/new-handle-step/issue-handle-step.vue"
	import tButton from "@/pagesA/components/t-button/t-button.vue"

	export default {
		components: {
			handleStep,
			tButton
		},
		data() {
			return {
				uShow: false,
				checked: false,
				content: '请先勾选已打开手机及ETC设备蓝牙',
				buttonList: [{
					title: '连接蓝牙，开始激活',
					handle: 'toNext'
				}],
				businessSource: null, //办理类型
				deviceType: '' //更换类型
			}
		},
		onLoad() {
			this.init();
		},
		methods: {
			init() {
				const vehicleInfo = this.$store.getters.issueVehicleInfo
				this.deviceType = vehicleInfo.deviceType
				this.businessSource = vehicleInfo.businessSource || 1
			},
			agree() {
				this.checked = !this.checked
			},
			checkBluetooth() {
				let url =
					'https://mp.weixin.qq.com/s?__biz=MzAxNjUxNDYwNg==&mid=2662548045&idx=1&sn=780e51d1bbeccf23d3ec8dde2e502aa7&chksm=80b4490cb7c3c01a13ac2b109b7d52fe4178c6af9c5810b8c26ea837c7ecc73e9fb1ddd96ec3#rd'
				uni.navigateTo({
					url: '/pages/uni-webview/h5-webview?ownPath=' + encodeURIComponent(url)
				})
			},
			toNext() {
				if (!this.checked) {
					this.uShow = true
					return
				}
				//新办和二次激活去新办激活页面,新增更换类型无需激活去二次激活流程
				if (this.businessSource == 1 || this.businessSource == 2 || (this.businessSource == 3 && this.deviceType ==
						'3')) {
					uni.navigateTo({
						url: '/pagesA/newBusiness/issue/issue'
					})
				} else if (this.businessSource == 3 || this.businessSource == 4) {
					//更换和补办去更换激活页面
					uni.navigateTo({
						url: '/pagesA/newBusiness/issue/changeIssue'
					})
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	.padding-30 {
		padding: 30rpx;
	}

	.issue-install {
		padding-top: 200rpx;
		padding-bottom: 160rpx;
	}

	.section {
		margin: 0 20rpx 20rpx 20rpx;
		padding-bottom: 58rpx;
		background: #FFFFFF;
		border-radius: 10rpx;
	}


	.title {
		padding-bottom: 20rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 50rpx;

	}

	.img {
		text-align: center;
	}

	.content {
		padding: 0 30rpx 30rpx 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #F65B5B;
		line-height: 40rpx;
	}

	.text {
		color: #333333;
	}

	.question-wrapper {
		margin: 0 30rpx 30rpx 30rpx;
		display: flex;
		align-items: center;

		.question-text {
			font-size: 30rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #0081FF;
			line-height: 40rpx;
		}

		.question-img {
			height: 32rpx;
			width: 32rpx;
			margin-left: 4rpx;
		}
	}

	.agree-wrapper {
		margin-top: 38rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		.desc {
			margin-left: 25rpx;
			font-size: 30rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #787878;
			line-height: 42rpx;
		}
	}
</style>