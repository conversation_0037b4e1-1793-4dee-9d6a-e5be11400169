<!-- ETC办理流程 二次激活和一键发行页面 -->
<template>
	<view class="issue">
		<handle-step :current="3" />
		<view class="section">
			<image style="width: 650rpx;height: 434rpx;text-align: center;"
				src="../../static/new-apply/issue/connecting.gif" mode="">
			</image>
			<view class="connect-container" v-if="!isSucess && !isFail">
				<view class="connect-text">
					{{connectingText}}，请勿离开此页面
				</view>
				<view class="progress">
					<u-line-progress :active-color="percentColor" :striped="true" :percent="percent"
						:striped-active="true"></u-line-progress>
				</view>
			</view>
			<view class="fail-conrainer" v-if="isFail">
				<view class="fail-text">
					激活失败
				</view>
				<view class="fail-reason">
					<view class="title">
						失败原因：
					</view>
					<view class="reason">
						{{failText}}
					</view>
				</view>
				<view class="address" style="color: #0081FF;" @click="toAddress">
					点击查询ETC服务网点地址
				</view>
			</view>
			<view class="success-conrainer" v-if="isSucess">
				<view class="success-text">
					激活成功
				</view>
				<view class="success-reason">
					<view class="reason">
						激活后请勿移动ETC设备，否则无法正常使用
					</view>
				</view>
			</view>
		</view>
		<tButton v-if="isBtnShow" :buttonList="buttonList" @issue="issue" @reIssue="reIssue"
			@redirectHandle='redirectHandle'></tButton>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tButton from "@/pagesA/components/t-button/t-button.vue"
	import tLoading from "@/components/common/t-loading.vue";
	import bleapi from "@/common/bluetooth/bleUtil.js";
	import handleStep from "@/pagesA/components/new-handle-step/issue-handle-step.vue"
	import {
		facapi
	} from "@/common/bluetooth/facUtil.js";

	import {
		vehicleColors,
	} from "@/common/const/optionData.js";

	import float from '@/common/method/float.js';
	const defaultRand = "00000000";
	const defaultObuId = "0000000000000000";
	var _that;
	//蓝牙服务名前缀
	import Vue from 'vue';
	const SERVICENAME_PREFIX = Vue.prototype.$serviceName;	
	import { initLoadMixin } from '@/common/mixins/initLoadMixin.js';
	export default {
		mixins: [initLoadMixin], 
		components: {
			tLoading,
			tButton,
			handleStep
		},
		data() {
			return {
				vehicleColors,
				isBtnShow: false,
				isConnect: false,
				isFail: false,
				isSucess: false,
				percent: 0,
				percentColor: '#0081FF',
				connectingText: '开始连接设备',
				failText: '',
				buttonList: [],
				buttonList1: [{
					title: '返回首页',
					handle: 'redirectHandle'
				}, {
					title: '立即重试',
					handle: 'reIssue'
				}],
				buttonList2: [{
					title: '回到首页',
					handle: 'redirectHandle'
				}],
				applyId: '',
				cpuId: '',
				obuId: '',
				issueCode: '',
				issueTimes: '',
				comParams: {
					customerId: '',
					vehicleCode: '',
					vehicleColor: '',
				},
				gxCardType: '', //广西卡类型
				businessSource: null, //办理类型
				redirectUri: '/pages/home/<USER>/p-home',
				bodyFileSerial: '', //车身照片id
				headFileSerial: '', //车头照片id
				drivingLicenseFrontSerial: '', //行驶证正页id
				drivingLicenseSubpageSerial: '', //行驶证副页id
				benefitServiceFee: '', //权益费
				deviceType: '', //更换补办设备类型
				sourceType: '0', //0新办1抖音

			}
		},
		watch: {
			isSucess(val) {
				console.log(val, '++++-----------')
			}
		},
		computed: {
			issueVehicleInfo() {
				let vehicleInfo = this.$store.getters.issueVehicleInfo;
				return Object.keys(vehicleInfo).length ? vehicleInfo : {}
			},
			opBusinessType() {
				// 1-线上发行，2-二次激活，3-设备更换，4-设备补办
				return this.issueVehicleInfo.businessSource || '';
			}
		},
		created() {
			_that = this
			let vehicleInfo = this.issueVehicleInfo;
			console.log('issuevehicleInfo', vehicleInfo)
			//从车辆缓存拿车辆想信息
			this.comParams.customerId = vehicleInfo.customerId
			this.comParams.vehicleCode = vehicleInfo.vehicleCode
			this.comParams.vehicleColor = vehicleInfo.vehicleColor
			this.gxCardType = vehicleInfo.gxCardType
			this.deviceType = vehicleInfo.deviceType
			this.businessSource = vehicleInfo.businessSource || 1
			this.bodyFileSerial = vehicleInfo.bodyFileSerial
			this.headFileSerial = vehicleInfo.headFileSerial
			this.drivingLicenseFrontSerial = vehicleInfo.drivingLicenseFrontSerial
			this.drivingLicenseSubpageSerial = vehicleInfo.drivingLicenseSubpageSerial
			this.benefitServiceFee = vehicleInfo.benefitServiceFee //权益服务费
			this.sourceType = vehicleInfo.sourceType
			//加载完面直接调蓝牙
			this.issue();
		},
		onLoad() {

		},
		methods: {
			// 显示弹框
			showModal(data) {
				//隐藏loading
				// _that.isBtnLoader = false;
				// console.log(data.content, 'sdk报错');
				// _that.closeLoading()
				// _that.disConnect()
				//显示弹框
				let obj = {
					...data
				}
				obj = data.showMyContent ? obj : {
					...data,
					content: '操作失败，请打开手机蓝牙，并确认已按步骤完成激活前准备。请将手机靠近设备后重试。(' + data.devResult.code + ':' + data.devResult
						.err_msg + ')'
				}
				uni.showModal({
					...obj,
					showCancel: false,
					success: () => {}
				});
			},
			// 跳转页面
			redirectHandle() {
				this.$store.dispatch('setAfterSaleBusinessType', '');
				this.$store.dispatch('setIssueVehicleInfo', {});
				uni.reLaunch({
					url: this.redirectUri,
				});
			},
			//跳转服务网点
			toAddress() {
				let url = 'https://www.gxetc.com.cn/h5/#/businessOutlets'
				uni.navigateTo({
					url: '/pages/uni-webview/h5-webview?ownPath=' + encodeURIComponent(url)
				})

			},
			reIssue() {
				this.isFail = false
				this.isSucess = false
				this.isBtnShow = false
				// this.issue()
				//回退到蓝牙连接页面  2023/5/11 dwz
				uni.navigateBack()
			},
			issue() {
				console.log('激活开启')
				//点击后，隐藏按钮
				// this.isBtnShow = false

				bleapi.CloseBle((obj) => {
					_that.scanDevice((result) => {
						_that.isConnect = result;
						if (result) {
							_that.activeFlag()
						}
					});
				})
			},
			scanDevice(callback) {
				bleapi.ScanDevice(SERVICENAME_PREFIX, (devResult) => {
					console.log("ScanDevice", devResult);
					if (devResult.code != 0) {
						_that.setFailStatus('2', devResult)
					} else {
						console.log(
							"搜索到设备:" + devResult + " " + devResult.data.device_name
						);
						facapi.ConnectDevice(
							devResult.data,
							function(onDisconnect) {
								console.log("连接回调：", onDisconnect);
							},
							function(result) {
								console.log(result, "result");
								bleapi.StopScanDevice(function(code) {
									console.log("返回数据", code);
								});
								if (result.code == 0) {
									console.log("连接标签设备成功");
									callback(true);
								} else {
									// _that.closeLoading();
									// _that.closeBle()
									// _that.showModal({
									// 	title: "错误",
									// 	devResult: result,
									// 	content: "设备连接失败，请将手机靠近设备后重试。",
									// 	showMyContent: true,
									// });
									_that.setFailStatus('1', '设备连接失败， 请将手机靠近设备后重试。')
									callback(false);
								}
							}
						);
					}
				});
			},
			// // 激活
			activeFlag() {

				this.connectingText = '设备激活中'
				this.connectedCallback(this.isConnect);
				// 60秒连接超时
				// setTimeout(() => {
				// 	if (this.isActive) {
				// 		this.disConnect();
				// 		uni.showModal({
				// 			title: "提示",
				// 			content: "连接超时,请重试",
				// 			showCancel: false,
				// 		});
				// 	}
				// }, 60000);
			},
			connectedCallback(isConnected) {
				console.log("连接成功回调");
				if (isConnected) {
					//连接成功，设置百分比10%
					this.percent = 10;

					facapi.OpenCard((devResult) => {
						if (devResult.code == 0) {
							facapi.GetCardFile15((devResult) => {
								if (devResult.code == 0) {
									this.cpu0015Info = devResult.data;
									console.log("GetCardNo==>>", JSON.stringify(devResult));
									//先获取卡号
									_that.cpuId = devResult.data.cardNo;
									// 业务类型是二次激活 车辆卡签和设备卡签信息一致
									if (this.opBusinessType == '2' || (this.opBusinessType == '3' && this
											.deviceType == '3')) {
										//更换补办，设备类型为无需更换时，需要进行二次激活流程
										if (this.issueVehicleInfo.cardNo ==
											_that.cpuId) {
											_that.openChannel();
										} else {
											_that.setFailStatus('1', '二次激活车辆卡签信息设备信息不一致');
										}

									} else {
										_that.saleOrder('CPU', _that.cpuId, (res) => {
											//卡销售完后，先发卡
											_that.cardIssue()
										})
									}


								} else {
									_that.setFailStatus('2', devResult, '获取卡号失败，')
								}
							});
						} else {
							_that.setFailStatus('2', devResult, '打开卡失败，')
						}
					});
				}
			},
			// 销售下单
			async saleOrder(type, serNum, callback) {
				//TODO 测试环境绕过订单校验
				// if (['桂A8RU63', '桂K73782'].indexOf(this.comParams.vehicleCode)) {
				// 	callback && callback();
				// 	return;
				// }
				let params = {
					goodsType: type == 'CPU' ? '1' : '2', //1-单卡，2-单OBU
					customerId: this.comParams.customerId,
					goodsCode: serNum, //设备序列号
					serType: this.opBusinessType == 1 ? '1' : '2', //1-新发,2-售后
					vehicleNo: this.comParams.vehicleCode,
					vehicleColor: this.comParams.vehicleColor,
					orderSource: '786',
					applyType: this.sourceType
				}
				await this.$request.post(this.$interfaces.saleOrder, {
					data: params
				}).then(res => {
					console.log('测试销售接口=========>>>>>', res)


					if (res.code != 200) {
						// uni.showModal({
						// 	title: '提示',
						// 	content: res.msg,
						// 	showCancel: false
						// });
						// _that.disConnect()
						_that.setFailStatus('1', res.msg)
						return;
					}

					let resData = res.data
					if (resData.result == '1') {
						//下单成功，可发行。
						callback && callback()

					} else {
						// uni.showModal({
						// 	title: '提示',
						// 	content: resData.resultMsg,
						// 	showCancel: false
						// });
						// _that.disConnect()
						_that.setFailStatus('1', resData.resultMsg)
					}
				}).catch(err => {
					console.log('测试err=========>>>>>', err)
					// uni.showModal({
					// 	title: '提示',
					// 	content: err.msg,
					// 	showCancel: false
					// });
					// _that.disConnect()
					_that.setFailStatus('1', err.msg)
				})
			},
			cardIssue() {
				//开始发卡，设置百分比20%
				this.percent = 20;

				this.beforeCardIssue((resType) => {
					if (resType == 'obuIssue') {
						//已发过卡，去发OBU
						this.openChannel()
					} else if (resType == 'cardIssue') {
						//继续发卡，写0016
						this.set0016()
					}
				})
			},

			//卡发行前校验
			beforeCardIssue(callback) {
				let params = {
					...this.comParams,
					// gxCardType: this.gxCardType,
					cpuCardId: this.cpuId,
					applyType: this.sourceType
				};
				this.$request.post(this.$interfaces.beforeCardIssue, {
					data: params
				}).then(res => {
					let result = res.data
					console.log('卡发行前校验=========>>>>>', res)
					this.issueCode = result.issueCode
					if (res.code == 200) {
						if (result.cardType == '22') {

							_that.setFailStatus('1', '储值卡不允许发行')
							return;
						}
						if (result.issueTimes == 1) {
							callback('obuIssue')
						} else {
							callback('cardIssue')
						}
					} else if (res.code == 1008) {
						callback('obuIssue')
					} else {

						_that.setFailStatus('1', res.msg)
					}


				}).catch(err => {

					_that.setFailStatus('1', err.msg)
				})
			},
			// 写0016
			set0016() {
				facapi.SetCardFile0016(
					(rand, callback) => {
						_that.getCpu0016WriteFile(rand, (result) => {
							console.log("|||=0016writeData=>", result);
							callback(result);
						});
					},
					(devResult) => {
						//写0016文件
						console.log("写0016文件结果", devResult);
						_that.setOO15(devResult);
					}
				);
			},
			// 写0015
			setOO15(devResult) {
				console.log("setOO15-devResult=>", devResult);
				if (devResult.code == 0) {
					facapi.SetCardFile0015(
						(rand, callback) => {
							_that.getCpu0015WriteFile(rand, (devResult) => {
								console.log("0015writeData", devResult);
								callback(devResult);
							});
						},
						(devResult) => {
							//写0015文件
							console.log("写0015文件", devResult);
							_that.getBalance(devResult);
						}
					);
				} else {

					_that.setFailStatus('2', devResult, '写0016文件失败，')
				}
			},


			// 0015
			async getCpu0015WriteFile(rand, callback) {
				let data = {

					...this.comParams,
					rand: rand,
					cpuCardId: this.cpuId,
					issueCode: this.issueCode,
				};
				let params = {
					data: data,
				};
				console.log("写0015文件入参", params);
				await this.$request
					.post(this.$interfaces.newIssueWrite0015, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.issueInfo,
						});
					})
					.catch((error) => {
						callback({
							code: 11006,
							err_msg: error,
						});
					});
			},
			// 0016
			async getCpu0016WriteFile(rand, callback) {
				let data = {
					...this.comParams,
					rand: rand,
					cpuCardId: this.cpuId,
					issueCode: this.issueCode,
				};
				// Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("写0016文件入参", params);
				await this.$request
					.post(this.$interfaces.newIssueWrite0016, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.issueInfo,
						});
					})
					.catch((error) => {
						callback({
							code: error.code,
							err_msg: error.msg,
						});
					});
			},

			getBalance(devResult){
				if (devResult.code == 0) {
					_that._getBalance(_that.cardSuccessHandle);
				} else {
					_that.showModal({
						title: "错误",
						devResult: devResult,
						content: "写0015文件失败：" +
							devResult.code +
							":" +
							devResult.err_msg +
							(devResult.msg ? ":" + devResult.msg : ""),
					});
					_that.disConnect();
				}
			},
			cardSuccessHandle(){
				//如果卡内余额不为0，调用发卡完成接口
				this.cardSuccess((res) => {
					if (res.code == "200") {
						//卡发成功，设置百分比40%
						this.percent = 40;
						_that.openChannel();
					} else {
				
						_that.setFailStatus('1', res.msg)
					}
				});
			},
			// 获取卡内余额
			getBalance1(devResult) {
				if (devResult.code == 0) {
					facapi.GetBalance((devResult) => {
						//获取余额成功
						console.log("获取余额成功-devResult", devResult);
						if (devResult.code == 0) {
							if (devResult.data && devResult.data.length == 8) {
								let balance = parseInt(devResult.data, 16);
								console.log("圈存余额balance", balance);
								if (balance == 0) {
									//如果卡内余额为0，调用初始化圈存
									_that.initLoad(devResult);
								} else {
									//如果卡内余额不为0，调用发卡完成接口
									this.cardSuccess((res) => {
										if (res.code == "200") {
											//卡发成功，设置百分比40%
											this.percent = 40;
											_that.openChannel();
										} else {

											_that.setFailStatus('1', res.msg)
										}
									});
								}
							} else {
								let devResult = {
									code: "11013",
									err_msg: "获取卡余额失败：长度不符",
								}
								_that.setFailStatus('1', devResult)

							}
						} else {

							_that.setFailStatus('2', devResult, '获取卡余额失败，')
						}
					});
				} else {
					_that.setFailStatus('2', devResult, '写0016文件失败，')
				}
			},

			//圈存
			initLoad1(devResult) {
				this.issueTimes = this.formatDate(new Date());
				if (devResult.code == 0) {
					console.log("初始化圈存", this.issueTimes, pinCode, money, terminalNo, devResult);
					facapi.InitLoad(
						pinCode,
						money,
						this.issueTimes,
						terminalNo,
						(rand, trade_no, mac1, balance, callback) => {
							console.log("initLoad:getMac", rand, trade_no, mac1, balance);
							_that.getInitLoad(rand, trade_no, balance, mac1, (result) => {
								callback(result);
							});
						},
						(nextDevResult) => {
							//初始化圈存完成成功
							//读卡内余额
							console.log("初始化圈存结果", nextDevResult);
							if (nextDevResult.code != 0) {

								_that.setFailStatus('2', nextDevResult, '圈存初始化失败，')
							} else {
								//圈存成功，调用发卡完成接口

								this.cardSuccess((res) => {
									if (res.code == "200") {
										_that.openChannel();
									} else {

										_that.setFailStatus('1', res.msg)
									}
								});
							}
						}
					);
				} else {

					_that.setFailStatus('2', devResult, '获取卡余额失败，')
				}
			},

			// 卡发行成功
			async cardSuccess(callback) {
				let data = {
					...this.comParams,
					rand: "123456",
					issueCode: this.issueCode,
					cpuCardId: this.cpuId,
				};
				let params = {
					data: data,
				};
				// Object.assign(data, this.comParams);
				console.log("卡发行成功入参", params);
				await this.$request
					.post(this.$interfaces.cardComplete, params)
					.then((res) => {
						console.log("卡发行成功-res", res);
						callback(res);
					})
					.catch((error) => {
						callback(error);
					});
			},

			// 圈存
			async getInitLoad(rand, tradeNo, balance, mac1, callback) {
				let data = {
					...this.comParams,
					rand: rand,
					money: money,
					termId: terminalNo,
					tradeNo: tradeNo,
					decTime: this.issueTimes,
					mac1: mac1,
					cardBalance: balance,
					issueCode: this.issueCode,
					cpuCardId: this.cpuId,
				};
				// Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("初始化圈存入参", params);
				await this.$request
					.post(this.$interfaces.cardInitLoad, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.mac2,
						});
					})
					.catch((error) => {
						callback({
							code: 11003,
							err_msg: error.msg,
						});
					});
			},

			// 打开OBU通道，读取obu号
			openChannel() {
				facapi.OpenChannel((devResult) => {
					console.log("sdk的OpenChannel调用", devResult);
					if (devResult.code == 0) {
						// 读取设备sn号
						facapi.GetSystemInfo((devResult) => {
							console.log("getSerialNo==>>", JSON.stringify(devResult));
							if (devResult.code == 0) {
								_that.obuId = devResult.data.serialNumber
								_that.obuSystemInfo  = devResult.data
								//如果设备有可能有问题读不出序列号，增加失败提示
								if (!_that.obuId) {
									let errMsg = '设备号：' + _that.obuId  + '，无法获取该设备的序列号，请联系客服处理。'
									_that.setFailStatus('1', errMsg);
									return
								}

								if (this.opBusinessType == '2' || (this.opBusinessType == '3' && this
										.deviceType == '3')) {
									//更换补办，设备类型为无需更换时，需要进行二次激活流程
									if (this.issueVehicleInfo.obuNo == _that.obuId ) {
										_that.obuActiveOnly()
									} else {
										_that.setFailStatus('1', '二次激活车辆卡签信息和设备信息不一致');
									}

								} else {
									_that.saleOrder('OBU', _that.obuId, () => {
										//OBU销售完成后，去发OBU
										_that.setSystemInfo(devResult)
									})
								}
							} else {
								_that.setFailStatus('2', devResult, '获取Obu编号失败，')
							}


						});
					} else {
						_that.setFailStatus('2', devResult, '打开Obu失败，')

						_that.getSystemInfo(
							devResult.code,
							devResult.err_msg + (devResult.msg ? ":" + devResult.msg : ""),
							defaultObuId,
							defaultRand,
							(result) => {}
						);
						// _that.disConnect();
					}
				});
			},
			// 获取obu号后进行obu发行前校验
			setSystemInfo(devResult) {
				console.log("|||setSystemInfo==>", JSON.stringify(devResult));
				if (devResult.code == 0) {
					console.log("获取obuid成功", devResult.data);
					// this.obuId = devResult.data;
					if (!devResult.data) {

						_that.setFailStatus('1', '获取不到设备序列号，请联系管理员')
						return
					}
					//OBU发行前校验
					this.obuCheckHandle()

				} else {

					_that.setFailStatus('2', devResult, '获取Obu编号失败，')
				}
			},

			// 校验OBU信息
			obuCheckHandle() {
				//开始发OBU，设置百分比50%
				this.percent = 50;

				this.obuCheck((res) => {
					console.log('obuCheckHandle', res, res && res.code == 200)
					if (res && res.code == 200) {
						// obu已经发行成功，卡发失败，直接发卡
						if (res.data.issue_times == 1) {

						} else {
							_that.obuReaday(res);
						}
					} else if (res.code == 1009) {
						//直接去激活
						_that.obuActiveOnly()
					} else {

						_that.setFailStatus('1', res.msg)
					}
				});
			},

			// obu发行准备完成，准备写卡写签
			obuReaday(devResult) {
				if (devResult.code == 200) {
					console.log("obu发行准备完成", devResult);
					facapi.SetSystemInfo(
						(rand, callback) => {
							console.log("setSystemInfo.getMac", JSON.stringify(devResult));
							_that.getSystemInfo(
								this.obuId,
								devResult.err_msg,
								rand,
								(result) => {
									callback(result);
								}
							);
						},
						(devResult) => {
							console.log("写系统信息结果", devResult);
							_that.setVehicleInfo(devResult);
						}
					);
				} else {

					_that.setFailStatus('2', devResult, 'Obu圈存失败，')
				}
			},
			// 写车辆信息
			setVehicleInfo(devResult) {
				//开始写车辆信息，设置百分比60%
				this.percent = 60;
				if (devResult.code == 0) {
					facapi.SetVehicleInfo(
						(rand, callback) => {
							_that.getVehicleInfo(
								devResult.code,
								devResult.err_msg,
								rand,
								(result) => {
									callback(result);
								}
							);
						},
						(devResult) => {
							console.log("setVehicleInfo：写车辆信息结果：", devResult);
							this.$logger.info('写车辆信息结果：', JSON.stringify(devResult))
							_that.obuResult(devResult);
						}
					);
				} else {
					this.$logger.error('写Obu系统信息失败：', JSON.stringify(devResult))
					_that.setFailStatus('2', devResult, '写Obu系统信息失败，')
				}
			},

			// obu发行完成业务处理结果上报
			obuResult(devResult) {
				if (devResult.code != 0) {
					this.$logger.info('写Obu车辆信息失败：', JSON.stringify(devResult))
					_that.setFailStatus('2', devResult, '写Obu车辆信息失败，')
				} else {
					// obu发行成功
					this.obuIssueComplete((res) => {
						if (res.code == '200') {

							//OBU发行成功，设置百分比80%
							this.percent = 80;

							_that.obuActiveOnly()
						} else {
							this.$logger.error('obu发行完成业务处理结果上报错误', JSON.stringify(res.msg))
							_that.setFailStatus('1', res.msg)
						}
					});
				}
			},
			// OBU发行成功后激活
			obuActiveOnly() {
				facapi.Activate(
					(rand, callback) => {
						_that.obuIssueActivation(rand, devResult => {
							console.log('OBU发行成功后激活+devResult111', devResult)
							if (devResult.code == 0) {
								callback(devResult);
							} else {
								_that.updateActivateRecord(3, devResult.err_msg)
								_that.setFailStatus('2', devResult, '激活失败，')
							}
						});

					},
					devResult => {
						console.log('OBU发行成功后激活', devResult);
						if (devResult.code == 0) {
							_that.obuIssueActivationComplete(devResult)
						} else {
							_that.setFailStatus('2', devResult, '激活失败，')
							_that.updateActivateRecord(3, devResult.err_msg)
						}
					}
				);
			},
			// OBU发行成功后激活请求,OBU激活取密钥
			async obuIssueActivation(rand, callback) {
				let data = {
					vehicleCode: this.comParams.vehicleCode,
					vehicleColor: this.comParams.vehicleColor,
					rand: rand,
					obuId: this.obuId,
					picCL: this.headFileSerial, //车辆车头图片 id
					picCLCS: this.bodyFileSerial, //车辆车身图片 id
					picXSZ: this.drivingLicenseFrontSerial, //行驶证正页id
					picXSZFY: this.drivingLicenseSubpageSerial, //副页id
				};
				let params = {
					data: data,
				};
				console.log("OBU发行成功后激活请求", params);
				await this.$request.post(this.$interfaces.obuIssueActivation, params).then(res => {
					console.log("OBU发行成功后激活请求-res", res);
					let code = res.code == '200' ? 0 : res.code
					callback({
						code: code,
						err_msg: res.msg,
						data: res.data.issueInfo,
					});
				}).catch(error => {
					callback({
						code: 11006,
						err_msg: error,
					});
				})
			},
			//OBU激活上报
			async obuIssueActivationComplete(devResult) {
				let data = {
					vehicleCode: this.comParams.vehicleCode,
					vehicleColor: this.comParams.vehicleColor,
					status: devResult.code,
					obuId: this.obuId,

				}
				console.log('激活成功上报请求参数', data)
				let params = {
					data: data
				}
				await this.$request.post(this.$interfaces.obuIssueActivationComplete, params).then((res) => {
					if (res.code == 200) {
						_that.updateActivateRecord(2, '')
						_that.setSuccesStatus()

					} else {
						_that.updateActivateRecord(3, '激活上报失败')
						_that.setFailStatus('1', '激活上报失败')
					}
				}).catch((error) => {
					_that.updateActivateRecord(3, '激活上报失败')
					_that.setFailStatus('1', '激活上报失败')

				})

			},
			//激活记录更新
			async updateActivateRecord(status, reason = '') {
				let data = {
					activateStatus: status, // 激活状态；0-未激活，1-激活中，2-激活成功，3-激活失败
					failureReason: reason,
					id: this.$store.getters.issueVehicleInfo.issueApplyId || '',
					cardNo: this.cpuId,
					obuNo: this.obuId,
				}
				let params = {
					data: data
				}
				await this.$request.post(this.$interfaces.updateActivateRecord, params).then((res) => {
					if (res.code == 200) {

					} else {

					}
				}).catch((error) => {

				})

			},
			// obu发行成功
			async obuIssueComplete(callback) {
				let data = {
					...this.comParams,
					obuId: this.obuId,
					issueCode: this.issueCode,
				};
				// Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("obu激活成功入参", params);
				this.$logger.info('新办发行调用完成接口入参：', JSON.stringify(params))
				await this.$request
					.post(this.$interfaces.obuWriteComplete, params)
					.then((res) => {
						console.log('obu激活成功res=>>>>>>>', res)
						this.$logger.info('新办发行调用完成接口返回：', JSON.stringify(res))
						callback(res);
					})
					.catch((error) => {
						this.$logger.error('新办发行调用完成接口返回错误：', JSON.stringify(error))
						callback(error);
					});
			},

			// 写车辆信息
			async getVehicleInfo(errCode, errMsg, rand, callback) {
				let data = {
					...this.comParams,
					obu_id: this.obuId,
					rand: rand,
					issue_code: this.issueCode
				};

				let params = {
					data: data
				};
				console.log('写车辆信息参数', JSON.stringify(params));
				await this.$request
					.post(this.$interfaces.obuWriteCarinfo, params)
					.then((res) => {
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.issueInfo
						});
					})
					.catch(error => {
						callback({
							code: 11000,
							err_msg: error.msg
						});
					});
			},


			// OBU写系统信息
			async getSystemInfo(obu_id, errMsg, rand, callback) {
				let data = {
					...this.comParams,
					obuId: this.obuId,
					rand: rand,
					issueCode: this.issueCode,
				};
				// Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("OBU写系统信息", JSON.stringify(params));
				await this.$request
					.post(this.$interfaces.obuWriteSysinfo, params)
					.then((res) => {
						console.log("OBU写系统信息res", JSON.stringify(res));
						let code = res.code == '200' ? 0 : res.code
						callback({
							code: code,
							err_msg: res.msg,
							data: res.data.issueInfo,
						});
					})
					.catch((error) => {
						callback({
							code: 11001,
							err_msg: error.msg,
						});
					});
			},

			// obu发行前校验
			async obuCheck(callback) {
				let data = {
					...this.comParams,
					obuId: this.obuId,
				};
				// Object.assign(data, this.comParams);
				let params = {
					data: data,
				};
				console.log("obu发行前校验", params);
				await this.$request
					.post(this.$interfaces.beforeObuIssue, params)
					.then((res) => {
						console.log("obu发行前校验res", res);
						this.issueCode = res.data.issue_code;
						callback(res);
					})
					.catch((error) => {
						callback(error);
					});
			},




			setFailStatus(reasonType, devResult, selfContent = '') {
				this.isFail = true
				this.isSucess = !this.isFail
				this.isConnect = false
				//显示按钮，换成失败在状态
				this.isBtnShow = true
				this.buttonList = this.buttonList1

				if (reasonType == '1') {
					//自定义
					this.failText = devResult
				}
				if (reasonType == '2' && Object.prototype.toString.call(devResult) === '[object Object]') {
					//SDK错误
					this.failText = selfContent + '状态码【' + devResult.code + '】' + devResult.err_msg
				}
				this.disConnect()
			},
			// 设置激活成功状态
			setSuccesStatus() {
				this.isSucess = true
				this.isFail = !this.isSucess
				this.isConnect = false
				this.isBtnShow = true
				this.buttonList = this.buttonList2
				//发行成功，设置百分比100%
				this.percent = 100;

				this.disConnect()

				if (this.businessSource == 1) {
					if (this.benefitServiceFee == 0) {
						//权益服务费为0不开票
						//新办业务类型直接跳转 2023/4/26 dwz
						if (this.gxCardType == '5') {
							//日日通
							uni.reLaunch({
								url: '/pagesA/orderBusiness/order/orderSuccess?type=13'
							})
						} else if (this.gxCardType == '10') {
							//次次顺
							uni.reLaunch({
								url: '/pagesA/orderBusiness/order/orderSuccess?type=14'
							})
						}
					} else {
						//新办业务类型直接跳转 2023/4/26 dwz
						if (this.gxCardType == '5') {
							//日日通
							uni.reLaunch({
								url: '/pagesA/orderBusiness/order/orderSuccess?type=11'
							})
						} else if (this.gxCardType == '10') {
							//次次顺
							uni.reLaunch({
								url: '/pagesA/orderBusiness/order/orderSuccess?type=6'
							})
						}
					}



				} else if (this.businessSource == 3 || this.businessSource == 4) {
					//日日通
					uni.reLaunch({
						url: '/pagesA/orderBusiness/order/orderSuccess?type=12'
					})
				}

			},
			// 中断或结束断开连接关闭蓝牙
			disConnect() {
				bleapi.closeBLEConnection();
				facapi.facSdk && facapi.facSdk.DisconnectDevice(function(code) {
					console.log("关闭连接结果", code);
				});
				//完成后关闭蓝牙模块
				bleapi.CloseBle((obj) => {
					console.log(obj);
				});
			},

			// 圈存存入当前时间
			formatDate(now) {
				var year = now.getFullYear();
				var month = now.getMonth() + 1 < 10 ? '0' + (now.getMonth() + 1) : now.getMonth() + 1;
				var date = now.getDate() < 10 ? '0' + now.getDate() : now.getDate();
				var hour = now.getHours() < 10 ? '0' + now.getHours() : now.getHours();
				var minute = now.getMinutes() < 10 ? '0' + now.getMinutes() : now.getMinutes();
				var second = now.getSeconds() < 10 ? '0' + now.getSeconds() : now.getSeconds();
				return year + '' + month + '' + date + '' + hour + '' + minute + '' + second;
			}
		}
	}
</script>

<style scoped lang="less">
	.issue {
		padding-top: 200rpx;
		padding-bottom: 160rpx;
	}

	.section {
		padding-top: 30rpx;
		margin: 0 20rpx 20rpx 20rpx;
		padding-bottom: 58rpx;
		background: #FFFFFF;
		border-radius: 10rpx;
		text-align: center;
	}

	.connect-container {
		margin-top: 63rpx;
	}

	.progress {
		margin: 88rpx 70rpx 150rpx 79rpx;
	}

	.fail-conrainer {
		margin-top: 63rpx;

		.fail-text {
			font-size: 40rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #D31C1C;
			line-height: 56rpx;
		}

		.fail-reason {
			text-align: left;
			margin: 52rpx 70rpx 200rpx 79rpx;

			.title {
				font-size: 30rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #333333;
				line-height: 50rpx;
			}

			.reason {
				color: #666666;
				font-size: 30rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				line-height: 50rpx;
			}
		}
	}

	.success-conrainer {
		margin-top: 63rpx;

		.success-text {
			font-size: 40rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #0081FF;
			line-height: 56rpx;
		}

		.success-reason {
			text-align: left;
			margin: 52rpx 70rpx 200rpx 79rpx;

			.reason {
				color: #666666;
				font-size: 30rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				line-height: 50rpx;
			}
		}
	}
</style>