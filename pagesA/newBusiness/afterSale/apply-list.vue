<template>
	<view class="apply-list">
		<view class="order-item" v-for="(item,index) in detailList" :key="index" @click="toDetail(item.id)">
			<view class="item-title">
				售后申请信息
			</view>
			<view class="item-container">
				<view class="item-bd">
					<view class="item-label">
						服务单号：
					</view>
					<view class="item-value">
						{{item.id}}
					</view>
				</view>
				<view class="item-bd">
					<view class="item-label">
						申请时间：
					</view>
					<view class="item-value">
						{{item.createTime}}
					</view>
				</view>
				<view class="item-bd">
					<view class="item-label">
						服务类型：
					</view>
					<view class="item-value" v-if="item.type == '1'">
						换货
					</view>
					<view class="item-value" v-if="item.type == '0'">
						退货
					</view>
				</view>
				<view class="item-bd">
					<view class="item-label">
						申请原因：
					</view>
					<view class="item-value">
						{{item.reason}}
					</view>
				</view>
			</view>
			<view class="item-status" :class="item.isDelete == '0'?'warnning':item.isDeleteStr=='已取消'?'info':'success'">
				{{item.isDeleteStr}}
			</view>
		</view>
	</view>
</template>

<script>
	import tTitle from '@/pagesA/components/t-title/t-title.vue'
	import tLoading from '@/components/common/t-loading.vue';
	export default {
		components: {
			tTitle,
			tLoading
		},
		data() {
			return {
				id: '',
				detailList: []
			}
		},
		onLoad(option) {
			if (option.applyId) {
				this.id = option.applyId
				this.getList()
			}

		},
		onShow() {
			if (this.id) {
				this.getList()
			}
		},
		methods: {
			getList() {
				this.isLoading = true
				let params = {
					id: this.id
				}

				this.$request.post(this.$interfaces.getReturnInfoList, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res', res)
						this.detailList = res.data
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			getDeviceType(deviceType) {
				if (deviceType == '0') {
					return 'ETC卡'
				} else if (deviceType == '1') {
					return 'OBU设备'
				} else if (deviceType == '2') {
					return 'ETC卡、OBU设备'
				}
			},
			toDetail(id) {
				uni.navigateTo({
					url: './apply-detail?saleId=' + id
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-item {
		position: relative;
		margin: 20rpx;
		border-radius: 12rpx;
		background-color: #ffffff;
		font-family: PingFangSC-Medium, PingFang SC;
		overflow: hidden;
	}

	.item-title {
		position: relative;
		margin: 22rpx 40rpx;
		height: 45rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		line-height: 45rpx;

		&:before {
			content: ' ';
			position: absolute;
			left: -39rpx;
			top: 8rpx;
			width: 7rpx;
			height: 30rpx;
			background-color: #333333;
		}
	}

	.item-title .is-trunk {
		font-size: 28rpx;
	}

	.item-status {
		position: absolute;
		right: 0;
		top: 0;
		width: 169rpx;
		height: 63rpx;
		border-radius: 0rpx 10rpx 0rpx 30rpx;
		text-align: center;
		line-height: 63rpx;
		font-size: 26rpx;
	}

	// 待支付，已取消，已退货退款，已退货不退款
	.item-status.info {
		background: rgba(133, 134, 134, 0.15);
		color: #6A6969;
	}

	// 设备已发货，审核通过，已完结，已签收，换货审核通过，退货审核通过，设备已寄回
	.item-status.success {
		background: rgba(0, 189, 50, 0.11);
		color: #00BD32;
	}

	// 换货审核中，退货审核中，待取货
	.item-status.warnning {
		background: rgba(255, 145, 0, 0.14);
		color: #FF9100;
	}

	// 后台审核中
	.item-status.primary {
		background: rgba(0, 102, 233, 0.12);
		color: #0066E9;
	}

	// 审核不通过，换货审核不通过，退货审核不通过
	.item-status.error {
		background: rgba(255, 84, 84, 0.15);
		color: #FF5454;
	}

	.item-container {
		margin: 20rpx 40rpx;
		padding-bottom: 20rpx;
		// border-bottom: 1rpx dashed #C3C3C3;
	}

	.item-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.item-label {
		width: 160rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.item-value {
		flex: 1;
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
	}

	.btn-container {
		display: flex;
		justify-content: space-between;
		margin: 20rpx 40rpx;
	}

	.left {}

	.right {
		flex: 1;
		display: flex;
		justify-content: flex-end;
	}

	.right .item-btn {
		margin-left: 20rpx;
	}

	.right .item-btn:first-child {
		margin-left: 0;
	}

	.item-btn {
		padding: 12rpx 40rpx;
		border-radius: 36rpx;
		font-size: 26rpx;
		text-align: center;
	}

	.item-btn.info {
		border: 2rpx solid #E8E8E8;
		color: #323435;
		background: #FFFFFF;
	}

	.item-btn.primary {
		border: 2rpx solid #0066E9;
		color: #FFFFFF;
		background: #0066E9;
	}
</style>