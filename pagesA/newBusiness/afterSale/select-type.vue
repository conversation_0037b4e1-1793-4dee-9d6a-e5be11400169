<template>
	<view class="select-type">
		<view class="desc-wrapper">
			<view class="desc">
				本次售后服务将由<text style="color:#3874FF;">捷通</text>为您提供
			</view>
		</view>
		<view class="card-warpper">
			<view class="card-item card-selector">
				<image class="img" src="../../static/new-apply/product-select/icon-card_1_check.png"
					v-if="applyData.productType == '5'"></image>
				<image class="img" v-if="applyData.productType == '10'"
					src="../../static/new-apply/product-select/icon-card_2_check.png" mode=""></image>
				<view class="card-text">
					<view class="card-name">
						{{applyData.productType == '5'?'捷通日日通记账卡':'捷通次次顺记账卡'}}
					</view>
					<view class="card-desc">
						{{applyData.productType == '5'?'预存式记账卡，灵活多样的充值方式':'绑定银行卡代扣，先通行后扣费'}}
					</view>
					<view class="info-warpper">
						<view class="info">
							{{applyData.customerName}}
						</view>
						<view class="info">
							{{applyData.vehicleCode}}
						</view>
						<view class="info">
							{{vehicleColorStr}}色
						</view>
					</view>
				</view>
			</view>
			<view class="type-wrapper">
				<view class="type-list">
					<view class="item" @click="change('1')">
						<view class="item-left">
							<image src="../../static/new-apply/after/after_sale1.png" mode=""></image>
							<view class="item-desc">
								换货
							</view>
						</view>
						<view class="item-right">
							<image src="../../static/new-apply/personal/arrow-right.png" mode=""></image>
						</view>
					</view>
					<view class="item" @click="change('0')">
						<view class="item-left">
							<image src="../../static/new-apply/after/after_sale2.png" mode=""></image>
							<view class="item-desc">
								退货
							</view>
						</view>
						<view class="item-right">
							<image src="../../static/new-apply/personal/arrow-right.png" mode=""></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="final-tips">
			<view class="desc">温馨提示：</view>
			<view class="desc" style="margin-top: 10rpx;">1.到货签收7天内，设备没有被使用过，而且包装和其他赠品完好，不影响再次销售，可以申请7天无理由退/换货。</view>
			<view class="desc" style="margin-top: 10rpx;">2.如非质量问题，所产生的邮寄费用需自行承担。</view>
			<view class="desc" style="margin-top: 10rpx;">3.售后申请审核通过后，请务必在7天内将整套设备寄回。</view>
			<view class="desc" style="margin-top: 10rpx;">4.如有其他疑问请联系在线客服。</view>
		</view>
	</view>
</template>

<script>
	import {
		getVehicleColor
	} from '@/common/method/filter.js';
	export default {
		data() {
			return {
				isLoading: false,
				// applyId: '',
				applyData: {}
			}
		},
		computed: {
			vehicleColorStr() {
				return getVehicleColor(this.applyData.vehicleColor);
			},
		},
		onLoad(options) {
			if (options) {
				let data = JSON.parse(decodeURIComponent(options.applyData));
				this.applyData = data
			}
			// console.log('this.$store.state', this.$store)
		},
		methods: {
			change(type) {
				uni.navigateTo({
					url: './apply?type=' + type + '&applyId=' + this.applyData.applyId
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.select-type {
		height: 100%;
		background-color: #F8F8F8;
		padding: 38rpx 20rpx 20rpx 20rpx;
	}

	.desc-wrapper {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 38rpx;
	}

	.desc-wrapper .desc {
		width: 344rpx;
		height: 43rpx;
		line-height: 43rpx;
		font-size: 20rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #888888;
		text-align: center;
		background: rgba(231, 231, 231, 0.5);
		border-radius: 25rpx;
	}

	.card-warpper {
		background-color: $uni-bg-color;
		// margin: 0 20rpx 20rpx 20rpx;
		border-radius: 12rpx;
		padding: 30rpx;

		.card-item {
			// margin: 30rpx;
			display: flex;
			// align-items: center;
			height: 182rpx;
			background: #F8F8F8;
			border-radius: 12rpx;
			padding: 25rpx 33rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;

			&>image {
				margin-right: 28rpx;
				width: 56rpx;
				height: 56rpx;
			}

			.card-text {
				width: 100%;
			}

			.card-name {
				font-size: 28rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #323435;
				margin-bottom: 10rpx;

			}

			.card-desc {
				line-height: 33rpx;
				font-size: 24rpx;
			}

		}

		.card-selector {
			background: #E4EFFF;
			border-radius: 12rpx;
			// border: 2rpx solid #009ff6;
			// border-image: linear-gradient(180deg, rgba(0, 159, 246, 1), rgba(0, 102, 233, 1)) 2 2;
			// clip-path: inset(0 round 12rpx);

			.card-type,
			.card-desc {
				// color: $uni-bg-color;
			}
		}
	}

	.info-warpper {
		display: flex;
		margin-top: 25rpx;
	}

	.info {
		margin-right: 50rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
	}


	.type-wrapper {
		background-color: #ffffff;
		// padding-bottom: 40rpx;
	}

	.type-list {
		margin-top: 38rpx;
		// margin: 0 48rpx;
		// padding: 0 30rpx 30rpx 30rpx;
		// border: 1rpx solid #c7c7c7;
		// border-radius: 18rpx;

		.item {
			height: 96rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1rpx solid #E9E9E9;

			// padding-bottom: 20rpx;
			// margin-top: 50rpx;
			&:last-child {
				border-bottom: 0;
			}

			.item-left {
				display: flex;
				align-items: center;

				.item-desc {
					font-size: 30rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;
				}

				&>image {
					margin-right: 30rpx;
					width: 42rpx;
					height: 40rpx;
				}

			}

			.item-right {
				&>image {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}
	}

	.final-tips {
		margin-top: 30rpx;
		background: #EFEFEF;
		border-radius: 8rpx;
		padding: 30rpx;
		font-size: 24rpx;
		color: #323435;

		.desc {
			line-height: 40rpx;
			// text-indent: 30rpx;

			&:first-child {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FF9038;
			}
		}
	}
</style>