<template>
	<view class="order-info__detail">
		<view class="personal-wrapper" v-if="Object.keys(infoDTO).length > 0">
			<tTitle title="售后信息"></tTitle>
			<view class="personal-bd" style="margin-top: 30rpx;">
				<view class="personal-wrapper__label">
					车牌号
				</view>
				<view class="personal-wrapper__value">
					{{infoDTO.vehicleNo}}【{{vehicleColorStr}}色】
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					售后类型
				</view>
				<view class="personal-wrapper__value">
					{{ infoDTO.type =='1'?'换货':'退货' }}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					申请时间
				</view>
				<view class="personal-wrapper__value">
					{{ infoDTO.createTime }}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					申请原因
				</view>
				<view class="personal-wrapper__value">
					{{ infoDTO.reason }}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					申请方式
				</view>
				<view class="personal-wrapper__value">
					{{ infoDTO.offLine == '1'?'线下' :'线上' }}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					补充说明
				</view>
				<view class="personal-wrapper__value">
					{{infoDTO.remarks || ""}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					申请照片
				</view>
				<view class="personal-wrapper__value">
					<view class="pic-wrapper" @tap="ViewImage(item)" v-for="(item,index) in imgList" :key="index">
						<image class="pic-img" :src="item" mode="aspectFill"></image>
					</view>
				</view>
			</view>
		</view>
		<view class="personal-wrapper">
			<tTitle title="设备退回信息"></tTitle>
			<view class="personal-bd" style="margin-top: 30rpx;">
				<view class="personal-wrapper__label">
					退回方式
				</view>
				<view class="personal-wrapper__value">
					{{infoDTO.routeType=='0'?'上门取件':'自行寄回'}}
				</view>
			</view>
			<view class="personal-bd" v-if="infoDTO.routeType=='0'">
				<view class="personal-wrapper__label">
					取件时间
				</view>
				<view class="personal-wrapper__value">
					{{infoDTO.pickUpTime}}
				</view>
			</view>
			<!-- 上门取件 -->
			<view class="personal-bd" v-if="infoDTO.routeType=='0'">
				<view class="personal-wrapper__label">
					取件地址
				</view>
				<view class="personal-wrapper__value" style="flex-direction: column;">
					<view class="item">
						{{infoDTO.pickUpUser}}
					</view>
					<view class="item">
						{{infoDTO.pickUpPhone}}
					</view>
					<view class="item">
						{{infoDTO.pickUpAddress}}
					</view>
				</view>
			</view>
			<!-- 自行寄回 -->
			<view class="personal-bd" v-if="infoDTO.routeType=='1' && Object.keys(addressDTO).length > 0">
				<view class="personal-wrapper__label">
					寄件地址
				</view>
				<view class="personal-wrapper__value" style="flex-direction: column;">
					<view class="item">
						{{addressDTO.contacts}}
					</view>
					<view class="item">
						{{addressDTO.telephone}}
					</view>
					<view class="item">
						{{addressDTO.address}}
					</view>
				</view>
			</view>
			<view class="personal-bd" v-if="infoDTO.returnWaybillNos">
				<view class="personal-wrapper__label">
					物流单号
				</view>
				<view class="personal-wrapper__value" style="display: flex;justify-content: space-between;">
					<view class="wl-no">
						{{infoDTO.returnWaybillNos}}
					</view>
					<view class="wl-detail" style="color: #0066E9;" @click="toWlDetail">
						查看物流
					</view>
				</view>
			</view>
			<view class="personal-bd" v-if="infoDTO.returnWaybillCompany">
				<view class="personal-wrapper__label">
					物流公司
				</view>
				<view class="personal-wrapper__value">
					{{infoDTO.returnWaybillCompany}}
				</view>
			</view>
		</view>
		<view class="personal-wrapper" v-if="infoDTO.type == '1' && infoDTO.payAmount && infoDTO.payAmount != 0">
			<tTitle title="售后费用"></tTitle>
			<view class="personal-bd" style="margin-top: 30rpx;">
				<view class="personal-wrapper__label">
					支付金额
				</view>
				<view class="personal-wrapper__value">
					￥{{moneyFilter(infoDTO.payAmount)}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					设备类型
				</view>
				<view class="personal-wrapper__value" v-if="infoDTO.isPay == 0">
					ETC卡
				</view>
				<view class="personal-wrapper__value" v-if="infoDTO.isPay == 1">
					电子标签设备
				</view>
				<view class="personal-wrapper__value" v-if="infoDTO.isPay == 2">
					卡签套装
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					原因
				</view>
				<view class="personal-wrapper__value">
					{{ infoDTO.payDesc || '' }}
				</view>
			</view>
		</view>
		<view class="personal-wrapper" v-if="infoDTO.type == '0' && infoDTO.payAmount && infoDTO.payAmount != 0">
			<tTitle title="售后费用"></tTitle>
			<view class="personal-bd" style="margin-top: 30rpx;">
				<view class="personal-wrapper__label">
					可退金额
				</view>
				<view class="personal-wrapper__value">
					￥{{moneyFilter(infoDTO.refundAmount)}}
				</view>
			</view>
			<view class="personal-bd" style="margin-top: 30rpx;">
				<view class="personal-wrapper__label">
					需赔付金额
				</view>
				<view class="personal-wrapper__value">
					￥{{moneyFilter(infoDTO.payAmount)}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					设备类型
				</view>
				<view class="personal-wrapper__value" v-if="infoDTO.isPay == 0">
					ETC卡
				</view>
				<view class="personal-wrapper__value" v-if="infoDTO.isPay == 1">
					电子标签设备
				</view>
				<view class="personal-wrapper__value" v-if="infoDTO.isPay == 2">
					卡签套装
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					原因
				</view>
				<view class="personal-wrapper__value">
					{{ infoDTO.payDesc || '' }}
				</view>
			</view>
		</view>
		<view class="personal-wrapper" v-if="selfCharge && !infoDTO.returnWaybillNos">
			<!-- <view class="personal-wrapper"> -->
			<tTitle title="设备寄回物流信息"></tTitle>
			<view class="personal-bd scan-bd" style="margin-top: 30rpx;">
				<view class="personal-wrapper__label">
					物流单号
				</view>
				<input class="personal-wrapper__input scan-input" v-model="wlNo" placeholder="请输入物流单号" />
				<view class="scan-wrapper" @click.stop="scan">
					<image class="scan-img " src="../../static/new-apply/after/icon-scan.png" mode="">
					</image>
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					物流公司
				</view>
				<input class="personal-wrapper__input" v-model="wlCompany" placeholder="请输入物流公司" />
			</view>
		</view>
		<v-block v-if="infoDTO.isDelete == '0'">
			<!-- 上门取件 -->
			<tButton v-if="nomalCharge" :buttonList="buttonList" @modify="modify" @cancel="cancel"></tButton>
			<!-- 自行寄回 -->
			<tButton v-if="selfCharge && !infoDTO.returnWaybillNos" :buttonList="buttonList1" @submit="submit"
				@cancel="cancel"></tButton>
			<!-- 需要支付 -->
			<tButton v-if="payCharge" :buttonList="buttonList2" @pay="pay" @cancel="cancel"></tButton>
		</v-block>
		<u-modal v-model="uShow" @confirm="uShowConfirm" confirm-text="确定" :content="limitContent" ref="uModal"
			:async-close="true">
		</u-modal>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import {
		getVehicleColor,
	} from '@/common/method/filter.js';
	import float from '@/common/method/float.js'
	import {
		getOpenid,
		setOpenid
	} from '@/common/storageUtil.js';
	export default {
		components: {
			tLoading,
			tTitle,
			tButton,
		},
		data() {
			return {
				isLoading: false,
				uShow: false,
				id: '',
				wlNo: '', //自行寄回物流信息
				wlCompany: '',
				// routeType: '', //1：自行寄回 0: 上门取件
				openId: '',
				buttonList: [{
					title: '修改售后材料',
					handle: 'modify'
				}, {
					title: '撤销售后申请',
					handle: 'cancel'
				}],
				buttonList1: [{
					title: '确认提交',
					handle: 'submit'
				}, {
					title: '撤销售后申请',
					handle: 'cancel'
				}],
				buttonList2: [{
					title: '确定支付',
					handle: 'pay'
				}, {
					title: '撤销售后申请',
					handle: 'cancel'
				}],
				limitContent: '', //支付信息
				addressDTO: {},
				resultData: {},
				imgList: [],
				infoDTO: {}, //售后信息
				info: {}, //退货信息
			}
		},
		computed: {
			vehicleColorStr() {
				return getVehicleColor(this.infoDTO.vehicleClolor);
			},
			//自行寄回判断
			selfCharge() {
				if (this.infoDTO.routeType == '1') {
					if (this.resultData.nodeCode == '2020' && this.resultData.nodeStatus == '3') {
						//无偿
						return true
					} else if (this.resultData.nodeCode == '2400') {
						//有偿
						if (this.infoDTO.isSalePay == '1') {
							return true
						} else {
							return false
						}
					}
				} else {
					return false
				}
			},
			nomalCharge() {
				if (this.resultData.nodeCode == '2000' || (this.resultData.nodeCode == '2020' && this.resultData
						.nodeStatus == '4')) {
					if (this.infoDTO.returnWaybillNos) {
						//有订单号后就不能修改
						this.buttonList.shift()
					}
					return true
				}
			},
			payCharge() {
				if (this.infoDTO.payAmount && this.infoDTO.payAmount != 0 && this.infoDTO.isSalePay == '0' && !this.infoDTO
					.returnWaybillNos && this
					.resultData
					.nodeCode ==
					'2400') {
					return true
				} else {
					return false
				}
			}
		},
		onLoad(option) {
			this.openId = getOpenid() || ''
			console.log('option', option.saleId)
			if (option.saleId) {
				this.id = option.saleId
				this.getDetail()
			}
		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
		},
		methods: {
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			scan() {
				uni.scanCode({
					success: (res) => {
						console.log('条码内容：' + res.result);
						this.wlNo = res.result
					},
					fail: err => {
						uni.showModal({
							title: '错误',
							content: err,
							showCancel: false
						});
					}
				});
			},
			//图片预览
			ViewImage(path) {
				if (!path) return
				let newArr = [];
				newArr.push(path);
				uni.previewImage({
					urls: newArr
				});
			},
			submit() {
				if (!this.wlNo) {
					uni.showModal({
						title: '提示',
						content: '请先填写物流单号',
						showCancel: false
					});
					return
				}
				if (!this.wlCompany) {
					uni.showModal({
						title: '提示',
						content: '请先填写物流公司',
						showCancel: false
					});
					return
				}
				this.isLoading = true
				let params = {
					id: this.id,
					logisticsNo: this.wlNo,
					returnWaybillCompany: this.wlCompany,
				}

				this.$request.post(this.$interfaces.sendLogisticsNo, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						//刷新数据
						// this.getDetail()
						uni.reLaunch({
							url: '/pagesA/newBusiness/order/orderSuccess?type=10&saleId=' + this.id
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			// confirm() {
			// 	uni.showModal({
			// 		title: '签收提示',
			// 		content: '确认是否对该售后单进行收货操作？',
			// 		confirmText: '确认收货',
			// 		success: res => {
			// 			if (res.confirm) {
			// 				this.confirmReceive()
			// 			}
			// 		}
			// 	});
			// },
			getDetail() {
				this.isLoading = true
				let params = {
					id: this.id
				}

				this.$request.post(this.$interfaces.getReturnInfoDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('详情信息=====>>', res)
						this.uShow = false
						this.resultData = res.data
						// this.routeType = res.data.routeType
						// this.current = parseInt(res.data.nodeCode)
						this.addressDTO = res.data.addressDTO
						this.infoDTO = res.data.infoDTO
						this.imgList = res.data.infoDTO.filePathData.split(',')
						// this.reviewedDTO = res.data.reviewedDTO
						// this.closeDesc = res.data.closeDesc
						if (res.data.nodeCode == '4' && res.data.info) {
							//退货信息
							this.info = res.data.info
						}
						// if (res.data.sfRouteResInfoDTO) {
						// 	this.changeData(res.data.sfRouteResInfoDTO)
						// }
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			cancel() {
				uni.showModal({
					title: '操作提示',
					content: '确定要撤销该售后申请单吗',
					success: (res) => {
						if (res.confirm) {
							this.doCancel()
						}
					}
				});
			},
			doCancel() {
				this.isLoading = true
				let params = {
					id: this.id
				}

				this.$request.post(this.$interfaces.returnCancel, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('取消操作', res)
						if (this.infoDTO.type == '1') {
							//换货
							uni.reLaunch({
								url: '/pagesA/newBusiness/order/orderSuccess?type=4&saleId=' + this.id
							})
						} else if (this.infoDTO.type == '0') {
							uni.reLaunch({
								url: '/pagesA/newBusiness/order/orderSuccess?type=8&saleId=' + this.id
							})
						}

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			uShowConfirm() {
				this.getDetail()
			},
			pay() {
				let _self = this;
				this.isLoading = true;
				let params = {
					// activationDeposit: this.priceList.activationDeposit,
					// benefitServiceFee: this.priceList.benefitServiceFee,
					// applyProductConfigId: this.priceList.id,
					applyId: this.id,
					openId: this.openId,
					payMoney: this.infoDTO.payAmount,
					payType: '10000601',
					source: '3',
				}
				console.log('入参params', params)
				let data = {
					routePath: this.$interfaces.applyAfterPay.method,
					bizContent: params
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;
						console.log(res, '支付');
						if (res.code == 200) {
							let result = res.data;
							let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};

							wx.requestPayment({
								...payMessage,
								success: function(successres) {
									_self.isLoading = true
									console.log(successres, '支付成功回调', _self.isLoading);
									setTimeout(() => {
										_self.applyPayOrderQuery(result);
									}, 6000);
									// _self.updatePayStatus(res.data.orderId, 2)
								},
								fail: function(err) {
									console.log('fail', err)
									_self.updatePayStatus(res.data.orderId, 3)
								},
							});
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			applyPayOrderQuery(data) {
				let params = {
					routePath: this.$interfaces.applyAfterPayOrderQuery.method,
					bizContent: {
						orderId: data.orderId
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					console.log('data===============>>>>>>>>>>', res)
					let status = res.data.status;
					let statusVal = {
						1: "支付中",
						2: "支付成功",
						3: "支付失败",
					};
					let msg = statusVal[status] || "支付中";

					//弹框
					this.uShow = true
					this.limitContent = msg

				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			updatePayStatus(orderId, status) {
				let params = {
					routePath: this.$interfaces.afterPayUpdate.method,
					bizContent: {
						orderId: orderId,
						status: status,
						payBusinessType: 2
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					// if (res.code == 200 && status == 2) {
					// 	// this.clearDraft() //清除草稿
					// 	//支付成功且更新状态成功
					// 	this.nextFlag = true
					// 	uni.reLaunch({
					// 		url: '/pagesA/order/orderList'
					// 	});
					// }
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			toWlDetail() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderWl/orderWlDetail?applyId=' + this.infoDTO.pid
				})
			},
			modify() {
				uni.redirectTo({
					url: './apply?saleId=' + this.id
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-info__detail {
		padding-bottom: 160rpx;
	}

	.personal-wrapper {
		background-color: #ffffff;
		margin: 20rpx;
		padding: 35rpx 30rpx 30rpx 30rpx;
		border-radius: 12rpx;
	}

	.personal-bd {
		position: relative;
		display: flex;
		margin-bottom: 30rpx;

		.scan-wrapper {
			display: flex;
			justify-content: flex-end;
			align-items: center;
			position: absolute;
			width: 70rpx;
			height: 46rpx;
			right: 0;
			bottom: 0;
			padding-right: 10rpx;
			z-index: 10;
		}

		.scan-input {
			padding-right: 50rpx;
		}

		.scan-img {
			width: 30rpx;
			height: 30rpx;
		}
	}


	.personal-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
		line-height: 44rpx;
	}

	.personal-wrapper__value {
		display: flex;
		flex-wrap: wrap;
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;

		.item {
			margin-top: 10rpx;

			&:first-child {
				margin-top: 0;
			}
		}
	}

	.personal-wrapper__input {
		padding: 0 10rpx;
		background: #EFEFEF;
		border-radius: 6rpx;
		flex: 1;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #333333;
	}


	.pic-wrapper {
		margin-bottom: 20rpx;

		&:nth-child(2n-1) {
			margin-right: 10rpx;
		}
	}

	.pic-img {
		width: 215rpx;
		height: 131rpx;
	}

	.pic-lable {
		text-align: center;
	}

	.car-wrapper {
		background-color: #ffffff;
		margin: 30rpx;
	}

	.car-bd {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.car-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.car-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}
</style>