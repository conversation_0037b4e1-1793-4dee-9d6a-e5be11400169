<template>
	<view class="after-sale">
		<view class="reason-select">
			<tTitle title="退换货原因" setPadding="20"></tTitle>
			<view class="radio-list">
				<uni-data-checkbox selectedColor="#3874FF" v-model="current" style="transform:scale(0.2)"
					:localdata="reasonList1">
				</uni-data-checkbox>
			</view>
			<view class="textarea-wrapper">
				<textarea class="textarea" @input="monitorInput" maxlength="200" cols="20" rows="5"
					v-model="formData.remarks" placeholder="补充描述，有助于商家更好的处理售后问题"
					placeholder-style="color:#B9B9B9;font-size:24rpx;"></textarea>
				<view class="limit">
					{{num}}/200
				</view>
			</view>
			<view class="tips">
				<view><text style="color:#FF9D09;">*</text>注意：</view>
				<view class="tip">
					1.请上传设备外观图片，需清晰拍摄电子标签及ETC卡的正、反面。
				</view>
				<view class="tip">
					2.如不清楚拍照要求，请先 <text style="color: #3874FF;" @click="preview">查看样例</text>
				</view>

			</view>
			<!-- <view class="upload-wrapper">
				<view class="upload-container my-flex-center">
					<view class="upload" @tap="ChooseImage()" v-if="imgList.length < 9">
						<image src="../../../static/etc/image-upload.png" class="image-upload" mode="widthFix"></image>
						<view class="upload-text">
							上传图片
						</view>
					</view>
				</view>
			</view> -->
			<view class="upload-wrapper">
				<saleUpload ref="saleUpload" :imgList="imgList" @delImg="delImg" @on-change="onChange()"></saleUpload>
			</view>
		</view>
		<view class="address-wrapper" style="padding-bottom: 40rpx;">
			<!-- 			<tTitle title="返回方式" setPadding="20">
				<template v-slot>
					<view class="btn-wrapper">
						<view class="left-btn" @click="selectType('0')"
							:class="formData.routeType == '0'?'selector':''">
							上门取件
						</view>
						<view class="right-btn" @click="selectType('1')"
							:class="formData.routeType == '1'?'selector':''">
							自行寄回
						</view>
					</view>
				</template>
			</tTitle> -->
			<view class="title-wrapper">
				<view class="title">
					返回方式
				</view>
				<view class="btn-wrapper">
					<view class="left-btn" @click="selectType('0')" :class="formData.routeType == '0'?'selector':''">
						上门取件
					</view>
					<view class="right-btn" @click="selectType('1')" :class="formData.routeType == '1'?'selector':''">
						自行寄回
					</view>
				</view>
			</view>
			<view class="section-1 border-bottom" v-if="formData.routeType == '0'" @click="autoFill('send')">
				<view class="icon-wrapper">
					<image src="../../static/new-apply/order/icon-location.png" mode=""></image>
					<view class="desc">
						取件地址
					</view>
				</view>
				<view class="content-wrapper">
					<view class="content" v-if="formData.pickUpUser && formData.pickUpAddress">
						<view class="name" :class="[formData.pickUpUser?'':'gray']">
							{{formData.pickUpUser || '寄件人'}}
						</view>
						<view class="phone" :class="[formData.pickUpPhone?'':'gray']">
							{{formData.pickUpPhone || '寄件人手机'}}
						</view>
					</view>
					<view class="address" :class="[formData.pickUpAddress?'':'gray']">
						{{formData.pickUpAddress || '请选择取件地址'}}
					</view>
				</view>
				<image style="width: 28rpx;height: 28rpx;" src="../../static/new-apply/personal/arrow-right.png"
					mode=""></image>
			</view>

			<view class="section-1 border-bottom" v-if="formData.routeType == '0'" @click="getTime">
				<view class="icon-wrapper">
					<image src="../../static/new-apply/after/icon-time.png" mode=""></image>
					<view class="desc">
						取件时间
					</view>
				</view>
				<view class="content-wrapper justify-center">
					<view class="" :class="[formData.pickUpTime?'':'gray']">
						{{formData.pickUpTime || '请选择取件的时间'}}
						<!-- 						<uni-datetime-picker @change="timeChange()" type="datetime" v-model="single" :start="timeStart"
							:end="timeEnd">请选择取件的时间</uni-datetime-picker> -->
					</view>
				</view>
				<image style="width: 28rpx;height: 28rpx;" src="../../static/new-apply/personal/arrow-right.png"
					mode=""></image>
			</view>

			<view class="section-2" v-if="formData.routeType == '1'">
				<view class="content">
					<view class="desc">
						<view> 1.请在售后申请审核通过后7天内将设备整套寄回并填写物流信息，以便我们尽快为您处理售后订单</view>
						<view style="margin-top: 10rpx;"> 2.寄件费用由用户本人承担</view>
					</view>
				</view>
			</view>
		</view>
		<view class="address-wrapper" v-if="formData.type == '1'" style="margin-top: 30rpx;">
			<tTitle title="收货地址" set-padding="30"></tTitle>
			<view class="section-1 border-top" style="margin-bottom: 30rpx;" @click="autoFill('receive')">
				<view class="icon-wrapper">
					<image src="../../static/new-apply/order/icon-location.png" mode=""></image>
					<view class="desc">
						收货地址
					</view>
				</view>
				<view class="content-wrapper">
					<view class="content" v-if="formData.addressUser && formData.addressPhone">
						<view class="name" :class="[formData.addressUser?'':'gray']">
							{{formData.addressUser || '收货人'}}
						</view>
						<view class="phone" :class="[formData.addressPhone?'':'gray']">
							{{formData.addressPhone || '收货人手机'}}
						</view>
					</view>
					<view class="address" :class="[formData.address?'':'gray']">
						{{formData.address || '请选择收货地址'}}
					</view>
				</view>
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/right-arr.png" mode=""></image>
			</view>
		</view>
		<!-- 		<view class="bottom-info" v-if="formData.routeType == '1'">
			寄件费用由用户本人承担
		</view> -->
		<tButton @submit="submitHandle" :buttonList="buttonList"></tButton>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" :imgcount="9" type="base64">
		</cpimg>
		<uni-calendar ref="calendar" :startDate="timeStart" :endDate="timeEnd" :date="timeStart" :insert="false"
			@confirm="timeChange()" />
		<!-- 		<time-picker ref="timePicker" type="time" @change="timeConfirm()" :border="false" :start="reactStartTime"
			:end="reactEndTime" :hideSecond="hideSecond" style="width: 100%;">
		</time-picker> -->
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import uniDataCheckbox from '@/pagesA/components/uni-data-checkbox/uni-data-checkbox.vue'
	import uniDatetimePicker from '@/pagesA/components/uni-datetime-picker/uni-datetime-picker.vue'
	import uniCalendar from '@/pagesA/components/uni-datetime-picker/calendar.vue'
	import timePicker from '@/pagesA/components/uni-datetime-picker/time-picker.vue'
	import saleUpload from '@/pagesA/components/sale-upload/sale-upload.vue'
	// import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import {
		previewFile
	} from '@/pagesA/common/method.js'
	import {
		vehicleColors,
	} from "@/common/const/optionData.js";
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		components: {
			tLoading,
			tTitle,
			tButton,
			uniDataCheckbox,
			uniDatetimePicker,
			uniCalendar,
			timePicker,
			saleUpload
		},
		data() {
			return {
				vehicleColors,
				isLoading: false,
				// show: false,
				hideSecond: false,
				applyId: '',
				date: '', //选择日期
				timeStart: '',
				timeEnd: '',
				// reactStartTime: '08:00:00',
				// reactEndTime: '20:00:00',
				num: 0,
				detail: {},
				productList: [{
					text: 'ETC卡',
					value: '0'
				}, {
					text: 'OBU',
					value: '1'
				}],
				productListDis: [{
					text: 'ETC卡',
					value: '0',
					disable: true
				}, {
					text: 'OBU',
					value: '1',
					disable: true
				}],
				checkboxValue: [],
				checkedValue: ['0', '1'],
				reasonList1: [{
						value: '1',
						text: '无法连接蓝牙',
					},
					{
						value: '2',
						text: '插卡时无反应'
					},
					{
						value: '3',
						text: '插卡时提示异常'
					},
					{
						value: '4',
						text: '发错货'
					},
					{
						value: '5',
						text: '不想要了'
					},
					{
						value: '6',
						text: '7天无理由退换',
					},
					{
						value: '7',
						text: '外观损坏',
					},
					{
						value: '8',
						text: '其他'
					},
				],
				//退货
				// reasonList0: [{
				// 		value: '1',
				// 		text: '7天无理由退货',
				// 	},
				// 	{
				// 		value: '2',
				// 		text: '已办理外省'
				// 	},
				// 	{
				// 		value: '3',
				// 		text: '不想办了'
				// 	},
				// 	{
				// 		value: '4',
				// 		text: '少件/漏发'
				// 	},
				// 	{
				// 		value: '5',
				// 		text: '插卡时无反应'
				// 	},
				// 	{
				// 		value: '6',
				// 		text: '插卡时提示异常'
				// 	},
				// 	{
				// 		value: '7',
				// 		text: '其他'
				// 	},
				// ],
				formData: {
					address: '',
					addressPhone: '', //收货电话
					addressUser: '', //收货人
					pickUpAddress: '', //上门取件地址
					pickUpPhone: '', //上门取件号码
					pickUpUser: '', //上门取件人
					pickUpTime: '', //上门取件时间
					deviceType: '', //设备类型0卡 1OBU 2套装
					filePathList: '', //图片拼接
					filePathListCode: '', //图片拼接code
					id: '',
					isAgain: '0',
					reason: '',
					remarks: '',
					type: '0', //换货1，退货0
					routeType: '0', //1：自行寄回 0: 上门取件
				},
				receiveAddress: {
					provinceName: '',
					cityName: '',
					countyName: '',
					detailInfo: '',
					userName: '',
					telNumber: '',
				},
				sendAddress: {
					provinceName: '',
					cityName: '',
					countyName: '',
					detailInfo: '',
					userName: '',
					telNumber: ''
				},
				current: '',
				imgList: [{
					url: '',
					code: ''
				}, {
					url: '',
					code: ''
				}, {
					url: '',
					code: ''
				}], //图片占位
				productType: null,
				buttonList: [{
					title: '提交',
					handle: 'submit'
				}],
				saleId: ''
			};
		},
		onLoad(options) {
			console.log('options', options)
			if (options.applyId) {
				this.formData.id = options.applyId
				this.getDetail()
			}
			if (options.saleId) {
				this.saleId = options.saleId
				//重新提交数据，回填数据
				this.showData(options.saleId)
			}
			if (options.type) {
				this.formData.type = options.type
				if (this.formData.type == '1') {
					this.getAddress()
				}
			}
		},
		methods: {
			//判断取件时间范围
			// chargeTime() {
			// 	// reactStartTime: '09:00:00'
			// 	// reactEndTime: '20:00:00'
			// 	// timeStart: '',
			// 	// timeEnd: '',
			// 	let nowDate = dayjs(new Date()).format('YYYY-MM-DD'); //获取当天时间
			// 	let nowDateTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'); //获取当天时间时分秒
			// 	let nowTime = dayjs(new Date()).format('HH:mm:ss'); //获取当天时间时分秒


			// 	let timeStart = nowDate + ' ' + this.reactStartTime
			// 	console.log('timeStart', timeStart)

			// 	let sameDate = dayjs(nowDate).isSame(this.date) //比较当前日期和范围开始日期
			// 	console.log('sameDate是TRUE吗', sameDate, this.date, nowDate)
			// 	if (sameDate) {
			// 		//如果相同，需要判断时间点不能早于当前时间
			// 		let beforeStartTime = dayjs(nowDateTime).isAfter(timeStart) //如果在当前时间点后，直接取当前时间点
			// 		console.log('beforeStartTime是TRUE吗', beforeStartTime, nowTime, this.reactStartTime)
			// 		if (beforeStartTime) {
			// 			//在9点后直接取当前时间
			// 			this.reactStartTime = nowTime
			// 		}
			// 	} else {
			// 		this.reactStartTime = '09:00:00'
			// 	}
			// },
			monitorInput() {
				let len = this.formData.remarks.length
				this.num = len
			},
			showData(id) {
				this.isLoading = true
				let params = {
					id: id
				}

				this.$request.post(this.$interfaces.getAgainData, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('回填数据res', res)
						this.changeData(res.data)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			changeData(detail) {
				this.formData.isAgain = detail.isAgain || ''
				this.formData.filePathList = detail.filePathData || ''
				this.formData.filePathListCode = detail.filePathDataCode || ''
				this.formData.pickUpAddress = detail.pickUpAddress || ''
				this.formData.pickUpPhone = detail.pickUpPhone || ''
				this.formData.pickUpUser = detail.pickUpUser || ''
				this.formData.address = detail.address || ''
				this.formData.addressPhone = detail.addressPhone || ''
				this.formData.addressUser = detail.addressUser || ''
				this.formData.remarks = detail.remarks || ''

				//计算字数
				let len = this.formData.remarks.length
				this.num = len

				this.formData.type = detail.type || ''
				this.formData.routeType = detail.routeType || ''
				this.formData.id = detail.id || ''
				this.formData.pickUpTime = detail.pickUpTime || ''
				//重新获取取件时间--上门区间才调用===先注释
				// if (this.formData.routeType == '0') {
				// 	this.getDateRange(detail.pickUpAddress)
				// }


				//回填原因
				this.formData.reason = detail.reason || ''
				// if (detail.type == '0') {
				// 	this.reasonList0.forEach(item => {
				// 		if (item.text == this.formData.reason) {
				// 			this.current = item.value
				// 		}
				// 	})
				// } else if (detail.type == '1') {
				this.reasonList1.forEach(item => {
					if (item.text == this.formData.reason) {
						this.current = item.value
					}
				})
				// }
				//回填图片
				let imgArr = [] //图片回填数组
				// let urlArr //处理图片url数组
				// let codeArr //处理图片code数组
				if (detail.filePathDataCode == '') {
					//存量档案没有code直接存入回填数组
					imgArr = detail.filePathData.split(',')
					imgArr.forEach(item => {
						item.code = ''
					})
				} else {
					console.log('detail111', detail.filePathData)
					console.log('detail222', detail.filePathDataCode)
					//改造档案有code需要回填进数组
					let pathArr = detail.filePathData.split(',')
					let codeArr = detail.filePathDataCode.split(',')
					console.log('pathArr', pathArr)
					console.log('codeArr', codeArr)
					for (let i = 0; i < 3; i++) {
						imgArr.push({
							url: pathArr[i] || '',
							code: codeArr[i] || ''
						})
					}
				}
				this.imgList = imgArr
				console.log('imgList', this.imgList)


			},
			getAddress() {
				this.isLoading = true
				let params = {
					id: this.formData.id
				}

				this.$request.post(this.$interfaces.getFillAddress, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.formData.address = res.data.address
						this.formData.addressUser = res.data.customerName
						this.formData.addressPhone = res.data.linkMobile
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			getTime() {
				if (!this.formData.pickUpAddress) {
					uni.showModal({
						title: '提示',
						content: '请先选择取件地址！',
						showCancel: false
					});
					return
				}
				this.getDateRange(this.formData.pickUpAddress)
			},
			timeChange(e) {
				console.log('e', e)
				this.date = e.fulldate
				// this.chargeTime()
				this.formData.pickUpTime = this.date
				//取消时间选择  2023/4/24
				// this.$refs.timePicker.tiggerTimePicker()
			},
			//时间确认
			timeConfirm(e) {
				console.log('时间格式', e)
				this.formData.pickUpTime = this.date + ' ' + e
				// console.log(this.timeDate, this.time)

			},
			submitHandle() {
				if (!this.validData()) return
				if (this.saleId) {
					console.log('sale')
					this.modify()
				} else {
					this.submit()
				}
			},
			modify() {
				console.log('current', this.current)

				//原因处理

				// if (this.formData.type == '0') {
				// 	this.reasonList0.forEach(item => {
				// 		if (item.value == this.current) {
				// 			this.formData.reason = item.text
				// 		}
				// 	})
				// } else if (this.formData.type == '1') {
				this.reasonList1.forEach(item => {
					if (item.value == this.current) {
						this.formData.reason = item.text
					}
				})
				// }

				// //设备类型处理
				// if (this.checkboxValue.length == 1) {
				// 	this.formData.deviceType = this.checkboxValue[0]
				// } else if (this.checkboxValue.length == 2) {
				// 	this.formData.deviceType = '2'
				// }

				// //退货的时候默认套装
				// if (this.formData.type == '0') {
				// 	this.formData.deviceType = '2'
				// }

				//图片数据处理
				let imgListStr = ''
				let codeListStr = ''
				this.imgList.forEach((item, index) => {
					// imgListData['imgData' + (index + 1)] = item
					if (index == this.imgList.length - 1) {
						imgListStr += item.url
						codeListStr += item.code
					} else {
						imgListStr += item.url + ','
						codeListStr += item.code + ','
					}
				})
				this.formData.filePathList = imgListStr
				this.formData.filePathListCode = codeListStr

				console.log('入参', this.formData)

				this.isLoading = true
				let params = {
					...this.formData
				}
				params.id = this.saleId

				this.$request.post(this.$interfaces.applyAfterModify, {
					data: params
				}).then(res => {
					this.isLoading = false;
					console.log('res===>>>>', res)
					if (res.code == 200) {
						// uni.redirectTo({
						// 	url: './apply-detail?saleId=' + this.saleId
						// })

						uni.reLaunch({
							url: '/pagesA/newBusiness/order/orderSuccess?type=9&saleId=' + this.saleId
						})

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			submit() {
				console.log('current', this.current)

				//原因处理

				// if (this.formData.type == '0') {
				// 	this.reasonList0.forEach(item => {
				// 		if (item.value == this.current) {
				// 			this.formData.reason = item.text
				// 		}
				// 	})
				// } else if (this.formData.type == '1') {
				this.reasonList1.forEach(item => {
					if (item.value == this.current) {
						this.formData.reason = item.text
					}
				})
				// }

				// //设备类型处理
				// if (this.checkboxValue.length == 1) {
				// 	this.formData.deviceType = this.checkboxValue[0]
				// } else if (this.checkboxValue.length == 2) {
				// 	this.formData.deviceType = '2'
				// }

				// //退货的时候默认套装
				// if (this.formData.type == '0') {
				// 	this.formData.deviceType = '2'
				// }

				//图片数据处理
				let imgListStr = ''
				let codeListStr = ''
				this.imgList.forEach((item, index) => {
					// imgListData['imgData' + (index + 1)] = item
					if (index == this.imgList.length - 1) {
						imgListStr += item.url
						codeListStr += item.code
					} else {
						imgListStr += item.url + ','
						codeListStr += item.code + ','
					}
				})
				this.formData.filePathList = imgListStr
				this.formData.filePathListCode = codeListStr

				console.log('入参', this.formData)

				this.isLoading = true
				let params = {
					...this.formData
				}

				this.$request.post(this.$interfaces.applyAfterCreate, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.redirectTo({
							url: './apply-list?applyId=' + this.formData.id
						})

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			validData() {
				// if (this.formData.type == '1' && this.checkboxValue.length == 0) {
				// 	uni.showModal({
				// 		title: '提示',
				// 		content: '请先选择需要换货的设备！',
				// 		showCancel: false
				// 	});
				// 	return false
				// }
				if (!this.current) {
					uni.showModal({
						title: '提示',
						content: '请先选择退换货的原因！',
						showCancel: false
					});
					return false
				}
				if (this.current == '8' && !this.formData.remarks) {
					uni.showModal({
						title: '提示',
						content: '选择其他原因时，需填写下方补充描述！',
						showCancel: false
					});
					return false
				}
				console.log('this,ig', this.imgList)
				if (!this.imgList[0].code) {
					uni.showModal({
						title: '提示',
						content: '请先上传电子标签及ETC卡正面！',
						showCancel: false
					});
					return false
				}
				if (!this.imgList[1].code) {
					uni.showModal({
						title: '提示',
						content: '请先上传电子标签及ETC卡反面！',
						showCancel: false
					});
					return false
				}
				if (this.formData.routeType == '0') {
					if (!this.formData.pickUpAddress) {
						uni.showModal({
							title: '提示',
							content: '请先选择上门取件地址！',
							showCancel: false
						});
						return false
					}
					if (!this.formData.pickUpTime) {
						uni.showModal({
							title: '提示',
							content: '请先选择上门取件时间！',
							showCancel: false
						});
						return false
					}
					if (!this.formData.address && this.formData.type == '1') {
						uni.showModal({
							title: '提示',
							content: '请先填写收货地址！',
							showCancel: false
						});
						return false
					}
				}

				return true
			},
			//获取物流时间段
			getDateRange(address, province, city, county) {
				this.isLoading = true
				let params = {
					address: address,
					province: province,
					city: city,
					county: county
				}

				this.$request.post(this.$interfaces.getDateRange, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('时间段', res)
						let datTime = res.data.day
						this.timeStart = datTime[0]
						console.log('this.timeStart', this.timeStart)
						this.timeEnd = datTime[datTime.length - 1]

						this.$refs.calendar.open()

						console.log('time', this.timeStart, this.timeEnd)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			//获取微信地址信息
			autoFill(type) {
				console.log('微信调用地址1')
				uni.authorize({
					scope: 'scope.address',
					success: () => {
						this.getWxAddress(type)
					}
				})
			},
			getWxAddress(type) {
				console.log('123123')

				uni.chooseAddress({
					success: user => {
						console.log('user', user)
						// if (user.provinceName != '广西壮族自治区') {
						// 	uni.showModal({
						// 		title: '提示',
						// 		content: '目前仅支持广西区内发货',
						// 		showCancel: false
						// 	});
						// 	return
						// }
						//微信获取地址信息赋值
						if (type == 'send') {
							this.formData.pickUpAddress = user.provinceName + user.cityName + user.countyName +
								user
								.detailInfo
							this.formData.pickUpPhone = user.telNumber
							this.formData.pickUpUser = user.userName
							//获取时间
							// this.getDateRange(this.formData.pickUpAddress, user.provinceName, user.cityName,
							// 	user.countyName)
						} else if (type == 'receive') {
							this.formData.address = user.provinceName + user.cityName + user.countyName +
								user.detailInfo
							this.formData.addressPhone = user.telNumber
							this.formData.addressUser = user.userName
						}
					}
				})
			},
			// radioChange(evt) {
			// 	for (let i = 0; i < this.reasonList.length; i++) {
			// 		if (this.reasonList[i].value === evt.detail.value) {
			// 			this.current = i;
			// 			break;
			// 		}
			// 	}
			// },
			getDetail() {
				this.isLoading = true
				let params = {
					id: this.formData.id
				}

				this.$request.post(this.$interfaces.applyAfterProduct, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('详情信息=========>>>>>', res)
						this.detail = res.data
						if (res.data.flag7Time == '1') {
							//不符合7天无理由，去掉
							this.reasonList1 = this.reasonList1.filter(item => {
								return item.value != '6'
							})
						}
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			selectType(type) {
				this.formData.routeType = type
			},
			selectProductType(index) {
				this.productType = index
			},
			onChange(imgList) {
				this.imgList[imgList.side].url = imgList.url
				this.imgList[imgList.side].code = imgList.code
			},
			delImg(index) {
				this.imgList[index].url = ''
				this.imgList[index].code = ''
			},
			// webview跳转
			preview() {
				let url = 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/etc/线上发行退换货OBU样例.png';
				previewFile(url)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.gray {
		color: #999999 !important;
		font-size: 28rpx !important;
	}

	.border-bottom {
		border-bottom: 1rpx solid #e8e8e8;
	}

	.border-top {
		border-top: 1rpx solid #e8e8e8;
	}

	.my-flex-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/deep/.uni-data-checklist .checklist-group .checklist-box .checklist-content .checklist-text {
		font-size: 24rpx !important;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435 !important;
	}

	/deep/.uni-data-checklist .checklist-group .checklist-box .radio__inner {
		width: 24rpx !important;
		height: 24rpx !important;

		.radio__inner-icon {
			width: 13rpx !important;
			height: 13rpx !important;
		}
	}

	.after-sale {
		padding: 20rpx 20rpx 180rpx 20rpx;
		// padding-bottom: 180rpx;
		// padding-top: 184rpx;

		// height: calc(100% - 184rpx);
		.card-warpper {
			background-color: $uni-bg-color;
			// margin-top: -20rpx;

			.card-item {
				width: 100%;
				// background-color: rgba(153, 153, 153, 0.1);
				border-color: rgb(187, 187, 187);
				border-width: 1rpx;
				border-style: solid;
				// box-shadow: 0px 4rpx 12rpx 0px rgba(0, 0, 0, 0.4);
				border-radius: 12rpx;
				font-size: 28rpx;
				margin: 50rpx 48rpx 50rpx 48rpx;
				background-color: rgb(245, 245, 245);
				text-align: center;
				box-shadow: 0px 4rpx 12rpx 0px rgba(0, 0, 0, 0.4);
				line-height: 40rpx;
				padding: 0 48rpx;

				.card-type {
					margin-top: 40rpx;
					color: $my-theme-color;
					font-size: 44rpx;
					text-align: left;
					font-family: PingFangSC-regular;
				}

				.card-desc {
					margin-top: 30rpx;
					color: $my-theme-color;
					font-size: 24rpx;
				}

				.card-car {
					margin-top: 20rpx;
					margin-bottom: 20rpx;
				}

			}

			.card-selector {
				background-color: $my-theme-color;
				color: $uni-bg-color;

				.card-type,
				.card-desc {
					color: $uni-bg-color;
				}
			}
		}

		.product-type {
			background-color: $uni-bg-color;
			padding-bottom: 10rpx;

			.car-type {
				padding-bottom: 20rpx;

				.car-item {
					flex: 1;
					margin-left: 24rpx;
					width: 100%;
					height: 70rpx;
					line-height: 70rpx;
					border-radius: 12rpx;
					// background-color: rgb(235, 235, 235);
					// color: $my-theme-color;
					color: rgba(16, 16, 16, 70);
					font-size: 32rpx;
					text-align: center;
					box-shadow: 0px 4rpx 12rpx 0px rgba(0, 0, 0, 0.4);
					font-family: Arial;
					border: 1rpx solid $my-theme-color;

					&:first-child {
						margin-left: 48rpx;
					}

					&:last-child {
						margin-right: 48rpx;
					}
				}

				.selector {
					background-color: $my-theme-color;
					color: $uni-bg-color;
				}
			}
		}

		/deep/.title-wrapper .title {
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #606060;
		}

		.reason-select {
			background-color: $uni-bg-color;
			margin-bottom: 30rpx;
			border-radius: 12rpx;


			.radio-list {
				padding: 0 20rpx;
			}

			.textarea-wrapper {
				position: relative;
				margin: 20rpx 20rpx 0 20rpx;
				// border-bottom: 1rpx solid #e8e8e8;
				padding-bottom: 20rpx;
				background: #F8F8F8;

				.limit {
					position: absolute;
					right: 50rpx;
					bottom: 10rpx;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #B9B9B9;
				}

				&>textarea {
					padding: 20rpx;
					width: 100%;
					border-radius: 4rpx;
					height: 202rpx;
					// border: 1rpx solid #BBBBBB;
				}
			}
		}

		.upload-wrapper {
			/deep/.title-wrapper {
				margin-bottom: 0;
			}

			.upload-container {
				// margin-bottom: 20rpx;
				background-color: #FFFFFF;
				padding-bottom: 30rpx;

				.upload {
					position: relative;
					padding-bottom: 20rpx;

					.image-upload {
						width: 200rpx;
						height: 100rpx;
					}

					.upload-text {
						position: absolute;
						bottom: 10rpx;
						left: 0;
						right: 0;
						margin: 0 auto;
						text-align: center;
					}
				}
			}
		}

		.view-container {
			padding: 0 20rpx;
			background-color: #FFFFFF;

			.image-box {
				display: flex;
				flex-wrap: wrap;
				box-sizing: border-box;

				.view-image {
					// flex: 1;
					padding: 20rpx 0;
					margin-right: 20rpx;

					&:nth-child(3n) {
						margin-right: 0;
					}

					// &:nth-child(2n) {
					// 	justify-content: flex-end;
					// }

					.border-container {
						display: flex;
						flex-direction: column;
						align-items: center;
						position: relative;
						margin: 0 auto;
						border: 2rpx dashed #CACACA;
						padding: 20rpx;
						border-radius: 10rpx;

						.img-wrapper {
							border: 1rpx dashed #CACACA;
							margin-bottom: 15rpx;
							width: 70rpx;
							height: 59rpx;
						}

						.view-img {
							width: 160rpx;
							height: 120rpx;
							border-radius: 10rpx;
						}

						.text {
							font-size: 18rpx;
							font-family: PingFangSC-Regular, PingFang SC;
							font-weight: 400;
							color: #888888;
							text-align: center
						}

						.my-tag {
							width: 40rpx;
							height: 40rpx;
							position: absolute;
							right: -20rpx;
							top: -20rpx;
							border-radius: 50%;
						}
					}
				}
			}
		}

		.tips {
			margin: 30rpx;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;

			.tip {
				line-height: 40rpx;
				margin-top: 10rpx;
			}
		}

		.address-wrapper {
			background-color: $uni-bg-color;
			font-size: 30rpx;
			overflow: hidden;
			border-radius: 12rpx;

			.title-wrapper {
				margin: 30rpx 30rpx 0 30rpx;
				display: flex;
				align-items: center;
				border-bottom: 1rpx solid #E9E9E9;
				padding-bottom: 30rpx;
			}

			.btn-wrapper {
				margin-left: 55rpx;
				display: flex;
				align-items: center;
				// justify-content: center;
				// margin-right: 30rpx;
			}

			.left-btn,
			.right-btn {
				border: 1rpx solid #e8e8e8;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
			}

			.left-btn {
				width: 200rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
			}

			.right-btn {
				margin-left: 30rpx;
				width: 200rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
			}

			.selector {
				background-color: #0066E9;
				color: $uni-bg-color;
				border-color: #0066E9;
			}

			/deep/ .title-wrapper {
				height: 100rpx;
				line-height: 100rpx;

				.t-title::before {
					top: 35rpx;
				}
			}

			.section-1 {
				height: 161rpx;
				margin: 0 30rpx;
				// margin: -20rpx 20rpx 0 25rpx;
				// padding: 30rpx 0;
				display: flex;
				align-items: center;

				.icon-wrapper {
					// flex: 0 0 160rpx;
					// width: 160rpx;
					margin-right: 33rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					&>image {
						margin-bottom: 19rpx;
						width: 44rpx;
						height: 44rpx;
					}

					.desc {
						font-size: 24rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #323435;
					}
				}

				.content-wrapper {
					flex: 1;
					// margin-left: 30rpx;

					.content {
						display: flex;
						// justify-content: space-between;
						// margin-bottom: 20rpx;
						line-height: 42rpx;
					}

					.address {
						margin-top: 11rpx;
						font-size: 24rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #A3A3A3;
						line-height: 33rpx;
					}
				}

				&>image {
					// margin-left: 50rpx;
					width: 44rpx;
					height: 44rpx;
				}
			}

			.section-2 {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-top: -20rpx;
				// height: 200rpx;
				background-color: $uni-bg-color;

				.content {
					margin: 30rpx;
					// height: 202rpx;
					background: #F8F8F8;
					border-radius: 4rpx;

					.desc {
						padding: 25rpx 38rpx 30rpx 38rpx;
						font-size: 24rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #B9B9B9;
					}
				}

				.info {
					font-family: PingFangSC;
					font-weight: 400;
					font-size: 28rpx;
					color: rgba(142, 142, 147, 1);
					line-height: 42rpx;
					margin: 20rpx 0;
				}
			}
		}

		// .tips-container {
		// 	padding: 20rpx 0;
		// 	background-color: #FFFFFF;

		// 	.tips {
		// 		line-height: 40rpx;
		// 		font-size: 24rpx;
		// 		color: #01C1B2;
		// 	}
		// }
	}

	.bottom-info {
		margin-top: 20rpx;
		height: 33rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #888888;
		line-height: 33rpx;
		text-align: center;
	}
</style>