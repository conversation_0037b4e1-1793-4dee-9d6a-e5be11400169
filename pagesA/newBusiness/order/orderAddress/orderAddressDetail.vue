<template>
	<view class="order-address__detail">
		<view class="address-wrapper">
			<view class="title-wrapper">
				<view class="title">
					取货地址信息
				</view>
				<view class="wx-location" @click="autoFill">
					获取微信地址
				</view>
			</view>
			<view class="install">
				<title title="收货地址" setPadding="30"></title>
				<view class="input-wrapper">
					<view class="input-title">
						办理类型
					</view>
					<input style="background-color: #ffffff;" class="input auto-input" disabled value="快递邮寄"
						placeholder="请输入收货人" type="text">
				</view>
				<view class="input-wrapper">
					<view class="input-title">
						收货人
					</view>
					<input class="input auto-input" v-model="formData.contactsName" placeholder="请输入收货人" type="text">
					<!-- 				<view class="autofill" @click="autoFill">
					微信获取
				</view> -->
				</view>
				<view class="input-wrapper">
					<view class="input-title">
						手机号码
					</view>
					<input class="input" placeholder="请输入收货人手机号码" type="number" maxlength="11"
						v-model="formData.contactsTelephone">
				</view>
				<view class="input-wrapper">
					<view class="input-title">
						所在地区
					</view>
					<!-- <uni-data-picker class="picker" ref="picker" v-slot:default="{data, options}" popup-title="请选择所在地区"
						:localdata="regionList" :options="options" @change="onchange" @popupopened="onpopupopened"
						@popupclosed="onpopupclosed">
						<view v-if="options.length > 0" class="input selected-item">{{ options[0]}}
						</view>
						<view v-else class="input">
							<text>选择区域 (仅支持广西区内)</text>
						</view>
					</uni-data-picker> -->
					<uni-data-picker class="picker" placeholder-class="pla-css" placeholder="选择区域 (仅支持广西区内)"
						:localdata="regionList" v-model="formData.areaCode" :clear-icon="false" @change="onchange"
						@popupopened="onpopupopened" @popupclosed="onpopupclosed">
					</uni-data-picker>
					<image class="icon-location" style="width: 30rpx;height: 31rpx;"
						src="../../../static/new-apply/order/icon-location2.png" mode=""></image>
				</view>
				<view class="input-wrapper">
					<view class="input-title">
						详细地址
					</view>
					<!-- 					<textarea :disable-default-padding="true" :class="[textClass?'ios-textarea':'textarea']"
						class="input textarea-input" placeholder="详细至门牌信息" v-model="formData.house" maxlength="36" /> -->
					<input class="input auto-input" v-model="formData.house" placeholder="请输入详细地址" type="text">
				</view>
			</view>
		</view>
		<tButton @modify="modify" :buttonList="buttonList"></tButton>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	import uniDataPicker from '@/pagesA/components/uni-data-picker/uni-data-picker.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
	} from '@/common/method/filter.js';
	export default {
		props: {
			configReqVO: {
				type: Object
			}
		},
		components: {
			tLoading,
			tTitle,
			uniDataPicker,
			tButton
		},
		data() {
			return {
				isLoading: false,
				applyId: "",
				buttonList: [{
					title: '提交',
					handle: 'modify'
				}],
				options: [],
				regionList: [],
				formData: {
					contactsName: '', //收货人
					contactsTelephone: '', //收货人手机号码
					// provinceCode: '', //省编号
					provinceName: '', //省名
					// cityCode: '', //市编号
					cityName: '', //市名
					// areaCode: '', //区编号
					areaName: '', //区名
					areaCode: '', //区域编号
					house: '', //详细门牌街道信息,
					address: ''
				},
				addressDetail: '', //所属地区地址
			}
		},
		onLoad(options) {
			if (options && options.applyId) {
				this.applyId = options.applyId
			}
			this.getRegion();
			this.getDetail()
		},
		methods: {
			modify() {
				this.isLoading = true
				let address = this.formData.provinceName + this.formData.cityName + this.formData.areaName + this.formData
					.house
				let params = {
					id: this.applyId,
					address: address,
					provinceName: this.formData.provinceName,
					cityName: this.formData.cityName,
					areaName: this.formData.areaName,
					areaCode: this.formData.areaCode,
					house: this.formData.house,
					type: '1',
					contactsName: this.formData.contactsName,
					contactsTelephone: this.formData.contactsTelephone
					// linkAddress: this.formData.contactsName,
					// linkPhone: this.formData.contactsTelephone
				}

				this.$request.post(this.$interfaces.applyUpdate, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.reLaunch({
							url: '/pagesA/newBusiness/order/orderDetail?applyId=' + this.applyId
						})

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			getDetail() {
				this.isLoading = true
				let params = {
					id: this.applyId
				}

				this.$request.post(this.$interfaces.applyOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('详情信息=========>>>>>', res)
						let result = res.data.applyRecordVO
						this.formData.contactsName = result.contactsName || ''
						this.formData.contactsTelephone = result.contactsTelephone || ''
						this.formData.provinceName = result.provinceName || ''
						this.formData.cityName = result.cityName || ''
						this.formData.areaName = result.areaName || ''
						this.formData.areaCode = result.areaCode || ''
						this.formData.house = result.house || ''


						// //地区信息回显
						// if (this.formData.provinceName && this.formData.cityName && this.formData.areaName) {
						// 	this.options = [this.formData.provinceName + ' ' + this.formData.cityName + ' ' +
						// 		this.formData.areaName
						// 	]
						// }
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			getRegion() {
				let params = {};
				this.$request.post(this.$interfaces.getRegion, {
					data: params
				}).then(res => {
					let list = [];
					if (res.code == 200) {
						list = res.data;
						for (let i = 0; i < list.length; i++) {
							list[i].children = list[i].child;
							list[i].text = list[i].label;
							for (let j = 0; j < list[i].children.length; j++) {
								list[i].children[j].children = list[i].children[j].child;
								list[i].children[j].text = list[i].children[j].label;
								for (let k = 0; k < list[i].children[j].children.length; k++) {
									list[i].children[j].children[k].children = list[i].children[j].children[k]
										.child;
									list[i].children[j].children[k].text = list[i].children[j].children[k].label;
								}
							}
						}
					}
					let gxArrList = list.filter(item => {
						return item.value == '450000'
					})
					this.regionList = gxArrList;
					//默认选择广西
					// console.log('province_code', this.regionList[12].value)
					// console.log('province_name', this.regionList[12].label)
					// this.formData.province_code = this.regionList[12].value
					// this.formData.province_name = this.regionList[12].label
				})
			},
			onpopupopened(e) {
				console.log('popupopened');
				this.regionShow = true
			},
			onpopupclosed(e) {
				console.log('popupclosed');
				this.regionShow = false
			},
			onchange(e) {
				if (e.detail.value.length == 0) return;
				console.log('---------onchange:', e);

				// this.formData.provinceCode = e.detail.value[0].value
				this.formData.provinceName = e.detail.value[0].text
				// this.formData.cityCode = e.detail.value[1].value
				this.formData.cityName = e.detail.value[1].text
				// this.formData.areaCode = e.detail.value[2].value
				this.formData.areaName = e.detail.value[2].text

				this.addressDetail = e.detail.value[0].text + ' ' + e.detail.value[1].text + ' ' + e.detail.value[2].text

				// this.$nextTick(() => {
				// 	this.options = [this.addressDetail]
				// })

				console.log('地区赋值=======>>', this.addressDetail, this.formData.areaCode)
			},
			//获取微信地址信息
			autoFill() {
				console.log('微信调用地址1')
				uni.authorize({
					scope: 'scope.address',
					success: () => {
						console.log('success')
						this.getWxAddress()
					},
					fail: () => {
						console.log('fail')
					},
					complete() {
						console.log('complete')
					}
				})
			},
			getWxAddress() {
				uni.chooseAddress({
					success: user => {
						if (user.provinceName != '广西壮族自治区') {
							uni.showModal({
								title: '提示',
								content: '目前仅支持广西区内发货',
								showCancel: false
							});
							return
						}
						//微信获取地址信息赋值
						this.formData.contactsName = user.userName
						this.formData.contactsTelephone = user.telNumber
						this.addressDetail = user.provinceName + ' ' + user.cityName + ' ' + user.countyName

						// this.$nextTick(() => {
						// 	this.options = [this.addressDetail]
						// 	this.$refs.picker.clear()
						// })
						this.formData.areaCode = user.nationalCode

						this.formData.provinceName = user.provinceName
						this.formData.cityName = user.cityName
						this.formData.areaName = user.countyName
						this.formData.house = user.detailInfo

						console.log('this.formdata', this.formData)
					}
				})
			},
			onclick() {
				this.$emit('click')
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-address__detail {
		padding: 20rpx;
	}

	.order-info {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding-bottom: 20rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}

	.status-value.info {
		background: #F6F6F6;
		color: #333333;
	}


	.status-value.success {
		background: rgba(0, 189, 50, 0.1);
		color: #00BD32;
	}

	.status-value.warnning {
		background: rgba(255, 145, 0, 0.1);
		color: #FF9100;
	}

	.status-value.error {
		background: rgba(255, 84, 84, 0.1);
		color: #FF5454;
	}

	.address-wrapper {
		// margin: 20rpx;
		padding-bottom: 50rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
	}

	.title-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx 30rpx 0 30rpx;

		.title {
			font-size: 30rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #323435;
		}
	}

	.wx-location {
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #3874FF;
	}

	.ios-textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-top: 45rpx;
	}

	.textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-bottom: 38rpx;
	}

	.picker {
		width: calc(100vw - 160rpx);
		flex: 1;
		height: 120rpx;
		display: flex;
		align-items: center;
		color: #777777;
		overflow: hidden;

		// line-height: 120rpx;
		// text-align: center;
	}

	/deep/.uni-data-tree {
		width: 100%;
		line-height: 66rpx;
	}


	.install {
		margin: 20rpx;
		background-color: $uni-bg-color;
		padding-bottom: 10rpx;
		border-radius: 12rpx;

		.title-container {
			padding: 30rpx;
		}

		.input-wrapper {
			padding: 0 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 80rpx;
			line-height: 40rpx;
			color: rgba(16, 16, 16, 100);
			background-color: $uni-bg-color;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #999999;

			.input-title {
				flex: 0 0 180rpx;
				width: 180rpx;
				// text-align: left;
			}

			.input {
				flex: 1;
				// width: calc(100% - 180rpx);
				// justify-content: flex-end;
				// text-align: left;
				height: 60rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				border: 2rpx solid #DDDDDD;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				padding-left: 20rpx;
			}

			// .textarea-input {
			// 	height: 120rpx;
			// }

			// .address {
			// 	flex-wrap: wrap;
			// }

			.auto-input {
				position: relative;
			}

			// .autofill {
			// 	position: absolute;
			// 	right: 30rpx;
			// 	// background-color: $my-theme-color;
			// 	// color: $uni-bg-color;
			// 	// border-radius: 12rpx;
			// 	height: 120rpx;
			// 	line-height: 120rpx;
			// 	padding: 0 20rpx;
			// 	color: #0081FF;
			// 	z-index: 98;
			// }
		}
	}

	.icon-location {
		position: absolute;
		width: 15px;
		height: 15px;
		right: 29px;
		pointer-events: none;
	}

	//地区级联组件样式修改
	/deep/.uni-data-tree {
		width: 100%;

		.input-value-border {
			flex: 1;
			height: 60rpx;
			background: #FFFFFF;
			border-radius: 8rpx;
			border: 2rpx solid #DDDDDD;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			padding-left: 20rpx;
			padding-right: 30rpx;

			.selected-area {
				font-size: 30rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 42rpx;

				.selected-list {
					padding: 0;
				}

				.selected-item {
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;
					padding-right: 10rpx;

					.input-split-line {
						display: none;
					}
				}
			}

			.arrow-area {
				display: none;
			}
		}
	}
</style>