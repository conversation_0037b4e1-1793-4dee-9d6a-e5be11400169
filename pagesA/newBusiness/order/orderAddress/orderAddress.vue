<template>
	<view class="order-address">
		<tTitle v-if="result.nodeCode == '3060'" title="取货信息" setPadding="30">
		</tTitle>
		<tTitle v-if="result.nodeCode != '3060'" title="取货信息" setPadding="30" rightText="修改" @click="onclick">
		</tTitle>
		<view class="status-wrapper">
			<view class="status-label">
				安装方式：
			</view>
			<view class="status-value info">
				快递邮寄
			</view>
		</view>
		<view class="address-wrapper">
			<view class="img">
				<image style="width: 40rpx;height: 41rpx;margin-top: 2rpx;"
					src="../../../static/new-apply/order/icon-location.png" mode="aspectFill">
				</image>
			</view>
			<view class="address">
				{{address}}
			</view>
		</view>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	export default {
		props: {
			address: {
				type: String
			},
			result: {
				type: Object
			}
		},
		components: {
			tLoading,
			tTitle
		},
		data() {
			return {}
		},
		created() {
			console.log('result', this.result)
		},
		methods: {
			onclick() {
				this.$emit('click')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-address {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
		align-items: center;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}

	.status-value.info {
		background: #F6F6F6;
		color: #333333;
	}


	.status-value.success {
		background: rgba(0, 189, 50, 0.1);
		color: #00BD32;
	}

	.status-value.warnning {
		background: rgba(255, 145, 0, 0.1);
		color: #FF9100;
	}

	.status-value.error {
		background: rgba(255, 84, 84, 0.1);
		color: #FF5454;
	}

	.address-wrapper {
		padding: 30rpx;
		display: flex;
	}

	.address {
		margin-left: 25rpx;
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
		line-height: 40rpx;
	}
</style>