<template>
	<view class="order">
		<handle-step :current="4" />
		<view class="card-warpper">
			<newTitle title="已选择产品" setPadding="30"></newTitle>
			<view class="card-item card-selector">
				<image class="img" src="../../static/new-apply/product-select/icon-card_1_check.png"
					v-if="result.productType == '5'"></image>
				<image class="img" v-if="result.productType == '10'"
					src="../../static/new-apply/product-select/icon-card_2_check.png" mode=""></image>
				<view class="card-text">
					<view class="card-name">
						{{result.productType == '5'?'捷通日日通记账卡':'捷通次次顺记账卡'}}
					</view>
					<view class="card-desc">
						{{result.productType == '5'?'预存式记账卡，灵活多样的充值方式':'绑定银行卡代扣，先通行后扣费'}}
					</view>
					<view class="info-warpper">
						<view class="info">
							{{result.customerName}}
						</view>
						<view class="info">
							{{result.vehicleCode}}
						</view>
						<view class="info">
							{{vehicleColorStr}}色
						</view>
					</view>
				</view>
			</view>
			<tTitle title="权益服务费" :desc="'￥'+benefitServiceFee">
			</tTitle>
			<tTitle title="激活保证金" :desc="'￥'+activationDeposit" v-if="result.productType != '10'">
			</tTitle>
			<tTitle title="支付方式">
				<template v-slot>
					<view class="vux-x-input weui-cell weui-cell_picker">
						<!-- 					<view class="weui-cell__hd">
							<view class="weui-label weui-label__require">证件类型</view>
						</view> -->
						<!-- <view class="weui-cell__bd weui-cell__primary"> -->
						<picker style="width:100%;" :range="payTypeList" range-key="label">
							<view class="weui-picker-value">{{payTypeList[pay_index].label}}</view>
						</picker>
						<!-- </view> -->
						<image style="width: 30rpx;height: 28rpx" src="../../static/new-apply/personal/arrow-right.png"
							mode=""></image>
					</view>
				</template>
			</tTitle>
		</view>
		<view class="pay-money">
			支付总金额：<text class="money-font">{{totalPrice}}元</text>
		</view>
		<!-- 非高频 -->
		<view class="final-tips" v-if="!isVip">
			<view class="desc">温馨提示：</view>
			<view class="desc" style="margin-top: 10rpx;">支付成功后，后台会尽快为您审核订单并邮寄设备。</view>
		</view>
		<!-- 高频 -->
		<view class="final-tips" v-else>
			<view class="desc">温馨提示：</view>
			<view class="desc" style="margin-top: 10rpx;" v-if="benefitServiceFee == 0">快递邮寄需预付设备激活保证金，在激活后3个工作日内将原路退回</view>
			<view class="desc" style="margin-top: 10rpx;" v-if="benefitServiceFee == 0">获取免费办理资格后，您可享受的权益为免费充值提醒/消费记录提醒；</view>
			<view class="desc" style="margin-top: 10rpx;">支付成功后，后台会尽快为您审核订单并邮寄设备</view>
		</view>
		<u-modal v-model="uShow" @confirm="uShowConfirm" confirm-text="前往查看" :content="limitContent" ref="uModal"
			:async-close="true">
		</u-modal>
		<tButton :buttonList="buttonList" @toNext="toNext" @toLast="toLast"></tButton>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/t-title/t-title.vue'
	import newTitle from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import float from '@/common/method/float.js'

	import {
		getOpenid,
		setOpenid
	} from '@/common/storageUtil.js';
	import {
		getVehicleColor
	} from '@/common/method/filter.js';
	export default {
		components: {
			handleStep,
			tLoading,
			tTitle,
			newTitle,
			tButton
		},
		data() {
			return {
				isLoading: false,
				uShow: false,
				nodeStep: '6',
				limitContent: '',
				buttonList: [
					// 	{
					// 	title: '上一步',
					// 	handle: 'toLast'
					// }, 
					{
						title: '支付并下单',
						handle: 'toNext'
					}
				],
				pay_index: 0,
				payTypeList: [{
					value: '0',
					label: '微信支付'
				}],
				applyId: '',
				priceList: {},
				totalPrice: '',
				benefitServiceFee: '',
				activationDeposit: '',
				result: {},
				openId: '',
				isVip: false //高频车
			}
		},
		computed: {
			vehicleColorStr() {
				return getVehicleColor(this.result.vehicleColor);
			},
		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
		},
		methods: {
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			getDraft() {
				this.isLoading = true
				let params = {
					id: this.applyId
				}
				console.log('支付页面', this.$store)
				this.$request.post(this.$interfaces.getDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						// //获取所有草稿
						let result = res.data
						this.result = result
						console.log('草稿==========>>>>', this.result)
						this.applyId = result.id
						this.getPackagePrice(result)
						this.checkIsFreeVehicle(result)

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			//高频车校验
			checkIsFreeVehicle(result) {
				this.isLoading = true
				this.$request.post(this.$interfaces.checkIsFreeVehicle, {
					data: {
						vehicleCode: result.vehicleCode,
						vehicleColor: result.vehicleColor
					}
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('isVip====>>>>>', res)
						// this.isVip = true
						res.data.isFree == '0' ? this.isVip = false : this.isVip = true
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			getPackagePrice(result) {
				let params = {
					installType: '1', //安装方式; 1：快递邮寄,0: 网点自提
					salesChannels: '0', //销售渠道;0 - C端小程序
					carNo: result.vehicleCode,
					carColor: result.vehicleColor,
					productCode: result.productType,
					isTrunk: result.vehicleType,
					deviceType: result.deviceType //默认基础款
				}
				this.$request.post(this.$interfaces.getPackagePrice, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						// 
						console.log('res,taa', res.data)
						let priceList = res.data
						this.priceList = priceList
						this.benefitServiceFee = float.div(priceList.benefitServiceFee, 100)
						this.activationDeposit = float.div(priceList.activationDeposit, 100)
						this.totalPrice = float.add(this.benefitServiceFee, this.activationDeposit)

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			toNext() {
				//支付
				this.payment()

			},
			payment() {
				let _self = this;
				this.isLoading = true;
				let params = {
					activationDeposit: this.priceList.activationDeposit,
					benefitServiceFee: this.priceList.benefitServiceFee,
					applyProductConfigId: this.priceList.id,
					applyId: this.applyId,
					openId: this.openId,
					payMoney: float.add(this.priceList.activationDeposit, this.priceList.benefitServiceFee),
					payType: '10000601',
					source: '3',
				}
				console.log('入参params', params)
				let data = {
					routePath: this.$interfaces.applyPay.method,
					bizContent: params
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;
						console.log(res, '支付');
						if (res.code == 200) {
							let result = res.data;
							let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};

							wx.requestPayment({
								...payMessage,
								success: function(successres) {
									_self.isLoading = true
									console.log(successres, '支付成功回调', _self.isLoading);
									setTimeout(() => {
										_self.applyPayOrderQuery(result);
									}, 6000);
									// _self.updatePayStatus(res.data.orderId, 2)
								},
								fail: function(err) {
									console.log('fail', err)
									_self.updatePayStatus(res.data.orderId, 3)
								},
							});
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			applyPayOrderQuery(data) {
				let params = {
					routePath: this.$interfaces.applyPayOrderQuery.method,
					bizContent: {
						orderId: data.orderId
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					console.log('data===============>>>>>>>>>>', res)
					let status = res.data.status;
					let statusVal = {
						1: "支付中",
						2: "支付成功",
						3: "支付失败",
					};
					let msg = statusVal[status] || "支付中";

					//弹框
					this.uShow = true
					this.limitContent = msg + ',（付款后订单可能会有延迟)'
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			uShowConfirm() {
				// this.saveDraft()
				this.uShow = false
				// this.$store.dispatch('setIssueVehicleInfo', {
				// 	gxCardType: this.result.gxCardType,
				// 	customerId: this.result.customerId,
				// 	vehicleCode: this.result.vehicleCode,
				// 	vehicleColor: this.result.vehicleColor
				// })
				uni.reLaunch({
					url: '/pagesA/newBusiness/order/orderSuccess?type=1'
				})
			},
			saveDraft() {
				//保存草稿
				this.isLoading = true
				// let params = this.formData
				// console.log(params, '入参');
				this.$request.post(this.$interfaces.saveDraft, {
					data: {
						nodeStep: this.nodeStep,
						id: this.applyId
					}
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						// //获取所有草稿
						// this.getDraft()
						//缓存激活车辆信息
						// this.$store.dispatch('setIssueVehicleInfo', {
						// 	gxCardType: this.result.gxCardType,
						// 	customerId: this.result.customerId,
						// 	vehicleCode: this.result.vehicleCode,
						// 	vehicleColor: this.result.vehicleColor
						// })
						uni.reLaunch({
							url: '/pagesA/newBusiness/order/orderSuccess?type=1'
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			updatePayStatus(orderId, status) {
				let params = {
					routePath: this.$interfaces.afterPayUpdate.method,
					bizContent: {
						orderId: orderId,
						status: status,
						payBusinessType: 1
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					// if (res.code == 200 && status == 2) {
					// 	// this.clearDraft() //清除草稿
					// 	//支付成功且更新状态成功
					// 	this.nextFlag = true
					// 	uni.reLaunch({
					// 		url: '/pagesA/order/orderList'
					// 	});
					// }
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			toLast() {
				uni.navigateBack({
					delta: 1
				})
			},
		},
		onLoad(options) {
			this.openId = getOpenid() || ''
			console.log('options', options)
			if (options) {
				// //从订单列表过来，不显示上一步
				// if (options.type == 'notNext') {
				// 	this.buttonList.shift()
				// }

				if (options.applyId) {
					this.applyId = options.applyId
					this.getDraft()
				}

			}
			console.log('this.$store.state', this.$store)
		},
		// onUnload() {
		// 	if (this.nextFlag) return;
		// 	let currentRoutes = getCurrentPages();
		// 	console.log('currentRoutes', currentRoutes)
		// 	let lastRoutes = currentRoutes[currentRoutes.length - 2].route
		// 	if (lastRoutes == 'pagesA/newBusiness/userAgreement/agreementList') return;
		// 	uni.navigateTo({
		// 		url: '/pagesA/newBusiness/userAgreement/agreementList'
		// 	})
		// },
	}
</script>

<style lang="scss" scoped>
	.order {
		padding-bottom: 180rpx;
		padding-top: 184rpx;
		background-color: #f8f8f8;

		.card-warpper {
			background-color: $uni-bg-color;
			margin: 20rpx;
			border-radius: 12rpx;

			.card-item {
				margin-bottom: 20rpx;
				display: flex;
				// align-items: center;
				// height: 128rpx;
				background: #F8F8F8;
				border-radius: 12rpx;
				padding: 25rpx 33rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #323435;
				margin: 0 30rpx 30rpx 30rpx;

				&>image {
					margin-right: 28rpx;
					margin-top: 10rpx;
					width: 56rpx;
					height: 56rpx;
				}

				.card-name {
					font-size: 28rpx;
					line-height: 40rpx;

				}

				.card-desc {
					line-height: 33rpx;
					font-size: 24rpx;
				}

			}

			.card-selector {
				background: #E4EFFF;
				border-radius: 12rpx;
				// border: 2rpx solid #009ff6;
				// border-image: linear-gradient(180deg, rgba(0, 159, 246, 1), rgba(0, 102, 233, 1)) 2 2;
				// clip-path: inset(0 round 12rpx);

				.card-type,
				.card-desc {
					// color: $uni-bg-color;
				}
			}
		}

		.pay-money {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			margin: 50rpx 30rpx 10rpx 0;
			font-size: 30rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;

			.money-font {
				margin-left: 30rpx;
				font-size: 42rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 700;
				color: #F65B5B;

			}
		}

		.final-tips {
			background: #EFEFEF;
			border-radius: 8rpx;
			margin: 30rpx;
			padding: 30rpx;

			.desc {
				line-height: 40rpx;
				// text-indent: 30rpx;

				&:first-child {
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FF9038;
				}
			}
		}

		.weui-cell {
			padding: 0;
		}

		.info-warpper {
			display: flex;
			margin-top: 25rpx;
		}

		.info {
			margin-right: 50rpx;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;
		}

		/deep/.title-wrapper {
			border-bottom-left-radius: 12rpx;
			border-bottom-right-radius: 12rpx;
		}
	}
</style>