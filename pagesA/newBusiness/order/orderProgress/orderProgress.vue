<template>
	<view class="order-progress">
		<tTitle title="办理进度" setPadding="30" rightText="查看详情" @click="onclick">
		</tTitle>
		<view class="status-wrapper">
			<view class="status-label">
				订单状态：
			</view>
			<view class="status-value" :class="statusInfoList[result.hsApplyStatusId].status">
				{{result.codeName}}
			</view>
		</view>
		<view class="progress-tip">
			{{statusText}}
		</view>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	import {
		statusInfoList
	} from '@/pagesA/common/optionsData.js'
	export default {
		props: {
			result: {
				type: Object
			},

		},
		components: {
			tLoading,
			tTitle
		},
		data() {
			return {
				statusInfoList
			}
		},
		computed: {
			statusText() {
				//获取是否展示自定义信息状态
				let isShowInfo = this.statusInfoList[this.result.hsApplyStatusId].showInfo
				if (!(this.result.hsApplyStatusId == '18' || this.result.hsApplyStatusId == '21')) {
					//换货退货提示不一样
					if (isShowInfo) {
						return this.statusInfoList[this.result.hsApplyStatusId].statusInfo
					} else {
						return this.result.reviewComments
					}
				} else if (this.result.hsApplyStatusId == '10') {
					//2023.5.24 dwz权益费为0修改描述
					if (this.result.configReqVO.benefitServiceFee == '0') {
						return '已发行激活'
					}
				} else {
					if (isShowInfo) {
						let str = 'statusInfo' + this.result.routeType
						return this.statusInfoList[this.result.hsApplyStatusId][str]
					} else {
						return this.result.reviewComments
					}
				}

			}
		},
		created() {
			// console.log('codeName', this.codeName)
		},
		methods: {
			onclick() {
				this.$emit('click')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-progress {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
		align-items: center;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}

	.status-value.info {
		background: #F6F6F6;
		color: #333333;
	}


	.status-value.success {
		background: rgba(0, 189, 50, 0.1);
		color: #00BD32;
	}

	.status-value.warnning {
		background: rgba(255, 145, 0, 0.1);
		color: #FF9100;
	}

	.status-value.error {
		background: rgba(255, 84, 84, 0.1);
		color: #FF5454;
	}

	.progress-tip {
		padding: 30rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #999999;
	}
</style>