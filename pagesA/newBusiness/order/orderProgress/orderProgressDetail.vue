<template>
	<view class="time-line__wrapper">
		<view class="time-line">
			<u-time-line>
				<u-time-line-item nodeTop="6">
					<!-- 此处自定义了左边内容，用一个图标替代 -->
					<template v-slot:node>
						<view class="steps-wrapper">
							<view class="steps-arrow">
								<view class="steps-hd">
									<view class="steps-item">
									</view>
								</view>
							</view>
						</view>
					</template>
					<template v-slot:content>
						<view>
							<view class="u-order-title">{{timeListFirst[0].nodeName}}</view>
							<view class="u-order-desc">{{timeListFirst[0].remark}}</view>
							<view class="u-order-time">{{timeListFirst[0].createdTime}}</view>
						</view>
					</template>
				</u-time-line-item>
				<u-time-line-item v-for="(item,index) in timeList" :key="index">
					<template v-slot:content>
						<view>
							<view class="u-order-title">{{item.nodeName}}</view>
							<view class="u-order-desc">{{item.remark}}</view>
							<view class="u-order-time">{{item.createdTime}}</view>
						</view>
					</template>
				</u-time-line-item>
			</u-time-line>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isLoading: false,
				applyId: '',
				timeList: [],
				timeListFirst: []
			}
		},
		onLoad(options) {
			if (options && options.applyId) {
				this.applyId = options.applyId
			}
			this.getTimeLine()
		},
		methods: {
			getTimeLine() {
				this.isLoading = true
				let params = {
					id: this.applyId
				}

				this.$request.post(this.$interfaces.getTimeLineList, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('时间轴========>>>>>', res)
						//拆分数组，要不然组件会有样式问题
						this.timeListFirst.push(res.data[0])
						let arrList = JSON.parse(JSON.stringify(res.data))
						arrList.shift()
						this.timeList = arrList
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.time-line__wrapper {
		margin: 0 20rpx;
		background: #ffffff;
		border-radius: 12rpx;
	}

	.time-line {
		margin: 20rpx;
		padding: 30rpx;

	}

	.u-node {
		width: 44rpx;
		height: 44rpx;
		border-radius: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #d0d0d0;
	}

	.u-order-title {
		margin-bottom: 8rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #333333;
	}

	.u-order-desc {
		margin-bottom: 8rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.u-order-time {
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	/deep/.u-dot {
		width: 17rpx !important;
		height: 17rpx !important;
		background: #D4D4D4 !important;
	}

	/deep/.u-time-axis::before {
		width: 3rpx !important;
		background: #D4D4D4 !important;
	}

	// /deep/.u-time-axis-node {
	// 	top: 12rpx !important;
	// }

	.steps-wrapper {
		position: relative;
		height: 27rpx;
		// justify-content: center;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.steps-arrow {
		position: relative;
		width: 27rpx;
		height: 27rpx;
		background: rgba(150, 193, 255, 0.82);
		border-radius: 50%;
		bottom: 0;
	}

	.steps-hd {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
		width: 17rpx;
		height: 17rpx;
		background: #5591FF;
		border-radius: 50rpx;
	}

	// .steps-item {
	// 	position: absolute;
	// 	left: 0;
	// 	right: 0;
	// 	top: 0;
	// 	bottom: 0;
	// 	margin: auto;
	// 	width: 6rpx;
	// 	height: 6rpx;
	// 	background: #FFFFFF;
	// 	border-radius: 50rpx;
	// }

	.steps-title {
		color: #323435;
		font-weight: 700;
	}
</style>
