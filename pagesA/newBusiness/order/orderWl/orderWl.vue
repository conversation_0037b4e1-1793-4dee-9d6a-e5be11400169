<template>
	<view class="order-wl">
		<tTitle title="物流信息" setPadding="30" rightText="查看详情" @click="onclick">
		</tTitle>
		<view class="title-wrapper">
			<view class="title">
				<view class="img">
					<image style="width: 40rpx;height: 40rpx;" src="../../../static/new-apply/order/icon-wl.png"
						mode="aspectFill"></image>
				</view>
				<view style="width: 100%;">
					<view class="title-container">
						<view class="number-wrapper">
							<view class="text">
								顺丰快递
							</view>
							<view class="number">
								{{sfRouteResInfoDTO.waybillNos}}
							</view>
						</view>
						<view class="copy" @click="copy(sfRouteResInfoDTO.waybillNos)">
							复制
						</view>
					</view>
					<view class="address-wrapper">
						<view class="address">
							收货地址：{{address}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="time-line__wrapper">
			<view class="time-line" v-if="!isShowMore && timeListFirst.length > 0">
				<u-time-line>
					<u-time-line-item nodeTop="6">
						<template v-slot:node>
							<view class="steps-wrapper">
								<view class="steps-arrow">
									<view class="steps-hd">
										<view class="steps-item">
										</view>
									</view>
								</view>
							</view>
						</template>
						<template v-slot:content>
							<view>
								<view class="u-order-title">{{timeListFirst[0].opName}}</view>
								<view class="u-order-title">{{timeListFirst[0].remark}}</view>
								<view class="u-order-desc">{{timeListFirst[0].acceptTime}}</view>
								<view class="u-order-time">{{timeListFirst[0].acceptAddress}}</view>
							</view>
						</template>
					</u-time-line-item>
				</u-time-line>
			</view>
			<view class="time-line" v-if="isShowMore && timeListFirst.length > 0">
				<u-time-line>
					<u-time-line-item nodeTop="6">
						<!-- 此处自定义了左边内容，用一个图标替代 -->
						<template v-slot:node>
							<view class="steps-wrapper">
								<view class="steps-arrow">
									<view class="steps-hd">
										<view class="steps-item">
										</view>
									</view>
								</view>
							</view>
						</template>
						<template v-slot:content>
							<view>
								<view class="u-order-title">{{timeListFirst[0].opName}}</view>
								<view class="u-order-title">{{timeListFirst[0].remark}}</view>
								<view class="u-order-desc">{{timeListFirst[0].acceptTime}}</view>
								<view class="u-order-time">{{timeListFirst[0].acceptAddress}}</view>
							</view>
						</template>
					</u-time-line-item>
					<u-time-line-item v-for="(item,index) in timeList" :key="index">
						<template v-slot:content>
							<view>
								<view class="u-order-title">{{item.opName}}</view>
								<view class="u-order-title">{{item.remark}}</view>
								<view class="u-order-desc">{{item.acceptTime}}</view>
								<view class="u-order-time">{{item.acceptAddress}}</view>
							</view>
						</template>
					</u-time-line-item>
				</u-time-line>
			</view>

		</view>

		<view class="show-more" @click="showMore" v-if="timeListFirst.length > 0">
			<view class="icon-wrapper g-flex g-flex-align-center">
				<view class="icon-text">
					{{isShowMore?'收起':'展开'}}
				</view>
				<image v-if="!isShowMore" style="width: 24rpx;height: 24rpx;"
					src="../../../static/new-apply/order/arrow-show.png" mode="aspectFill">
				</image>
				<image v-else style="width: 24rpx;height: 24rpx;" src="../../../static/new-apply/order/arrow-up.png"
					mode="aspectFill">
				</image>
			</view>
		</view>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	export default {
		props: {
			sfRouteResInfoDTO: {
				type: Object
			},
			address: {
				type: String
			}
		},
		components: {
			tLoading,
			tTitle
		},
		data() {
			return {
				isShowMore: false,
				timeListFirst: [],
				timeList: {}
			}
		},
		created() {
			if (this.sfRouteResInfoDTO.sfRouteResInfos) {
				let wlInfo = this.sfRouteResInfoDTO.sfRouteResInfos
				this.timeListFirst.push(wlInfo[0])
				let arrList = JSON.parse(JSON.stringify(wlInfo))
				arrList.shift()
				this.timeList = arrList
			}
		},
		methods: {
			copy(value) {
				uni.setClipboardData({
					data: value,
					// success: () => {
					// 	uni.showModal({
					// 		title: '复制',
					// 		content: '已复制物流编号' + value
					// 	})
					// }
				});
			},
			showMore() {
				this.isShowMore = !this.isShowMore
			},
			onclick() {
				this.$emit('click')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-wl {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
		align-items: center;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}

	.status-value.info {
		background: #F6F6F6;
		color: #333333;
	}


	.status-value.success {
		background: rgba(0, 189, 50, 0.1);
		color: #00BD32;
	}

	.status-value.warnning {
		background: rgba(255, 145, 0, 0.1);
		color: #FF9100;
	}

	.status-value.error {
		background: rgba(255, 84, 84, 0.1);
		color: #FF5454;
	}

	.address-wrapper {
		margin-right: 30rpx;
		margin-top: 10rpx;
		padding-bottom: 20rpx;
		// display: flex;
	}

	.title-wrapper {
		margin-top: 10rpx;
	}

	.title {
		display: flex;
	}

	.img {
		flex: 0 0 90rpx;
		width: 90rpx;
		text-align: center;
	}

	.title-container {
		flex: 1;
		display: flex;
		justify-content: space-between;
	}

	.number-wrapper {
		display: flex;
	}

	.number-wrapper .text {
		line-height: 44rpx;
		margin-right: 19rpx;
	}
	
	.number-wrapper .number {
		line-height: 44rpx;
	}

	.copy {
		margin-right: 34rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #3874FF;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
	}

	//物流CSS
	.time-line__wrapper {
		margin: 0 20rpx;
		background: #ffffff;
		border-radius: 12rpx;
	}

	.time-line {
		margin: 30rpx 20rpx 20rpx 30rpx;
		// padding: 30rpx;

	}

	.u-node {
		width: 44rpx;
		height: 44rpx;
		border-radius: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #d0d0d0;
	}

	.u-order-title {
		margin-bottom: 8rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #333333;
	}

	.u-order-desc {
		margin-bottom: 8rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.u-order-time {
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	/deep/.u-dot {
		width: 17rpx !important;
		height: 17rpx !important;
		background: #D4D4D4 !important;
	}

	/deep/.u-time-axis::before {
		width: 3rpx !important;
		background: #D4D4D4 !important;
	}

	// /deep/.u-time-axis-node {
	// 	top: 12rpx !important;
	// }

	.steps-wrapper {
		position: relative;
		height: 27rpx;
		// justify-content: center;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.steps-arrow {
		position: relative;
		width: 27rpx;
		height: 27rpx;
		background: rgba(150, 193, 255, 0.82);
		border-radius: 50%;
		bottom: 0;
	}

	.steps-hd {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
		width: 17rpx;
		height: 17rpx;
		background: #5591FF;
		border-radius: 50rpx;
	}

	// .steps-item {
	// 	position: absolute;
	// 	left: 0;
	// 	right: 0;
	// 	top: 0;
	// 	bottom: 0;
	// 	margin: auto;
	// 	width: 6rpx;
	// 	height: 6rpx;
	// 	background: #FFFFFF;
	// 	border-radius: 50rpx;
	// }

	.steps-title {
		color: #323435;
		font-weight: 700;
	}

	.show-more {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0 30rpx 30rpx 30rpx;
	}

	.icon-text {
		margin-right: 4rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #656565;
	}
</style>