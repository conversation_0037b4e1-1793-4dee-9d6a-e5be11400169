<template>
	<view class="order-product__detail">
		<view class="card-warpper">
			<tTitle title="ETC产品详情" setPadding="20"></tTitle>
			<view class="card-item card-selector">
				<image class="img" src="../../../static/new-apply/product-select/icon-card_1_check.png"
					v-if="configReqVO.productCode == '5'"></image>
				<image class="img" v-if="configReqVO.productCode == '10'"
					src="../../../static/new-apply/product-select/icon-card_2_check.png" mode=""></image>
				<view class="card-text">
					<view class="card-name">
						{{configReqVO.productCode == '5'?'捷通日日通记账卡':'捷通次次顺记账卡'}}
					</view>
					<view class="card-desc">
						{{configReqVO.productCode == '5'?'预存式记账卡，灵活多样的充值方式':'绑定银行卡代扣，先通行后扣费'}}
					</view>
					<view class="info-warpper">
						<view class="info">
							{{applyRecordVO.customerName}}
						</view>
						<view class="info">
							{{applyRecordVO.vehicleCode}}
						</view>
						<view class="info">
							{{vehicleColorStr}}色
						</view>
					</view>
				</view>
			</view>
			<view class="item-wrapper">
				<view class="item-bd">
					<view class="item-wrapper__label">
						权益服务费
					</view>
					<view class="iteml-wrapper__value">
						￥{{moneyFilter(configReqVO.benefitServiceFee)}}
					</view>
				</view>
				<view class="item-bd" v-if="configReqVO.productCode != '10'">
					<view class="item-wrapper__label">
						激活保证金
					</view>
					<view class="item-wrapper__value">
						￥{{moneyFilter(configReqVO.activationDeposit)}}
					</view>
				</view>
				<view class="item-bd" v-if="configReqVO.productCode == '10'">
					<view class="item-wrapper__label">
						免密代扣渠道
					</view>
					<view class="item-wrapper__value">
						{{silkyBinding.payOrgId || ''}}
					</view>
				</view>
				<view class="item-bd" v-if="configReqVO.productCode == '10'">
					<view class="item-wrapper__label">
						免密签约时间
					</view>
					<view class="item-wrapper__value">
						<!-- {{moneyFilter(configReqVO.benefitServiceFee)}} -->
						{{silkyBinding.signTime || ''}}
					</view>
				</view>
				<view class="price-bd" v-if="configReqVO.productCode == '5'">
					<view class="price-wrapper__label">
						权益服务内容
					</view>
					<!-- 高频日日通  -->
					<!-- <view class="price-wrapper__value" v-if="isVip">
						<view class="tips-item">
							1.提供1套八桂行卡及蓝牙电子标签供用户使用；
						</view>
						<view class="tips-item">
							2.免费短信提醒/公众号消息提醒。
						</view>
					</view> -->
					<!-- 非高频日日通  -->
					<view class="price-wrapper__value">
						<view class="tips-item">
							1.赠送最高30元自助充值增值券1张；
						</view>
						<view class="tips-item">
							2.连续12个月免费领取50元/月商城消费券礼包；
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="user-wrapper" v-if="Object.keys(queryRes).length > 0">
			<tTitle title="用户协议" setPadding="20"></tTitle>
			<view class="sign-wrapper">
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						协议名称
					</view>
					<view class="sign-wrapper__value">
						{{queryRes.contractDoc}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						协议编号
					</view>
					<view class="sign-wrapper__value">
						{{queryRes.cid}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						签署人
					</view>
					<view class="sign-wrapper__value">
						{{queryRes.signName}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						签署时间
					</view>
					<view class="sign-wrapper__value">
						{{queryRes.createTime}}
					</view>
				</view>
				<view class="btn-wrapper">
					<view class="btn" @click="showRrt">
						协议详情
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="user-wrapper" v-if="Object.keys(silkyBinding).length > 0">
			<tTitle title="用户协议" setPadding="20"></tTitle>
			<view class="sign-wrapper">
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						协议名称
					</view>
					<view class="sign-wrapper__value">
						{{silkyBinding.contractDoc || '代收业务委托协议'}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						协议编号
					</view>
					<view class="sign-wrapper__value">
						{{silkyBinding.bindingSerialNo}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						签署人
					</view>
					<view class="sign-wrapper__value">
						{{silkyBinding.custName}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						证件号码
					</view>
					<view class="sign-wrapper__value">
						{{silkyBinding.custIdNo}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						银行卡号
					</view>
					<view class="sign-wrapper__value">
						{{silkyBinding.bankCardNo}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						手机号
					</view>
					<view class="sign-wrapper__value">
						{{silkyBinding.custMobile}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						签署时间
					</view>
					<view class="sign-wrapper__value">
						{{silkyBinding.createTime}}
					</view>
				</view>
				<view class="btn-wrapper">
					<view class="btn" @click="showCcs">
						协议详情
					</view>
				</view>
			</view>
		</view> -->
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getVehicleColor
	} from '@/common/method/filter.js';
	export default {
		components: {
			tLoading,
			tTitle
		},
		data() {
			return {
				isLoading: false,
				applyId: '',
				configReqVO: {},
				applyRecordVO: {},
				queryRes: {},
				silkyBinding: {},
				isVip: false //高频车
			}
		},
		computed: {
			vehicleColorStr() {
				return getVehicleColor(this.applyRecordVO.vehicleColor);
			},
		},
		onLoad(options) {
			if (options && options.applyId) {
				this.applyId = options.applyId
			}
			this.getDetail()
		},
		methods: {
			showCcs() {
				let params = {
					customer_name: this.silkyBinding.custName,
					certificates_code: this.silkyBinding.custIdNo,
					mobile: this.silkyBinding.custMobile,
					customerId: '',
					bankNo: this.silkyBinding.bankCardNo,
					isShow: '1',
				}
				uni.navigateTo({
					url: '/pagesA/newBusiness/signature/agreement?' + this.objToUrlParam(params)
				})
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},
			showRrt() {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.previewContracts.method,
					bizContent: {
						conId: this.queryRes.contractId,
						vehicleCode: this.queryRes.carNo,
						vehicleColor: this.queryRes.carColor
					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						uni.navigateTo({
							url: "/pages/uni-webview/h5-webview?ownPath=" + encodeURIComponent(res.data
								.viewUrl)
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "错误",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//高频车校验
			checkIsFreeVehicle(result) {
				this.isLoading = true
				this.$request.post(this.$interfaces.checkIsFreeVehicle, {
					data: {
						vehicleCode: result.vehicleCode,
						vehicleColor: result.vehicleColor
					}
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('isVip====>>>>>', res)
						// this.isVip = true
						res.data.isFree == '0' ? this.isVip = false : this.isVip = true
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			getDetail() {
				this.isLoading = true
				let params = {
					id: this.applyId
				}

				this.$request.post(this.$interfaces.applyOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('详情信息=========>>>>>', res)
						let result = res.data
						this.configReqVO = result.configReqVO || {}
						this.applyRecordVO = result.applyRecordVO || {}
						this.queryRes = result.queryRes || {}
						this.silkyBinding = result.silkyBinding || {}
						this.checkIsFreeVehicle(result.applyRecordVO)

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}

	}
</script>

<style lang="scss" scoped>
	.card-warpper {
		background-color: $uni-bg-color;
		margin: 20rpx;
		border-radius: 12rpx;

		.card-item {
			margin-bottom: 20rpx;
			display: flex;
			// align-items: center;
			// height: 128rpx;
			background: #F8F8F8;
			border-radius: 12rpx;
			padding: 25rpx 33rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;
			margin: 0 30rpx 30rpx 30rpx;

			&>image {
				margin-right: 28rpx;
				margin-top: 10rpx;
				width: 56rpx;
				height: 56rpx;
			}

			.card-name {
				font-size: 28rpx;
				line-height: 40rpx;

			}

			.card-desc {
				line-height: 33rpx;
				font-size: 24rpx;
			}

		}

		.card-selector {
			background: #E4EFFF;
			border-radius: 12rpx;
			// border: 2rpx solid #009ff6;
			// border-image: linear-gradient(180deg, rgba(0, 159, 246, 1), rgba(0, 102, 233, 1)) 2 2;
			// clip-path: inset(0 round 12rpx);

			.card-type,
			.card-desc {
				// color: $uni-bg-color;
			}
		}
	}

	.item-wrapper {
		margin: 0 30rpx;
		padding-bottom: 38rpx;
		// border-bottom: 1rpx dashed #C3C3C3;
	}

	.item-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.item-wrapper__label {
		flex: 0 0 160rpx;
		width: 160rpx;
		margin-right: 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.item-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}

	.price-bd {
		margin-bottom: 20rpx;
	}

	.price-wrapper__label {
		margin-right: 30rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.price-wrapper__value {
		margin-top: 30rpx;
		padding: 30rpx;
		background: #EFEFEF;
		border-radius: 8rpx;
		line-height: 44rpx;
		font-size: 26rpx;

		.tips-item {
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;
			line-height: 40rpx;
			margin-bottom: 10rpx;
		}
	}

	.user-wrapper {
		background-color: $uni-bg-color;
		margin: 20rpx;
		padding-bottom: 30rpx;
		border-radius: 12rpx;
	}

	.sign-wrapper {
		margin: 0 30rpx;
		padding: 30rpx;
		border-radius: 12rpx;
		border: 1rpx solid #E8E8E8;
	}

	.sign-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.sign-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.sign-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}


	.btn-wrapper {
		padding: 20rpx 20rpx 0 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-top: 1rpx dashed #C3C3C3;
	}

	.btn {
		width: 190rpx;
		height: 58rpx;
		line-height: 58rpx;
		background: #FFFFFF;
		border-radius: 36rpx;
		border: 2rpx solid #E8E8E8;
		text-align: center;
	}

	.info-warpper {
		display: flex;
		margin-top: 25rpx;
	}

	.info {
		margin-right: 50rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
	}
</style>