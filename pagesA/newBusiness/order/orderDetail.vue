<template>
	<view class="order-detail">
		<view class="order-detail__title">
			<view class="title-hd">
				<view class="title-hd__label">
					订单编号：
				</view>
				<view class="title-hd__value">
					{{applyId}}
				</view>
			</view>
			<view class="title-bd">
				<view class="copy" @click="copy(applyId)">
					复制
				</view>
				<!-- 上线先屏蔽 -->
				<!-- <view class="icon-question">
					<image style="width: 40rpx;height: 40rpx;margin-top: 6rpx;"
						src="../../static/new-apply/order/icon-question.png" mode="">
					</image>
				</view> -->
			</view>
		</view>
		<view class="section">
			<orderProgress v-if="Object.keys(result).length > 0" @click="toOrderProgress" :result="result">
			</orderProgress>
		</view>
		<view class="section">
			<orderAddress v-if="address && Object.keys(sfRouteResInfoDTO).length == 0" :result="result"
				@click="toAddress" :address="address">
			</orderAddress>
		</view>
		<view class="section" v-if="Object.keys(sfRouteResInfoDTO).length > 0">
			<orderWl v-if="Object.keys(sfRouteResInfoDTO).length > 0 && wlAddress" @click="toWl"
				:sfRouteResInfoDTO="sfRouteResInfoDTO" :address="wlAddress"></orderWl>
		</view>
		<view class="section">
			<orderInfo v-if="Object.keys(applyRecordVO).length > 0" @click="toInfo" :applyRecordVO="applyRecordVO">
			</orderInfo>
		</view>
		<view class="section">
			<!-- <orderWl v-if="Object.keys(sfRouteResInfoDTO).length > 0" @click="toWl" :sfRouteResInfoDTO="sfRouteResInfoDTO"></orderWl> -->
			<orderProduct v-if="Object.keys(configReqVO).length > 0" @click="toProduct" :configReqVO="configReqVO">
			</orderProduct>
		</view>
		<view class="last-wrapper" v-if="result.returnInfoDTO.afterOrder == '1'">
			已申请售后，<text style="color: #3874FF;" @click="toAfter('afterList')">点击查看售后记录</text>
		</view>
		<tButton v-if="buttonList.length > 0" :moreBtn="true" :buttonList="buttonList" @click="toHandle"></tButton>
		<tButton v-if="result.salePayFlag == '1'" :moreBtn="true" :buttonList="payButtonList" @click="toHandle">
		</tButton>
		<tButton v-if="result.sendBackFlag == '1'" :moreBtn="true" :buttonList="sendButtonList" @click="toHandle">
		</tButton>
		<tButton v-if="result.nodeCode == '900'" :moreBtn="true" :buttonList="signButtonList" @click="toHandle">
		</tButton>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import orderProgress from '../order/orderProgress/orderProgress.vue';
	import orderAddress from '../order/orderAddress/orderAddress.vue';
	import orderWl from '../order/orderWl/orderWl.vue';
	import orderInfo from '../order/orderInfo/orderInfo.vue';
	import orderProduct from '../order/orderProduct/orderProduct.vue';
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import {
		statusInfoList
	} from '@/pagesA/common/optionsData.js'
	import {
		vehicleColors
	} from "@/common/const/optionData.js";
	import {
		getOpenid,
	} from '@/common/storageUtil.js';
	export default {
		components: {
			tLoading,
			tButton,
			orderProgress,
			orderAddress,
			orderWl,
			orderInfo,
			orderProduct
		},
		data() {
			return {
				statusInfoList,
				vehicleColors,
				isLoading: false,
				buttonList: [],
				payButtonList: [{
					title: '查看售后并支付',
					type: 'info',
					handle: 'toAfterDetail'
				}],
				snedButtonList: [{
					title: '回填物流单号',
					type: 'info',
					handle: 'toAfterDetail'
				}],
				signButtonList: [{
					title: '去签署',
					handle: 'toSign',
					type: 'primary',
				}, {
					title: '取消订单',
					handle: 'handleCancelOrder',
					type: 'info',
				}],
				applyId: '',
				result: {},
				applyRecordVO: {},
				configReqVO: {},
				payOrder: {},
				agreementList: {},
				sfRouteResInfoDTO: {},
				address: '',
				wlAddress: '', //物流地址
				hsApplyStatusId: '', //节点状态
				benefitServiceFee: '', //权益服务费金额
				openId: ''
			}
		},
		onLoad(options) {
			if (options && options.applyId) {
				this.applyId = options.applyId
			}
			this.getDetail()
		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif	
		},
		methods: {
			toHandle(handle) {
				switch (handle) {
					case 'handleCancelOrder':
						//取消订单
						this.toCancel()
						break;
					case 'toPay':
						//去支付
						this.toPay()
						break;
					case 'toSign':
						// 去签署
						this.toSign()
						break;
					case 'handleConfirm':
						//确认签收
						this.handleConfirm()
						break;
					case 'handleRefundConfirm':
						//售后签收
						this.handleRefundConfirm()
						break;
					case 'afterApply':
						//申请退换
						this.toAfter('afterApply')
						break;
					case 'toIssue':
						//前往激活
						this.toIssue()
						break;
					case 'toAfterDetail':
						//修改售后材料
						let saleId = this.result.returnInfoDTO.record.id
						this.toAfterDetail(saleId)
						break;
					case 'reApply':
						//重新申请
						this.reApply()
						break;
					case 'toInvoice':
						//重新申请
						this.toInvoice()
						break;
					default:
						break;
				}
			},
			//获取openId
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			// //获取产品金额
			// getPackagePrice(result) {
			// 	let params = {
			// 		installType: '1', //安装方式; 1：快递邮寄,0: 网点自提
			// 		salesChannels: '0', //销售渠道;0 - C端小程序
			// 		carNo: result.vehicleCode,
			// 		carColor: result.vehicleColor,
			// 		productCode: result.productType,
			// 		isTrunk: result.vehicleType
			// 	}
			// 	this.$request.post(this.$interfaces.getPackagePrice, {
			// 		data: params
			// 	}).then(res => {
			// 		this.isLoading = false;
			// 		if (res.code == 200) {
			// 			this.benefitServiceFee = res.data.benefitServiceFee

			// 		} else {
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: res.msg,
			// 				showCancel: false
			// 			});
			// 		}
			// 	}).catch(err => {
			// 		this.isLoading = false;
			// 		uni.showModal({
			// 			title: '提示',
			// 			content: err.msg,
			// 			showCancel: false
			// 		});
			// 	})
			// },
			checkUnderSign(callback) {
				this.isLoading = true;
				let params = {
					vehicleCode: this.applyRecordVO.vehicleCode,
					vehicleColor: this.applyRecordVO.vehicleColor,
					hsApplyRecordId: this.applyId,
					openId: getOpenid() || this.openId
				}
				let data = {
					data: params,
				};

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.checkUnderSign, data)
					.then((res) => {
						console.log('res', res)
						this.isLoading = false;
						if (res.code == 200) {
							callback(res.data)
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			toSign() {
				if (this.applyRecordVO.productType == '5') {
					//日日通签约
					//下单成功，判断是否签约过，签约过直接前往订单页面。
					this.getSignStatus(this.applyId, resStatus => {
						if (resStatus) {
							//没签约过,去签约
							console.log('执行', resStatus)
							this.createUserSign()
						} else {
							//签约过直接去下单
							uni.redirectTo({
								url: '/pagesA/newBusiness/userAgreement/agreementList'
							})
						}
					})
				} else if (this.applyRecordVO.productType == '10') {
					//下单成功，判断是否签约过银联，签约过去签约次次顺协议。
					// this.searchSilkyInfo().then(response => {
					// 	console.log('response', response)
					// 	if (response.code == 200) {
					// 		if (response.data.length == 0) {
					// 			//未签约过，先签约银联
					// 			uni.navigateTo({
					// 				url: '/pagesA/newBusiness/signature/ccsSign/index'
					// 			})
					// 		} else {
					// 			//有签约数据需要判断
					// 			if (response.data[0].signStatus == '0') {
					// 				//未签约过，去签约银联
					// 				uni.navigateTo({
					// 					url: '/pagesA/newBusiness/signature/ccsSign/index'
					// 				})

					// 			} else if (response.data[0].signStatus == '2') {
					// 				//已经签署完成，继续签署次次顺协议
					// 				this.getSignStatus(this.applyId, resStatus => {
					// 					if (resStatus) {
					// 						//没签约过,去签约
					// 						this.createUserSign()
					// 					} else {
					// 						//签约过直接去下单
					// 						uni.redirectTo({
					// 							url: '/pagesA/newBusiness/userAgreement/agreementList'
					// 						})
					// 					}
					// 				})
					// 			} else if (response.data[0].signStatus == '1') {
					// 				//签署中
					// 				uni.showModal({
					// 					title: "提示",
					// 					content: '订单签约中，请稍后再尝试下一步'
					// 				});
					// 			} else if (response.data[0].signStatus == '4') {
					// 				//签约失败，去签约
					// 				uni.navigateTo({
					// 					url: '/pagesA/newBusiness/signature/ccsSign/index'
					// 				})
					// 			}
					// 		}
					// 	}
					// })

					//兼容签约
					this.checkUnderSign((res) => {
						if (res.checkSilkyFlag == 1) {
							//线下签约判断，已签约，直接跳过签约
							this.getSignStatus(this.applyId, resStatus => {
								if (resStatus) {
									//没签约过,去签约
									this.createUserSign()
								} else {
									//签约过直接去下单
									uni.redirectTo({
										url: '/pagesA/newBusiness/userAgreement/agreementList'
									})
								}
							})
						} else {
							//线下签约判断，未签约，调用签约查询接口查询状态
							this.searchSilkyInfo().then(response => {
								console.log('response', response)
								if (response.code == 200) {
									if (response.data.length == 0) {
										//未签约过，先签约银联
										uni.redirectTo({
											url: '/pagesA/newBusiness/signature/ccsSign/index'
										})
									} else {
										//有签约数据需要判断
										if (response.data[0].signStatus == '0') {
											//未签约过，去签约银联
											uni.redirectTo({
												url: '/pagesA/newBusiness/signature/ccsSign/index'
											})

										} else if (response.data[0].signStatus == '2') {
											//已经签署完成，继续签署次次顺协议
											// uni.redirectTo({
											// 	url: '/pagesA/newBusiness/order/order?applyId=' +
											// 		this
											// 		.$store.state.applyId
											// })
											this.getSignStatus(applyResult.id,
												resStatus => {
													if (resStatus) {
														//没签约过,去签约
														this.createUserSign()
													} else {
														//签约过直接去下单
														uni.redirectTo({
															url: '/pagesA/newBusiness/userAgreement/agreementList'
														})
													}
												})
										} else if (response.data[0].signStatus == '1') {
											//签署中
											uni.showModal({
												title: "提示",
												content: '订单签约中，请稍后再尝试下一步'
											});
										} else if (response.data[0].signStatus == '4') {
											//签约失败，去签约
											uni.redirectTo({
												url: '/pagesA/newBusiness/signature/ccsSign/index'
											})
										}
									}
								}
							})
						}
					})
				}

			},
			//校验合同签署状态
			getSignStatus(applyId, callback) {
				console.log('签约状态查询==========>>>>', applyId)
				this.isLoading = true;
				let params = {
					otherId: applyId,
					vehicleCode: this.applyRecordVO.vehicleCode,
					vehicleColor: this.applyRecordVO.vehicleColor,
					cardType: this.applyRecordVO.gxCardType, //-1是八桂卡，其他是卡类型
					signVersion: 'V2',
					businessType: '1',
				}
				let data = {
					data: params,
				};

				// if (payMoney) {
				// 	//有历史订单的时候，加上订单金额
				// 	params.saleAmount = payMoney
				// } else {
				// 	//没有历史订单
				// 	params.saleAmount && delete params.saleAmount
				// }

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.getSignStatus, data)
					.then((res) => {
						console.log('res', res)
						this.isLoading = false;
						if (res.code == 200) {
							if (res.data.isNecessary && !res.data.conStatus) {
								//需要签署协议。
								callback(true)
							} else {
								callback(false)
							}
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			//查询次次顺签约
			searchSilkyInfo() {
				let params = {
					// customerId: this.customerInfo.customer_id, //改成申请单id
					hsApplyRecordId: this.applyId,
					vehicleCode: this.applyRecordVO.vehicleCode,
					vehicleColor: this.applyRecordVO.vehicleColor
				}
				this.isLoading = true
				return new Promise((resolve, reject) => {
					this.$request
						.post(this.$interfaces.newSearchSilkyInfo, {
							data: params
						})
						.then((res) => {
							console.log(res, '查询次次顺签约信息')
							this.isLoading = false
							if (res.code == 200) {

							} else {
								this.isLoading = false
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
							resolve(res)
						}).catch(err => {
							this.isLoading = false
							reject(err)
						})
				})

			},
			createUserSign() {
				this.isLoading = true;
				// console.log('签约==========>>>>', this.vehicleColors[this.applyRecordVO.vehicleColor] + '色')
				// let carNoColor = this.vehicleColors[this.applyRecordVO.vehicleColor] + '色'

				let params = {
					source: '2', //2线上
					orderId: this.applyId,
					customerId: this.applyId,
					// custType: '2',
					vehicles: {
						vehicleCode: this.applyRecordVO.vehicleCode,
						vehicleColor: this.applyRecordVO.vehicleColor,
					},
					signName: this.applyRecordVO.customerName,
					signPhone: this.applyRecordVO.linkMobile,
					signIdNo: this.applyRecordVO.certificatesCode,
					// redirectUrl: 'https://portal.gxetc.com.cn/agreement?vehicleCode=' + encodeURIComponent(this
					// 	.applyRecordVO
					// 	.vehicleCode) + '&vehicleColor=' + encodeURIComponent(carNoColor),
					productType: this.applyRecordVO.productType,
					// saleAmount: this.configReqVO.benefitServiceFee,
					businessType: '1', //新办1
					isOwner: this.applyRecordVO.owner == 0 ? '1' : '0' //1本人，0代签
				}

				console.log('prams===========>', params)

				let data = {
					data: params,
				};
				this.isLoading = true
				this.$request
					.post(this.$interfaces.getSignPreview, data)
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('签章返回===>>>', res)
							// let signKey = res.data.signKey
							//设置缓存key
							uni.setStorageSync('signKey', res.data.signKey)
							console.log('signKey===>>>', res.data.signKey)
							let pdfInfo = res.data.data
							let signUrl = 'https://portal.gxetc.com.cn/new-agreement?btnType=C&type=sign&signInfo=' +
								encodeURIComponent(JSON.stringify(
									pdfInfo))

							uni.reLaunch({
								url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
									.stringify(
										signUrl))
							})
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			copy(value) {
				uni.setClipboardData({
					data: value,
					// success: () => {
					// 	uni.showModal({
					// 		title: '复制',
					// 		content: '已复制订单编号' + value
					// 	})
					// }
				});
			},
			toOrderProgress() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderProgress/orderProgressDetail?applyId=' + this.applyId
				})
			},
			toAddress() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderAddress/orderAddressDetail?applyId=' + this.applyId
				})
			},
			toWl() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderWl/orderWlDetail?applyId=' + this.applyId
				})
			},
			toInfo() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderInfo/orderInfoDetail?applyId=' + this.applyId
				})
			},
			toProduct() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderProduct/orderProductDetail?applyId=' + this.applyId
				})
			},
			toPay() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/order?applyId=' + this.applyId
				})
			},
			reApply() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/productSelect/productSelect'
				})
			},
			toInvoice() {
				uni.navigateTo({
					url: '/pagesB/invoiceBusiness/home/<USER>'
				})
			},
			getDetail() {
				this.isLoading = true
				let params = {
					id: this.applyId
				}

				this.$request.post(this.$interfaces.applyOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('详情信息=========>>>>>', res)
						let result = res.data
						this.result = res.data || {}
						console.log('result', this.result)
						this.applyRecordVO = result.applyRecordVO || {}
						//获取产品价格
						// this.getPackagePrice(result.applyRecordVO)
						this.configReqVO = result.configReqVO || {}
						this.payOrder = result.payOrder || {}
						this.agreementList = result.queryRes || {}
						this.sfRouteResInfoDTO = result.sfRouteResInfoDTO || {}

						if (result.sfRouteResInfoDTO) {
							this.wlAddress = result.sfRouteResInfoDTO.address
						}
						this.address = result.applyRecordVO.address
						//节点状态
						this.hsApplyStatusId = result.hsApplyStatusId

						if (result.configReqVO.benefitServiceFee == '0') {
							//2023.5.24 dwz权益费不显示开票按钮
							if (result.hsApplyStatusId == '10' || result.hsApplyStatusId == '15') {
								return
							}
						} else {
							if (statusInfoList[result.hsApplyStatusId].buttonList.length > 0) {
								//关联售后状态按钮
								this.buttonList = statusInfoList[result.hsApplyStatusId].buttonList
							}
						}


					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			toCancel() {
				uni.showModal({
					title: "取消订单原因",
					confirmText: '确定',
					placeholderText: '请输入原因',
					editable: true,
					success: (res) => {
						if (res.confirm) {
							let reason = res.content || '无'
							this.cancelApplyOrder(reason)
							// console.log('确认取消')
						}
					}

				});

			},
			cancelApplyOrder(reason) {
				this.isLoading = true
				this.$request.post(this.$interfaces.cancelApplyOrder, {
					data: {
						id: this.applyId,
						reason: reason
					}
				}).then(res => {
					this.isLoading = false
					console.log(res, '获取申请单列表');
					if (res.code == 200) {

						// this.$emit('handleCancel')
						// uni.reLaunch({
						// 	url: '/pagesA/orderBusiness/order//orderList'
						// })
						this.getDetail()

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			handleConfirm() {
				uni.showModal({
					title: "提示",
					content: '确定收货?',
					success: (res) => {
						if (res.confirm) {
							this.confirmProduct(this.applyId)
							// console.log('确认取消')
						}
					}

				});
			},
			confirmProduct(id) {
				this.isLoading = true
				this.$request.post(this.$interfaces.confirmProduct, {
					data: {
						id: id,
					}
				}).then(res => {
					console.log(res, '获取申请单列表');
					this.isLoading = false
					if (res.code == 200) {

						// this.$emit('handleUpdate', id)
						// this.getDetail()
						//前往激活
						uni.reLaunch({
							url: '/pagesA/newBusiness/order/orderSuccess?type=5&applyId=' + id
						})

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			handleRefundConfirm() {
				uni.showModal({
					title: "提示",
					content: '确定收货?',
					success: (res) => {
						if (res.confirm) {
							let saleId = this.result.returnInfoDTO.record.id
							this.confirmRefundProduct(saleId)
							// console.log('确认取消')
						}
					}

				});
			},
			confirmRefundProduct(id) {
				this.isLoading = true
				this.$request.post(this.$interfaces.confirmRefundProduct, {
					data: {
						id: id,
					}
				}).then(res => {
					console.log(res, '获取申请单列表');
					this.isLoading = false
					if (res.code == 200) {

						// this.$emit('handleUpdate', id)
						// this.getDetail()
						//前往激活
						uni.reLaunch({
							url: '/pagesA/newBusiness/order/orderSuccess?type=5&applyId=' + this.applyId
						})

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			toAfter(type) {
				console.log('resut', this.result)
				let data = this.result
				let params = {
					applyId: data.applyRecordVO.id,
					productType: data.applyRecordVO.productType,
					customerName: data.applyRecordVO.customerName,
					vehicleCode: data.applyRecordVO.vehicleCode,
					vehicleColor: data.applyRecordVO.vehicleColor

				}
				if (type == 'afterApply') {
					uni.navigateTo({
						url: '/pagesA/newBusiness/afterSale/select-type?applyData=' + encodeURIComponent(JSON
							.stringify(params))
					})
				} else if (type == 'afterList') {
					if (this.result.returnInfoDTO.orderSize == 1) {
						let saleId = this.result.returnInfoDTO.record.id
						//正在进行的直接去详情里
						uni.navigateTo({
							url: '/pagesA/newBusiness/afterSale/apply-detail?saleId=' + saleId
						})
					} else {
						console.log('执行了吗')
						uni.navigateTo({
							url: '/pagesA/newBusiness/afterSale/apply-list?applyId=' +
								this.applyId
						})
					}
				}
			},
			toAfterDetail(id) {
				uni.navigateTo({
					url: '/pagesA/newBusiness/afterSale/apply-detail?saleId=' + id
				})
			},
			toIssue() {
				this.beforeToIssue(res => {
					if (res.checkSilkyFlag == 1) {
						//签约过了
						this.$store.dispatch('setIssueVehicleInfo', {
							businessSource: 1, //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
							orderId: this.result.applyRecordVO.id,
							gxCardType: this.result.applyRecordVO.productType,
							customerId: this.result.applyRecordVO.customerId,
							vehicleCode: this.result.applyRecordVO.vehicleCode,
							vehicleColor: this.result.applyRecordVO.vehicleColor,
							benefitServiceFee: this.result.configReqVO.benefitServiceFee
						})
						uni.navigateTo({
							url: '/pagesA/newBusiness/issue/issue-install'
						})
					} else {
						uni.showModal({
							title: '提示',
							content: '您已解约，如要继续激活请先进行签约？',
							cancelText: '取消',
							confirmText: '去签约',
							success: res => {
								if (res.confirm) {
									//没签约去C端代扣签约
									uni.reLaunch({
										url: '/pagesB/vehicleBusiness/vehicleList?fontType=ccsSign'
									})
								}
							}
						})
					}
				})
			},
			//激活前校验
			beforeToIssue(callback) {
				this.isLoading = true
				this.$request.post(this.$interfaces.newSilkyActivationCheck, {
					data: {
						hsApplyRecordId: this.result.applyRecordVO.id,
					}
				}).then(res => {
					console.log(res, '激活前校验');
					this.isLoading = false
					if (res.code == 200) {
						callback(res.data)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-detail {
		padding-bottom: 180rpx;
		font-family: PingFangSC-Regular, PingFang SC;
	}

	.order-detail__title {
		height: 68rpx;
		padding: 0 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #ffffff;
	}

	.title-hd {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.title-hd__label {
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.title-hd__value {
		// margin-left: 10rpx;
		color: #323435;
		font-weight: 400;
	}

	.title-bd {
		flex: 0 0 210rpx;
		width: 200rpx;
		height: 31rpx;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		border-left: 1rpx solid #C6C6C6;
	}

	.title-bd .copy {
		margin-right: 50rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #3874FF;
	}

	.section {
		margin: 20rpx;
	}

	.last-wrapper {
		margin-top: 20rpx;
		margin-left: 20rpx;
	}
</style>