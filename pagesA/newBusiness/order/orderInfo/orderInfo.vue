<template>
	<view class="order-info">
		<tTitle title="申办材料" setPadding="30" rightText="查看详情" @click="onclick">
		</tTitle>
		<view class="personal-wrapper">
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					开户人
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.customerName}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					证件号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.certificatesCode}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					手机号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.linkMobile}}
				</view>
			</view>
		</view>
		<view class="car-wrapper">
<!-- 			<view class="car-bd">
				<view class="car-wrapper__label">
					车牌颜色
				</view>
				<view class="car-wrapper__value">
					{{vehicleColorStr}}色
				</view>
			</view> -->
			<view class="car-bd">
				<view class="car-wrapper__label">
					车牌号
				</view>
				<view class="car-wrapper__value">
					{{applyRecordVO.vehicleCode}}【{{vehicleColorStr}}色】
				</view>
			</view>
			<view class="car-bd">
				<view class="car-wrapper__label">
					车型
				</view>
				<view class="car-wrapper__value">
					{{vehicleClassType}}{{vehicleType}}车
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
	} from '@/common/method/filter.js';
	export default {
		props: {
			applyRecordVO: {
				type: Object
			}
		},
		components: {
			tLoading,
			tTitle
		},
		computed: {
			vehicleType() {
				return getVehicleType(this.applyRecordVO.vehicleType);
			},
			vehicleColorStr() {
				return getVehicleColor(this.applyRecordVO.vehicleColor);
			},
			vehicleClassType() {
				return getVehicleClassType(this.applyRecordVO.vehicleNationalType);
			},
		},
		data() {
			return {}
		},
		methods: {
			onclick() {
				this.$emit('click')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-info {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}

	.status-value.info {
		background: #F6F6F6;
		color: #333333;
	}


	.status-value.success {
		background: rgba(0, 189, 50, 0.1);
		color: #00BD32;
	}

	.status-value.warnning {
		background: rgba(255, 145, 0, 0.1);
		color: #FF9100;
	}

	.status-value.error {
		background: rgba(255, 84, 84, 0.1);
		color: #FF5454;
	}

	.progress-tip {
		padding: 30rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #999999;
	}

	.personal-wrapper {
		margin: 0 30rpx;
		border-bottom: 1rpx dashed #C3C3C3;
	}

	.personal-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.personal-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.personal-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}

	.car-wrapper {
		margin: 30rpx;
		overflow: hidden;
	}

	.car-bd {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.car-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.car-wrapper__value {
		line-height: 44rpx;
		font-size: 26rpx;
		flex: 1;
	}
</style>
