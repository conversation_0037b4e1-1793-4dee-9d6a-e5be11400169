<template>
	<view class="order-info__detail">
		<view class="personal-wrapper">
			<tTitle title="开户人资料"></tTitle>
			<view class="personal-bd" style="margin-top: 38rpx;">
				<view class="personal-wrapper__label">
					档案照片
				</view>
				<view class="personal-wrapper__value">
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.positiveImageUrl)">
						<image class="pic-img" :src="applyRecordVO.positiveImageUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							身份证人像
						</view>
					</view>
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.negativeImageUrl)">
						<image class="pic-img" :src="applyRecordVO.negativeImageUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							身份证国徽
						</view>
					</view>
				</view>
			</view>
			<view class="personal-bd" style="margin-top: 60rpx;">
				<view class="personal-wrapper__label">
					开户人
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.customerName}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					证件类型
				</view>
				<view class="personal-wrapper__value">
					{{getLabel(personalType,applyRecordVO.customerType)}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					证件号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.certificatesCode}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					手机号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.linkMobile}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					联系地址
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.address}}
				</view>
			</view>
		</view>
		<view class="personal-wrapper">
			<tTitle title="申办车辆资料"></tTitle>
			<view class="personal-bd" style="margin-top: 38rpx;">
				<view class="personal-wrapper__label">
					档案照片
				</view>
				<view class="personal-wrapper__value">
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.positiveVehicleImgUrl)">
						<image class="pic-img" :src="applyRecordVO.positiveVehicleImgUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							行驶证正页
						</view>
					</view>
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.negativeVehicleImgUrl)">
						<image class="pic-img" :src="applyRecordVO.negativeVehicleImgUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							行驶证副页
						</view>
					</view>
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.vehicleImgUrl)">
						<image class="pic-img" :src="applyRecordVO.vehicleImgUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							行驶证车辆
						</view>
					</view>
					<view v-if="applyRecordVO.owner == 1" class="pic-wrapper"
						@tap="ViewImage(applyRecordVO.ownerPositiveImgUrl)">
						<image class="pic-img" :src="applyRecordVO.ownerPositiveImgUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							车主身份证人像
						</view>
					</view>
					<view v-if="applyRecordVO.owner == 1" class="pic-wrapper"
						@tap="ViewImage(applyRecordVO.ownerNegativeImgUrl)">
						<image class="pic-img" :src="applyRecordVO.ownerNegativeImgUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							车主身份证国徽
						</view>
					</view>
					<view v-if="applyRecordVO.owner == 2" class="pic-wrapper"
						@tap="ViewImage(applyRecordVO.ownerPositiveImgUrl)">
						<image class="pic-img" :src="applyRecordVO.ownerPositiveImgUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							单位证件照
						</view>
					</view>
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.authLetterImgUrl)"
						v-if="applyRecordVO.owner != 0 && applyRecordVO.authLetterImgUrl">
						<image class="pic-img" :src="applyRecordVO.authLetterImgUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							车辆授权书
						</view>
					</view>
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.transportIdImgUrl)"
						v-if="applyRecordVO.isContainer =='0' && applyRecordVO.transportIdImgUrl">
						<image class="pic-img" :src="applyRecordVO.transportIdImgUrl" mode="aspectFill"></image>
						<view class="pic-lable">
							车辆道路运输证
						</view>
					</view>
				</view>
			</view>
			<view class="personal-bd" style="margin-top: 60rpx;">
				<view class="personal-wrapper__label">
					车牌颜色
				</view>
				<view class="personal-wrapper__value">
					{{vehicleColorStr}}色
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车牌号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleCode}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车辆归属人
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.ownerName}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车辆种类
				</view>
				<view class="personal-wrapper__value">
					{{vehicleClassType}}{{vehicleType}}车
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车辆类型
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleCarType}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					发动机号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleEngine}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车辆识别代码
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleDistinguish}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					长·宽·高
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleLength}}·{{applyRecordVO.vehicleWidth}}·{{applyRecordVO.vehicleHeight}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					座位数
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleSeat}}座
				</view>
			</view>
		</view>
		<view class="tips">
			如需修改车牌号/车牌颜色/车辆归属人/开户人姓名及证件号等信息，请取消此订单，重新申请
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	import {
		personalType,
	} from "@/common/systemConstant.js";
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
	} from '@/common/method/filter.js';
	export default {
		components: {
			tTitle
		},
		data() {
			return {
				personalType,
				isLoading: false,
				applyId: '',
				applyRecordVO: {},
				address: '',
			}
		},
		computed: {
			vehicleType() {
				return getVehicleType(this.applyRecordVO.vehicleType);
			},
			vehicleColorStr() {
				return getVehicleColor(this.applyRecordVO.vehicleColor);
			},
			vehicleClassType() {
				return getVehicleClassType(this.applyRecordVO.vehicleNationalType);
			},
		},
		onLoad(options) {
			if (options && options.applyId) {
				this.applyId = options.applyId
			}
			this.getDetail()
		},
		methods: {
			//图片预览
			ViewImage(path) {
				if (!path) return
				let newArr = [];
				newArr.push(path);
				uni.previewImage({
					urls: newArr
				});
			},
			getLabel(list, value) {
				for (let i = 0; i < list.length; i++) {
					if (list[i].value == value) {
						return list[i].label
					}
				}
				return ''
			},
			getDetail() {
				this.isLoading = true
				let params = {
					id: this.applyId
				}

				this.$request.post(this.$interfaces.applyOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('详情信息=========>>>>>', res)
						let result = res.data
						this.applyRecordVO = result.applyRecordVO || {}
						this.address = result.applyRecordVO.address
						// this.getSignStatus(res.data.applyRecordVO, () => {
						// 	this.getAgreementList(res.data.applyRecordVO)
						// })
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.personal-wrapper {
		background-color: #ffffff;
		margin: 20rpx;
		padding: 35rpx 30rpx 30rpx 30rpx;
		border-radius: 12rpx;
	}

	.personal-bd {
		display: flex;
		margin-bottom: 30rpx;
	}

	.personal-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.personal-wrapper__value {
		display: flex;
		flex-wrap: wrap;
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}

	.pic-wrapper {
		margin-bottom: 20rpx;

		&:nth-child(2n-1) {
			margin-right: 10rpx;
		}
	}

	.pic-img {
		width: 215rpx;
		height: 131rpx;
	}

	.pic-lable {
		text-align: center;
	}

	.car-wrapper {
		background-color: #ffffff;
		margin: 30rpx;
	}

	.car-bd {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.car-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.car-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}

	.tips {
		padding: 20rpx 24rpx 50rpx 24rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #888888;
	}
</style>