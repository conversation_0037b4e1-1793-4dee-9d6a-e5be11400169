<template>
	<view class="cu-card article margin-top bg-white order-item" v-if="Object.keys(singInfo).length > 0">
		<view class="cu-item padding-top item-hd" style="padding-bottom: 0px;">
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text
						class="text-bold">协议名称：{{singInfo.conTypeDesc}}</text>
				</view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>协议编号：</text>{{singInfo.conId}}
				</view>
			</view>

			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>车牌号码：</text>{{singInfo.vehicleCode}}
					【{{getVehicleColor(singInfo.vehicleColor)}}】
				</view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>用户名：</text>{{singInfo.custName}}</view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>签署人：</text>{{singInfo.signName}}</view>
			</view>
			<view class="g-flex">
				<view class="item-text padding-left g-flex-item"><text>签署时间：</text>{{singInfo.conCreateTime}}</view>
			</view>
			<view class="refundBtn">
				<button class="cu-btn" style="margin-right: 10upx;" @click="preview(singInfo)">
					查看
				</button>
				<!-- 				<button class="cu-btn" @click="sendEmail(singInfo)">
					发送到邮箱
				</button> -->
			</view>
		</view>
		<tLoading :isShow="isShowLoding" />

	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getPayStatus,
		getVehicleColor
	} from '@/common/method/filter'
	export default {
		props: {
			singInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {

				isShowLoding: false,
				schoolDetail: {

				},
			}
		},
		watch: {

		},
		components: {
			tLoading
		},
		computed: {

		},
		created() {

		},
		methods: {
			getVehicleColor,
			preview(val) {
				if (val.version == 'V2') {
					let newArr = []
					console.log('val.details', val.details)

					val.details.forEach(item => {
						newArr.push({
							procotolName: item.remark,
							procotolCacheFileUrl: item.filePath
						})
					})
					let signUrl = 'https://portal.gxetc.com.cn/new-agreement?type=preview&signInfo=' + encodeURIComponent(
						JSON
						.stringify(
							newArr))

					uni.navigateTo({
						url: "/pagesB/signWebview/signPreview?ownPath=" + encodeURIComponent(JSON
							.stringify(
								signUrl))
					})
					return
				}

				this.isShowLoding = true
				let data = {
					routePath: this.$interfaces.previewContracts.method,
					bizContent: {
						conId: val.contractId,
						vehicleCode: val.carNo,
						vehicleColor: val.carColor
					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isShowLoding = false
					if (res.code == 200) {
						uni.navigateTo({
							url: "/pages/uni-webview/h5-webview?ownPath=" + encodeURIComponent(res.data
								.viewUrl)
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isShowLoding = false
					uni.showModal({
						title: "错误",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			// sendEmail(val) {
			// 	this.isShowLoding = true
			// 	let data = {
			// 		routePath: this.$interfaces.sendContractsEmail.method,
			// 		bizContent: {
			// 			conId: val.contractId,
			// 			vehicleCode: val.carNo,
			// 			vehicleColor: val.carColor
			// 		}
			// 	}

			// 	this.$request.post(this.$interfaces.issueRoute, {
			// 		data: data
			// 	}).then(res => {
			// 		this.isShowLoding = false
			// 		if (res.code == 200) {
			// 			uni.showModal({
			// 				title: "提示",
			// 				content: '电子协议已发送到您开户预留的邮箱，请注意查收',
			// 				showCancel: false,
			// 			});
			// 		} else {
			// 			uni.showModal({
			// 				title: "提示",
			// 				content: res.msg,
			// 				showCancel: false,
			// 			});
			// 		}
			// 	})
			// },
		}
	}
</script>
<style lang="scss" scoped>
	.cu-btn {
		min-width: 120rpx;
		height: 60rpx !important;
		opacity: 1;
		font-size: 28rpx;
	}

	.case-item {
		border-radius: 10upx;
	}

	.case-item .item-hd .item-hd__box {
		padding-bottom: 30rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
	}

	.view-overflow-hide {
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		line-clamp: 3;
		-webkit-box-orient: vertical;
	}



	.cu-card>.cu-item {
		margin: 0;
	}

	.item-text {
		line-height: 50upx;
	}

	.refundBtn {
		float: right;
		margin-bottom: 18upx;
		margin-right: 30upx;
	}

	.animation {
		transition-property: all;
		transition-duration: 0.5s;
		transition-timing-function: ease;
	}
</style>