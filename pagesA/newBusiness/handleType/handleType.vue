<template>
	<view class="handlingType">
		<view class="card" @click="goPage('0')">
			<auto-image width="690rpx" height="220rpx" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/<EMAIL>"></auto-image>
			<view class="content">
				<view class="name">个人办理</view>
				<view class="text">请准备您的身份证、车辆行驶证；</view>
				<view class="text">如车辆非您本人所有，请准备车主身份证。</view>
			</view>
		</view>
		<view class="card" @click="goPage('1')">
			<auto-image width="690rpx" height="220rpx" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/<EMAIL>"></auto-image>
			<view class="content">
				<view class="name">单位办理</view>
				<view class="text">请准备您的身份证、单位营业执照副本、车辆行驶证，目前只提供企业办理，使馆、社会组织等单位请前往线下网点办理。</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		onLoad() {},
		methods: {
			goPage(type){
				let companyType = '1';
				if(companyType == type){
					uni.navigateTo({
						url: "/pagesA/newBusiness/company/company"
					})
				} else {
					uni.navigateTo({
						url: "/pagesA/newBusiness/personal/personal"
					})
				}
				console.log(type);
			}
		}
	}
</script>

<style scoped lang="scss">
	.handlingType{
		display: flex;
		flex-direction: column;
		padding: 30rpx;
		.card{
			display: flex;
			width: 690rpx;
			height: 220rpx;
			align-items: center;
			margin-bottom: 20rpx;
			position: relative;
			.content{
				position: absolute;
				width: 100%;
				height: 220rpx;
				display: flex;
				flex-direction: column;
				padding: 0 50rpx;
				justify-content: center;
				.name{
					color: #FFFFFF;
					font-weight: 600;
					font-size: 36rpx;
					margin-bottom: 10rpx;
				}
				.text{
					color: #FFFFFF;
					font-weight: 400;
					font-size: 24rpx;
					opacity: 0.8;
					display: inline-block;
					width: 462rpx;
				}
			}
		}
	}
</style>
