<!--
  * @projectName:gxetc-issue-mp2c
  * @desc:
  * @author:zhangys
  * @date:2022/11/29 13:38:18
!-->
<template>
	<view class="container" :class="signData.isShow == '1'?'container-no-padding':''">
		<handle-step v-if="signData.isShow != '1'" :current="3" />
		<view class="container-html">


			<p style=" orphans:0; text-align:center; widows:0;margin-bottom:60rpx">
				<text style="font-family:方正小标宋_GBK; font-size:15pt; font-weight:bold">代收业务委托协议</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">甲方</text><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-style:italic; font-weight:normal">（持卡人）</text><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">：</text><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:normal">{{signData.customer_name}}</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">身份证号码：</text><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">{{signData.certificates_code}}</text></p>
			<p style="line-height:13pt; orphans:0; widows:0;margin-bottom:40rpx"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">联系电话：</text><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">{{signData.mobile}}</text></p>

			<p style="line-height:13pt; margin:0pt; orphans:0; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">乙方</text><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-style:italic; font-weight:normal">（商户）</text><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">：广西捷通高速科技有限公司</text></p>
			<p style="line-height:13pt; orphans:0; widows:0;margin-bottom:40rpx"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">地址：南宁市青秀区民族大道152号铁投大厦17楼</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">为提高支付服务效率，甲方现委托并授权乙方，定期从甲方指定的银行账户/支付账户中扣收资金，用于完成ETC相关费用（含ETC高速公路、ETC停车场及其他ETC应用场景产生的相关费用）的缴纳。本协议下代收业务是指，甲方不在现场、未出示卡片、未提供交易密码的情况下，甲方许可商户发起代收交易，并从其指定的银行账户/支付账户完成本协议所约定的款项支付。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">一、委托内容</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">（一）用户号码</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">1.甲方用户号码为：</text>

				<text style="font-family:方正仿宋_GB2312; font-size:12pt">{{signData.customerId}}</text>

			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">2.乙方从甲方指定银行账户/支付账户扣收的资金，仅限用于缴纳该用户号码所产生的费用。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">（二）扣款周期</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">乙方从甲方指定银行账户/支付账户扣收资金的周期（或固定扣款日期）为500次/周。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">（三）交易金额</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">1.乙方应根据甲方用户号码实际产生的费用金额，从甲方指定银行账户/支付账户扣收资金。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">2.单笔扣款金额应不超过50000元，单日累计扣款金额应不超过200000元。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">3．乙方不得收取协议约定以外的其他款项。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">（四）委托期限</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0">
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">1.本委托生效日期为</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">{{nowDate.year}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">年</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">{{nowDate.month}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">月</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">{{nowDate.days}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">日；终止日期为2099年12月31日。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">2.甲方有权申请提前解除委托关系。如甲方需于上述终止日前解除委托关系，可通过向乙方提出申请。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">乙方应发起解除委托关系交易，并应于五个工作日内向甲方反馈结果。如委托关系成功解除，乙方应向甲方提供成功解除委托的系统</text><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">凭证作为有效凭证，否则甲方视委托关系未成功解除。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">3.委托关系失效或成功解除后，对于甲方用户号码所产生的费用，乙方不可再通过从甲方指定银行账户/支付账户扣款方式收取。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">（五）银行账户</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0">
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">1.甲方指定的银行账号/支付账户号为</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">{{signData.bankNo}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">该账户为甲方本人所有；账户登记姓名、身份证号码均为甲方本人信息。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">2.现已通过“建立委托关系”交易验证该银行账户/支付账户真实性与有效性。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">（六）付款人承诺</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">1.甲方承诺，对本协议约定的代收交易不得否认；</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">2.甲方承诺，其与乙方签署协议时，向乙方提供的身份证（原件）、指定账户的银行卡（原件）、签约信息均真实、合法、有效。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">二、商品或服务交付方式</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">    甲乙双方确认，乙方及时、足额收到货款是乙方履行发货义务的前提。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">三、退货政策</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">   
					甲方与乙方应当定期进行对账，任何一方对已经发生的扣划金额有异议的，应当及时通知对方并协商解决方案。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">四、异常处理</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">（一）因甲方银行账户/支付账户余额不足、账户状态异常等甲方原因，导致乙方未能按期完成资金扣收的情形。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">（二）如因乙方原因导致未能按期完成资金扣收，则甲方不承担相应责任。具体包含但不限于以下情形：</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">1.乙方（或乙方有关合作机构）人员操作失误；</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">2.乙方（或乙方有关合作机构）系统故障；</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">3.不可抗力因素导致未能按期扣收资金。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">（三）如甲方对委托关系或扣款交易存有异议，可向乙方进行咨询或投诉。乙方应提供咨询及投诉渠道，并予以积极配合并及时处理、反馈。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">乙方咨询或投诉电话为0771-5896333 。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">五、协议期限</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">1.本协议期限与“一、（四）委托期限”之生效日期、终止日期一致。如委托关系提前成功解除，本协议即终止。</text>
			</p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">2.本协议经甲方签字、乙方负责人或授权代理人签字并加盖公章有效。</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">六、其他</text></p>
			<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt">本协议一式份，甲、乙双方各持份。</text></p>

			<p style="margin-bottom:10rpx; orphans:0; text-align:justify; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">甲方：</text></p>

			<p style="line-height:12pt; margin:0pt; orphans:0; text-align:right; widows:0">
				<text style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:normal">{{nowDate.year}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:normal">年 </text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:normal">{{nowDate.month}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:normal">月 </text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:normal">{{nowDate.days}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:normal">日</text>
			</p>
			<p style="margin-bottom:10rpx; orphans:0; text-align:justify; widows:0"><text
					style="font-family:方正仿宋_GB2312; font-size:12pt; font-weight:bold">乙方：广西捷通高速科技有限公司</text></p>

			<p style="margin:0pt; orphans:0; text-align:right; widows:0">
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">{{nowDate.year}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">年 </text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">{{nowDate.month}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">月 </text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">{{nowDate.days}}</text>
				<text style="font-family:方正仿宋_GB2312; font-size:12pt">日</text>
			</p>

			<view class="agreement" v-if="signData.isShow != '1'">
				<view class="g-flex g-flex-align-center g-flex-center">
					<checkbox-group @change="checkboxChange">
						<checkbox value="checked" :checked="isChecked" color="#0066E9" style="transform:scale(0.7)"
							class="cyan checked remember-check" />
						同意<text style="color:#3e62e8">《代收业务委托协议》
						</text>
					</checkbox-group>
				</view>
			</view>


			<!-- <view class="bottom-box" style="margin: 40rpx 0px;padding-bottom: 40rpx;">
				<view class="btn-item">
					<button class="weui-btn" @click="back">
						上一步
					</button>
				</view>
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="sign">
						同意协议并签约
					</button>
				</view>

			</view> -->
		</view>
		<tButton v-if="signData.isShow != '1'" :buttonList="buttonList" @toNext="sign"></tButton>
	</view>

</template>

<script>
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	export default {
		name: '',
		props: {},
		components: {
			tButton,
			handleStep
		},
		data() {
			return {
				buttonList: [{
					title: '同意协议并签约',
					handle: 'toNext'
				}],
				nowDate: {
					year: '',
					month: '',
					days: ''
				},
				signData: {},
				isChecked: false,
			}
		},
		onLoad(obj) {
			for (let key in obj) {
				this.signData[key] = obj[key]
			}
			this.getNowTime()
		},
		computed: {},
		watch: {},
		created() {},
		methods: {
			getNowTime() {
				let nowTime = new Date()
				this.nowDate.year = nowTime.getFullYear()
				this.nowDate.month = nowTime.getMonth() + 1
				this.nowDate.days = nowTime.getDate()
			},
			checkboxChange(e) {
				this.isChecked = !!e.detail.value.length;
			},
			back() {
				uni.navigateBack(-1);
			},
			sign() {
				if (!this.isChecked) {
					uni.showModal({
						title: "提示",
						content: '请阅读并勾选同意代扣协议',
						showCancel: false,
					});
					return
				}
				uni.redirectTo({
					//关闭当前页面，跳转到应用内的某个页面。
					url: './ccsSign?isSign=' + this.isChecked
				});
			},
		}
	}
</script>

<style lang='scss'>
	page {
		background-color: #f8f8f8;
	}

	.container {
		padding-bottom: 180rpx;
		padding-top: 184rpx;
	}

	.container-no-padding {
		padding-bottom: 0rpx;
		padding-top: 0rpx;
	}

	.container-html {
		background-color: #ffffff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 12rpx;
	}

	.agreement {
		margin-top: 20rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}
</style>