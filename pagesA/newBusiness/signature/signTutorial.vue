<template>
	<view class="sign-tutorial">
		<handle-step :current="3" />
		<!-- <tTitle title="协议签署记录"></tTitle>
		 -->
		<view class="image-wrapper">
			<image style="width: 710rpx;" src="../../static/new-apply/sign_img.png" mode="widthFix"></image>
		</view>
		<tButton :buttonList="buttonList" @toNext="toNext"></tButton>
	</view>
</template>

<script>
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	export default {

		name: '',
		components: {
			handleStep,
			tButton,
		},
		data() {
			return {
				buttonList: [{
					title: '我已知晓，直接办理',
					handle: 'toNext'
				}],
				gxCardType: '',
				urlData: null
			}
		},
		onLoad(options) {
			if (options && options.gxCardType) {
				this.gxCardType = options.gxCardType
				if (options.urlData) {
					console.log('options.urlData1', options.urlData)
					this.urlData = JSON.parse(decodeURIComponent(options.urlData));
					console.log('options.urlData1', JSON.parse(decodeURIComponent(options.urlData)))
				}
			}
		},
		methods: {
			toNext() {
				// if (this.gxCardType == '5' && this.urlData) {
				// 	console.log('url===>>>', this.urlData)
				//日日通
				uni.redirectTo({
					url: '/pagesA/newBusiness/signature/signature?ownPath=' +
						encodeURIComponent(
							this.urlData.signUrl) + '&vehicles=' + encodeURIComponent(JSON
							.stringify({
								vehicleCode: this.urlData.vehicleCode,
								vehicleColor: this.urlData.vehicleColor,
							}))
				})
				// } else if (this.gxCardType == '10') {
				// 	//次次顺
				// 	uni.navigateTo({
				// 		url: '/pagesA/newBusiness/signature/ccsSign'
				// 	})
				// }
			},
		},
	}
</script>

<style lang='scss' scoped>
	.sign-tutorial {
		padding-bottom: 180rpx;
		padding-top: 184rpx;
	}

	.image-wrapper {
		padding: 20rpx;
	}
</style>