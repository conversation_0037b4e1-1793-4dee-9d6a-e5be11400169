<template>
	<view class="personal">
		<handle-step :current="3" />
		<view class="sellPay-container">
			<view class="sellPay-Info">
				<view class="c-title">签约车辆信息</view>
				<form>
					<view class="cu-form-group">
						<view class="title">签约车牌号</view>
						<view class="value">
							{{vehicleInfo.vehicle_code}}【{{vehicleColorStr(vehicleInfo.vehicle_color)}}】
						</view>
					</view>
					<view class="cu-form-group">
						<view class="title">签约车型</view>
						<view class="value">
							{{vehicleClassType(vehicleInfo.vehicle_class_str)}}
						</view>
					</view>
				</form>
			</view>

			<view class="sellPay-Info">
				<view class="title-wrapper">
					<view class="c-title">签约银行卡信息</view>
					<view class="bankList" @click="showBankList">支持签约的银行列表<text class="cuIcon-question"></text>
					</view>
				</view>

				<view class="weui-cells">
					<!-- 					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							
						</view>
					</view> -->
					<view class="vux-x-input weui-cell weui-cell_picker">
						<view class="weui-cell__hd">
							<view class="weui-label ">签约渠道</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary ">
							<view class="">
								银行卡代扣(银联渠道)
							</view>
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">持卡人姓名</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input v-model="formData.customer_name" :disabled='silkyInfo.signStatus=="2"'
								placeholder="请输入持卡人姓名"></input>
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">持卡人证件号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input type="idcard" v-model="formData.certificates_code"
								:disabled='silkyInfo.signStatus=="2"' placeholder="请输入持卡人证件号"></input>
						</view>
					</view>
					<view class="vux-x-input weui-cell code-input ">
						<view class="weui-cell__hd">
							<view class="weui-label ">银行卡号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary ">
							<input type="digit" v-model="formData.hideBankNo" @blur="onBlur" placeholder="请输入银行卡号"
								@input="(e)=> changeInput(e)" :disabled='silkyInfo.signStatus=="2"'>
							</input>
						</view>
						<view class="weui-cell__ft g-flex g-flex-align-center">
							<text v-if="!isChange&&silkyInfo.signStatus"
								:class="[showFlag?'cuIcon-attention':'cuIcon-attentionforbid']"
								@click="showFlag=!showFlag" style="margin-right:10rpx; font-size:38rpx;"></text>

							<text v-if="silkyInfo.signStatus=='0'||!silkyInfo.signStatus||silkyInfo.signStatus=='4'"
								class='cuIcon-cameraadd' style="font-size: 38rpx;" @click="ocrBankImg"></text>
						</view>

					</view>
					<view class="vux-x-input weui-cell"
						v-if='silkyInfo.signStatus=="0"||!silkyInfo.signStatus||showBankName'>
						<view class="weui-cell__hd">
							<view class="weui-label ">签约银行</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary g-flex">
							<input disabled v-model="formData.bankName" placeholder="输入银行卡号自动识别"></input>

						</view>

					</view>

					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label ">银行预留手机号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input type="digit" v-model="formData.mobile" :disabled='silkyInfo.signStatus=="2"'
								placeholder="请输入银行卡预留手机号"></input>

						</view>
					</view>
					<view class="vux-x-input weui-cell"
						v-if="silkyInfo.signStatus=='0'||!silkyInfo.signStatus||silkyInfo.signStatus=='4'">
						<view class="weui-cell__hd">
							<view class="weui-label ">短信验证码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<input placeholder="请输入短信验证码" v-model="formData.mobileCode"></input>
						</view>
						<view class="weui-cell__ft g-flex g-flex-align-center">
							<button class="codebtn" :disabled="codebr" @tap="getverification">
								<span v-if="codebr">重新发送({{count}})s</span>
								<span v-else>发送验证码</span>
							</button>
						</view>
					</view>
				</view>


			</view>
			<view class="fail-reason" v-if="silkyInfo.signStatus == '4'||silkyInfo.signStatus == '1'">
				{{silkyInfo.signStatus == '4'?'签约失败：':'签约中：'}}{{silkyInfo.signStatus == '4'?'请重新签署':'请稍后再进行下一步'}}
			</view>
			<!-- 			<view class="weui-bottom-fixed" v-if="silkyInfo.signStatus!='1'">
				<view class="weui-bottom-fixed__box bottom-box">

					<view class="btn-item" v-if='!silkyInfo.cancelStatus||silkyInfo.cancelStatus=="0"'>
						<button v-if="silkyInfo.signStatus=='2'&&silkyInfo.cancelStatus=='0'"
							class="weui-btn weui-btn_primary" @click="changeSign">
							签约变更
						</button>
						<button v-else class="weui-btn weui-btn_primary" @click="next">
							{{silkyInfo.signStatus == '4'?'重新签约':'下一步'}}
						</button>
					</view>
				</view>
			</view> -->
		</view>

		<!-- 签约完成弹框提示 -->
		<TModal :showModal='checkDialogVisible' modalTitle='温馨提醒' :showCancelFlag='false' @okModal='signOk' okText='继续'
			:modalStyle="'width:70%'" :modalTitleStyle="'color: #3e62e8;font-size:36rpx'"
			:comfirmBtnStyle="'color: #3e62e8;'">
			<view slot='content'>
				<view class="g-flex g-flex-wrap cu-form-group success-text">
					恭喜您，已成功签约ETC免密代扣协议，请继续进行后续操作。
				</view>
				<view class="confirm-content">
					<view class="confirm-label g-flex g-flex-start ">
						签约银行：{{tmpBankName}}
					</view>
					<view class="confirm-label g-flex g-flex-start ">
						银行卡尾号：{{tailNumber}}
					</view>
				</view>
				<!-- 				<view class="cu-form-group g-flex g-flex-wrap tips-content">
					※如果需要开通停车场等拓展交易免密代扣，请点击【去开通】按钮跳转开通界面继续操作！
				</view> -->

			</view>
		</TModal>

		<!-- 签约变更输入验证码弹框 -->
		<TModal :showModal='smsDialogVisible' modalTitle='签约变更' :showCancelFlag='true' @okModal='changeSingConfirm'
			@cancelModal='smsDialogVisible=false' okText='下一步' :comfirmBtnStyle="'color: #3e62e8;'"
			:modalStyle="'width:95%'">
			<view slot='content' class="bind-Info" v-if="smsDialogVisible">
				<view class="cu-form-group">
					<view class="title">签约渠道:</view>
					<view class="weui-picker-value value">
						银行卡代扣(银联渠道)
					</view>
					<!-- <picker disabled style="width:100%;" @change="bindPayTypeChange" :range="payTypePartOptions"
							range-key="label">
							<view class="weui-picker-value value">
								银行卡代扣(银联代扣)
							</view> 
						</picker> 
						<text class="cuIcon-right"></text>-->
				</view>
				<view class="cu-form-group">
					<view class="title">持卡人姓名:</view>
					<input v-model="formData.customer_name" placeholder="请输入持卡人姓名"></input>
				</view>
				<view class="cu-form-group">
					<view class="title">持卡人证件号:</view>
					<input type="idcard" v-model="formData.certificates_code" placeholder="请输入持卡人证件号"></input>
				</view>
				<view class="cu-form-group">
					<view class="title">银行卡号:</view>
					<input type="digit" v-model="formData.hideBankNo" @blur="onBlur" placeholder="请输入银行卡号"
						@input="(e)=> changeInput(e)">
					<text v-if="!isChange&&silkyInfo.signStatus"
						:class="[showFlag?'cuIcon-attention':'cuIcon-attentionforbid']" @click="showFlag=!showFlag"
						style="margin-right:10rpx;"></text>

					<text v-if='!silkyInfo.cancelStatus||silkyInfo.cancelStatus=="0"' class='cuIcon-cameraadd'
						@click="ocrBankImg"></text>
					</input>
				</view>
				<view class="cu-form-group">
					<view class="title">签约银行:</view>
					<input disabled v-model="formData.bankName" placeholder="输入银行卡号自动识别"></input>
				</view>
				<view class="cu-form-group">
					<view class="title">银行预留手机号:</view>
					<input type="digit" v-model="formData.mobile" placeholder="请输入银行卡预留手机号"></input>
				</view>

				<view class="cu-form-group">
					<view class="title form_label-require">短信验证码:</view>
					<input placeholder="请输入验证码" v-model="formData.mobileCode"></input>
					<button class="codebtn" :disabled="codebr" @tap="getverification">
						<span v-if="codebr">重新发送({{count}})s</span>
						<span v-else>发送验证码</span>
					</button>
				</view>
			</view>
		</TModal>

		<!-- 支持签约银行弹框 -->
		<TModal :showModal='bankListDialogVisible' modalTitle='支持签约的银行列表' :showCancelFlag='false'
			@cancelModal='bankListDialogVisible=false' okText='我已知晓' @okModal='bankListDialogVisible=false'
			:modalStyle="'width:75%'">
			<view slot='content' class="bind-Info" style="max-height: 400rpx;overflow-y: scroll;">
				<view class="cu-form-group" v-for="(item,index) in bankData" :key='index'>
					<view class="bank-item g-flex g-flex-row g-flex-align-center">
						<view class="bankImg">
							<img :src="item.logoPath" alt="">
						</view>
						<view class="bankName g-flex g-flex-column g-flex-align-start">
							<text style="font-size: 30rpx;font-weight: bolds">{{item.bankName}}</text>
							<text style="font-size: 26rpx;">注：{{item.desc}}</text>
						</view>
					</view>
				</view>
			</view>
		</TModal>

		<!-- ocr识别 -->
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
		<tButton :buttonList="buttonList" @toNext="next"></tButton>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import vue from 'vue';
	import {
		getCurrUserInfo,
		getCurrentCar,
		getTicket,
		getOpenid,
		getEtcAccountInfo,
	} from "@/common/storageUtil.js";
	import tLoading from '@/components/common/t-loading.vue';
	// import TButton from "@/components/t-button.vue";
	import TModal from '@/components/t-modal/t-modal.vue'
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import {
		checkPhone
	} from '@/common/util.js'
	import {
		getVehicleColor,
		getVehicleClassType
	} from '@/common/method/filter.js'
	import {
		vehicleColors,
	} from "@/common/const/optionData.js";
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	export default {
		name: '',
		components: {
			tButton,
			tTitle,
			TModal,
			tLoading,
			cpimg,
			handleStep
		},
		data() {
			return {
				vehicleColors,
				vehicleInfo: {},
				customerInfo: {},
				buttonList: [{
					title: '下一步',
					handle: 'toNext'
				}],
				formData: {
					customer_name: '',
					certificates_code: '',
					bankNo: '',
					mobile: '',
					mobileCode: '',
					umsMsgNo: ''
				},
				isChecked: false,
				isBtnLoader: false,
				checkDialogVisible: false,
				timer: null,
				count: 60,
				codebr: false,
				isLoading: false,
				tailNumber: '',
				silkyInfo: {
					signStatus: '',
					cancelStatus: ''
				},
				isSign: false,
				showFlag: true, //判断是否展示脱敏银行卡号
				isChange: false, //监听银行卡输入框是否变化
				smsDialogVisible: false,
				bankListDialogVisible: false,
				showBankName: false,
				bankData: [],
				tmpBankName: '', //签约完成后展示银行信息
				resultData: {}, //新办草稿
				benefitServiceFee: '' //权益服务费
			}
		},
		computed: {},
		onLoad(option) {
			this.getDraft()
			// this.vehicleInfo = getCurrentCar() ? getCurrentCar() : {}
			this.customerInfo = getCurrUserInfo() ? getCurrUserInfo() : {},
				//个人用户默认取用户信息里的姓名和身份证号，可更改
				this.formData.customer_name =
				this.customerInfo.customer_type == '0' ?
				this.customerInfo.customer_name :
				''
			this.formData.certificates_code =
				this.customerInfo.customer_type == '0' ?
				this.customerInfo.certificates_code :
				''

			//如果是从查看协议界面跳回此界面，取缓存信息
			if (option && option.isSign) {
				this.isSign = true
			}
			// this.searchSilkyInfo()
		},

		created() {
			this.getBankList()
		},
		watch: {
			//监听showflag,实现银行卡账号的显示与加密
			showFlag(val) {
				if (val) {
					this.dealBankNo()
				} else {
					this.formData.hideBankNo = this.formData.bankNo
				}
			}
		},
		methods: {
			vehicleColorStr(val) {
				return getVehicleColor(val)
			},
			vehicleClassType(val) {
				return getVehicleClassType(val)
			},
			//监听输入框变化，判断是否更改表单内容
			changeInput(e) {
				if (e.detail.value) {
					this.isChange = true
					this.formData.bankNo = e.target.value
				} else {
					this.formData.bankNo = ''
				}
			},

			//获取草稿
			getDraft() {
				this.isLoading = true
				// let params = this.formData
				// console.log(params, '入参');
				let params = {
					id: this.$store.state.applyId
				}
				this.$request.post(this.$interfaces.getDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						// //获取所有草稿
						this.resultData = result
						this.showData(result)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			showData(result) {
				this.vehicleInfo.vehicle_code = result.vehicleCode
				this.vehicleInfo.vehicle_color = result.vehicleColor
				this.vehicleInfo.vehicle_class_str = result.vehicleNationalType

				//获取服务费
				this.getPackagePrice(result)
				//获取签约信息
				this.searchSilkyInfo()

			},
			//查询次次顺签约
			searchSilkyInfo() {
				let params = {
					// customerId: this.customerInfo.customer_id, //改成申请单id
					hsApplyRecordId: this.$store.state.applyId,
					vehicleCode: this.vehicleInfo.vehicle_code,
					vehicleColor: this.vehicleInfo.vehicle_color
				}
				this.isLoading = true
				return new Promise((resolve, reject) => {
					this.$request
						.post(this.$interfaces.newSearchSilkyInfo, {
							data: params
						})
						.then((res) => {
							console.log(res, '查询次次顺签约信息')
							this.isLoading = false
							if (res.code == 200) {
								let searchData = res.data[0]
								this.silkyInfo = {}
								this.silkyInfo.signStatus = ''
								this.silkyInfo.cancelStatus = ''
								if (searchData) {
									this.silkyInfo = searchData
									if (searchData.length != 0) {
										this.formData.bankNo = searchData.bankCardNo
										this.formData.mobile = searchData.bankMobile
										console.log('searchData.custIdNo', searchData.custIdNo)
										this.formData.certificates_code = searchData.custIdNo
										this.formData.customer_name = searchData.custName
										this.formData.hideBankNo = searchData.bankCardNo
									}
									if (this.silkyInfo.signStatus == '4') {
										this.buttonList[0].title = '重新签署'
									}
									this.dealBankNo()
								}
								//查看协议后跳转回到此界面，取缓存里的验证码信息
								if (this.isSign) {
									let signData = {}
									try {
										signData = uni.getStorageSync('signData')
									} catch (e) {
										signData = {}
									}

									this.formData.mobile = signData.mobile
									this.formData.bankNo = signData.bankNo
									this.formData.hideBankNo = signData.bankNo
									this.formData.mobileCode = signData.mobileCode
									this.formData.umsMsgNo = signData.umsMsgNo
									this.formData.customer_name = signData.customer_name
									this.formData.certificates_code = signData.certificates_code
									this.dealBankNo()
									this.sign()
									console.log(signData, 'signData');
								}
							} else {
								this.isLoading = false
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
							resolve(res)
						}).catch(err => {
							this.isLoading = false
							reject(err)
						})
				})

			},

			//获取短息验证码
			getverification() {
				if (!this.validate()) return
				this.codebr = true
				let params = {
					channelId: '********',
					mobile: this.formData.mobile,
					accountNo: this.formData.bankNo,
					idType: '101',
					idNumber: this.formData.certificates_code,
					name: this.formData.customer_name,
					licenseCode: this.vehicleInfo.vehicle_code,
					licenseColor: this.vehicleInfo.vehicle_color
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.silkySms, {
						data: params
					})
					.then((res) => {
						if (res.code === 200) {
							this.isLoading = false
							console.log(res, '短信验证码接口返回')
							this.formData.umsMsgNo = res.data.umsMsgNo
							this.timer = setInterval(() => {
								if (this.count > 0 && this.count <= 60) {
									this.count--
								} else {
									this.isLoading = false
									this.codebr = false
									clearInterval(this.timer)
									this.timer = null
									this.count = 60
								}
							}, 1000)
						} else {
							this.isLoading = false
							this.codebr = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						this.codebr = false
						uni.showModal({
							title: '提示',
							content: '获取失败',
							showCancel: false
						})
					})
			},
			changeSign() {
				this.smsDialogVisible = true
			},
			changeSingConfirm() {
				if (!this.validate()) return
				this.getBankName().then(res => {
					if (res.code == 200) {
						if (!this.formData.mobileCode || !this.formData.umsMsgNo) {
							uni.showModal({
								title: '提示',
								content: '请输入短信验证码',
								showCancel: false
							})
							return
						}
						this.next()
						this.smsDialogVisible = false
					}
				})

			},
			//次次顺签约
			sign() {

				this.isLoading = true
				let params = {
					hsApplyRecordId: this.$store.state.applyId,
					vehicleCode: this.vehicleInfo.vehicle_code,
					vehicleColor: this.vehicleInfo.vehicle_color,
					isTrunk: this.resultData.vehicleType,
					carType: this.resultData.vehicleNationalType,
					vehicleType: this.resultData.vehicleCarType,
					useCharacter: this.resultData.vehicleUserType,
					custType: this.resultData.customerType,
					channelId: '********',
					custName: this.formData.customer_name,
					custIdType: '101',
					custIdNo: this.formData.certificates_code,
					bankMobile: this.formData.mobile,
					bankCardNo: this.formData.bankNo,
					umsVerifyCode: this.formData.mobileCode,
					umsMsgNo: this.formData.umsMsgNo,
					channelType: '1',
					//签约：签约状态不存在、签约状态==0创建、签约状态==4签约失败，都要传0（签约），其余传1（更改签约）
					isFirst: !this.silkyInfo.signStatus || this.silkyInfo.signStatus == '0' || this.silkyInfo
						.signStatus == '4' ? '0' : '1'
				}
				//更改签约需要传入singId
				if (params.isFirst == '1') {
					params.oldSignId = this.silkyInfo.signId
				}
				this.$request
					.post(this.$interfaces.newSaveSilkyInfo, {
						data: params
					})
					.then((res) => {
						if (res.code === 200) {
							this.getBankName('cancelEndLoading')
							// this.isLoading = false
							console.log(res, '次次顺签约接口返回')
							this.searchSilkyInfo().then(response => {
								if (response.code == '200' && response.data[0].signStatus == '2') {
									this.checkDialogVisible = true
									//签署完成
								} else {
									uni.showModal({
										title: '提示',
										content: response.data[0].signStatus_str + ',' + response.data[
											0].remark,
										showCancel: false
									})
								}
							})
							this.tailNumber = this.formData.bankNo.substr(-4)

							uni.setStorageSync('signData', {})
							this.isSign = false
							this.formData.mobileCode = ''
							this.formData.umsMsgNo = ''
						} else {
							uni.setStorageSync('signData', {})
							this.isSign = false
							this.formData.mobileCode = ''
							this.formData.umsMsgNo = ''
							this.searchSilkyInfo()
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error,
							showCancel: false
						})
					})
			},
			//表单校验
			validate() {
				if (!this.formData.customer_name) {
					uni.showModal({
						title: '提示',
						content: '请输入持卡人姓名',
						showCancel: false
					})
					return false
				}
				if (!this.formData.certificates_code) {
					uni.showModal({
						title: '提示',
						content: '请输入持卡人证件号',
						showCancel: false
					})
					return false
				}
				if (!this.formData.bankNo) {
					uni.showModal({
						title: '提示',
						content: '请输入银行卡号',
						showCancel: false
					})
					return false
				}

				if (!checkPhone(this.formData.mobile)) {
					uni.showModal({
						title: '提示',
						content: '请输入合法预留手机号',
						showCancel: false
					})
					return false
				}

				return true
			},
			//撤销签约
			cancelConfirm() {
				uni.showModal({
					title: '提示',
					content: '请确定是否要撤销签约',
					cancelText: '取消',
					confirmText: '确定',
					success: res => {
						if (res.confirm) {
							this.cancel()
						}
					}
				})
			},
			cancel() {
				this.isLoading = true
				let params = {
					// customerId: this.customerInfo.customer_id ? Number(this.customerInfo.customer_id) : "",
					hsApplyRecordId: this.$store.state.applyId,
					vehicleCode: this.vehicleInfo.vehicle_code,
					vehicleColor: this.vehicleInfo.vehicle_color,
					channelId: '********',
				}
				this.$request
					.post(this.$interfaces.newSilkyCancel, {
						data: params
					})
					.then((res) => {
						if (res.code === 200) {
							// this.isLoading = false
							this.searchSilkyInfo().then(res => {
								if (res.code == '200') {
									uni.showModal({
										title: '提示',
										content: '撤销签约成功',
										showCancel: false
									})
								}
							})
							this.formData.mobileCode = ''
							this.formData.umsMsgNo = ''
							Object.keys(this.formData).forEach((key) => {
								this.formData[key] = ''
							})
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error,
							showCancel: false
						})
					})
			},

			signOk() {
				//签约完成，签约签章
				// uni.navigateTo({
				// 	url: '/pagesA/newBusiness/order/order?applyId=' + this.$store.state.applyId
				// })
				this.getSignStatus(resStatus => {
					if (resStatus) {
						//没签约过,去签约
						this.createUserSign()
					} else {
						//签约过直接去下单
						uni.redirectTo({
							url: '/pagesA/newBusiness/userAgreement/agreementList'
						})
					}
				})
			},

			//获取产品金额
			getPackagePrice(result) {
				let params = {
					installType: '1', //安装方式; 1：快递邮寄,0: 网点自提
					salesChannels: '0', //销售渠道;0 - C端小程序
					carNo: result.vehicleCode,
					carColor: result.vehicleColor,
					productCode: result.productType,
					isTrunk: result.vehicleType
				}
				this.$request.post(this.$interfaces.getPackagePrice, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.benefitServiceFee = res.data.benefitServiceFee

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},

			createUserSign() {
				console.log('签约==========>>>>', this.$store.state.applyId)
				this.isLoading = true;
				// let carNoColor = this.vehicleColors[this.resultData.vehicleColor] + '色'

				let params = {
					source: '2', //2线上
					orderId: this.$store.state.applyId,
					customerId: this.$store.state.applyId,
					// custType: '2',
					vehicles: {
						vehicleCode: this.resultData.vehicleCode,
						vehicleColor: this.resultData.vehicleColor,
					},
					signName: this.resultData.customerName,
					signPhone: this.resultData.linkMobile,
					signIdNo: this.resultData.certificatesCode,
					// redirectUrl: 'https://portal.gxetc.com.cn/agreement?vehicleCode=' + encodeURIComponent(this
					// 	.resultData
					// 	.vehicleCode) + '&vehicleColor=' + encodeURIComponent(carNoColor),
					productType: this.resultData.productType,
					businessType: '1', //新办1
					// saleAmount: this.benefitServiceFee,
					isOwner: this.resultData.owner == 0 ? '1' : '0' //1本人，0代签
				}

				console.log('prams===========>', params)

				let data = {
					data: params,
				};

				this.$request
					.post(this.$interfaces.getSignPreview, data)
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('签章返回===>>>', res)
							let signKey = res.data.signKey
							//设置缓存key
							uni.setStorageSync('signKey', signKey)
							let pdfInfo = res.data.data
							let signUrl = 'https://portal.gxetc.com.cn/new-agreement?btnType=C&type=sign&signInfo=' +
								encodeURIComponent(JSON.stringify(
									pdfInfo))

							uni.reLaunch({
								url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
									.stringify(
										signUrl))
							})
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			//校验合同签署状态
			getSignStatus(callback) {
				console.log('签约状态查询==========>>>>')
				this.isLoading = true;
				let params = {
					otherId: this.$store.state.applyId,
					vehicleCode: this.resultData.vehicleCode,
					vehicleColor: this.resultData.vehicleColor,
					cardType: this.resultData.productType, //-1是八桂卡，其他是卡类型
					signVersion: 'V2',
					businessType: '1',
				}
				let data = {
					data: params,
				};

				// if (payMoney) {
				// 	//有历史订单的时候，加上订单金额
				// 	params.saleAmount = payMoney
				// } else {
				// 	//没有历史订单
				// 	params.saleAmount && delete params.saleAmount
				// }

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.getSignStatus, data)
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							if (res.data.isNecessary && !res.data.conStatus) {
								//需要签署协议。
								callback(true)
							} else {
								callback(false)
							}
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},

			// //跳转拓展业务平台 新办不用
			// goH5() {
			// 	this.checkDialogVisible = false
			// 	let etcAccountInfo = getEtcAccountInfo() && Object.keys(getEtcAccountInfo()).length ? getEtcAccountInfo() :
			// 	{}
			// 	let vehicleInfo = getCurrentCar() && Object.keys(getCurrentCar()).length ? getCurrentCar() : {}
			// 	let data = {
			// 		ticket: getTicket() || '',
			// 		custMastId: etcAccountInfo.custMastId || '',
			// 		openId: getOpenid(),
			// 		carNo: vehicleInfo.vehicle_code || this.vehicleInfo.vehicle_code,
			// 		carColor: vehicleInfo.vehicle_color || this.vehicleInfo.vehicle_colors,
			// 		type: 'sign'
			// 	}
			// 	vue.prototype.$request
			// 		.post(vue.prototype.$interfaces.expandUrl, {
			// 			data: data
			// 		})
			// 		.then((res) => {
			// 			console.log(res, '获取拓展业务跳转url');
			// 			if (res.code == 200) {
			// 				const callcenter = res.data
			// 				uni.navigateTo({
			// 					url: '/pages/uni-webview/uni-webview?ownPath=' +
			// 						encodeURIComponent(callcenter)
			// 				})
			// 			} else {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				})
			// 			}

			// 		})
			// 		.catch((error) => {
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error.msg,
			// 				showCancel: false
			// 			})
			// 		})
			// },
			//跳转协议界面，缓存表单信息
			next() {
				if (!this.validate()) return

				if (!this.formData.mobileCode || !this.formData.umsMsgNo) {
					uni.showModal({
						title: '提示',
						content: '请输入短信验证码',
						showCancel: false
					})
					return false
				}
				this.getBankName().then(res => {
					if (res.code == 200) {
						let params = {
							...this.formData,
							customerId: this.customerInfo.customer_id, //新办改成申请单hsApplyRecordId
							umsVerifyCode: this.formData.mobileCode,
							umsMsgNo: this.formData.umsMsgNo,
							// custIdType: 
						}
						uni.setStorageSync('signData', params)
						uni.redirectTo({
							url: './agreement?' + this.objToUrlParam(params)
						})
					}
				})

			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},
			//脱敏银行卡账号
			dealBankNo() {
				if (!this.formData.bankNo) return
				this.formData.hideBankNo = this.formData.bankNo.substring(0, 4) + "********" + this.formData.bankNo
					.substring(this.formData.bankNo.length - 4);
			},
			//显示可签约银行列表
			showBankList() {
				this.bankListDialogVisible = true
			},

			ocrBankImg() {
				this.$refs.cpimg._changImg('0');
			},
			cpimgOk(file) {
				this.sendOCR(file);
			},
			cpimgErr(e) {
				console.log(e)
			},
			// ocr识别
			sendOCR(file) {
				let base64Img = file.toString()
				let ocr_type = '';
				let current = {};
				var imgStr = base64Img.split(';')[1].split(",")[1] + '';
				let biz_content = {
					ocr_type: 1,
					file_name: 'file_name',
				}
				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let encryptedData = res.data.encryptedData;
						if (encryptedData.bankNo) {
							let trimBankNo = encryptedData.bankNo.replace(/\s*/g, "")
							this.formData.bankNo = trimBankNo
							this.formData.hideBankNo = trimBankNo
							this.isChange = true
							this.getBankName()
						}

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			onBlur() {
				if (!this.formData.bankNo) return
				let reg = /^(\d{14,19})$/g
				if (!reg.test(this.formData.bankNo)) {
					uni.showModal({
						title: "提示",
						content: '银行卡号长度不合法，重新识别或重新输入',
						showCancel: false,
					});
					return
				}
				this.getBankName()
			},
			//根据银行卡号查询名称
			getBankName(val) {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.getBankName.method,
					bizContent: {
						cardNo: this.formData.bankNo
					}
				}
				return new Promise((resolve, reject) => {
					this.$request
						.post(this.$interfaces.issueRoute, {
							data: data
						})
						.then((res) => {
							console.log(res, '00000');
							if (!val) {
								this.isLoading = false
							}
							if (res.code === 200) {
								this.formData.bankName = res.data.issueBankName
								this.tmpBankName = res.data.issueBankName
								this.showBankName = true
							} else {
								this.formData.bankName = ''
								this.isLoading = false
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
							resolve(res)
						}).catch(err => {
							this.isLoading = false
							reject(err)
						})
				})

			},
			//查询支持银行卡列表
			getBankList() {
				let data = {
					routePath: this.$interfaces.getBankList.method,
					bizContent: {

					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						if (res.code === 200) {
							this.bankData = res.data
						} else {

							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {

						uni.showModal({
							title: '提示',
							content: error,
							showCancel: false
						})
					})
			},
			// //取消跳转到首页
			// checkDialogVisibleClose() {
			// 	this.checkDialogVisible = false
			// 	uni.redirectTo({
			// 		url: '/pages/home/<USER>/p-home'
			// 	})
			// }
		}
	}
</script>

<style lang='scss' scoped>
	.sellPay-container {
		/* padding-bottom: 20%; */
		padding-bottom: 180rpx;
		padding-top: 184rpx;
	}

	.sellPay-Info {
		background-color: #ffffff;
		margin: 20rpx;
		border-radius: 12rpx;

		.c-title {
			/* margin-top: 30upx; */
			border-top-left-radius: 12rpx;
			border-top-right-radius: 12rpx;
			padding: 0 30upx;
			font-size: 32upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.sellPay-Info .title-wrapper {
		display: flex;
		justify-content: space-between;
	}

	.sellPay-Info .cu-form-group:last-of-type {
		border-bottom-left-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}

	.sellPay-Info .weui-cells {
		border-radius: 12rpx;
	}

	.sellPay-Info .cu-form-group .title {
		height: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
		line-height: 30rpx;
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		height: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
		line-height: 30rpx;
		text-align: right;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.cu-form-group {
		min-height: 100rpx;
	}

	.code-img {
		width: 210upx;
		height: 90upx;
		margin-right: 10upx;
	}

	.codebtn {
		/* background: #01c1b2; */
		min-width: 210upx;
		font-size: 24upx;
		height: 70upx;
		margin-right: 10upx;
		background: #ffffff;
	}

	.codebtn::after {
		border: 0;
	}

	.codebtn span {
		display: inline-block;
		color: #0066E9;
		line-height: 70upx;
	}

	.agreement {
		margin-top: 20rpx;
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.confirm-label {
		color: #3e62e8;
		margin-bottom: 6rpx;
		font-size: 30rpx;
		font-weight: bold;
	}

	.confirm-content {
		padding: 0 50rpx;
		min-height: 100rpx;
	}

	.tips-content {
		margin-top: 10rpx;
		text-align: start;
		text-indent: 2em;
		color: #666
	}

	.success-text {
		text-indent: 2em;
		text-align: start;
		font-size: 30rpx;
		font-weight: bold;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}


	.fail-reason {
		padding: 26rpx;
		font-size: 28rpx;
		font-family: PingFangSC, PingFangSC-Light;
		font-weight: 400;
		background: rgba(255, 84, 84, 0.15);
		color: #FF5454;
		text-align: center;
	}



	.bind-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.bind-Info .cu-form-group input {
		text-align: left;
	}

	.sms-tips {
		font-size: 30rpx;
		font-family: PingFangSC, PingFangSC-Light;
		font-weight: 500;
		color: #555555;
		margin-top: 10rpx;
		padding: 10rpx;
	}

	.bankList {
		padding-right: 20rpx;
		display: flex;
		align-items: center;
		color: #39b9f2;
		font-size: 28rpx;
		color: #0081FF;
	}

	.bankImg {
		margin-right: 20rpx;

		&>img {
			width: 70rpx;
			height: 70rpx;
		}
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-cells {
		padding-top: 0;
	}

	/deep/.cu-modal .cu-dialog .cu-dialog_hd {
		padding: 50rpx 0 50rpx 0 !important;
	}

	/deep/.cu-modal .cu-dialog .cu-dialog_hd .modal-title {
		font-size: 36rpx;
		font-family: PingFangSC-Semibold, PingFang SC;
		font-weight: 600;
		color: #323435;
	}

	/deep/.cu-modal .cu-dialog .cu-dialog_ft {
		height: 120rpx !important;
	}

	/deep/.cu-modal .cu-dialog .cu-dialog_ft .action {
		color: #0066E9;
	}
</style>