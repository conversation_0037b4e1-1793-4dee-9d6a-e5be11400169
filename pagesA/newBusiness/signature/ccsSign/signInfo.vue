<template>
	<view class="weui-form">
		<view class="weui-cells__title">
			<view class="weui-cells__title__decoration">
				签约信息
			</view>
		</view>
		<view class="weui-cells">
			<block v-if='signInfo.bankId =="S_WECHAT" || signInfo.bankId =="S_WECHAT"'>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">签约渠道</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{signInfo.bankId =="S_WECHAT" ? "微信钱包代扣":"银行卡代扣"}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if='signInfo.bankId =="S_WECHAT"'>
					<view class="weui-cell__hd">
						<view class="weui-label">服务费</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							每次通行收取通行金额的 {{signInfo.rate}}‰
						</view>
					</view>
				</view>
			</block>
			<!--  -->
			<block v-if='signInfo.bankId =="C_WECHAT" || signInfo.bankId =="CUP"'>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">签约银行</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{signInfo.bankName}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label">服务费</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							每次通行收取通行金额的{{signInfo.rate}}‰
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label ">持卡人姓名</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{signInfo.custName}}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label ">持卡人证件号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{signInfo.custIdNo }}
						</view>
					</view>
				</view>
				<view class="vux-x-input weui-cell code-input ">
					<view class="weui-cell__hd">
						<view class="weui-label ">银行卡号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{signInfo.bankCardNo|bankNoFilter}}
						</view>
					</view>

				</view>


				<view class="vux-x-input weui-cell" v-if='signInfo.bankId =="CUP"'>
					<view class="weui-cell__hd">
						<view class="weui-label ">银行预留手机号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__value">
							{{signInfo.bankMobile}}
						</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			silkyInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {
				signInfo: {}
			}
		},

		watch: {
			silkyInfo(val) {
				console.log('val====>>>签约信息', val)
				this.signInfo = val;
			}
		},
		filters: {
			bankNoFilter(cardNumber) {
				const regex = /(\d{4})\d+(\d{4})/;
				let hideCount = (cardNumber.toString().length - 8);
				let starString = hideCount > 0 ? '*'.repeat(hideCount) : '';
				return !!cardNumber && hideCount > 0 ? cardNumber.replace(regex, `$1${starString}$2`) : cardNumber
			}
		},
		created() {
			this.signInfo = this.silkyInfo;
		},

		methods: {

		}
	}
</script>

<style lang='scss' scoped>

</style>