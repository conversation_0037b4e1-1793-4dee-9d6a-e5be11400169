<!--
  * @desc:次次顺签约
  * @author:zhangys
  * @date:2022-10-26 11:24:56
!-->
<template>
	<view class="sign-container">
		<!-- 签约车辆信息 -->
		<vehicleInfo :vehicleInfo="vehicleInfo"></vehicleInfo>
		<view class="sign-tip">
			温馨提示:若您选择微信钱包代扣渠道，需阅读代扣协议后跳转至微信车主服务平台进行签约操作:
		</view>
		<!-- 签约信息 -->
		<signInfo v-if="silkyInfo.signStatus=='1' || (silkyInfo.signStatus=='2'&&silkyInfo.cancelStatus=='0')"
			:silkyInfo='silkyInfo'></signInfo>
		<block v-else>
			<block v-if="signTypeDict.length">
				<view class="weui-form">
					<view class="weui-cells__title">
						<view class="weui-cells__title__decoration">
							签约信息
						</view>
					</view>
					<view class="weui-cells">
						<!-- 微信免密代扣签约 -->
						<block v-if="isWechatSign && isWechatSignConfig">
							<view>
								<view class="vux-x-input weui-cell weui-cell_picker">
									<view class="weui-cell__hd">
										<view class="weui-label">签约渠道</view>
									</view>
									<view class="weui-cell__bd weui-cell__primary">
										<picker :model="signMode" range-key="label" :value="signModeIndex"
											:range="signModeList" @change="signModeChange">
											<view class="weui-picker-value">
												{{signModeList[signModeIndex].label}}
											</view>
										</picker>
									</view>
									<view class="weui-cell__ft"></view>
								</view>
								<view class="vux-x-input weui-cell" v-if="signMode=='S_WECHAT'">
									<view class="weui-cell__hd">
										<view class="weui-label">通行服务费</view>
									</view>
									<view class="weui-cell__bd weui-cell__primary">
										<view class="weui-cell__value">
											每次通行收取通行金额的{{serviceRate}}‰
										</view>
									</view>
								</view>
								<view class="vux-x-input weui-cell" v-if="signMode=='S_WECHAT'">
									<view class="weui-cell__hd">
										<view class="weui-label">逾期服务费</view>
									</view>
									<view class="weui-cell__bd weui-cell__primary">
										<view class="weui-cell__value">
											按ETC消费金额*{{overRate}}‰ *逾期天数
										</view>
									</view>
								</view>
							</view>
						</block>
					</view>
				</view>
				<view class="sign-tips" style="padding-bottom: 10rpx;">
					*逾期服务费收取规则:ETC消费后未成功扣款的，自首次扣款当日起前两个自然日不算作逾期，超过两个自然日后视为逾期并开始计收逾期服务费。
					<span style="color: #0066E9;" v-if="!isExpand" @click="expandHandle(true)">查看示例</span>
				</view>
				<view class="sign-tips" style="color: #333333;padding-top: 0;" v-if="isExpand">
					例:若8月10号下午3时，代扣渠道反馈A用户ETC消费扣款失败，捷通公司立即限制A用户的ETC消费，截止至8月11日24时，仍未能扣款成功的，8月12日0时开始计算滞纳金。
					<span style="color: #0066E9;" @click="expandHandle(false)">收起</span>
				</view>
				<view class="fail-reason" v-if="silkyInfo.signStatus == '4'||silkyInfo.signStatus == '1'">
					{{silkyInfo.signStatus == '4'?'签约失败：':'签约中：'}}{{silkyInfo.signStatus == '4'?silkyInfo.remark:'请稍后查询'}}
				</view>
				<!-- 不存在签约关系 -->
				<view class="weui-bottom-fixed"
					v-if="(!silkyInfo.signStatus && !silkyInfo.cancelStatus) || (silkyInfo.signStatus && silkyInfo.signStatus == '4')"
					style="z-index: 10;">
					<view class="weui-bottom-fixed__box bottom-box">
						<!-- 微信免密代扣签约 -->
						<view class="btn-item" v-if='isWechatSignConfig && isWechatSign && signMode=="S_WECHAT"'>
							<button class="weui-btn weui-btn_primary" @click="wechatSignHandle">
								前往签约
							</button>
						</view>
					</view>
				</view>
				<view class="weui-bottom-fixed" v-if='isSignState' style="z-index: 10;">
					<view class="weui-bottom-fixed__box bottom-box">
						<!-- 微信免密代扣签约 -->
						<view class="btn-item"
							v-if='isWechatSignConfig && silkyInfo.bankId == "S_WECHAT" && silkyInfo.wechatSign== "UNAUTHORIZED"'>
							<button class="weui-btn weui-btn_primary" @click="wechatSignHandle">
								前往签约
							</button>
						</view>
					</view>
				</view>
			</block>

		</block>
		<!-- 签约完成弹框提示 -->
		<TModal :showModal='checkDialogVisible' :modalTitle='title' :showCancelFlag='false' :showOkFlag="false"
			@okModal='goNext' okText='前往签署用户协议' :modalStyle="'width:80%'"
			:modalTitleStyle="'color: #000000;font-size:36rpx'">
			<view slot='content'>
				<!-- 				<view class="g-flex g-flex-wrap cu-form-group success-text">
					您已签约微信免密代扣
				</view> -->
				<template v-if="silkyInfo.signStatus == '2'">
					<view class="confirm-content">
						<view class="confirm-label g-flex g-flex-start ">
							<view class="label">
								车牌号：
							</view>
							<view class="value">
								{{vehicleInfo.vehicleCode}}
							</view>
						</view>
						<view class="confirm-label g-flex g-flex-start ">
							<view class="label">
								签约车型：
							</view>
							<view class="value">
								{{vehicleClassType(vehicleInfo.vehicleNationalType)}}【{{vehicleTypeList[vehicleInfo.vehicleType] +'车'}}】
							</view>
						</view>
						<view class="confirm-label g-flex g-flex-start ">
							<view class="label">
								签约渠道：
							</view>
							<view class="value">
								微信钱包代扣
							</view>
						</view>
						<view class="confirm-label g-flex g-flex-start ">
							<view class="label">
								通行服务费：
							</view>
							<view class="value">
								每次通行收取通行金额的{{serviceRate}}‰
							</view>
						</view>
						<view class="confirm-label g-flex g-flex-start ">
							<view class="label">
								逾期服务费：
							</view>
							<view class="value">
								按ETC消费金额*{{overRate}}‰ *逾期天数
							</view>
						</view>
						<view class="img-wrapper">
							<image class="img" src="../../../static/new-apply/ccs/ccs_success.png" mode="aspectFill">
							</image>
						</view>
					</view>
					<view class="cu-form-group g-flex g-flex-wrap tips-content">
						*温馨提示: 请确保您的签约代扣账户状态正常，以免影响后续使用;
					</view>
					<view class="btn-wrapper">
						<view class="btn" @click="goNext">
							前往签署用户协议
						</view>
					</view>
				</template>
				<template v-if="silkyInfo.signStatus == '4'">
					<view class="fail-content">
						<view class="fail-text">
							抱歉，您可选择其他产品:捷通日日通 (预存式记账卡) ;
						</view>
						<view class="fail-text">
							如需继续办理可截图后联系在线客服或拨打广西捷通ETC客服热线0771-5896333处理
						</view>
						<view class="img-wrapper">
							<image class="img" src="../../../static/new-apply/ccs/ccs_error.png" mode="aspectFill">
							</image>
						</view>
					</view>
					<view class="cu-form-group g-flex g-flex-wrap fail-tips">
						错误原因：签约失败
					</view>
				</template>
			</view>
		</TModal>
		<!-- ocr识别 -->
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import vue from 'vue';
	import {
		getCurrUserInfo,
		getCurrentCar,
		getTicket,
		getOpenid,
		getEtcAccountInfo,
	} from "@/common/storageUtil.js";
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import TModal from '@/components/t-modal/t-modal.vue'
	import {
		checkPhone
	} from '@/common/util.js'
	import {
		getVehicleColor,
		getVehicleClassType
	} from '@/common/method/filter.js'
	import {
		vehicleType,
		vehicleColors
	} from "@/common/const/optionData.js";
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import minxins from "./minxins";
	// import bankSelect  from "./bankSelect.vue";
	import signInfo from "./signInfo.vue";
	import vehicleInfo from "./components/vehicleInfo.vue";
	export default {
		mixins: [minxins], //混入多个文件的写法
		name: '',
		components: {
			TButton,
			TModal,
			tLoading,
			cpimg,
			// bankSelect,
			signInfo,
			vehicleInfo
		},
		data() {
			return {
				vehicleColors,
				vehicleTypeList: vehicleType,
				selectBankName: '',
				closeIconPos: 'top-right',
				title: '',
				vehicleInfo: {},
				customerInfo: {},
				formData: {
					customer_name: '',
					certificates_code: '',
					bankNo: '',
					mobile: '',
					mobileCode: '',
					umsMsgNo: ''
				},
				isChecked: false,
				isBtnLoader: false,
				checkDialogVisible: false,
				timer: null,
				count: 60,
				codebr: false,
				isLoading: false,
				silkyInfo: {
					signStatus: '',
					cancelStatus: ''
				},
				isSign: false,
				showFlag: true, //判断是否展示脱敏银行卡号
				isChange: false, //监听银行卡输入框是否变化
				smsDialogVisible: false,
				bankListDialogVisible: false,
				showBankName: false,
				isExpand: false,
				bankData: [],
				channelId: '********', //默认生产环境配置,
				selectBankItem: {}, // 银行信息
				bankCardInfo: {}, // 根据卡号查询银行卡信息
				curVehicleInfo: {}, //当前车辆信息
				sourceType: '0', //订单来源，默认新办0，抖音1
			}
		},
		computed: {},
		onShow(res) {

		},
		filters: {
			bankCardNoFilter(val) {
				return !!val ? val.substr(-4) : ''
			}
		},
		onLoad(option) {
			console.log('onLoad执行===>>>>');
			this.sourceType = option.sourceType
			this.getDraft()
			this.getDictHandle();
			//测试环境修改channelId
			// if (process.env.NODE_ENV === "development") {
			// 	this.channelId = '********'
			// }
			// this.vehicleInfo = getCurrentCar() ? getCurrentCar() : {}
			// this.customerInfo = getCurrUserInfo() ? getCurrUserInfo() : {},
			// 	//个人用户默认取用户信息里的姓名和身份证号，可更改
			// 	this.formData.customer_name =
			// 	this.customerInfo.customer_type == '0' ?
			// 	this.customerInfo.customer_name :
			// 	''
			// this.formData.certificates_code =
			// 	this.customerInfo.customer_type == '0' ?
			// 	this.customerInfo.certificates_code :
			// 	''

			// //如果是从查看协议界面跳回此界面，取缓存信息
			// if (option && option.isSign) {
			// 	this.isSign = true
			// }
			uni.onAppShow((res) => {
				console.log('uni.onAppShow===>>>>', res);
				if (res && res.scene === 1038) {
					const {
						appId,
						extraData
					} = res.referrerInfo
					if (appId == 'wxbcad394b3d99dac9') { // appId为wxbcad394b3d99dac9：从车主小程序跳转回来
						this.searchSilkyInfo();
					}
				}
			});


		},
		created() {
			this.getBankList()
		},
		watch: {
			//监听showflag,实现银行卡账号的显示与加密
			showFlag(val) {
				if (val) {
					this.dealBankNo()
				} else {
					this.formData.hideBankNo = this.formData.bankNo
				}
			}
		},
		onUnload() {
			uni.offAppShow();
		},
		methods: {
			onBankSelectHandle(item) {
				this.formData.bankNo = '';
				this.formData.mobile = '';
				this.formData.hideBankNo = '';
				this.selectBankItem = item;
				this.selectBankName = item.bankName;
				// 查询费率
				this.getServiceRate();
			},
			expandHandle(flag) {
				this.isExpand = flag
			},
			closePopup(type) {
				this.$refs.popup.close(type)
			},
			open() {
				this.$refs.bankSelect.open();

			},
			vehicleColorStr(val) {
				return getVehicleColor(val)
			},
			vehicleClassType(val) {
				return getVehicleClassType(val)
			},
			//监听输入框变化，判断是否更改表单内容
			changeInput(e) {
				if (e.detail.value) {
					this.isChange = true
					this.formData.bankNo = e.target.value
				} else {
					this.formData.bankNo = ''
				}
			},
			cancelDialog() {
				this.smsDialogVisible = false
				this.isChange = true
				this.formData.hideBankNo = this.formData.bankNo
			},
			//查询次次顺签约
			searchSilkyInfo() {
				console.log('签约查询===》》》》')
				if (!(this.vehicleInfo.vehicleCode && this.vehicleInfo.vehicleColor))
					return;
				let params = {
					hsApplyRecordId: this.vehicleInfo.id,
					vehicleCode: this.vehicleInfo.vehicleCode,
					vehicleColor: this.vehicleInfo.vehicleColor,
					sourceType: this.sourceType
				}
				this.isLoading = true
				return new Promise((resolve, reject) => {
					this.$request
						.post(this.$interfaces.newSearchSilkyInfo, {
							data: params
						})
						.then((res) => {
							console.log(res, '查询次次顺签约信息')
							this.isLoading = false
							if (res.code == 200) {
								let searchData = res.data[0]
								this.silkyInfo = {}
								this.silkyInfo.signStatus = ''
								this.silkyInfo.cancelStatus = ''
								this.title = ''
								if (searchData) {
									this.silkyInfo = searchData
									if (searchData.signStatus == '2') {
										this.title = '您已签约微信免密代扣'
										//签约成功
										this.checkDialogVisible = true
									} else if (searchData.signStatus == '4') {
										//其他签约状态
										this.title = '免密代扣签约失败'
									}
								} else {
									//无签约信息
								}

							} else {
								this.isLoading = false
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
							resolve(res)
						}).catch(err => {
							this.isLoading = false
							reject(err)
						})
				})

			},
			viewSignInfo(searchData) {
				if (this.isWechatSign) {
					this.signMode = searchData.bankId;
					this.signModeIndex = this.signModeList.findIndex((item) => {
						return item.value == this.signMode;
					})
				}
				this.formData.bankNo = searchData.bankCardNo
				this.formData.mobile = searchData.bankMobile
				this.formData.certificates_code = searchData.custIdNo
				this.formData.customer_name = searchData.custName
				this.formData.hideBankNo = searchData.bankCardNo;
			},
			//获取短息验证码
			getverification() {
				if (!this.validate()) return
				this.codebr = true
				let params = {
					channelId: this.channelId,
					mobile: this.formData.mobile,
					accountNo: this.formData.bankNo,
					idType: '101',
					idNumber: this.formData.certificates_code,
					name: this.formData.customer_name,
					licenseCode: this.vehicleInfo.vehicle_code,
					licenseColor: this.vehicleInfo.vehicle_color
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.silkySms, {
						data: params
					})
					.then((res) => {
						if (res.code === 200) {
							this.isLoading = false
							console.log(res, '短信验证码接口返回')
							this.formData.umsMsgNo = res.data.umsMsgNo
							this.timer = setInterval(() => {
								if (this.count > 0 && this.count <= 60) {
									this.count--
								} else {
									this.isLoading = false
									this.codebr = false
									clearInterval(this.timer)
									this.timer = null
									this.count = 60
								}
							}, 1000)
						} else {
							this.isLoading = false
							this.codebr = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						this.codebr = false
						uni.showModal({
							title: '提示',
							content: '获取失败',
							showCancel: false
						})
					})
			},
			changeSign() {
				this.smsDialogVisible = true
			},
			changeSingConfirm() {
				if (!this.validate()) return
				this.getBankName().then(res => {
					if (res.code == 200) {
						if (!this.formData.mobileCode || !this.formData.umsMsgNo) {
							uni.showModal({
								title: '提示',
								content: '请输入短信验证码',
								showCancel: false
							})
							return
						}
						this.next()
						this.smsDialogVisible = false
					}
				})

			},
			//次次顺签约
			// sign() {

			// 	this.isLoading = true
			// 	let params = {
			// 		customerId: this.customerInfo.customer_id,
			// 		vehicleCode: this.vehicleInfo.vehicle_code,
			// 		vehicleColor: this.vehicleInfo.vehicle_color,
			// 		channelId: this.channelId,
			// 		custName: this.formData.customer_name,
			// 		custIdType: '101',
			// 		custIdNo: this.formData.certificates_code,
			// 		bankMobile: this.formData.mobile,
			// 		bankCardNo: this.isChange ? this.fromData.dealBankNo : this.formData.bankNo,
			// 		umsVerifyCode: this.formData.mobileCode,
			// 		umsMsgNo: this.formData.umsMsgNo,
			// 		channelType: '2',
			// 		//签约：签约状态不存在、签约状态==0创建、签约状态==4签约失败，都要传0（签约），其余传1（更改签约）
			// 		isFirst: !this.silkyInfo.signStatus || this.silkyInfo.signStatus == '0' || this.silkyInfo
			// 			.signStatus == '4' ? '0' : '1'
			// 	}
			// 	//更改签约需要传入singId
			// 	if (params.isFirst == '1') {
			// 		params.oldSignId = this.silkyInfo.signId
			// 	}
			// 	this.$request
			// 		.post(this.$interfaces.saveSilkyInfo, {
			// 			data: params
			// 		})
			// 		.then((res) => {
			// 			console.log(res);
			// 			if (res.code === 200) {
			// 				console.log(res, '次次顺签约接口返回')
			// 				this.searchSilkyInfo().then(response => {

			// 					if (response.code == '200' && response.data[0].signStatus == '2') {
			// 						// 签约变更
			// 						if (params.oldSignId) {
			// 							uni.showModal({
			// 								title: '提示',
			// 								content: '更换绑定银行卡成功',
			// 								showCancel: false,
			// 							})
			// 							return
			// 						}
			// 						this.checkDialogVisible = true
			// 					} else {
			// 						uni.showModal({
			// 							title: '提示',
			// 							content: response.data[0].signStatus_str,
			// 							showCancel: false
			// 						})
			// 					}
			// 				})


			// 				uni.setStorageSync('signData', {})
			// 				this.isSign = false
			// 				this.formData.mobileCode = ''
			// 				this.formData.umsMsgNo = ''
			// 				return
			// 			}
			// 			if (res.code == '500') {
			// 				this.isSign = false
			// 				this.isLoading = false
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: '换绑业务已受理，点击确认查看受理结果',
			// 					success: res => {
			// 						if (res.confirm) {
			// 							this.searchSilkyInfo().then(response => {
			// 								if (response.code == '200' && response.data[0]
			// 									.signStatus == '2') {
			// 									if (params.oldSignId) {
			// 										uni.showModal({
			// 											title: '提示',
			// 											content: '更换绑定银行卡成功',
			// 											showCancel: false,
			// 										})
			// 										return
			// 									}
			// 									this.checkDialogVisible = true
			// 								} else {
			// 									uni.showModal({
			// 										title: '提示',
			// 										content: response.data[0]
			// 											.signStatus_str,
			// 										showCancel: false
			// 									})
			// 								}
			// 							})
			// 						}
			// 					}
			// 				})
			// 				return
			// 			}
			// 			uni.setStorageSync('signData', {})
			// 			this.isSign = false
			// 			this.formData.mobileCode = ''
			// 			this.formData.umsMsgNo = ''
			// 			this.searchSilkyInfo()
			// 			this.isLoading = false
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: res.msg,
			// 				showCancel: false
			// 			})

			// 		})
			// 		.catch((error) => {
			// 			this.isLoading = false
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error,
			// 				showCancel: false
			// 			})
			// 		})
			// },
			//表单校验
			validate() {
				if (!this.formData.customer_name) {
					uni.showModal({
						title: '提示',
						content: '请输入持卡人姓名',
						showCancel: false
					})
					return false
				}
				if (!this.formData.certificates_code) {
					uni.showModal({
						title: '提示',
						content: '请输入持卡人证件号',
						showCancel: false
					})
					return false
				}
				if (!this.formData.bankNo) {
					uni.showModal({
						title: '提示',
						content: '请输入银行卡号',
						showCancel: false
					})
					return false
				}

				if (!checkPhone(this.formData.mobile)) {
					uni.showModal({
						title: '提示',
						content: '请输入合法预留手机号',
						showCancel: false
					})
					return false
				}
				if (!!this.bankCardInfo && this.bankCardInfo.issueBankName != this.formData.bankName) {
					let bankName = this.bankCardInfo.issueBankName || ''
					uni.showModal({
						title: '提示',
						content: '输入的银行【' + bankName + '】与所选签约银行不符请更换银行卡或签约银行后进行重试',
						showCancel: false
					})
					return false
				}
				return true
			},
			//撤销签约
			// cancelConfirm() {
			// 	uni.showModal({
			// 		title: '提示',
			// 		content: '请确定是否要撤销签约',
			// 		cancelText: '取消',
			// 		confirmText: '确定',
			// 		success: res => {
			// 			if (res.confirm) {
			// 				this.cancel()
			// 			}
			// 		}
			// 	})
			// },
			// cancel() {
			// 	this.isLoading = true
			// 	let params = {
			// 		customerId: this.customerInfo.customer_id ? Number(this.customerInfo.customer_id) : "",
			// 		vehicleCode: this.vehicleInfo.vehicle_code,
			// 		vehicleColor: this.vehicleInfo.vehicle_color,
			// 		channelId: this.channelId,
			// 	}
			// 	this.$request
			// 		.post(this.$interfaces.silkyCancel, {
			// 			data: params
			// 		})
			// 		.then((res) => {
			// 			if (res.code === 200) {
			// 				// this.isLoading = false
			// 				this.searchSilkyInfo().then(res => {
			// 					if (res.code == '200') {
			// 						uni.showModal({
			// 							title: '提示',
			// 							content: '撤销签约成功',
			// 							showCancel: false
			// 						})
			// 					}
			// 				})
			// 				this.formData.mobileCode = ''
			// 				this.formData.umsMsgNo = ''
			// 				Object.keys(this.formData).forEach((key) => {
			// 					this.formData[key] = ''
			// 				})
			// 			} else {
			// 				this.isLoading = false
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				})
			// 			}
			// 		})
			// 		.catch((error) => {
			// 			this.isLoading = false
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error,
			// 				showCancel: false
			// 			})
			// 		})
			// },
			goNext() {
				if (this.sourceType == '1') {
					//抖音流程先跳过,直接完成
					uni.reLaunch({
						url: '/pagesA/newBusiness/order/orderSuccess?type=1'
					})
					return
				}
				this.getSignStatus(resStatus => {
					if (resStatus) {
						//没签约过,去签约
						this.createUserSign()
					} else {
						//签约过直接去下单
						uni.redirectTo({
							url: '/pagesA/newBusiness/userAgreement/agreementList'
						})
					}
				})
			},
			//跳转拓展业务平台
			// goH5() {
			// 	this.checkDialogVisible = false
			// 	let etcAccountInfo = getEtcAccountInfo() && Object.keys(getEtcAccountInfo()).length ? getEtcAccountInfo() :
			// 	{}
			// 	let vehicleInfo = getCurrentCar() && Object.keys(getCurrentCar()).length ? getCurrentCar() : {}
			// 	let data = {
			// 		ticket: getTicket() || '',
			// 		custMastId: etcAccountInfo.custMastId || '',
			// 		openId: getOpenid(),
			// 		carNo: vehicleInfo.vehicle_code || this.vehicleInfo.vehicle_code,
			// 		carColor: vehicleInfo.vehicle_color || this.vehicleInfo.vehicle_colors,
			// 		type: 'sign'
			// 	}
			// 	vue.prototype.$request
			// 		.post(vue.prototype.$interfaces.expandUrl, {
			// 			data: data
			// 		})
			// 		.then((res) => {
			// 			console.log(res, '获取拓展业务跳转url');
			// 			if (res.code == 200) {
			// 				const callcenter = res.data
			// 				uni.navigateTo({
			// 					url: '/pages/uni-webview/uni-webview?ownPath=' +
			// 						encodeURIComponent(callcenter)
			// 				})
			// 			} else {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				})
			// 			}

			// 		})
			// 		.catch((error) => {
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error.msg,
			// 				showCancel: false
			// 			})
			// 		})
			// },
			//跳转协议界面，缓存表单信息
			next() {
				if (!this.validate()) return

				if (!this.formData.mobileCode || !this.formData.umsMsgNo) {
					uni.showModal({
						title: '提示',
						content: '请输入短信验证码',
						showCancel: false
					})
					return false
				}
				// 移除银行卡信息校验 ********
				let params = {
					...this.formData,
					customerId: this.customerInfo.customer_id,
					umsVerifyCode: this.formData.mobileCode,
					umsMsgNo: this.formData.umsMsgNo,
					selectBankName: this.selectBankName,
					serviceRate: this.serviceRate
				}
				uni.setStorageSync('signData', params)
				uni.navigateTo({
					url: './agreement?' + this.objToUrlParam(params)
				})

			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},
			//脱敏银行卡账号
			dealBankNo() {
				if (!this.formData.bankNo) return
				this.formData.hideBankNo = this.formData.bankNo.substring(0, 4) + "********" + this.formData.bankNo
					.substring(this.formData.bankNo.length - 4);
			},
			//显示可签约银行列表
			showBankList() {
				this.bankListDialogVisible = true
			},
			ocrBankImg() {
				this.$refs.cpimg._changImg('0');
			},
			cpimgOk(file) {
				this.sendOCR(file);
			},
			cpimgErr(e) {
				console.log(e)
			},
			// ocr识别
			sendOCR(file) {
				let base64Img = file.toString()
				let ocr_type = '';
				let current = {};
				var imgStr = base64Img.split(';')[1].split(",")[1] + '';
				let biz_content = {
					ocr_type: 1,
					file_name: 'file_name',
				}
				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let encryptedData = res.data.encryptedData;
						if (encryptedData.bankNo) {
							let trimBankNo = encryptedData.bankNo.replace(/\s*/g, "")
							this.formData.bankNo = trimBankNo
							this.formData.hideBankNo = trimBankNo
							this.isChange = true
							// 仅银联渠道银行卡校验
							if (this.signMode == 'CUP' || this.selectBankItem.type == 'CUP') {
								// 对比OCR识别银行名称是否一致
								this.checkBankName();
							}

						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			onBlur(event) {
				event.stopPropagation();
				if (!this.formData.bankNo) return
				let reg = /^(\d{14,19})$/g
				if (!reg.test(this.formData.bankNo)) {
					uni.showModal({
						title: "提示",
						content: '银行卡号长度不合法，重新识别或重新输入',
						showCancel: false,
					});
					return
				}
				// 银行卡号失去焦点 查询银行名称是否一致
				this.checkBankName()
			},
			checkBankName() {
				let params = {
					cardNo: this.formData.bankNo
				}
				this.isLoading = true
				this.bankCardInfo = null;
				this.getBankNameByCardNo(params).then((res) => {
					this.isLoading = false
					console.log('getBankNameByCardNo==>>>', res)
					if (res.code === 200) {
						this.bankCardInfo = res.data;
						if ((this.silkyInfo && this.silkyInfo.signStatus == '2') || res.data.issueBankName == this
							.selectBankItem.bankName) {
							this.formData.bankName = res.data.issueBankName
							this.showBankName = true
						} else {
							uni.showModal({
								title: '提示',
								content: '输入的银行与所选签约银行不符请更换银行卡或签约银行后进行重试',
								showCancel: false
							})
						}
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						})
					}
				}).catch(err => {
					this.isLoading = false
				})
			},
			//根据银行卡号查询名称
			getBankName(val) {
				this.isLoading = true
				this.bankCardInfo = null;
				let data = {
					routePath: this.$interfaces.getBankName.method,
					bizContent: {
						cardNo: this.formData.bankNo
					}
				}
				return new Promise((resolve, reject) => {
					this.$request
						.post(this.$interfaces.issueRoute, {
							data: data
						})
						.then((res) => {

							if (!val) {
								this.isLoading = false
							}
							this.bankCardInfo = res.data;
							if (res.code === 200) {
								this.formData.bankName = res.data.issueBankName
								this.showBankName = true
							} else {
								this.formData.bankName = ''
								this.isLoading = false
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
							resolve(res)
						}).catch(err => {
							this.isLoading = false
							reject(err)
						})
				})

			},
			//查询支持银行卡列表
			getBankList() {
				let data = {
					routePath: this.$interfaces.getBankList.method,
					bizContent: {

					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						if (res.code === 200) {
							let result = res.data
							//黄牌及黄绿双拼牌 才有微信指定卡签约
							if (!this.isWechatCardSign) {
								this.bankData = result.filter(item => {
									return item.type == 'CUP'
								});
							} else {
								this.bankData = result
							}
							console.log('银行卡列表===>>>', result)



						} else {

							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {

						uni.showModal({
							title: '提示',
							content: error,
							showCancel: false
						})
					})
			},
			//取消跳转到首页
			checkDialogVisibleClose() {
				this.checkDialogVisible = false
				uni.redirectTo({
					url: '/pages/home/<USER>/p-home'
				})
			}
		}
	}
</script>

<style lang='scss'>
	@import './sign.scss'
</style>