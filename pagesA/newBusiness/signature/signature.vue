<template>
	<view>
		<web-view ref="webview" :src="ownPath" @message="handlePostMessage" />
	</view>
</template>

<script>
	import {
		uni_decodeURIComponent
	} from '@/common/helper.js';

	export default {
		data() {
			return {
				ownPath: '',
				isVip: false,
				nodeStep: '4',
				vehicles: {},
				nextFlag: false
			}
		},
		onLoad(options) {
			this.ownPath = uni_decodeURIComponent(options.ownPath) || ''
			this.vehicles = JSON.parse(uni_decodeURIComponent(options.vehicles)) || {}

			// 废弃
			// if (Object.keys(this.vehicles).length > 0) {
			// 	this.checkIsFreeVehicle(this.vehicles)
			// }
		},
		// onUnload() {
		// 	if (this.nextFlag) return;
		// 	let currentRoutes = getCurrentPages();
		// 	console.log('currentRoutessign', currentRoutes)
		// 	let lastRoutes = currentRoutes[currentRoutes.length - 2].route
		// 	if (lastRoutes == 'pagesA/newBusiness/vehicle/vehicle') return;
		// 	uni.navigateTo({
		// 		url: '/pagesA/newBusiness/vehicle/vehicle'
		// 	})
		// },
		methods: {
			// webview向外部发送消息
			handlePostMessage(evt) {
				console.log("接收到消息：" + JSON.stringify(evt.detail));
				if (evt.detail.data && evt.detail.data[0] && evt.detail.data[0].action == 'toUserAgreement') {
					this.toNext()
				}
			},
			toNext() {
				this.isLoading = true
				let params = {
					id: this.$store.state.applyId
				}
				this.$request.post(this.$interfaces.getDraft, {
					data: params
				}).then(res => {
					this.isLoading = false
					if (res.code != 200) {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						return
					}

					let result = res.data
					this.getSignStatus(result, (res) => {
						if (res) {
							//需要签协议，返回上一页重签
							uni.navigateBack()
						} else {
							//签完下一页
							uni.redirectTo({
								url: '/pagesA/newBusiness/userAgreement/agreementList'
							})
						}
					})

				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//查看签署状态
			getSignStatus(applyId, callback) {
				console.log('签约状态查询==========>>>>', applyId)
				this.isLoading = true;
				let params = {
					otherId: result.id,
					vehicleCode: result.vehicleCode,
					vehicleColor: result.vehicleColor,
					cardType: result.gxCardType ,//-1是八桂卡，其他是卡类型
					signVersion: 'V2',
					businessType: '1',
				}
				let data = {
					data: params,
				};

				this.$request
					.post(this.$interfaces.getSignStatus, data)
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							if (res.data.isNecessary && !res.data.conStatus) {
								//需要签署协议。
								callback(true)
							} else {
								callback(false)
							}
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
		}
	}
</script>

<style lang="scss" scoped>
</style>