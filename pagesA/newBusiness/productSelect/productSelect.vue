<!-- 安装方式选择 -->
<template>
	<view class="product-select">
		<handle-step :current="0" />
		<view class="order-wrapper" v-if="showOrdering" @click="toApplyList">
			<view class="order-tip">
				查看进行中的订单
			</view>
			<image style="width: 34rpx;height: 34rpx" src="../../static/new-apply/product-select/order-arrow.png"
				mode=""></image>
		</view>
		<view class="car-type">
			<title title="客货选择"></title>
			<view class="car-wrapper g-flex g-flex-align-center justify-center">
				<view class="car-item" @click="selectType(item.value)"
					:class="item.value == formData.vehicleType?'selector':''" v-for="(item,index) in vehicleTypeList"
					:key="index">
					<image :src="item.src" mode=""></image>
					<view>{{item.label}}</view>
				</view>
			</view>
		</view>
		<view class="card-type">
			<title title="产品选择"></title>
			<view class="card-warpper">
				<view v-for="(item,index) in productList" :key="index" @click="selectGxCard(item)" class="card-item"
					:class="formData.gxCardType == item.gxCardType?'card-selector' :''">
					<image class="img" v-if="!formData.gxCardType"
						:src="`../../static/new-apply/product-select/icon-card_${index + 1}.png`" mode=""></image>
					<image class="img" v-if="formData.gxCardType == '5' && formData.gxCardType == item.gxCardType"
						:src="card1_check" mode=""></image>
					<image class="img" v-if="formData.gxCardType == '10' && formData.gxCardType == item.gxCardType"
						:src="card2_check" mode=""></image>
					<image class="img" v-if="formData.gxCardType == '5' && formData.gxCardType != item.gxCardType"
						:src="card2" mode=""></image>
					<image class="img" v-if="formData.gxCardType == '10' && formData.gxCardType != item.gxCardType"
						:src="card1" mode=""></image>
					<view class="card-text">
						<view class="card-name">
							{{item.productName}}
						</view>
						<view class="card-desc">
							{{item.productDesc}}
						</view>
					</view>
				</view>
				<view class="ccs-content" :class="item.deviceType == formData.deviceType?'selector':''"
					v-if="formData.gxCardType == '10'" v-for="(item,index) in ccsProductList" :key="index"
					@click="setDeviceType(item)">
					<view class="content-card">
						<view class="card-title">
							捷通次次顺记账卡（{{item.deviceType == '1'?'基础款':'进阶款' }}）
						</view>
						<view class="card-content">
							<image class="img"
								:src="item.deviceType == '2'?'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/jjk.png':'../../static/new-apply/ccs/ccs_product.png'"
								style="width: 190rpx;height: 120rpx;" mode=""></image>
							<view class="card-text">
								<view class="text">
									1、绑定微信支付账户代扣
								</view>
								<view class="text" style="margin-top: 24rpx;">
									2、先通行后付费
								</view>
							</view>
							<view class="price">
								￥{{moneyFilter(item.benefitServiceFee)}}
							</view>
						</view>
						<view class="card-tips">
							*权益服务内容:连续12个月免费领取{{item.deviceType == '2'?'60':'50'}}元/月商场消费券礼包，每张消费券须在领取当月使用完毕。
						</view>
					</view>
				</view>
				<view v-if="formData.gxCardType == '10'" class="tip-text">
					* 图片仅供参考，产品以实物为准
				</view>
			</view>
		</view>
		<view class="package" v-if="formData.gxCardType == '5'">
			<title title="产品明细"></title>
			<view class="package-wrapper">
				<view class="price-package">
					<view class="price-desc">
						权益服务费
					</view>
					<view class="price">
						{{moneyFilter(benefitServiceFee)}}元
					</view>
				</view>
				<view class="tips">
					<view class="tips-title">
						权益服务内容
					</view>
					<view class="tips-item">
						1.赠送最高30元自助充值增值券1张；
					</view>
					<view class="tips-item">
						2.连续12个月免费领取50元/月商城消费券礼包；
					</view>
				</view>
			</view>
		</view>
		<tButton @toNext="toNext" :buttonList="buttonList"></tButton>
		<tLoading :isShow="isLoading" />

		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:showConfirm="modal.showConfirm">
			<view class="content-wrapper">
				<view class="title">办理须知</view>
				<scroll-view class="desc-wrapper" scroll-y="true">
					<view class="">
						<rich-text v-if="Object.keys(config).length > 0 && formData.gxCardType == '5'" class="rich-text"
							:nodes="config.ddpRemark"></rich-text>
						<rich-text
							v-if="Object.keys(config).length > 0 && formData.gxCardType == '10' && formData.deviceType == '1'"
							class="rich-text" :nodes="config.ttpRemark"></rich-text>
						<rich-text
							v-if="Object.keys(config).length > 0 && formData.gxCardType == '10' && formData.deviceType == '2'"
							class="rich-text" :nodes="config.ttpNewRemark"></rich-text>
					</view>
				</scroll-view>
				<view class="check-wrapper" @click="check">
					<image v-if="!isChecked" style="width: 28rpx;height: 28rpx;"
						src="../../static/new-apply/vehicle/check.png" mode="">
					</image>
					<image v-if="isChecked" style="width: 28rpx;height: 28rpx;"
						src="../../static/new-apply/vehicle/checked.png" mode="">
					</image>
					<text class="check-text">我已阅读，确认无问题</text>
				</view>
				<view class="btn-wrapper">
					<view class="cancel-btn" @click="cancel">
						取消
					</view>
					<view class="confirm-btn" @click="confirm">
						继续办理
					</view>
				</view>
			</view>
		</neil-modal>
		<u-modal v-model="uShow" @confirm="uShowConfirm" :confirm-text="confirmText" :content="limitContent"
			ref="uModal" :async-close="true">
		</u-modal>
	</view>
</template>

<script>
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import title from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import neilModal from '@/components/neil-modal/neil-modal.vue'
	import {
		getOpenid,
		setOpenidForRead,
		getOpenidForRead,
		getLoginUserInfo,
		setUserId,
		getUserId
	} from '@/common/storageUtil.js';
	export default {
		components: {
			handleStep,
			tLoading,
			title,
			tButton,
			neilModal
		},
		data() {
			return {
				isLoading: false,
				isChecked: false,
				uShow: false,
				showOrdering: false, //正在进行中订单
				limitContent: '', //新办限制内容
				benefitServiceFee: '',
				salesChannels: '0', //C端渠道
				confirmText: '返回首页',
				applyId: '',
				formData: {
					businessType: '1', //1：新办 2：换卡 3：换obu
					vehicleType: '',
					gxCardType: '',
					nodeStep: '1',
					book: '',
					id: '',
					productType: '', //产品编号
					deviceType: '1' //次次顺设备类型
				},
				userId: '', //推广码操作员ID
				buttonList: [{
					title: '下一步',
					handle: 'toNext'
				}],
				vehicleTypeList: [{
					value: '2',
					src: '../../static/new-apply/product-select/car_1.png',
					label: '客车',
				}, {
					value: '1',
					src: '../../static/new-apply/product-select/car_2.png',
					label: '货车',
				}, {
					value: '3',
					src: '../../static/new-apply/product-select/car_3.png',
					label: '专项车',
				}],
				productList: [], //产品列表
				ccsProductList: [], //次次顺产品列表
				config: {}, //办理须知内容
				card1: '../../static/new-apply/product-select/icon-card_1.png',
				card2: '../../static/new-apply/product-select/icon-card_2.png',
				card1_check: '../../static/new-apply/product-select/icon-card_1_check.png',
				card2_check: '../../static/new-apply/product-select/icon-card_2_check.png',
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					showConfirm: false,
				},
				vehicleCode: '',
				vehicleColor: '',
				openId: ''
			};
		},
		onLoad(option) {
			if (option && option.applyId) {
				this.formData.id = option.applyId
			}
			if (option && option.userId) {
				this.userId = option.userId
				setUserId(option.userId)
			} else {
				this.userId = getUserId() || ''
			}
			this.isOrder()
			this.limit()
			if (!this.formData.productType) {
				this.getProduct()
			}

			// this.getRuleConfig()
		},
		// created() {
		// 	//#ifdef  MP-WEIXIN
		// 	this.getOpenIdHandle();
		// 	// #endif	
		// },
		methods: {
			// isCcsShow(item) {
			// 	return this.formData.vehicleType == '2' || (this.formData.vehicleType != '2' && item.productCode != '10')
			// },
			//获取openId
			// getOpenIdHandle() {
			// 	if (getOpenid()) return;
			// 	let _self = this;
			// 	wx.login({
			// 		success(res) {
			// 			let params = {
			// 				code: res.code
			// 			}

			// 			_self.$request.post(_self.$interfaces.getOpenid, {
			// 				data: params
			// 			}).then((res) => {
			// 				if (res.code == 200) {
			// 					if (res.data && res.data.openid) {
			// 						_self.openId = res.data.openid
			// 						setOpenid(res.data.openid)
			// 					}
			// 				}
			// 			})
			// 		}
			// 	})
			// },
			// getRuleConfig() {
			// 	this.isLoading = true
			// 	this.$request
			// 		.post(this.$interfaces.getRuleConfig)
			// 		.then(res => {
			// 			console.log('getRuleConfig', res)
			// 			this.isLoading = false
			// 			if (res.code != 200) {
			// 				uni.showModal({
			// 					title: '提示',
			// 					content: res.msg,
			// 					showCancel: false
			// 				});
			// 				return
			// 			} else {
			// 				this.config = res.data
			// 			}
			// 		})
			// 		.catch(error => {
			// 			this.isLoading = false
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error.msg,
			// 				showCancel: false
			// 			});
			// 		});
			// },
			// limit() {
			// 	let data = {
			// 		userNo: getLoginUserInfo().userNo
			// 	};
			// 	this.isLoading = true
			// 	this.$request
			// 		.post(this.$interfaces.applyStatus, {
			// 			data: data
			// 		})
			// 		.then(res => {
			// 			console.log('limit', res)
			// 			this.isLoading = false
			// 			if (res.code != 200) {
			// 				this.uShow = true
			// 				this.limitContent = res.msg || '您已超过申办订单的最大限制'
			// 			} else {
			// 				this.getApplyId((resp1) => {
			// 					console.log('resp1', resp1)
			// 					if (res.data.nodeCode) {
			// 						this.applyId = res.data.applyRecordId
			// 						if (res.data.nodeCode == '900') {
			// 							let vehicleInfo = {
			// 								vehicleCode: resp1.vehicleCode,
			// 								vehicleColor: resp1.vehicleColor,
			// 								hsApplyRecordId: this.applyId,
			// 								openId: getOpenid() || this.openId
			// 							}
			// 							this.checkUnderSign(vehicleInfo, (resp2) => {
			// 								console.log('resp2', resp2)
			// 								if (resp2.checkSilkyFlag == 1) {
			// 									//线下签约判断，已签约，直接跳过签约，去签章
			// 									this.uShow = true
			// 									this.limitContent = '您有待签署的订单未完成！'
			// 									this.confirmText = '去签署'
			// 									this.$store.dispatch('setApplyId', res.data
			// 										.applyRecordId)
			// 								} else {
			// 									//线下签约判断，未签约，调用签约查询接口查询状态
			// 									this.searchSilkyInfo().then(response => {
			// 										console.log('response', response)
			// 										if (response.code == 200) {
			// 											if (response.data.length == 0) {
			// 												//未签约过，先签约银联
			// 												this.uShow = true
			// 												this.limitContent =
			// 													'您有待签约的订单未完成！'
			// 												this.confirmText = '去签约'
			// 												this.$store.dispatch(
			// 													'setApplyId', this
			// 													.applyId)
			// 											} else {
			// 												//有签约数据需要判断
			// 												if (response.data[0].signStatus ==
			// 													'0') {
			// 													//未签约过，去签约银联
			// 													//已经签署完成，继续签署次次顺协议
			// 													this.uShow = true
			// 													this.limitContent =
			// 														'您有待签约的订单未完成！'
			// 													this.confirmText = '去签约'
			// 													this.$store.dispatch(
			// 														'setApplyId', this
			// 														.applyId)

			// 												} else if (response.data[0]
			// 													.signStatus ==
			// 													'2') {
			// 													//已经签署完成，继续签署次次顺协议
			// 													this.uShow = true
			// 													this.limitContent =
			// 														'您有待签署的订单未完成！'
			// 													this.confirmText = '去签署'
			// 													this.$store.dispatch(
			// 														'setApplyId', this
			// 														.applyId)
			// 												} else if (response.data[0]
			// 													.signStatus ==
			// 													'1') {
			// 													//签署中
			// 													uni.showModal({
			// 														title: "提示",
			// 														content: '订单签约中，请稍后再尝试下一步'
			// 													});
			// 												} else if (response.data[0]
			// 													.signStatus ==
			// 													'4') {
			// 													//签约失败，去签约
			// 													this.uShow = true
			// 													this.limitContent =
			// 														'您有待签约的订单未完成！'
			// 													this.confirmText = '去签约'
			// 													this.$store.dispatch(
			// 														'setApplyId', this
			// 														.applyId)
			// 												}
			// 											}
			// 										}
			// 									})
			// 								}
			// 							})
			// 						} else if (res.data.nodeCode == '990') {
			// 							this.uShow = true
			// 							this.limitContent = '您有待支付的订单未完成！'
			// 							this.confirmText = '去支付'
			// 							this.$store.dispatch('setApplyId', res.data.applyRecordId)
			// 						}
			// 					}
			// 				})
			// 				// else {
			// 				//无限制继续进行
			// 				// this.getApplyId()
			// 				// }

			// 			}
			// 		})
			// 		.catch(error => {
			// 			this.isLoading = false
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: error.msg,
			// 				showCancel: false
			// 			});
			// 		});
			// },
			limit() {
				let data = {
					userNo: getLoginUserInfo().userNo
				};
				this.isLoading = true
				this.$request
					.post(this.$interfaces.applyStatus, {
						data: data
					})
					.then(res => {
						console.log('limit', res)
						this.isLoading = false
						if (res.code != 200) {
							this.uShow = true
							this.limitContent = res.msg || '您已超过申办订单的最大限制'
						} else {
							if (res.data.nodeCode) {
								this.applyId = res.data.applyRecordId
								if (res.data.nodeCode == '900') {
									this.uShow = true
									this.limitContent = '您有待签署的订单未完成！'
									this.confirmText = '去签署'
									this.$store.dispatch('setApplyId', res.data.applyRecordId)
								} else if (res.data.nodeCode == '990') {
									this.uShow = true
									this.limitContent = '您有待支付的订单未完成！'
									this.confirmText = '去支付'
									this.$store.dispatch('setApplyId', res.data.applyRecordId)
								}
							} else {
								//无限制继续进行
								this.getApplyId()
							}

						}
					})
					.catch(error => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			// checkUnderSign(vehicleInfo, callback) {
			// 	this.isLoading = true;
			// 	let params = {
			// 		vehicleCode: vehicleInfo.vehicleCode,
			// 		vehicleColor: vehicleInfo.vehicleColor,
			// 		hsApplyRecordId: vehicleInfo.hsApplyRecordId,
			// 		openId: vehicleInfo.openId
			// 	}
			// 	let data = {
			// 		data: params,
			// 	};

			// 	console.log('入参', data)
			// 	this.$request
			// 		.post(this.$interfaces.checkUnderSign, data)
			// 		.then((res) => {
			// 			console.log('res', res)
			// 			this.isLoading = false;
			// 			if (res.code == 200) {
			// 				callback(res.data)
			// 			} else {
			// 				uni.showModal({
			// 					title: "错误",
			// 					content: res.msg,
			// 					showCancel: false,
			// 				});
			// 			}
			// 		})
			// 		.catch((err) => {
			// 			this.isLoading = false;
			// 			uni.showModal({
			// 				title: "错误",
			// 				content: err.msg,
			// 				showCancel: false,
			// 			});
			// 		});
			// },
			searchSilkyInfo() {
				let params = {
					// customerId: this.customerInfo.customer_id, //改成申请单id
					hsApplyRecordId: this.applyId,
					vehicleCode: this.vehicleCode,
					vehicleColor: this.vehicleColor
				}
				this.isLoading = true
				return new Promise((resolve, reject) => {
					this.$request
						.post(this.$interfaces.newSearchSilkyInfo, {
							data: params
						})
						.then((res) => {
							console.log(res, '查询次次顺签约信息')
							this.isLoading = false
							if (res.code == 200) {

							} else {
								this.isLoading = false
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
							resolve(res)
						}).catch(err => {
							this.isLoading = false
							reject(err)
						})
				})

			},
			isOrder() {
				let data = {
					userNo: getLoginUserInfo().userNo
				};
				// this.isLoading = true
				this.$request
					.post(this.$interfaces.isOrderIng, {
						data: data
					})
					.then(res => {
						console.log('isOrder', res)
						if (res.code == 200) {
							this.showOrdering = res.data.isOrderIng
						}
					})
					.catch(error => {
						// this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			uShowConfirm() {
				if (this.confirmText == '返回首页') {
					uni.reLaunch({
						url: '/pages/home/<USER>/p-home'
					})
				} else if (this.confirmText == '去签署') {
					this.uShow = false
					//设置推广员信息
					this.setPromoterId()
					//跳转车辆列表
					uni.redirectTo({
						url: '/pagesA/newBusiness/order/orderDetail?applyId=' + this.applyId
					})
				} else if (this.confirmText == '去支付') {
					//设置推广员信息
					this.setPromoterId()
					//支付页面
					uni.redirectTo({
						url: '/pagesA/newBusiness/order/order?applyId=' + this.applyId
					})
				} else if (this.confirmText == '去签约') {
					uni.redirectTo({
						url: '/pagesA/newBusiness/signature/ccsSign/index'
					})
				}
			},
			check() {
				this.isChecked = !this.isChecked
			},
			cancel() {
				this.modal.show = false
			},
			//设置推广员，不需要报错
			setPromoterId() {
				let data = {
					id: this.applyId,
					promoterId: this.userId
				};

				this.$request
					.post(this.$interfaces.setPromoterId, {
						data: data
					})
					.then(res => {})
					.catch(error => {});
			},
			confirm() {
				if (!this.isChecked) {
					uni.showModal({
						title: '提示',
						content: '请确认无问题后勾选我已阅读。',
					});
					return
				}
				this.modal.show = false
				//保存草稿
				this.isLoading = true
				let params = this.formData
				console.log(params, '入参');
				this.$request.post(this.$interfaces.saveDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						// //获取所有草稿
						// 订阅
						// if (!this.formData.book) {
						this.read()
						// }

						uni.navigateTo({
							url: '/pagesA/newBusiness/installation/installation'
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			getApplyId(callback) {
				this.isLoading = true
				let data = {
					id: this.formData.id,
					promoterId: this.userId
				};

				if (!data.id) {
					delete data.id
				}

				this.$request
					.post(this.$interfaces.getApplyId, {
						data: data
					})
					.then(res => {
						this.isLoading = false
						if (res.code != 200) {
							uni.showModal({
								title: '提示',
								content: res.msg,
							});
						} else {
							console.log(res, '申请单创建');
							let applyId = res.data.id
							this.formData.id = applyId
							this.$store.dispatch('setApplyId', applyId)
							this.getDraft(applyId, callback)
						}
					})
					.catch(error => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			read() {
				console.log('订阅方法')
				uni.requestSubscribeMessage({
					tmplIds: ['6Q0Praee_bD-2ZFNkFSQYSztnN3u8LKBWzM-i8W2Gc4'],
					success: (res) => {
						console.log('订阅方法', res['6Q0Praee_bD-2ZFNkFSQYSztnN3u8LKBWzM-i8W2Gc4'])
						// let msgContent = res['6Q0Praee_bD-2ZFNkFSQYSztnN3u8LKBWzM-i8W2Gc4']
						// if (msgContent == 'accept') {
						// 	this.formData.book = '1'
						this.wxGetOpenId()
						// } else if (msgContent == 'reject') {
						// 	this.formData.book = '0'
						// 	uni.showModal({
						// 		title: "提示",
						// 		content: '已拒绝订阅消息',
						// 		showCancel: false,
						// 	});
						// }
					},
					fail: (err) => {
						console.log(err)
					}
				})
			},
			wxGetOpenId() {
				let _self = this;
				let readOpenId = getOpenidForRead()
				console.log('readOpenId-product', readOpenId)
				if (readOpenId) {
					this.saveOpenId(readOpenId)
				} else {
					// 微信手机号授权登录
					wx.login({
						success(res) {
							let params = {
								code: res.code
							}

							_self.$request.post(_self.$interfaces.getOpenid, {
								data: params
							}).then((res) => {
								if (res.code == 200) {
									if (res.data && res.data.openid) {
										console.log('获取微信openId', res.data.openid)
										setOpenidForRead(res.data.openid)
										_self.saveOpenId(res.data.openid)
									}
								}
							})
						}
					})
				}
			},
			//获取用户openId保存订阅
			saveOpenId(openId) {
				this.$request.post(this.$interfaces.saveOpenId, {
					data: {
						netUserId: getLoginUserInfo().userIdStr,
						openId: openId
					}
				}).then(res => {}).catch(err => {})
			},
			getProduct() {
				this.isLoading = true
				// let params = this.formData
				// console.log(params, '入参');
				this.$request.post(this.$interfaces.getProduct, {
					data: {
						salesChannels: this.salesChannels
					}
				}).then(res => {
					console.log('res==>>>', res)
					this.isLoading = false;
					if (res.code == 200) {
						console.log('resdata', res.data)
						let resutl = res.data
						let rrtFilter = resutl.filter(item => {
							return item.productCode == '5'
						})
						let ccsFilter = resutl.filter(item => {
							return item.productCode == '10'
						})
						if (ccsFilter.length > 0) {
							this.productList = rrtFilter.concat(ccsFilter[0])
							this.ccsProductList = ccsFilter
						} else {
							this.productList = rrtFilter
						}

						// console.log('this.formData.productType', this.formData.productType, this.productList)
						//获取办理须知
						if (this.formData.productType == '5') {
							let configFilter = resutl.filter(item => {
								return item.productCode == '5'
							})
							this.config = configFilter[0] || {}
						} else if (this.formData.productType == '10') {
							console.log('this.formData.deviceType', this.formData.deviceType, this.formData
								.productType)
							let configDevice = resutl.filter(item => {
								return item.deviceType == this.formData.deviceType && item.productCode ==
									this.formData.productType
							})
							console.log('configDevice', configDevice)
							this.config = configDevice[0] || {}
						}
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			setPrice() {
				console.log('==>>>>>>>>>>>>>>>>>>>>', this.productList)
				this.productList.forEach(item => {
					if (item.gxCardType == this.formData.gxCardType) {
						this.benefitServiceFee = item.benefitServiceFee
						this.productContent = item.productDetailDesc || ''
					}
				})
			},
			getDraft(id, callback) {
				this.isLoading = true
				let params = {
					id: id
				}
				// console.log(params, '入参');
				this.$request.post(this.$interfaces.getDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data

						console.log('草稿===》》》', res.data)
						this.formData.vehicleType = result.vehicleType || ''
						this.formData.gxCardType = result.gxCardType || ''
						this.formData.productType = result.productType || ''
						this.formData.deviceType = result.deviceType || '1'
						this.vehicleCode = result.vehicleCode || ''
						this.vehicleColor = result.vehicleColor || ''
						if (result.gxCardType) {
							this.setPrice()
							this.getProduct()
						}
						callback(result)

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			selectType(value) {
				if (this.formData.gxCardType == '10' && value != '2') {
					uni.showModal({
						title: "提示",
						content: "其他次次顺车型即将上线，敬请期待",
						showCancel: false,
					});
					return
				}
				this.formData.vehicleType = value
			},
			selectGxCard(item) {
				if (this.formData.gxCardType == item.gxCardType) {
					//不可重复选
					return
				}
				if (item.gxCardType == '10' && this.formData.vehicleType && this.formData.vehicleType != '2') {
					uni.showModal({
						title: "提示",
						content: "其他次次顺车型即将上线，敬请期待",
						showCancel: false,
					});
					return
				}
				// if (this.formData.gxCardType == item.gxCardType) {
				// 	this.formData.gxCardType = ''
				// } else {
				this.formData.gxCardType = item.gxCardType
				// }

				this.benefitServiceFee = item.benefitServiceFee
				this.formData.productType = this.formData.gxCardType
				if (this.formData.gxCardType == '10') {
					this.formData.deviceType = item.deviceType
				} else if (this.formData.gxCardType == '5') {
					this.formData.deviceType = '1'
				}
			},
			setDeviceType(item) {
				this.formData.deviceType = item.deviceType
				this.config = item
			},
			valid() {
				if (!this.formData.vehicleType) {
					uni.showModal({
						title: "提示",
						content: "请先选择客货类型",
						showCancel: false,
					});
					return false
				} else if (!this.formData.gxCardType) {
					uni.showModal({
						title: "提示",
						content: "请先选择产品",
						showCancel: false,
					});
					return false
				} else if (!this.formData.deviceType) {
					uni.showModal({
						title: "提示",
						content: "请先选择产品的设备类型",
						showCancel: false,
					});
					return false
				}
				return true
			},
			toNext() {
				if (!this.valid()) {
					return
				}
				this.getProduct()
				this.modal.show = true
			},
			toApplyList() {
				uni.navigateTo({
					url: '/pagesA/serviceOrder/index?routeType=productSelect'
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	};
</script>



<style scoped lang="scss">
	.product-select {
		padding-bottom: 180rpx;
		padding-top: 184rpx;

		.order-wrapper {
			width: 710rpx;
			height: 90rpx;
			background: linear-gradient(270deg, #8C9EBD 0%, #666F96 100%);
			border-radius: 10rpx;
			margin: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 26rpx 0 47rpx;

			.order-tip {
				height: 42rpx;
				font-size: 30rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 42rpx;
			}
		}

		.car-type {
			margin: 20rpx;
			height: 300rpx;
			background: $uni-bg-color;
			border-radius: 12rpx;
			padding: 30rpx;

			.car-wrapper {
				margin-top: 20rpx;

				.car-item {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					height: 180rpx;
					width: 206rpx;
					background: #F7F7F7;
					border-radius: 12rpx;
					margin-right: 16rpx;

					&>image {
						width: 90rpx;
						height: 90rpx;
					}
				}

				.car-item:last-of-type {
					margin-right: 0;
				}

				.selector {
					// background-color: $my-theme-color;
					// color: $uni-bg-color;
					background: #E4EFFF;
					color: #0066E9;
				}
			}
		}

		.card-type {
			margin: 20rpx;
			// height: 300rpx;
			background: $uni-bg-color;
			border-radius: 12rpx;
			padding: 30rpx;

			.card-warpper {
				margin-top: 20rpx;

				.card-item {
					margin-bottom: 20rpx;
					display: flex;
					align-items: center;
					height: 128rpx;
					background: #F8F8F8;
					border-radius: 12rpx;
					padding: 25rpx 33rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;

					&>image {
						margin-right: 28rpx;
						width: 56rpx;
						height: 56rpx;
					}

					.card-name {
						font-size: 28rpx;
						line-height: 40rpx;

					}

					.card-desc {
						line-height: 33rpx;
						font-size: 24rpx;
					}

				}

				.card-selector {
					background: #E4EFFF;
					border-radius: 12rpx;
					border: 2rpx solid #009ff6;
					// border-image: linear-gradient(180deg, rgba(0, 159, 246, 1), rgba(0, 102, 233, 1)) 2 2;
					// clip-path: inset(0 round 12rpx);

					.card-type,
					.card-desc {
						// color: $uni-bg-color;
					}
				}

				.ccs-content {
					margin-top: -20rpx;
					font-family: PingFangSC, PingFang SC;
					background: #F6FAFF;
					border-radius: 0rpx 0rpx 15rpx 15rpx;
					border: 1rpx solid #C2DDFF;

					.card-title {
						padding: 30rpx;
						font-weight: 500;
						font-size: 28rpx;
						color: #323435;
						line-height: 40rpx;
					}

					.card-content {
						display: flex;
						align-items: center;
						justify-content: center;

						.img {
							margin-left: 30rpx;
						}

						.card-text {
							font-weight: 400;
							font-size: 24rpx;
							color: #323435;
							line-height: 33rpx;
						}

						.price {
							margin-left: auto;
							height: 81rpx;
							padding: 0 10rpx;
							line-height: 81rpx;
							text-align: center;
							background: #FF9D09;
							border-radius: 100rpx 0rpx 0rpx 100rpx;
							font-weight: 500;
							font-size: 28rpx;
							color: #FFFFFF;
						}
					}

					.card-tips {
						margin: 30rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #888888;
						line-height: 33rpx;
						// text-align: center;
					}
				}

				.selector {
					// background-color: $my-theme-color;
					// color: $uni-bg-color;
					background: #E4EFFF;
					color: #0066E9;
				}
			}
		}

		.package {
			margin: 20rpx;
			background: $uni-bg-color;
			border-radius: 12rpx;
			padding: 30rpx;


			.package-wrapper {
				margin-top: 35rpx;

				.price-package {
					display: flex;
					justify-content: space-between;

					.price-desc {
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #333333;
						line-height: 28rpx;
					}

					.price {
						font-size: 36rpx;
						font-family: PingFangSC-Semibold, PingFang SC;
						font-weight: 600;
						color: #F65B5B;
						line-height: 18rpx;
					}
				}

				.tips {
					margin-top: 37rpx;
					background: #F8F8F8;
					border-radius: 12rpx;
					padding: 25rpx;


					.tips-title {
						position: relative;
						margin-bottom: 20rpx;

						&:before {
							content: ' ';
							position: absolute;
							left: -20rpx;
							top: 4rpx;
							width: 7rpx;
							height: 30rpx;
							background-color: #333333;
						}
					}

					.tips-item {
						font-size: 24rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #888888;
						line-height: 40rpx;
					}
				}
			}
		}

		/deep/.neil-modal__container {
			width: 80%;
			height: 80%;

			.neil-modal__content {
				height: 100%;
			}
		}

		.content-wrapper {
			height: 100%;

			.title {
				height: 116rpx;
				font-size: 36rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #323435;
				line-height: 116rpx;
				text-align: center;
				border-bottom: 1rpx solid #E9E9E9;
			}

			.desc-wrapper {
				height: calc(100% - 312rpx);
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				padding: 45rpx 45rpx 0 45rpx;

				.rich-text {
					text-align: left;
				}

				.desc {
					margin-bottom: 15rpx;
				}
			}

			.check-wrapper {
				display: flex;
				align-items: center;
				padding: 40rpx 0 40rpx 40rpx;

				.check-text {
					margin-left: 10rpx;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #888888;
					line-height: 33rpx;
				}
			}

			.btn-wrapper {
				display: flex;
				// height: 88rpx;

				.cancel-btn {
					flex: 1;
					height: 88rpx;
					line-height: 88rpx;
					background: #FFFFFF;
					border-top: 1rpx solid #E7E7E7;
				}

				.confirm-btn {
					flex: 1;
					height: 88rpx;
					line-height: 88rpx;
					background: #0066E9;
					color: #FFFFFF;
					border-bottom: 1rpx solid #0066E9;
				}
			}
		}
	}

	.tip-text {
		text-align: right;
		font-size: 22rpx;
		margin-top: 10rpx;
		color: #777777;
	}
</style>