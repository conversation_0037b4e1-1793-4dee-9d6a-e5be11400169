<template>
	<!-- 使用uni-popup后需要加入page-meta组件防止页面滚动穿透 -->
	<page-meta :page-style="'overflow:'+(modal.show?'hidden':'visible')"></page-meta>
	<view class="personal">
		<handle-step :current="2" />
		<view class="ocr-wrapper">
			<tTitle title="请拍照上传车主证件" setPadding="30" v-if="owner == 1">
				<template>
					<view class="single-wrapper">
						<view class="single" @click="preview">
							授权书填写样例
						</view>
						<view class="single" @click="downLoad">
							下载模板
						</view>
					</view>
				</template>
			</tTitle>
			<ocrOwnerUpload ref="ocrOwnerUpload" key="ocrOwnerUpload" uploadType="normal" v-if="owner == 1"
				:title="'请拍照上传车主证件'" :imgOwnerList="imgOwnerList" @ocr-change="ocrOwnerChange" @delImg="delOwnerImg">
			</ocrOwnerUpload>
			<tTitle title="请拍照上传单位证件" setPadding="30" v-if="owner == 2">
				<template>
					<view class="single-wrapper">
						<view class="single" @click="preview">
							授权书填写样例
						</view>
						<view class="single" @click="downLoad">
							下载模板
						</view>
					</view>
				</template>
			</tTitle>
			<ocrUploadCorp ref="ocrUploadCorp" key="ocrUploadCorp" v-if="owner == 2" :imgCompanyList="imgCompanyList"
				@ocr-change="ocrCompanyChange" @delImg="delCorpImg">
			</ocrUploadCorp>
		</view>
		<view class="weui-form" v-if="owner == 1 || owner == 2">
			<tTitle title="请仔细核对车主信息" setPadding="30"></tTitle>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell" style="padding: 0 30rpx;">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require" v-if="owner == 1">车主姓名</view>
						<view class="weui-label weui-label__require" v-if="owner == 2" style="width: 260rpx;">单位名称
						</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<!-- 						<input @input="changeInput($event,'ownerName')" :value="vehicleInfo.ownerName"
							class="weui-input" placeholder="请输入用户名称" /> -->
						<textarea v-if="owner == 1" disabled :disable-default-padding="true"
							:class="[textClass?'ios-textarea':'textarea']" class="weui-input" placeholder="上传图片识别车主姓名"
							placeholder-class="pla-css" :value="vehicleInfo.ownerName" />

						<textarea v-if="owner == 2" disabled :disable-default-padding="true"
							:class="[textClass?'ios-textarea':'textarea']" class="weui-input" placeholder="上传图片识别单位名称"
							placeholder-class="pla-css" :value="vehicleInfo.ownerName" />

						<!-- 												<input v-if="owner == 1" disabled :value="vehicleInfo.ownerName" class="weui-input"
							placeholder="上传图片识别车主姓名" />
						<input v-if="owner == 2" disabled :value="vehicleInfo.ownerName" class="weui-input"
							placeholder="上传图片识别单位名称" /> -->
					</view>

					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" v-if="owner == 1">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车主证件类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<!-- 						<picker style="width:100%;" @change="bindUserPickerChange" :range="personalType"
							range-key="label">
							<view class="weui-picker-value">{{personalType[user_type_index].label}}</view>
						</picker> -->
						<view class="weui-cell__bd weui-cell__primary">
							<input :value="personalType[user_type_index].label" placeholder-class="pla-css" disabled
								class="weui-input"></input>
						</view>
					</view>
					<!-- 					<view class="weui-cell__ft">
					</view> -->
					<!-- 					<image style="width: 30rpx;height: 28rpx" src="../../static/new-apply/personal/arrow-right.png"
						mode=""></image> -->
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" v-if="owner == 2">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">单位证件类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<picker style="width:100%;" @change="bindCompanyPickerChange" :range="certificatesTypes"
							range-key="label">
							<view v-if="cer_type_index" class="weui-picker-value">
								{{certificatesTypes[cer_type_index].label}}
							</view>
							<view v-else class="weui-picker-value" style="color: #999999;">请选择单位证件类型</view>
						</picker>
					</view>
					<!-- 					<view class="weui-cell__ft">
					</view> -->
					<image style="width: 30rpx;height: 28rpx" src="../../static/new-apply/personal/arrow-right.png"
						mode=""></image>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require" v-if="owner == 1">车主证件号码</view>
						<view class="weui-label weui-label__require" v-if="owner == 2">单位证件号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<!-- 						<input class="weui-input" @input="changeInput($event,'ownerIdNum')"
							:value="vehicleInfo.ownerIdNum" placeholder="请输入证件号码" /> -->
						<input class="weui-input" disabled v-if="owner == 1" placeholder-class="pla-css"
							:value="vehicleInfo.ownerIdNum" placeholder="上传图片识别证件号码" />
						<input class="weui-input" disabled v-if="owner == 2" placeholder-class="pla-css"
							:value="vehicleInfo.ownerIdNum" placeholder="上传图片识别证件号码" />
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
		</view>

		<view class="ocr-wrapper">
			<ocrUpload ref="ocrUpload" :imgList="imgList" :owner="owner"
				:uploadType="vehicleInfo.isContainer == '0'?'normal':''"
				:imgOwnerList="{'ownerPositiveImg':vehicleInfo.ownerPositiveImg,'ownerNegativeImg':vehicleInfo.ownerNegativeImg}"
				@ocr-change="ocrChange" @delImg="delvehicleImg">
			</ocrUpload>
		</view>

		<view class="weui-form">
			<tTitle title="请仔细核对车辆信息" setPadding="30"></tTitle>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车牌颜色</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary">
							<input :value="vehicleColors[vehicleInfo.vehicleColor] +'色'" disabled
								class="weui-input"></input>
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<!-- 可修改车牌号，测试方便使用，记得注释 -->
				<!-- 				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车牌号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary">
							<picker style="width:100%;" @change="PickerProviceChange" :range="provincesPicker"
								range-key="label">
								<view class="weui-picker-value">{{provincesPicker[provice_index].label}}</view>
							</picker>
						</view>

					</view>

					<view class="weui-cell__ft">
					</view>
					<input placeholder="车牌号码" name="b" style="width: 140rpx;text-align: right;" :value='vehicleCode'
						@input="changeInput($event,'vehicleCode')">
					</input>
				</view> -->
				<!-- 不可修改车牌号，正式版本的时候使用。 -->
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车牌号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input disabled :value="vehicleInfo.vehicleCode" class="weui-input"></input>
					</view>
				</view>




				<view class="vux-x-input weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">客货类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary">
							<input :value="vehicleTypeList[vehicleInfo.vehicleType] +'车'" disabled
								class="weui-input"></input>
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<!-- <view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary">
							<input :value="getVehicleClassType(vehicleInfo.vehicleNationalType)" disabled
								class="weui-input"></input>
						</view>
					</view>
				</view> -->
				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车辆使用性质</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary">
							<picker style="width:100%;" @change="bindPickerUseChange" :range="vehicleUserType"
								range-key="label" :value="vehicle_use_index">
								<view class="weui-picker-value">{{vehicleUserType[vehicle_use_index].label}}</view>
							</picker>
						</view>
					</view>
					<!-- 					<view class="weui-cell__ft">
					</view> -->
					<image style="width: 30rpx;height: 28rpx" src="../../static/new-apply/personal/arrow-right.png"
						mode=""></image>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" v-if="vehicleInfo.isqcy == '0'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">是否为集装箱车辆</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary select">
							<view class="select-wrapper" v-if="vehicleInfo.isContainer == '0'">
								<!-- <image class="select-img"
									:src="vehicleInfo.isContainer == '0'? '../../static/new-apply/vehicle/checked.png':'../../static/new-apply/vehicle/check.png'"
									mode="">
								</image> -->
								<text class="select-text">是</text>
							</view>
							<view class="select-wrapper" v-if="vehicleInfo.isContainer == '1'">
								<!-- <image class="select-img"
									:src="vehicleInfo.isContainer == '1'?'../../static/new-apply/vehicle/checked.png':'../../static/new-apply/vehicle/check.png'"
									mode="">
								</image> -->
								<text class="select-text">否</text>
							</view>
						</view>
					</view>
					<!-- 					<view class="weui-cell__ft">
					</view> -->
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require" style="width: 250rpx;">发动机号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input @input="changeInput($event,'vehicleEngine')" placeholder-class="pla-css" disabled
							:value="vehicleInfo.vehicleEngine" class="weui-input" placeholder="上传识别发动机号"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require" style="width: 250rpx;">车辆识别代码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'vehicleDistinguish')" :value="vehicleInfo.vehicleDistinguish"
							:placeholder="'上传识别车辆识别代码'"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>

				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车辆长（mm）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'vehicleLength')" :value="vehicleInfo.vehicleLength"
							placeholder="上传识别车辆长"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车辆宽（mm）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'vehicleWidth')" :value="vehicleInfo.vehicleWidth"
							placeholder="上传识别车辆宽"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车辆高（mm）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'vehicleHeight')" :value="vehicleInfo.vehicleHeight"
							placeholder="上传识别车辆高"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">座位数</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'vehicleSeat')" :value="vehicleInfo.vehicleSeat"
							placeholder="上传识别座位数"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<!-- 总质量需要————判断 -->
				<view class="vux-x-input weui-cell"
					v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.vehicleTon != '0'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">总质量（千克）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'vehicleTon')" :value="vehicleInfo.vehicleTon"
							placeholder="上传识别总质量"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell"
					v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.vehicleTon == '0'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">总质量（千克）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'vehicleTon')" value="--" placeholder="上传识别总质量"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">整备质量（千克）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css"
							@input="changeInput($event,'maintenanceMass')" :value="vehicleInfo.maintenanceMass"
							placeholder="请输入整备质量"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>

				<view class="vux-x-input weui-cell"
					v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.permittedWeight != '0'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">额定载质量（千克）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'permittedWeight')" :value="vehicleInfo.permittedWeight"
							placeholder="上传识别额定载质量"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>

				<view class="vux-x-input weui-cell"
					v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.permittedWeight == '0'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">额定载质量（千克）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'permittedWeight')" value="--" placeholder="上传识别额定载质量"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell"
					v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.permittedTowWeight != '0'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">准牵引总质量（千克）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'permittedTowWeight')" :value="vehicleInfo.permittedTowWeight"
							placeholder="上传识别准牵引总质量"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell"
					v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.permittedTowWeight == '0'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">准牵引总质量（千克）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" disabled
							@input="changeInput($event,'permittedTowWeight')" value="--"
							placeholder="上传识别准牵引总质量"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车轴数</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" type="number"
							@input="changeInput($event,'vehicleAxles')" :value="vehicleInfo.vehicleAxles"
							placeholder="请输入车轴数"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell" v-if="vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车轮数</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css" type="number"
							@input="changeInput($event,'vehicleWheels')" :value="vehicleInfo.vehicleWheels"
							placeholder="请输入车轮数"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<!-- <view class="vux-x-input weui-cell" v-if="vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3'">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">轴距（毫米）</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" placeholder-class="pla-css"
							@input="changeInput($event,'vehicleWheelbases')" :value="vehicleInfo.vehicleWheelbases"
							placeholder="请输入轴距"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view> -->
			</view>
			<view class="tips">
				<text class="tips-text">
					请仔细核对证件信息，如信息有误，请重新拍照上传
				</text>
			</view>
		</view>
		<tButton :buttonList="buttonList" @toNext="toNext" @toLast="toLast"></tButton>
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:showConfirm="modal.showConfirm">
			<view class="content-wrapper">
				<view class="title">请仔细核对车辆信息</view>
				<scroll-view class="scroll-modal" scroll-y="true">
					<view class="weui-cells">
						<view class="vux-x-input weui-cell ">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车牌颜色</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleColors[vehicleInfo.vehicleColor] +'色'}}</view>
							</view>
						</view>

						<view class="vux-x-input weui-cell ">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车牌号码</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleCode}}</view>
							</view>
						</view>


						<view class="vux-x-input weui-cell ">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">客货类型</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleTypeList[vehicleInfo.vehicleType] +'车'}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell weui-cell_picker">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车型</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-cell__bd weui-cell__primary">
									<view class="weui-picker-value">
										{{getVehicleClassType(vehicleInfo.vehicleNationalType)}}
									</view>
								</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell weui-cell_picker">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车辆使用性质</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-cell__bd weui-cell__primary">
									<view class="weui-picker-value">{{vehicleUserType[vehicle_use_index].label}}</view>
								</view>
							</view>
						</view>

						<view class="vux-x-input weui-cell">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require" style="width: 250rpx;">发动机号</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleEngine}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require" style="width: 250rpx;">车辆识别代码</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleDistinguish}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车辆长（mm）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleLength}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车辆宽（mm）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleWidth}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车辆高（mm）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleHeight}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">座位数</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleSeat}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell"
							v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.vehicleTon !='0'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">总质量（千克）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleTon}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell"
							v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.vehicleTon =='0'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">总质量（千克）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">--</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell"
							v-if="vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">整备质量（千克）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.maintenanceMass}}</view>
							</view>
						</view>

						<view class="vux-x-input weui-cell"
							v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.permittedWeight != '0'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">额定载质量（千克）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.permittedWeight}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell"
							v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.permittedWeight != '0'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">额定载质量（千克）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">--</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell"
							v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.permittedTowWeight != '0'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">准牵引总质量（千克）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.permittedTowWeight}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell"
							v-if="(vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3') && vehicleInfo.permittedTowWeight == '0'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">准牵引总质量（千克）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">--</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell"
							v-if="vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车轴数</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleAxles}}</view>
							</view>
						</view>
						<view class="vux-x-input weui-cell"
							v-if="vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">车轮数</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleWheels}}</view>
							</view>
						</view>
						<!-- 	<view class="vux-x-input weui-cell"
							v-if="vehicleInfo.vehicleType=='1' || vehicleInfo.vehicleType=='3'">
							<view class="weui-cell__hd">
								<view class="weui-label weui-label__require">轴距（毫米）</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-input">{{vehicleInfo.vehicleWheelbases}}</view>
							</view>
						</view> -->
					</view>
				</scroll-view>
				<!-- 				<view class="check-wrapper" @click="check">
					<image v-if="!isChecked" style="width: 28rpx;height: 28rpx;"
						src="../../static/new-apply/vehicle/check.png" mode="">
					</image>
					<image v-if="isChecked" style="width: 28rpx;height: 28rpx;"
						src="../../static/new-apply/vehicle/checked.png" mode="">
					</image>
					<text class="check-text">我已阅读，确认无问题</text>
				</view> -->
				<view class="btn-wrapper">
					<view class="cancel-btn" @click="cancel">
						返回修改
					</view>
					<view class="confirm-btn" @click="carConfirm">
						{{draftResult.productType == '5'?'确认并签署协议':'确认并开通免密支付'}}
					</view>
				</view>
			</view>
		</neil-modal>
		<!-- 		<neil-modal @confirm="carConfirm" confirmText="下单并签署协议" :show="showCarConfirm" @cancel="showCarConfirm = false"
			cancelColor="#ffffff">
			<tTitle title="请仔细核对车辆信息" setPadding="30"></tTitle>
			
		</neil-modal> -->
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import ocrUpload from '@/pagesA/components/ocr-upload/uploadCar.vue'
	import ocrUploadCorp from '@/pagesA/components/ocr-upload/uploadCorp.vue'
	import ocrOwnerUpload from '@/pagesA/components/ocr-upload/uploadPersonal.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import neilModal from "@/components/neil-modal/neil-modal.vue"
	import {
		downLoadFile,
		previewFile
	} from '@/pagesA/common/method.js'
	import {
		personalType,
		newCertificatesTypes,
		vehicleClassType,
		newVehicleUserType,
	} from "@/common/systemConstant.js";
	import {
		checkIdCard,
		checkPhone,
		checkPosttal
	} from "@/common/util.js";
	import {
		getProvince,
		provincesOptions,
		vehicleColors,
		vehicleType,
		getVehicleClassType
	} from "@/common/const/optionData.js";
	import {
		getOpenid,
		getStore,
		getLoginUserInfo
	} from '@/common/storageUtil.js';
	export default {
		components: {
			handleStep,
			ocrUpload,
			ocrUploadCorp,
			ocrOwnerUpload,
			tLoading,
			tTitle,
			tButton,
			neilModal
		},
		data() {
			return {
				provincesPicker: provincesOptions,
				personalType,
				certificatesTypes: newCertificatesTypes,
				vehicleColors,
				vehicleTypeList: vehicleType,
				// showCarConfirm: false,
				examine: false,
				isEdit: false,
				textClass: false,
				buttonList: [
					// 	{
					// 	title: '上一步',
					// 	handle: 'toLast'
					// }, 
					{
						title: '下一步',
						handle: 'toNext'
					}
				],
				isLoading: false,
				vehicleInfo: {
					positiveVehicleImgUrlCode: '', //行驶证正页Code
					negativeVehicleImgUrlCode: '', //行驶证副页Code
					ownerPositiveImgUrlCode: '', //车主证件正面Code
					ownerNegativeImgUrlCode: '', //车主证件反面Code
					vehicleImgUrlCode: '', //行驶证车辆照片Code
					authLetterImgUrlCode: '', //车辆授权书Code
					transportIdImgUrlCode: '', //车辆道路运输证Code
					// customer_id: '',
					positiveVehicleImgUrl: '', //行驶证正页
					negativeVehicleImgUrl: '', //行驶证副页
					ownerPositiveImgUrl: '', //车主证件正面
					ownerNegativeImgUrl: '', //车主证件反面
					positiveVehicleImg: '', //行驶证md5
					negativeVehicleImg: '', //行驶证md5
					ownerPositiveImg: '', //车主证件正面md5
					ownerNegativeImg: '', //车主证件反面md5
					vehicleImgUrl: '', //行驶证车辆照片
					vehicleImg: '', //行驶证车辆照片
					authLetterImgUrl: '', //车辆授权书
					authLetterImg: '',
					transportIdImgUrl: '', //车辆道路运输证
					transportIdImg: '',
					//	车牌颜色（0蓝 1黄 2黑 3白 4渐变绿 5黄绿双拼 6蓝白渐变）
					vehicleColor: '0',
					vehicleCode: '', //车牌号
					vehicleModel: '', //车型
					vehicleEngine: '', //发动机号
					vehicleNationalType: '', //车型
					vehicleCarType: '',
					//	车型（车型 2客车,1货车）
					vehicleType: '',
					//	车辆用户类型(0 普通车 1集卡车 2卧铺车 8军车(交通战备车) 9警车 15紧急车 16特殊公务车)，集卡车只有车型选货车的时候显示
					vehicleUserType: '0',
					//	座位数
					vehicleSeat: '',
					//	吨数(核定载质量)(默认置空 让操作员填写)
					permittedWeight: '',
					//VIN码
					vehicleDistinguish: '',
					//牵引总质量
					permittedTowWeight: '',
					//整备质量(默认置空 让操作员填写)
					maintenanceMass: '',
					//车辆总质量 (默认置空 让操作员填写)
					vehicleTon: '',
					//长
					vehicleLength: '',
					//	宽
					vehicleWidth: '',
					//	高
					vehicleHeight: '',
					//	车轮数
					vehicleWheels: '',
					//	车轴数
					vehicleAxles: '',
					//	轴距默认0
					vehicleWheelbases: '0',
					// 发证日期
					issueDate: '',
					//车主姓名
					ownerName: '',
					//车主证件类型
					ownerIdType: '',
					//车主证件号码
					ownerIdNum: '',
					// nodeStep: '4',
					isContainer: '1', //集装箱0是，1不是
					isqcy: '1', //集装箱0是，1不是
				},
				ocrData: {
					ocrOwnerName: '', //OCR车主，公司姓名	
					ocrOwnerIdNum: '', //OCR车主，公司姓名
				},
				user_type_index: 0,
				cer_type_index: null, //单位默认营业执照
				vehicleClassType,
				vehicle_class_index: null,
				vehicleType_index: 1,
				vehicleUserType: newVehicleUserType,
				vehicle_use_index: 0,
				carType: "1",
				provice_index: 0,
				proviceLabel: "桂",
				vehicleCode: '',
				plate: '', //回显车牌号
				leftImg: false,
				rightImg: false,
				nextFlag: false,
				uploadSuccess: false,
				// notShow: false,
				imgList: {
					positiveVehicleImgUrl: '',
					negativeVehicleImgUrl: '',
					positiveVehicleImg: '',
					negativeVehicleImg: '',
					vehicleImg: '', //行驶证车辆
					vehicleImgUrl: '',
					transportIdImgUrl: '', //车辆道路运输证
					transportIdImg: '',
				},
				imgCompanyList: {
					ownerPositiveImgUrl: '', //单位证件正面
					ownerPositiveImg: '', //单位证件
					authLetterImgUrl: '', //车辆授权书
					authLetterImg: '' //车辆授权书
				},
				imgOwnerList: {
					positiveImageUrl: '', //车主证件正面
					negativeImageUrl: '', //车主证件反面
					positiveImage: '', //车主证件正面md5
					negativeImage: '', //车主证件反面md5
					authLetterImgUrl: '', //车辆授权书
					authLetterImg: '', //车辆授权书
				},
				owner: null, //所属人类型
				userForm: {
					customerName: '', //办理人姓名
					certificatesType: '0', //办理人证件类型
					certificatesCode: '', //办理人证件号码
					linkMobile: '', //办理人手机号码
					positiveImageUrlCode: '', //办理人身份证正面
					negativeImageUrlCode: '', //办理人身份证反面
					positiveImageUrl: '', //办理人身份证正面
					negativeImageUrl: '', //办理人身份证反面
					positiveImage: '', //办理人身份证正面md5
					negativeImage: '', //办理人身份证反面md5
				},
				draftResult: {},
				state: '0', //是否修改签约的信息判断重签状态
				benefitServiceFee: '', //产品金额
				applyId: '',
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					showConfirm: false,
				},
				productType: '', //产品类型
				openId: ''
			};
		},
		onLoad(options) {
			// if (options.uploadSuccess) {
			// 	// this.uploadSuccess = options.uploadSuccess
			// 	this.modal.show = true
			// }
			//获取手机类型
			const phoneType = uni.getSystemInfoSync();
			if (phoneType.platform == 'ios') {
				this.textClass = true
			} else if (phoneType.platform == 'android') {
				this.textClass = false
			}

			this.getDraft()
		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif	
		},
		methods: {
			getVehicleClassType,
			cancel() {
				this.modal.show = false
			},
			//获取openId
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			//方法废弃=====自动校验，不需要选
			// checkContainer(checkFlag) {
			// 	console.log('checkFlag', checkFlag)
			// 	this.vehicleInfo.isContainer = checkFlag
			// 	if (checkFlag == '1') {
			// 		//不是集装箱去掉车辆道路运输证的缓存
			// 		let type = 'transportIdImg'
			// 		this.delvehicleImg(type)
			// 	}
			// },
			//获取产品金额
			getPackagePrice(result) {
				let params = {
					installType: '1', //安装方式; 1：快递邮寄,0: 网点自提
					salesChannels: '0', //销售渠道;0 - C端小程序
					carNo: result.vehicleCode,
					carColor: result.vehicleColor,
					productCode: result.productType,
					isTrunk: result.vehicleType,
					deviceType: result.deviceType //默认基础款
				}
				this.$request.post(this.$interfaces.getPackagePrice, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.benefitServiceFee = res.data.benefitServiceFee

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			setProvincesPicker() {
				for (let i = 0; i < this.provincesPicker.length; i++) {
					if (this.provincesPicker[i].label == this.proviceLabel) {
						this.provice_index = i
					}
				}
			},
			PickerProviceChange(e) {
				this.provice_index = e.detail.value;
				this.proviceLabel = this.provincesPicker[e.detail.value].value || "";
				if (this.vehicleInfo.vehicleCode !== undefined) {
					let prefix = this.vehicleInfo.vehicleCode.substr(0, 1);
					if (getProvince(prefix) === " ") {
						this.vehicleInfo.vehicleCode =
							this.proviceLabel + this.vehicleInfo.vehicleCode;
					} else {
						this.vehicleInfo.vehicleCode =
							this.proviceLabel + this.vehicleInfo.vehicleCode.substr(1);
					}
				}

			},
			bindUserPickerChange(e) {
				this.user_type_index = e.detail.value;
				this.vehicleInfo.ownerIdType =
					this.personalType[e.detail.value].value || "";
			},
			bindCompanyPickerChange(e) {
				this.cer_type_index = e.detail.value;
				this.vehicleInfo.ownerIdType =
					this.certificatesTypes[e.detail.value].value || "";
			},

			// 车型 废弃
			// bindPickerClassChange(e) {
			// 	this.vehicle_class_index = e.detail.value;
			// 	this.vehicleInfo.vehicleNationalType =
			// 		this.vehicleClassType[e.detail.value].value || "";
			// },
			// 车辆使用性质
			bindPickerUseChange(e) {
				console.log('车辆使用性质', e)
				let value = parseInt(e.detail.value)
				if (this.vehicleInfo.isqcy == '0' && value == 0) {
					uni.showModal({
						title: '提示',
						content: '您的车辆为牵引车，请选择其他的车辆使用性质',
						showCancel: false
					});
					return
				}
				this.vehicle_use_index = value;
				this.vehicleInfo.vehicleUserType =
					this.vehicleUserType[value].value || "";
				console.log('车辆使用性质2222', this.vehicleInfo.vehicleUserType)
				if (this.vehicleInfo.isqcy == '0') {
					//OCR是牵引车时，如果选择了集装箱J1\J2，集装箱车辆必选是，不需要传道路运输证
					if (this.vehicleInfo.vehicleUserType == '24' || this.vehicleInfo.vehicleUserType == '28') {
						this.vehicleInfo.isContainer = '0'
					} else {
						//普通牵引车掉车辆道路运输证的缓存
						this.vehicleInfo.isContainer = '1'
						let type = 'transportIdImg'
						this.delvehicleImg(type)
					}
				}
			},
			changeInput(event, data) {
				this.vehicleInfo[data] = event.detail.value;
				if (data === "vehicleCode") {
					this.vehicleCode = event.detail.value.toUpperCase();
					this.vehicleInfo.vehicleCode =
						this.proviceLabel + event.detail.value.toUpperCase();
				}
			},
			modify() {
				this.show = true
				this.isEdit = true
			},

			ocrChange(encryptedData, imgList) {
				// this.notShow = true
				console.log(encryptedData, 'ocr识别');
				let bizContent = encryptedData.encryptedData
				if (!encryptedData) {
					this.$refs.ocrUpload.delImgByRef(encryptedData.side)
					uni.showModal({
						title: "提示",
						content: '无法识别车辆信息，请确认图片是否完整清晰。如果无法识别，请前往网点办理。',
						showCancel: false,
					});
					return
				}
				if (encryptedData.side == 1) {

					if (!bizContent['name'] || !bizContent['vin']) {
						this.$refs.ocrUpload.delImgByRef(encryptedData.side)
						uni.showModal({
							title: "提示",
							content: '无法识别车辆信息，请确认图片是否完整清晰。如果无法识别，请前往网点办理。',
							showCancel: false,
						});
						return
					}

					//新增校验，判断客货类型--专项作业车
					if (bizContent.vehicleType.includes("专项") && this.vehicleInfo.vehicleType != '3') {
						uni.showModal({
							title: "提示",
							content: '您当前所选的客货类型(' + this.vehicleTypeList[this.vehicleInfo.vehicleType] +
								')与行驶证上的车辆类型(' + bizContent.vehicleType + ')不符，请修改',
							confirmText: '确定',
							success: (res) => {
								if (res.confirm) {
									uni.navigateBack({
										delta: 3
									})
								}
							}
						});
						return;
					}
					//新增校验，判断客货类型--专项客
					if ((bizContent.vehicleType.includes("客") || bizContent.vehicleType.includes("轿") || bizContent
							.vehicleType.includes("面包")) && this.vehicleInfo.vehicleType != '2') {
						uni.showModal({
							title: "提示",
							content: '您当前所选的客货类型(' + this.vehicleTypeList[this.vehicleInfo.vehicleType] +
								')与行驶证上的车辆类型(' + bizContent.vehicleType + ')不符，请修改',
							confirmText: '确定',
							success: (res) => {
								if (res.confirm) {
									uni.navigateBack({
										delta: 3
									})
								}
							}
						});
						return;
					}

					//新增校验，判断客货类型--货车--牵引车
					if ((bizContent.vehicleType.includes("货") || bizContent.vehicleType.includes("牵引")) && this
						.vehicleInfo.vehicleType != '1') {
						uni.showModal({
							title: "提示",
							content: '您当前所选的客货类型(' + this.vehicleTypeList[this.vehicleInfo.vehicleType] +
								')与行驶证上的车辆类型(' + bizContent.vehicleType + ')不符，请修改',
							confirmText: '确定',
							success: (res) => {
								if (res.confirm) {
									uni.navigateBack({
										delta: 3
									})
								}
							}
						});
						return;
					}

					if (this.owner == 1) {
						//他人车辆，验证行驶证车主信息
						console.log('this.ocrData.ocrOwnerName', this.ocrData.ocrOwnerName)
						console.log('name', bizContent['name'])
						if (this.ocrData.ocrOwnerName) {
							if (this.ocrData.ocrOwnerName != bizContent['name']) {
								//清除图片
								this.$refs.ocrUpload.delImgByRef(encryptedData.side)
								uni.showModal({
									title: "提示",
									content: '该车辆所属人为【' + bizContent['name'] + '】不是【' + this.ocrData.ocrOwnerName +
										'】的车辆，请确认信息',
									showCancel: false,
									//跳转-1页
								});
								return;
							}
						}

						//未选单位，判断是否公司车辆
						if (bizContent['name'].includes('公司')) {
							uni.showModal({
								title: "提示",
								content: '您当前提交行驶证显示该车为单位车辆，请重新选择车辆归属人。点击确定可跳转至车辆归属人选择页面。',
								confirmText: '重选',
								success: (res) => {
									if (res.confirm) {
										uni.navigateBack({
											delta: 2
										})
									}
								}
							});
							return;
						}

					} else if (this.owner == 2) {
						//单位车辆，验证行驶证车主信息
						if (this.ocrData.ocrOwnerName) {
							if (this.ocrData.ocrOwnerName != bizContent['name']) {
								//清除图片
								this.$refs.ocrUpload.delImgByRef(encryptedData.side)
								uni.showModal({
									title: "提示",
									content: '该车辆所属人为【' + bizContent['name'] + '】不是【' + this.ocrData.ocrOwnerName +
										'】的车辆，请确认信息',
									showCancel: false,
								});
								return;
							}
						}

					} else if (this.owner == 0) {
						//本人车辆，校验车主信息
						if (this.userForm.customerName && bizContent['name']) {
							if (this.userForm.customerName != bizContent['name']) {
								//清除图片
								this.$refs.ocrUpload.delImgByRef(encryptedData.side)
								uni.showModal({
									title: "提示",
									content: '您当前提交的身份证名称【' + this.userForm
										.customerName +
										'】与行驶证车主名称【' + bizContent['name'] +
										'】不一致，请重新提交身份证材料或重选车辆归属人，点击确定返回开户人信息上传页。',
									confirmText: '重选',
									success: (res) => {
										if (res.confirm) {
											uni.navigateBack({
												delta: 1
											})
										}
									}
								});
								return;
							}
							//未选单位，判断是否公司车辆
							if (bizContent['name'].includes('公司')) {
								uni.showModal({
									title: "提示",
									content: '您当前提交行驶证显示该车为单位车辆，请重新选择车辆归属人。点击确定可跳转至车辆归属人选择页面。',
									confirmText: '重选',
									success: (res) => {
										if (res.confirm) {
											uni.navigateBack({
												delta: 2
											})
										}
									}
								});
								return;
							}
						}
					}


					this.ocrData.ocrOwnerName = bizContent['name'] || ''

					//车牌校验
					if (this.plate && this.plate != bizContent['plateNum']) {
						//清除图片
						this.$refs.ocrUpload.delImgByRef(encryptedData.side)
						let msg = '识别的车牌号【' + bizContent['plateNum'] + '】与填写资料所选车牌号【' + this.plate +
							'】不符，请仔细核对或后退重选'

						uni.showModal({
							title: "提示",
							content: msg,
							confirmText: '重选',
							success: (res) => {
								if (res.confirm) {
									uni.navigateBack({
										delta: 2
									})
								}
							}
						});
						return
					}


					this.vehicleInfo.vehicleCode = bizContent['plateNum'] || "";
					if (bizContent['plateNum']) {
						this.proviceLabel = this.vehicleInfo.vehicleCode.substr(0, 1);
						this.setProvincesPicker();
						this.vehicleCode = this.vehicleInfo.vehicleCode.substr(1)
					}
					this.vehicleInfo.vehicleDistinguish = bizContent['vin'] || '';
					this.vehicleInfo.vehicleModel = bizContent['vehicleModel'] || ''
					this.vehicleInfo.vehicleCarType = bizContent['vehicleType'] || ''
					//集装箱车辆判断
					if (bizContent['vehicleType'] && bizContent['vehicleType'].includes('牵引')) {
						this.vehicleInfo.isqcy = '0' //是牵引车		
						this.bindPickerUseChange({
							detail: {
								value: 1
							}
						})
					}

					this.vehicleInfo.vehicleEngine = bizContent['engineNum'] || ''

					if (bizContent['issueDate']) {
						let reg =
							/(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229)/
						// 发行认证日期判断
						if (bizContent['issueDate'] && reg.test(bizContent['issueDate'])) {
							this.vehicleInfo.issueDate = bizContent['issueDate']
						}
						console.log(this.vehicleInfo.issueDate)
					}
				}
				if (encryptedData.side == 2) {
					// this.rightImg = true
					this.vehicleInfo.vehicleLength = bizContent['length'] || ''
					this.vehicleInfo.vehicleWidth = bizContent['width'] || ''
					this.vehicleInfo.vehicleHeight = bizContent['height'] || ''
					this.vehicleInfo.permittedWeight = bizContent['permittedWeight'] || '0'
					this.vehicleInfo.permittedTowWeight = bizContent['permittedTowWeight'] || '0'
					this.vehicleInfo.maintenanceMass = bizContent['maintenanceMass'] || ''
					this.vehicleInfo.vehicleTon = bizContent['totalMass'] || '0'
					this.vehicleInfo.vehicleSeat = bizContent['approvedCount'] ?
						bizContent['approvedCount'] + '' : '';
				}

				if (imgList.side == 1) {
					this.vehicleInfo.positiveVehicleImgUrl = imgList.img_url
					this.vehicleInfo.positiveVehicleImg = imgList.md5Code
					this.vehicleInfo.positiveVehicleImgUrlCode = imgList.code
				}

				if (imgList.side == 2) {
					this.vehicleInfo.negativeVehicleImgUrl = imgList.img_url
					this.vehicleInfo.negativeVehicleImg = imgList.md5Code
					this.vehicleInfo.negativeVehicleImgUrlCode = imgList.code
				}

				if (imgList.side == 20) {
					this.vehicleInfo.transportIdImgUrl = imgList.img_url
					this.vehicleInfo.transportIdImg = imgList.md5Code
					this.vehicleInfo.transportIdImgUrlCode = imgList.code
				}


				if (imgList.side == 6) {
					this.vehicleInfo.vehicleImgUrl = imgList.img_url
					this.vehicleInfo.vehicleImg = imgList.md5Code
					this.vehicleInfo.vehicleImgUrlCode = imgList.code
				}
			},
			ocrOwnerChange(encryptedData, imgList) {
				// this.notShow = true
				console.log(encryptedData, 'ocr识别');
				console.log('imgList====>>>', imgList)
				let bizContent = encryptedData.encryptedData
				if (!encryptedData) {
					this.$refs.ocrOwnerUpload.delImgByRef(encryptedData.side)
					uni.showModal({
						title: "提示",
						content: '无法车主证件信息，请确认图片是否完整清晰。如果无法识别，请前往网点办理。',
						showCancel: false,
					});
					return
				}
				if (encryptedData.side == 1) {
					if (!bizContent['realName']) {
						this.$refs.ocrOwnerUpload.delImgByRef(encryptedData.side)
						uni.showModal({
							title: "提示",
							content: '无法车主证件信息，请确认图片是否完整清晰。如果无法识别，请前往网点办理。',
							showCancel: false,
						});
						return
					}
					// //验证车主信息
					if (this.ocrData.ocrOwnerName) {
						if (this.ocrData.ocrOwnerName != bizContent['realName'] && this.owner == 1) {
							this.$refs.ocrOwnerUpload.delImgByRef(encryptedData.side)
							uni.showModal({
								title: "提示",
								content: '该车辆所属人为【' + this.ocrData.ocrOwnerName + '】' + '，不是【' + bizContent[
										'realName'] +
									'】的车辆，请仔细核对',
								showCancel: false,
							});
							return;
						}
					}

					//校验用
					this.ocrData.ocrOwnerIdNum = bizContent['cardNum'] || ''
					this.ocrData.ocrOwnerName = bizContent['realName'] || ''

					//赋值
					this.vehicleInfo.ownerIdNum = bizContent['cardNum'] || ''
					this.vehicleInfo.ownerName = bizContent['realName'] || ''

				}
				if (imgList.side == 1) {
					this.vehicleInfo.ownerPositiveImgUrl = imgList.img_url
					this.vehicleInfo.ownerPositiveImg = imgList.md5Code
					this.vehicleInfo.ownerPositiveImgUrlCode = imgList.code

				}
				if (imgList.side == 2) {
					this.vehicleInfo.ownerNegativeImgUrl = imgList.img_url
					this.vehicleInfo.ownerNegativeImg = imgList.md5Code
					this.vehicleInfo.ownerNegativeImgUrlCode = imgList.code
				}
				if (imgList.side == 5) {
					this.vehicleInfo.authLetterImgUrl = imgList.img_url
					this.vehicleInfo.authLetterImg = imgList.md5Code
					this.vehicleInfo.authLetterImgUrlCode = imgList.code
				}
			},
			ocrCompanyChange(encryptedData, imgList) {
				// this.notShow = true
				console.log(imgList, 'ocr识别');
				let bizContent = encryptedData.encryptedData
				console.log('bizContent', bizContent)
				if (imgList.photo_code == '2') {
					//单位证件照的逻辑判断
					if (!encryptedData || !bizContent['name'] || !bizContent['id']) {
						this.$refs.ocrUploadCorp.delImgByRef(imgList.photo_code)
						uni.showModal({
							title: "提示",
							content: '无法识别单位营业执照信息，请确认图片是否完整清晰。如果无法识别，请前往网点办理。',
							showCancel: false,
						});
						return
					}
					if (this.ocrData.ocrOwnerName) {
						if (this.ocrData.ocrOwnerName != bizContent['name'] && this.owner == 2) {
							this.$refs.ocrUploadCorp.delImgByRef(imgList.photo_code)
							uni.showModal({
								title: "提示",
								content: '该车辆所属人为【' + this.ocrData.ocrOwnerName + '】' + '，不是【' + bizContent[
										'name'] +
									'】的车辆，请仔细核对',
								showCancel: false,
							});
							return;
						}
					}

					//判断用
					this.ocrData.ocrOwnerIdNum = bizContent['id'] || ''
					this.ocrData.ocrOwnerName = bizContent['name'] || ''

					//赋值
					this.vehicleInfo.ownerIdNum = bizContent['id'] || ''
					this.vehicleInfo.ownerName = bizContent['name'] || ''

					this.vehicleInfo.ownerPositiveImgUrl = imgList.img_url
					this.vehicleInfo.ownerPositiveImg = imgList.md5Code
					this.vehicleInfo.ownerPositiveImgUrlCode = imgList.code
				}

				if (imgList.photo_code == '5') {
					this.vehicleInfo.authLetterImgUrl = imgList.img_url
					this.vehicleInfo.authLetterImg = imgList.md5Code
					this.vehicleInfo.authLetterImgUrlCode = imgList.code
				}
			},
			validate() {
				if (this.vehicleInfo) {
					console.log('owner===>>>', this.owner)
					let msg = "";
					if (!(this.vehicleInfo.ownerPositiveImg && this.vehicleInfo.ownerNegativeImg) && this.owner ==
						1) {
						msg = "请上传车主图片";
					} else if (!this.vehicleInfo.ownerPositiveImg && this.owner == 2) {
						msg = "请上传单位营业执照图片";
					} else if (!this.ocrData.ocrOwnerName && this.owner == 2) {
						msg = "请输入单位名称";
					} else if (!this.vehicleInfo.ownerIdType && this.owner == 2) {
						msg = "请选择单位证件类型";
					} else if (!this.ocrData.ocrOwnerIdNum && this.owner == 2) {
						msg = "请输入单位证件号码";
					} else if (!this.ocrData.ocrOwnerName && this.owner == 1) {
						msg = "请输入车主姓名";
					} else if (!this.ocrData.ocrOwnerIdNum && this.owner == 1) {
						msg = "请输入车主证件号码";
					} else if (!this.vehicleInfo.authLetterImg && (this.owner == 1 || this.owner == 2)) {
						msg = "非本人车辆需要上传车辆授权书图片";
					} else if (!(this.vehicleInfo.positiveVehicleImg && this.vehicleInfo.negativeVehicleImg)) {
						msg = "请上传行驶证图片";
					} else if (!this.vehicleInfo.vehicleImg) {
						msg = "请上传行驶证车辆图片"
					} else if (!this.vehicleInfo.transportIdImg && this.vehicleInfo.isContainer == '0') {
						msg = "集装箱车辆需要上传车辆道路运输证";
					} else if (!this.vehicleInfo.vehicleCode) {
						msg = "请输入车牌号";
					} else if (this.plate && this.plate != this.vehicleInfo.vehicleCode) {
						msg = '当前车牌号与填写资料所选车牌号【' + this.plate +
							'】不符，请仔细核对或后退重选'
					} else if (this.owner == 0 && this.userForm.customerName != this.vehicleInfo.ownerName) {
						msg = '该车辆所属人为【' + this.vehicleInfo.ownerName + '】，不是【' + this.userForm.customerName +
							'】的本人车辆，请仔细核对或后退重选'
					} else if (!this.vehicleInfo.vehicleSeat) {
						msg = "请输入核定人数";
					} else if (!this.vehicleInfo.vehicleCarType) {
						msg = "请输入车型";
					} else if (!this.vehicleInfo.vehicleDistinguish) {
						msg = "请输入识别码";
					} else if (!this.vehicleInfo.vehicleEngine) {
						msg = "请输入发动机号";
					} else if (!this.vehicleInfo.vehicleLength) {
						msg = "请输入车辆长";
					} else if (!this.vehicleInfo.vehicleWidth) {
						msg = "请输入车辆宽";
					} else if (!this.vehicleInfo.vehicleHeight) {
						msg = "请输入车辆高";
					}


					if (msg) {
						uni.showModal({
							title: "提示",
							content: msg,
							showCancel: false,
						});
						return false;
					}
				}
				if (this.vehicleInfo.vehicleType == '1' || this.vehicleInfo.vehicleType == '3') {
					let msg = "";
					if (!this.vehicleInfo.vehicleTon) {
						msg = "请输入总质量";
					} else if (!this.vehicleInfo.maintenanceMass) {
						msg = "请输入整备质量";
					} else if (!this.vehicleInfo.permittedWeight) {
						msg = "请输入额定载质量";
					} else if (!this.vehicleInfo.permittedTowWeight) {
						msg = "请输入牵引总质量";
					}
					if (msg) {
						uni.showModal({
							title: "提示",
							content: msg,
							showCancel: false,
						});
						return false;
					}
				}

				return true;
			},
			checkVehicle(params, callback) {
				this.isLoading = true;
				this.$request
					.post(this.$interfaces.checkVehicle, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							callback(true)
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			getCarClass(params, callback) {
				this.isLoading = true;
				this.$request
					.post(this.$interfaces.getCarClass, {
						data: params
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							callback(res.data)
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			//保存草稿，进行下一步
			toNext() {
				//赋值
				if (this.owner == 0) {
					//本人办理,车主信息赋值
					this.vehicleInfo.ownerName = this.userForm.customerName
					this.vehicleInfo.ownerIdType = this.userForm.certificatesType
					this.vehicleInfo.ownerIdNum = this.userForm.certificatesCode
					this.vehicleInfo.ownerPositiveImgUrlCode = this.userForm.positiveImageUrlCode
					this.vehicleInfo.ownerNegativeImgUrlCode = this.userForm.negativeImageUrlCode
					this.vehicleInfo.ownerPositiveImgUrl = this.userForm.positiveImageUrl
					this.vehicleInfo.ownerNegativeImgUrl = this.userForm.negativeImageUrl
					this.vehicleInfo.ownerPositiveImg = this.userForm.positiveImage
					this.vehicleInfo.ownerNegativeImg = this.userForm.negativeImage

				}
				if (this.owner == 1) {
					//他人车辆,证件默认身份证类型0
					this.vehicleInfo.ownerIdType = '0'
				}

				if (this.owner == 1 || this.owner == 2) {
					this.vehicleInfo.ownerName = this.ocrData.ocrOwnerName
					this.vehicleInfo.ownerIdNum = this.ocrData.ocrOwnerIdNum
				}

				if (this.validate()) {

					if (this.vehicleInfo.vehicleType == '2') {

						this.vehicleInfo.vehicleWheels = "0";
						this.vehicleInfo.vehicleAxles = "0";
						this.vehicleInfo.vehicleWheelbases = "0";

						this.vehicleInfo.maintenanceMass = "0";
						this.vehicleInfo.permittedWeight = "0";
						this.vehicleInfo.permittedTowWeight = "0";
						this.vehicleInfo.vehicleTon = "0";
					}
					let params = JSON.parse(JSON.stringify(this.vehicleInfo))
					if (!params.issueDate) {
						params.issueDate = ''
					}


					let paramsClass = {
						vehicleType: params.vehicleType,
						vehicleSeat: params.vehicleSeat,
						vehicleLength: params.vehicleLength,
						vehicleAxles: params.vehicleAxles,
						vehicleTon: params.vehicleTon
					}

					//车型接口识别
					this.getCarClass(paramsClass, (resData) => {
						if (resData) {
							// params
							console.log('resData', resData)
							params.vehicleNationalType = resData.vehicleNationalType
							this.vehicleInfo.vehicleNationalType = resData.vehicleNationalType

							//获取车型通过后再存草稿
							//车辆校验
							this.checkVehicle(params, (resHandle) => {
								if (resHandle) {
									//通过后存草稿
									this.saveDraft(params)
								}
							})
						}
					})

					// params.transportIdImg = ''
					// params.issueDate = params.issueDate ? params.issueDate + '000000' : ''
				}
			},
			saveDraft(params) {
				this.isLoading = true
				this.$request.post(this.$interfaces.saveDraft, {
					data: {
						...params,
						id: this.applyId
					}
				}).then(res => {
					if (res.code == 200) {
						this.isLoading = false

						//v3废弃
						// uni.redirectTo({
						// 	url: '/pagesA/newBusiness/vehicleFile/vehicleFile'
						// })
						this.modal.show = true

					} else {
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//校验合同签署状态
			getSignStatus(applyId, callback) {
				console.log('签约状态查询==========>>>>', applyId)
				this.isLoading = true;
				let params = {
					otherId: applyId,
					vehicleCode: this.vehicleInfo.vehicleCode,
					vehicleColor: this.vehicleInfo.vehicleColor,
					cardType: this.draftResult.productType, //-1是八桂卡，其他是卡类型
					signVersion: 'V2',
					businessType: '1',
				}
				let data = {
					data: params,
				};

				// if (payMoney) {
				// 	//有历史订单的时候，加上订单金额
				// 	params.saleAmount = payMoney
				// } else {
				// 	//没有历史订单
				// 	params.saleAmount && delete params.saleAmount
				// }

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.getSignStatus, data)
					.then((res) => {
						console.log('res', res)
						this.isLoading = false;
						if (res.code == 200) {
							if (res.data.isNecessary && !res.data.conStatus) {
								//需要签署协议。
								callback(true)
							} else {
								callback(false)
							}
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			createUserSign(applyId) {
				// console.log('签约==========>>>>', applyId)
				// // this.isLoading = true;
				// console.log('签约==========>>>>', this.vehicleColors[this.vehicleInfo.vehicleColor] + '色')
				// let carNoColor = this.vehicleColors[this.vehicleInfo.vehicleColor] + '色'

				let params = {
					source: '2', //2线上
					orderId: applyId,
					// custType: '2',
					customerId: applyId,
					vehicles: {
						vehicleCode: this.vehicleInfo.vehicleCode,
						vehicleColor: this.vehicleInfo.vehicleColor,
					},
					signName: this.userForm.customerName,
					signPhone: this.userForm.linkMobile,
					signIdNo: this.userForm.certificatesCode,
					// redirectUrl: 'https://portal.gxetc.com.cn/agreement?vehicleCode=' + encodeURIComponent(this
					// 	.vehicleInfo
					// 	.vehicleCode) + '&vehicleColor=' + encodeURIComponent(carNoColor),
					productType: this.draftResult.productType,
					businessType: '1', //新办1
					// saleAmount: this.benefitServiceFee,
					isOwner: this.draftResult.owner == 0 ? '1' : '0' //1本人，0代签
				}

				// console.log('prams===========>', params)

				let data = {
					data: params,
				};
				this.isLoading = true
				this.$request
					.post(this.$interfaces.getSignPreview, data)
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('签章返回===>>>', res)
							let signKey = res.data.signKey
							//设置缓存key
							uni.setStorageSync('signKey', signKey)
							let pdfInfo = res.data.data
							let signUrl = 'https://portal.gxetc.com.cn/new-agreement?btnType=C&type=sign&signInfo=' +
								encodeURIComponent(JSON.stringify(
									pdfInfo))

							uni.reLaunch({
								url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
									.stringify(
										signUrl))
							})

						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			createApplySheet(callback) {
				this.isLoading = true;

				this.$request
					.post(this.$interfaces.createApplySheet, {
						data: {
							id: this.$store.state.applyId,
							state: this.state
						}
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							// this.signType = params.conType
							callback(res.data)

						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			//查询次次顺签约
			searchSilkyInfo() {
				let params = {
					// customerId: this.customerInfo.customer_id, //改成申请单id
					hsApplyRecordId: this.$store.state.applyId,
					vehicleCode: this.vehicleInfo.vehicleCode,
					vehicleColor: this.vehicleInfo.vehicleColor
				}
				this.isLoading = true
				return new Promise((resolve, reject) => {
					this.$request
						.post(this.$interfaces.newSearchSilkyInfo, {
							data: params
						})
						.then((res) => {
							console.log(res, '查询次次顺签约信息')
							this.isLoading = false
							if (res.code == 200) {

							} else {
								this.isLoading = false
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
							resolve(res)
						}).catch(err => {
							this.isLoading = false
							reject(err)
						})
				})

			},
			checkUnderSign(callback) {
				this.isLoading = true;
				let params = {
					vehicleCode: this.vehicleInfo.vehicleCode,
					vehicleColor: this.vehicleInfo.vehicleColor,
					hsApplyRecordId: this.$store.state.applyId,
					openId: getOpenid() || this.openId
				}
				let data = {
					data: params,
				};

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.checkUnderSign, data)
					.then((res) => {
						console.log('res', res)
						this.isLoading = false;
						if (res.code == 200) {
							callback(res.data)
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			carConfirm() {
				if (this.productType == '5') {
					//日日通签约
					this.createApplySheet(applyResult => {
						if (applyResult.id && applyResult.recordState == '0') {
							//下单成功，判断是否签约过，签约过直接前往订单页面。
							this.getSignStatus(applyResult.id, resStatus => {
								if (resStatus) {
									//没签约过,去签约
									this.createUserSign(applyResult.id)
								} else {
									//签约过直接去下单
									uni.redirectTo({
										url: '/pagesA/newBusiness/userAgreement/agreementList'
									})
								}
							})
						} else if (applyResult.recordState == '1') {
							this.state = applyResult.recordState
							uni.showModal({
								title: "提示",
								content: '您已变更签约相关信息，需要重新签署协议，是否进行重签协议。',
								confirmText: '重新签署',
								success: (res) => {
									if (res.confirm) {
										//需要重新生成签约订单
										this.carConfirm()
									}
								}

							});
						}
					})
				} else if (this.productType == '10') {
					this.createApplySheet(applyResult => {
						if (applyResult.id && applyResult.recordState == '0') {
							this.checkUnderSign((res) => {
								if (res.checkSilkyFlag == 1) {
									//线下签约判断，已签约，直接跳过签约
									this.getSignStatus(applyResult.id, resStatus => {
										if (resStatus) {
											//没签约过,去签约
											this.createUserSign(applyResult.id)
										} else {
											//签约过直接去下单
											uni.reLaunch({
												url: '/pagesA/newBusiness/userAgreement/agreementList'
											})
										}
									})
								} else {
									//线下签约判断，未签约，调用签约查询接口查询状态
									this.searchSilkyInfo().then(response => {
										console.log('response', response)
										if (response.code == 200) {
											if (response.data.length == 0) {
												//未签约过，先签约银联
												uni.reLaunch({
													url: '/pagesA/newBusiness/signature/ccsSign/index'
												})
											} else {
												//有签约数据需要判断
												if (response.data[0].signStatus == '0') {
													//未签约过，去签约银联
													uni.reLaunch({
														url: '/pagesA/newBusiness/signature/ccsSign/index'
													})

												} else if (response.data[0].signStatus == '2') {
													//已经签署完成，继续签署次次顺协议
													// uni.redirectTo({
													// 	url: '/pagesA/newBusiness/order/order?applyId=' +
													// 		this
													// 		.$store.state.applyId
													// })
													this.getSignStatus(applyResult.id,
														resStatus => {
															if (resStatus) {
																//没签约过,去签约
																this.createUserSign(applyResult
																	.id)
															} else {
																//签约过直接去下单
																uni.reLaunch({
																	url: '/pagesA/newBusiness/userAgreement/agreementList'
																})
															}
														})
												} else if (response.data[0].signStatus == '1') {
													//签署中
													uni.showModal({
														title: "提示",
														content: '订单签约中，请稍后再尝试下一步'
													});
												} else if (response.data[0].signStatus == '4') {
													//签约失败，去签约
													uni.reLaunch({
														url: '/pagesA/newBusiness/signature/ccsSign/index'
													})
												}
											}
										}
									})
								}
							})
						}
					})
				}

			},
			//回显数据
			showData(result) {
				console.log('回填数据====>>>', result)
				this.applyId = result.id
				//产品类型回传
				this.productType = result.productType
				//档案赋值
				this.vehicleInfo.positiveVehicleImgUrl = result.positiveVehicleImgUrl || ''
				this.vehicleInfo.negativeVehicleImgUrl = result.negativeVehicleImgUrl || ''
				this.vehicleInfo.positiveVehicleImgUrlCode = result.positiveVehicleImgUrlCode || ''
				this.vehicleInfo.negativeVehicleImgUrlCode = result.negativeVehicleImgUrlCode || ''
				this.vehicleInfo.positiveVehicleImg = result.positiveVehicleImg || ''
				this.vehicleInfo.negativeVehicleImg = result.negativeVehicleImg || ''
				this.vehicleInfo.vehicleImgUrl = result.vehicleImgUrl || ''
				this.vehicleInfo.vehicleImgUrlCode = result.vehicleImgUrlCode || ''
				this.vehicleInfo.vehicleImg = result.vehicleImg || ''

				//车主信息赋值
				this.vehicleInfo.ownerPositiveImgUrl = result.ownerPositiveImgUrl || '' //车主证件正面
				this.vehicleInfo.ownerNegativeImgUrl = result.ownerNegativeImgUrl || '' //车主证件反面
				this.vehicleInfo.ownerPositiveImgUrlCode = result.ownerPositiveImgUrlCode || '' //车主证件正面
				this.vehicleInfo.ownerNegativeImgUrlCode = result.ownerNegativeImgUrlCode || '' //车主证件反面
				this.vehicleInfo.ownerPositiveImg = result.ownerPositiveImg || ''
				this.vehicleInfo.ownerNegativeImg = result.ownerNegativeImg || ''
				this.vehicleInfo.authLetterImgUrl = result.authLetterImgUrl || ''
				this.vehicleInfo.authLetterImgUrlCode = result.authLetterImgUrlCode || ''
				this.vehicleInfo.authLetterImg = result.authLetterImg || ''
				this.vehicleInfo.transportIdImgUrl = result.transportIdImgUrl || ''
				this.vehicleInfo.transportIdImgUrlCode = result.transportIdImgUrlCode || ''
				this.vehicleInfo.transportIdImg = result.transportIdImg || ''
				this.vehicleInfo.ownerName = result.ownerName || ''
				this.vehicleInfo.ownerIdType = result.ownerIdType || ''
				this.vehicleInfo.ownerIdNum = result.ownerIdNum || ''
				this.vehicleInfo.isContainer = result.isContainer || '1'



				this.ocrData.ocrOwnerName = result.ownerName
				this.ocrData.ocrOwnerIdNum = result.ownerIdNum


				if ((this.vehicleInfo.ownerPositiveImg || this.vehicleInfo.ownerNegativeImg) && result.owner ==
					1) {
					this.imgOwnerList = {
						positiveImageUrl: this.vehicleInfo.ownerPositiveImgUrl,
						negativeImageUrl: this.vehicleInfo.ownerNegativeImgUrl,
						positiveImage: this.vehicleInfo.ownerPositiveImg,
						negativeImage: this.vehicleInfo.ownerNegativeImg,
					}
				}

				if (this.vehicleInfo.authLetterImg) {
					if (result.owner == 1) {
						this.imgOwnerList.authLetterImgUrl = this.vehicleInfo.authLetterImgUrl
						this.imgOwnerList.authLetterImg = this.vehicleInfo.authLetterImg
					}
					if (result.owner == 2) {
						this.imgCompanyList.authLetterImgUrl = this.vehicleInfo.authLetterImgUrl
						this.imgCompanyList.authLetterImg = this.vehicleInfo.authLetterImg
					}
				}

				if (this.vehicleInfo.ownerPositiveImg && result.owner == 2) {
					this.imgCompanyList.ownerPositiveImgUrl = this.vehicleInfo.ownerPositiveImgUrl
					this.imgCompanyList.ownerPositiveImg = this.vehicleInfo.ownerPositiveImg
				}

				if (this.vehicleInfo.positiveVehicleImg || this.vehicleInfo.negativeVehicleImg) {
					this.imgList = {
						positiveVehicleImgUrl: this.vehicleInfo.positiveVehicleImgUrl,
						negativeVehicleImgUrl: this.vehicleInfo.negativeVehicleImgUrl,
						positiveVehicleImg: this.vehicleInfo.positiveVehicleImg,
						negativeVehicleImg: this.vehicleInfo.negativeVehicleImg
					}
				}

				if (this.vehicleInfo.transportIdImg) {
					this.imgList.transportIdImgUrl = this.vehicleInfo.transportIdImgUrl
					this.imgList.transportIdImg = this.vehicleInfo.transportIdImg
				}
				if (this.vehicleInfo.vehicleImg) {
					this.imgList.vehicleImgUrl = this.vehicleInfo.vehicleImgUrl
					this.imgList.vehicleImg = this.vehicleInfo.vehicleImg
				}
				//车牌信息等赋值
				this.vehicleInfo.vehicleColor = result.vehicleColor
				this.vehicleInfo.vehicleCode = result.vehicleCode
				this.vehicleInfo.vehicleType = result.vehicleType
				this.owner = result.owner || null
				this.userForm.customerName = result.customerName
				this.userForm.certificatesType = result.certificatesType
				this.userForm.certificatesCode = result.certificatesCode
				this.userForm.linkMobile = result.linkMobile
				this.userForm.positiveImageUrlCode = result.positiveImageUrlCode
				this.userForm.negativeImageUrlCode = result.negativeImageUrlCode
				this.userForm.positiveImageUrl = result.positiveImageUrl
				this.userForm.negativeImageUrl = result.negativeImageUrl
				this.userForm.positiveImage = result.positiveImage
				this.userForm.negativeImage = result.negativeImage
				//车牌组件赋值
				this.plate = result.vehicleCode
				//省份组件赋值
				this.proviceLabel = this.vehicleInfo.vehicleCode.substr(0, 1)
				//picker回填
				this.provice_index = this.provincesPicker.map(item => item.value).indexOf(this.proviceLabel)
				//车牌号组件赋值
				this.vehicleCode = this.vehicleInfo.vehicleCode.substr(1, this.vehicleInfo.vehicleCode
					.length - 1)

				//其他参数赋值
				//车型
				this.vehicleInfo.vehicleModel = result.vehicleModel || ''
				//发动机号
				this.vehicleInfo.vehicleEngine = result.vehicleEngine || ''
				//车型
				this.vehicleInfo.vehicleNationalType = result.vehicleNationalType || ''
				this.vehicleInfo.vehicleCarType = result.vehicleCarType || '2'
				//VIN码
				this.vehicleInfo.vehicleDistinguish = result.vehicleDistinguish || ''
				//	车辆用户类型(0 普通车 1集卡车 2卧铺车 8军车(交通战备车) 9警车 15紧急车 16特殊公务车)，集卡车只有车型选货车的时候显示
				this.vehicleInfo.vehicleUserType = result.vehicleUserType || '0'
				//	座位数
				this.vehicleInfo.vehicleSeat = result.vehicleSeat || ''
				//	吨数(核定载质量)(默认置空 让操作员填写)//额定载重量
				this.vehicleInfo.permittedWeight = result.permittedWeight || ''
				//牵引总质量
				this.vehicleInfo.permittedTowWeight = result.permittedTowWeight || ''
				//整备质量(默认置空 让操作员填写)
				this.vehicleInfo.maintenanceMass = result.maintenanceMass || ''
				//车辆总质量 (默认置空 让操作员填写)
				this.vehicleInfo.vehicleTon = result.vehicleTon || ''
				//长
				this.vehicleInfo.vehicleLength = result.vehicleLength || ''
				//	宽
				this.vehicleInfo.vehicleWidth = result.vehicleWidth || ''
				//	高
				this.vehicleInfo.vehicleHeight = result.vehicleHeight || ''
				//	车轮数
				this.vehicleInfo.vehicleWheels = result.vehicleWheels || ''
				//	车轴数
				this.vehicleInfo.vehicleAxles = result.vehicleAxles || ''
				//	轴距
				// this.vehicleInfo.vehicleWheelbases = result.vehicleWheelbases || ''

				if (this.vehicleInfo.vehicleType != '2') {
					//要求重置为空
					if (this.vehicleInfo.vehicleWheels == '0') {
						this.vehicleInfo.vehicleWheels = ''
					}
					if (this.vehicleInfo.vehicleAxles == '0') {
						this.vehicleInfo.vehicleAxles = ''
					}
					// if (this.vehicleInfo.vehicleWheelbases == '0') {
					// 	this.vehicleInfo.vehicleWheelbases = ''
					// }
				}

				// 发证日期
				this.vehicleInfo.issueDate = result.issueDate || ''

				//回填picker
				//车主证件类型
				if (result.ownerIdType && result.owner == 1) {
					this.user_type_index = this.personalType.map(item => item.value).indexOf(result.ownerIdType)
				}

				//公司证件类型
				console.log('result.ownerIdType', result.ownerIdType)
				if (result.ownerIdType && result.owner == 2) {
					this.cer_type_index = this.certificatesTypes.map(item => item.value).indexOf(ownerIdType)
				}

				if (result.vehicleNationalType) {
					this.vehicle_class_index = this.vehicleClassType.map(item => item.value).indexOf(result
						.vehicleNationalType)
				}

				if (result.vehicleUserType) {
					this.vehicle_use_index = this.vehicleUserType.map(item => item.value).indexOf(result
						.vehicleUserType)
				}

			},
			//获取草稿
			getDraft() {
				this.isLoading = true
				// let params = this.formData
				// console.log(params, '入参');
				let params = {
					id: this.$store.state.applyId
				}
				this.$request.post(this.$interfaces.getDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						this.draftResult = result
						this.getPackagePrice(result)
						// //获取所有草稿
						this.showData(result)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			delOwnerImg(type) {
				if (type == 'positiveImage') {
					this.vehicleInfo['ownerPositiveImgUrl'] = '' //删除照片
					this.vehicleInfo['ownerPositiveImg'] = '' //删除照片md5
					this.ocrData.ocrOwnerIdNum = ''
					this.ocrData.ocrOwnerName = ''
					this.vehicleInfo.ownerIdNum = ''
					this.vehicleInfo.ownerName = ''
				}

				if (type == 'negativeImage') {
					this.vehicleInfo['ownerNegativeImgUrl'] = '' //删除照片
					this.vehicleInfo['ownerNegativeImg'] = '' //删除照片md5
				}

				if (type == 'authLetterImg') {
					this.vehicleInfo['authLetterImgUrl'] = '' //删除照片
					this.vehicleInfo['authLetterImg'] = '' //删除照片md5
				}

			},
			delCorpImg(type) {
				this.vehicleInfo[type + 'Url'] = '' //删除照片
				this.vehicleInfo[type] = '' //删除照片md5
				this.ocrData.ocrOwnerIdNum = ''
				this.ocrData.ocrOwnerName = ''
				this.vehicleInfo.ownerIdNum = ''
				this.vehicleInfo.ownerName = ''
			},
			delvehicleImg(type) {
				console.log('type======>>>>>', type)
				this.vehicleInfo[type + 'Url'] = '' //删除照片
				this.vehicleInfo[type] = '' //删除照片md5

				if (type == 'positiveVehicleImg') {
					this.vehicle_use_index = 0
					this.vehicleInfo.isqcy = '1'
					this.vehicleInfo.isContainer = '1'
				}
				// if (type == 'positiveVehicleImg') {
				// 	this.ocrData.ocrOwnerIdNum = ''
				// 	this.ocrData.ocrOwnerName = ''
				// }
			},
			// webview跳转
			preview() {
				let url = 'https://portal.gxetc.com.cn/public-static/file/车辆授权书样例.png';
				previewFile(url)
			},
			downLoad() {
				let url = 'https://portal.gxetc.com.cn/public-static/file/车辆授权书模板.docx'
				downLoadFile(url)
			},
			toLast() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	};
</script>

<style>
	.pla-css {
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}
</style>

<style scoped lang="scss">
	// .tips {
	// 	margin-bottom: 170rpx;
	// }
	.ios-textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-top: 45rpx;
	}

	.textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-bottom: 38rpx;
	}

	.textarea-class {
		flex: 1;
		width: calc(100% - 170rpx);
	}

	.personal {
		padding-bottom: 180rpx;
		padding-top: 184rpx;

		.weui-form {
			margin: 20rpx 20rpx 0 20rpx;
			border-radius: 12rpx;
		}

		.ocr-wrapper {
			background-color: #ffffff;
			margin: 20rpx;
			border-radius: 12rpx;
		}

		.weui-form {
			margin: 20rpx 20rpx 0 20rpx;
			border-radius: 12rpx;

			.weui-cell__bd.select {
				display: flex;
				justify-content: flex-end;

				.select-wrapper {
					display: flex;
					align-items: center;

					&:first-child {
						// margin-right: 50rpx;
					}

					.select-img {
						width: 36rpx;
						height: 36rpx;
					}

					.select-text {
						margin-left: 10rpx;
					}
				}
			}

			.tips {
				background: #F8F8F8;
				border-radius: 8rpx;
				font-weight: 400;
				font-size: 28rpx;
				text-align: center;
				margin: 30rpx 30rpx 60rpx 30rpx;

				.tips-text {
					height: 45rpx;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FF9038;
					line-height: 45rpx;
				}
			}
		}

		.weui-cells {
			padding-top: 0;
			border-bottom-left-radius: 12rpx;
			border-bottom-right-radius: 12rpx;

			&::before {
				border: 0;
			}
		}

	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 304rpx;
	}

	.plate_color {
		position: relative;
		display: flex;
		flex-wrap: wrap;

		.plateColor {
			width: 200rpx;
			height: 80rpx;
		}

		.plateColorList {
			position: relative;
			margin-right: 30rpx;
			margin-bottom: 20rpx;

			.checkplateColor {
				position: absolute;
				bottom: -8rpx;
				right: -8rpx;
				width: 40rpx;
				height: 40rpx;
			}
		}

		.otherColor {
			width: 200rpx;
			height: 80rpx;
			color: #666666;
			background: #ffffff;
			border: 1px solid #666666;
			border-radius: 4px;
			line-height: 80rpx;
			text-align: center;
			font-size: 26rpx;
		}
	}

	.idCard {
		background-color: #f6f6f6;

		.examine {
			position: absolute;
			height: calc(100% - 436rpx);
			background-color: #FFFFFF;
			top: 400rpx;
			width: 100%;
			border-radius: 16rpx 16rpx 0px 0px;

			.examine-content {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-top: 150rpx;

				.text {
					font-size: 32rpx;
					font-weight: 400;
					color: #333333;
					width: 464rpx;
					text-align: center;
					margin-top: 40rpx;
				}
			}
		}

		.idCard-top {
			position: relative;
			z-index: 9;
			padding: 48rpx 30rpx;
			background-color: #fff;
			border-radius: 16rpx 16rpx 0px 0px;

			.title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
			}

			.content {
				display: flex;

				.left,
				.right {
					position: relative;
					margin-top: 40rpx;

					.photo_icon {
						position: absolute;
						top: 55rpx;
						left: 125rpx;
					}

					.text {
						text-align: center;
						font-size: 28rpx;
						color: #333;
						font-weight: 400;
						margin-top: 20rpx;
					}

					.delIcon {
						position: absolute;
						right: 0;
						top: 0;
					}
				}

				.right {
					margin-left: 10rpx;
				}
			}

			.center {
				justify-content: center;
			}
		}

		.idCard-bottom {
			padding: 30rpx 0;
			background-color: #fff;
			margin-top: 20rpx;

			.title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
				margin-bottom: 30rpx;
				margin-left: 30rpx;
			}

			.text_line {
				padding: 0 30rpx;
				height: 100rpx;
				width: 100%;
				font-size: 30rpx;
				color: #999999;
				font-weight: 400;
				line-height: 100rpx;
				border-top: 1px solid #e9e9e9;
				display: flex;
				justify-content: space-between;

				.text {
					display: inline-block;
					width: 250rpx;
					color: #999999;
					font-size: 30rpx;
					font-weight: 400;
				}
			}

			.bottom {
				border-bottom: 1px solid #e9e9e9;
			}
		}

		.tips {
			color: #ff9000;
			font-weight: 400;
			font-size: 22rpx;
			text-align: center;
			margin: 10rpx 0;
		}

		.btn {
			display: flex;
			justify-content: space-around;
			background-color: #FFFFFF;
			padding: 20rpx;
		}
	}

	.slot-content {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
		flex-direction: column;
		align-items: center;

		.text {
			font-size: 26rpx;
			font-weight: 400;
			color: #666666;
			width: 500rpx;
			text-align: center;
			margin-bottom: 10rpx;
		}

		.phone {
			font-size: 36rpx;
			font-weight: 500;
			color: #333333;
		}
	}

	.tips {
		text-align: center;
		height: 61rpx;
		line-height: 61rpx;
		background: rgba(255, 144, 56, 0.11);

		.tips-text {
			height: 45rpx;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FF9038;
			line-height: 45rpx;
		}
	}


	/deep/.neil-modal__container {
		width: 90%;
	}

	.scroll-modal {
		height: calc(80vh - 280rpx);
		border-bottom: 1rpx solid #ccc;

		.weui-cell__hd {
			text-align: left;
		}
	}

	.content-wrapper {
		.title {
			height: 116rpx;
			font-size: 36rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: 600;
			color: #323435;
			line-height: 116rpx;
			text-align: center;
			border-bottom: 1rpx solid #E9E9E9;
		}

		.desc-wrapper {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			padding: 45rpx;

			.desc {
				margin-bottom: 15rpx;
			}
		}

		.check-wrapper {
			display: flex;
			align-items: center;
			padding: 80rpx 0 40rpx 40rpx;

			.check-text {
				margin-left: 10rpx;
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #888888;
				line-height: 33rpx;
			}
		}

		.btn-wrapper {
			display: flex;
			// height: 88rpx;

			.cancel-btn {
				flex: 1;
				height: 88rpx;
				line-height: 88rpx;
				background: #FFFFFF;
				border-bottom: 1rpx solid #FFFFFF;
			}

			.confirm-btn {
				flex: 1;
				height: 88rpx;
				line-height: 88rpx;
				background: #0066E9;
				color: #FFFFFF;
				border-bottom: 1rpx solid #0066E9;
			}
		}
	}

	.single-wrapper {
		display: flex;
		align-items: center;

		.single {
			margin-left: 30rpx;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #0081FF;
			line-height: 33rpx;
		}
	}
</style>