<template>
	<view class="order-success g-flex g-flex-center g-flex-align-center g-flex-column">
		<!-- <handle-step :current="4" /> -->
		<image class="img" src="../../../static/new-apply/order/pic_success.png" mode=""></image>
		<view class="success-desc">
			{{contentList[type].dyContent}} <text v-if="contentList[type].invoince" style="color: #0066E9;"
				@click="toInvoice">前往开票</text>
		</view>
		<tButton :buttonList="buttonList" @toApplyList="toApplyList" @toApplyDetail="toApplyDetail"
			@toAfterDetail="toAfterDetail" @toHome="toHome" @toActive="toActive" @toNewApply="toNewApply"
			@toCharge="toCharge"></tButton>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	// import handleStep from "@/components/t-handleStep/handleStep.vue"
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue';

	import {
		getOpenid
	} from '@/common/storageUtil.js';
	import {
		successBtnList
	} from '@/pagesA/common/optionsData.js';

	export default {
		components: {
			// handleStep,
			tButton,
			tLoading
		},
		onLoad(options) {
			if (options) {
				if (options.type) {
					this.type = options.type
					this.buttonList = this.contentList[this.type].button
					console.log('this.buttonList', this.buttonList)
				}
				if (options.applyId) {
					this.applyId = options.applyId
				}

				if (options.saleId) {
					this.saleId = options.saleId
				}
			}
		},
		data() {
			return {
				isLoading: false,
				type: '',
				applyId: '',
				saleId: '',
				sourceType: '1',
				buttonList: [],
				contentList: successBtnList
			}
		},
		methods: {
			getDetail() {

			},
			toApplyList() {
				uni.reLaunch({
					url: '/pagesA/serviceOrder/index'
				})
			},
			toApplyDetail() {
				uni.reLaunch({
					url: '/pagesA/newBusiness/douyin/order/orderDetail?applyId=' + this.applyId
				})
			},
			toAfterDetail() {
				uni.reLaunch({
					url: '/pagesA/newBusiness/afterSale/apply-detail?saleId=' + this.saleId
				})
			},
			toHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>/p-home'
				})
			},
			toActive() {
				if (this.applyId) {
					this.getDetail()
				} else {
					uni.reLaunch({
						url: '/pages/home/<USER>/p-home'
					})
				}
			},
			toInvoice() {
				uni.reLaunch({
					url: '/pagesB/invoiceBusiness/home/<USER>'
				})
			},
			toCharge() {
				uni.reLaunch({
					url: '/pagesB/loadBusiness/recharge/recharge'
				})
			},
			getDetail() {
				this.isLoading = true
				let params = {
					id: this.applyId
				}

				this.$request.post(this.$interfaces.dyApplyOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						//缓存激活车辆信息
						this.$store.dispatch('setIssueVehicleInfo', {
							businessSource: 1, //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
							orderId: result.id,
							gxCardType: result.productType,
							customerId: result.context.customerId,
							vehicleCode: result.context.vehicleCode,
							vehicleColor: result.context.vehicleColor,
							benefitServiceFee: '1', //不为0可开票，抖音默认开票
							sourceType: '1' //抖音
						})
						//跳转激活页面
						uni.reLaunch({
							url: '/pagesA/newBusiness/issue/issue-install'
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			toNewApply() {
				uni.reLaunch({
					url: '/pagesA/newBusiness/douyin/entry/entry'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-success {
		padding-top: 60rpx;

		.success-desc {
			padding: 0 50rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;
		}

		.img {
			width: 452rpx;
			height: 437rpx;
		}
	}
</style>