<template>
	<view class="order-info">
		<tTitle title="产品信息" setPadding="30" rightText="查看详情" @click="onclick">
		</tTitle>
		<view class="personal-wrapper">
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					产品类型
				</view>
				<view class="personal-wrapper__value">
					{{configReqVO.deviceType == '1'?"捷通次次顺记账卡(基础款）":'捷通次次顺记账卡(进阶款）'}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					权益费
				</view>
				<view class="personal-wrapper__value">
					￥{{configReqVO.orderAmount}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					代扣签约渠道
				</view>
				<view class="personal-wrapper__value">
					{{result.payOrgStr || '未签约'}}
				</view>
			</view>
			<!-- <view class="personal-bd" v-if="configReqVO.productCode != '10'">
				<view class="personal-wrapper__label">
					激活保证金
				</view>
				<view class="personal-wrapper__value">
					￥{{moneyFilter(configReqVO.activationDeposit)}}
				</view>
			</view> -->
		</view>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
	} from '@/common/method/filter.js';
	export default {
		props: {
			configReqVO: {
				type: Object
			},
			result: {
				type: Object
			}
		},
		components: {
			tLoading,
			tTitle
		},
		data() {
			return {}
		},
		methods: {
			onclick() {
				this.$emit('click')
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-info {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding-bottom: 20rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}

	.status-value.info {
		background: #F6F6F6;
		color: #333333;
	}


	.status-value.success {
		background: rgba(0, 189, 50, 0.1);
		color: #00BD32;
	}

	.status-value.warnning {
		background: rgba(255, 145, 0, 0.1);
		color: #FF9100;
	}

	.status-value.error {
		background: rgba(255, 84, 84, 0.1);
		color: #FF5454;
	}

	.progress-tip {
		padding: 30rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #999999;
	}

	.personal-wrapper {
		margin: 0 30rpx;
		// border-bottom: 1rpx dashed #C3C3C3;
	}

	.personal-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.personal-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.personal-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}

	.car-wrapper {
		margin: 30rpx;
		overflow: hidden;
	}

	.car-bd {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.car-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.car-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}
</style>