<template>
	<view class="order-info__detail">
		<view class="personal-wrapper">
			<tTitle title="开户人资料"></tTitle>
			<view class="personal-bd" style="margin-top: 38rpx;">
				<view class="personal-wrapper__label">
					档案照片
				</view>
				<view class="personal-wrapper__value">
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.positiveImageUrl)">
						<template v-if="applyRecordVO.positiveImageUrl">
							<image class="pic-img" :src="applyRecordVO.positiveImageUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('positiveImageUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.positiveImageUrl">
							<image @tap="ChooseImage('positiveImage')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							身份证人像
						</view>
					</view>
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.negativeImageUrl)">
						<template v-if="applyRecordVO.negativeImageUrl">
							<image class="pic-img" :src="applyRecordVO.negativeImageUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('negativeImageUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.negativeImageUrl">
							<image @tap="ChooseImage('negativeImage')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							身份证国徽
						</view>
					</view>
				</view>
			</view>
			<view class="personal-bd" style="margin-top: 60rpx;">
				<view class="personal-wrapper__label">
					开户人
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.customerName}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					证件类型
				</view>
				<view class="personal-wrapper__value">
					{{getLabel(personalType,applyRecordVO.customerType)}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					证件号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.certificatesCode}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					手机号
				</view>
				<input v-if="type == 'update'" type="number" v-model="phone" class="personal-wrapper__value input" />
				<view v-if="type != 'update'" class="personal-wrapper__value">
					{{phone}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					联系地址
				</view>
				<input v-if="type == 'update'" type="text" v-model="linkAddress"
					class="personal-wrapper__value input" />
				<view v-if="type != 'update'" class="personal-wrapper__value">
					{{linkAddress}}
				</view>
			</view>
		</view>
		<view class="personal-wrapper">
			<tTitle title="申办车辆资料"></tTitle>
			<view class="personal-bd" style="margin-top: 38rpx;">
				<view class="personal-wrapper__label">
					档案照片
				</view>
				<view class="personal-wrapper__value">
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.positiveVehicleImgUrl)">
						<template v-if="applyRecordVO.positiveVehicleImgUrl">
							<image class="pic-img" :src="applyRecordVO.positiveVehicleImgUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('positiveVehicleImgUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.positiveVehicleImgUrl">
							<image @tap="ChooseImage('positiveVehicleImg')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							行驶证正页
						</view>
					</view>
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.negativeVehicleImgUrl)">
						<template v-if="applyRecordVO.negativeVehicleImgUrl">
							<image class="pic-img" :src="applyRecordVO.negativeVehicleImgUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('negativeVehicleImgUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.negativeVehicleImgUrl">
							<image @tap="ChooseImage('negativeVehicleImg')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							行驶证副页
						</view>
					</view>
					<view class="pic-wrapper" @tap="ViewImage(applyRecordVO.vehicleImgUrl)">
						<template v-if="applyRecordVO.vehicleImgUrl">
							<image class="pic-img" :src="applyRecordVO.vehicleImgUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('vehicleImgUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.vehicleImgUrl">
							<image @tap="ChooseImage('vehicleImg')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							行驶证车辆
						</view>
					</view>

					<view v-if="applyRecordVO.owner == 1" class="pic-wrapper"
						@tap="ViewImage(applyRecordVO.ownerPositiveImgUrl)">
						<template v-if="applyRecordVO.ownerPositiveImgUrl">
							<image class="pic-img" :src="applyRecordVO.ownerPositiveImgUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('ownerPositiveImgUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.ownerPositiveImgUrl">
							<image @tap="ChooseImage('ownerPositiveImg')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							车主身份证人像
						</view>
					</view>
					<view v-if="applyRecordVO.owner == 1" class="pic-wrapper"
						@tap="ViewImage(applyRecordVO.ownerNegativeImgUrl)">
						<template v-if="applyRecordVO.ownerNegativeImgUrl">
							<image class="pic-img" :src="applyRecordVO.ownerNegativeImgUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('ownerNegativeImgUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.ownerNegativeImgUrl">
							<image @tap="ChooseImage('ownerNegativeImg')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							车主身份证国徽
						</view>
					</view>

					<view v-if="applyRecordVO.owner == 2" class="pic-wrapper"
						@tap="ViewImage(applyRecordVO.ownerPositiveImgUrl)">
						<template v-if="applyRecordVO.ownerPositiveImgUrl">
							<image class="pic-img" :src="applyRecordVO.ownerPositiveImgUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('ownerPositiveImgUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.ownerPositiveImgUrl">
							<image @tap="ChooseImage('ownerPositiveImg')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/owner_positive_img.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							单位证件照
						</view>
					</view>

					<view v-if="applyRecordVO.owner != 0 && isShowAuthLetterImgUrl" class="pic-wrapper"
						@tap="ViewImage(applyRecordVO.authLetterImgUrl)">
						<template v-if="applyRecordVO.authLetterImgUrl">
							<image class="pic-img" :src="applyRecordVO.authLetterImgUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('authLetterImgUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.authLetterImgUrl">
							<image @tap="ChooseImage('authLetterImg')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unload_bg_img.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							车辆授权书
						</view>
					</view>

					<view v-if="applyRecordVO.isContainer =='0' && isShowTransportIdImgUrl" class="pic-wrapper"
						@tap="ViewImage(applyRecordVO.transportIdImgUrl)">
						<template v-if="applyRecordVO.transportIdImgUrl">
							<image class="pic-img" :src="applyRecordVO.transportIdImgUrl" mode="aspectFill"></image>
							<view v-if="type == 'update'" class="cu-tag bg-brown upload-box__close"
								@tap.stop="DelImg('transportIdImgUrl')">
								<text class='cuIcon-close close'></text>
							</view>
						</template>
						<template v-if="!applyRecordVO.transportIdImgUrl">
							<image @tap="ChooseImage('transportIdImg')" class="pic-img"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unload_bg_img.png"
								mode="aspectFill"></image>
						</template>
						<view class="pic-lable">
							车辆道路运输证
						</view>
					</view>
				</view>
			</view>
			<view class="personal-bd" style="margin-top: 60rpx;">
				<view class="personal-wrapper__label">
					车牌颜色
				</view>
				<view class="personal-wrapper__value">
					{{vehicleColorStr}}色
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车牌号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleCode}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车辆归属人
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.ownerName}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车辆种类
				</view>
				<view class="personal-wrapper__value">
					{{vehicleClassType}}{{vehicleType}}车
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车辆类型
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleCarType}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					发动机号
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleEngine}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					车辆识别代码
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleDistinguish}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					长·宽·高
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleLength}}·{{applyRecordVO.vehicleWidth}}·{{applyRecordVO.vehicleHeight}}
				</view>
			</view>
			<view class="personal-bd">
				<view class="personal-wrapper__label">
					座位数
				</view>
				<view class="personal-wrapper__value">
					{{applyRecordVO.vehicleSeat}}座
				</view>
			</view>
		</view>
		<view class="tips">
			如需修改车牌号/车牌颜色/车辆归属人/开户人姓名及证件号等信息，请取消此订单，重新申请
		</view>
		<tButton v-if="type == 'update'" :buttonList="buttonList" @orderUpdate="orderUpdate"></tButton>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tTitle from '@/pagesA/components/new-title/new-title.vue';
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import {
		personalType,
	} from "@/common/systemConstant.js";
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
	} from '@/common/method/filter.js';
	export default {
		components: {
			tTitle,
			cpimg,
			tButton,
			tLoading
		},
		data() {
			return {
				personalType,
				isLoading: false,
				isUpload: false,
				isShowAuthLetterImgUrl: false,
				isShowTransportIdImgUrl: false,
				type: 'normal',
				applyId: '',
				applyRecordVO: {},
				address: '',
				linkAddress: '', //联系地址
				oriLinkAddress: '', //联系地址
				phone: '',
				oriPhone: '', //手机号码
				buttonList: [{
					title: '确认修改',
					type: 'primary',
					handle: 'orderUpdate'
				}],
				chooseName: '', //当前选择照片名字
				imgList: {},
				uploadArr: [], //上传图片名字数组
			}
		},
		computed: {
			vehicleType() {
				return getVehicleType(this.applyRecordVO.vehicleType);
			},
			vehicleColorStr() {
				return getVehicleColor(this.applyRecordVO.vehicleColor);
			},
			vehicleClassType() {
				return getVehicleClassType(this.applyRecordVO.vehicleNationalType);
			},
		},
		onLoad(options) {
			if (options && options.applyId) {
				this.applyId = options.applyId
			}
			if (options && options.type) {
				this.type = options.type
				this.imgList.id = this.applyId
			}
			this.getDetail()
		},
		methods: {
			orderUpdate() {
				if (!this.validate()) return
				this.isLoading = true
				let params = {
					...this.imgList,
					linkPhone: this.phone,
					linkAddress: this.linkAddress
				};
				// this.isLoading = true;
				this.$request.post(this.$interfaces.dyOrderUpdate, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.showModal({
							title: "提示",
							content: '修改成功',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									let pages = getCurrentPages(); //获取所有页面栈实例列表
									let prevPage = pages[pages.length - 2]; //上一页页面实例
									prevPage.$vm.getDetail() //调用签约方法
									uni.navigateBack({
										delta: 1
									})
								}
							}
						});
						// this.getDetail()
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			validate() {
				if (this.uploadArr.length > 0) {
					uni.showModal({
						title: "提示",
						content: '您还有修改的图片未上传，请先上传后再进行操作',
						showCancel: false,
					});
					return false
				} else {
					if (!this.isUpload && this.phone == this.oriPhone && this.linkAddress == this.oriLinkAddress) {
						uni.showModal({
							title: "提示",
							content: '您还未修改任何信息，请先修改后再进行操作',
							showCancel: false,
						});
						return false
					}
				}
				return true
			},
			DelImg(name) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							if (!this.uploadArr.includes(name)) {
								console.log('添加图片信息')
								this.uploadArr.push(name)
							}
							this.applyRecordVO[name] = ''
							// let current = {}
							// for (let i = 0; i < this.fileList.length; i++) {
							// 	if (this.fileList[i].photo_code == data.photo_code) {
							// 		this.fileList[i].file_url = '';
							// 		this.fileList[i].file_serial = '';
							// 		this.current = this.fileList[i]
							// 	}
							// }
							// this.$emit('on-change', this.current)
						}
					}
				})
			},
			ChooseImage(chooseName) {
				this.chooseName = chooseName;
				this.$refs.cpimg._changImg(0);
			},
			//图片压缩成功
			cpimgOk(file) {
				// if(process.env.NODE_ENV === "development"){
				// 	this.unloadHandle(file);
				// 	return;
				// }
				console.log('chooseName', this.chooseName)
				if (this.chooseName == 'positiveImage' || this.chooseName == 'positiveVehicleImg') {
					this.sendOCR(file);
				} else {
					this.sendUploadFile(file)
				}

			},
			sendOCR(file) {
				let biz_content = {
					ocr_type: this.chooseName == 'positiveImage' ? 2 : 4, //OCR 识别车头照片
					file_name: 'file_name',
				}
				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('OCR识别========>>>>>>>>>>', res)
						console.log('激活车牌号', this.applyRecordVO.vehicleCode)
						// this.setLogger(res.data.vehicleNo)
						let result = res.data
						if (this.chooseName == 'positiveVehicleImg') {
							//行驶证OCR判断
							if (result.encryptedData.plateNum == this.applyRecordVO.vehicleCode) {
								this.sendUploadFile(file);
							} else {
								uni.showModal({
									title: '提示',
									content: '识别车牌号码【' + result.encryptedData.plateNum +
										'】与修改订单车辆车牌号码【' + this.applyRecordVO.vehicleCode +
										'】不一致，请按提示上传清晰的有效图片。',
									showCancel: false
								});
							}
						} else if (this.chooseName == 'positiveImage') {
							//身份证人像OCR
							console.log('人像OCR识别========>>>>>>>>>>', result.encryptedData.cardNum, this
								.applyRecordVO.certificatesCode)
							if (result.encryptedData.cardNum == this.applyRecordVO.certificatesCode) {
								console.log('进来了吗1')
								this.sendUploadFile(file);
							} else {
								uni.showModal({
									title: '提示',
									content: '识别证件号【' + result.encryptedData.cardNum +
										'】与修改订单证件号【' + this.applyRecordVO.certificatesCode +
										'】不一致，请按提示上传清晰的有效图片。',
									showCancel: false
								});
							}
						}

						// this.$emit("ocr-change", res.data);
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			sendUploadFile(fileParams) {
				this.isLoading = true
				// let current = fileParams ? JSON.parse(JSON.stringify(fileParams)) : {}
				// let biz_content = {
				// 	customer_id: this.customer_id,
				// 	photo_code: current.photo_code,
				// 	scene: 9, // C端档案上传
				// };
				// console.log('side==>>>>>>>>', result.side)
				let params = {
					image: fileParams.toString(),
					// method_code: "2", // 档案上传
					// biz_content: JSON.stringify(biz_content),
				};
				// this.isLoading = true;
				this.$request.post(this.$interfaces.archivesUpload, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('图片上传====>>>', res)
						this.isUpload = true
						let result = res.data.data

						this.applyRecordVO[this.chooseName + 'Url'] = result.fileUrl
						this.imgList[this.chooseName] = result.md5Code
						this.imgList[this.chooseName + 'Url'] = result.fileUrl
						this.imgList[this.chooseName + 'UrlCode'] = result.code
						//删除名字数组数据
						let delIndex = this.uploadArr.indexOf(this.chooseName + 'Url')
						if (delIndex > -1) {
							this.uploadArr.splice(delIndex, 1); // 第二个参数为1表示删除一个元素
						}
						// this.$emit('ocr-change', result, imgList)
						console.log('this.imgList====>>>', this.imgList)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e)
			},
			//图片预览
			ViewImage(path) {
				if (!path) return
				let newArr = [];
				newArr.push(path);
				uni.previewImage({
					urls: newArr
				});
			},
			getLabel(list, value) {
				for (let i = 0; i < list.length; i++) {
					if (list[i].value == value) {
						return list[i].label
					}
				}
				return ''
			},
			getDetail() {
				this.isLoading = true
				let params = {
					id: this.applyId
				}

				this.$request.post(this.$interfaces.dyApplyOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('详情信息=========>>>>>', res)
						let result = res.data
						this.applyRecordVO = result.context || {}
						if (this.applyRecordVO.authLetterImgUrl) {
							this.isShowAuthLetterImgUrl = true
						}
						if (this.applyRecordVO.transportIdImgUrl) {
							this.isShowTransportIdImgUrl = true
						}
						this.phone = this.applyRecordVO.linkMobile
						this.oriPhone = this.phone
						this.linkAddress = this.applyRecordVO.linkAddress
						this.oriLinkAddress = this.linkAddress
						// this.address = result.applyRecordVO.address
						// this.getSignStatus(res.data.applyRecordVO, () => {
						// 	this.getAgreementList(res.data.applyRecordVO)
						// })
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-info__detail {
		padding-bottom: 180rpx;
	}

	.input {
		border: 1rpx solid #E8E8E8;
		padding: 0 6rpx;
		border-radius: 10rpx;
	}

	.personal-wrapper {
		background-color: #ffffff;
		margin: 20rpx;
		padding: 35rpx 30rpx 30rpx 30rpx;
		border-radius: 12rpx;
	}

	.personal-bd {
		display: flex;
		margin-bottom: 30rpx;
	}

	.personal-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.personal-wrapper__value {
		display: flex;
		flex-wrap: wrap;
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}

	.pic-wrapper {
		position: relative;
		margin-bottom: 20rpx;

		&:nth-child(2n-1) {
			margin-right: 10rpx;
		}
	}

	.pic-wrapper .upload-box__close {
		position: absolute;
		right: 0;
		top: 0;
		padding: 0 10rpx;
		font-size: 24rpx;
	}

	.pic-wrapper .upload-box__close .close {
		font-size: 24rpx;
	}

	.pic-img {
		width: 215rpx;
		height: 131rpx;
	}

	.pic-lable {
		text-align: center;
	}

	.car-wrapper {
		background-color: #ffffff;
		margin: 30rpx;
	}

	.car-bd {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.car-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.car-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}

	.tips {
		padding: 20rpx 24rpx 50rpx 24rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #888888;
	}
</style>