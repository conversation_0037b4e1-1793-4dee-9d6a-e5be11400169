<template>
	<view class="agreement-list">
		<handle-step type="douyin" :current="3" />
		<tTitle title="协议签署记录"></tTitle>
		<view>
			<!-- <signItem v-for="(item,index) in agreementList" :key='index' :singInfo='item' /> -->
			<signItem v-if="agreementList" :singInfo='agreementList'></signItem>
		</view>
		<tButton :buttonList="buttonList" @toNext="toNext" @toLast="toLast"></tButton>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,
		getLoginUserInfo
	} from "@/common/storageUtil.js";
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import signItem from './item.vue'
	import tTitle from '@/pagesA/components/t-title/t-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue';
	export default {

		name: '',
		components: {
			handleStep,
			signItem,
			tTitle,
			tButton,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				agreementList: {},
				buttonList: [
					// 	{
					// 	title: '上一步',
					// 	handle: 'toLast'
					// }, 
					{
						title: '下一步',
						handle: 'toNext'
					}
				],
				nodeStep: '5',
				timer: null,
				payStatus: '', //支付状态
				applyId: ''
			}
		},
		onLoad() {
			// this.getAgreementList()
			// setTimeout(() => {
			this.getDraft()
			// }, 10 * 1000)
			// this.timer = setTimeout(() => {
			// 	this.getAgreementList()
			// }, 3000)

		},
		methods: {
			getDraft() {
				this.isLoading = true
				// let params = this.formData
				// console.log(params, '入参');
				let params = {
					id: this.$store.state.applyId
				}
				this.$request.post(this.$interfaces.dyGetDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						this.applyId = result.id
						// if (result.payStatus) {
						// 	//判断是否已经支付过、
						// 	this.payStatus = result.payStatus
						// }
						// //获取所有草稿
						// this.getDraft()
						// uni.navigateTo({
						// 	url: '/pagesA/newBusiness/installation/installation'
						// })
						// console.log('草稿===》》》', res.data)
						this.getSignStatus(result, (res) => {
							console.log('res', res)
							if (res) {
								//未签约，等待签约
								uni.showModal({
									title: '提示',
									content: '您的电子协议正在签约中，请稍作等待...',
									showCancel: false,
									success: (res) => {
										if (res.confirm) {
											this.getDraft()
										}
									}
								});
							} else {
								this.getAgreementList(result)
							}

						})

					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			//校验合同签署状态
			getSignStatus(result, callback) {
				// console.log('签约状态查询==========>>>>', applyId)
				this.isLoading = true;
				let params = {
					otherId: result.id,
					vehicleCode: result.vehicleCode,
					vehicleColor: result.vehicleColor,
					cardType: result.productType, //-1是八桂卡，其他是卡类型
					signVersion: 'V2',
					businessType: '1', //线下
					applyType: '1', //抖音传1
				}
				let data = {
					data: params,
				};

				// if (payMoney) {
				// 	//有历史订单的时候，加上订单金额
				// 	params.saleAmount = payMoney
				// } else {
				// 	//没有历史订单
				// 	params.saleAmount && delete params.saleAmount
				// }

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.getSignStatus, data)
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							//更新状态
							if (res.data.isNecessary && !res.data.conStatus) {
								//需要签署协议。
								callback(true)
							} else {
								callback(false)
							}

						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			getAgreementList(result) {
				this.isLoading = true
				// queryLastContracts
				let data = {
					otherId: result.id,
					vehicleCode: result.vehicleCode,
					vehicleColor: result.vehicleColor,
					applyType: '1', //抖音传1
					pageNum: 1,
					pageSize: 1
				}
				this.$request.post(this.$interfaces.queryApplyContracts, {
					data: data
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						console.log(res, '-----------');
						this.agreementList = res.data

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

					console.log(res, '-----------');
					console.log(this.agreementList);
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "错误",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			// getAgreementList(result) {
			// 	this.isLoading = true
			// 	// queryLastContracts
			// 	let data = {
			// 		routePath: this.$interfaces.queryApplyContracts.method,
			// 		bizContent: {
			// 			otherId: result.id,
			// 			vehicleCode: result.vehicleCode,
			// 			vehicleColor: result.vehicleColor,
			// 		}
			// 	}
			// 	this.$request.post(this.$interfaces.issueRoute, {
			// 		data: data
			// 	}).then(res => {
			// 		this.isLoading = false
			// 		if (res.code == 200) {
			// 			console.log(res, '-----------');
			// 			this.agreementList = res.data

			// 		} else {
			// 			uni.showModal({
			// 				title: "提示",
			// 				content: res.msg,
			// 				showCancel: false,
			// 			});
			// 		}

			// 		console.log(res, '-----------');
			// 		console.log(this.agreementList);
			// 	}).catch(err => {
			// 		this.isLoading = false
			// 		uni.showModal({
			// 			title: "错误",
			// 			content: err.msg,
			// 			showCancel: false,
			// 		});
			// 	})
			// },
			toNext() {
				uni.reLaunch({
					url: '/pagesA/newBusiness/douyin/order/orderSuccess?type=1&sourceType=1'
				})
				// console.log('进来了吗')
				// this.isLoading = true
				// let params = {
				// 	nodeStep: this.nodeStep,
				// 	id: this.applyId
				// }
				// console.log(params, '入参');
				// this.$request.post(this.$interfaces.saveDraft, {
				// 	data: params
				// }).then(res => {
				// 	this.isLoading = false;
				// 	if (res.code == 200) {
				// 		// //获取所有草稿
				// 		// this.getDraft()
				// 		this.nextFlag = true
				// 		if (this.payStatus == 1 || this.payStatus == 2) {
				// 			//已经支付过了，直接去订单列表
				// 			uni.reLaunch({
				// 				url: '/pagesA/serviceOrder/index'
				// 			})
				// 		} else {
				// 			uni.navigateTo({
				// 				url: '/pagesA/newBusiness/order/order?applyId=' + this.applyId +
				// 					'&type=isNext'
				// 			})
				// 		}
				// 	} else {
				// 		uni.showModal({
				// 			title: '提示',
				// 			content: res.msg,
				// 			showCancel: false
				// 		});
				// 	}
				// }).catch(err => {
				// 	this.isLoading = false;
				// 	uni.showModal({
				// 		title: '提示',
				// 		content: err.msg,
				// 		showCancel: false
				// 	});
				// })
			},
			toLast() {
				// let currentRoutes = getCurrentPages();
				// console.log('currentRoutes', currentRoutes)
				// let lastRoutes = currentRoutes[currentRoutes.length - 2].route
				this.nextFlag = true
				// uni.redirectTo({
				// 	url: '/pagesA/newBusiness/vehicle/vehicle'
				// })
				uni.navigateBack()
			}
		},
		// onUnload() {
		// 	if (this.nextFlag) return;
		// 	uni.navigateTo({
		// 		url: '/pagesA/newBusiness/vehicle/vehicle'
		// 	})
		// 	// let currentRoutes = getCurrentPages();
		// 	// console.log('currentRoutes', currentRoutes)
		// 	// let lastRoutes = currentRoutes[currentRoutes.length - 2].route
		// 	// if (lastRoutes == 'pagesA/newBusiness/signature/signature') {
		// 	// 	uni.navigateBack({
		// 	// 		delta: 2
		// 	// 	})
		// 	// } else {
		// 	// 	uni.navigateBack({
		// 	// 		delta: 1
		// 	// 	})
		// 	// }
		// },
	}
</script>

<style lang='scss' scoped>
	.agreement-list {
		padding-bottom: 180rpx;
		padding-top: 184rpx;
	}

	.no-vehicle {
		width: 100%;
		background-color: #f3f3f3;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 28rpx;
		color: #999999;
		font-weight: 400;
		text-align: center;
		margin-top: 20rpx;
	}
</style>