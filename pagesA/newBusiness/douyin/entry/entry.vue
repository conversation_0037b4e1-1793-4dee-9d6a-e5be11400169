<template>
	<view class="entry">
		<handle-step type="douyin" :current="0" />
		<view class="order-info">
			<title title="订单信息验证"></title>
			<view class="order-info_text">
				请在下方输入抖音商城订单编号
			</view>
			<view class="input-wrapper">
				<input v-model="douYinNo" class="search-input" type="text" />
				<button class="search-btn" @click="searchOrder">确定</button>
			</view>
			<view class="order-info_list" v-if="orderData.code == '2'">
				该订单已提交ETC新办申请，查看详情请点击 <text @click="toServiceOrder" style="color: #0066E9;">服务订单</text>
			</view>
			<view class="order-info_error" v-if="orderData.code == '3'">
				{{orderData.msg}}
			</view>
			<view class="order-info_error" v-if="orderData.code == '4'">
				未查询到该订单号相关的订单信息，请你确认输入正确的订单编号
			</view>
			<view class="order-info_tips">
				<text style="color: #FF9038;">*</text>
				<text class="tips-text">温馨提示：输入订单号后将展示您在其他平台已下单的相关产品信息和支付信息, 订单状态同步可能存在延迟，敬请谅解。</text>
			</view>
		</view>
		<view class="product-info" v-if="orderData && orderData.orderId">
			<title title="产品信息"></title>
			<view class="product-info_section">
				<view class="product-info_card">
					<view class="label">
						渠道
					</view>
					<view class="value">
						抖音商城
					</view>
				</view>
				<view class="product-info_card">
					<view class="label">
						已支付费用
					</view>
					<view class="value">
						￥{{orderData.orderAmount}}
					</view>
				</view>
				<view class="product-info_card">

					<view class="label">
						产品类型
					</view>
					<view class="value">
						{{orderData.deviceType == '1'? '次次顺记账卡（基础）' :'次次顺记账卡（进阶）'}}
					</view>
				</view>
				<view class="product-info_card">
					<view class="label">
						支付时间
					</view>
					<view class="value">
						{{orderData.payTime}}
					</view>
				</view>
				<view class="product-info_card">
					<view class="label">
						订单状态
					</view>
					<view class="value">
						{{orderData.orderStatus}}
					</view>
				</view>
			</view>
			<view class="ccs-content">
				<view class="content-card">
					<view class="card-item card-selector">
						<image src="../../../static/new-apply/product-select/icon-card_2_check.png" mode=""></image>
						<view class="card-text">
							<view class="card-name">
								捷通次次顺记账卡
							</view>
							<view class="card-desc">
								绑定银行卡代扣，先通行后扣费
							</view>
						</view>
					</view>
					<view class="card-title">
						捷通次次顺记账卡（{{orderData.deviceType == '1'?'基础':'进阶'}}）
					</view>
					<view class="card-content">
						<image class="img"
							:src="orderData.deviceType == '2'?'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/jjk.png':'../../../static/new-apply/ccs/ccs_product.png'"
							style="width: 190rpx;height: 120rpx;" mode=""></image>
						<view class="card-text">
							<view class="text">
								1、绑定微信支付账户代扣
							</view>
							<view class="text" style="margin-top: 24rpx;">
								2、先通行后付费
							</view>
						</view>
						<view class="price">
							<!-- ￥{{moneyFilter(benefitServiceFee)}} -->
							{{orderData.deviceType == '1'?'￥168':'￥188'}}
						</view>
					</view>
					<view class="card-tips">
						*权益服务内容:连续12个月免费领取{{orderData.deviceType == '2'?'60':'50'}}元/月商场消费券礼包，每张消费券须在领取当月使用完毕。
					</view>
				</view>
			</view>

		</view>
		<view class="teach-info" v-if="!showTeach" @click="showTeach = true">
			<title title="如何复制订单号">
				<template>
					<view class="img-wrapper">
						<image src="../../../static/new-apply/douyin/cant-open.png" style="width: 40rpx;height: 40rpx;"
							mode=""></image>
					</view>
				</template>
			</title>
		</view>
		<view class="teach-info" v-if="showTeach">
			<title title="如何复制订单号">
				<template>
					<view class="img-wrapper">
						<image @click="showTeach = false" src="../../../static/new-apply/douyin/can-open.png"
							style="width: 40rpx;height: 40rpx;" mode=""></image>
					</view>
				</template>
			</title>
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/dy_bg.png"
				style="width: 710rpx;height:2128rpx;" mode="aspectFit"></image>
		</view>
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:showConfirm="modal.showConfirm">
			<view class="content-wrapper">
				<view class="title">办理须知</view>
				<scroll-view class="desc-wrapper" scroll-y="true">
					<view class="">
						<!-- 						<rich-text v-if="Object.keys(config).length > 0 && formData.gxCardType == '5'" class="rich-text"
							:nodes="config.ddpRemark"></rich-text>
						<rich-text v-if="Object.keys(config).length > 0 && formData.gxCardType == '10'"
							class="rich-text" :nodes="config.ttpRemark"></rich-text> -->
						<rich-text v-if="baseConfig && orderData.deviceType == '1' " class="rich-text"
							:nodes="config"></rich-text>
						<rich-text v-if="advanceConfig && orderData.deviceType == '2'" class="rich-text"
							:nodes="config"></rich-text>
					</view>
				</scroll-view>
				<view class="check-wrapper" @click="check">
					<image v-if="!isChecked" style="width: 28rpx;height: 28rpx;"
						src="../../../static/new-apply/vehicle/check.png" mode="">
					</image>
					<image v-if="isChecked" style="width: 28rpx;height: 28rpx;"
						src="../../../static/new-apply/vehicle/checked.png" mode="">
					</image>
					<text class="check-text">我已阅读，确认无问题</text>
				</view>
				<view class="btn-wrapper">
					<view class="cancel-btn" @click="cancel">
						取消
					</view>
					<view class="confirm-btn" @click="confirm">
						继续办理
					</view>
				</view>
			</view>
		</neil-modal>
		<view class="error-tip" v-if="orderData.code == '0'">
			提示：订单发货前暂不支持下一步操作
		</view>
		<tButton @toNext="toNext" :buttonList="buttonList" :disabled="orderData.code != '1'"></tButton>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import title from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	// import neilModal from '@/components/neil-modal/neil-modal.vue'
	import {
		getOpenid,
		setOpenidForRead,
		getOpenidForRead,
		getLoginUserInfo,
		setUserId,
		getUserId
	} from '@/common/storageUtil.js';
	export default {
		components: {
			handleStep,
			tLoading,
			title,
			tButton,
			// neilModal
		},
		data() {
			return {
				isLoading: false,
				isChecked: false,
				showTeach: false,
				buttonList: [{
					title: '下一步',
					handle: 'toNext'
				}],
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					showConfirm: false,
				},
				formData: {
					businessType: '1', //1：新办 2：换卡 3：换obu
					book: '',
					id: '',
					sourceType: '1',
					deviceType: '', //次次顺设备类型
					productType: '10', //抖音默认次次顺
				},
				douYinNo: '',
				orderData: null,
				baseConfig: {}, //办理须知内容
				advanceConfig: {}
			};
		},
		onLoad(option) {
			if (option && option.applyId) {
				this.formData.id = option.applyId
			}
			this.getRuleConfig()
		},
		methods: {
			toNext() {
				console.log('下一步')
				// if (!this.valid()) {
				// 	return
				// }
				this.modal.show = true
			},
			toServiceOrder() {
				uni.navigateTo({
					url: '/pagesA/serviceOrder/index'
				})
			},
			confirm() {
				if (!this.isChecked) {
					uni.showModal({
						title: '提示',
						content: '请确认无问题后勾选我已阅读。',
					});
					return
				}
				if (!this.orderData.id) {
					this.getApplyId(() => {
						this.modal.show = false
						//保存草稿
						this.isLoading = true
						let params = this.formData
						console.log(params, '入参');
						this.$request.post(this.$interfaces.dySaveDraft, {
							data: params
						}).then(res => {
							this.isLoading = false;
							if (res.code == 200) {
								// //获取所有草稿
								// 订阅
								// if (!this.formData.book) {
								this.read()
								// }

								uni.navigateTo({
									url: '/pagesA/newBusiness/douyin/carInfo/carInfo'
								})
							} else {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								});
							}
						}).catch(err => {
							this.isLoading = false;
							uni.showModal({
								title: '提示',
								content: err.msg,
								showCancel: false
							});
						})
					})
				} else {
					this.modal.show = false
					//保存草稿
					this.isLoading = true
					let params = this.formData
					console.log(params, '入参');
					this.$request.post(this.$interfaces.dySaveDraft, {
						data: params
					}).then(res => {
						this.isLoading = false;
						if (res.code == 200) {
							// //获取所有草稿
							// 订阅
							// if (!this.formData.book) {
							this.read()
							// }

							uni.navigateTo({
								url: '/pagesA/newBusiness/douyin/carInfo/carInfo'
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					}).catch(err => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						});
					})
				}

			},
			getRuleConfig() {
				this.isLoading = true

				let data = {
					routePath: this.$interfaces.dyGetRuleConfig.method,
					bizContent: {}
				};
				console.log('入参params', data)
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false
						console.log('配置信息', res)
						if (res.code != 200) {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
							return
						} else {
							this.advanceConfig = res.data.advanceNotice
							this.baseConfig = res.data.basicNotice
						}
					})
					.catch(error => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			searchOrder() {
				if (!this.douYinNo) {
					uni.showModal({
						title: '提示',
						content: '请先输入订单编号',
						showCancel: false
					});
					return
				}
				let params = {
					douYinNo: this.douYinNo
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.selectDouYinOrder, {
						data: params
					})
					.then(res => {
						console.log('抖音订单查询', res)
						this.isLoading = false
						if (res.code != 200) {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
							return
						} else {
							this.orderData = res.data
							if (this.orderData.id) {
								this.formData.id = res.data.id
								this.formData.deviceType = this.orderData.deviceType
								this.$store.dispatch('setApplyId', res.data.id)
							}
						}
					})
					.catch(error => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			getApplyId(callback) {
				console.log('创建applyId==========>>>>>>>>>>>>>>>>>>>>>')
				this.isLoading = true
				this.$request
					.post(this.$interfaces.dyGetApplyId, {
						data: {
							douYinNo: this.orderData.orderId
						}
					})
					.then(res => {
						this.isLoading = false
						if (res.code != 200) {
							uni.showModal({
								title: '提示',
								content: res.msg,
							});
						} else {

							console.log(res, '申请单创建');
							let applyId = res.data.applyId
							this.formData.id = applyId
							this.$store.dispatch('setApplyId', applyId)
							callback()
							// this.getDraft(applyId, callback)
						}
					})
					.catch(error => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			read() {
				console.log('订阅方法')
				uni.requestSubscribeMessage({
					tmplIds: ['6Q0Praee_bD-2ZFNkFSQYSztnN3u8LKBWzM-i8W2Gc4'],
					success: (res) => {
						console.log('订阅方法', res['6Q0Praee_bD-2ZFNkFSQYSztnN3u8LKBWzM-i8W2Gc4'])
						// let msgContent = res['6Q0Praee_bD-2ZFNkFSQYSztnN3u8LKBWzM-i8W2Gc4']
						// if (msgContent == 'accept') {
						// 	this.formData.book = '1'
						this.wxGetOpenId()
						// } else if (msgContent == 'reject') {
						// 	this.formData.book = '0'
						// 	uni.showModal({
						// 		title: "提示",
						// 		content: '已拒绝订阅消息',
						// 		showCancel: false,
						// 	});
						// }
					},
					fail: (err) => {
						console.log(err)
					}
				})
			},
			wxGetOpenId() {
				let _self = this;
				let readOpenId = getOpenidForRead()
				console.log('readOpenId-product', readOpenId)
				if (readOpenId) {
					this.saveOpenId(readOpenId)
				} else {
					// 微信手机号授权登录
					wx.login({
						success(res) {
							let params = {
								code: res.code
							}

							_self.$request.post(_self.$interfaces.getOpenid, {
								data: params
							}).then((res) => {
								if (res.code == 200) {
									if (res.data && res.data.openid) {
										console.log('获取微信openId', res.data.openid)
										setOpenidForRead(res.data.openid)
										_self.saveOpenId(res.data.openid)
									}
								}
							})
						}
					})
				}
			},
			//获取用户openId保存订阅
			saveOpenId(openId) {
				this.$request.post(this.$interfaces.saveOpenId, {
					data: {
						netUserId: getLoginUserInfo().userIdStr,
						openId: openId
					}
				}).then(res => {}).catch(err => {})
			},
			check() {
				this.isChecked = !this.isChecked
			},
			cancel() {
				this.modal.show = false
			},
			// getDraft(id) {
			// 	this.isLoading = true
			// 	let params = {
			// 		id: id
			// 	}
			// 	// console.log(params, '入参');
			// 	this.$request.post(this.$interfaces.getDraft, {
			// 		data: params
			// 	}).then(res => {
			// 		this.isLoading = false;
			// 		if (res.code == 200) {
			// 			let result = res.data

			// 			console.log('草稿===》》》', res.data)
			// 			// this.formData.vehicleType = result.vehicleType || ''
			// 			// this.formData.gxCardType = result.gxCardType || ''
			// 			// this.formData.productType = result.productType || ''
			// 			// if (result.gxCardType) {
			// 			// 	this.setPrice()
			// 			// }

			// 		} else {
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: res.msg,
			// 				showCancel: false
			// 			});
			// 		}
			// 	}).catch(err => {
			// 		this.isLoading = false;
			// 		uni.showModal({
			// 			title: '提示',
			// 			content: err.msg,
			// 			showCancel: false
			// 		});
			// 	})
			// },
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	};
</script>

<style scoped lang="scss">
	.entry {
		font-family: PingFang SC, PingFang SC;
		padding-bottom: 180rpx;
		padding-top: 184rpx;

		.order-info {
			margin: 20rpx;
			background: $uni-bg-color;
			border-radius: 12rpx;
			padding: 30rpx 30rpx 10rpx 30rpx;

			.input-wrapper {
				display: flex;
				justify-content: center;
				align-items: center;

				.search-input {
					flex: 1;
					margin-right: 16rpx;
					height: 70rpx;
					line-height: 70rpx;
					padding: 0 20rpx;
					border-radius: 8rpx;
					border: 2rpx solid #E9E9E9;

				}

				.search-btn {
					width: 134rpx;
					height: 70rpx;
					line-height: 70rpx;
					background: #5591FF;
					border-radius: 8rpx;
					font-weight: 400;
					font-size: 30rpx;
					color: #FFFFFF;
				}
			}

			&_text {
				margin: 16rpx 0 20rpx 0;
				font-weight: 400;
				font-size: 26rpx;
				color: #666666;
				// line-height: 30rpx;
			}

			&_tips {
				margin: 20rpx -8rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				line-height: 38rpx;
			}

			&_list {
				padding: 0 14rpx;
				height: 61rpx;
				line-height: 61rpx;
				margin: 25rpx 0;
				background: rgba(85, 145, 255, 0.1);
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				font-size: 26rpx;
			}

			&_error {
				display: flex;
				align-items: center;
				height: 104rpx;
				margin: 25rpx 0;
				padding: 0 14rpx;
				// line-height: 104rpx;
				background: rgba(255, 84, 84, 0.1);
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #FF5454;
			}
		}

		.teach-info {
			margin: 20rpx;
			background: $uni-bg-color;
			border-radius: 12rpx;
			padding: 30rpx;
		}

		.product-info {
			margin: 20rpx;
			background: $uni-bg-color;
			border-radius: 12rpx;
			padding: 30rpx 30rpx 50rpx 30rpx;

			&_card {
				display: flex;
				align-items: center;
				margin: 30rpx 0;

				.label {
					width: 132rpx;
					font-weight: 400;
					font-size: 26rpx;
					color: #999999;
					line-height: 26rpx;
					text-align: left;
					font-style: normal;
				}

				.value {
					margin-left: 55rpx;
					font-weight: 400;
					font-size: 26rpx;
					color: #333333;
					line-height: 26rpx;
					text-align: left;
					font-style: normal;
				}
			}

			.ccs-content {
				margin-top: 20rpx;
				font-family: PingFangSC, PingFang SC;
				background: #F6FAFF;
				border-radius: 12rpx;
				border: 1rpx solid #C2DDFF;

				.card-title {
					padding: 30rpx;
					font-weight: 500;
					font-size: 28rpx;
					color: #323435;
					line-height: 40rpx;
				}

				.card-item {
					margin-bottom: 20rpx;
					display: flex;
					align-items: center;
					height: 128rpx;
					background: #F8F8F8;
					border-radius: 12rpx;
					padding: 25rpx 33rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;

					&>image {
						margin-right: 28rpx;
						width: 56rpx;
						height: 56rpx;
					}

					.card-name {
						font-size: 28rpx;
						line-height: 40rpx;

					}

					.card-desc {
						line-height: 33rpx;
						font-size: 24rpx;
					}

				}

				.card-selector {
					background: #E4EFFF;
					border-radius: 12rpx;
					// border: 2rpx solid #009ff6;
				}

				.card-content {
					display: flex;
					align-items: center;
					justify-content: center;

					.img {
						margin-left: 30rpx;
					}

					.card-text {
						font-weight: 400;
						font-size: 24rpx;
						color: #323435;
						line-height: 33rpx;
					}

					.price {
						margin-left: auto;
						height: 81rpx;
						padding: 0 10rpx;
						line-height: 81rpx;
						text-align: center;
						background: #FF9D09;
						border-radius: 100rpx 0rpx 0rpx 100rpx;
						font-weight: 500;
						font-size: 28rpx;
						color: #FFFFFF;
					}
				}

				.card-tips {
					margin: 30rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: #888888;
					line-height: 33rpx;
					// text-align: center;
				}
			}

		}

		.error-tip {
			position: fixed;
			bottom: 164rpx;
			left: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 61rpx;
			width: 100%;
			// margin: 25rpx 0;
			padding: 0 14rpx;
			// line-height: 104rpx;
			background: rgb(255, 238, 238);
			// opacity: 0.5;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-weight: 400;
			font-size: 26rpx;
			color: #FF5454;
		}


		/deep/.neil-modal__container {
			width: 80%;
			height: 80%;

			.neil-modal__content {
				height: 100%;
			}
		}

		.content-wrapper {
			height: 100%;

			.title {
				height: 116rpx;
				font-size: 36rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #323435;
				line-height: 116rpx;
				text-align: center;
				border-bottom: 1rpx solid #E9E9E9;
			}

			.desc-wrapper {
				height: calc(100% - 312rpx);
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				padding: 45rpx 45rpx 0 45rpx;

				.rich-text {
					text-align: left;
				}

				.desc {
					margin-bottom: 15rpx;
				}
			}

			.check-wrapper {
				display: flex;
				align-items: center;
				padding: 40rpx 0 40rpx 40rpx;

				.check-text {
					margin-left: 10rpx;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #888888;
					line-height: 33rpx;
				}
			}

			.btn-wrapper {
				display: flex;
				// height: 88rpx;

				.cancel-btn {
					flex: 1;
					height: 88rpx;
					line-height: 88rpx;
					background: #FFFFFF;
					border-top: 1rpx solid #E7E7E7;
				}

				.confirm-btn {
					flex: 1;
					height: 88rpx;
					line-height: 88rpx;
					background: #0066E9;
					color: #FFFFFF;
					border-bottom: 1rpx solid #0066E9;
				}
			}
		}
	}
</style>