<!-- 安装方式选择 -->
<template>
	<!-- 使用uni-popup后需要加入page-meta组件防止页面滚动穿透 -->
	<page-meta :page-style="'overflow:'+(plateShow || regionShow?'hidden':'visible')"></page-meta>
	<view class="installation">
		<handle-step type="douyin" :current="1" />
		<!-- 		<scroll-view scroll-y="true">
			<view class="scroll-wrapper"> -->
		<view class="car-type_select">
			<title title="客货选择"></title>
			<view class="car-wrapper g-flex g-flex-align-center justify-center">
				<view class="car-item" @click="selectCarType(item.value)" v-for="(item,index) in vehicleTypeList"
					:class="item.value == vehicleInfo.vehicleType?'selector':''" :key="index">
					<image :src="item.src" mode=""></image>
					<view>{{item.label}}</view>
				</view>
			</view>
		</view>
		<view class="car-no">
			<title title="请选择车牌颜色" titleDesc="（非车身颜色）" setPadding="30"></title>
			<view class="plate_color">
				<view v-for="(item,index) in palteColorList" :key="index" @click="selectPlateColor(index)"
					class="plateColorList" v-show="item.isShow">

					<image :src="item.icon" class="plateColor" width="200rpx" height="80rpx" mode='aspectFilt'>
					</image>
					<image
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/icon_select_license_plate_color.png"
						v-show="item.checked" class="checkplateColor" width="40rpx" height="40rpx" mode='aspectFilt'>
					</image>
					<view class="plate-color__text" style="text-align: center;">{{item.label}}</view>
				</view>
				<!-- <view class="plateColorList" @click="selectPlateColor('more')">
					<view class="otherColor" v-if="!otherChecked">其他颜色</view>
					<image v-if="otherChecked && moreColorName == '白色'"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_white.png"
						class="plateColor" width="200rpx" height="80rpx" mode='aspectFilt'>
					</image>

					<image v-if="otherChecked && moreColorName == '蓝白渐变色'"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_blue_white.png"
						class="plateColor" width="200rpx" height="80rpx" mode='aspectFilt'>
					</image>
					<image
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/icon_select_license_plate_color.png"
						v-show="otherChecked" class="checkplateColor" width="40rpx" height="40rpx" mode='aspectFilt'>
					</image>
					<view class="plate-color__text" style="text-align: center;margin-top: 4rpx;">
						{{!otherChecked?'其他':moreColorName}}
					</view>
				</view> -->
				<!-- <view class="select-more" v-if="showMoreColor">
					<view class="select-more__item" @click="confirmColor(0)">
						<image class="select-more__image"
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_white.png"
							mode="aspectFilt"></image>
						<view class="select-more__text">
							白色
						</view>
					</view>
					<view class="select-more__item" @click="confirmColor(1)">
						<image class="select-more__image"
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_blue_white.png"
							mode="aspectFilt"></image>
						<view class="select-more__text">
							蓝白渐变色
						</view>
					</view>
					<image class="icon-select__color" src="../../static/new-apply/product-select/select-color.png"
						mode="">
					</image>
				</view> -->
			</view>

		</view>

		<view class="plate_code">
			<title title="请输入车牌号" setPadding="30"></title>
			<view class="so-plate-body" @click="showPlate">
				<view class="so-plate-word" :class="{ active: currentInputIndex == 0 }" data-index="0">
					<text>{{ currentInputValue[0] }}</text>
				</view>
				<view class="so-plate-word" :class="{ active: currentInputIndex == 1 }" data-index="1">
					<text>{{ currentInputValue[1] }}</text>
				</view>
				<view class="so-plate-dot"></view>
				<view class="so-plate-word" :class="{ active: currentInputIndex == 2 }" data-index="2">
					<text>{{ currentInputValue[2] }}</text>
				</view>
				<view class="so-plate-word" :class="{ active: currentInputIndex == 3 }" data-index="3">
					<text>{{ currentInputValue[3] }}</text>
				</view>
				<view class="so-plate-word" :class="{ active: currentInputIndex == 4 }" data-index="4">
					<text>{{ currentInputValue[4] }}</text>
				</view>
				<view class="so-plate-word" :class="{ active: currentInputIndex == 5 }" data-index="5">
					<text>{{ currentInputValue[5] }}</text>
				</view>
				<view class="so-plate-word" :class="{ active: currentInputIndex == 6 }" data-index="6">
					<text>{{ currentInputValue[6] }}</text>
				</view>
				<view v-if="keyType == 1" class="so-plate-word so-plate-dashed" data-index="7">
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/add-plus.png"
						mode=""></image>
					<view class="so-plate-font">新能源</view>
				</view>
				<view v-if="keyType == 2" class="so-plate-word" :class="{ active: currentInputIndex == 7 }"
					data-index="7">
					<text>{{ currentInputValue[7] }}</text>
				</view>
			</view>
			<view class="error-text" v-if="errorMsg">
				{{errorMsg}}
			</view>
		</view>
		<view class="owner">
			<title title="请选择车辆归属人" setPadding="30">
				<template>
					<view class="single-wrapper" v-if="formData.owner == 1 || formData.owner == 2">
						<view class="single" @click="preview">
							授权书填写样例
						</view>
						<view class="single" @click="downLoad">
							下载模板
						</view>
					</view>
				</template>
			</title>
			<view class="car-type g-flex g-flex-align-center justify-center">
				<view class="car-item" @click="selectType(index)" :class="{'selector':index == formData.owner}"
					v-for="(item,index) in ownerList" :key="index">
					{{item}}
					<image v-if="index == formData.owner"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/icon_select_license_plate_color.png"
						class="check-type" width="40rpx" height="40rpx" mode='aspectFilt'></image>
				</view>
			</view>
			<view class="owner-tips" v-if="formData.owner != null">
				温馨提示：<text class="tips">{{tips[formData.owner]}}</text>
			</view>
		</view>
		<tButton @toNextHandle="toNextHandle" :buttonList="buttonList"></tButton>

		<plate-input v-if="plateShow" :keyType="keyType" :plate="formData.vehicleCode" @export="setPlate"
			@close="plateShow=false" />
		<u-mask :show="showMoreColor" @click="showMoreColor = false"></u-mask>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import plateInput from '@/components/uni-plate-input/uni-plate-input.vue';
	import title from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import float from '@/common/method/float.js'
	import {
		checkPhone,
	} from "@/common/util.js";
	import {
		downLoadFile,
		previewFile
	} from '@/pagesA/common/method.js'
	export default {
		components: {
			handleStep,
			tLoading,
			title,
			plateInput,
			tButton,
		},
		data() {
			return {
				options: '',
				regionShow: false,
				showConfirmSign: false,
				isLoading: false,
				plateShow: false,
				otherChecked: false,
				showMoreColor: false,
				showMoreCarColor: false,
				textClass: false,
				vehicleTypeList: [{
					value: '2',
					src: '../../../static/new-apply/product-select/car_1.png',
					label: '客车',
				}, {
					value: '1',
					src: '../../../static/new-apply/product-select/car_2.png',
					label: '货车',
				}, {
					value: '3',
					src: '../../../static/new-apply/product-select/car_3.png',
					label: '专项车',
				}],
				moreColorName: "选择其他颜色",
				otherPalteColorList: [{
						value: '3',
						label: '白色'
					},
					{
						value: '6',
						label: '蓝白渐变色'
					}
				],
				palteColorList: [{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_blue.png",
						checked: false,
						isShow: true,
						value: '0',
						label: '蓝色'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_gradient_green.png",
						checked: false,
						isShow: true,
						value: '4',
						label: '渐变绿'
					},
					// {
					// 	icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_green.png",
					// 	checked: false,
					// 	isShow: true,
					// 	value: '1',
					// 	label: '黄色'
					// },
					// {
					// 	icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_yellow_green.png",
					// 	checked: false,
					// 	isShow: true,
					// 	value: '5',
					// 	label: '黄绿色'
					// },
					// {
					// 	icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor_2.8.0/plate_black.png",
					// 	checked: false,
					// 	isShow: true,
					// 	value: '2',
					// 	label: '黑色'
					// },
				],
				ownerList: ['本人车辆', '他人车辆', '单位车辆'],
				tips: ['请准备您的身份证及行驶证', '他人车辆将办理在您的个人账户名下，请准备您的身份证、车主身份证、行驶证、车辆授权书',
					'单位车辆将办理在您的个人账户名下，请准备您的身份证、单位营业执照、车辆授权书、行驶证'
				],
				buttonList: [
					// 	{
					// 	title: '上一步',
					// 	handle: 'toLast'
					// }, 
					{
						title: '下一步',
						handle: 'toNextHandle'
					}
				],
				vehicleInfo: {
					// customer_id: '',
					positiveVehicleImgUrl: '', //行驶证正页
					negativeVehicleImgUrl: '', //行驶证副页
					ownerPositiveImgUrl: '', //车主证件正面
					ownerNegativeImgUrl: '', //车主证件反面
					positiveVehicleImg: '', //行驶证md5
					negativeVehicleImg: '', //行驶证md5
					ownerPositiveImg: '', //车主证件正面md5
					ownerNegativeImg: '', //车主证件反面md5
					vehicleImgUrl: '', //行驶证车辆照片
					vehicleImg: '', //行驶证车辆照片
					authLetterImgUrl: '', //车辆授权书
					authLetterImg: '',
					transportIdImgUrl: '', //车辆道路运输证
					transportIdImg: '',
					//	车牌颜色（0蓝 1黄 2黑 3白 4渐变绿 5黄绿双拼 6蓝白渐变）
					vehicleColor: '0',
					vehicleCode: '', //车牌号
					vehicleModel: '', //车型
					vehicleEngine: '', //发动机号
					vehicleNationalType: '1',
					//车辆类型
					vehicleCarType: '2',
					//	车型（车型 2客车,1货车）
					vehicleType: '2',
					//	车辆用户类型(0 普通车 1集卡车 2卧铺车 8军车(交通战备车) 9警车 15紧急车 16特殊公务车)，集卡车只有车型选货车的时候显示
					vehicleUserType: '0',
					//	座位数
					vehicleSeat: '',
					//	吨数(核定载质量)(默认置空 让操作员填写)
					permittedWeight: '',
					//VIN码
					vehicleDistinguish: '',
					//牵引总质量
					permittedTowWeight: '',
					//整备质量(默认置空 让操作员填写)
					maintenanceMass: '',
					//车辆总质量 (默认置空 让操作员填写)
					vehicleTon: '',
					//长
					vehicleLength: '',
					//	宽
					vehicleWidth: '',
					//	高
					vehicleHeight: '',
					//	车轮数
					vehicleWheels: '',
					//	车轴数
					vehicleAxles: '',
					//	轴距
					vehicleWheelbases: '0',
					// 发证日期
					issueDate: '',
					//车主姓名
					ownerName: '',
					//车主证件类型
					ownerIdType: '0',
					//车主证件号码
					ownerIdNum: '',
					//产品编号
					productCode: '',
					// nodeStep: '4',
					isContainer: '1', //集装箱0是，1不是
					isqcy: '1', //集装箱0是，1不是

				},
				oldVehicleCode: '', //记录历史车牌号码
				oldVehicleColor: '', //记录历史车牌颜色
				oldOwner: '',
				formData: {
					vehicleColor: '',
					vehicleCode: '',
					owner: null,
					id: '',
				},
				currentInputIndex: null,
				currentInputValue: ['', '', '', '', '', '', ''],
				plateNo: '',
				keyType: 1, //车牌类型,
				errorMsg: '', //车牌唯一性校验提示
				canIssue: '', //车牌是否可以发行，
			};
		},
		onLoad() {
			const phoneType = uni.getSystemInfoSync();
			if (phoneType.platform == 'ios') {
				this.textClass = true
			} else if (phoneType.platform == 'android') {
				this.textClass = false
			}

			this.getDraft()
		},
		methods: {
			//车牌唯一性校验
			checkVehicleNo(vehicle, callback) {
				this.isLoading = true
				this.$request.post(this.$interfaces.dyCheckVehicleNo, {
					data: vehicle
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res====>>>', res)
						if (res.data.result == '1') {
							this.errorMsg = res.data.reason || '该车牌号不可发行'
						} else {
							this.errorMsg = ''
						}
						this.canIssue = res.data.result
						//接口回调，作提交前处理
						callback && callback()
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			getDraft() {
				this.isLoading = true
				// let params = this.formData
				// console.log(params, '入参');
				let params = {
					id: this.$store.state.applyId
				}
				this.$request.post(this.$interfaces.dyGetDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						// //获取所有草稿
						let result = res.data
						console.log('草稿==========>>>>', res.data)
						this.showData(result)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			//信息回显
			showData(result) {
				console.log('result===>>>', result.productType)
				//基础信息回显
				this.formData.id = result.id || ''
				this.formData.vehicleColor = result.vehicleColor || ''
				this.formData.vehicleCode = result.vehicleCode || ''
				this.vehicleInfo.vehicleType = result.vehicleType || '2'
				this.vehicleInfo.productCode = result.productType || ''
				if (result.vehicleCode) {
					//有车辆信息判断下，更改车牌就要提示清掉
					// this.isHaveVehicleInfo = true
					this.oldVehicleCode = result.vehicleCode
					this.oldVehicleColor = result.vehicleColor
				}
				if (result.owner) {
					this.oldOwner = result.owner
				}
				this.formData.owner = result.owner || null
				//地区回显

				if (result.vehicleCode) {
					//回显车牌组件
					this.plateNo = result.vehicleCode
					let inputValueArr = result.vehicleCode.split('');
					console.log('inputValueArr==>>>>>', inputValueArr)
					if (inputValueArr.length == '7') {
						//普通车
						this.keyType = 1
					} else if (inputValueArr.length == '8') {
						//新能源车
						this.keyType = 2
					}
					//回显车牌
					this.currentInputValue = inputValueArr
				}

				// //地区信息回显
				// if (this.formData.provinceName && this.formData.cityName && this.formData.areaName) {
				// 	this.options = [this.formData.provinceName + ' ' + this.formData.cityName + ' ' +
				// 		this.formData.areaName
				// 	]
				// }
				// this.options = user.nationalCode
				// this.areaCode = user.nationalCode
				//车牌信息回显
				if (this.formData.vehicleColor == '0' || this.formData.vehicleColor == '1' || this.formData.vehicleColor ==
					'4') {
					//普通类型车牌回显(蓝，黄，渐变绿)
					for (let obj of this.palteColorList) {
						if (obj['value'] == this.formData.vehicleColor)
							obj.checked = true
					}
				} else if (this.formData.vehicleColor == '2' || this.formData.vehicleColor == '5') {
					//普通类型车牌回显(黄绿，黑色)
					this.showMoreCarColor = true
					for (let obj of this.palteColorList) {
						obj.isShow = true
						if (obj['value'] == this.formData.vehicleColor)
							obj.checked = true
					}
				} else if (this.formData.vehicleColor == '3' || this.formData.vehicleColor == '6') {
					//其他颜色车牌
					for (let obj of this.palteColorList) {
						obj.isShow = true
					}
					for (let obj of this.otherPalteColorList) {
						this.showMoreCarColor = true
						this.showMoreColor = true
						this.otherChecked = true
						if (obj['value'] == this.formData.vehicleColor) {
							this.moreColorName = obj.label
						}

					}
				}
			},
			showPlate() {
				if (!this.formData.vehicleColor) {
					uni.showModal({
						title: "提示",
						content: "请先选择车牌颜色",
						showCancel: false,
					});
					return false
				}
				this.plateShow = true
			},
			valid() {
				if (this.errorMsg || this.canIssue == '1') {
					uni.showModal({
						title: "提示",
						content: "您所输入的车牌有错误提示，请按提示进行修改",
						showCancel: false,
					});
					return false
				} else if (!this.formData.vehicleColor) {
					uni.showModal({
						title: "提示",
						content: "请先选择车牌颜色",
						showCancel: false,
					});
					return false
				} else if (!this.formData.vehicleCode) {
					uni.showModal({
						title: "提示",
						content: "请先输入车牌号",
						showCancel: false,
					});
					return false
				} else if (this.keyType == 2 && this.formData.vehicleCode.length != 8) {
					uni.showModal({
						title: "提示",
						content: "请确认您的车牌号码是否正确",
						showCancel: false,
					});
					return false
				} else if (this.formData.owner == null) {
					uni.showModal({
						title: "提示",
						content: "请先选择车辆归属人",
						showCancel: false,
					});
					return false
				}

				return true
			},
			onpopupopened(e) {
				console.log('popupopened');
				this.regionShow = true
			},
			onpopupclosed(e) {
				console.log('popupclosed');
				this.regionShow = false
			},

			selectCarType(value) {
				if (value == '1' || value == '3') {
					uni.showModal({
						title: '提示',
						content: '敬请期待'
					})
					return
				}
				this.vehicleInfo.vehicleType = value
			},
			selectType(index) {
				this.formData.owner = index
			},
			// //车牌获取回调
			setPlate(plate) {
				console.log('plate', plate, plate.length)
				let inputValueArr = plate.split('');
				console.log('inputValueArr==>>>>>', inputValueArr.length, this.formData.vehicleCode)
				if (inputValueArr.length == '7') {
					if (this.formData.vehicleColor == '4' || this.formData.vehicleColor == '5') {
						uni.showModal({
							title: '提示',
							content: '当前车牌颜色选择为新能源车牌而不是普通车牌，请确认车牌颜色与车辆号码。'
						})
						return
					}
					//普通车
					this.keyType = 1
				} else if (inputValueArr.length >= '8') {
					if (plate.slice(-2) == '应急') {
						this.keyType = 3
					} else {
						if (this.formData.vehicleColor != '4' && this.formData.vehicleColor != '5') {
							uni.showModal({
								title: '提示',
								content: '当前车牌颜色选择为普通车牌而不是新能源车牌，请确认车牌颜色与车辆号码'
							})
							return
						}
						//新能源车
						this.keyType = 2
					}
				}
				//车牌赋值
				if (plate.length >= 7) this.formData.vehicleCode = plate

				//回显车牌
				this.currentInputValue = inputValueArr
				this.plateShow = false

				//车牌唯一性校验
				this.checkVehicleNo({
					vehicleCode: this.formData.vehicleCode,
					vehicleColor: this.formData.vehicleColor,
					vehicleType: this.vehicleInfo.vehicleType
				})
			},

			// 选择车牌颜色
			selectPlateColor(index) {
				console.log('选择车牌颜')
				if (index === "more") {
					this.showMoreColor = true
				} else {
					this.otherChecked = false
					for (let obj of this.palteColorList) {
						obj.checked = false
					}
					this.moreColorName = "选择其他颜色"
					this.palteColorList[index].checked = true
					this.formData.vehicleColor = this.palteColorList[index].value

					//判断是新能源还是普通车
					if (this.palteColorList[index].value == '4' || this.palteColorList[index].value == '5') {
						//4渐变绿，5黄绿色
						this.keyType = 2
					} else {
						this.keyType = 1
						//重置新能源位为空
						if (this.formData.vehicleCode.length == 8) {
							this.currentInputValue[7] = ''
							this.formData.vehicleCode = this.formData.vehicleCode.substr(0, 7)
						}
					}
				}

				if (this.formData.vehicleCode && this.formData.vehicleColor) {
					//车牌唯一性校验
					this.checkVehicleNo({
						vehicleCode: this.formData.vehicleCode,
						vehicleColor: this.formData.vehicleColor,
						vehicleType: this.vehicleInfo.vehicleType
					})
				}

			},
			close() {
				this.plateShow = false
				this.currentInputIndex = null
			},
			// clearVehicleInfo() {
			// 	//保存草稿
			// 	this.isLoading = true
			// 	let params = this.vehicleInfo
			// 	console.log(params, '入参');
			// 	this.$request.post(this.$interfaces.dySaveDraft, {
			// 		data: params
			// 	}).then(res => {
			// 		this.isLoading = false;
			// 		if (res.code == 200) {

			// 		} else {
			// 			uni.showModal({
			// 				title: '提示',
			// 				content: res.msg,
			// 				showCancel: false
			// 			});
			// 		}
			// 	}).catch(err => {
			// 		this.isLoading = false;
			// 		uni.showModal({
			// 			title: '提示',
			// 			content: err.msg,
			// 			showCancel: false
			// 		});
			// 	})
			// },
			toNextHandle() {
				let vehicle = {
					vehicleCode: this.formData.vehicleCode,
					vehicleColor: this.formData.vehicleColor,
					vehicleType: this.vehicleInfo.vehicleType
				}
				//车牌唯一性校验
				this.checkVehicleNo(vehicle, () => {
					this.toNext()
				})
			},
			toNext() {
				if (!this.valid()) return;
				console.log('填写资料数据', this.formData)

				//保存草稿
				this.isLoading = true
				let params = {
					...this.formData,
					vehicleType: this.vehicleInfo.vehicleType
				}
				if (this.oldOwner && this.oldOwner != this.formData.owner) {
					//如果是他人车辆修改后，重置车主信息
					params = {
						...this.vehicleInfo,
						...this.formData
					}
				}

				console.log('this.oldVehicleCode', this.oldVehicleColor, this.oldVehicleCode)
				console.log('this.formData', this.formData.vehicleColor, this.formData.vehicleCode)

				if (this.oldVehicleCode && this.oldVehicleColor && (this.oldVehicleCode != this.formData
						.vehicleCode ||
						this.oldVehicleColor != this.formData.vehicleColor)) {
					//重置车辆信息
					params = {
						...this.vehicleInfo,
						...this.formData
					}
				}

				console.log(params, 'params=====>>>>>>>>>>>>');
				this.$request.post(this.$interfaces.dySaveDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						//没有对比数据时，初始化对比数据，防止后退修改数据时无数据
						this.oldOwner = this.formData.owner
						this.oldVehicleCode = this.formData.vehicleCode
						this.oldVehicleColor = this.formData.vehicleColor

						//下一步操作
						// this.nextFlag = true
						uni.navigateTo({
							url: '/pagesA/newBusiness/douyin/personal/personal'
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			// webview跳转
			preview() {
				let url = 'https://portal.gxetc.com.cn/public-static/file/车辆授权书样例.png';
				previewFile(url)
			},
			downLoad() {
				let url = 'https://portal.gxetc.com.cn/public-static/file/车辆授权书模板.docx'
				downLoadFile(url)
			}
		}
	};
</script>

<style>
	.pla-css {
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
		line-height: 42rpx;
	}
</style>

<style scoped lang="scss">
	.z-index {
		z-index: 999;
	}

	.ios-textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-top: 45rpx;
	}

	.textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-bottom: 38rpx;
	}

	.installation {
		padding-bottom: 180rpx;
		padding-top: 184rpx;
		// height: calc(100% - 184rpx);
	}

	// .installation-mask {
	// 	position: fixed;
	// 	left: 0;
	// 	top: 0;
	// 	right: 0;
	// 	bottom: 0;
	// 	width: 100%;
	// 	height: 100%;
	// 	z-index: 999;
	// 	background: #323435;
	// }
	.car-type_select {
		margin: 20rpx;
		height: 300rpx;
		background: $uni-bg-color;
		border-radius: 12rpx;
		padding: 30rpx;

		.car-wrapper {
			margin-top: 20rpx;

			.car-item {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				height: 180rpx;
				width: 206rpx;
				background: #F7F7F7;
				border-radius: 12rpx;
				margin-right: 16rpx;

				&>image {
					width: 90rpx;
					height: 90rpx;
				}
			}

			.car-item:last-of-type {
				margin-right: 0;
			}

			.selector {
				// background-color: $my-theme-color;
				// color: $uni-bg-color;
				background: #E4EFFF;
				color: #0066E9;
			}
		}
	}

	.car-no {
		margin: 20rpx;
		background-color: $uni-bg-color;
		border-radius: 12rpx;

		.title-container {
			padding: 30rpx;

			/deep/.title {
				font-size: 32rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #323435;
				line-height: 45rpx;
			}
		}
	}

	.plate_color {
		position: relative;
		display: flex;
		flex-wrap: wrap;
		background-color: $uni-bg-color;
		// margin-top: -20rpx;
		padding: 10rpx 20rpx 30rpx 30rpx;
		border-bottom-right-radius: 12rpx;
		border-bottom-left-radius: 12rpx;

		.plateColor {
			width: 202rpx;
			height: 68rpx;
		}

		.more-color {
			line-height: 100rpx;
			// margin-left: 30rpx;
			display: flex;
			justify-content: center;
			flex: 1;
			margin-top: 16rpx;

			&>image {
				width: 40rpx;
				height: 44rpx;
			}
		}

		.select-more {
			position: absolute;
			bottom: -106rpx;
			z-index: 100071;

			.select-more__item {
				position: absolute;

				&:first-child {
					left: 85rpx;
					bottom: 30rpx;
					z-index: 100071;
					// z-index: 20;
				}

				&:last-of-type {
					right: 85rpx;
					bottom: 30rpx;
					z-index: 100071;
					// z-index: 20;
				}

				.select-more__image {
					width: 202rpx;
					height: 68rpx;
				}

				.select-more__text {
					text-align: center;
					color: #ffffff;
				}
			}
		}

		.icon-select__color {
			width: 647rpx;
			height: 209rpx;
		}

		.plateColorList {
			position: relative;
			margin-right: 20rpx;
			margin-bottom: 20rpx;

			&:nth-child(3n) {
				margin-right: 0rpx;
			}

			.plate-color__text {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #888888;
			}

			.checkplateColor {
				position: absolute;
				bottom: 30rpx;
				right: -12rpx;
				width: 40rpx;
				height: 40rpx;
			}
		}


		.otherColor {
			width: 202rpx;
			height: 68rpx;
			background: #ffffff;
			border: 1px dashed #B8B8B8;
			border-radius: 4px;
			line-height: 68rpx;
			text-align: center;
			font-size: 30rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #323435;
		}
	}

	.plate_code {
		// position: relative;
		margin: 20rpx;
		background-color: $uni-bg-color;
		border-radius: 12rpx;

		.title-container {
			padding: 30rpx;
		}

		.so-plate {
			box-sizing: border-box;
			position: absolute;
			bottom: 0;
			width: 100%;
			left: 0;
			background: #fff;
			padding: 25rpx 25rpx 0 25rpx;


			&-body {
				box-sizing: border-box;
				padding: 0rpx 25rpx 30rpx 25rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			&-word {
				border-radius: 7rpx;
				border: 1rpx solid #B8B8B8;
				height: 0rpx;
				margin: 0 5rpx;
				box-sizing: border-box;
				padding-bottom: calc((100% - 118rpx) / 8);
				width: calc((100% - 150rpx) / 8);
				position: relative;

				&.active {
					border-color: #007aff;
					box-shadow: 0 0 15rpx 0 #007aff;
				}

				text {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translateX(-50%) translateY(-50%);
					font-size: 28rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #323435;
				}
			}

			&-dashed {
				// border: 1rpx dashed #ccc;
				border-width: 1rpx;
				border-style: dashed;
				border-color: #B8B8B8;
				padding-bottom: calc((100% - 118rpx) / 8);
				width: calc((100% - 150rpx) / 8);

				&>image {
					margin: 10rpx auto 0 auto;
					width: 18rpx;
					height: 18rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}

			}

			&-font {
				margin-top: 4rpx;
				text-align: center;
				font-size: 18rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #CACACA;
			}

			&-dot {
				width: 15rpx;
				height: 15rpx;
				background: #ccc;
				border-radius: 50%;
				margin: 0 5rpx;
			}
		}
	}

	.owner {
		margin: 20rpx;
		background-color: $uni-bg-color;
		padding-bottom: 10rpx;
		border-radius: 12rpx;

		.title-container {
			padding: 30rpx;
		}

		.single-wrapper {
			display: flex;
			align-items: center;

			.single {
				margin-left: 30rpx;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #0081FF;
				line-height: 33rpx;
			}
		}

		.car-type {

			.car-item {
				position: relative;
				flex: 1;
				margin-left: 24rpx;
				line-height: 70rpx;
				font-size: 32rpx;
				text-align: center;
				border: 2rpx dashed #c7c7c7;
				background: #f7f7f7;
				color: #323435;


				&:first-child {
					margin-left: 30rpx;
				}

				&:last-child {
					margin-right: 30rpx;
				}

				.check-type {
					position: absolute;
					bottom: -10rpx;
					right: -10rpx;
					width: 40rpx;
					height: 40rpx;
				}
			}

			.selector {
				background-color: #E4EFFF;
				// color: #E4EFFF;
				border-width: 2rpx;
				border-color: #009FF6;
			}
		}

		.owner-tips {
			margin: 30rpx 30rpx 20rpx 30rpx;
			padding: 10rpx 20rpx;
			line-height: 40rpx;
			border-radius: 12rpx;
			background-color: rgba(245, 245, 245, 80);
			color: #FF9038;
			font-size: 24rpx;
			font-family: Arial;

			.tips {
				color: rgba(16, 16, 16, 100);
			}
		}
	}

	.install-type {
		margin: 20rpx;
		background-color: #ffffff;
		border-radius: 12rpx;

		.type-wrapper {
			display: flex;
			padding: 0 30rpx 39rpx 30rpx;

			.type-item {
				width: 312rpx;
				height: 78rpx;
				line-height: 78rpx;
				border-radius: 7rpx;
				text-align: center;
				font-size: 32rpx;

				// &:first-child {
				// 	background-color: #f7f7f7;
				// 	margin-right: 26rpx;
				// 	border: 1rpx dashed #e8e8e8;
				// }

				&:last-of-type {
					color: #ffffff;
					background-color: #0066E9;
				}
			}
		}
	}

	.install {
		margin: 20rpx;
		padding: 20rpx 0;
		background-color: $uni-bg-color;
		padding-bottom: 10rpx;
		border-radius: 12rpx;

		.title-container {
			padding: 30rpx;
		}

		.input-wrapper {
			padding: 0 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 80rpx;
			line-height: 40rpx;
			color: rgba(16, 16, 16, 100);
			font-size: 28rpx;
			// text-align: center;
			font-family: Arial;
			background-color: $uni-bg-color;

			.input-title {
				flex: 0 0 180rpx;
				width: 180rpx;
				// text-align: left;
				font-size: 30rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #323435;
			}

			.input {
				flex: 1;
				// width: calc(100% - 180rpx);
				// justify-content: flex-end;
				// text-align: left;
			}

			// .address {
			// 	flex-wrap: wrap;
			// }

			.auto-input {
				position: relative;
			}

			.autofill {
				position: absolute;
				right: 30rpx;
				// background-color: $my-theme-color;
				// color: $uni-bg-color;
				// border-radius: 12rpx;
				height: 120rpx;
				line-height: 120rpx;
				padding: 0 20rpx;
				color: #0081FF;
				z-index: 98;
			}
		}
	}

	.final-tips {
		// color: $my-theme-color;
		text-align: left;
		margin: 30rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		// height: 296rpx;
		padding-bottom: 20rpx;
		line-height: 45rpx;
		background: #F8F8F8;
		border-radius: 8rpx;

		.tips-title {
			padding-top: 20rpx;
			padding-left: 30rpx;
			color: #FF9038;
		}

		.desc {
			line-height: 40rpx;
			padding: 0 30rpx;
		}
	}

	.picker {
		width: calc(100vw - 210rpx);
		flex: 1;
		height: 120rpx;
		display: flex;
		align-items: center;
		color: #777777;
		overflow: hidden;

		// line-height: 120rpx;
		// text-align: center;
	}

	.selected {
		display: flex;
		width: calc(100vw - 180rpx);

		&-item {
			margin-left: 20rpx;

			&:first-child {
				margin-left: 0;
			}
		}

	}

	.selected-item {
		line-height: 40rpx;
		color: rgba(16, 16, 16, 100);
		font-size: 28rpx;
		font-family: Arial;
		// width: calc(100vh - 180rpx);
		width: 100%;
		display: flex;
		align-items: center;
	}

	.error-text {
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #F65B5B;
		line-height: 33rpx;
		padding: 0 30rpx 30rpx 30rpx;
	}

	/deep/.u-mask {
		background-color: transparent !important;
	}


	//地区级联组件样式修改
	/deep/.uni-data-tree {
		width: 100%;

		.input-value-border {
			border: none;
			padding: 0;

			.selected-area {
				font-size: 30rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 42rpx;

				.selected-list {
					padding: 0;
				}

				.selected-item {
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;
					padding-right: 10rpx;

					.input-split-line {
						display: none;
					}
				}
			}

			.arrow-area {
				display: none;
			}
		}

	}
</style>