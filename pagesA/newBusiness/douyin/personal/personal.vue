<template>
	<view class="personal">
		<handle-step type="douyin" :current="2" />
		<view class="tips">
			<text class="tips-text">
				温馨提示：线上申办仅支持个人开通ETC账户
			</text>
		</view>
		<view class="ocr-wrapper">
			<t-title title="请拍照上传办理人证件" setPadding="30">
			</t-title>
			<ocrUpload ref="ocrUpload" :imgList="imgList" @delImg="delImg" @ocr-change="ocrChange()"
				@on-change="onChange()"></ocrUpload>
		</view>
		<view class="weui-form">
			<!-- 			<view class="weui-cells__title">
				请核对OCR识别信息
			</view> -->
			<t-title title="请仔细核对用户信息" setPadding="30"></t-title>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">客户姓名</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input @input="changeInput($event,'customerName')" disabled :value="formData.customerName"
							class="weui-input" placeholder="上传图片识别姓名" />
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">客户类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input disabled :value="'个人'" class="weui-input" placeholder="请输入客户类型" />
					</view>
					<!-- 					<view class="weui-cell__bd weui-cell__primary">
						<input @input="changeInput($event,'customerType')" :value="formData.customerType"
							class="weui-input" placeholder="请输入客户类型" />
					</view> -->
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">手机号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input " @input="changeInput($event,'linkMobile')"
							:value="formData.linkMobile" placeholder="手机号用于开发票"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">证件类型</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<!-- 						<picker style="width:100%;" @change="bindUserPickerChange" :range="personalType"
							range-key="label">
							<view class="weui-picker-value">{{personalType[user_type_index].label}}</view>
						</picker> -->
						<view class="weui-cell__bd weui-cell__primary">
							<input class="weui-input" disabled :value="personalType[user_type_index].label"></input>
						</view>
					</view>
					<!-- 					<view class="weui-cell__ft">
					</view> -->
					<!-- 					<image style="width: 30rpx;height: 28rpx" src="../../static/new-apply/personal/arrow-right.png"
						mode=""></image> -->
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">证件号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input class="weui-input" disabled @input="changeInput($event,'certificatesCode')"
							:value="formData.certificatesCode" placeholder="上传图片识别证件号码" />
					</view>
					<view class="weui-cell__ft">
					</view>

				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">联系地址</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<textarea :disable-default-padding="true" @input="changeInput($event,'linkAddress')"
							:class="[textClass?'ios-textarea':'textarea']" class="weui-input" placeholder="详细至门牌信息"
							v-model="formData.linkAddress" />
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
			</view>
			<view class="tips">
				<text class="tips-text">
					请仔细核对证件信息，如信息有误，请重新拍照上传
				</text>
			</view>
		</view>
		<tButton :buttonList="buttonList" @toNext="toNext" @toLast="toLast"></tButton>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import handleStep from '@/pagesA/components/new-handle-step/new-handle-step.vue';
	import ocrUpload from '@/pagesA/components/ocr-upload/uploadPersonal.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesA/components/new-title/new-title.vue'
	import tButton from '@/pagesA/components/t-button/t-button.vue'
	import {
		personalType
	} from "@/common/systemConstant.js";
	import {
		checkIdCard,
		checkPhone,
		checkPosttal
	} from "@/common/util.js";
	import {
		getCurrUserInfo,
		setCurrUserInfo
	} from "@/common/storageUtil.js";
	export default {
		components: {
			handleStep,
			ocrUpload,
			tLoading,
			tTitle,
			tButton
		},
		data() {
			return {
				isLoading: false,
				textClass: false,
				noticeList: [
					"采集的身份证件信息仅用于车辆认证，不会提供给第三方平台。"
				],
				formData: {
					customerType: "0",
					customerName: "", // 用户名称
					certificatesType: "0", // 证件类型
					certificatesCode: "", // 证件号码
					linkMobile: "", // 手机号码，
					linkAddress: '', //联系人地址
					// registered_type: "1",
					negativeImageUrl: '', //证件背面照图相对路径
					negativeImageUrlCode: '', //证件背面照图相对路径Code
					positiveImageUrl: '', //证件正面照图相对路径
					positiveImageUrlCode: '', //证件正面照图相对路径Code
					negativeImage: '', //md5
					positiveImage: '', //md5
					nodeStep: '3',
					id: ''
				},
				buttonList: [{
					title: '下一步',
					handle: 'toNext'
				}],
				personalType,
				user_type_index: 0,
				redirect_url: '',
				leftImg: false,
				rightImg: false,
				nextFlag: false,
				// nodeStep: '',
				imgList: {
					negativeImageUrl: '', //证件背面照图相对路径
					positiveImageUrl: '', //证件正面照图相对路径
					negativeImage: '', //md5
					positiveImage: '', //md5
				},
			};
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			}
		},
		onLoad() {
			const phoneType = uni.getSystemInfoSync();
			if (phoneType.platform == 'ios') {
				this.textClass = true
			} else if (phoneType.platform == 'android') {
				this.textClass = false
			}
			this.getDraft()
		},
		// onUnload() {
		// 	if (this.nextFlag) return;
		// 	let currentRoutes = getCurrentPages();
		// 	console.log('currentRoutes', currentRoutes)
		// 	let lastRoutes = currentRoutes[currentRoutes.length - 2].route
		// 	if (lastRoutes == 'pagesA/newBusiness/installation/installation') return;
		// 	uni.navigateTo({
		// 		url: '/pagesA/newBusiness/installation/installation'
		// 	})
		// },
		methods: {
			bindUserPickerChange(e) {
				this.user_type_index = e.detail.value;
				this.formData.certificatesType =
					this.personalType[e.detail.value].value || "";
			},
			changeInput(event, data) {
				this.formData[data] = event.detail.value.trim();
			},
			modify() {
				this.show = true
				this.isEdit = true
			},

			ocrChange(encryptedData, imgList) {
				console.log(encryptedData, 'ocr识别');
				console.log(encryptedData.side, 'ocr识别');
				let bizContent = encryptedData.encryptedData
				if (encryptedData.side == 1) {
					if (!bizContent['cardNum'] || !bizContent['realName']) {
						this.$refs.ocrUpload.delImgByRef(encryptedData.side)
						uni.showModal({
							title: "提示",
							content: '无法识别身份证信息，请确认图片是否完整清晰。如果无法识别，请前往网点办理。',
							showCancel: false,
						});
						return
					}
					// this.leftImg = true
					this.formData.certificatesCode = bizContent['cardNum'] || ''
					this.formData.customerName = bizContent['realName'] || ''
				}
				// if (encryptedData.side == 2) {
				// 	this.rightImg = true
				// }

				if (imgList.side == 1) {
					this.formData.positiveImageUrl = imgList.img_url
					this.formData.positiveImage = imgList.md5Code
					//档案改造新增code
					this.formData.positiveImageUrlCode = imgList.code
				}
				if (imgList.side == 2) {
					this.formData.negativeImageUrl = imgList.img_url
					this.formData.negativeImage = imgList.md5Code
					//档案改造新增code
					this.formData.negativeImageUrlCode = imgList.code
				}
			},
			onChange(current, imgList) {
				// if (imgList.side == 1) {
				// 	this.formData.positiveImage = imgList.img_url
				// }
				// if (imgList.side == 2) {
				// 	this.formData.negativeImage = imgList.img_url
				// }
			},


			validate() {
				if (!(this.formData.positiveImage && this.formData.negativeImage)) {
					uni.showModal({
						title: "提示",
						content: "请上传证件照片",
						showCancel: false,
					});
					return false;
				}

				if (!this.formData.customerName) {
					uni.showModal({
						title: "提示",
						content: "请输入用户名称",
						showCancel: false,
					});
					return false;
				}
				if (this.formData.certificatesType == "0") {
					if (!checkIdCard(this.formData.certificatesCode)) {
						uni.showModal({
							title: "提示",
							content: "请输入正确的证件号码",
							showCancel: false,
						});
						return false;
					}
				} else {
					if (!this.formData.certificatesCode) {
						uni.showModal({
							title: "提示",
							content: "请输入证件号码",
							showCancel: false,
						});
						return false;
					}
				}

				if (!this.formData.linkAddress) {
					uni.showModal({
						title: "提示",
						content: "请输入联系地址",
						showCancel: false,
					});
					return false;
				}

				if (!checkPhone(this.formData.linkMobile)) {
					uni.showModal({
						title: "提示",
						content: "请输入正确的手机号码",
						showCancel: false,
					});
					return false;
				}
				return true
			},

			//保存草稿，进行下一步
			toNext() {
				if (this.validate()) {
					let params = JSON.parse(JSON.stringify(this.formData))
					console.log(params, '入参');
					this.isLoading = true
					this.$request.post(this.$interfaces.dySaveDraft, {
						data: params
					}).then(res => {
						if (res.code == 200) {
							this.isLoading = false
							// this.nodeStep = this.formData.nodeStep					
							this.nextFlag = true
							uni.navigateTo({
								url: '/pagesA/newBusiness/douyin/vehicle/vehicle'
							})
						} else {
							this.isLoading = false
							// this.nextFlag = true
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
							});
						}
						console.log(res, '保存个人信息-------------------');
					}).catch(err => {
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: err.msg,
							showCancel: false,
						});
					})
				}
			},

			//获取草稿
			getDraft() {
				this.isLoading = true
				let params = {
					id: this.$store.state.applyId
				}
				this.$request.post(this.$interfaces.dyGetDraft, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						// //获取所有草稿
						console.log('草稿t==>>>>>>', result)
						// this.nodeStep = result.nodeStep
						this.formData.id = result.id || ''
						this.formData.customerType = result.customerType || '0'
						this.formData.customerName = result.customerName || ''
						this.formData.certificatesType = result.certificatesType || '0'
						this.formData.certificatesCode = result.certificatesCode || ''
						this.formData.linkMobile = result.linkMobile || ''
						this.formData.linkAddress = result.linkAddress || ''			
						this.formData.positiveImageUrlCode = result.positiveImageUrlCode || ''
						this.formData.negativeImageUrlCode = result.negativeImageUrlCode || ''
						this.formData.positiveImageUrl = result.positiveImageUrl || ''
						this.formData.negativeImageUrl = result.negativeImageUrl || ''
						this.formData.positiveImage = result.positiveImage || ''
						this.formData.negativeImage = result.negativeImage || ''

						if (this.formData.positiveImage || this.formData.negativeImage) {
							this.imgList = {
								positiveImageUrl: this.formData.positiveImageUrl,
								positiveImage: this.formData.positiveImage,
								negativeImageUrl: this.formData.negativeImageUrl,
								negativeImage: this.formData.negativeImage
							}
						}

						//回填picker
						//车主证件类型
						if (result.certificatesType) {
							this.user_type_index = this.personalType.map(item => item.value).indexOf(result
								.certificatesType)
						}


					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			delImg(type) {
				this.formData[type + 'Url'] = '' //删除照片
				this.formData[type] = '' //删除照片md5
			},
			toLast() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	};
</script>

<style scoped lang="scss">
	.ios-textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-top: 45rpx;
		// text-align: right;
	}

	.textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-bottom: 38rpx;
		// text-align: right;
	}

	.personal {
		padding-bottom: 180rpx;
		padding-top: 184rpx;

		.ocr-wrapper {
			background-color: #FFFFFF;
			margin: 20rpx 20rpx 0 20rpx;
			border-radius: 12rpx;
		}

		.weui-form {
			margin: 20rpx 20rpx 0 20rpx;
			border-radius: 12rpx;

			.tips {
				background: #F8F8F8;
				border-radius: 8rpx;
				font-weight: 400;
				font-size: 28rpx;
				text-align: center;
				margin: 0 30rpx 60rpx 30rpx;

				.tips-text {
					height: 45rpx;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FF9038;
					line-height: 45rpx;
				}
			}
		}

		.weui-cells {
			padding-top: 0;

			&::before {
				border: 0;
			}

			.weui-cell:nth-child(1)::before {
				border: 0;
			}
		}
	}

	.activation-page {
		position: relative;
	}

	.idCard {
		background-color: #f6f6f6;

		.examine {
			position: absolute;
			height: calc(100% - 436rpx);
			background-color: #FFFFFF;
			top: 400rpx;
			width: 100%;
			border-radius: 16rpx 16rpx 0px 0px;

			.examine-content {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-top: 150rpx;

				.text {
					font-size: 32rpx;
					font-weight: 400;
					color: #333333;
					width: 464rpx;
					text-align: center;
					margin-top: 40rpx;
				}
			}
		}

		.idCard-top {
			position: relative;
			z-index: 9;
			padding: 48rpx 30rpx;
			background-color: #fff;
			border-radius: 16rpx 16rpx 0px 0px;

			.title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
			}

			.content {
				display: flex;

				.left,
				.right {
					position: relative;
					margin-top: 40rpx;

					.photo_icon {
						position: absolute;
						top: 55rpx;
						left: 125rpx;
					}

					.text {
						text-align: center;
						font-size: 28rpx;
						color: #333;
						font-weight: 400;
						margin-top: 20rpx;
					}

					.delIcon {
						position: absolute;
						right: 0;
						top: 0;
					}
				}

				.right {
					margin-left: 10rpx;
				}
			}

			.center {
				justify-content: center;
			}
		}

		.idCard-bottom {
			padding: 30rpx 0;
			background-color: #fff;
			margin-top: 20rpx;

			.title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
				margin-bottom: 30rpx;
				margin-left: 30rpx;
			}

			.text_line {
				padding: 0 30rpx;
				height: 100rpx;
				width: 100%;
				font-size: 30rpx;
				color: #999999;
				font-weight: 400;
				line-height: 100rpx;
				border-top: 1px solid #e9e9e9;
				display: flex;
				justify-content: space-between;

				.text {
					display: inline-block;
					width: 250rpx;
					color: #999999;
					font-size: 30rpx;
					font-weight: 400;
				}
			}

			.bottom {
				border-bottom: 1px solid #e9e9e9;
			}
		}

		.btn {
			display: flex;
			justify-content: space-around;
			background-color: #FFFFFF;
			padding: 20rpx;
		}
	}

	.slot-content {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
		flex-direction: column;
		align-items: center;

		.text {
			font-size: 26rpx;
			font-weight: 400;
			color: #666666;
			width: 500rpx;
			text-align: center;
			margin-bottom: 10rpx;
		}

		.phone {
			font-size: 36rpx;
			font-weight: 500;
			color: #333333;
		}
	}

	.tips {
		text-align: center;
		height: 61rpx;
		line-height: 61rpx;
		background: rgba(255, 144, 56, 0.11);

		.tips-text {
			height: 45rpx;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FF9038;
			line-height: 45rpx;
		}
	}
</style>