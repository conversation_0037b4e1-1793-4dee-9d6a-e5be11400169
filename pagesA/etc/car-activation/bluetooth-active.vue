<template>
	<view class="top">
		<form>
			<view class="cu-form-group">
				<view class="title">选择品牌：</view>
				<picker @change="changeBrand" :value="brandIndex" :range="vehicleBrand" range-key="label">
					<view class="uni-input">{{vehicleBrand[brandIndex].label}}</view>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</picker>
			</view>
			<view class="cu-form-group">
				<view class="title">选择车型：</view>
				<picker @change="changeModel" :value="modelIndex" :range="vehicleModel" range-key="vehicleModel">
					<view class="uni-input">{{vehicleModel[modelIndex].vehicleModel}}</view>
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/index/down-ar.png" class="down-ar"></image>
				</picker>
			</view>
		</form>
	</view>
</template>

<script>
	import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue';
	import Api from '@/common/api/index.js';
	import {
		plateColorToColorMap,
		bankCodeToBankNameMap,
		vehicleTypeOptions,
		vehicleColorOptions
	} from '@/common/systemConstant.js';
	import bleapi from '@/common/bluetooth/bleUtil.js';
	import {
		facapi
	} from '@/common/bluetooth/facUtil.js';
	import {
		request,
	} from '@/common/request-1/requestFunc.js'
	import {
		getTokenId,
		getAccountId,
		getCurrentCar,
		getCurrUserInfo,
		getStore
	} from '@/common/storageUtil.js';
	export default {
		props: {
			isConnect: {
				type: Boolean,
				default: false
			}
		},
		components: {
			tLoading,
			TButton
		},
		data() {
			return {
				isLoading: false,
				brandIndex: 0,
				modelIndex: 0,
				vehicleBrand: [],
				vehicleModel: [],
				activeUrl: "",
				isShowLoding: false
			};
		},
		mounted() {
			this.reqCarInfo()
		},
		methods: {
			changeBrand(e) {
				this.brandIndex = e.detail.value
				this.vehicleModel = this.vehicleBrand[this.brandIndex].carTypeList
				this.modelIndex = 0
				this.activeUrl = this.vehicleModel[0].activeUrl
				this.$emit('activeUrl', this.activeUrl)
			},
			changeModel(e) {
				this.modelIndex = e.detail.value
				this.activeUrl = this.vehicleModel[this.modelIndex].activeUrl
				this.$emit('activeUrl', this.activeUrl)
			},
			// 获取车辆类型
			reqCarInfo() {
				
				let data = {
					
					'data': {}
				};
				this.isShowLoding = true
				request(this.$interfaces.issueCaractiveInfo, data, (res) => {
					this.isShowLoding = false;
					let data = res.data;
					this.vehicleBrand = []
					this.vehicleModel = []
					console.log("获取车类型列表", data);
					let list = data.list || []
					for (let obj of list) {
						this.vehicleBrand.push({
							label: obj.vehicleBrand,
							carTypeList: obj.vehicleBrandList
						})
					}
					if (this.vehicleBrand.length > 0) {
						this.vehicleModel = this.vehicleBrand[0].carTypeList
						this.activeUrl = this.vehicleModel[0].activeUrl
						this.$emit('activeUrl', this.activeUrl)
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.top {
		background-color: #fff;
		.cu-form-group {
			position: relative;

			.down-ar {
				position: absolute;
				width: 32rpx;
				height: 32rpx;
				right: 0;
				top: 6rpx;
			}
		}
	}
</style>
