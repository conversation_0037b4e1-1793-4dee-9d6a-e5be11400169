<!-- ETC办理流程 流程——一键激活 -->
<template>
	<view>
		<view class="car-activation">
			<EtcSteps :num="num" />
			<view class="title-nav" v-if="!isSuccess" @click="showActiveHandle">
				查看设备蓝牙激活指导
			</view>
			<view class="content" v-if="!isSuccess && userInfo">
				<form >
					<view class="cu-form-group">
						<view class="title">请核对车主信息</view>
					</view>
					<view class="cu-form-group">
						<view class="title">车主姓名：</view>
						<input name="a" :value="userInfo.ownerName"></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车牌号码：</view>
						<input name="b" :value="userInfo.plateNum"></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车牌颜色：</view>
						<input name="b" :value="plateColorToColorMap.get(userInfo.plateColor+'')"></input>
					</view>
					<view class="cu-form-group">
						<view class="title">座位数：</view>
						<input name="b" :value="userInfo.approvedCount" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车辆识别代码：</view>
						<input name="b" :value="userInfo.vin" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">发动机号：</view>
						<input name="b" :value="userInfo.engineNo" disabled></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车辆尺寸：</view>
						<input name="b" :value="userInfo.outsideDimensions" disabled></input>
					</view>
					<view class="cu-form-group" v-if="userInfo.totalMass">
						<view class="title">总质量（千克）：</view>
						<input name="b" :value="userInfo.totalMass" disabled></input>
					</view>
					<view class="cu-form-group" v-if="userInfo.maintenanceMass">
						<view class="title">整备质量（千克）：</view>
						<input name="b" :value="userInfo.maintenanceMass" disabled></input>
					</view>
					<view class="cu-form-group" v-if="userInfo.permittedWeight">
						<view class="title">额定载质量（千克）：</view>
						<input name="b" :value="userInfo.permittedWeight" disabled></input>
					</view>
					<view class="cu-form-group" v-if="userInfo.axleCount">
						<view class="title">车轴数：</view>
						<input name="b" :value="userInfo.axleCount" @input="handleChangeData($event,9)"></input>
					</view>
					<view class="cu-form-group" v-if="userInfo.wheelCount">
						<view class="title">车轮数：</view>
						<input name="b" :value="userInfo.wheelCount" @input="handleChangeData($event,10)"></input>
					</view>
					<view class="cu-form-group" v-if="userInfo.axleDistance">
						<view class="title">轴距（毫米）：</view>
						<input name="b" :value="userInfo.axleDistance" @input="handleChangeData($event,11)"></input>
					</view>
				</form>
				<view class="sb-active-btn" v-if="!isSuccess">
					<TButton title="连接设备" @clickButton="connect" class="active-btn" />
					<TButton title="一键激活" @clickButton="activeFlag" class="active-btn" :disabled="!isConnect" />
				</view>
			</view>
			<view class="success-content" v-if='isSuccess'>
				<view class="success-text">
					<image src="/static/etc/success.png" class="success-image"></image>
					您的汽车选装ETC已成功激活
				</view>
				<form>
					<view class="cu-form-group">
						<view class="title">车牌号：</view>
						<input name="b" :value="userInfo.plateNum"></input>
					</view>
					<view class="cu-form-group">
						<view class="title">车牌颜色：</view>
						<input name="b" :value="plateColorToColorMap.get(userInfo.plateColor+'')" disabled></input>
					</view>
				</form>
			</view>
			<view class="success-btn" v-if="isSuccess">
				<TButton title="返回首页" @clickButton="goBack" class="active-btn" />
				<TButton title="查看我的车辆" @clickButton="goMycar" class="active-btn" />
			</view>
		</view>
		<view class="cu-load load-modal-car-activation" v-if="connectLoaing">
			<image src="/static/etc/loading.gif" class="link-image-gif" v-if='!isLoading'></image>
			<image src="/static/components/delete.png" class="delete-image" v-if='isLoading' @click="closeLoading"></image>
			<view class="gray-text" v-if='!isLoading && isActive'>激活中，请耐心等待</view>
			<view v-if='!isActive'>
				<view class="gray-text" v-if="!isConnect">设备连接中，请耐心等待</view>
				<view class="gray-text" v-else>连接设备成功</view>
			</view>
		</view>
		<tLoading :isShow="isShowLoding" />
		<neil-modal class="modal" :show="showActive" @confirm="showActiveHandle" confirmText="关闭" :show-cancel="false">
			<view class="dialog">
				<bluetooth-active @activeUrl="getActiveUrl"></bluetooth-active>
				<TButton title="根据车型查看蓝牙连接指导" @clickButton="goPage" class="boll-btn" />
			</view>
		</neil-modal>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>

</template>

<script>
	import TButton from "@/components/t-button.vue"
	import tLoading from '@/components/common/t-loading.vue';
	import EtcSteps from "@/components/etc-steps/etc-steps.vue"
	import Api from '@/common/api/index.js';
	import bluetoothActive from "@/pagesA/etc/car-activation/bluetooth-active"
	import {
		plateColorToColorMap,
		bankCodeToBankNameMap,
		vehicleTypeOptions,
		vehicleColorOptions
	} from '@/common/systemConstant.js';
	import {
		request,
	} from '@/common/request-1/requestFunc.js'
	import bleapi from '@/common/bluetooth/bleUtil.js';
	import {
		facapi
	} from '@/common/bluetooth/facUtil.js';
	import neilModal from '@/components/neil-modal/neil-modal.vue';
	import {
		getTokenId,
		getAccountId,
		getCurrentCar,
		getCurrUserInfo,
		getStore
	} from '@/common/storageUtil.js';
	// 正式，顺序优先
	const newPinCode = '123456';
	// 正式，顺序优先
	const pinCode = '************';
	const terminalNo = '************';
	const money = ********;
	const defaultRand = '********';
	const defaultObuId = '****************';
	var _that;
	//蓝牙服务名前缀
	const SERVICENAME_PREFIX = ['obu', 'etc', 'jl'];
	export default {
		components: {
			EtcSteps,
			TButton,
			tLoading,
			bluetoothActive,
			neilModal
		},
		data() {
			return {
				isBtnLoader: false,
				isLoading: false,
				num: 4,
				isSuccess: false,
				connectLoaing: false,
				isShowLoding: false,
				userInfo: null,
				cpu_id: '', // cpu卡号
				obu_id: '', // obu编号
				cpu_issue_code: '', // cpu发行随机码
				obu_issue_code: '', // obu发行随机码
				issue_times: '', // 发行服务器时间
				cpuStatus: false,
				obuStatus: false,
				bankCodeToBankNameMap,
				vehicleTypeOptions,
				plateColorToColorMap,
				comParams: {
					vehicleId: "",
					accountId: "",
					plateNum: "",
					plateColor: "",
				},
				obuType: "1", // 0双片式 1单片式
				proListNo: "",
				carTypeIndex: 0,
				carTypeList: vehicleColorOptions,
				isConnect: false,
				isActive: false,
				showActive: false,
				activeUrl: ""
			};
		},
		onLoad(option) {
			this.closeBle()
			_that = this;
			console.log("前装车辆", getCurrentCar())
			this.comParams = {
				accountId: getAccountId(),
				vehicleId: getCurrentCar().vehicleId,
				plateNum: getCurrentCar().plateNum,
				plateColor: getCurrentCar().plateColor,
			}
			this.reqCarInfo()
		},
		methods: {
			showActiveHandle() {
				this.showActive = !this.showActive;
			},
			getActiveUrl(url) {
				this.activeUrl = url
			},
			goPage() {
				this.showActive = false
				console.log(this.activeUrl)
				if (!this.activeUrl) {
					uni.showModal({
						title: "提示",
						content: "请选择车辆品牌类型",
						showCancel: false
					})
					return
				}
				uni.navigateTo({
					url: "/pages/uni-webview/h5-webview?ownPath=" + encodeURIComponent(this.activeUrl)
				})
			},
			goBack() {
				uni.reLaunch({
					url: '/pages/home/<USER>/s-home'
				})
			},
			goMycar() {
				uni.reLaunch({
					url: "/pagesB/personal/my-car/s-my-car"
				});
			},
			// 选择车辆类型
			changeCarType(e) {
				this.carTypeIndex = e.detail.value
			},
			closeLoading() {
				this.connectLoaing = false;
				// this.isLoading = false;
			},
			// 连接设备
			connect() {
				this.connectLoaing = true
				this.isBtnLoader = true;
				this.isActive = false
				this.scanDevice((result) => {
					this.isConnect = result
					setTimeout(() => {
						this.connectLoaing = false
						this.isBtnLoader = false;
					}, 1000)
				})
			},
			// 激活
			activeFlag() {
				this.isActive = true
				this.connectLoaing = true
				this.isBtnLoader = true;
				this.connectedCallback(this.isConnect);

				// 30秒连接超时
				setTimeout(() => {
					if (this.isActive) {
						this.disConnect()
						uni.showModal({
							title: "提示",
							content: "连接超时,请重试",
							showCancel: false
						})
					}
				}, 30000)
			},
			// 获取车辆信息
			reqCarInfo() {
				this.isShowLoding = true
				let params = {
					accountId: getAccountId(),
					vehicleId: getCurrentCar().vehicleId
				};
				let data = {
					'data': params
				};

				request(this.$interfaces.userVehicleQuery, data, (res) => {
					this.isShowLoding = false;
					console.log("获取车辆信息...", res.data);
					this.userInfo = res.data
				}, () => {
					this.isShowLoding = false
				}, () => {
					this.isShowLoding = false
				})
			},
			// 显示弹框
			showModal(data) {
				//隐藏loading
				_that.isBtnLoader = false;
				console.log(data.content, 'sdk报错');
				_that.closeLoading()
				//显示弹框
				let obj = { ...data
				}
				obj = data.showMyContent ? obj : { ...data,
					content: '操作失败，请打开手机蓝牙，并确认已按步骤完成激活前准备。请将手机靠近设备后重试。(' + data.devResult.code + ':' + data.devResult
						.err_msg + ')'
				}
				uni.showModal({
					...obj,
					showCancel: false,
					success: () => {}
				});
			},
			// 开启扫描蓝牙设备
			scanDevice(callback) {
				bleapi.ScanDevice(SERVICENAME_PREFIX, (devResult) => {
					console.log('ScanDevice', devResult);
					if (devResult.code != 0) {
						console.log('搜索失败', devResult);
						_that.closeLoading()
						//搜索设备失败
						_that.showModal({
							title: '错误',
							devResult: devResult,
							content: devResult.err_msg,
							showMyContent: true
						});
					} else {
						console.log('搜索到设备:' + devResult + ' ' + devResult.data.device_name);
						facapi.ConnectDevice(
							devResult.data,
							function(onDisconnect) {
								console.log('连接回调：' + onDisconnect);
							},
							function(result) {
								console.log(result, 'result');
								bleapi.StopScanDevice(function(code) {
									console.log('返回数据', code);
								});
								if (result.code == 0) {
									console.log('连接标签设备成功');
									callback(true);
								} else {
									_that.closeLoading()
									_that.showModal({
										title: '错误',
										devResult: result,
										content: '设备连接失败，请将手机靠近设备后重试。',
										showMyContent: true
									});
									callback(false);
								}
							}
						);
					}
				});
			},
			// 连接成功回调
			async connectedCallback(isConnected) {
				console.log('连接成功回调');
				if (isConnected) {
					facapi.OpenCard(devResult => {
						if (devResult.code == 0) {
							facapi.GetCardNo(devResult => {
								if (devResult.code == 0) {
									_that.cpu_id = devResult.data;
									console.log('获取cpuid成功', devResult.data);
									this.openChannel(devResult)
								} else {
									_that.showModal({
										title: '错误',
										devResult: devResult,
										content: '获取卡号失败：' + devResult.code + ':' +
											devResult.err_msg + (devResult.msg ? ':' +
												devResult.msg : '')
									});
									_that.disConnect();
								}
							});
						} else {
							_that.showModal({
								title: '错误',
								devResult: devResult,
								content: '打开卡失败：' + devResult.code + ':' + devResult.err_msg + (
									devResult.msg ? ':' + devResult.msg : '')
							});
							_that.disConnect();
						}
					});
				}
			},
			// 打开OBU通道，读取obu号
			openChannel(devResult) {
				console.log('openChannel的调用');
				if (devResult.code == 0) {
					facapi.OpenChannel(devResult => {
						console.log('sdk的OpenChannel调用');
						if (devResult.code == 0) {
							// 读取设备sn号
							facapi.GetSerialNo(devResult => {
								console.log('getSerialNo==>>', JSON.stringify(devResult));
								_that.setSystemInfo(devResult);
							});
						} else {
							_that.showModal({
								title: '错误',
								devResult: devResult,
								content: '打开Obu失败:' + devResult.code + ':' + devResult.err_msg + (
									devResult.msg ? ':' + devResult.msg : '')
							});
							_that.getSystemInfo(devResult.code, devResult.err_msg + (devResult.msg ? ':' +
								devResult.msg : ''), defaultObuId, defaultRand, result => {});
							_that.disConnect();
						}
					});
				} else {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '卡发行失败:' + devResult.code + ':' + devResult.err_msg + (devResult.msg ? ':' +
							devResult.msg : '')
					});
					_that.disConnect();
				}
			},
			// 获取oub号后进行obu发行前校验
			setSystemInfo(devResult) {
				console.log('|||setSystemInfo==>', JSON.stringify(devResult));
				if (devResult.code == 0) {
					console.log('获取obuid成功', devResult.data);
					this.obu_id = devResult.data
					// obu发行前校验
					this.obuCheck((res) => {
						if (res) {
							// obu已经发行成功，卡发失败，直接发卡
							if (res.isIssue == 1) {
								// 卡发行前校验
								this.cardCheck((data) => {
									console.log("卡发行前校验成功", data)
									if (data) {
										_that.set0016()
									} else {
										this.disConnect()
									}
								})
							} else {
								_that.opuReaday(devResult);
							}
						} else {
							this.disConnect()
						}
					})
				} else {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '获取Obu编号失败:' + devResult.code + ':' + devResult.err_msg + (devResult.msg ? ':' +
							devResult.msg : '')
					});
					_that.disConnect();
				}
			},
			// obu发行准备完成，准备写卡写签
			opuReaday(devResult) {
				if (devResult.code == 0) {
					console.log('obu发行准备完成', devResult);
					facapi.SetSystemInfo(
						(rand, callback) => {
							console.log('setSystemInfo.getMac', JSON.stringify(devResult));
							_that.getSystemInfo(this.obu_id, devResult.err_msg, rand, result => {
								callback(result);
							});
						},
						devResult => {
							console.log('写系统信息结果', devResult);
							_that.setVehicleInfo(devResult);
						});
				} else {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '圈存失败：' + devResult.code + ':' + devResult.err_msg + (devResult.msg ? ':' +
							devResult.msg : '')
					});
					_that.disConnect();
				}
			},
			// 写车辆信息
			setVehicleInfo(devResult) {
				if (devResult.code == 0) {
					facapi.SetVehicleInfo(
						(rand, callback) => {
							_that.getVehicleInfo(devResult.code, devResult.err_msg, rand, result => {
								callback(result);
							});
						},
						devResult => {
							console.log('setVehicleInfo：写车辆信息结果：', devResult);
							_that.obuResult(devResult);
						}
					);
				} else {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '写Obu系统信息失败:' + devResult.code + ':' + devResult.err_msg + (devResult.msg ?
							':' + devResult.msg : '')
					});
					_that.disConnect();
				}
			},
			// obu发行完成业务处理结果上报
			obuResult(devResult) {
				if (devResult.code != 0) {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '写Obu车辆信息失败:' + devResult.code + ':' + devResult.err_msg + (devResult.msg ?
							':' + devResult.msg : '')
					});
					_that.disConnect();
				} else {
					// obu激活成功上报，准备发卡
					this.obuActiveSuccess((res) => {
						if (res) {
							// 卡发行前校验
							this.cardCheck((data) => {
								console.log("卡发行前校验成功", data)
								if (data) {
									_that.set0016()
								} else {
									this.disConnect()
								}
							})
						} else {
							uni.showModal({
								title: '提示',
								content: 'obu激活成功上报失败',
								showCancel: false
							});
							this.disConnect()
						}
					})
				}
			},
			// 写0016
			set0016() {
				facapi.SetCardFile0016(
					(rand, callback) => {
						_that.getCpu0016WriteFile(rand, result => {
							console.log('|||=0016writeData=>', result);
							callback(result);
						});
					},
					devResult => {
						//写0016文件
						console.log('写0016文件结果', devResult);
						_that.setOO15(devResult);
						// _that.getBalance(devResult)
					}
				);
			},
			// 写0015
			setOO15(devResult) {
				console.log("setOO15-devResult=>", devResult);
				if (devResult.code == 0) {
					facapi.SetCardFile0015(
						(rand, callback) => {
							_that.getCpu0015WriteFile(rand, devResult => {
								console.log('0015writeData', devResult);
								callback(devResult);
							});
						},
						devResult => {
							//写0015文件
							console.log('写0015文件', devResult);
							//读卡内余额
							_that.getBalance(devResult);

						}
					);
				} else {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '写0016文件失败：' + devResult.code + ':' + devResult.err_msg + (devResult.msg ? ':' +
							devResult.msg : '')
					});
					_that.disConnect();
				}
			},
			// 获取卡内余额
			getBalance(devResult) {
				if (devResult.code == 0) {
					facapi.CpuCommand('805c000204', devResult => {
						//获取余额成功
						if (devResult.code == 0) {
							console.log('balance-devResult', devResult);
							if (devResult.data && devResult.data.length == 8) {
								let balance = parseInt(devResult.data, 16);
								console.log('圈存余额balance', balance);
								if (balance == 0) {
									//如果卡内余额为0，调用初始化圈存
									_that.initLoad(devResult);
								} else {
									//如果卡内余额不为0，调用发卡完成接口
									this.cardSuccess((res) => {
										if (res) {
											uni.showModal({
												title: '提示',
												content: '发行成功',
												showCancel: false
											});
											this.isSuccess = true
										} else {
											uni.showModal({
												title: '提示',
												content: '发卡成功上报失败',
												showCancel: false
											});
										}
										_that.closeLoading()
										_that.disConnect();
									})
								}
							} else {
								_that.showModal({
									title: '错误',
									devResult: {
										code: '11013',
										err_msg: '获取卡余额失败：长度不符'
									},
									content: '获取卡余额失败：长度不符'
								});
								_that.cpuComplete({
									code: '11013',
									err_msg: '获取卡余额失败：长度不符'
								});
							}
						} else {
							console.log(devResult, 'devResultdevResultdevResult');
							_that.showModal({
								title: '错误',
								devResult: devResult,
								content: '获取卡余额失败：' + devResult.code + ':' + devResult.err_msg + (
									devResult.msg ? ':' + devResult.msg : '')
							});
							_that.disConnect();
						}
					});
				}
			},
			// 圈存
			initLoad(devResult) {
				this.issue_times = this.formatDate(new Date())
				if (devResult.code == 0) {
					console.log('初始化圈存', pinCode, money, terminalNo);
					facapi.InitLoad(
						pinCode,
						money,
						this.issue_times,
						terminalNo,
						(rand, trade_no, mac1, balance, callback) => {
							console.log('initLoad:getMac', rand, trade_no, mac1, balance);
							_that.getInitLoad(rand, trade_no, balance, mac1, result => {
								callback(result);
							});
						},
						nextDevResult => {
							//初始化圈存完成成功
							//读卡内余额
							console.log('初始化圈存结果', nextDevResult);
							if (nextDevResult.code != 0) {
								_that.showModal({
									title: '错误',
									devResult: nextDevResult,
									content: '圈存初始化失败：' + nextDevResult.code
								});
								_that.disConnect();
							} else {
								//圈存成功，调用发卡完成接口
								this.cardSuccess((res) => {
									if (res) {
										uni.showModal({
											title: '提示',
											content: '发行成功',
											showCancel: false
										});
										this.isSuccess = true
									} else {
										uni.showModal({
											title: '提示',
											content: '发卡成功上报失败',
											showCancel: false
										});
									}
									_that.disConnect();
								})
							}
						}
					);
				} else {
					_that.showModal({
						title: '错误',
						devResult: devResult,
						content: '写0015文件失败：' + devResult.code + ':' + devResult.err_msg + (devResult.msg ? ':' +
							devResult.msg : '')
					});
					_that.disConnect();
				}
			},
			// OBU写系统信息
			async getSystemInfo(obu_id, errMsg, rand, callback) {
				console.log(this.proListNo, 'OBU写系统信息------')
				let data = {
					obuId: this.obu_id,
					rand: rand,
					proListNo: this.proListNo,
					activeFlag: "01"
				}
				Object.assign(data, this.comParams);
				let params = {
					data: data
				};
				console.log('OBU写系统信息', JSON.stringify(params));
				await request(this.$interfaces.issueOBUISSUEMFEF01, params, res => {
					console.log('系统信息res', res);
					callback({
						code: 0,
						err_msg: '成功',
						data: res.data.command
					});
				}, error => {
					console.log('error', error);
					callback({
						code: 11001,
						err_msg: error
					});
				});
			},
			// 写车辆信息
			async getVehicleInfo(errCode, errMsg, rand, callback) {
				let data = {
					obuId: this.obu_id,
					rand: rand,
					proListNo: this.proListNo,
				}
				Object.assign(data, this.comParams);
				let params = {
					data: data
				};
				console.log('写车辆信息参数', JSON.stringify(params));
				await request(this.$interfaces.issueOBUISSUEDFEF01, params, res => {
					console.log('车辆信息res', res);
					callback({
						code: 0,
						err_msg: '成功',
						data: res.data.command
					});
				}, error => {
					console.log('error', error);
					callback({
						code: 11000,
						err_msg: error
					});
				});
			},
			// 卡发行前校验
			async cardCheck(callback) {
				let data = {
					cardId: this.cpu_id
				}
				Object.assign(data, this.comParams);
				let params = {
					data: data

				};
				console.log('卡发行前校验', params)
				await request(this.$interfaces.issueCPUISSUED, params, res => {
					console.log('卡发行前校验-res', res);
					this.proListNo = res.data.proListNo
					callback(res.data)
				}, error => {
					console.log('error', error);
					callback(false)
				});
			},
			// obu发行前校验
			async obuCheck(callback) {
				let data = {
					obuId: this.obu_id,
					obuType: this.obuType,
					type: "1"
				}
				Object.assign(data, this.comParams);
				let params = {
					data: data
				};
				console.log('obu发行前校验', params)
				await request(this.$interfaces.issueOBUIssue, params, res => {
					console.log('obu发行前校验-res', res.data);
					this.proListNo = res.data.proListNo
					callback(res.data)
				}, error => {
					console.log('error', error);
					callback(false)
				});
			},
			// 卡发行成功
			async cardSuccess(callback) {
				let data = {
					rand: "123456",
					accountId: this.comParams.accountId,
					vehicleId: this.comParams.vehicleId,
					plateNum: this.comParams.plateNum,
					plateColor: this.comParams.plateColor,
					proListNo: this.proListNo,
					cardId: this.cpu_id
				}
				let params = {
					data: data
				};
				console.log('卡发行成功入参', params)
				await request(this.$interfaces.issueCPUISSUERESULT, params, res => {
					console.log('卡发行成功-res', res);
					callback(res.resultCode=='0')
				}, error => {
					console.log('error', error);
					callback(false)
				});
			},
			// obu激活成功
			async obuActiveSuccess(callback) {
				let data = {

					accountId: this.comParams.accountId,
					vehicleId: this.comParams.vehicleId,
					plateNum: this.comParams.plateNum,
					plateColor: this.comParams.plateColor,
					proListNo: this.proListNo,
					obuId: this.obu_id,
					obuType: this.obuType,
					type: "1",
					code: "00"
				}
				let params = {
					data: data
				};
				console.log('obu激活成功入参', params)
				await request(this.$interfaces.issueOBUISSUERESULT, params, res => {
					console.log('obu激活成功-res', res);
					callback(res.resultCode =='0')
				}, error => {
					console.log('error', error);
					callback(false)
				});
			},
			// 圈存
			async getInitLoad(rand, tradeNo, balance, mac1, callback) {
				let data = {
					rand: rand,
					money: money,
					terminalId: terminalNo,
					tradeNo: tradeNo,
					time: this.issue_times,
					mac1: mac1,
					balance,
					proListNo: this.proListNo,
					cardId: this.cpu_id
				}
				Object.assign(data, this.comParams);
				let params = {
					data: data
				};
				console.log('初始化圈存入参', params);
				await request(this.$interfaces.issueCPULoadApply, params, res => {
					console.log('初始化圈存-res', res);
					callback({
						code: 0,
						err_msg: '成功',
						data: res.mac2
					});
				}, error => {
					console.log('error', error);
					callback({
						code: 11003,
						err_msg: error.message
					});
				});
			},
			// 0015
			async getCpu0015WriteFile(rand, callback) {
				let data = {
					rand: rand,
					cardId: this.cpu_id,
					proListNo: this.proListNo
				}
				Object.assign(data, this.comParams)
				let params = {
					data: data
				}
				console.log('写0015文件入参', params)
				await request(this.$interfaces.issueWRITE0015, params, (res) => {
					console.log('0015-res', res);
					callback({
						code: 0,
						err_msg: '成功',
						data: res.data.command
					});
				}, error => {
					callback({
						code: 11006,
						err_msg: error
					});
				});
			},
			// 0016
			async getCpu0016WriteFile(rand, callback) {
				let data = {
					rand: rand,
					cardId: this.cpu_id,
					proListNo: this.proListNo
				}
				Object.assign(data, this.comParams)
				let params = {
					data: data
				}
				console.log('写0016文件入参', params)
				await request(this.$interfaces.issueWRITE0016, params, res => {
					console.log('0016文件-res', res);
					callback({
						code: 0,
						err_msg: '成功',
						data: res.data.command
					});
				}, error => {
					console.log('error', error);
					callback({
						code: 11005,
						err_msg: error
					});
				});
			},
			// 中断或结束断开连接关闭蓝牙
			disConnect() {
				this.closeLoading()
				this.isActive = false
				this.isConnect = false
				this.isBtnLoader = false;
				facapi.facSdk.DisconnectDevice(function(code) {
					console.log('关闭连接结果', code);
				});
				// 完成后关闭蓝牙模块
				bleapi.CloseBle((obj) => {
					console.log(obj)
				})
			},
			closeBle() {
				// 关闭蓝牙模块，防止中途断开，连接不上
				bleapi.CloseBle((obj) => {
					console.log(obj)
				})
			},
			// 圈存存入当前时间
			formatDate(now) {
				var year = now.getFullYear();
				var month = now.getMonth() + 1 < 10 ? '0' + (now.getMonth() + 1) : now.getMonth() + 1;
				var date = now.getDate() < 10 ? '0' + now.getDate() : now.getDate();
				var hour = now.getHours() < 10 ? '0' + now.getHours() : now.getHours();
				var minute = now.getMinutes() < 10 ? '0' + now.getMinutes() : now.getMinutes();
				var second = now.getSeconds() < 10 ? '0' + now.getSeconds() : now.getSeconds();
				return year + "" + month + "" + date + "" + hour + "" + minute + "" + second;
			}
		}
	};
</script>

<style scoped lang="less">
	.nav {
		/deep/.navigation-bar {
			background-color: #fff;
		}

		/deep/.navigation-bar-title {
			font-size: 26rpx !important;
			// font-weight: normal !important;
			// margin-top:54rpx !important;
		}

		/deep/.custom-icon {
			width: 32rpx !important;
			height: 32rpx !important;
			position: absolute;
			top: 19rpx;
			right: 18rpx;
		}
	}

	.load-modal-car-activation {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 140rpx;
		left: 0;
		margin: auto;
		width: 350rpx;
		height: 350rpx;
		background-color: var(--white);
		border-radius: 10rpx;
		box-shadow: 0 0 0rpx 2000rpx rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		font-size: 28rpx;
		z-index: 9999;
		align-items: center;
		flex-direction: column;

		.link-image {
			width: 200rpx;
			height: 200rpx;
		}

		.link-image-gif {
			width: 120rpx;
			height: 120rpx;
			margin-bottom: 20rpx;
		}

		.delete-image {
			width: 20rpx;
			height: 20rpx;
			position: absolute;
			right: 20rpx;
			top: 20rpx;
		}
	}

	.car-activation {
		.title-nav {
			height: 70rpx;
			color: #1D82D2;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			padding-right: 20rpx;
		}

		.content {
			// margin-top:30rpx;
		}

		.success-btn,
		.sb-active-btn {
			display: flex;
			justify-content: center;
			margin-top: 20rpx;
			justify-content: space-around;

			.active-btn {
				/deep/.cu-btn {
					width: 328rpx !important;
					height: 64rpx;
					border-radius: 8rpx;
					font-size: 28rpx;
				}
			}
		}

		.sb-active-btn {
			margin-top: 0rpx;
		}

		.success-content {
			background-color: #fff;
			display: flex;
			flex-direction: column;

			.success-text {
				font-size: 32rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #F8F8F8;
				padding: 40rpx 0;
			}

			.success-image {
				width: 60rpx;
				height: 60rpx;
			}
		}
	}

	.modal {
		/deep/.neil-modal {
			.neil-modal__header {
				text {
					color: #000000 !important;
					background-color: #fff !important;
				}
			}

			.neil-modal__footer-left,
			.neil-modal__footer-right {
				color: #47A8EE !important;
				background-color: #FFF;
				border: 1px solid #1D82D2;
				border-radius: 10rpx;
				height: 70rpx;
				line-height: 70rpx;
			}

			.neil-modal__footer-right {
				color: #fff !important;
				background-color: #1D82D2;
				border: 1px solid#1D82D2;
			}
		}

		.dialog {
			.boll-btn {
				position: relative;

				/deep/.cu-btn {
					position: relative;
					font-size: 30rpx;
					height: 80rpx;
				}
			}
		}
	}
</style>
