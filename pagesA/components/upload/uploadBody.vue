<template>
	<view class="ocr">
		<!-- 		<view class="weui-cells__title">
			请上传车辆行驶证
		</view> -->
		<view class="orc-form">
			<view class="upload" v-for="(item,index) in fileList" :key='index'>
				<view class="upload-box" @tap="ViewImage(item.file_url)" v-if="item.file_url !=''">
					<image :src="item.file_url" class="upload-box__img" mode='aspectFilt'></image>
					<view class="cu-tag bg-brown upload-box__close" @tap.stop="DelImg(item)">
						<text class='cuIcon-close close'></text>
					</view>
				</view>
				<view v-if="!item.file_url" class="upload-box" @tap="ChooseImage(item)">
					<image :src="item.url" class="upload-box__img"></image>
				</view>
				<view class="upload__tip">{{item.label}}</view>
			</view>
		</view>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>

</template>

<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import {
		getCurrentCar,
		getCurrUserInfo,
		getTokenId,
		getStore,
		getBusinessTypes,
		getAccountId
	} from '@/common/storageUtil.js'
	import tLoading from '@/components/common/t-loading.vue';
	export default {
		components: {
			cpimg,
			tLoading
		},
		props: {
			sourceType: {
				type: Number,
				default: 0
			},
			vehicle: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {
				isLoading: false,
				ownFlag: '-1',
				returnData: {},
				comParams: {
					customer_id: "",
					vehicle_code: "",
					vehicle_color: ""
				},
				fileList: [{
					photo_code: "16",
					label: "车身照片",
					file_url: '',
					file_serial: '',
					url: require('../../static/vehicle_body.png')
				}],
				sides: 1,
			}
		},
		watch: {
			vehicle(val) {
				if (val) {
					this.init()
				}

			}
		},
		computed: {

		},
		created() {
			this.init()
		},
		methods: {
			init() {
				this.comParams.customer_id = this.vehicle.customer_id
				this.comParams.vehicle_code = this.vehicle.vehicle_code
				this.comParams.vehicle_color = this.vehicle.vehicle_color
			},
			ChooseImage(data) {
				this.sides = data.photo_code;
				this.$refs.cpimg._changImg(this.sourceType);
			},
			//图片压缩成功
			cpimgOk(file) {
				// if(process.env.NODE_ENV === "development"){
				// 	this.unloadHandle(file);
				// 	return;
				// }
				// this.sendOCR(file);
				this.unloadHandle(file);
			},
			unloadHandle(file) {
				this.sendUploadFile(file);
			},
			sendUploadFile(file) {
				let base64Img = file.toString()
				let current = {};
				var imgStr = base64Img.split(';')[1].split(",")[1] + '';
				for (let i = 0; i < this.fileList.length; i++) {
					if (this.fileList[i].photo_code == this.sides) {
						current = this.fileList[i];
						break;
					}
				}
				let biz_content = {
					photo_code: current.photo_code,
					scene: 5,
					...this.comParams
				}
				let params = {
					file_content: base64Img,
					method_code: "2", // 档案上传
					biz_content: JSON.stringify(biz_content),
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.uploadFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let item = {};
						for (let i = 0; i < this.fileList.length; i++) {
							if (this.fileList[i].photo_code == res.data.photo_code) {
								this.fileList[i].file_url = res.data.file_url
								this.fileList[i].file_serial = res.data.file_serial
							}
						}
						this.$emit('on-change', res.data);
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e)
			},
			ViewImage(path) {
				let newArr = [];
				newArr.push(path)
				uni.previewImage({
					urls: newArr
				});
			},
			DelImg(data) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							let current = {}
							for (let i = 0; i < this.fileList.length; i++) {
								if (this.fileList[i].photo_code == data.photo_code) {
									this.fileList[i].file_url = '';
									this.fileList[i].file_serial = '';
									this.current = this.fileList[i]
								}
							}
							this.$emit('on-change', this.current)
						}
					}
				})
			},
			delImgByRef(data) {
				let current = {}
				for (let i = 0; i < this.fileList.length; i++) {
					if (this.fileList[i].photo_code == data.photo_code) {
						this.fileList[i].file_url = '';
						this.fileList[i].file_serial = '';
						this.current = this.fileList[i]
					}
				}
				this.$emit('on-change', this.current)
			}
		}
	}
</script>

<style scoped lang="scss">
	$uploadWidth: 311rpx;
	$uploadHeight: 183rpx;

	.ocr {
		background-color: #FFFFFF
	}

	.orc-form {
		padding: 0 15rpx 30rpx 30rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		background-color: #FFFFFF;
	}

	.upload {
		width: $uploadWidth;
	}

	.upload:first-child {
		margin-right: 10rpx;
	}

	.upload .upload__tip {
		font-size: 28rpx;
		text-align: center;
		width: 100%;
		color: #333333;
		font-weight: 400;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.upload .upload-box {
		width: $uploadWidth;
		height: $uploadHeight;
		position: relative;
	}

	.upload .upload-box .upload-box__img {
		width: $uploadWidth;
		height: $uploadHeight;
	}

	.upload .upload-box .upload-box__close {
		position: absolute;
		right: 0;
		top: 0;
		padding: 0 10rpx;
		font-size: 36rpx;
	}

	.upload .upload-box .upload-box__close .close {
		font-size: 36rpx;
	}
</style>