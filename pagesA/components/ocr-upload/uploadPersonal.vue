<!-- OCR识别档案上传 -->
<template>
	<view class="ocr">
		<!-- 		<view class="weui-cells__title">
			请拍照上传本人身份证
		</view> -->
		<!-- 		<t-title :title="title" setPadding="30">
			<template>
				<view class="single-wrapper">
					<view class="single">
						授权书填写样例
					</view>
					<view class="single">
						下载模板
					</view>
				</view>
			</template>
		</t-title> -->
		<view class="orc-form">
			<view class="upload" v-for="(item, index) in fileList" :key="index">
				<template v-if="item.isUpload">
					<view class="upload-box" @tap="ViewImage(item)" v-if="item.md5Code">
						<image :src="item.url" class="upload-box__img" mode="aspectFilt"></image>
						<view v-if="item.md5Code" class="cu-tag bg-brown upload-box__close" @tap.stop="DelImg(item)">
							<text class="cuIcon-close close"></text>
						</view>
					</view>
					<view v-if="!item.md5Code" class="upload-box" @tap="ChooseImage(item)">
						<image :src="item.url" class="upload-box__img"></image>
					</view>
					<view class="upload__tip">{{ item.label }}</view>
				</template>
			</view>
		</view>
		<neil-modal @confirm="methodEnsure" :show="alltoastData.showFlag" :align="alltoastData.allign"
			:title="alltoastData.title" :content="alltoastData.msg" :confirmText="alltoastData.btnTextEnsure"
			:show-cancel="alltoastData.btnTextCloseFlag">
		</neil-modal>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import tTitle from '@/pagesA/components/new-title/new-title.vue'
	import {
		setCurrentCar,
		getLoginUserInfo
	} from '@/common/storageUtil.js'

	import neilModal from '@/components/neil-modal/neil-modal.vue';
	import tLoading from '@/components/common/t-loading.vue';

	export default {
		components: {
			cpimg,
			neilModal,
			tLoading,
			tTitle
		},
		props: {
			carType: {
				type: Number,
				default: 1
			},
			sourceType: {
				type: Number,
				default: 0
			},
			showOne: {
				type: Boolean,
				default: false
			},
			type: {
				type: Number,
				default: 1
			},
			title: {
				type: String,
				default: '请拍照上传办理人证件'
			},
			imgList: {
				type: Object
			},
			imgOwnerList: {
				type: Object
			},
			uploadType: {
				type: String,
				default: 'ocr'
			}
		},
		watch: {
			imgList(img) {
				console.log('imgList===>>>', img)

				if (img.positiveImage) {
					this.fileList[0].url = img.positiveImageUrl
					this.fileList[0].md5Code = img.positiveImage
				}

				if (img.negativeImage) {
					this.fileList[1].url = img.negativeImageUrl
					this.fileList[1].md5Code = img.negativeImage
				}

				if (img.authLetterImg) {
					this.fileList[2].url = img.authLetterImgUrl
					this.fileList[2].md5Code = img.authLetterImg
				}
			},
		},
		data() {
			return {
				showLoad: false,
				isLoading: false,
				ownFlag: 0,
				sides: '',
				// customer_id: '',
				fileList: [{
						photo_code: "1",
						// base64img: '',
						// photo_code: "3",
						// label: "行驶证正页",
						// url: "../../../static/toc/vehicle_front.png",
						ocr_type: 2,
						label: "身份证人像面",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png",
						md5Code: '',
						origin_img: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png',
						photo_type: 'positiveImage',
						isUpload: true,
					},
					{
						photo_code: "2",
						// base64img: '',
						ocr_type: 3,
						label: "身份证国徽面",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png",
						origin_img: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png",
						md5Code: '',
						photo_type: 'negativeImage',
						isUpload: true,
					},
					{
						photo_code: "5",
						md5Code: '',
						label: "车辆授权书",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unload_bg_img.png",
						origin_img: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unload_bg_img.png",
						photo_type: 'authLetterImg',
						isUpload: false,
						isChoose: true,
					},
				]
			}

		},
		created() {
			// this.customer_id = getLoginUserInfo().userNo;
			if (this.imgOwnerList && Object.keys(this.imgOwnerList).length > 0) {
				this.setImgOwnerList(this.imgOwnerList)
			}
			console.log('this.uploadType', this.uploadType)
			if (this.uploadType == 'normal') {
				this.fileList[2].isUpload = true
				console.log('this.fileList', this.fileList)
			}
		},
		methods: {
			setImgOwnerList(img) {
				console.log('设置车主信息===>>>', img)

				if (img.positiveImage) {
					this.fileList[0].url = img.positiveImageUrl
					this.fileList[0].md5Code = img.positiveImage
				}

				if (img.negativeImage) {
					this.fileList[1].url = img.negativeImageUrl
					this.fileList[1].md5Code = img.negativeImage
				}

				if (img.authLetterImg) {
					this.fileList[2].url = img.authLetterImgUrl
					this.fileList[2].md5Code = img.authLetterImg
				}
			},
			ChooseImage(data) {
				this.sides = data.photo_code;
				this.$refs.cpimg._changImg(this.sourceType);
			},
			////图片压缩成功
			cpimgOk(file) {
				this.isLoading = true;
				if (this.sides == '1' || this.sides == '2') {
					//身份信息去ocr
					this.sendOCR(file);
				}
				if (this.sides == '5') {
					let base64Img = file.toString()
					//车辆授权书直接上传
					this.sendUploadFile(base64Img, {
						side: this.sides
					})
				}

			},
			// ocr识别
			sendOCR(file) {
				let base64Img = file.toString()
				let ocr_type = '';
				let current = {};
				var imgStr = base64Img.split(';')[1].split(",")[1] + '';
				for (let i = 0; i < this.fileList.length; i++) {
					if (this.fileList[i].photo_code == this.sides) {
						current = this.fileList[i];
					}
				}
				// this.$set(current, 'base64img', base64Img)

				let biz_content = {
					ocr_type: current.ocr_type,
					file_name: 'file_name',
				}

				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						let result = res.data;
						let encryptedData = result.encryptedData;
						this.sendUploadFile(base64Img, result)
						// this.$emit("ocr-change", result);
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			sendUploadFile(fileParams, result) {
				// let current = fileParams ? JSON.parse(JSON.stringify(fileParams)) : {}
				// let biz_content = {
				// 	customer_id: this.customer_id,
				// 	photo_code: current.photo_code,
				// 	scene: 9, // C端档案上传
				// };
				console.log('side==>>>>>>>>', result.side)
				let params = {
					image: fileParams,
					// method_code: "2", // 档案上传
					// biz_content: JSON.stringify(biz_content),
				};
				// this.isLoading = true;
				this.$request.post(this.$interfaces.archivesUpload, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('====>>>', res)
						//当前页面图片显示
						for (let obj of this.fileList) {
							// console.log('obj进来===>>>>>>>>>>>', obj['photo_code'])
							if (obj['photo_code'] == result.side) {
								// console.log('side进来===>>>>>>>>>>>')
								obj.url = res.data.data.fileUrl
								obj.md5Code = res.data.data.md5Code
							}
						}

						console.log('图片显示==>>>>>>>>>>>', this.fileList)
						let imgList = {
							side: result.side,
							img_url: res.data.data.fileUrl,
							md5Code: res.data.data.md5Code,
							code: res.data.data.code
						}
						this.$emit('ocr-change', result, imgList)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e);
			},
			//图片预览
			ViewImage(path) {
				if (!path.md5Code) return
				let newArr = [];
				newArr.push(path.url);
				uni.previewImage({
					urls: newArr
				});
			},
			// 删除图片
			DelImg(data) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							// 	let current = {};
							// 	for (let i = 0; i < this.fileList.length; i++) {
							// 		if (this.fileList[i].photo_code == data.photo_code) {
							// 			this.fileList[i].base64img = '';
							// 			current = this.fileList[i];
							// 		}
							// 	}
							// 	this.$set(current, 'file_serial', '');
							console.log('data', data)
							data.url = data.origin_img;
							data.md5Code = '';
							console.log('data', data)
							this.$emit('delImg', data.photo_type)
						}
					}
				});
			},
			delImgByRef(side) {
				console.log('父调用子', side)
				for (let obj of this.fileList) {
					// console.log('obj进来===>>>>>>>>>>>', obj['photo_code'])
					if (obj['photo_code'] == side) {
						// console.log('side进来===>>>>>>>>>>>')
						obj.url = obj.origin_img
						obj.md5Code = ""
					}
				}

			}
		}
	}
</script>

<style scoped lang="scss">
	$uploadWidth: 321rpx;
	$uploadHeight: 183rpx;

	.ocr {
		background-color: #FFFFFF;
		border-radius: 12rpx;
	}

	.title-container {
		padding: 30rpx;
	}

	.orc-form {
		padding: 0 30rpx 30rpx 30rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		background-color: #FFFFFF;
		border-bottom-left-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}

	.upload {
		width: $uploadWidth;
	}

	.upload .upload__tip {
		font-size: 28rpx;
		text-align: center;
		width: 100%;
		color: #333333;
		font-weight: 400;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.upload .upload-box {
		width: $uploadWidth;
		height: $uploadHeight;
		position: relative;
	}

	.upload .upload-box .upload-box__img {
		width: $uploadWidth;
		height: $uploadHeight;
	}

	.upload .upload-box .upload-box__close {
		position: absolute;
		right: 0;
		top: 0;
		padding: 0 10rpx;
		font-size: 36rpx;
	}

	.upload .upload-box .upload-box__close .close {
		font-size: 36rpx;
	}
</style>