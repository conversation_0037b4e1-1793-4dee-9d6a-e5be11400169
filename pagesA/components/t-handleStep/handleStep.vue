<template>
	<view class="handleStep">
		<view class="steps">
			<view class="step" v-for="(item,index) in stepList" :key='index'
				:class="{'is-finish':index<current,'is-process':index==current}">
				<view class="step__hd">
					<view class="step__icon">
						<view class="icon-bg"></view>
						<image v-if="index<current" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/check_icon.png" mode="aspectFilt"
							style="width: 22rpx;height: 22rpx;"></image>
						<view class="icon">

						</view>
					</view>
					<view class="step__line"></view>
				</view>
				<view class="step__bd">
					<view class="step__title">{{item.label}}</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		props: {
			current: {
				type: Number,
				default: 0
			},
			stepList: {
				type: Array
			}
		},
		data() {
			return {
				active: 0,
			}
		},
		computed: {

		},
		onLoad() {},
		methods: {
			getClass(index) {
				return '1111';
			}
		}
	}
</script>

<style scoped lang="scss">
	.handleStep {
		background-color: #0066E9;
		width: 100%;
		padding-top: 48rpx;
		padding-bottom: 30rpx;
		height: 184rpx;
		z-index: 998;

	}

	.steps {
		display: flex;


	}

	.step {
		flex: 1;
		position: relative;
	}

	.step .step__line {
		position: absolute;
		background-color: #FFFFFF;
		opacity: 0.3;
		-webkit-transition: background-color 0.3s;
		transition: background-color 0.3s;
		top: 30rpx;
		left: 50%;
		right: -50%;
		width: 100%;
		height: 1px;
	}

	.step:last-of-type .step__line {
		display: none;
	}

	.step.is-finish .step__line {
		opacity: 0.85;
	}

	.step__hd {
		position: relative;
		height: 56rpx;
		text-align: center;
	}

	.step__icon {
		width: 80rpx;
		height: 56rpx;
		background-color: #0066E9;
		display: inline-flex;
		justify-content: center;
		align-items: center;
		position: relative;
		z-index: 1;
	}

	.step.is-process .step__icon .icon-bg {
		width: 56rpx;
		height: 56rpx;
		opacity: 0.18;
		background: #FFFFFF;
		border-radius: 50%;
	}

	.step .step__icon .icon {
		width: 28rpx;
		height: 28rpx;
		border-radius: 50%;
		position: absolute;
		opacity: 0.3;
		background-color: #FFFFFF;
		position: absolute;
	}

	.step.is-process .step__icon .icon {
		width: 28rpx;
		height: 28rpx;
		border-radius: 50%;
		opacity: 0.85;
		background-color: #FFFFFF;
		position: absolute;
	}

	.step.is-finish .step__icon .icon {
		opacity: 0.7;
	}

	.step__bd {
		text-align: center;
		width: 100%;
		margin-top: 20rpx;
	}

	.step__bd .step__title {
		font-size: 24rpx;
		font-weight: 400;
		text-align: center;
		opacity: 0.3;
		color: #FFFFFF;
	}

	.step.is-finish .step__title {
		opacity: 0.7;
	}

	.step.is-process .step__title {
		opacity: 1;
	}
</style>
