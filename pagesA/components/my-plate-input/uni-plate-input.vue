/**
* <AUTHOR>
* @version 20190814
*/
<template>
	<view class="so-mask">
		<view class="so-plate animation-scale-up" @tap.stop>
			<view class="so-plate-head">
				<view class="so-plate-type">
					<radio-group @change.stop="typeChange">
						<label>
							<radio value="1" :checked="keyType==1" />
							普通车牌
						</label>
						<label>
							<radio value="2" :checked="keyType==2" />
							新能源车牌
						</label>
					</radio-group>
				</view>
			</view>
			<view class="so-plate-foot">
				<view class="so-plate-keyboard" :style="{height:keyboardHeight}">
					<view id="keyboard">
						<block v-if="inputType == 1">
							<view hover-class="hover" class="so-plate-key" v-for="el of provinceText" :key="el"
								:data-value="el" @tap="chooseKey">{{ el }}</view>
						</block>
						<block v-if="inputType == 1">
							<text class="so-plate-key fill-block"></text>
							<text class="so-plate-key fill-block"></text>
						</block>
						<block v-if="inputType >= 3">
							<view hover-class="hover" class="so-plate-key" v-for="el of numberText" :key="el"
								:data-value="el" @tap="chooseKey">{{ el }}</view>
						</block>
						<block v-if="inputType >= 2">
							<view hover-class="hover" class="so-plate-key" v-for="el of wordText" :key="el"
								:data-value="el" @tap="chooseKey">{{ el }}</view>
						</block>
						<block v-if="inputType == 3">
							<text v-for="el of fillBlock" :key="el.num" class="so-plate-key fill-block"></text>
						</block>
						<block v-if="inputType == 4">
							<view hover-class="hover" class="so-plate-key" v-for="el of lastWordText" :key="el"
								:data-value="el" @tap="chooseKey">{{ el }}</view>
						</block>
						<text v-if="inputType == 4" class="so-plate-key fill-block"></text>
					</view>
				</view>
				<view class="so-plate-btn-group justify-end">
					<view>
						<button class="so-plate-btn so-plate-btn--cancel" @tap="close">取消</button>
					</view>
					<view>
						<button class="so-plate-btn so-plate-btn--delete" @tap="deleteKey">删除</button>
						<button class="so-plate-btn so-plate-btn--submit" @tap="exportPlate">完成</button>

					</view>

				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: 'uni-plate-input',
		data() {
			return {
				// inputType: 0, //键盘类型
				type: 1, //车牌类型
				currentIndex: 0, //当前编辑的输入框
				// currentInputValue: ['', '', '', '', '', '', ''],
				currentValue: [],
				fillBlock: [{
					num: 11
				}, {
					num: 12
				}, {
					num: 13
				}, {
					num: 14
				}, {
					num: 15
				}, {
					num: 16
				}], //避免:key报错
				keyboardHeightInit: false,
				keyboardHeight: '470rpx',
				provinceText: [
					'桂',
					'粤',
					'京',
					'冀',
					'沪',
					'津',
					'晋',
					'蒙',
					'辽',
					'吉',
					'黑',
					'苏',
					'浙',
					'皖',
					'闽',
					'赣',
					'鲁',
					'豫',
					'鄂',
					'湘',
					'琼',
					'渝',
					'川',
					'贵',
					'云',
					'藏',
					'陕',
					'甘',
					'青',
					'宁',
					'新'
				],
				numberText: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
				wordText: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U',
					'V', 'W', 'X', 'Y', 'Z'
				],
				lastWordText: ['港', '澳', '学', '领', '警']
			};
		},
		props: {
			plate: {
				type: String
			},
			currentInputIndex: {
				type: Number
			},
			currentInputValue: {
				type: Array
			},
			keyType: {
				type: Number,
				default: 1
			}
		},
		computed: {
			//输入框类型
			inputType() {
				switch (this.currentInputIndex) {
					case 0:
						return 1;
						break;
					case 1:
						return 2;
						break;
					case 2:
						return 3;
						break;
					case 3:
						return 3;
						break;
					case 4:
						return 3;
						break;
					case 5:
						return 3;
						break;
					case 6:
						return this.keyType == 2 ? 3 : 4;
						break;
					case 7:
						return 4;
						break;
					default:
						return 1;
						break;
				}
			}
		},
		// watch: {
		// 	currentInputIndex: function(n, o) {
		// 		if (!this.keyboardHeightInit) return
		// 		this.$nextTick(() => {
		// 			//2022/1/11-dwz-注释掉键盘自适应高度
		// 			// this.changeKeyboardHeight()
		// 		})
		// 	},
		// },
		methods: {
			//车牌类型切换
			typeChange(e) {
				const {
					value
				} = e.detail;
				this.type = parseInt(value)
				this.$emit('handleSetType', this.type)

			},
			chooseKey(e) {
				const {
					value
				} = e.currentTarget.dataset;
				this.currentIndex = this.currentInputIndex
				this.currentValue = this.currentInputValue

				console.log(this.keyType, this.currentIndex)
				this.$set(this.currentValue, this.currentIndex, value);
				this.$emit('handleSetValue', this.currentValue)
				if (this.keyType == 1 && this.currentIndex < 6) {
					this.currentIndex++
					this.$emit('handleSetIndex', this.currentIndex, this.currentValue)
				}
				if (this.keyType == 2 && this.currentIndex < 7) {
					this.currentIndex++
					this.$emit('handleSetIndex', this.currentIndex, this.currentValue)
				}

			},
			deleteKey() {
				this.currentIndex = this.currentInputIndex
				this.currentValue = this.currentInputValue

				if (this.currentIndex == 0) {
					return
				}
				this.$set(this.currentValue, this.currentIndex, '')
				this.currentIndex--
				this.$emit('handleSetIndex', this.currentIndex, this.currentValue)

			},
			exportPlate() {
				const plate = this.currentInputValue.join('')
				let err = false
				if (this.keyType == 1 && plate.length != 7) {
					err = true
				} else if (this.keyType == 2 && plate.length != 8) {
					err = true
				}
				if (err) return uni.showToast({
					title: '请输入完整的车牌号码',
					icon: 'none'
				})

				this.$emit('export', plate)
			},
			changeKeyboardHeight() {
				const that = this
				const query = uni.createSelectorQuery().in(this);
				query.select('#keyboard').boundingClientRect();
				query.exec(function(res) {
					if (res && res[0]) {
						that.keyboardHeight = res[0].height + uni.upx2px(30) + 'px'
						that.keyboardHeightInit = true
					}
				});
			},
			close(type) {
				const plate = this.currentInputValue.join('')
				//没点取消，也没点完成，车牌长度够的时候，获取完整车牌
				console.log(this.keyType, plate.length)
				if (this.keyType == 1 && plate.length == 7) {
					this.$emit('export', plate)
				} else if (this.keyType == 2 && plate.length == 8) {
					this.$emit('export', plate)
				} else {
					this.$emit('close')
				}
			}
		},
		mounted() {
			console.log(this.plate);
			const plateKey = this.plate.split('')
			if (plateKey.length == 7) {
				this.type = 1
			} else if (plateKey.length == 8) {
				this.type = 2
			}
			if (plateKey.length == 7 || plateKey.length == 8) {
				this.currentInputValue = plateKey
				// this.currentInputIndex = plateKey.length-1
				this.$emit('handleSetIndex', plateKey.length - 1)
			}

			// setTimeout(() => { //在动画结束之后才开始获取
			// 	this.$nextTick(() => {
			// 		this.changeKeyboardHeight()
			// 	})
			// }, 500);
		}
	};
</script>
<style scoped lang="less">
	@import './uni-plate-input'; //引入css
</style>
