<template>
	<view class="title-wrapper" @click="handleClick">
		<view class="t-title">
			{{title}}
			<text v-if="titleDesc" style="color: red;">{{titleDesc}}</text>
		</view>
		<view v-if="btnTitle" class="btn-wrapper">
			<view class="btn">
				快递邮寄
			</view>
		</view>
		<view v-if="desc" class="right-desc">
			{{desc}}
		</view>
		<slot v-else></slot>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: ''
			},
			desc: {
				type: String,
				default: ''
			},
			btnTitle: {
				type: String
			},
			titleDesc: {
				type: String,
				default: ''
			},
		},
		data() {
			return {

			}
		},
		methods: {
			handleClick() {
				this.$emit('click')
			}
		}
	};
</script>

<style lang="scss" scoped>
	.title-wrapper {
		width: 100%;
		height: 82rpx;
		line-height: 82rpx;
		background-color: rgba(255, 255, 255, 100);
		// padding-left: 50rpx;
		padding: 0 33rpx;
		margin: 20rpx 0;
		display: flex;
		justify-content: space-between;

		.t-title {
			font-size: 30rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;
		}

		.right-desc {
			// margin-right: 30rpx;
			font-size: 30rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FF9038;
		}

		.btn-wrapper {
			// margin-right: 30rpx;
			// padding: 20rpx 0;
			display: flex;
			align-items: center;

			.btn {
				// width: 85px;
				height: 60rpx;
				line-height: 60rpx;
				padding: 0 20rpx;
				border-radius: 12rpx;
				background-color: $my-theme-color;
				color: rgba(255, 255, 255, 100);
				font-size: 28rpx;
				text-align: center;
				font-family: Arial;
			}
		}
	}
</style>
