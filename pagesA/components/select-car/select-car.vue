<template>
	<view class="car-component">
		<view class="radio-car">
			<!-- <view class="all-select">
				<view>
					<checkbox-group @change="checkAll" class="checkbox-label">
						<label class="radio">
							<checkbox :checked="isAll" value="all" color="#47A8EE" style="transform:scale(0.7)" />
						</label>
						<text class="select-all">全选</text>
					</checkbox-group>
				</view>
				<text>已选{{selectAry.length}}张</text>
			</view> -->
			<scroll-view :scroll-top="scrollTop" 
			scroll-y="true" 
			class="scroll-Y" 
			@scrolltolower="lower" 
			lower-threshold="40"
			@scroll="scroll">
				<view class="radio-list" v-if="stateCarList.length > 0">
					<checkbox-group @change="checkboxChange" class="radio-label" :style="style">
						<label class="radio-list-label" v-for="(item,index) in stateCarList" :key="index">
							<!-- <view class="radios">
								<checkbox :value="item.value" :checked="item.checked" color="#47A8EE" style="transform:scale(0.7)" />
							</view> -->
							<view class="radio-value">
								<view>车牌号码:<text class="radio-text">{{item.plateNum}}</text></view>
								<view>车牌颜色:<text class="radio-text" style="color:#2484E8">[{{plateColorToColorMap.get(item.plateColor+'')}}]</text></view>
							</view>
						</label>
					</checkbox-group>
				</view>
				<view v-else class="emptyList" :style="style">当前无车牌</view>
			</scroll-view>
		</view>
		<paging :loadingType="loadingType" class="paging" v-if="pageNo > 1"></paging>
		<view class="paging" v-else></view>
		<button type="default" class="next-btn" @click="addSelectCar">确定</button>
	</view>
</template>

<script>
	import util from '@/common/util.js'
	import paging from '@/pagesB/components/load-paging/paging.vue';
	import {plateColorToColorMap} from '@/common/systemConstant.js';
	export default {
		components:{
			paging
		},
		props: {
			carList:{
				type: Array,
				default: []
			},
			loadingType:{
				type:Number, 
				default:0 //定义加载方式 0---contentdown  1---contentrefresh 2---contentnomore
			},
			type:{
				type:String,
				default:""
			},
			pageNo:{ //最大页数
				type:Number,
				default:1
			}
		},
		computed: {
		    style() {
		        var systemInfo = uni.getSystemInfoSync()
				return `height:${systemInfo.windowHeight-160}px`
		    }
		},
		data() {
			return {
				isAll: false,
				scrollTop: 0,
				old:{
					scrollTop: 0
				},
				selectAry: [],
				stateCarList: JSON.parse(JSON.stringify(this.carList)),
				plateColorToColorMap
			}
		},
		methods:{
			lower() {
				console.log('底部')
				this.$emit('getList')
			},
			scroll(e) {
				// console.log(e)
				this.old.scrollTop = e.detail.scrollTop
			},
			checkboxChange(e) {
				let items = this.stateCarList,
					values = e.detail.value;
				for (let i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					if (values.includes(item.value)) {
						this.$set(item, 'checked', true)
					} else {
						this.$set(item, 'checked', false)
					}
				}
				this.selectAry = []
				for (let obj of this.stateCarList) {
					if (obj.checked) {
						this.selectAry.push(obj.value)
					}
				}
				this.isAll = false
				if (this.selectAry.length === this.stateCarList.length) {
					this.isAll = true
				}
			},
			checkAll(e) {
				if (e.detail.value.length > 0) {
					this.isAll = true
					this.selectAry = []
					for (let obj of this.stateCarList) {
						this.$set(obj, 'checked', true)
						this.selectAry.push(obj.value)
					}
				} else {
					this.isAll = false
					for (let obj of this.stateCarList) {
						this.$set(obj, 'checked', false)
					}
					this.selectAry = []
				}
			},
			addSelectCar() {
				this.$emit("getSelectAry")
			}
		},
		watch:{
			carList(){
				this.stateCarList = JSON.parse(JSON.stringify(this.carList))
			}
		}
	}
</script>

<style lang="scss" scoped>
	.car-component {
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;
	}

	.radio-car {
		width: 100%;
		background-color: #fff;
		margin-top: 30rpx;

		.all-select {
			height: 88rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 38rpx 0 24rpx;
			border-bottom: 1px solid #eee;

			.select-all {
				position: relative;
				left: 20rpx;
				top: 4rpx;
			}
		}
		.emptyList{
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 40rpx;

		}
		.radio-label,
		.unit-radio-label{
			width: 100%;
			min-height: 140rpx;
			
			.radio-list-label {
				min-height: 140rpx;
				display: flex;
				position: relative;
				align-items: center;
				padding: 10rpx;
				border-bottom: 1px solid #eee;
				
				.radios {
					position: relative;
					left: 7px;
				}

				.radio-value {
					font-size: 28rpx;
					color: #333;
					position: relative;
					left: 16px;

					view {
						margin-top: 10rpx;
					}

					.radio-text {
						display: inline-block;
						margin-left: 20rpx;
					}

					.detail {
						color:#1D82D2;
						margin-left: 20rpx;
					}
				}

				.status {
					position: absolute;
					right: 40rpx;
				}
			}
		}
		.unit-radio-label{
			height: 670rpx;
		}
	}
	.paging{
		width: 100%;
		background-color: #fff;
		height: 50rpx;
	}
	.next-btn {
		position: absolute;
		bottom: 34rpx;
		width: 720rpx;
		height: 104rpx;
		font-size: 32rpx;
		line-height: 104rpx;
		text-align: center;
		background-color:#1D82D2;
		color: #fff;
	}
</style>
