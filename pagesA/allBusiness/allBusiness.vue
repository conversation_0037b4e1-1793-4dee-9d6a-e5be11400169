<template>
	<view class="otherServer">
		<view class="top">
			<view class="commonlyUsed">
				<view class="commonly-top">
					<view class="all">
						<view class="title-icon"></view>
						<view class="title">全部功能</view>
					</view>
				</view>
				<view class="commonlyList">
					<block v-for="(item, index) in commonlyList" :key="index">
						<view class="card" v-if="!item.isNoVisible"
							@click='goBusinessPageHandle(item)' >
						
								<image :src="item.icon" style="width: 64rpx;height: 64rpx;" mode="monthlyBillt">
								</image>
								<view class="name">{{item.name}}</view>
							
						
						</view>
					</block>
					
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCurrUserInfo
	} from '@/common/storageUtil.js';
	export default {
		data() {
			return {
				userInfo: {},
				isAccountBind: false,
				isEdit: false,
				commonlyList: [
				],
			};
		},
		computed: {

		},
		onLoad(option) {

		},
		methods: {
			goBusinessPageHandle(row) {
				let type = row.type;
				if (type == 'rechargeOtherList') {
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/rechargeOtherList/rechargeOtherList'
					});
					return
				}
				if (type == 'myAccount') {
					uni.navigateTo({
						url: '/pagesB/personal/more/p-more'
					});
					return
				}
				// 读卡圈存
				if (type == 'loadBusiness') {
					uni.navigateTo({
						url: '/pagesB/loadBusiness/loadType'
					});
					return
				}
				if (type == 'otherRecharge') {
					uni.navigateTo({
						url: "/pagesB/rechargeBusiness/rechargeOther/rechargeOther"
					});
					return;
				}
				if (type == 'recharge') {
					uni.navigateTo({
						url: '/pagesB/personal/cardCharge/cardCharge?fontType=' + 'recharge'
					});
				
					return;
				}
				if (!(getCurrUserInfo() && getCurrUserInfo().customer_id)) {
					uni.showModal({
						title: "提示",
						content: "请先绑定ETC用户",
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: "/pagesB/accountBusiness/accountList/accountList"
								})
							}
						}
					})
					return
				}

				if (type == 'onlineserver') {
					// var callcenter = "http://ccrm.wengine.cn/online.html?tntInstId=HSYQGXGS&scene=SCE0000027";
					var callcenter = "https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027"
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(callcenter)
					});
					return
				}
				// ECSS账户迁移
				if (type == 'ecssSync') {
					uni.navigateTo({
						url: '/pagesB/ecssBusiness/index'
					});
					return
				}
				//优惠券
				if (type == 'coupon') {
					uni.navigateTo({
						url: '/pagesC/coupons/selectCoupons/selectCoupons'
					});
					return
				}
				if (type) {
					uni.navigateTo({
						url: "/pagesB/personal/my-car/p-my-car?fontType=" + type
					})
				}
			}

		}
	}
</script>

<style lang="scss" scoped>
	.otherServer {
		background-color: #f6f6f6f;

		.top,
		.content {
			background-color: #fff;
			margin: 20rpx 0;
			padding: 20rpx 0;

			.commonlyUsed {
				width: 100%;
				display: flex;
				flex-direction: column;

				.commonly-top {
					display: flex;
					justify-content: space-between;
					padding: 20rpx;
					background-color: #fff;

					.title-icon {
						font-weight: 800;
						background-color: #0066E9;
						width: 8rpx;
						height: 26rpx;
						border-radius: 4rpx;
						margin-left: 10rpx;
					}

					.title {
						color: #333333;
						font-size: 30rpx;
						font-weight: 600;
						padding-left: 12rpx;

						&:before {
							// content: "|";
							// font-weight: 800;
							// color: #FF9D09;
							// position: relative;
							// right: 10rpx;
							// top: -4rpx;
							// width: 8rpx;
							// height: 26rpx;
						}
					}

					.all {
						display: flex;
						color: #3874FF;
						font-size: 26rpx;
						margin-right: 20rpx;
						align-items: center;
					}
				}

				.commonly-two {
					justify-content: normal;
					align-items: center;
				}

				.commonlyList {
					display: flex;
					// margin-top: 20rpx;
					flex-wrap: wrap;

					.card {
						display: flex;
						width: 185rpx;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						margin: 22rpx 0;
						position: relative;

						.name {

							color: #666;
							font-weight: 400;
							font-size: 26rpx;
							margin-top: 8rpx;
						}

						.editIcon {
							position: absolute;
							right: 50rpx;
							top: -10rpx;
						}
					}
				}
			}
		}

		.content {
			margin: 0;
		}
	}
</style>
