function downLoadFile(url) {
	let fileExt = url.substring(url.lastIndexOf('.') + 1).toLowerCase()
	let fileName = '车辆授权书'
	wx.downloadFile({
		url: url,
		filePath: wx.env.USER_DATA_PATH + '/' + fileName,
		success: res => {
			let filePath = res.filePath
			wx.openDocument({
				filePath: filePath,
				fileType: fileExt,
				showMenu: true,
				success: res => {
					console.log('文档打开成功');
				}
			})
		}
	})
}


function previewFile(url) {
	uni.navigateTo({
		url: '/pages/uni-webview/uni-webview?ownPath=' + encodeURIComponent(url)
	});
}


module.exports = {
	previewFile,
	downLoadFile
}