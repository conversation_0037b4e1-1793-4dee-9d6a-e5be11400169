//车辆用户类型字典
export const statusInfoList = {
	'1': { //待支付
		status: 'info',
		statusInfo: '请于30天内完成支付',
		showInfo: true,
		buttonList: [{
			title: '去支付',
			handle: 'toPay',
			type: 'primary',
			isShow: true
		}, {
			title: '取消订单',
			handle: 'handleCancelOrder',
			type: 'info',
			isShow: true
		}]
	},
	'2': { //待取货
		status: 'warnning',
		statusInfo: '请于30天内完成支付',
		showInfo: true,
	},
	'3': { //审核中
		status: 'warnning',
		statusInfo: '我们会尽快为您审核，请耐心等待！',
		showInfo: true,
		buttonList: [{
			title: '取消订单',
			handle: 'handleCancelOrder',
			type: 'info',
			isShow: true
		}]
	},
	'4': { //审核通过
		status: 'success',
		statusInfo: '订单已审核通过，我们会尽快为您发货，请留意物流信息',
		showInfo: true,
	},
	'5': { //审核不通过
		status: 'error',
		showInfo: false,
		buttonList: [{
			title: '取消订单',
			handle: 'handleCancelOrder',
			type: 'info',
			isShow: true
		}]
	},
	'6': { //设备已发货
		status: 'success',
		statusInfo: '已为您寄出设备，请留意物流信息',
		showInfo: true,
		buttonList: [{
			title: '确认签收',
			handle: 'handleConfirm',
			type: 'primary',
			isShow: true
		}, {
			title: '申请退换',
			handle: 'afterApply',
			type: 'info',
			isShow: true
		}]
	},
	'7': { //新设备已发货
		status: 'success',
		statusInfo: '已为您寄出新设备，请留意物流信息',
		showInfo: true,
		buttonList: [{
			title: '确认收货',
			handle: 'handleRefundConfirm',
			type: 'primary',
			isShow: true
		}, {
			title: '申请退换',
			handle: 'afterApply',
			type: 'info',
			isShow: true
		}]
	},


	'8': { //已签收待激活已签收
		status: 'success',
		statusInfo: '设备已送达，请尽快安装激活',
		showInfo: true,
		buttonList: [{
			title: '前往激活',
			handle: 'toIssue',
			type: 'primary',
			isShow: true
		}, {
			title: '申请退换',
			handle: 'afterApply',
			type: 'info',
			isShow: true
		}]
	},
	'9': { // 已签收待激活已发行未激活
		status: 'success',
		statusInfo: '设备已送达，请尽快安装激活',
		showInfo: true,
	},
	'10': { // 已发行激活已完结
		status: 'success',
		statusInfo: '订单已完成，如需开票，请点击去开票按钮',
		showInfo: true,
		buttonList: [{
			title: '去开票',
			handle: 'toInvoice',
			type: 'primary',
			isShow: true
		}]
	},
	'11': { //已线下发行已完结
		status: 'success',
		statusInfo: '订单已完成，如需开票，请点击去开票按钮',
		showInfo: true,
	},
	'12': { //已取消
		status: 'info',
		statusInfo: '订单已取消，费用已退至您的支付账户',
		showInfo: true,
		buttonList: [{
			title: '重新申请',
			handle: 'reApply',
			type: 'primary',
			isShow: true
		}]
	},
	'13': { //已退货退款
		status: 'info',
		statusInfo: '已退货退款',
		showInfo: true,
	},
	'14': { //已退货不退款
		status: 'info',
		statusInfo: '不符合退款条件，如有疑问请联系在线客服。',
		showInfo: true,
	},
	'15': { //已完结  已退激活保证金
		status: 'success',
		statusInfo: '已退激活保证金',
		showInfo: true,
		buttonList: [{
			title: '去开票',
			handle: 'toInvoice',
			type: 'primary',
			isShow: true
		}]
	},
	'16': { //换货申请待审核
		status: 'warnning',
		statusInfo: '后台正在审核您的售后申请，请耐心等待',
		showInfo: true,
	},
	'17': { //换货审核不通过
		status: 'error',
		showInfo: false,
		buttonList: [{
			title: '修改售后材料',
			handle: 'toAfterDetail',
			type: 'info',
			isShow: true
		}]
	},
	'18': { //换货审核通过-------------分自行寄回设备，上门取件
		status: 'success',
		statusInfo0: '快递小哥正在来的路上，请耐心等待', //上门取件
		statusInfo1: '请将设备整套寄回并填写设备寄回的物流信息，查看收件地址', //自行寄回
		showInfo: true,
	},
	'19': { //退货审核中
		status: 'warnning',
		statusInfo: '后台正在审核您的售后申请，请耐心等待',
		showInfo: true,
	},
	'20': { //退货审核不通过
		status: 'error',
		showInfo: false,
		buttonList: [{
			title: '修改售后材料',
			handle: 'toAfterDetail',
			type: 'info',
			isShow: true
		}]
	},
	'21': { //退货审核通过
		status: 'success',
		statusInfo0: '快递小哥正在来的路上，请耐心等待', //上门取件
		statusInfo1: '请将设备整套寄回并填写设备寄回的物流信息，查看收件地址', //自行寄回
		showInfo: true,
	},
	'22': { //设备已寄回
		status: 'success',
		statusInfo: '我们收到货后，会尽快为您处理',
		showInfo: true,
	},
	'23': { //新办订单创建
		status: 'success',
		statusInfo: '已为您寄出设备，请留意物流信息',
		showInfo: true,
	},
	'24': { //待签署
		status: 'info',
		statusInfo: '您还有未签署的协议，请先去签署',
		showInfo: true,
	},
	'25': { //全额退款-----------
		status: 'success',
		statusInfo: '已全额退款',
		showInfo: true,
	},
	'26': { //售后签收
		status: 'success',
		statusInfo: '新设备已送达，请尽快安装激活',
		showInfo: true,
		buttonList: [{
			title: '前往激活',
			handle: 'toIssue',
			type: 'primary',
			isShow: true
		}, {
			title: '申请退换',
			handle: 'afterApply',
			type: 'info',
			isShow: true
		}]
	},
}

export const dyStatusInfoList = {
	'5000': { //待支付
		status: 'success',
		statusInfo: '订单创建',
	},
	'5010': { //待签署
		status: 'warnning',
		statusInfo: '您还有未签署的协议，请先去签署',
		showInfo: true,
		buttonList: [{
			title: '去签署',
			handle: 'toSign',
			type: 'primary',
			isShow: true
		}, {
			title: '取消订单',
			handle: 'handleCancelOrder',
			type: 'info',
			isShow: true
		}]
	},
	'5020': { //审核中
		status: 'warnning',
		statusInfo: '我们会尽快为您审核，请耐心等待！',
		showInfo: false,
		buttonList: [{
			title: '取消申办',
			handle: 'handleCancelOrder',
			type: 'info',
			isShow: true
		}]
	},
	'5030': { //审核通过
		status: 'success',
		statusInfo: '订单已审核通过，到货后即可前往激活！',
		showInfo: true,
		buttonList: [{
			title: '前往激活',
			handle: 'toIssue',
			type: 'primary',
			isShow: true
		}]
	},
	'5040': { //审核不通过
		status: 'error',
		showInfo: false,
		buttonList: [{
			title: '修改订单',
			handle: 'orderUpdate',
			type: 'primary',
			isShow: true
		}, {
			title: '取消订单',
			handle: 'handleCancelOrder',
			type: 'info',
			isShow: true
		}]
	},
	'5060': { //设备已发货
		status: 'success',
		statusInfo: '已退激活保证金',
		showInfo: true,
		buttonList: [{
			title: '去开票',
			handle: 'toInvoice',
			type: 'primary',
			isShow: true
		}]
	},
	'5070': { //新设备已发货
		status: 'info',
		statusInfo: '已退货退款',
		showInfo: true,
	},

	'5050': { //已签收待激活已签收
		status: 'info',
		statusInfo: '订单已取消',
		showInfo: true,
		buttonList: [{
			title: '重新申请',
			handle: 'reApply',
			type: 'primary',
			isShow: true
		}]
	},
}
//车辆用户类型字典
export const afterSaleStatusList = {
	//线上注销
	'401': { //待审核
		status: 'warning',
		statusInfo: '我们会尽快为您审核，请耐心等待',
		showInfo: true,
	},
	'402': { //审核通过
		status: 'success',
		statusInfo: '审核通过，请尽快激活',
		showInfo: true,
	},
	'403': { //注销不成功
		status: 'danger',
		statusInfo: '我们会尽快为您审核，请耐心等待',
		showInfo: true,
	},
	'404': { //已注销清算中
		status: 'info',
		statusInfo: '订单已取消',
		showInfo: true,
	},
	'405': { //注销退款中
		status: 'success',
		statusInfo: '已激活完成',
		showInfo: false,
	},
	'406': { //注销退款不成功
		status: 'info',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'407': { //已完结
		status: 'info',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'408': { //已取消
		status: 'info',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'409': { //注销欠款
		status: 'info',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},


}

//成功页面按钮
export const successBtnList = {
	'1': {
		content: '订单提交成功，我们会尽快为您审核订单！',
		dyContent: '申办提交成功，请您耐心等待，我们将在24小时内为您审核！',
		button: [{
			title: '查看订单详情',
			handle: 'toApplyList'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'2': {
		content: '您的ETC新办申请订单已取消，如有需要您可重新申请，或通过捷通线下网点进行办理。',
		dyContent: '您的ETC新办申请订单已取消，如有需要您可重新申请。',
		button: [{
			title: '重新申请',
			handle: 'toNewApply'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'3': {
		content: '您的售后申请提交成功，我们会尽快为您审核！',
		button: [{
			title: '查看售后详情',
			handle: 'toAfterDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'4': {
		content: '您的ETC换货申请已撤销。',
		button: [{
			title: '查看售后详情',
			handle: 'toAfterDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'5': {
		content: '请前往激活。',
		dyContent: '请前往激活。',
		button: [{
			title: '前往激活',
			handle: 'toActive'
		}]
	},
	//次次顺激活成功
	'6': {
		content: '激活成功，快去感受ETC快速通行的体验吧。如您需开票，请点击',
		button: [{
			title: '返回首页',
			handle: 'toHome'
		}],
		invoince: true,
	},
	'7': {
		content: '修改成功，我们会尽快为您发货，请耐心等待。',
		button: [{
			title: '查看订单详情',
			handle: 'toApplyDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'8': {
		content: '您的ETC退货申请已撤销。',
		button: [{
			title: '查看售后详情',
			handle: 'toAfterDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'9': {
		content: '修改成功，我们将在3个工作日内完成审核，请耐心等待。',
		button: [{
			title: '查看售后详情',
			handle: 'toAfterDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'10': {
		content: '我们收到设备后，会尽快为您处理，请耐心等待。',
		button: [{
			title: '查看售后详情',
			handle: 'toAfterDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	// 日日通激活成功
	'11': {
		content: '激活成功，充值后就可感受ETC快速通行的体验啦！如您需开票，请点击',
		button: [{
			title: '前往充值',
			handle: 'toCharge'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}],
		invoince: true,
	},
	//更换补办3,4,激活成功
	'12': {
		content: '激活成功，如您需开票，请点击',
		button: [{
			title: '返回首页',
			handle: 'toHome'
		}],
		invoince: true,
	},
	// 日日通激活成功不开票
	'13': {
		content: '激活成功，充值后就可感受ETC快速通行的体验啦！',
		button: [{
			title: '前往充值',
			handle: 'toCharge'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}],
	},
	//次次顺激活成功不开票
	'14': {
		content: '激活成功，快去感受ETC快速通行的体验吧。',
		button: [{
			title: '返回首页',
			handle: 'toHome'
		}],
	},
}