!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var r=t();for(var i in r)("object"==typeof exports?exports:e)[i]=r[i]}}("undefined"!=typeof self?self:this,function(){return function(e){function t(i){if(r[i])return r[i].exports;var n=r[i]={i:i,l:!1,exports:{}};return e[i].call(n.exports,n,n.exports,t),n.l=!0,n.exports}var r={};return t.m=e,t.c=r,t.d=function(e,r,i){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:i})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=8)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=i||function(e,t){var r=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),i={},n=i.lib={},o=n.Base=function(){return{extend:function(e){var t=r(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),a=n.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,r=e.words,i=this.sigBytes,n=e.sigBytes;if(this.clamp(),i%4)for(var o=0;o<n;o++){var a=r[o>>>2]>>>24-o%4*8&255;t[i+o>>>2]|=a<<24-(i+o)%4*8}else for(var o=0;o<n;o+=4)t[i+o>>>2]=r[o>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=o.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var r,i=[],n=0;n<t;n+=4){var o=function(t){var t=t,r=987654321,i=4294967295;return function(){r=36969*(65535&r)+(r>>16)&i,t=18e3*(65535&t)+(t>>16)&i;var n=(r<<16)+t&i;return n/=4294967296,(n+=.5)*(e.random()>.5?1:-1)}}(4294967296*(r||e.random()));r=987654071*o(),i.push(4294967296*o()|0)}return new a.init(i,t)}}),s=i.enc={},c=s.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var o=t[n>>>2]>>>24-n%4*8&255;i.push((o>>>4).toString(16)),i.push((15&o).toString(16))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i+=2)r[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new a.init(r,t/2)}},u=s.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var o=t[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(o))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new a.init(r,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},d=n.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r=this._data,i=r.words,n=r.sigBytes,o=this.blockSize,s=4*o,c=n/s;c=t?e.ceil(c):e.max((0|c)-this._minBufferSize,0);var u=c*o,l=e.min(4*u,n);if(u){for(var d=0;d<u;d+=o)this._doProcessBlock(i,d);var f=i.splice(0,u);r.sigBytes-=l}return new a.init(f,l)},clone:function(){var e=o.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),f=(n.Hasher=d.extend({cfg:o.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new f.HMAC.init(e,r).finalize(t)}}}),i.algo={});return i}(Math);!function(){function e(e,t,r){for(var i=[],o=0,a=0;a<t;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2,c=r[e.charCodeAt(a)]>>>6-a%4*2;i[o>>>2]|=(s|c)<<24-o%4*8,o++}return n.create(i,o)}var t=i,r=t.lib,n=r.WordArray,o=t.enc;o.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,i=this._map;e.clamp();for(var n=[],o=0;o<r;o+=3)for(var a=t[o>>>2]>>>24-o%4*8&255,s=t[o+1>>>2]>>>24-(o+1)%4*8&255,c=t[o+2>>>2]>>>24-(o+2)%4*8&255,u=a<<16|s<<8|c,l=0;l<4&&o+.75*l<r;l++)n.push(i.charAt(u>>>6*(3-l)&63));var d=i.charAt(64);if(d)for(;n.length%4;)n.push(d);return n.join("")},parse:function(t){var r=t.length,i=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var o=0;o<i.length;o++)n[i.charCodeAt(o)]=o}var a=i.charAt(64);if(a){var s=t.indexOf(a);-1!==s&&(r=s)}return e(t,r,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),function(e){function t(e,t,r,i,n,o,a){var s=e+(t&r|~t&i)+n+a;return(s<<o|s>>>32-o)+t}function r(e,t,r,i,n,o,a){var s=e+(t&i|r&~i)+n+a;return(s<<o|s>>>32-o)+t}function n(e,t,r,i,n,o,a){var s=e+(t^r^i)+n+a;return(s<<o|s>>>32-o)+t}function o(e,t,r,i,n,o,a){var s=e+(r^(t|~i))+n+a;return(s<<o|s>>>32-o)+t}var a=i,s=a.lib,c=s.WordArray,u=s.Hasher,l=a.algo,d=[];!function(){for(var t=0;t<64;t++)d[t]=4294967296*e.abs(e.sin(t+1))|0}();var f=l.MD5=u.extend({_doReset:function(){this._hash=new c.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,i){for(var a=0;a<16;a++){var s=i+a,c=e[s];e[s]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}var u=this._hash.words,l=e[i+0],f=e[i+1],h=e[i+2],p=e[i+3],v=e[i+4],_=e[i+5],y=e[i+6],g=e[i+7],k=e[i+8],S=e[i+9],m=e[i+10],A=e[i+11],b=e[i+12],I=e[i+13],T=e[i+14],w=e[i+15],U=u[0],E=u[1],C=u[2],B=u[3];U=t(U,E,C,B,l,7,d[0]),B=t(B,U,E,C,f,12,d[1]),C=t(C,B,U,E,h,17,d[2]),E=t(E,C,B,U,p,22,d[3]),U=t(U,E,C,B,v,7,d[4]),B=t(B,U,E,C,_,12,d[5]),C=t(C,B,U,E,y,17,d[6]),E=t(E,C,B,U,g,22,d[7]),U=t(U,E,C,B,k,7,d[8]),B=t(B,U,E,C,S,12,d[9]),C=t(C,B,U,E,m,17,d[10]),E=t(E,C,B,U,A,22,d[11]),U=t(U,E,C,B,b,7,d[12]),B=t(B,U,E,C,I,12,d[13]),C=t(C,B,U,E,T,17,d[14]),E=t(E,C,B,U,w,22,d[15]),U=r(U,E,C,B,f,5,d[16]),B=r(B,U,E,C,y,9,d[17]),C=r(C,B,U,E,A,14,d[18]),E=r(E,C,B,U,l,20,d[19]),U=r(U,E,C,B,_,5,d[20]),B=r(B,U,E,C,m,9,d[21]),C=r(C,B,U,E,w,14,d[22]),E=r(E,C,B,U,v,20,d[23]),U=r(U,E,C,B,S,5,d[24]),B=r(B,U,E,C,T,9,d[25]),C=r(C,B,U,E,p,14,d[26]),E=r(E,C,B,U,k,20,d[27]),U=r(U,E,C,B,I,5,d[28]),B=r(B,U,E,C,h,9,d[29]),C=r(C,B,U,E,g,14,d[30]),E=r(E,C,B,U,b,20,d[31]),U=n(U,E,C,B,_,4,d[32]),B=n(B,U,E,C,k,11,d[33]),C=n(C,B,U,E,A,16,d[34]),E=n(E,C,B,U,T,23,d[35]),U=n(U,E,C,B,f,4,d[36]),B=n(B,U,E,C,v,11,d[37]),C=n(C,B,U,E,g,16,d[38]),E=n(E,C,B,U,m,23,d[39]),U=n(U,E,C,B,I,4,d[40]),B=n(B,U,E,C,l,11,d[41]),C=n(C,B,U,E,p,16,d[42]),E=n(E,C,B,U,y,23,d[43]),U=n(U,E,C,B,S,4,d[44]),B=n(B,U,E,C,b,11,d[45]),C=n(C,B,U,E,w,16,d[46]),E=n(E,C,B,U,h,23,d[47]),U=o(U,E,C,B,l,6,d[48]),B=o(B,U,E,C,g,10,d[49]),C=o(C,B,U,E,T,15,d[50]),E=o(E,C,B,U,_,21,d[51]),U=o(U,E,C,B,b,6,d[52]),B=o(B,U,E,C,p,10,d[53]),C=o(C,B,U,E,m,15,d[54]),E=o(E,C,B,U,f,21,d[55]),U=o(U,E,C,B,k,6,d[56]),B=o(B,U,E,C,w,10,d[57]),C=o(C,B,U,E,y,15,d[58]),E=o(E,C,B,U,I,21,d[59]),U=o(U,E,C,B,v,6,d[60]),B=o(B,U,E,C,A,10,d[61]),C=o(C,B,U,E,h,15,d[62]),E=o(E,C,B,U,S,21,d[63]),u[0]=u[0]+U|0,u[1]=u[1]+E|0,u[2]=u[2]+C|0,u[3]=u[3]+B|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;r[n>>>5]|=128<<24-n%32;var o=e.floor(i/4294967296),a=i;r[15+(n+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),r[14+(n+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(r.length+1),this._process();for(var s=this._hash,c=s.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=u.clone.call(this);return e._hash=this._hash.clone(),e}});a.MD5=u._createHelper(f),a.HmacMD5=u._createHmacHelper(f)}(Math),function(){var e=i,t=e.lib,r=t.WordArray,n=t.Hasher,o=e.algo,a=[],s=o.SHA1=n.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],s=r[3],c=r[4],u=0;u<80;u++){if(u<16)a[u]=0|e[t+u];else{var l=a[u-3]^a[u-8]^a[u-14]^a[u-16];a[u]=l<<1|l>>>31}var d=(i<<5|i>>>27)+c+a[u];d+=u<20?1518500249+(n&o|~n&s):u<40?1859775393+(n^o^s):u<60?(n&o|n&s|o&s)-1894007588:(n^o^s)-899497514,c=s,s=o,o=n<<30|n>>>2,n=i,i=d}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[14+(i+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(i+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});e.SHA1=n._createHelper(s),e.HmacSHA1=n._createHmacHelper(s)}(),function(e){var t=i,r=t.lib,n=r.WordArray,o=r.Hasher,a=t.algo,s=[],c=[];!function(){function t(e){return 4294967296*(e-(0|e))|0}for(var r=2,i=0;i<64;)(function(t){for(var r=e.sqrt(t),i=2;i<=r;i++)if(!(t%i))return!1;return!0})(r)&&(i<8&&(s[i]=t(e.pow(r,.5))),c[i]=t(e.pow(r,1/3)),i++),r++}();var u=[],l=a.SHA256=o.extend({_doReset:function(){this._hash=new n.init(s.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],a=r[3],s=r[4],l=r[5],d=r[6],f=r[7],h=0;h<64;h++){if(h<16)u[h]=0|e[t+h];else{var p=u[h-15],v=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,_=u[h-2],y=(_<<15|_>>>17)^(_<<13|_>>>19)^_>>>10;u[h]=v+u[h-7]+y+u[h-16]}var g=s&l^~s&d,k=i&n^i&o^n&o,S=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),m=(s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25),A=f+m+g+c[h]+u[h],b=S+k;f=d,d=l,l=s,s=a+A|0,a=o,o=n,n=i,i=A+b|0}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0,r[5]=r[5]+l|0,r[6]=r[6]+d|0,r[7]=r[7]+f|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;return r[n>>>5]|=128<<24-n%32,r[14+(n+64>>>9<<4)]=e.floor(i/4294967296),r[15+(n+64>>>9<<4)]=i,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=o._createHelper(l),t.HmacSHA256=o._createHmacHelper(l)}(Math),function(){function e(e){return e<<8&4278255360|e>>>8&16711935}var t=i,r=t.lib,n=r.WordArray,o=t.enc;o.Utf16=o.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n+=2){var o=t[n>>>2]>>>16-n%4*8&65535;i.push(String.fromCharCode(o))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return n.create(r,2*t)}};o.Utf16LE={stringify:function(t){for(var r=t.words,i=t.sigBytes,n=[],o=0;o<i;o+=2){var a=e(r[o>>>2]>>>16-o%4*8&65535);n.push(String.fromCharCode(a))}return n.join("")},parse:function(t){for(var r=t.length,i=[],o=0;o<r;o++)i[o>>>1]|=e(t.charCodeAt(o)<<16-o%2*16);return n.create(i,2*r)}}}(),function(){if("function"==typeof ArrayBuffer){var e=i,t=e.lib,r=t.WordArray,n=r.init;(r.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,r=[],i=0;i<t;i++)r[i>>>2]|=e[i]<<24-i%4*8;n.call(this,r,t)}else n.apply(this,arguments)}).prototype=r}}(),/** @preserve
(c) 2012 by Cédric Mesnil. All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/
function(e){function t(e,t,r){return e^t^r}function r(e,t,r){return e&t|~e&r}function n(e,t,r){return(e|~t)^r}function o(e,t,r){return e&r|t&~r}function a(e,t,r){return e^(t|~r)}function s(e,t){return e<<t|e>>>32-t}var c=i,u=c.lib,l=u.WordArray,d=u.Hasher,f=c.algo,h=l.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),p=l.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),v=l.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),_=l.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),y=l.create([0,1518500249,1859775393,2400959708,2840853838]),g=l.create([1352829926,1548603684,1836072691,2053994217,0]),k=f.RIPEMD160=d.extend({_doReset:function(){this._hash=l.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,i){for(var c=0;c<16;c++){var u=i+c,l=e[u];e[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}var d,f,k,S,m,A,b,I,T,w,U=this._hash.words,E=y.words,C=g.words,B=h.words,x=p.words,D=v.words,O=_.words;A=d=U[0],b=f=U[1],I=k=U[2],T=S=U[3],w=m=U[4];for(var P,c=0;c<80;c+=1)P=d+e[i+B[c]]|0,P+=c<16?t(f,k,S)+E[0]:c<32?r(f,k,S)+E[1]:c<48?n(f,k,S)+E[2]:c<64?o(f,k,S)+E[3]:a(f,k,S)+E[4],P|=0,P=s(P,D[c]),P=P+m|0,d=m,m=S,S=s(k,10),k=f,f=P,P=A+e[i+x[c]]|0,P+=c<16?a(b,I,T)+C[0]:c<32?o(b,I,T)+C[1]:c<48?n(b,I,T)+C[2]:c<64?r(b,I,T)+C[3]:t(b,I,T)+C[4],P|=0,P=s(P,O[c]),P=P+w|0,A=w,w=T,T=s(I,10),I=b,b=P;P=U[1]+k+T|0,U[1]=U[2]+S+w|0,U[2]=U[3]+m+A|0,U[3]=U[4]+d+b|0,U[4]=U[0]+f+I|0,U[0]=P},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;t[i>>>5]|=128<<24-i%32,t[14+(i+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var n=this._hash,o=n.words,a=0;a<5;a++){var s=o[a];o[a]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return n},clone:function(){var e=d.clone.call(this);return e._hash=this._hash.clone(),e}});c.RIPEMD160=d._createHelper(k),c.HmacRIPEMD160=d._createHmacHelper(k)}(Math),function(){var e=i,t=e.lib,r=t.Base,n=e.enc,o=n.Utf8,a=e.algo;a.HMAC=r.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var r=e.blockSize,i=4*r;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var n=this._oKey=t.clone(),a=this._iKey=t.clone(),s=n.words,c=a.words,u=0;u<r;u++)s[u]^=1549556828,c[u]^=909522486;n.sigBytes=a.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})}(),function(){var e=i,t=e.lib,r=t.Base,n=t.WordArray,o=e.algo,a=o.SHA1,s=o.HMAC,c=o.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:a,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,i=s.create(r.hasher,e),o=n.create(),a=n.create([1]),c=o.words,u=a.words,l=r.keySize,d=r.iterations;c.length<l;){var f=i.update(t).finalize(a);i.reset();for(var h=f.words,p=h.length,v=f,_=1;_<d;_++){v=i.finalize(v),i.reset();for(var y=v.words,g=0;g<p;g++)h[g]^=y[g]}o.concat(f),u[0]++}return o.sigBytes=4*l,o}});e.PBKDF2=function(e,t,r){return c.create(r).compute(e,t)}}(),function(){var e=i,t=e.lib,r=t.Base,n=t.WordArray,o=e.algo,a=o.MD5,s=o.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:a,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,i=r.hasher.create(),o=n.create(),a=o.words,s=r.keySize,c=r.iterations;a.length<s;){u&&i.update(u);var u=i.update(e).finalize(t);i.reset();for(var l=1;l<c;l++)u=i.finalize(u),i.reset();o.concat(u)}return o.sigBytes=4*s,o}});e.EvpKDF=function(e,t,r){return s.create(r).compute(e,t)}}(),function(){var e=i,t=e.lib,r=t.WordArray,n=e.algo,o=n.SHA256,a=n.SHA224=o.extend({_doReset:function(){this._hash=new r.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=o._doFinalize.call(this);return e.sigBytes-=4,e}});e.SHA224=o._createHelper(a),e.HmacSHA224=o._createHmacHelper(a)}(),function(e){var t=i,r=t.lib,n=r.Base,o=r.WordArray,a=t.x64={};a.Word=n.extend({init:function(e,t){this.high=e,this.low=t}}),a.WordArray=n.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],i=0;i<t;i++){var n=e[i];r.push(n.high),r.push(n.low)}return o.create(r,this.sigBytes)},clone:function(){for(var e=n.clone.call(this),t=e.words=this.words.slice(0),r=t.length,i=0;i<r;i++)t[i]=t[i].clone();return e}})}(),function(e){var t=i,r=t.lib,n=r.WordArray,o=r.Hasher,a=t.x64,s=a.Word,c=t.algo,u=[],l=[],d=[];!function(){for(var e=1,t=0,r=0;r<24;r++){u[e+5*t]=(r+1)*(r+2)/2%64;var i=t%5,n=(2*e+3*t)%5;e=i,t=n}for(var e=0;e<5;e++)for(var t=0;t<5;t++)l[e+5*t]=t+(2*e+3*t)%5*5;for(var o=1,a=0;a<24;a++){for(var c=0,f=0,h=0;h<7;h++){if(1&o){var p=(1<<h)-1;p<32?f^=1<<p:c^=1<<p-32}128&o?o=o<<1^113:o<<=1}d[a]=s.create(c,f)}}();var f=[];!function(){for(var e=0;e<25;e++)f[e]=s.create()}();var h=c.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,i=this.blockSize/2,n=0;n<i;n++){var o=e[t+2*n],a=e[t+2*n+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8);var s=r[n];s.high^=a,s.low^=o}for(var c=0;c<24;c++){for(var h=0;h<5;h++){for(var p=0,v=0,_=0;_<5;_++){var s=r[h+5*_];p^=s.high,v^=s.low}var y=f[h];y.high=p,y.low=v}for(var h=0;h<5;h++)for(var g=f[(h+4)%5],k=f[(h+1)%5],S=k.high,m=k.low,p=g.high^(S<<1|m>>>31),v=g.low^(m<<1|S>>>31),_=0;_<5;_++){var s=r[h+5*_];s.high^=p,s.low^=v}for(var A=1;A<25;A++){var s=r[A],b=s.high,I=s.low,T=u[A];if(T<32)var p=b<<T|I>>>32-T,v=I<<T|b>>>32-T;else var p=I<<T-32|b>>>64-T,v=b<<T-32|I>>>64-T;var w=f[l[A]];w.high=p,w.low=v}var U=f[0],E=r[0];U.high=E.high,U.low=E.low;for(var h=0;h<5;h++)for(var _=0;_<5;_++){var A=h+5*_,s=r[A],C=f[A],B=f[(h+1)%5+5*_],x=f[(h+2)%5+5*_];s.high=C.high^~B.high&x.high,s.low=C.low^~B.low&x.low}var s=r[0],D=d[c];s.high^=D.high,s.low^=D.low}},_doFinalize:function(){var t=this._data,r=t.words,i=(this._nDataBytes,8*t.sigBytes),o=32*this.blockSize;r[i>>>5]|=1<<24-i%32,r[(e.ceil((i+1)/o)*o>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var a=this._state,s=this.cfg.outputLength/8,c=s/8,u=[],l=0;l<c;l++){var d=a[l],f=d.high,h=d.low;f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),u.push(h),u.push(f)}return new n.init(u,s)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});t.SHA3=o._createHelper(h),t.HmacSHA3=o._createHmacHelper(h)}(Math),function(){function e(){return a.create.apply(a,arguments)}var t=i,r=t.lib,n=r.Hasher,o=t.x64,a=o.Word,s=o.WordArray,c=t.algo,u=[e(1116352408,3609767458),e(1899447441,602891725),e(3049323471,3964484399),e(3921009573,2173295548),e(961987163,4081628472),e(1508970993,3053834265),e(2453635748,2937671579),e(2870763221,3664609560),e(3624381080,2734883394),e(310598401,1164996542),e(607225278,1323610764),e(1426881987,3590304994),e(1925078388,4068182383),e(2162078206,991336113),e(2614888103,633803317),e(3248222580,3479774868),e(3835390401,2666613458),e(4022224774,944711139),e(264347078,2341262773),e(604807628,2007800933),e(770255983,1495990901),e(1249150122,1856431235),e(1555081692,3175218132),e(1996064986,2198950837),e(2554220882,3999719339),e(2821834349,766784016),e(2952996808,2566594879),e(3210313671,3203337956),e(3336571891,1034457026),e(3584528711,2466948901),e(113926993,3758326383),e(338241895,168717936),e(666307205,1188179964),e(773529912,1546045734),e(1294757372,1522805485),e(1396182291,2643833823),e(1695183700,2343527390),e(1986661051,1014477480),e(2177026350,1206759142),e(2456956037,344077627),e(2730485921,1290863460),e(2820302411,3158454273),e(3259730800,3505952657),e(3345764771,106217008),e(3516065817,3606008344),e(3600352804,1432725776),e(4094571909,1467031594),e(275423344,851169720),e(430227734,3100823752),e(506948616,1363258195),e(659060556,3750685593),e(883997877,3785050280),e(958139571,3318307427),e(1322822218,3812723403),e(1537002063,2003034995),e(1747873779,3602036899),e(1955562222,1575990012),e(2024104815,1125592928),e(2227730452,2716904306),e(2361852424,442776044),e(2428436474,593698344),e(2756734187,3733110249),e(3204031479,2999351573),e(3329325298,3815920427),e(3391569614,3928383900),e(3515267271,566280711),e(3940187606,3454069534),e(4118630271,4000239992),e(116418474,1914138554),e(174292421,2731055270),e(289380356,3203993006),e(460393269,320620315),e(685471733,587496836),e(852142971,1086792851),e(1017036298,365543100),e(1126000580,2618297676),e(1288033470,3409855158),e(1501505948,4234509866),e(1607167915,987167468),e(1816402316,1246189591)],l=[];!function(){for(var t=0;t<80;t++)l[t]=e()}();var d=c.SHA512=n.extend({_doReset:function(){this._hash=new s.init([new a.init(1779033703,4089235720),new a.init(3144134277,2227873595),new a.init(1013904242,4271175723),new a.init(2773480762,1595750129),new a.init(1359893119,2917565137),new a.init(2600822924,725511199),new a.init(528734635,4215389547),new a.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],a=r[3],s=r[4],c=r[5],d=r[6],f=r[7],h=i.high,p=i.low,v=n.high,_=n.low,y=o.high,g=o.low,k=a.high,S=a.low,m=s.high,A=s.low,b=c.high,I=c.low,T=d.high,w=d.low,U=f.high,E=f.low,C=h,B=p,x=v,D=_,O=y,P=g,L=k,M=S,z=m,H=A,R=b,F=I,j=T,N=w,K=U,V=E,W=0;W<80;W++){var q=l[W];if(W<16)var J=q.high=0|e[t+2*W],X=q.low=0|e[t+2*W+1];else{var G=l[W-15],Z=G.high,Y=G.low,Q=(Z>>>1|Y<<31)^(Z>>>8|Y<<24)^Z>>>7,$=(Y>>>1|Z<<31)^(Y>>>8|Z<<24)^(Y>>>7|Z<<25),ee=l[W-2],te=ee.high,re=ee.low,ie=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ne=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),oe=l[W-7],ae=oe.high,se=oe.low,ce=l[W-16],ue=ce.high,le=ce.low,X=$+se,J=Q+ae+(X>>>0<$>>>0?1:0),X=X+ne,J=J+ie+(X>>>0<ne>>>0?1:0),X=X+le,J=J+ue+(X>>>0<le>>>0?1:0);q.high=J,q.low=X}var de=z&R^~z&j,fe=H&F^~H&N,he=C&x^C&O^x&O,pe=B&D^B&P^D&P,ve=(C>>>28|B<<4)^(C<<30|B>>>2)^(C<<25|B>>>7),_e=(B>>>28|C<<4)^(B<<30|C>>>2)^(B<<25|C>>>7),ye=(z>>>14|H<<18)^(z>>>18|H<<14)^(z<<23|H>>>9),ge=(H>>>14|z<<18)^(H>>>18|z<<14)^(H<<23|z>>>9),ke=u[W],Se=ke.high,me=ke.low,Ae=V+ge,be=K+ye+(Ae>>>0<V>>>0?1:0),Ae=Ae+fe,be=be+de+(Ae>>>0<fe>>>0?1:0),Ae=Ae+me,be=be+Se+(Ae>>>0<me>>>0?1:0),Ae=Ae+X,be=be+J+(Ae>>>0<X>>>0?1:0),Ie=_e+pe,Te=ve+he+(Ie>>>0<_e>>>0?1:0);K=j,V=N,j=R,N=F,R=z,F=H,H=M+Ae|0,z=L+be+(H>>>0<M>>>0?1:0)|0,L=O,M=P,O=x,P=D,x=C,D=B,B=Ae+Ie|0,C=be+Te+(B>>>0<Ae>>>0?1:0)|0}p=i.low=p+B,i.high=h+C+(p>>>0<B>>>0?1:0),_=n.low=_+D,n.high=v+x+(_>>>0<D>>>0?1:0),g=o.low=g+P,o.high=y+O+(g>>>0<P>>>0?1:0),S=a.low=S+M,a.high=k+L+(S>>>0<M>>>0?1:0),A=s.low=A+H,s.high=m+z+(A>>>0<H>>>0?1:0),I=c.low=I+F,c.high=b+R+(I>>>0<F>>>0?1:0),w=d.low=w+N,d.high=T+j+(w>>>0<N>>>0?1:0),E=f.low=E+V,f.high=U+K+(E>>>0<V>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[30+(i+128>>>10<<5)]=Math.floor(r/4294967296),t[31+(i+128>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});t.SHA512=n._createHelper(d),t.HmacSHA512=n._createHmacHelper(d)}(),function(){var e=i,t=e.x64,r=t.Word,n=t.WordArray,o=e.algo,a=o.SHA512,s=o.SHA384=a.extend({_doReset:function(){this._hash=new n.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var e=a._doFinalize.call(this);return e.sigBytes-=16,e}});e.SHA384=a._createHelper(s),e.HmacSHA384=a._createHmacHelper(s)}(),i.lib.Cipher||function(e){var t=i,r=t.lib,n=r.Base,o=r.WordArray,a=r.BufferedBlockAlgorithm,s=t.enc,c=(s.Utf8,s.Base64),u=t.algo,l=u.EvpKDF,d=r.Cipher=a.extend({cfg:n.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){a.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?b:S}return function(t){return{encrypt:function(r,i,n){return e(i).encrypt(t,r,i,n)},decrypt:function(r,i,n){return e(i).decrypt(t,r,i,n)}}}}()}),f=(r.StreamCipher=d.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),t.mode={}),h=r.BlockCipherMode=n.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=f.CBC=function(){function t(t,r,i){var n=this._iv;if(n){var o=n;this._iv=e}else var o=this._prevBlock;for(var a=0;a<i;a++)t[r+a]^=o[a]}var r=h.extend();return r.Encryptor=r.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize;t.call(this,e,r,n),i.encryptBlock(e,r),this._prevBlock=e.slice(r,r+n)}}),r.Decryptor=r.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize,o=e.slice(r,r+n);i.decryptBlock(e,r),t.call(this,e,r,n),this._prevBlock=o}}),r}(),v=t.pad={},_=v.Pkcs7={pad:function(e,t){for(var r=4*t,i=r-e.sigBytes%r,n=i<<24|i<<16|i<<8|i,a=[],s=0;s<i;s+=4)a.push(n);var c=o.create(a,i);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},y=(r.BlockCipher=d.extend({cfg:d.cfg.extend({mode:p,padding:_}),reset:function(){d.reset.call(this);var e=this.cfg,t=e.iv,r=e.mode;if(this._xformMode==this._ENC_XFORM_MODE)var i=r.createEncryptor;else{var i=r.createDecryptor;this._minBufferSize=1}this._mode&&this._mode.__creator==i?this._mode.init(this,t&&t.words):(this._mode=i.call(r,this,t&&t.words),this._mode.__creator=i)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){e.pad(this._data,this.blockSize);var t=this._process(!0)}else{var t=this._process(!0);e.unpad(t)}return t},blockSize:4}),r.CipherParams=n.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}})),g=t.format={},k=g.OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;if(r)var i=o.create([1398893684,1701076831]).concat(r).concat(t);else var i=t;return i.toString(c)},parse:function(e){var t=c.parse(e),r=t.words;if(1398893684==r[0]&&1701076831==r[1]){var i=o.create(r.slice(2,4));r.splice(0,4),t.sigBytes-=16}return y.create({ciphertext:t,salt:i})}},S=r.SerializableCipher=n.extend({cfg:n.extend({format:k}),encrypt:function(e,t,r,i){i=this.cfg.extend(i);var n=e.createEncryptor(r,i),o=n.finalize(t),a=n.cfg;return y.create({ciphertext:o,key:r,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:i.format})},decrypt:function(e,t,r,i){return i=this.cfg.extend(i),t=this._parse(t,i.format),e.createDecryptor(r,i).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),m=t.kdf={},A=m.OpenSSL={execute:function(e,t,r,i){i||(i=o.random(8));var n=l.create({keySize:t+r}).compute(e,i),a=o.create(n.words.slice(t),4*r);return n.sigBytes=4*t,y.create({key:n,iv:a,salt:i})}},b=r.PasswordBasedCipher=S.extend({cfg:S.cfg.extend({kdf:A}),encrypt:function(e,t,r,i){i=this.cfg.extend(i);var n=i.kdf.execute(r,e.keySize,e.ivSize);i.iv=n.iv;var o=S.encrypt.call(this,e,t,n.key,i);return o.mixIn(n),o},decrypt:function(e,t,r,i){i=this.cfg.extend(i),t=this._parse(t,i.format);var n=i.kdf.execute(r,e.keySize,e.ivSize,t.salt);return i.iv=n.iv,S.decrypt.call(this,e,t,n.key,i)}})}(),i.mode.CFB=function(){function e(e,t,r,i){var n=this._iv;if(n){var o=n.slice(0);this._iv=void 0}else var o=this._prevBlock;i.encryptBlock(o,0);for(var a=0;a<r;a++)e[t+a]^=o[a]}var t=i.lib.BlockCipherMode.extend();return t.Encryptor=t.extend({processBlock:function(t,r){var i=this._cipher,n=i.blockSize;e.call(this,t,r,n,i),this._prevBlock=t.slice(r,r+n)}}),t.Decryptor=t.extend({processBlock:function(t,r){var i=this._cipher,n=i.blockSize,o=t.slice(r,r+n);e.call(this,t,r,n,i),this._prevBlock=o}}),t}(),i.mode.ECB=function(){var e=i.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),e.Decryptor=e.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),e}(),i.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,i=4*t,n=i-r%i,o=r+n-1;e.clamp(),e.words[o>>>2]|=n<<24-o%4*8,e.sigBytes+=n},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},i.pad.Iso10126={pad:function(e,t){var r=4*t,n=r-e.sigBytes%r;e.concat(i.lib.WordArray.random(n-1)).concat(i.lib.WordArray.create([n<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},i.pad.Iso97971={pad:function(e,t){e.concat(i.lib.WordArray.create([2147483648],1)),i.pad.ZeroPadding.pad(e,t)},unpad:function(e){i.pad.ZeroPadding.unpad(e),e.sigBytes--}},i.mode.OFB=function(){var e=i.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,o=this._keystream;n&&(o=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var a=0;a<i;a++)e[t+a]^=o[a]}});return e.Decryptor=t,e}(),i.pad.NoPadding={pad:function(){},unpad:function(){}},function(e){var t=i,r=t.lib,n=r.CipherParams,o=t.enc,a=o.Hex,s=t.format;s.Hex={stringify:function(e){return e.ciphertext.toString(a)},parse:function(e){var t=a.parse(e);return n.create({ciphertext:t})}}}(),function(){var e=i,t=e.lib,r=t.BlockCipher,n=e.algo,o=[],a=[],s=[],c=[],u=[],l=[],d=[],f=[],h=[],p=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;for(var r=0,i=0,t=0;t<256;t++){var n=i^i<<1^i<<2^i<<3^i<<4;n=n>>>8^255&n^99,o[r]=n,a[n]=r;var v=e[r],_=e[v],y=e[_],g=257*e[n]^16843008*n;s[r]=g<<24|g>>>8,c[r]=g<<16|g>>>16,u[r]=g<<8|g>>>24,l[r]=g;var g=16843009*y^65537*_^257*v^16843008*r;d[n]=g<<24|g>>>8,f[n]=g<<16|g>>>16,h[n]=g<<8|g>>>24,p[n]=g,r?(r=v^e[e[e[y^v]]],i^=e[e[i]]):r=i=1}}();var v=[0,1,2,4,8,16,32,64,128,27,54],_=n.AES=r.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,i=this._nRounds=r+6,n=4*(i+1),a=this._keySchedule=[],s=0;s<n;s++)if(s<r)a[s]=t[s];else{var c=a[s-1];s%r?r>6&&s%r==4&&(c=o[c>>>24]<<24|o[c>>>16&255]<<16|o[c>>>8&255]<<8|o[255&c]):(c=c<<8|c>>>24,c=o[c>>>24]<<24|o[c>>>16&255]<<16|o[c>>>8&255]<<8|o[255&c],c^=v[s/r|0]<<24),a[s]=a[s-r]^c}for(var u=this._invKeySchedule=[],l=0;l<n;l++){var s=n-l;if(l%4)var c=a[s];else var c=a[s-4];u[l]=l<4||s<=4?c:d[o[c>>>24]]^f[o[c>>>16&255]]^h[o[c>>>8&255]]^p[o[255&c]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,c,u,l,o)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,d,f,h,p,a);var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,i,n,o,a,s){for(var c=this._nRounds,u=e[t]^r[0],l=e[t+1]^r[1],d=e[t+2]^r[2],f=e[t+3]^r[3],h=4,p=1;p<c;p++){var v=i[u>>>24]^n[l>>>16&255]^o[d>>>8&255]^a[255&f]^r[h++],_=i[l>>>24]^n[d>>>16&255]^o[f>>>8&255]^a[255&u]^r[h++],y=i[d>>>24]^n[f>>>16&255]^o[u>>>8&255]^a[255&l]^r[h++],g=i[f>>>24]^n[u>>>16&255]^o[l>>>8&255]^a[255&d]^r[h++];u=v,l=_,d=y,f=g}var v=(s[u>>>24]<<24|s[l>>>16&255]<<16|s[d>>>8&255]<<8|s[255&f])^r[h++],_=(s[l>>>24]<<24|s[d>>>16&255]<<16|s[f>>>8&255]<<8|s[255&u])^r[h++],y=(s[d>>>24]<<24|s[f>>>16&255]<<16|s[u>>>8&255]<<8|s[255&l])^r[h++],g=(s[f>>>24]<<24|s[u>>>16&255]<<16|s[l>>>8&255]<<8|s[255&d])^r[h++];e[t]=v,e[t+1]=_,e[t+2]=y,e[t+3]=g},keySize:8});e.AES=r._createHelper(_)}(),function(){function e(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function t(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}var r=i,n=r.lib,o=n.WordArray,a=n.BlockCipher,s=r.algo,c=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],u=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],l=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],d=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],f=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],h=s.DES=a.extend({_doReset:function(){for(var e=this._key,t=e.words,r=[],i=0;i<56;i++){var n=c[i]-1;r[i]=t[n>>>5]>>>31-n%32&1}for(var o=this._subKeys=[],a=0;a<16;a++){for(var s=o[a]=[],d=l[a],i=0;i<24;i++)s[i/6|0]|=r[(u[i]-1+d)%28]<<31-i%6,s[4+(i/6|0)]|=r[28+(u[i+24]-1+d)%28]<<31-i%6;s[0]=s[0]<<1|s[0]>>>31;for(var i=1;i<7;i++)s[i]=s[i]>>>4*(i-1)+3;s[7]=s[7]<<5|s[7]>>>27}for(var f=this._invSubKeys=[],i=0;i<16;i++)f[i]=o[15-i]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(r,i,n){this._lBlock=r[i],this._rBlock=r[i+1],e.call(this,4,252645135),e.call(this,16,65535),t.call(this,2,858993459),t.call(this,8,16711935),e.call(this,1,1431655765);for(var o=0;o<16;o++){for(var a=n[o],s=this._lBlock,c=this._rBlock,u=0,l=0;l<8;l++)u|=d[l][((c^a[l])&f[l])>>>0];this._lBlock=c,this._rBlock=s^u}var h=this._lBlock;this._lBlock=this._rBlock,this._rBlock=h,e.call(this,1,1431655765),t.call(this,8,16711935),t.call(this,2,858993459),e.call(this,16,65535),e.call(this,4,252645135),r[i]=this._lBlock,r[i+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});r.DES=a._createHelper(h);var p=s.TripleDES=a.extend({_doReset:function(){var e=this._key,t=e.words;this._des1=h.createEncryptor(o.create(t.slice(0,2))),this._des2=h.createEncryptor(o.create(t.slice(2,4))),this._des3=h.createEncryptor(o.create(t.slice(4,6)))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});r.TripleDES=a._createHelper(p)}(),function(){function e(){for(var e=this._S,t=this._i,r=this._j,i=0,n=0;n<4;n++){t=(t+1)%256,r=(r+e[t])%256;var o=e[t];e[t]=e[r],e[r]=o,i|=e[(e[t]+e[r])%256]<<24-8*n}return this._i=t,this._j=r,i}var t=i,r=t.lib,n=r.StreamCipher,o=t.algo,a=o.RC4=n.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,i=this._S=[],n=0;n<256;n++)i[n]=n;for(var n=0,o=0;n<256;n++){var a=n%r,s=t[a>>>2]>>>24-a%4*8&255;o=(o+i[n]+s)%256;var c=i[n];i[n]=i[o],i[o]=c}this._i=this._j=0},_doProcessBlock:function(t,r){t[r]^=e.call(this)},keySize:8,ivSize:0});t.RC4=n._createHelper(a);var s=o.RC4Drop=a.extend({cfg:a.cfg.extend({drop:192}),_doReset:function(){a._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)e.call(this)}});t.RC4Drop=n._createHelper(s)}(),/** @preserve
 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
 * derived from CryptoJS.mode.CTR
 * <NAME_EMAIL>
 */
i.mode.CTRGladman=function(){function e(e){if(255==(e>>24&255)){var t=e>>16&255,r=e>>8&255,i=255&e;255===t?(t=0,255===r?(r=0,255===i?i=0:++i):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=i}else e+=1<<24;return e}function t(t){return 0===(t[0]=e(t[0]))&&(t[1]=e(t[1])),t}var r=i.lib.BlockCipherMode.extend(),n=r.Encryptor=r.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize,o=this._iv,a=this._counter;o&&(a=this._counter=o.slice(0),this._iv=void 0),t(a);var s=a.slice(0);i.encryptBlock(s,0);for(var c=0;c<n;c++)e[r+c]^=s[c]}});return r.Decryptor=n,r}(),function(){function e(){for(var e=this._X,t=this._C,r=0;r<8;r++)s[r]=t[r];t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<s[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<s[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<s[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<s[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<s[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<s[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<s[6]>>>0?1:0)|0,this._b=t[7]>>>0<s[7]>>>0?1:0;for(var r=0;r<8;r++){var i=e[r]+t[r],n=65535&i,o=i>>>16,a=((n*n>>>17)+n*o>>>15)+o*o,u=((4294901760&i)*i|0)+((65535&i)*i|0);c[r]=a^u}e[0]=c[0]+(c[7]<<16|c[7]>>>16)+(c[6]<<16|c[6]>>>16)|0,e[1]=c[1]+(c[0]<<8|c[0]>>>24)+c[7]|0,e[2]=c[2]+(c[1]<<16|c[1]>>>16)+(c[0]<<16|c[0]>>>16)|0,e[3]=c[3]+(c[2]<<8|c[2]>>>24)+c[1]|0,e[4]=c[4]+(c[3]<<16|c[3]>>>16)+(c[2]<<16|c[2]>>>16)|0,e[5]=c[5]+(c[4]<<8|c[4]>>>24)+c[3]|0,e[6]=c[6]+(c[5]<<16|c[5]>>>16)+(c[4]<<16|c[4]>>>16)|0,e[7]=c[7]+(c[6]<<8|c[6]>>>24)+c[5]|0}var t=i,r=t.lib,n=r.StreamCipher,o=t.algo,a=[],s=[],c=[],u=o.Rabbit=n.extend({_doReset:function(){for(var t=this._key.words,r=this.cfg.iv,i=0;i<4;i++)t[i]=16711935&(t[i]<<8|t[i]>>>24)|4278255360&(t[i]<<24|t[i]>>>8);var n=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],o=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var i=0;i<4;i++)e.call(this);for(var i=0;i<8;i++)o[i]^=n[i+4&7];if(r){var a=r.words,s=a[0],c=a[1],u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),d=u>>>16|4294901760&l,f=l<<16|65535&u;o[0]^=u,o[1]^=d,o[2]^=l,o[3]^=f,o[4]^=u,o[5]^=d,o[6]^=l,o[7]^=f;for(var i=0;i<4;i++)e.call(this)}},_doProcessBlock:function(t,r){var i=this._X;e.call(this),a[0]=i[0]^i[5]>>>16^i[3]<<16,a[1]=i[2]^i[7]>>>16^i[5]<<16,a[2]=i[4]^i[1]>>>16^i[7]<<16,a[3]=i[6]^i[3]>>>16^i[1]<<16;for(var n=0;n<4;n++)a[n]=16711935&(a[n]<<8|a[n]>>>24)|4278255360&(a[n]<<24|a[n]>>>8),t[r+n]^=a[n]},blockSize:4,ivSize:2});t.Rabbit=n._createHelper(u)}(),i.mode.CTR=function(){var e=i.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,o=this._counter;n&&(o=this._counter=n.slice(0),this._iv=void 0);var a=o.slice(0);r.encryptBlock(a,0),o[i-1]=o[i-1]+1|0;for(var s=0;s<i;s++)e[t+s]^=a[s]}});return e.Decryptor=t,e}(),function(){function e(){for(var e=this._X,t=this._C,r=0;r<8;r++)s[r]=t[r];t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<s[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<s[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<s[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<s[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<s[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<s[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<s[6]>>>0?1:0)|0,this._b=t[7]>>>0<s[7]>>>0?1:0;for(var r=0;r<8;r++){var i=e[r]+t[r],n=65535&i,o=i>>>16,a=((n*n>>>17)+n*o>>>15)+o*o,u=((4294901760&i)*i|0)+((65535&i)*i|0);c[r]=a^u}e[0]=c[0]+(c[7]<<16|c[7]>>>16)+(c[6]<<16|c[6]>>>16)|0,e[1]=c[1]+(c[0]<<8|c[0]>>>24)+c[7]|0,e[2]=c[2]+(c[1]<<16|c[1]>>>16)+(c[0]<<16|c[0]>>>16)|0,e[3]=c[3]+(c[2]<<8|c[2]>>>24)+c[1]|0,e[4]=c[4]+(c[3]<<16|c[3]>>>16)+(c[2]<<16|c[2]>>>16)|0,e[5]=c[5]+(c[4]<<8|c[4]>>>24)+c[3]|0,e[6]=c[6]+(c[5]<<16|c[5]>>>16)+(c[4]<<16|c[4]>>>16)|0,e[7]=c[7]+(c[6]<<8|c[6]>>>24)+c[5]|0}var t=i,r=t.lib,n=r.StreamCipher,o=t.algo,a=[],s=[],c=[],u=o.RabbitLegacy=n.extend({_doReset:function(){var t=this._key.words,r=this.cfg.iv,i=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],n=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var o=0;o<4;o++)e.call(this);for(var o=0;o<8;o++)n[o]^=i[o+4&7];if(r){var a=r.words,s=a[0],c=a[1],u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),d=u>>>16|4294901760&l,f=l<<16|65535&u;n[0]^=u,n[1]^=d,n[2]^=l,n[3]^=f,n[4]^=u,n[5]^=d,n[6]^=l,n[7]^=f;for(var o=0;o<4;o++)e.call(this)}},_doProcessBlock:function(t,r){var i=this._X;e.call(this),a[0]=i[0]^i[5]>>>16^i[3]<<16,a[1]=i[2]^i[7]>>>16^i[5]<<16,a[2]=i[4]^i[1]>>>16^i[7]<<16,a[3]=i[6]^i[3]>>>16^i[1]<<16;for(var n=0;n<4;n++)a[n]=16711935&(a[n]<<8|a[n]>>>24)|4278255360&(a[n]<<24|a[n]>>>8),t[r+n]^=a[n]},blockSize:4,ivSize:2});t.RabbitLegacy=n._createHelper(u)}(),i.pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){for(var t=e.words,r=e.sigBytes-1;!(t[r>>>2]>>>24-r%4*8&255);)r--;e.sigBytes=r+1}},t.default=i},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=function(){function e(){i(this,e)}return n(e,null,[{key:"get",value:function(e,t,r,i,n){r=r||function(){},wx.request({url:e,success:function(e){200===e.statusCode?t(e):r(e)},fail:function(e){r(e)}})}}]),e}();t.default=o},function(e,t,r){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),a=r(3),s=i(a),c=r(13),u=i(c),l=r(4),d=i(l),f=r(5),h=i(f),p=r(1),v=(i(p),function(){function e(t){n(this,e);var r=h.default.os.name,i=h.default.os.version||"",o=h.default.browser.name,a=h.default.browser.version||"",c=0;wx.canIUse("getAccountInfoSync")&&(c=wx.getAccountInfoSync().miniProgram.appId);var u="pc";h.default.os.ipad?u="pad":(h.default.os.iphone||h.default.os.android)&&(u="phone"),this._ri=s.default.create(),this.initParam={APIVersion:"0.6.0",lv:"1",av:d.default.version,pd:"upload",sm:"upload",md:"uploader",uuid:e.getUuid(),os:r,ov:i,et:o,ev:a,uat:JSON.stringify(wx.getSystemInfoSync()),app_n:c,tt:u,dm:"h5",ut:""}}return o(e,[{key:"log",value:function(e,t){}}],[{key:"getUuid",value:function(){var e=u.default.get("p_h5_upload_u");return e||(e=s.default.create(),u.default.set("p_h5_upload_u",e,730)),e}},{key:"getClientId",value:function(){return u.default.get("p_h5_upload_clientId")}},{key:"setClientId",value:function(e){return e||(e=s.default.create()),u.default.set("p_h5_upload_clientId",e,730),e}}]),e}());t.default=v},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=function(){function e(){i(this,e)}return n(e,null,[{key:"create",value:function(e,t){var r,i="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),n=[];if(t=t||i.length,e)for(r=0;r<e;r++)n[r]=i[0|Math.random()*t];else{var o;for(n[8]=n[13]=n[18]=n[23]="-",n[14]="4",r=0;r<36;r++)n[r]||(o=0|16*Math.random(),n[r]=i[19==r?3&o|8:o])}return n.join("")}}]),e}();t.default=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={version:"1.0.1"};t.default=i},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=function(){var e={},t={},r=wx.getSystemInfoSync(),i=r.system;return i.indexOf("Android")>-1?(e.android=!0,e.name="Android"):i.indexOf("iOS")>-1&&(e.ios=!0,e.name="iOS"),t.version=i.version,t.name="miniprogram",t.miniprogram=!0,{os:e,browser:t}}(),a=function(){function e(){i(this,e)}return n(e,null,[{key:"getHost",value:function(e){var t="";if(void 0===e||null==e||""==e)return"";var r=e.indexOf("//"),i=e;r>-1&&(i=e.substring(r+2));var t=i,n=i.split("/");return n&&n.length>0&&(t=n[0]),n=t.split(":"),n&&n.length>0&&(t=n[0]),t}},{key:"os",get:function(){return o.os}},{key:"browser",get:function(){var e=o.browser;return e.name||(e.name="miniprogram"),e}}]),e}();t.default=a},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=function(){function e(){i(this,e),this.randomString=function(e){e=e||32;for(var t="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",r=t.length,i="",n=0;n<e;n++)i+=t.charAt(Math.floor(Math.random()*r));return i}}return n(e,null,[{key:"detectIEVersion",value:function(){for(var e=4,t=document.createElement("div"),r=t.getElementsByTagName("i");t.innerHTML="\x3c!--[if gt IE "+e+"]><i></i><![endif]--\x3e",r[0];)e++;return e>4&&e}},{key:"extend",value:function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&t[r]&&(e[r]=t[r])}},{key:"isArray",value:function(e){return"[object Array]"===Object.prototype.toString.call(arg)}},{key:"getFileType",value:function(e){return e=e.toLowerCase(),/.mp4|.flv|.m3u8|.avi|.rm|.rmvb|.mpeg|.mpg|.mov|.wmv|.3gp|.asf|.dat|.dv|.f4v|.gif|.m2t|.m4v|.mj2|.mjpeg|.mpe|.mts|.ogg|.qt|.swf|.ts|.vob|.wmv|.webm/.test(e)?"video":/.mp3|.wav|.ape|.cda|.au|.midi|.mac|.aac|.ac3|.acm|.amr|.caf|.flac|.m4a|.ra|.wma/.test(e)?"audio":/.bmp|.jpg|.jpeg|.png|.gif/.test(e)?"img":"other"}},{key:"isImage",value:function(e){return e=e.toLowerCase(),!!/.jpg|.jpeg|.png|.gif/.test(e)}},{key:"ISODateString",value:function(e){function t(e){return e<10?"0"+e:e}return e.getUTCFullYear()+"-"+t(e.getUTCMonth()+1)+"-"+t(e.getUTCDate())+"T"+t(e.getUTCHours())+":"+t(e.getUTCMinutes())+":"+t(e.getUTCSeconds())+"Z"}},{key:"isIntNum",value:function(e){return!!/^\d+$/.test(e)}}]),e}();t.default=o},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=r(0),a=function(e){return e&&e.__esModule?e:{default:e}}(o),s=function(){function e(){i(this,e)}return n(e,null,[{key:"randomUUID",value:function(){for(var e=[],t="0123456789abcdef",r=0;r<36;r++)e[r]=t.substr(Math.floor(16*Math.random()),1);return e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-",e.join("")}},{key:"aliyunEncodeURI",value:function(e){var t=encodeURIComponent(e);return t=t.replace(/\+/g,"%20").replace(/\*/g,"%2A").replace(/%7E/g,"~").replace(/!/g,"%21").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/'/g,"%27")}},{key:"makeUTF8sort",value:function(t,r,i){if(!t)throw new Error("PrismPlayer Error: vid should not be null!");var n=[];for(var o in t)n.push(o);for(var a=n.sort(),s="",c=a.length,o=0;o<c;o++){var u=e.aliyunEncodeURI(a[o]),l=e.aliyunEncodeURI(t[a[o]]);""===s?s=u+r+l:s+=i+u+r+l}return s}},{key:"makeChangeSiga",value:function(t,r){if(!t)throw new Error("PrismPlayer Error: vid should not be null!");var i="GET&"+e.aliyunEncodeURI("/")+"&"+e.aliyunEncodeURI(e.makeUTF8sort(t,"=","&")),n=r+"&";return a.default.enc.Base64.stringify(a.default.HmacSHA1(i,n))}}]),e}();t.default=s},function(e,t,r){e.exports=r(9)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(10),n=function(e){return e&&e.__esModule?e:{default:e}}(i);t.default=n.default},function(e,t,r){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),a=r(11),s=r(0),c=i(s),u=r(12),l=i(u),d=r(2),f=i(d),h=r(6),p=i(h),v=r(3),_=i(v),y=r(14),g=i(y),k=r(15),S=(i(k),r(16)),m=i(S),A=r(17),b=i(A),I=function(){function e(t){n(this,e),this.options=t,this.options.region=this.options.region||"cn-shanghai",this.options.userId=this.options.userId||0,this.options.cname=this.options.cname||!1,this.options.localCheckpoint=this.options.localCheckpoint||!1,this.options.enableUploadProgress=this.options.enableUploadProgress||!0,this._ossCreditor=new Object,this._state=a.VODSTATE.INIT,this._uploadList=[],this._curIndex=-1,this._ossUpload=null,this._log=new f.default,this._retryCount=0,this._retryTotal=this.options.retryCount||3,this._retryDuration=this.options.retryDuration||2,this._state=a.VODSTATE.INIT,this._uploadWay="vod",this._onbeforeunload=!1,this._invalidUserId=!1,this._initEvent()}return o(e,[{key:"init",value:function(e,t,r,i){return this._retryCount=0,!(r&&!i||!r&&i)&&(!(e&&!t||!e&&t)&&(this._ossCreditor.accessKeyId=e,this._ossCreditor.accessKeySecret=t,this._ossCreditor.securityToken=r,this._ossCreditor.expireTime=i,!0))}},{key:"addFile",value:function(e,t,r,i,n,o){if(!e)return!1;if(0==e.size)try{this.options.onUploadFailed({file:file},"EmptyFile","文件大小为0，不能上传")}catch(e){console.log(e)}for(var s=(this.options,0);s<this._uploadList.length;s++)if(this._uploadList[s].url==e.url)return!1;var u=new Object;u.url=e.url,u.coverUrl=e.coverUrl,u._endpoint=t,u._bucket=r,u._object=i,u.state=a.UPLOADSTATE.INIT,u.isImage=p.default.isImage(e.url);var l=this,d=u;m.default.getFileInfo(e.url,function(e){d.fileHash=e.digest,d.fileSize=e.size;var t=l._getCheckoutpoint(d);l.options.localCheckpoint||t||l._getCheckoutpointFromCloud(d,function(e){if(e.UploadPoint){var t=JSON.parse(e.UploadPoint);1!=t.loaded&&(d.checkpoint=t.checkpoint,d.loaded=t.loaded,d.videoId=e.VideoId,l._saveCheckoutpoint(d,t.checkpoint))}},function(e){try{if((e=JSON.parse(e))&&"InvalidParameter"==e.Code&&e.Message.indexOf("UserId")>0){l._invalidUserId=!0;var t=e.Message+"，正确账号ID(userId)请参考：https://help.aliyun.com/knowledge_detail/37196.html";console.log(t)}}catch(e){console.log(e)}})}),n&&(u.videoInfo=n?JSON.parse(n).Vod:{},u.userData=c.default.enc.Base64.stringify(c.default.enc.Utf8.parse(n))),u.ri=_.default.create(),this._uploadList.push(u),this._reportLog("20001",u,{ql:this._uploadList.length});try{this.options.addFileSuccess&&this.options.addFileSuccess(u)}catch(e){console.log(e)}return!0}},{key:"deleteFile",value:function(e){return!!this.cancelFile(e)&&(this._uploadList.splice(e,1),!0)}},{key:"cleanList",value:function(){this.stopUpload(),this._uploadList.length=0,this._curIndex=-1}},{key:"cancelFile",value:function(e){this.options;if(e<0||e>=this._uploadList.length)return!1;var t=this._uploadList[e];if(e==this._curIndex&&t.state==a.UPLOADSTATE.UPLOADING){t.state=a.UPLOADSTATE.CANCELED;var r=this._getCheckoutpoint(t);r&&r.checkpoint&&(r=r.checkpoint),r&&this._ossUpload.abort(t),this._removeCheckoutpoint(t),this.nextUpload()}else t.state!=a.UPLOADSTATE.SUCCESS&&(t.state=a.UPLOADSTATE.CANCELED);return this._reportLog("20008",t),!0}},{key:"resumeFile",value:function(e){this.options;if(e<0||e>=this._uploadList.length)return!1;var t=this._uploadList[e];return t.state==a.UPLOADSTATE.CANCELED&&(t.state=a.UPLOADSTATE.INIT,!0)}},{key:"listFiles",value:function(){return this._uploadList}},{key:"getCheckpoint",value:function(e){return this._getCheckoutpoint({file:e})}},{key:"startUpload",value:function(e){this._retryCount=0;this.options;if(this._state==a.VODSTATE.START||this._state==a.VODSTATE.EXPIRE)return void console.log("already started or expired");if(this._initState(),this._curIndex=this._findUploadIndex(),-1==this._curIndex)return void(this._state=a.VODSTATE.END);var t=this._uploadList[this._curIndex];this._ossUpload=null,this._upload(t),this._state=a.VODSTATE.START}},{key:"nextUpload",value:function(){var e=this.options;if(this._state==a.VODSTATE.START)if(this._curIndex=this._findUploadIndex(),-1!=this._curIndex){var t=this._uploadList[this._curIndex];this._ossUpload=null,this._upload(t)}else{this._state=a.VODSTATE.END;try{e.onUploadEnd&&e.onUploadEnd(t)}catch(e){console.log(e)}}}},{key:"clear",value:function(e){for(var t=this.options,r=0,i=0;i<this._uploadList.length;i++)t.uploadList[i].state==a.UPLOADSTATE.SUCCESS&&r++,this._uploadList[i].state==e&&(t.uploadList.splice(i,1),i--);t.onClear&&t.onClear(t.uploadList.length,r)}},{key:"stopUpload",value:function(){if((this._state==a.VODSTATE.START||this._state==a.VODSTATE.FAILURE||-1==this._curIndex)&&-1!=this._curIndex){var e=this._uploadList[this._curIndex];this._state=a.VODSTATE.STOP,e.state=a.UPLOADSTATE.STOPED,this._changeState(e,a.UPLOADSTATE.STOPED),this._ossUpload.cancel()}}},{key:"resumeUploadWithAuth",value:function(e){var t=this;if(!e)return!1;var r=JSON.parse(base64.decode(e));return!!(r.AccessKeyId&&r.AccessKeySecret&&r.SecurityToken&&r.Expiration)&&t.resumeUploadWithToken(r.AccessKeyId,r.AccessKeySecret,r.SecurityToken,r.Expiration)}},{key:"resumeUploadWithToken",value:function(e,t,r,i){this.options;if(!(e&&t&&r&&i))return!1;if(this._state!=a.VODSTATE.EXPIRE)return!1;if(-1==this._curIndex)return!1;var n="";return this._uploadList.length>this._curIndex&&(n=this._uploadList[this._curIndex]),n&&(this.init(e,t,r,i),this._state=a.VODSTATE.START,this._ossUpload=null,this._uploadCore(n,n.retry),n.retry=!1),!0}},{key:"resumeUploadWithSTSToken",value:function(e,t,r){if(-1==this._curIndex)return!1;if(this._state!=a.VODSTATE.EXPIRE)return!1;if(this._uploadList.length>this._curIndex){var i=this._uploadList[this._curIndex];i.object?this._refreshSTSTokenUpload(i,e,t,r):this.setSTSToken(i,e,t,r)}}},{key:"setSTSTokenDirectlyUpload",value:function(e,t,r,i,n){if(!(t&&r&&i&&n))return console.log("accessKeyId、ccessKeySecret、securityToken and expiration should not be empty."),!1;this._ut="oss";var o=e;this.init(t,r,i,n),o.endpoint=o._endpoint,o.bucket=o._bucket,o.object=o._object,this._ossUpload=null,this._uploadCore(o,e.retry),e.retry=!1}},{key:"setSTSToken",value:function(e,t,r,i){if(!t||!r||!i)return console.log("accessKeyId、ccessKeySecret、securityToken should not be empty."),!1;this._ut="vod",this._uploadWay="sts";var n=e,o={accessKeyId:t,securityToken:i,accessKeySecret:r,fileName:e.url,title:e.url,requestId:e.ri,region:this.options.region};n.ImageType&&(o.imageType=n.ImageType),n.ImageExt&&(o.imageExt=n.ImageExt),n.FileSize&&(o.fileSize=n.FileSize),n.Description&&(o.description=n.Description),n.CateId&&(o.cateId=n.CateId),n.Tags&&(o.tags=n.Tags),n.TemplateGroupId&&(o.templateGroupId=n.TemplateGroupId),n.StorageLocation&&(o.storageLocation=n.StorageLocation),n.CoverURL&&(o.coverUrl=n.CoverURL),n.TransCodeMode&&(o.transCodeMode=n.TransCodeMode),n.UserData&&(o.userData=n.UserData);var s=this,c="getUploadAuth";e.videoId?(o.videoId=e.videoId,c="refreshUploadAuth"):p.default.isImage(e.url)&&(c="getImageUploadAuth"),g.default[c](o,function(t){e.videoId=t.VideoId?t.VideoId:e.videoId,s.setUploadAuthAndAddress(e,t.UploadAuth,t.UploadAddress),s._state=a.VODSTATE.START},function(t){s._error(e,{name:t.Code,code:t.Code,message:t.Message,requestId:t.RequestId})})}},{key:"setUploadAuthAndAddress",value:function(e,t,r,i){if(!e||!t||!r)return!1;var n=JSON.parse(c.default.enc.Utf8.stringify(c.default.enc.Base64.parse(t)));if(!(n.AccessKeyId&&n.AccessKeySecret&&n.SecurityToken&&n.Expiration))return console.error("uploadauth is invalid"),!1;var o={},a=e;if(r){var o=JSON.parse(c.default.enc.Utf8.stringify(c.default.enc.Base64.parse(r)));if(!o.Endpoint||!o.Bucket||!o.FileName)return console.error("uploadAddress is invalid"),!1}else o.Endpoint=a.endpoint,o.Bucket=a.bucket,o.FileName=a.object;this._ut="vod",this._uploadWay="vod",this.options.region=n.Region||this.options.region,this.init(n.AccessKeyId,n.AccessKeySecret,n.SecurityToken,n.Expiration),a.endpoint=a._endpoint?a._endpoint:o.Endpoint,a.bucket=a._bucket?a._bucket:o.Bucket,a.object=a._object?a._object:o.FileName,a.region=this.options.region,i&&(a.videoId=i),this._ossUpload=null,this._uploadCore(a,e.retry),e.retry=!1}},{key:"_refreshSTSTokenUpload",value:function(e,t,r,i){if(!t||!r||!i)return console.log("accessKeyId、ccessKeySecret、securityToken should not be empty."),!1;var n={accessKeyId:t,securityToken:i,accessKeySecret:r,videoId:e.object,requestId:e.ri,region:this.options.region},o=this,s="refreshUploadAuth";e.isImage&&(s="getImageUploadAuth"),g.default[s](n,function(t){o.setUploadAuthAndAddress(e,t.UploadAuth,UploadAddress),o._state=a.VODSTATE.START},function(t){o._error(e,{name:t.Code,code:t.Code,message:t.Message,requestId:t.RequestId})})}},{key:"_upload",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.options;if(e.retry=t,r.onUploadstarted&&!t)try{var i=this._getCheckoutpoint(e);i&&i.state!=a.UPLOADSTATE.UPLOADING&&(e.checkpoint=i,e.videoId=i.videoId),r.onUploadstarted(e)}catch(e){console.log(e)}}},{key:"_uploadCore",value:function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this._ossCreditor.accessKeyId||!this._ossCreditor.accessKeySecret||!this._ossCreditor.securityToken)throw new Error("AccessKeyId、AccessKeySecret、securityToken should not be null");if(e.state=a.UPLOADSTATE.UPLOADING,!this._ossUpload){e.endpoint=e.endpoint||"http://oss-cn-hangzhou.aliyuncs.com";var t=this;this._ossUpload=new b.default({bucket:e.bucket,endpoint:e.endpoint,AccessKeyId:this._ossCreditor.accessKeyId,AccessKeySecret:this._ossCreditor.accessKeySecret,SecurityToken:this._ossCreditor.securityToken,timeout:this.options.timeout,cname:this.options.cname},{onerror:function(e,r){t._error.call(t,e,r)},oncomplete:function(e,r){t._complete.call(t,e,r)},onprogress:function(e,r,i){t._progress.call(t,e,r,i)}})}var r=p.default.getFileType(e.url),i=this._getCheckoutpoint(e),n="",o="";i&&i.checkpoint&&(o=i.state,n=i.videoId,i=i.checkpoint),i&&n==e.videoId&&o!=a.UPLOADSTATE.UPLOADING&&(i.file=e.file,e.checkpoint=i,i.uploadId);var s=this._adjustPartSize(e);this._reportLog("20002",e,{ft:r,fs:e.fileSize,bu:e.bucket,ok:e.object,vid:e.videoId||"",fn:e.fileName,fw:null,fh:null,ps:s});var c={headers:{"x-oss-notification":e.userData?e.userData:""},partSize:s,parallel:this.options.parallel};this._ossUpload.upload(e,c)}},{key:"_findUploadIndex",value:function(){for(var e=-1,t=0;t<this._uploadList.length;t++)if(this._uploadList[t].state==a.UPLOADSTATE.INIT){e=t;break}return e}},{key:"_error",value:function(e,t){if("cancel"==t.name)try{this.options.onUploadCanceled(e,t)}catch(e){console.log(e)}else{if(t.message.indexOf("InvalidAccessKeyIdError")>0||"SignatureDoesNotMatchError"==t.name||"SecurityTokenExpired"==t.code||"InvalidSecurityToken.Expired"==t.code||"InvalidAccessKeyId"==t.code&&this._ossCreditor.securityToken){if(this.options.onUploadTokenExpired){this._state=a.VODSTATE.EXPIRE,e.state=a.UPLOADSTATE.FAIlURE;try{this.options.onUploadTokenExpired(e,t)}catch(e){console.log(e)}}return}if(("RequestTimeoutError"==t.name||"ConnectionTimeout"==t.name||"ConnectionTimeoutError"==t.name)&&this._retryTotal>this._retryCount){var r=this;return setTimeout(function(){r._uploadCore(e,!0)},1e3*r._retryDuration),void this._retryCount++}"NoSuchUploadError"==t.name&&this._removeCheckoutpoint(e),this._handleError(e,t)}}},{key:"_handleError",value:function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=a.UPLOADSTATE.FAIlURE;if(e.state!=a.UPLOADSTATE.CANCELED&&(e.state=a.UPLOADSTATE.FAIlURE,this._state=a.VODSTATE.FAILURE,this.options.onUploadFailed&&t&&t.code&&t.message))try{this.options.onUploadFailed(e,t.code,t.message)}catch(e){console.log(e)}if(r&&this._changeState(e,i),this._reportLog("20006",e,{code:t.name,message:t.message,requestId:t.requestId,fs:e.fileSize,bu:e.bucket,ok:e.object,fn:e.url}),this._reportLog("20004",e,{requestId:t.requestId,fs:e.fileSize,bu:e.bucket,ok:e.object,fn:e.url}),e.ri=_.default.create(),-1!=this._findUploadIndex()){var n=this;this._state=a.VODSTATE.START,setTimeout(function(){n.nextUpload()},100)}}},{key:"_complete",value:function(e,t){if(e.state=a.UPLOADSTATE.SUCCESS,this.options.onUploadSucceed)try{this.options.onUploadSucceed(e)}catch(e){console.log(e)}var r=0;t&&t.res&&t.res.headers&&(r=t.res.headers["x-oss-request-id"]),this._removeCheckoutpoint(e);var i=this;setTimeout(function(){i.nextUpload()},100),this._retryCount=0,this._reportLog("20003",e,{requestId:r})}},{key:"_progress",value:function(e,t,r){if(this.options.onUploadProgress)try{e.loaded=t.loaded,this.options.onUploadProgress(e,t.total,t.loaded)}catch(e){console.log(e)}var i=t.checkpoint,n=0;i&&(e.checkpoint=i,this._saveCheckoutpoint(e,i,a.UPLOADSTATE.UPLOADING),n=i.uploadId),this._retryCount=0;var o=this._getPortNumber(i),s=0;if(r&&r.headers&&(s=r.headers["x-oss-request-id"]),0!=t.loaded&&this._reportLog("20007",e,{pn:o,requestId:s}),1!=t.loaded&&this._reportLog("20005",e,{UploadId:n,pn:o+1,pr:e.retry?1:0,fs:e.fileSize,bu:e.bucket,ok:e.object,fn:e.url}),!this._invalidUserId&&!e.isImage&&"vod"==this._ut&&this.options.enableUploadProgress){this.options.userId,e.videoId,this.options.region,e.fileHash}}},{key:"_getPortNumber",value:function(e){if(e){var t=e.doneParts;if(t&&t.length>0)return t[t.length-1].number}return 0}},{key:"_removeCheckoutpoint",value:function(e){var t=this._getCheckoutpointKey(e);l.default.remove(t)}},{key:"_getCheckoutpoint",value:function(e){var t=this._getCheckoutpointKey(e),r=l.default.get(t);if(r)try{return JSON.parse(r)}catch(e){}return""}},{key:"_saveCheckoutpoint",value:function(e,t,r){if(t){var i=this._getCheckoutpointKey(e),n=e.file,o={fileName:n.name,lastModified:n.lastModified,size:n.size,object:e.object,videoId:e.videoId,bucket:e.bucket,endpoint:e.endpoint,checkpoint:t,loaded:e.loaded,state:r};l.default.set(i,JSON.stringify(o))}}},{key:"_changeState",value:function(e,t){var r=this._getCheckoutpoint(e);r&&((this._onbeforeunload=!0)&&(t=a.UPLOADSTATE.STOPED),this._saveCheckoutpoint(e,r.checkpoint,t))}},{key:"_getCheckoutpointKey",value:function(e){var t;return e.file&&(t="upload_"+e.file.lastModified+"_"+e.file.name+"_"+e.file.size),t}},{key:"_getCheckoutpointFromCloud",value:function(e,t,r){this.options.userId,e.fileName,e.fileSize,e.fileLastModified,e.fileHash,this.options.region}},{key:"_reportLog",value:function(e,t,r){r||(r={}),r.ri=t.ri,this._ut&&(r.ut=this._ut),this._log.log(e,r)}},{key:"_initEvent",value:function(){var e=this;window&&(window.onbeforeunload=function(t){if(e._onbeforeunload=!0,-1!=e._curIndex&&e._uploadList.length>e._curIndex){var r=e._uploadList[e._curIndex];e._changeState(r,a.UPLOADSTATE.STOPED)}})}},{key:"_initState",value:function(){for(var e=0;e<this._uploadList.length;e++){var t=this._uploadList[e];t.state!=a.UPLOADSTATE.FAIlURE&&t.state!=a.UPLOADSTATE.STOPED||(t.state=a.UPLOADSTATE.INIT)}this._state=a.VODSTATE.INIT}},{key:"_adjustPartSize",value:function(e){return e.fileSize/this.options.partSize>1e4?e.fileSize/9999:this.options.partSize}}]),e}();t.default=I},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.UPLOADSTATE={INIT:"Ready",UPLOADING:"Uploading",SUCCESS:"Success",FAIlURE:"Failure",CANCELED:"Canceled",STOPED:"Stoped"},t.VODSTATE={INIT:"Init",START:"Start",STOP:"Stop",FAILURE:"Failure",EXPIRE:"Expire",END:"End"}},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=function(){function e(){i(this,e)}return n(e,null,[{key:"set",value:function(e,t){}},{key:"get",value:function(e){return null}},{key:"remove",value:function(e){}}]),e}();t.default=o},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=function(){function e(){i(this,e)}return n(e,null,[{key:"get",value:function(e){return null}},{key:"set",value:function(e,t,r){}}]),e}();t.default=o},function(e,t,r){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),a=r(1),s=i(a),c=r(7),u=i(c),l=function(){function e(){n(this,e)}return o(e,null,[{key:"refreshUploadAuth",value:function(e,t,r){var i=(u.default.randomUUID(),u.default.randomUUID()),n={AccessKeyId:e.accessKeyId,SecurityToken:e.securityToken,Action:"RefreshUploadVideo",VideoId:e.videoId,Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:i,RequestId:e.requestId},o=u.default.makeUTF8sort(n,"=","&")+"&Signature="+u.default.aliyunEncodeURI(u.default.makeChangeSiga(n,e.accessKeySecret)),a="https://vod."+e.region+".aliyuncs.com/?"+o;s.default.get(a,function(e){var e=JSON.parse(e);t&&t(e)},function(e){if(r){var t=JSON.parse(e);r(t)}})}},{key:"getUploadAuth",value:function(e,t,r){var i=(u.default.randomUUID(),u.default.randomUUID()),n={AccessKeyId:e.accessKeyId,SecurityToken:e.securityToken,Action:"CreateUploadVideo",Title:e.title,FileName:e.fileName,Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:i,RequestId:e.requestId};e.fileSize&&(n.FileSize=e.fileSize),e.description&&(n.Description=e.description),e.cateId&&(n.CateId=e.cateId),e.tags&&(n.Tags=e.tags),e.templateGroupId&&(n.TemplateGroupId=e.templateGroupId),e.storageLocation&&(n.StorageLocation=e.storageLocation),e.coverUrl&&(n.CoverURL=e.coverUrl),e.transCodeMode&&(n.TransCodeMode=e.transCodeMode),e.userData&&(n.UserData=JSON.stringify(e.userData));var o=u.default.makeUTF8sort(n,"=","&")+"&Signature="+u.default.aliyunEncodeURI(u.default.makeChangeSiga(n,e.accessKeySecret)),a="https://vod."+e.region+".aliyuncs.com/?"+o;s.default.get(a,function(e){t&&t(e.data)},function(e){if(r){var t={Code:"GetUploadAuthFailed",Message:"获取uploadauth失败"};try{t=JSON.parse(e)}catch(e){}r(t)}})}},{key:"getImageUploadAuth",value:function(e,t,r){var i=(u.default.randomUUID(),u.default.randomUUID()),n={AccessKeyId:e.accessKeyId,SecurityToken:e.securityToken,Action:"CreateUploadImage",ImageType:e.imageType?e.imageType:"default",Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:i,RequestId:e.requestId};e.title&&(n.Title=e.title),e.imageExt&&(n.ImageExt=e.imageExt),e.tags&&(n.Tags=e.tags),e.storageLocation&&(n.StorageLocation=e.storageLocation);var o=u.default.makeUTF8sort(n,"=","&")+"&Signature="+u.default.aliyunEncodeURI(u.default.makeChangeSiga(n,e.accessKeySecret)),a="https://vod."+e.region+".aliyuncs.com/?"+o;s.default.get(a,function(e){t&&t(e.data)},function(e){if(r){var t=JSON.parse(e);r(t)}})}}]),e}();t.default=l},function(e,t,r){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),a=r(1),s=i(a),c=r(5),u=i(c),l=r(2),d=i(l),f=r(4),h=i(f),p=r(6),v=i(p),_=r(7),y=i(_),g=r(0),k=i(g),S=function(){function e(){n(this,e)}return o(e,null,[{key:"getAuthInfo",value:function(e,t,r){var i=e+"|f#Ylm&^1TppeRhLg|"+r;return t&&(i=e+"|"+t+"|f#Ylm&^1TppeRhLg|"+r),k.default.enc.Hex.stringify(k.default.MD5(i))}},{key:"upload",value:function(t,r,i){var n=v.default.ISODateString(new Date),o=Math.floor((new Date).valueOf()/1e3),a=d.default.getClientId();a=d.default.setClientId(a);var c=e.getAuthInfo(t.userId,a,o),l=y.default.randomUUID(),f={Source:"WebSDK",BusinessType:"UploadVideo",Action:"ReportUploadProgress",TerminalType:"H5",DeviceModel:u.default.browser.name+(u.default.browser.version||""),AppVersion:h.default.version,AuthTimestamp:o,Timestamp:n,AuthInfo:c,FileName:t.file.fileName,FileSize:t.file.fileSize,FileCreateTime:t.file.lastModified,FileHash:t.file.fileHash,UploadId:t.checkpoint.checkpoint.uploadId,PartSize:t.checkpoint.checkpoint.partSize,DonePartsCount:t.checkpoint.checkpoint.doneParts.length,UploadPoint:JSON.stringify(t.checkpoint),UploadRatio:t.checkpoint.loaded,UserId:t.userId,VideoId:t.videoId,Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:l};a&&(f.ClientId=a);var p=y.default.makeUTF8sort(f,"=","&")+"&Signature="+y.default.aliyunEncodeURI(y.default.makeChangeSiga(f,t.accessKeySecret)),_="https://vod."+t.region+".aliyuncs.com/?"+p;s.default.get(_,function(e){r&&r()},function(e){e&&(i(e),console.log(e))})}},{key:"get",value:function(t,r,i){var n=v.default.ISODateString(new Date),o=Math.floor((new Date).valueOf()/1e3),a=d.default.getClientId(),c=e.getAuthInfo(t.userId,a,o),l=y.default.randomUUID(),f={Source:"WebSDK",BusinessType:"UploadVideo",Action:"GetUploadProgress",TerminalType:"H5",DeviceModel:u.default.browser.name+(u.default.browser.version||""),AppVersion:h.default.version,AuthTimestamp:o,Timestamp:n,AuthInfo:c,UserId:t.userId,UploadInfoList:JSON.stringify(t.uploadInfoList),Version:"2017-03-21",Format:"JSON",SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0",SignatureNonce:l};a&&(f.ClientId=a);var p=y.default.makeUTF8sort(f,"=","&")+"&Signature="+y.default.aliyunEncodeURI(y.default.makeChangeSiga(f,t.accessKeySecret)),_="https://vod."+t.region+".aliyuncs.com/?"+p;s.default.get(_,function(e){var t={},i=a;e=e.data?e.data:{},e.UploadProgress&&e.UploadProgress.UploadProgressList&&e.UploadProgress.UploadProgressList.length>0&&(t=e.UploadProgress.UploadProgressList[0],i=t.ClientId),d.default.setClientId(i),r&&r(t)},function(e){e&&(i(e),console.log(e))})}}]),e}();t.default=S},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=function(){function e(){i(this,e)}return n(e,null,[{key:"getFileInfo",value:function(e,t,r){wx.getFileInfo({filePath:e,digestAlgorithm:"md5",success:function(e){t(e)},fail:function(e){console.log(e)},complete:function(e){}})}},{key:"getSuffix",value:function(e){var t=e.lastIndexOf("."),r="";return-1!=t&&(r=e.substring(t)),r}}]),e}();t.default=o},function(e,t,r){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),o=r(18),a=r(0),s=function(e){return e&&e.__esModule?e:{default:e}}(a),c=function(){function e(t,r){if(i(this,e),t){this._config=t,this.create(this._config),this._uploadInfo=null,this._callback={};var n=function(){};this._callback.onerror=r.onerror||n,this._callback.oncomplete=r.oncomplete||n,this._callback.onprogress=r.onprogress||n}}return n(e,[{key:"create",value:function(e){if(e.endpoint=e.endpoint||this._config.endpoint,e.bucket=e.bucket||this._config.bucket,!(e.AccessKeyId&&e.AccessKeySecret&&e.endpoint&&e.SecurityToken))throw new Error("AccessKeyId、AccessKeySecret、endpoint should not be null");var t={accessKeyId:e.AccessKeyId,accessKeySecret:e.AccessKeySecret,stsToken:e.SecurityToken,endpoint:e.endpoint||this._config.endpoint,bucket:e.bucket||this._config.bucket,secure:!0,cname:e.cname};e.timeout&&(t.timeout=e.timeout),this.oss={options:t}}},{key:"abort",value:function(e){if(e.checkpoint){var t=e.checkpoint.uploadId;this.oss.abortMultipartUpload(e.object,t)}}},{key:"getVersion",value:function(){}},{key:"cancel",value:function(){}},{key:"upload",value:function(e,t){this._uploadInfo=e;var r=this,i=function(e,t,i){return function(n){r._progress(e,t,i),n()}},n={parallel:t.parallel||this._config.parallel||o.UPLOADDEFAULT.PARALLEL,partSize:t.partSize||this._config.partSize||o.UPLOADDEFAULT.PARTSIZE,progress:i};t.headers&&(n.headers=t.headers),e.checkpoint&&(n.checkpoint=e.checkpoint),e.bucket||(this.oss.options.bucket=e.bucket),e.endpoint||(this.oss.options.endpoint=e.endpoint),e.object||(this.oss.options.object=e.object);var a=this.oss.options.endpoint.split("://"),c=a[0]+"://"+this.oss.options.bucket+"."+a[1],u=new Date,l=new Date(u.setMinutes(u.getMinutes()+5)),d={expiration:l,conditions:[["content-length-range",0,1048576e3]]},f=s.default.enc.Base64.stringify(s.default.enc.Utf8.parse(JSON.stringify(d))),h=s.default.enc.Base64.stringify(s.default.HmacSHA1(f,this.oss.options.accessKeySecret));wx.uploadFile({url:c,filePath:e.url,timeout:this.oss.options.timeout,name:"file",formData:{key:e.object,policy:f,OSSAccessKeyId:this.oss.options.accessKeyId,"x-oss-security-token":this.oss.options.stsToken,success_action_status:"200",signature:h},success:function(e){r._complete(e),console.log("上传结果 success：",e)},fail:function(e){r._error(e),console.log("上传结果 fail：",e)}}).onProgressUpdate(function(e){r._progress(e.progress,null,e)})}},{key:"header",value:function(e,t,r){}},{key:"_progress",value:function(e,t,r){this._callback.onprogress(this._uploadInfo,{loaded:e,total:this._uploadInfo.fileSize,checkpoint:t},r)}},{key:"_error",value:function(e){this._callback.onerror(this._uploadInfo,e)}},{key:"_complete",value:function(e){this._callback.oncomplete(this._uploadInfo,e)}}]),e}();t.default=c},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.UPLOADSTATE={INIT:"init",UPLOADING:"uploading",COMPLETE:"complete",INTERRUPT:"interrupt"},t.UPLOADSTEP={INIT:"init",PART:"part",COMPLETE:"complete"},t.UPLOADDEFAULT={PARALLEL:5,PARTSIZE:1048576}}])});