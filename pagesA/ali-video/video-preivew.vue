<template>
  <view class="video-preview">
    <!-- 预览视频弹窗 -->
    <view
      class="mask"
      v-if="showVideo == true"
      @touchmove.stop.prevent="ondefault"
      @click="hideShow"
    >
    </view>
    <view class="previewvideo" v-if="showVideo == true">
      <view class="close" @click="hideShow">
        <cover-image
          class="video-icon"
          src="~@/pagesC/static/close.png"
        ></cover-image>
      </view>
      <view class="videos">
        <video
          class="nowvideos"
          id="nowVideo"
          v-if="showVideo == true"
          :src="videos"
          :autoplay="showVideo"
          :show-center-play-btn="true"
          :show-mute-btn="true"
          :show-fullscreen-btn="false"
        ></video>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showVideo: false,
      videos: ""
    };
  },
  props: {
    videoSrc: {
      type: String,
      default: ""
    }
  },
  watch: {
    videoSrc: {
      deep: true,
      immediate: true,
      handler(val) {
        this.videos = val;
      }
    }
  },
  methods: {
    //操作视频
    setShowVideo(showVideo = true) {
      this.showVideo = showVideo;
      console.log("视频点击播放");
    },
    // 关闭视频
    hideShow() {
      this.showVideo = false;
    }
  }
};
</script>
<style scoped lang="scss">
.video-preview {
  position: relative;
  overflow: hidden;
}

/* 预览视频弹窗 */
.mask {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 1);
  z-index: 200;
}

.previewvideo {
  width: 100vw;
  height: 100vw;
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  background-color: #000;
  z-index: 900;
  opacity: 1;
}

.close {
  display: flex;
  align-content: center;
  align-items: flex-end;
  .video-icon {
    position: absolute;
    top: 0rpx;
    right: 10rpx;
    width: 40rpx;
    height: 40rpx;
    z-index: 950;
    display: block;
    justify-content: center;
    border-radius: 50%;
    padding: 10rpx;
    background-color: rgba(0, 0, 0, 0.2);
  }
}

.videos {
  height: 100vw;
  width: 100vw;
  z-index: 10;
  position: relative;

  video {
    width: 100%;
    height: 100%;
  }
}

.nowvideos {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
</style>
