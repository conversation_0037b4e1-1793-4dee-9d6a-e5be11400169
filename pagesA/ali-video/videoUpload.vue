<template>
  <view class="video-upload">
    <!-- 视频上传 -->
    <view class="upload-content">
      <view class="uploader-files" id="uploaderFiles">
        <!-- 视频选择 -->
        <view class="orc-form">
          <view class="title-wrap">
            <view class="t-title">{{ title }}</view>
            <view class="desc" @click="preview">
              查看样例
            </view>
          </view>

          <view class="upload-box">
            <!-- 已选择的视频 -->
            <block v-for="item in videos" :key="item.url">
              <view class="uploader-file">
                <video class="uploader-img" :src="item.url" />
                <image
                  src="../static/close.png"
                  @click="removeVideo"
                  :id="item.url"
                  mode=""
                  class="close"
                ></image>
              </view>
            </block>

            <image
              v-if="videos.length <= 0"
              @click="chooseVideo"
              src="../static/obu_video.png"
              class="upload-box-img"
            ></image>
          </view>
        </view>
      </view>

      <!-- <videoPre ref="videoPre" :videoSrc="videosSrc"></videoPre> -->
    </view>
    <slot></slot>
  </view>
</template>

<script>
import VODUpload from "./aliSdk/aliyun-upload-sdk-1.0.1.min.js";
import ApiAfterSale from "@/common/api/afterSale.js";
import dayjs from "@/js_sdk/dayjs/dayjs.min.js";
// import videoPre from "./video-preivew.vue";
import { previewFile } from "@/pagesA/common/method.js";
import { getAccountId } from "@/common/storageUtil.js";

export default {
  // components: {
  //   videoPre
  // },
  props: {
    title: {
      type: String,
      default: ""
    },
    exampUrl: {
      // 查看样例url
      type: String,
      default: ""
    }
  },
  data() {
    return {
      options: {},
      videos: [],
      videosSrc: "",
      uploadInfoTemp: {}
    };
  },
  onLoad(option) {
    this.options = option;
  },
  onReady() {
    this.aliVideoSdkInit();
  },
  methods: {
    // 阿里视频上传sdk初始化
    aliVideoSdkInit() {
      let that = this;

      let uploader = new VODUpload({
        timeout: that.options.timeout || 60000,
        region: that.options.region || "cn-shenzhen",
        // 添加文件成功
        addFileSuccess(uploadInfo) {
          // console.log("addFileSuccess" + JSON.stringify(uploadInfo));
        },
        // 开始上传
        async onUploadstarted(uploadInfo) {
          // console.log("文件开始上传...");
          let data = {
            title: `自助激活-小程序上传-${getAccountId()}-${dayjs(
              new Date()
            ).format("YYYY-MM-DD HH:mm:ss")}`,
            fileName: uploadInfo.url
            // coverURL: uploadInfo.coverUrl
          };

          try {
            let res = await that.$request.post(that.$interfaces.getUploadAuth, {
              data: data
            });
            let akInfo = res.data;
            uploader.setUploadAuthAndAddress(
              uploadInfo,
              akInfo.uploadAuth,
              akInfo.uploadAddress,
              akInfo.videoId
            );
          } catch (err) {
            uploader.stopUpload();
            uni.hideLoading();
            console.log(err);
          }
        },
        // 文件上传成功
        onUploadSucceed: function(uploadInfo) {
          console.log("文件上传成功!");
          that.$emit("uploadSucceed", uploadInfo);
        },
        // 文件上传失败
        onUploadFailed: function(uploadInfo, code, message) {
          uni.hideLoading();
          console.log("文件上传失败!");
          uploader.stopUpload();
        },
        // 取消文件上传
        onUploadCanceled(uploadInfo, code, message) {
          console.log(uploadInfo, code, message);
          console.log("文件已暂停上传!");
          uploader.stopUpload();
        },
        // 文件上传进度，单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
        onUploadProgress(uploadInfo, totalSize, progress) {
          let files;
          if (uploadInfo.isImage) {
            files = that.images;
          } else {
            files = that.videos;
          }

          files.forEach((file, idx) => {
            if (file.url === uploadInfo.url) {
              file.progress = progress;
              if (uploadInfo.isImage) {
                that.images = files;
              } else {
                that.videos = files;
              }
            }
          });

          let progressPercent = Math.ceil(progress);
          uni.showLoading({
            title: `上传中, ${progressPercent}%`
          });
          console.log("文件上传中..." + progressPercent);
        },
        // 全部文件上传结束
        onUploadEnd(uploadInfo) {
          uni.showToast({
            title: "上传成功",
            icon: "success",
            duration: 2000
          });
          // that.isLoading = false;
          uni.hideLoading();
        }
      });
      this.uploader = uploader;
    },
    // 选择视频
    chooseVideo(e) {
      let that = this;
      uni.chooseMedia({
        mediaType: ["video"],
        count: 1,
        sourceType: ["camera"], // album
        extension: "[.mp4]",
        maxDuration: 40,
        compressed: false,
        camera: "back",
        success(result) {
          console.log(e, result, 3333);
          let res = result.tempFiles[0];
          if (res.duration >= 41) {
            uni.showToast({
              title: "视频拍摄时长最长不超过40秒",
              icon: "error",
              duration: 2000
            });
            return;
          }
          let file = { url: res.tempFilePath, coverUrl: res.thumbTempFilePath };
          that.uploadInfoTemp = res;
          that.videos = that.videos.concat(file);
          let uploader = that.uploader;
          let userData = '{"Vod":{}}';
          uploader.addFile(file, null, null, null, userData);
          that.$nextTick(() => {
            that.uploadVideo();
          });
        }
      });
    },
    // 删除视频
    removeVideo(event) {
      let url = event.currentTarget.id;
      this.videos.forEach((video, idx) => {
        if (video.url === url) {
          // 重置
          this.videos.splice(idx, 1);
          this.videosSrc = "";
          this.uploadInfoTemp = {};
          this.uploader.deleteFile(idx);
          this.$emit("removeVideo", url);
        }
      });
    },
    //点击上传
    uploadVideo(e) {
      this.uploader.startUpload();
    },
    preview() {
      let url = this.exampUrl;
      previewFile(url);
    }
    // previewVideo() {
    //   this.videosSrc = this.uploadInfoTemp.tempFilePath;
    //   this.$refs.videoPre.setShowVideo();
    // }
  }
};
</script>

<style lang="scss" scoped>
.close {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  color: #fff;
  // border-radius: 50%;
  width: 38rpx;
  height: 38rpx;
  text-align: center;
}
.uploader-file {
  position: relative;
  width: 100%;
  height: 320rpx;
  video {
    width: 100%;
    height: 100%;
  }
}

.video-upload {
  background-color: #ffffff;
  border-radius: 8rpx;
}

.orc-form {
  padding: 30rpx;
  border-bottom-left-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
  // margin-bottom: 30rpx;
  .title-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;
    .t-title {
      height: 45rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #323435;
      line-height: 45rpx;
    }
    .desc {
      font-size: 24rpx;
      text-align: left;
      font-weight: 400;
      color: #0081ff;
    }
  }
}

.upload-box {
  width: 100%;
  height: 320rpx;
  position: relative;
  background: rgba(243, 243, 243, 0.87);
  border: 1px dashed #bbbbbb;
  border-radius: 12rpx;
  .upload-box-img {
    width: 148rpx;
    height: 148rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>