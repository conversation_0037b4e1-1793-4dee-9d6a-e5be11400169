<template>
	<uni-popup ref="popup" background-color="#F9F9F9">
		<view class="half-screen-dialog" style="background: #F9F9F9;border-radius: 20px 20px 0px 0px">
			<view class="half-screen-dialog__hd g-flex g-flex-align-center">
				<view class="half-screen-dialog__hd__main ">
					<view class="title">
						支持签约的银行列表
					</view>
				</view>
				<!-- <view class="half-screen-dialog__hd__side"
					:class="['half-screen-dialog__hd__side--' + closeIconPos]" @click="closePopup('bottom')">
					<text class="cuIcon-close close"></text>
				</view> -->
			</view>
			<view class="half-screen-dialog__bd" style="margin: 30rpx 0; max-height: 520rpx; overflow-y: scroll;">
				<view class="weui-media" v-for="(item,index) in bankList" :key="index">
					<view class="weui-media-hd">
						<img :src="item.logoPath" alt="" class='weui-media-hd_icon'>
					</view>
					<view class="weui-media-bd">
						<view class="title g-flex">
							<view class="">
								{{item.bankName}}
							</view>

						</view>
						<view class="desc">
							【{{item.type =='CUP' ? '银联渠道' :'微信指定卡'}}】{{item.desc ? item.desc: ''}}
						</view>
					</view>
					<view>
						<radio-group @change="checked=>changeRadio(checked,item,index)">
							<radio :value="item.bankName" class="cyan" style="transform: scale(0.7)"
								:checked="item.bankName == selectBankName" />
						</radio-group>
					</view>
				</view>
			</view>
			<view class="half-screen-dialog__ft">
				<view class="bottom-box">

					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="onCloseHandle('bottom','confirm')">
							确定
						</button>
					</view>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	export default {
		props: {
			bankData: {
				type: Array,
				default () {
					return []
				}
			}
		},
		data() {
			return {
				selectBankName: '',
				selectBankItem: {},
				closeIconPos: 'top-right',
				bankList: []
			}
		},
		watch: {
			bankData(val) {
				this.bankList = val;
			}
		},
		created() {
			this.bankList = this.bankData;
		},

		methods: {
			onCloseHandle(type, action) {
				if (action == 'confirm') {
					if (this.selectBankName) {
						this.$emit('on-select', this.selectBankItem);
						this.closePopup('bottom');
						return
					}
				}
			},
			//单选框更改事件
			changeRadio(checked, item, index) {
				this.selectBankName = item.bankName
				this.selectBankItem = item;
			},
			open() {
				this.bankList = this.bankData;
				this.$refs.popup.open('bottom')
			},
			closePopup(type) {
				this.$refs.popup.close(type)
			},
		}
	}
</script>

<style lang='scss' scoped>
	.weui-media {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		border: 1px solid #F1F1F1;
		margin: 20rpx 30rpx;
		border-radius: 16rpx;
		padding: 30rpx 30rpx;
		box-shadow: 0px 1px 7px 0px rgba(220, 220, 220, 0.5);

		&:first-child {
			margin-top: 0;
		}
	}

	.weui-media .weui-media-hd {
		width: 80rpx;
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		-moz-box-pack: center;
		-ms-box-pack: center;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-justify-content: center;
		justify-content: center;
	}

	.weui-media .weui-media-hd .weui-media-hd_icon {
		display: block;
		width: 68rpx;
		height: 68rpx;
	}

	.weui-media .weui-media-bd {
		flex: 1;
		margin-left: 30rpx;
	}

	.weui-media .weui-media-bd .title {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
		position: relative;
	}

	.weui-media .weui-media-bd .desc .badge {
		border-radius: 50%;
		display: flex;
		flex-direction: row;
		height: 40rpx;
		line-height: 40rpx;
		width: 50rpx;
		text-align: center;
		font-size: 24rpx;
		color: #fff;
		background-color: #0066E9;

	}

	.weui-media .weui-media-bd .value {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 10rpx;
	}

	.weui-media .weui-media-bd .desc {
		margin-top: 10rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #999999;
		display: flex;
	}

	.weui-media .weui-media-ft {
		width: 28rpx;
	}

	.weui-media .weui-media-ft .weui-media-ft_icon {
		width: 28rpx;
		height: 28rpx;
		display: block;
	}

	.half-screen-dialog__hd {
		height: 100rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;
		z-index: 999;
		position: relative;
	}

	.half-screen-dialog__hd__main {
		width: 100%;
	}

	.half-screen-dialog__hd__main .title {
		color: #333333;
		font-size: 36rpx;
		font-weight: 700;
		padding-left: 12rpx;
		text-align: center;
		position: relative;
	}

	.half-screen-dialog__hd__main .desc_title {
		font-size: 26rpx;
		font-weight: 400;
		color: #555555;
		margin-top: 12rpx;
		text-align: center;
	}

	.half-screen-dialog__hd__side {
		position: absolute;
		z-index: 999;
	}

	.half-screen-dialog__hd__side .close {
		font-size: 36rpx;
		color: #333333;
	}

	.half-screen-dialog__hd__side--top-left {
		top: 30rpx;
		left: 30rpx;
	}

	.half-screen-dialog__hd__side--top-right {
		top: 30rpx;
		right: 30rpx;
	}

	.half-screen-dialog__bd {
		margin-top: 30rpx;
	}

	.half-screen-dialog__ft {
		padding: 20rpx 56rpx 48rpx 56rpx;
		background-color: #ffffff;
	}

	.fixed-margin-bottom {
		padding-bottom: 160rpx;
	}

	.fixed-padding-top {
		padding-top: 20rpx;
	}
</style>
