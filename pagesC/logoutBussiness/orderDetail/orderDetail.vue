<template>
	<view class="apply-after-sale">
		<view class="order-detail__title">
			<view class="title-hd">
				<view class="title-hd__label">
					订单编号：
				</view>
				<view class="title-hd__value">
					<!-- {{applyId}} -->
					{{result.id}}
				</view>
			</view>
			<view class="title-bd">
				<view class="copy" @click="copy(result.id)">
					复制
				</view>
				<view class="icon-question">
					<image style="width: 40rpx;height: 40rpx;margin-top: 6rpx;" src="../../static/icon-question.png"
						mode="">
					</image>
				</view>
			</view>
		</view>
		<view class="section">
			<view class="order-progress">
				<tTitle title="办理进度" setPadding="30" rightText="查看详情" @click="toProgress">
				</tTitle>
				<view class="status-wrapper">
					<view class="status-label">
						订单状态：
					</view>
					<view class="status-value" :class="statusInfoList[result.hsCancelRecord.nodeCode].status">
						{{statusInfoList[result.hsCancelRecord.nodeCode].name}}
						<!-- 注销退款不成功 问题清单 -->
						<view v-if="result.hsCancelRecord.nodeCode == '404' && questionList.contentArr > 0"
							class="question-list" @click="question">
							问题清单
						</view>
					</view>
				</view>
				<view class="progress-tip">
					{{result.codeDesc || ''}}
				</view>
			</view>
		</view>
		<view class="section">
			<vehicle :vehicleObj="vehicleObj"></vehicle>
		</view>
		<view class="section">
			<commonTitle title="提交材料"></commonTitle>
			<uploadDetail @on-change="onChange" :uploadList="uploadList" :isUpdate="isUpdate" @delImg="delImg"
				:userInfo="formData">
			</uploadDetail>
		</view>
		<!-- 正常流程，不属于注销不成功，需要判断余额 -->
		<template v-if="typeNeedBank && result.amount > 0">
			<view class="section">
				<commonTitle title="余额处理"></commonTitle>
				<view class="card-price">
					卡内可用余额：{{moneyFilter(result.amount)+'元'}}
				</view>
				<view class="tips">
					余额仅供参考，以实际到账信息为准
				</view>
				<view class="amount-select">
					<template v-if="isUpdate">
						<view class="label">余额处理方式</view>
						<view class="select-wrapper" @click="showReturnType">
							<view class="select-value">
								{{amountHandle.text}}
							</view>
							<image class="select-icon" src="../../static/arrow-show.png"
								style="width: 34rpx;height: 26rpx;" mode=""></image>
						</view>
					</template>
					<template v-else>
						<view class="label" style="flex: 0 0 190rpx;width: 190rpx;">余额处理方式：</view>
						<view class="select-text">
							{{amountHandle.text}}
						</view>
					</template>

				</view>
			</view>
			<view class="section" v-if="formData.balanceDisposal == 1">
				<commonTitle title="银行收款信息">
					<!-- 不兼容需求，废弃 -->
					<!-- <template #rightBtn v-if="isUpdate || isCangeBank">
						<view class="question-wrapper" @click="hadAccount">
							<view class="question-text">
								已有银行账户
							</view>
						</view>
					</template> -->
				</commonTitle>
				<view class="bank-wrapper">
					<view class="bank-item" style="margin-bottom: 27rpx;">
						<view class="label">
							户名
						</view>
						<view class="value">
							{{formData.custName}}
						</view>
					</view>
					<view class="bank-item other">
						<view class="label">
							<text style="color: #F65B5B;">*</text>开户行
						</view>
						<template v-if="isUpdate || isCangeBank">
							<view class="content">
								<view class="select-wrapper" @click="showBankList">
									<view class="select-value" v-if="!formData.bankName">
										请选择开户银行
									</view>
									<view class="select-value" v-else>
										{{formData.bankName}}
									</view>
									<image class="select-icon" src="../../static/arrow-show.png"
										style="width: 34rpx;height: 26rpx;" mode=""></image>
								</view>
								<!-- 	<input v-if="bankNameIndex == 8" class="other-input" v-model="otherBankName" type="text"
									placeholder="请输入开户行信息"> -->
							</view>
						</template>
						<template v-else>
							<view class="content">
								<view class="select-text" style="height: 68rpx;line-height: 68rpx;">
									{{formData.bankName}}
								</view>
							</view>
						</template>
					</view>
					<view class="bank-item">
						<view class="label">
							<text style="color: #F65B5B;">*</text>银行卡号
						</view>
						<template v-if="isUpdate || isCangeBank">
							<view class="select-wrapper">
								<input class="select-input" v-model="formData.bankAccount" type="number"
									placeholder="请输入ETC开户人名下的银行卡号">
								<view class="bank-img" @click="bankImg">
									<image src="../../static/photo_icon.png" style="width: 40rpx;height: 40rpx" mode="">
									</image>
								</view>
							</view>
						</template>
						<template v-else>
							<view class="select-text" style="height: 42rpx;line-height: 42rpx;">
								{{formData.bankAccount}}
							</view>
						</template>
					</view>
				</view>
			</view>
			<view class="section" v-if="formData.balanceDisposal == 2 && formData.accountType == '1'">
				<view class="account-wrapper">
					<view class="title">
						余额将退至以下ETC账户，请仔细核对单位名称、证件号、登录账号、手机号等内容
					</view>
					<view class="account-info">
						<view class="account-name">
							{{companyInfo.custName}}
						</view>
						<view class="account-item">
							<view class="label">
								账户编号：
							</view>
							<view class="value">
								{{companyInfo.userIdStr}}
							</view>
						</view>
						<view class="account-item">
							<view class="label">
								登录编号：
							</view>
							<view class="value">
								{{companyInfo.loginName}}
							</view>
						</view>
						<view class="account-item">
							<view class="label">
								证件号：
							</view>
							<view class="value">
								{{companyInfo.custIdNo}}
							</view>
						</view>
						<view class="account-item">
							<view class="label">
								手机号：
							</view>
							<view class="value">
								{{companyInfo.custMobile}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</template>
		<!-- 注销不成功单独退银行信息 -->
		<template
			v-if="typeNeedBank && (result.hsCancelRecord.nodeCode == '406' || result.hsCancelRecord.nodeCode == '407')">
			<view class="section" v-if="formData.balanceDisposal == 1">
				<!-- 不兼容需求废弃 -->
				<commonTitle title="银行收款信息">
					<!-- <template #rightBtn v-if="isUpdate || isCangeBank">
						<view class="question-wrapper" @click="hadAccount">
							<view class="question-text">
								已有银行账户
							</view>
						</view>
					</template> -->
				</commonTitle>
				<view class="bank-wrapper">
					<view class="bank-item" style="margin-bottom: 27rpx;">
						<view class="label">
							户名
						</view>
						<view class="value">
							{{formData.custName}}
						</view>
					</view>
					<view class="bank-item other">
						<view class="label">
							<text style="color: #F65B5B;">*</text>开户行
						</view>
						<template v-if="isUpdate || isCangeBank">
							<view class="content">
								<view class="select-wrapper" @click="showBankList">
									<view class="select-value" v-if="!formData.bankName">
										请选择开户银行
									</view>
									<view class="select-value" v-else>
										{{disputeRefundBankName[bankNameIndex].label}}
									</view>
									<image class="select-icon" src="../../static/arrow-show.png"
										style="width: 34rpx;height: 26rpx;" mode=""></image>
								</view>
								<!-- 								<input v-if="bankNameIndex == 8" class="other-input" v-model="otherBankName" type="text"
									placeholder="请输入开户行信息"> -->
							</view>
						</template>
						<template v-else>
							<view class="content">
								<view class="select-text" style="height: 68rpx;line-height: 68rpx;">
									{{formData.bankName}}
								</view>
							</view>
						</template>
					</view>
					<view class="bank-item">
						<view class="label">
							<text style="color: #F65B5B;">*</text>银行卡号
						</view>
						<template v-if="isUpdate || isCangeBank">
							<view class="select-wrapper">
								<input class="select-input" maxlength="30" v-model="formData.bankAccount" @blur="onBlur"
									type="number" placeholder="请输入ETC开户人名下的银行卡号">
								<view class="bank-img" @click="bankImg">
									<image src="../../static/photo_icon.png" style="width: 40rpx;height: 40rpx" mode="">
									</image>
								</view>
							</view>
						</template>
						<template v-else>
							<view class="select-text" style="height: 42rpx;line-height: 42rpx;">
								{{formData.bankAccount}}
							</view>
						</template>
					</view>
				</view>
			</view>
		</template>
		<LButton v-if="buttonList.length > 0" :buttonList="buttonList" :moreBtn="true" @click="toHandle">
		</LButton>
		<ocrBankCard ref="ocrBank" @on-change="onBankChange" :userInfo="formData" v-if="formData.custMastId">
		</ocrBankCard>
		<u-select v-model="show" :default-value="[handleIds]" value-name="code" label-name="name"
			:list="BalanceDisposalEnum" @confirm="selectReturnConfirm">
		</u-select>
		<uni-popup ref="popup" background-color="#FFFFFF;">
			<view class="search-wrapper">
				<view class="search-title g-flex g-flex-align-center">
					<view class="cancel" @click="close">
						取消
					</view>
					<view class="confirm" :class="isPickStart?'gray':''" @click="selectConfirm">
						确认
					</view>
				</view>
				<view class="search-input">
					<u-search v-model="keyword" @change="searchChange" :show-action="false" @clear="searchClear"
						:clearabled="true" placeholder="输入关键字快速查询"></u-search>
				</view>
				<view class="picker-wrapper" v-if="bankNameshow">
					<picker-view :indicator-style="indicatorStyle" :value="[bankNameIndex]" @pickstart="pickstart"
						@pickend="pickend" @change="pickerChange" class="picker-view">
						<picker-view-column>
							<view class="picker-item" v-for="(item,index) in bankNameList" :key="index">
								{{item.label}}
							</view>
						</picker-view-column>
					</picker-view>
				</view>
			</view>
		</uni-popup>
		<smsDialog ref="smsDialog" :show.sync="showSmsDialog" :msgType="'logout'" :mobile="formData.mobile" @confirm="smsConfirm">
		</smsDialog>
		<!-- 问题清单 -->
		<needKnow redText="注销不成功！" :show.sync="isShowQuestionList" :showTips="questionList.showTips"
			:content="questionList.content" :contentArr="questionList.contentArr" @comfirm="listConfirm"
			@goToRecharge="goToRecharge">
		</needKnow>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import vehicle from '@/pagesC/components/vehicle/vehicle.vue';
	import tTitle from '@/pagesC/components/new-title/new-title.vue';
	import commonTitle from '@/pagesC/components/common-title/common-title.vue'
	import LButton from '@/pagesC/components/logout-button/logout-button.vue'
	import smsDialog from '@/pagesC/components/logout-dialog/sms-dialog.vue'
	import uploadDetail from '@/pagesC/components/logout-upload/uploadDetail.vue';
	import needKnow from '@/pagesC/components/logout-dialog/need-know.vue'
	import ocrBankCard from '@/pagesC/components/logout-upload/ocrBankCard.vue';
	import {
		request
	} from '@/common/request-1/requestFunc.js';
	import {
		disputeRefundBankName
	} from '@/common/const/optionData.js'

	import {
		statusInfoList
	}
	from '@/pagesC/common/optionsData.js'
	import {
		vehicleColors
	} from "@/common/const/optionData.js";
	import {
		getLoginUserInfo
	} from '@/common/storageUtil.js'
	// import {
	// 	imgToBase64
	// } from '@/common/commonMethod.js'
	export default {
		components: {
			tLoading,
			tTitle,
			vehicle,
			commonTitle,
			LButton,
			smsDialog,
			uploadDetail,
			needKnow,
			ocrBankCard
		},
		computed: {
			buttonList(list) {
				if (this.result.hsCancelRecord) {
					if (this.result.hsCancelRecord.nodeCode == '401' || this.result.hsCancelRecord.nodeCode == '403') {
						//待审核与审核不通过
						if (!this.isUpdate) {
							list = [{
								title: '修改信息',
								handle: 'update'
							}, {
								title: '取消订单',
								type: 'info',
								handle: 'toCancel'
							}]
						} else {
							list = [{
								title: '提交',
								handle: 'saveHandle'
							}]
						}
					} else if (this.result.hsCancelRecord.nodeCode == '404') {
						//注销不成功
						list = [{
							title: '继续注销',
							handle: 'goOn'
						}, {
							title: '取消订单',
							type: 'info',
							handle: 'toCancel'
						}]
					} else if (this.result.hsCancelRecord.nodeCode == '410') {
						//前往补缴
						list = [{
							title: '前往补缴',
							handle: 'toAfterPay'
						}]
					} else if (this.result.hsCancelRecord.nodeCode == '406' || this.result.hsCancelRecord.nodeCode ==
						'407') {
						if (this.formData.balanceDisposal == 1) {
							if (!this.isCangeBank) {
								list = [{
									title: '修改收款信息',
									handle: 'changeBank'
								}]
							} else {
								list = [{
									title: '提交',
									handle: 'editBank'
								}]
							}
						}

					}
				}
				return list
			},
			typeNeedBank() {
				if (this.formData.productType == '0' || this.formData.productType == '2' || this.formData.productType ==
					'3' ||
					this.formData.productType == '5' || this.formData.productType == '8') {
					return true
				}
			}
		},
		data() {
			return {
				statusInfoList,
				// vehicleColors,
				disputeRefundBankName,
				isUpdate: false, //页面可修改
				isCangeBank: false, //银行信息可修改
				show: false, //余额处理方式
				bankNameshow: false, //银行卡开户行
				isLoading: false,
				showSmsDialog: false,
				isShowQuestionList: false,
				isPickStart: false,
				orderId: '', //订单id
				result: {},
				formData: {
					// applyPic: null, // 申请材料
					// applyReason: '', //申请理由详细描述
					// applyReasonType: '0', //申请理由				
					custMastId: '', //ETC用户ID			
					custIdNo: '', //ETC用户身份证
					// drivingLicense: null, //行驶证ocr识别信息
					// netUserNo: '', //互联网账户ID			
					// opBusinessType: '', // 操作业务类型 1-失效重新激活，2-遗失/损毁补办，3-设备故障更换，4-设备注销
					// mobileCode: '', // 短信验证码
					mobile: '', // 手机号码
					accountType: '', // 客户类型
					custName: '', //用户名称
					vehicleNo: '', //车牌
					vehicleColor: '', //车牌颜色
					cardNo: '', //ETC卡号
					obuNo: '', //OBU号
					isTrunk: '', //客货
					carType: '', // 车型1-6
					productType: '', // 产品类型
					applyChannel: '2', // 申请渠道 0-八桂行APP，1-公众出行APP，2-微信小程序，3-微信公众号，4-Web网上营业厅
					cancelReasonDetail: '', //注销原因
					balanceDisposal: '', //余额处理方式，默认1退银行账户
					beneficiaryName: '', //收款方户名
					bankName: '', //收款银行
					bankAccount: '', //银行账号
					hsNetAccountId: '', //互联网id
					netUserNo: '', //用户编号
				},
				cardType: '', //卡类型
				vehicleObj: {
					carNo: '',
					cardNo: '', //ETC卡号
					obuNo: '', //OBU号
				},
				uploadList: [],
				amountHandle: {
					text: '直接退至银行账户',
					value: '1'
				},
				handleIds: 0, //处理方式index
				bankNameIndex: null, //开户行index
				BalanceDisposalEnum: [],
				// otherBankName: '', //其他银行账户名
				companyInfo: { //退至单位余额信息展示
					custName: '',
					custIdNo: '', //单位脱敏证件号
					custMobile: '', //单位脱敏手机号
					userIdStr: '', //登录USERID
					loginName: '', //登录USERNAME
				},
				accountItem: null, //选择联系人账户银行信息，
				questionList: { //问题清单
					content: '',
					showTips: false,
					contentArr: []
				},
				// base64Img: '',
				base64ImgUrl: require('@/pagesC/static/add_icon.png'),
				isBankNameIndex: 0, //选中的下标
				isBankName: '', //选中的银行名
				bankNameList: [], //模糊查询银行列表
				keyword: ''
			}
		},
		onLoad(options) {
			if (options && options.cardNo) {
				this.cardNo = options.cardNo
			}
			if (options.orderId) {
				this.orderId = options.orderId
			}
			if (options.isUpdate) {
				this.isUpdate = options.isUpdate
			}
			if (options.isCangeBank) {
				this.isCangeBank = options.isCangeBank
			}

			this.bankNameList = [].concat(this.disputeRefundBankName)
			// uni.$once('setAccountItem', this.setAccount)
			this.getDetail()
		},
		methods: {
			// 银行选账户废弃
			// setAccount(e) {
			// 	console.log('eeeeee', e)
			// 	this.accountItem = {
			// 		...e
			// 	}
			// 	this.formData.bankAccount = this.accountItem.bankNoStr
			// 	this.formData.bankName = this.accountItem.bankName

			// 	//查询回填银行选择
			// 	let bankIndex = this.disputeRefundBankName.findIndex((item) => {
			// 		return item.label == this.formData.bankName;
			// 	});
			// 	if (bankIndex > -1) {
			// 		this.bankNameIndex = bankIndex
			// 	} else {
			// 		this.bankNameIndex = 8
			// 		this.otherBankName = this.formData.bankName
			// 	}
			// },
			onBlur() {
				if (!this.formData.bankAccount) return
				let reg = /^(\d{12,30})$/g
				if (!reg.test(this.formData.bankAccount)) {
					uni.showModal({
						title: "提示",
						content: '银行卡号长度或格式不合法，重新识别或重新输入',
						showCancel: false,
					});
					return
				}
			},
			toHandle(handle) {
				switch (handle) {
					case 'update':
						this.setUpdate()
						break;
					case 'toCancel':
						this.toCancel()
						break;
					case 'saveHandle':
						this.saveHandle()
						break;
					case 'goOn':
						//继续注销
						this.goOn()
						break;
					case 'toAfterPay':
						//前往补缴
						this.toAfterPay()
						break;
					case 'changeBank':
						//修改银行信息按钮
						this.isCangeBank = true
						break;
					case 'editBank':
						//修改银行信息按钮
						this.editBank()
						break;
					default:
						break;
				}
			},
			getLogoutEnum(result) {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					...this.formData,
				}
				console.log('params', params)
				this.$request.post(this.$interfaces.getLogoutEnum).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res', res.data)
						//初始化字典数据结构,个人用户去掉单位的字典
						let BalanceDisposalEnum = res.data.BalanceDisposalEnum

						if (this.formData.accountType == '0') {
							let arr = BalanceDisposalEnum.filter(item => {
								return item.code != '2'
							})
							// console.log('this.arr', arr)
							this.BalanceDisposalEnum = arr
						} else if (this.formData.accountType == '1' && !result.hsNetAccount) {
							//没有互联网账户的话，去掉2互联网退费
							let netArr = BalanceDisposalEnum.filter(item => {
								return item.code != '2'
							})
							console.log('this.arr', netArr)
							this.BalanceDisposalEnum = netArr
						} else {
							this.BalanceDisposalEnum = BalanceDisposalEnum
						}

						//余额处理方式
						this.amountHandle.value = this.formData.balanceDisposal

						console.log('this.BalanceDisposalEnum', this.BalanceDisposalEnum)
						//回填余额处理方式
						let index = this.BalanceDisposalEnum.findIndex((item) => {
							console.log('item1', item)
							return item.code == this.formData.balanceDisposal;
						});
						console.log('index', index)
						if (index > -1) {
							this.handleIds = index
							this.amountHandle.text = this.BalanceDisposalEnum[index].name
						}
						console.log('this.amountHandle', this.amountHandle)

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			copy(value) {
				uni.setClipboardData({
					data: value,
					// success: () => {
					// 	uni.showModal({
					// 		title: '复制',
					// 		content: '已复制订单编号' + value
					// 	})
					// }
				});
			},
			getDetail() {
				this.isLoading = true
				let params = {
					cardNo: this.cardNo,
					orderId: this.orderId
				}

				this.$request.post(this.$interfaces.getLogoutDetail, {
					data: params
				}).then(res => {
					console.log('详情信息=========>>>>>', res)
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						this.result = result
						this._initData(result)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			_initData(result) {
				//初始化回填数据
				this.vehicleObj.carNo = result.carNo
				this.vehicleObj.cardNo = result.cardNo
				this.vehicleObj.obuNo = result.obuNo

				this.formData = {
					...this.formData,
					...result.hsCancelRecord,
				}
				this.formData.custIdNo = result.custIdNo

				//注销不成功时，获取问题清单列表，有就显示，没有就不显示问题清单
				if (result.hsCancelRecord.nodeCode == '404') {
					this.getQuestionList()
				}

				//根据用户类型获取字典
				this.getLogoutEnum(result)
				//档案回填
				this.uploadList = result.uploadList
				console.log('resultresult', result)
				if (result.hsNetAccount) {
					//单位退回余额,根据客户id获取最新ETC账户信息
					// this.getEtcAccount(result)
					//单位信息需要存入脱敏信息
					this.companyInfo.custIdNo = result.hsNetAccount.cardNo
					this.companyInfo.custName = result.hsNetAccount.companyName
					this.companyInfo.custMobile = result.hsNetAccount.netMobile
					//单位互联网用户
					this.companyInfo.loginName = result.hsNetAccount.netLoginName
					this.companyInfo.userIdStr = result.hsNetAccount.netUserNo
				}

				// if (this.accountItem) {
				// 	this.formData.bankAccount = this.accountItem.bankNo
				// 	this.formData.bankName = this.accountItem.bankName
				// }

				//查询回填银行选择
				let bankIndex = this.disputeRefundBankName.findIndex((item) => {
					return item.label == this.formData.bankName;
				});
				if (bankIndex > -1) {
					this.bankNameIndex = bankIndex
				}
				// else {
				// 	this.bankNameIndex = 8
				// 	this.otherBankName = this.formData.bankName
				// }
			},
			showBankList() {
				this.keyword = ''
				this.bankNameshow = true
				this.$refs.popup.open('bottom')
				this.$nextTick(() => {
					this.bankNameList = [].concat(this.disputeRefundBankName)
					let index = this.bankNameList.findIndex((item) => {
						return item.label == this.formData.bankName;
					});
					if (index > -1) {
						//存在银行
						this.bankNameIndex = index
						this.isBankNameIndex = index
					}
					// console.log('变更后的index', this.bankNameIndex)
				})


			},
			searchChange(keyword) {
				this.bankNameIndex = 0
				this.isBankNameIndex = 0

				const searchResult = this.fuzzySearch(keyword, this.disputeRefundBankName);
				this.bankNameList = searchResult
			},
			searchClear() {

			},
			pickstart() {
				this.isPickStart = true
			},
			pickend() {
				this.isPickStart = false
			},
			pickerChange(e) {
				console.log('e', e)
				const value = e.detail.value
				this.isBankNameIndex = value[0]
			},
			selectConfirm() {
				if (this.isPickStart) return
				this.bankNameIndex = this.isBankNameIndex
				const label = this.bankNameList[this.isBankNameIndex].label
				this.formData.bankName = label
				// console.log('this.bankNameIndex', this.bankNameIndex)
				this.close()
			},
			fuzzySearch(keyword, dataArray) {
				// 将关键字转换为小写，便于不区分大小写的匹配
				const lowerKeyword = keyword.toLowerCase();
				// 使用filter方法进行模糊查询
				const result = dataArray.filter(item => {
					// 遍历对象的所有属性值，看是否有匹配的
					for (const key in item) {
						if (item.hasOwnProperty(key) && typeof item[key] === 'string') {
							// 将属性值转换为小写，便于不区分大小写的匹配
							const lowerValue = item[key].toLowerCase();

							// 如果属性值包含关键字，则认为匹配成功
							if (lowerValue.includes(lowerKeyword)) {
								return true;
							}
						}
					}
					return false;
				});
				return result;
			},
			close() {
				this.$refs.popup.close()
			},
			selectReturnConfirm(option) {
				console.log('option', option)
				this.handleIds = this.BalanceDisposalEnum.findIndex(item => item.code == option[0].value)
				this.amountHandle.text = option[0].label
				this.formData.balanceDisposal = option[0].value

			},
			toCancel() {
				uni.showModal({
					title: "取消订单",
					confirmText: '确定',
					// placeholderText: '请输入原因',
					// editable: true,
					success: (res) => {
						if (res.confirm) {
							// let reason = res.content || '无'
							this.cancelApplyOrder()
							// console.log('确认取消')
						}
					}

				});

			},
			cancelApplyOrder() {
				this.isLoading = true
				this.$request.post(this.$interfaces.logoutRecordCancel, {
					data: {
						orderId: this.result.id,
					}
				}).then(res => {
					this.isLoading = false
					console.log(res, '取消订单');
					if (res.code == 200) {

						// this.$emit('handleCancel')
						// uni.reLaunch({
						// 	url: '/pagesA/orderBusiness/order//orderList'
						// })
						//跳转成功页面 todo
						uni.reLaunch({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=14"
						})

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			onChange(imgList) {
				console.log('imgList', imgList)
				//照片上传
				if (this.uploadList.length == 0) {
					this.uploadList.push(imgList)
				} else {
					//覆盖
					let index = this.uploadList.findIndex((item) => {
						return item.uploadType == imgList.uploadType;
					});
					if (index > -1) {
						this.uploadList[index].uploadPath = imgList.uploadPath
					} else {
						this.uploadList.push(imgList)
					}
				}

				if (imgList.uploadType == '26') {
					let promise26 = this.sendUploadFile(imgList.base64Img, '22')
					if (!promise26) {
						uni.showModal({
							title: "提示",
							content: '系统正在开小差，请稍等片刻后再上传',
							showCancel: false,
						});
						this.$refs.uploadDetail.delImgByRef(imgList.uploadType)
					}
				}

				if (imgList.uploadType == '27') {
					let promise27 = this.sendUploadFile(imgList.base64Img, '23')
					if (!promise27) {
						uni.showModal({
							title: "提示",
							content: '系统正在开小差，请稍等片刻后再上传',
							showCancel: false,
						});
						this.$refs.uploadDetail.delImgByRef(imgList.uploadType)
					}
				}
			},
			sendUploadFile(base64Img, photoCode) {
				return new Promise((resolve, reject) => {
					let biz_content = {
						customer_id: this.formData.custMastId,
						photo_code: photoCode,
						scene: 6, // 销卡上传
						vehicle_code: this.formData.vehicleNo,
						vehicle_color: this.formData.vehicleColor,
						other_code: this.formData.cardNo
					};
					let params = {
						file_content: base64Img,
						method_code: "2", // 档案上传
						biz_content: JSON.stringify(biz_content),
					};
					this.$request.post(this.$interfaces.uploadFile, {
						data: params
					}).then(res => {
						console.log('上传图片===>>>', res)
						if (res.code == 200) {
							// let imgList = {
							// 	uploadType: res.data.photo_code,
							// 	uploadPath: res.data.file_url
							// }
							//身份证上传
							// if (this.uploadList.length == 0) {
							// 	this.uploadList.push(imgList)
							// } else {
							//覆盖原详情值
							let index = this.uploadList.findIndex((item) => {
								return item.uploadType == res.data.photo_code;
							});
							if (index > -1) {
								this.uploadList[index].uploadPath = res.data.code
							}
							// else {
							// 	this.uploadList.push(imgList)
							// }
							// }
							resolve(true)
						} else {
							reject(false)
						}
					}).catch(err => {
						reject(false)
					})
				})
			},
			setUpdate() {
				this.isUpdate = !this.isUpdate
			},
			saveHandle() {
				//其他银行开户行信息赋值
				// if (this.bankNameIndex == 8) {
				// 	this.formData.bankName = this.otherBankName
				// }
				if (!this.validData()) {
					return
				}
				this.showSmsDialog = true
				this.$refs.smsDialog.getCaptcha()
			},
			editBank() {
				//修改收款信息
				uni.showModal({
					title: "修改收款信息",
					confirmText: '确定',
					success: (res) => {
						if (res.confirm) {
							this.edit()
						}
					}

				});
			},
			edit() {
				if (this.typeNeedBank && this.result.amount > 0) {
					if (!this.formData.bankName) {
						uni.showModal({
							title: "提示",
							content: '请先输入开户行',
							showCancel: false,
						});
						return
					}
					if (!this.formData.bankAccount) {
						uni.showModal({
							title: "提示",
							content: '请先输入银行卡号',
							showCancel: false,
						});
						return
					}
				}


				let params = {
					id: this.result.id,
					balanceDisposal: '1',
					beneficiaryName: this.formData.beneficiaryName,
					bankName: this.formData.bankName,
					bankAccount: this.formData.bankAccount,
				}

				if (this.isLoading) return;
				this.isLoading = true;

				this.$request.post(this.$interfaces.logoutUpdate, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res', res)
						//跳转成功页面 todo
						uni.reLaunch({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=13&cardNo=" + this
								.cardNo + '&orderId=' + this.orderId
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			smsConfirm(mobileCode) {
				this.isLoading = true
				let params = {
					mobile: this.formData.mobile,
					mobileCode: mobileCode
				}
				this.$request.post(this.$interfaces.smsCheckCodeV2, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('身份验证', res)
						this.save()
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
				this.showSmsDialog = false
			},
			validData() {
				if (this.formData.accountType == '0') {
					let frontIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '22';
					});
					if (frontIndex == -1) {
						uni.showModal({
							title: "提示",
							content: '请先上传身份证人像面',
							showCancel: false,
						});
						return false
					}
					let backIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '23';
					});
					if (backIndex == -1) {
						uni.showModal({
							title: "提示",
							content: '请先上传身份证国徽面',
							showCancel: false,
						});
						return false
					}
				}

				if (this.formData.accountType == '1') {
					//单位车辆
					let letterIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '25';
					});
					if (letterIndex == -1) {
						uni.showModal({
							title: "提示",
							content: '请先上传委托书',
							showCancel: false,
						});
						return false
					}

					let frontIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '26';
					});
					if (frontIndex == -1) {
						uni.showModal({
							title: "提示",
							content: '请先上传身份证人像面',
							showCancel: false,
						});
						return false
					}
					let backIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '27';
					});
					if (backIndex == -1) {
						uni.showModal({
							title: "提示",
							content: '请先上传身份证国徽面',
							showCancel: false,
						});
						return false
					}
				}

				if (this.typeNeedBank && this.result.amount > 0) {
					if (this.formData.balanceDisposal == '1') {
						if (!this.formData.bankName) {
							uni.showModal({
								title: "提示",
								content: '请先输入开户行',
								showCancel: false,
							});
							return false
						}
						if (!this.formData.bankAccount) {
							uni.showModal({
								title: "提示",
								content: '请先输入银行卡号',
								showCancel: false,
							});
							return false
						}
					}
				}

				return true
			},
			save() {
				this.uploadList.forEach(item => {
					if (item.base64Img) {
						delete item.base64Img
					}
				})

				let params = {
					id: this.result.id,
					balanceDisposal: this.formData.balanceDisposal,
					beneficiaryName: this.formData.beneficiaryName,
					bankName: this.formData.bankName,
					bankAccount: this.formData.bankAccount,
					uploadList: this.uploadList
				}


				if (this.isLoading) return;
				this.isLoading = true;

				this.$request.post(this.$interfaces.logoutUpdate, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res', res)
						//跳转成功页面 todo
						uni.reLaunch({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=13&cardNo=" + this
								.cardNo + '&orderId=' + this.orderId
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			toOrderProgress() {
				uni.navigateTo({
					url: 'pagesC/logoutBussiness/orderDetail/orderProgressDetail'
				})
			},
			showReturnType() {
				this.show = true
			},
			// hadAccount() {
			// 	uni.navigateTo({
			// 		url: '/pagesB/disputeRefund/accountList?type=onlineLogout&isDetail=true&custName=' + this
			// 			.formData.custName + '&cardNo=' + this.cardNo
			// 	})
			// },
			bankImg() {
				console.log('识别图片', this.$refs.ocrBank)
				this.$refs.ocrBank.ChooseImage()
			},
			onBankChange(imgList, encryptedData) {
				console.log('encryptedData', encryptedData)
				if (this.uploadList.length == 0) {
					this.uploadList.push(imgList)
				} else {
					//覆盖
					let index = this.uploadList.findIndex((item) => {
						return item.uploadType == imgList.uploadType;
					});
					if (index > -1) {
						this.uploadList[index].uploadPath = imgList.uploadPath
					} else {
						this.uploadList.push(imgList)
					}
				}


				if (encryptedData.bankNo) {
					this.formData.bankAccount = encryptedData.bankNo
					// let trimBankNo = encryptedData.bankNo.replace(/\s*/g, "")
				}
				if (encryptedData.bankName) {
					let index = this.disputeRefundBankName.findIndex((item) => {
						return item.label == encryptedData.bankName;
					});
					if (index > -1) {
						//存在银行
						this.bankNameIndex = index
						//银行名赋值
						this.formData.bankName = encryptedData.bankName
					}
					// else {
					// 	this.bankNameIndex = 8
					// 	//银行名赋值
					// 	this.otherBankName = encryptedData.bankName
					// }
				}
			},
			delImg(uploadType) {
				let index = this.uploadList.findIndex((item) => {
					return item.uploadType == uploadType;
				});
				if (index > -1) {
					this.uploadList.splice(index, 1)
				}
				console.log('index', index)
				console.log('this.uploadList', this.uploadList)
			},
			//去充值页面判断
			goToRecharge() {
				let vehicleInfo = {
					vehicleCode: this.formData.vehicleNo,
					vehicleColor: this.formData.vehicleColor,
					cardNo: this.formData.cardNo
				}
				this.$goToRecharge(vehicleInfo)
			},
			goOn() {
				//继续注销，调用手动注销接口
				uni.showModal({
					title: "继续注销",
					content: '确定执行继续注销吗?',
					success: (res) => {
						if (res.confirm) {
							this.logoutDeviceHandle()
						}
					}

				});
			},
			logoutDeviceHandle() {
				let params = {
					orderId: this.result.id,
				}

				if (this.isLoading) return;
				this.isLoading = true;

				this.$request.post(this.$interfaces.logoutDeviceHandle, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res', res)
						//跳转成功页面 todo
						uni.reLaunch({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=15&cardNo=" + this
								.cardNo + '&orderId=' + this.orderId
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			getQuestionList() {
				let params = {
					cardNo: this.cardNo,
					callType: '1' //问题清单
				}

				console.log(params)
				this.$request.post(this.$interfaces.beforCheckLogout, {
					data: params
				}).then(res => {
					console.log(res)
					if (res.code == 200) {
						if (res.data && res.data.length > 0) {
							//不满足条件温馨提示
							this.questionList.contentArr = res.data
						}
					}
				}).catch(err => {
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			question() {
				let params = {
					cardNo: this.cardNo,
					callType: '1' //问题清单
				}

				this.isShowQuestionList = true
				console.log(params)
				this.$request.post(this.$interfaces.beforCheckLogout, {
					data: params
				}).then(res => {
					console.log(res)
					if (res.code == 200) {
						if (res.data && res.data.length > 0) {
							//不满足条件温馨提示
							this.questionList.showTips = true
							this.questionList.contentArr = res.data
						} else {
							this.isShowQuestionList = false
						}
					} else {
						//报错温馨提示
						this.questionList.content = res.msg
						this.questionList.showTips = true
					}
				}).catch(err => {
					this.isShowQuestionList = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			listConfirm() {
				this.isShowQuestionList = false
			},
			toAfterPay() {
				//前往补缴页面
				//有车辆去车辆列表
				uni.navigateTo({
					url: '/pagesB/vehicleBusiness/vehicleList?fontType=' + 'afterPay'
				})
			},
			toProgress() {
				//查看办理进度
				uni.navigateTo({
					url: './orderProgressDetail?hsCancelRecordId=' + this.result.id
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.apply-after-sale {
		padding-bottom: 180rpx;
		font-family: PingFangSC-Regular, PingFang SC;
	}

	.section {
		background: #ffffff;
		border-radius: 10rpx;
	}


	.order-detail__title {
		height: 68rpx;
		padding: 0 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #ffffff;
	}

	.title-hd {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.title-hd__label {
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.title-hd__value {
		// margin-left: 10rpx;
		color: #323435;
		font-weight: 400;
	}

	.title-bd {
		flex: 0 0 210rpx;
		width: 200rpx;
		height: 31rpx;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		border-left: 1rpx solid #C6C6C6;
	}

	.title-bd .copy {
		margin-right: 50rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #3874FF;
	}

	.section {
		margin: 20rpx;
	}

	.last-wrapper {
		margin-top: 20rpx;
		margin-left: 20rpx;
	}


	.order-progress {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;

		.status-wrapper {
			padding: 0 30rpx;
			display: flex;
			align-items: center;

		}

		.status-label {
			flex: 0 0 150rpx;
			width: 150rpx;
		}

		.status-value {
			position: relative;
			flex: 1;
			height: 68rpx;
			line-height: 68rpx;
			padding-left: 30rpx;
			font-size: 26rpx;
			font-weight: 400;
			border-radius: 8rpx;

			.question-list {
				position: absolute;
				top: 6rpx;
				right: 8rpx;
				width: 125rpx;
				background: linear-gradient(180deg, #FF8FA4 0%, #F54455 100%);
				border-radius: 8rpx;
				border: 1rpx solid #F64C5C;
				font-size: 23rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 52rpx;
				text-align: center;
			}
		}



		.status-value.info {
			background: #F6F6F6;
			color: #333333;
		}


		.status-value.success {
			background: rgba(0, 189, 50, 0.1);
			color: #00BD32;
		}

		.status-value.warnning {
			background: rgba(255, 145, 0, 0.1);
			color: #FF9100;
		}

		.status-value.error {
			background: rgba(255, 84, 84, 0.1);
			color: #FF5454;
		}

		.progress-tip {
			padding: 30rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #999999;
		}

	}

	.card-price {
		margin: 0rpx 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
		line-height: 26rpx;
	}

	.tips {
		margin: 20rpx 30rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
		line-height: 24rpx;
	}

	.amount-select {
		margin: 0 30rpx;
		padding-bottom: 36rpx;
		display: flex;
		align-items: center;

		.label {
			flex: 0 0 200rpx;
			width: 200rpx;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			line-height: 26rpx;
		}

		.select-wrapper {
			flex: 1;
			position: relative;
			line-height: 64rpx;
			padding: 0 30rpx;
			width: 100%;
			height: 64rpx;
			background: #FFFFFF;
			border-radius: 8rpx;
			border: 2rpx solid #DDDDDD;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;

			.select-icon {
				position: absolute;
				right: 13rpx;
				top: 14rpx;
			}
		}
	}

	.question-wrapper {
		display: flex;

		.question-text {
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #0081FF;
		}
	}

	.bank-wrapper {
		margin: 0 30rpx;
		padding-bottom: 36rpx;

		.content {
			flex: 1;
			width: 100%;
		}

		.bank-item {
			display: flex;
			margin-bottom: 16rpx;
			align-items: center;

			.label {
				flex: 0 0 147rpx;
				width: 147rpx;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 42rpx;
			}

			.value {
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 42rpx;
			}

			.select-wrapper {
				flex: 1;
				position: relative;
				line-height: 64rpx;
				padding: 0 30rpx;
				// width: 481rpx;
				width: 100%;
				height: 64rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				border: 2rpx solid #DDDDDD;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;


				.select-input {
					height: 64rpx;
					line-height: 64rpx;
					padding-right: 30rpx;
				}

				.bank-img {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 64rpx;
					width: 64rpx;
					position: absolute;
					right: 0;
					top: 0;
				}

				.select-icon {
					position: absolute;
					right: 13rpx;
					top: 14rpx;
				}
			}


			.other-input {
				margin-top: 16rpx;
				width: 100%;
				height: 64rpx;
				line-height: 64rpx;
				padding: 0 30rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				border: 2rpx solid #DDDDDD;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
			}
		}

		.other {
			align-items: flex-start;

			.label {
				margin-top: 14rpx;
			}
		}
	}

	.account-wrapper {
		padding: 30rpx;

		.account-info {
			margin-top: 20rpx;
			padding: 15rpx 30rpx 30rpx 30rpx;
			background: #F8F8F8;
			border-radius: 8rpx;

			.account-name {
				font-size: 26rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #323435;
				line-height: 37rpx;
			}

			.account-item {
				display: flex;
				align-items: center;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				margin-top: 20rpx;

				.label {
					line-height: 26rpx;
					flex: 0 0 203rpx;
					width: 203rpx;
				}

				.value {
					line-height: 26rpx;
				}
			}
		}
	}

	/deep/.weui-card-hd-title {
		font-weight: bold;
	}

	/deep/.title-wrapper {
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;
		background: #ffffff;
		padding: 30rpx;
	}

	.search-wrapper {
		height: 600rpx;

		.search-title {
			justify-content: space-between;
			height: 80rpx;
			line-height: 80rpx;
			padding: 0 40rpx;
			background-color: #fbf9fe;

			.cancel {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 42rpx;
			}

			.confirm {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #0066E9;
				line-height: 42rpx;
			}

			.gray {
				color: #999999;
			}
		}

		.search-input {
			height: 100rpx;
			line-height: 100rpx;
			font-size: 36rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #0066E9;
			text-align: center;
			padding: 0 30rpx;
		}

		.picker-wrapper {
			height: 100%;

			.picker-view {
				height: calc(100% - 166rpx);

				.picker-item {
					text-align: center;
					line-height: 68rpx;
					font-size: 34rpx;
				}
			}
		}

		.search-bottom {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 84rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 33rpx;
			background-color: #f8f8f8;


			.bottom-left,
			.bottom-right {
				display: flex;
				align-items: center;
			}

			.bottom-left {
				margin-right: 50rpx;
			}

			.circle {
				width: 15rpx;
				height: 15rpx;
				margin-right: 10rpx;
				background: #0066E9;
				border-radius: 50%;

			}

			.red {
				background: #F65B5B;
			}
		}
	}
</style>