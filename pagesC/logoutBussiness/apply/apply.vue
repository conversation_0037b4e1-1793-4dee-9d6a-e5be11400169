<template>
	<view class="apply-after-sale">
		<logoutTitleStep current="0"></logoutTitleStep>
		<view class="section">
			<vehicle :vehicleObj="vehicleObj">
				<!-- <template #rightBtn>
					<view class="question-wrapper" @click="questionClick">
						<view class="question-text" style="margin-right: 10rpx;">
							注销帮助
						</view>
						<image style="width: 28rpx;height: 28rpx;margin-top: 3rpx;" src="../../static/icon-question.png"
							mode=""></image>
					</view>
				</template> -->
			</vehicle>
		</view>
		<view class="section">
			<commonTitle title="提交材料">
				<template #rightBtn>
					<view class="question-wrapper">
						<view class="question-text g-flex">
							<!-- <view class="">
								查看样例
							</view> -->
							<view class="download" style="margin-left: 10rpx;" v-if="formData.accountType == '1'"> |
								委托书模板下载</view>
						</view>
					</view>
				</template>
			</commonTitle>
			<uploadPerson @on-change="onChange" @delImg="delImg" :userInfo="formData">
			</uploadPerson>
			<!-- 单位用户上传委托书 -->
			<uploadLetter @on-change="onChange" @delImg="delImg" v-if="formData.accountType == '1'"
				:userInfo="formData">
			</uploadLetter>
		</view>
		<template v-if="typeNeedBank && result.amount > 0">
			<view class="section">
				<commonTitle title="余额处理"></commonTitle>
				<view class="card-price">
					卡内可用余额：{{moneyFilter(result.amount)+'元'}}
				</view>
				<view class="tips">
					余额仅供参考，以实际到账信息为准
				</view>
				<view class="amount-select">
					<view class="label">余额处理方式</view>
					<view class="select-wrapper" @click="show = true">
						<view class="select-value">
							{{amountHandle.text}}
						</view>
						<image class="select-icon" src="../../static/arrow-show.png" style="width: 34rpx;height: 26rpx;"
							mode=""></image>
					</view>
				</view>
			</view>
			<view class="section" v-if="formData.balanceDisposal == 1">
				<commonTitle title="银行收款信息">
					<!-- 需求不兼容，暂时废弃 -->
					<!-- <template #rightBtn>
						<view class="question-wrapper" @click="hadAccount">
							<view class="question-text">
								已有银行账户
							</view>
						</view>
					</template> -->
				</commonTitle>
				<view class="bank-wrapper">
					<view class="bank-item" style="margin-bottom: 27rpx;">
						<view class="label">
							户名
						</view>
						<view class="value">
							{{formData.custName}}
						</view>
					</view>
					<view class="bank-item other">
						<view class="label">
							<text style="color: #F65B5B;">*</text>开户行
						</view>
						<view class="content">
							<view class="select-wrapper" @click="showBankList">
								<view class="select-value" v-if="!formData.bankName">
									请选择开户银行
								</view>
								<view class="select-value" v-else>
									{{formData.bankName}}
								</view>
								<image class="select-icon" src="../../static/arrow-show.png"
									style="width: 34rpx;height: 26rpx;" mode=""></image>
							</view>
							<!-- <input v-if="bankNameIndex == 8" class="other-input" v-model="otherBankName" type="text"
								placeholder="请输入开户行信息"> -->
						</view>
					</view>
					<view class="bank-item">
						<view class="label">
							<text style="color: #F65B5B;">*</text>银行卡号
						</view>
						<view class="select-wrapper">
							<input class="select-input" maxlength="30" v-model="formData.bankAccount" @blur="onBlur"
								type="number" placeholder="请输入ETC开户人名下的银行卡号">
							<view class="bank-img" @click="bankImg">
								<image src="../../static/photo_icon.png" style="width: 40rpx;height: 40rpx" mode="">
								</image>
							</view>
						</view>

					</view>
				</view>
			</view>
			<view class="section" v-if="formData.balanceDisposal == 2 && formData.accountType == '1'">
				<view class="account-wrapper">
					<view class="title">
						余额将退至以下ETC账户，请仔细核对单位名称、证件号、登录账号、手机号等内容
					</view>
					<view class="account-info">
						<view class="account-name">
							{{companyInfo.custName}}
						</view>
						<view class="account-item">
							<view class="label">
								账户编号：
							</view>
							<view class="value">
								{{companyInfo.userIdStr}}
							</view>
						</view>
						<view class="account-item">
							<view class="label">
								登录编号：
							</view>
							<view class="value">
								{{companyInfo.loginName}}
							</view>
						</view>
						<view class="account-item">
							<view class="label">
								证件号：
							</view>
							<view class="value">
								{{companyInfo.custIdNo}}
							</view>
						</view>
						<view class="account-item">
							<view class="label">
								手机号：
							</view>
							<view class="value">
								{{companyInfo.custMobile}}
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="tips" v-if="formData.balanceDisposal == 1">
				<view class="tip">
					为确保资金安全，余额仅支持退至ETC开户人银行账户，请仔细核对信息。
				</view>
				<view class="tip" style="margin-top: 10rpx;">
					如有疑问，请联系在线客服
				</view>
			</view>
		</template>
		<view class="error-wrapper" v-if="errorText && errorId">
			<text class="error-text">{{errorText}}</text>
		</view>
		<ocrBankCard ref="ocrBank" @on-change="onBankChange" :userInfo="formData" v-if="formData.custMastId">
		</ocrBankCard>
		<u-select v-model="show" :default-value="[handleIds]" value-name="code" label-name="name"
			:list="BalanceDisposalEnum" @confirm="selectReturnConfirm">
		</u-select>
		<uni-popup ref="popup" background-color="#FFFFFF;">
			<view class="search-wrapper">
				<view class="search-title g-flex g-flex-align-center">
					<view class="cancel" @click="close">
						取消
					</view>
					<view class="confirm" :class="isPickStart?'gray':''" @click="selectConfirm">
						确认
					</view>
				</view>
				<view class="search-input">
					<u-search v-model="keyword" @change="searchChange" :show-action="false" @clear="searchClear"
						:clearabled="true" placeholder="输入关键字快速查询"></u-search>
				</view>
				<view class="picker-wrapper" v-if="bankNameshow">
					<picker-view :indicator-style="indicatorStyle" :value="[bankNameIndex]" @pickstart="pickstart"
						@pickend="pickend" @change="pickerChange" class="picker-view">
						<picker-view-column>
							<view class="picker-item" v-for="(item,index) in bankNameList" :key="index">
								{{item.label}}
							</view>
						</picker-view-column>
					</picker-view>
				</view>
			</view>
		</uni-popup>
		<smsDialog ref="smsDialog" :show.sync="showSmsDialog" :msgType="'logout'" :mobile="formData.mobile"
			@confirm="smsConfirm">
		</smsDialog>
		<tButton :buttonList="buttonList" @save="saveHandle"></tButton>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import logoutTitleStep from '@/pagesC/components/logout-title-step/logout-title-step.vue'
	import tButton from '@/pagesC/components/t-button/t-button.vue'
	import vehicle from '@/pagesC/components/vehicle/vehicle.vue';
	import uploadPerson from '@/pagesC/components/logout-upload/uploadPerson.vue';
	import uploadLetter from '@/pagesC/components/logout-upload/uploadLetter.vue';
	import ocrBankCard from '@/pagesC/components/logout-upload/ocrBankCard.vue';
	import commonTitle from '@/pagesC/components/common-title/common-title.vue'
	import smsDialog from '@/pagesC/components/logout-dialog/sms-dialog.vue'
	import {
		getLoginUserInfo,
		getAccountId,
		getCurrUserInfo,
		getEtcAccountInfo,
		setOpenidForRead,
		getOpenidForRead
	} from '@/common/storageUtil.js'
	import {
		disputeRefundBankName
	} from '@/common/const/optionData.js'
	import {
		error
	} from '../../../common/method/log';
	export default {
		components: {
			tLoading,
			logoutTitleStep,
			tButton,
			vehicle,
			uploadPerson,
			uploadLetter,
			ocrBankCard,
			commonTitle,
			smsDialog
		},
		data() {
			return {
				disputeRefundBankName,
				isLoading: false,
				show: false,
				bankNameshow: false,
				showSmsDialog: false,
				isPickStart: false,
				buttonList: [{
					title: '提交',
					handle: 'save'
				}],
				result: {},
				formData: {
					// applyPic: null, // 申请材料
					// applyReason: '', //申请理由详细描述
					// applyReasonType: '0', //申请理由				
					custMastId: '', //ETC用户ID			
					custIdNo: '', //ETC用户身份证
					// drivingLicense: null, //行驶证ocr识别信息
					// netUserNo: '', //互联网账户ID			
					// opBusinessType: '', // 操作业务类型 1-失效重新激活，2-遗失/损毁补办，3-设备故障更换，4-设备注销
					// mobileCode: '', // 短信验证码
					mobile: '', // 手机号码
					accountType: '', // 互联网用户类型
					custName: '', //用户名称
					vehicleNo: '', //车牌
					vehicleColor: '', //车牌颜色
					cardNo: '', //ETC卡号
					obuNo: '', //OBU号
					isTrunk: '', //客货
					carType: '', // 车型1-6
					productType: '', // 产品类型
					applyChannel: '2', // 申请渠道 0-八桂行APP，1-公众出行APP，2-微信小程序，3-微信公众号，4-Web网上营业厅
					cancelReasonDetail: '', //注销原因
					balanceDisposal: '1', //余额处理方式，默认1退银行账户
					beneficiaryName: '', //收款方户名
					bankName: '', //收款银行
					bankAccount: '', //银行卡号
					hsNetAccountId: '', //互联网id
					netUserNo: '', //用户编号
				},
				cardType: '', //卡片类型
				vehicleObj: {
					carNo: '',
					cardNo: '', //ETC卡号
					obuNo: '', //OBU号
				},
				uploadList: [],
				amountHandle: {
					text: '直接退至银行账户',
					value: '1'
				},
				handleIds: 0, //处理方式index
				companyInfo: {
					custName: '',
					custIdNo: '', //单位脱敏证件号
					custMobile: '', //单位脱敏手机号
					userIdStr: '', //登录USERID
					loginName: '', //登录USERNAME
				},
				bankNameIndex: 0, //开户行index
				BalanceDisposalEnum: [],
				// otherBankName: '', //其他银行账户名
				errorId: null,
				errorText: '',
				base64ImgUrl: require('@/pagesC/static/search_fail.png'),
				vehicleInfo: {}, //缓存
				isBankNameIndex: 0, //选中的下标
				isBankName: '', //选中的银行名
				bankNameList: [], //模糊查询银行列表
				keyword: '',
				imgLoading: false, //图片补传防抖
			}
		},
		watch: {
			'formData.bankName'(val) {
				if (this.errorId == 4) {
					this.errorId = null
					this.errorText = ''
				}
			},
			'formData.bankAccount'(val) {
				if (this.errorId == 5) {
					this.errorId = null
					this.errorText = ''
				}
			}
		},
		computed: {
			typeNeedBank() {
				if (this.vehicleInfo.gxCardType == '0' || this.vehicleInfo.gxCardType == '2' || this.vehicleInfo
					.gxCardType == '3' ||
					this.vehicleInfo.gxCardType == '5' || this.vehicleInfo.gxCardType == '8') {
					return true
				}
			}
		},
		onLoad(options) {
			if (options.cancelReasonDetail) {
				this.formData.cancelReasonDetail = options.cancelReasonDetail || ''
			}

			this.bankNameList = [].concat(this.disputeRefundBankName)
			//单次监听联系人账户事件
			// uni.$once('setAccountItem', this.setAccount)
			this._initData()
			this.getDetail()


		},
		methods: {
			onBlur() {
				if (!this.formData.bankAccount) return
				let reg = /^(\d{12,30})$/g
				if (!reg.test(this.formData.bankAccount)) {
					uni.showModal({
						title: "提示",
						content: '银行卡号长度或格式不合法，重新识别或重新输入',
						showCancel: false,
					});
					return
				}
			},
			_initData() {

				let vehicleInfo = this.$store.getters['afterSale/afterSaleVehicle'] || {};
				this.vehicleInfo = vehicleInfo
				console.log('vehicleInfo', vehicleInfo)
				console.log('getEtcAccountInfo', getEtcAccountInfo())
				console.log('getCurrUserInfo', getCurrUserInfo())
				console.log('getLoginUserInfo', getLoginUserInfo())

				if (Object.keys(vehicleInfo).length) {
					this.formData.vehicleColor = vehicleInfo.vehicleColor || '';
					this.formData.vehicleNo = vehicleInfo.vehicleCode || '';
					this.formData.cardNo = vehicleInfo.cardNo || '';
					this.formData.obuNo = vehicleInfo.obuNo || '';
					//isTrunk客货
					this.formData.isTrunk = vehicleInfo.vehicleType || ''
					//carType车型
					this.formData.carType = vehicleInfo.vehicleClass
					//bindingChannel绑定渠道
					this.formData.productType = vehicleInfo.gxCardType || '';
					this.cardType = vehicleInfo.cardType || ''
				}

				this.formData.mobile = getEtcAccountInfo().mobile || ''
				this.formData.accountType = getCurrUserInfo().customer_type || ''
				console.log('getCurrUserInfo().customer_type ', getCurrUserInfo().customer_type)

				this.formData.custName = getCurrUserInfo().customer_name || ''
				this.formData.custIdNo = getCurrUserInfo().certificates_code || ''
				//互联网用户信息
				// this.formData.hsNetAccountId = getLoginUserInfo().userIdStr || ''
				this.formData.netUserNo = getLoginUserInfo().userNo || ''
				// this.formData.opBusinessType = options.opBusinessType || '';
				this.formData.custMastId = getEtcAccountInfo().custMastId

				//车辆信息
				this.vehicleObj.carNo = this.formData.vehicleNo
				this.vehicleObj.cardNo = this.formData.cardNo
				this.vehicleObj.obuNo = this.formData.obuNo
			},
			getLogoutEnum(result) {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					...this.formData,
				}
				console.log('params', params)
				this.$request.post(this.$interfaces.getLogoutEnum).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res', res.data)
						//初始化字典数据结构,个人用户去掉单位的字典
						let BalanceDisposalEnum = res.data.BalanceDisposalEnum

						if (this.formData.accountType == '0') {
							let arr = BalanceDisposalEnum.filter(item => {
								return item.code != '2'
							})
							// console.log('this.arr1', arr)
							this.BalanceDisposalEnum = arr
						} else if (this.formData.accountType == '1' && !result.hsNetAccount) {
							//没有互联网账户的话，去掉2互联网退费
							let netArr = BalanceDisposalEnum.filter(item => {
								return item.code != '2'
							})
							// console.log('this.arr2', arr)
							this.BalanceDisposalEnum = netArr
						} else {
							this.BalanceDisposalEnum = BalanceDisposalEnum
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			chargeNetAccount(result) {
				//根据互联网返回信息确定获取的银行接口
				this.getLogoutEnum(result)
				console.log('result', result)
				if (result.hsNetAccount) {
					//单位信息需要存入脱敏信息
					this.companyInfo.custIdNo = result.hsNetAccount.cardNo
					this.companyInfo.custName = result.hsNetAccount.companyName
					this.companyInfo.custMobile = result.hsNetAccount.netMobile
					//单位互联网用户
					this.companyInfo.loginName = result.hsNetAccount.netLoginName
					this.companyInfo.userIdStr = result.hsNetAccount.netUserNo
				}
			},
			getDetail() {
				this.isLoading = true
				let params = {
					cardNo: this.formData.cardNo
				}

				this.$request.post(this.$interfaces.getLogoutDetail, {
					data: params
				}).then(res => {
					console.log('详情信息=========>>>>>', res)
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						this.result = result
						//判断是否需要互联网账户。
						this.chargeNetAccount(result)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},

			showBankList() {
				this.keyword = ''
				this.bankNameshow = true
				this.$refs.popup.open('bottom')
				this.$nextTick(() => {
					this.bankNameList = [].concat(this.disputeRefundBankName)
					let index = this.bankNameList.findIndex((item) => {
						return item.label == this.formData.bankName;
					});
					if (index > -1) {
						//存在银行
						this.bankNameIndex = index
						this.isBankNameIndex = index
					}
					// console.log('变更后的index', this.bankNameIndex)
				})


			},
			searchChange(keyword) {
				this.bankNameIndex = 0
				this.isBankNameIndex = 0

				const searchResult = this.fuzzySearch(keyword, this.disputeRefundBankName);
				this.bankNameList = searchResult
			},
			searchClear() {

			},
			pickstart() {
				this.isPickStart = true
			},
			pickend() {
				this.isPickStart = false
			},
			pickerChange(e) {
				console.log('e', e)
				const value = e.detail.value
				this.isBankNameIndex = value[0]
			},
			selectConfirm() {
				if (this.isPickStart) return
				this.bankNameIndex = this.isBankNameIndex
				const label = this.bankNameList[this.isBankNameIndex].label
				this.formData.bankName = label
				// console.log('this.bankNameIndex', this.bankNameIndex)
				this.close()
			},
			fuzzySearch(keyword, dataArray) {
				// 将关键字转换为小写，便于不区分大小写的匹配
				const lowerKeyword = keyword.toLowerCase();
				// 使用filter方法进行模糊查询
				const result = dataArray.filter(item => {
					// 遍历对象的所有属性值，看是否有匹配的
					for (const key in item) {
						if (item.hasOwnProperty(key) && typeof item[key] === 'string') {
							// 将属性值转换为小写，便于不区分大小写的匹配
							const lowerValue = item[key].toLowerCase();

							// 如果属性值包含关键字，则认为匹配成功
							if (lowerValue.includes(lowerKeyword)) {
								return true;
							}
						}
					}
					return false;
				});
				return result;
			},
			close() {
				this.$refs.popup.close()
			},
			selectReturnConfirm(option) {
				console.log('option', option)
				this.handleIds = this.BalanceDisposalEnum.findIndex(item => item.code == option[0].value)
				this.amountHandle.text = option[0].label
				this.formData.balanceDisposal = option[0].value

			},
			onChange(imgList) {
				//清除错误信息
				this.clearErrorText(imgList.uploadType)
				//身份证上传
				if (this.uploadList.length == 0) {
					this.uploadList.push(imgList)
				} else {
					//覆盖
					let index = this.uploadList.findIndex((item) => {
						return item.uploadType == imgList.uploadType;
					});
					if (index == -1) {
						this.uploadList.push(imgList)

					} else {
						this.uploadList[index].uploadPath = imgList.uploadPath
					}
				}
			},
			clearErrorText(imgType) {
				if (imgType == '22' || imgType == '26') {
					if (this.errorId == 1) {
						this.errorId = null
						this.errorText = ''
					}
				} else if (imgType == '23' || imgType == '27') {
					if (this.errorId == 2) {
						this.errorId = null
						this.errorText = ''
					}

				} else if (imgType == '25') {
					if (this.errorId == 3) {
						this.errorId = null
						this.errorText = ''
					}
				}
			},
			delImg(uploadType) {
				let index = this.uploadList.findIndex((item) => {
					return item.uploadType == uploadType;
				});
				if (index > -1) {
					this.uploadList.splice(index, 1)
				}
				console.log('index', index)
				console.log('this.uploadList', this.uploadList)
			},
			bankImg() {
				console.log('识别图片', this.$refs.ocrBank)
				this.$refs.ocrBank.ChooseImage()
			},
			onBankChange(imgList, encryptedData) {
				console.log('imgList', imgList)
				if (this.uploadList.length == 0) {
					this.uploadList.push(imgList)
				} else {
					//覆盖
					let index = this.uploadList.findIndex((item) => {
						return item.uploadType == imgList.uploadType;
					});
					if (index > -1) {
						this.uploadList[index].uploadPath = imgList.uploadPath
					} else {
						this.uploadList.push(imgList)
					}
				}


				if (encryptedData.bankNo) {
					this.formData.bankAccount = encryptedData.bankNo
					// let trimBankNo = encryptedData.bankNo.replace(/\s*/g, "")
				}
				if (encryptedData.bankName) {
					let index = this.disputeRefundBankName.findIndex((item) => {
						return item.label == encryptedData.bankName;
					});
					if (index > -1) {
						//存在银行
						this.bankNameIndex = index
						//银行名赋值
						this.formData.bankName = encryptedData.bankName
					}
					// else {
					// 	this.bankNameIndex = 8
					// 	//银行名赋值
					// 	this.otherBankName = encryptedData.bankName
					// }
				}
			},
			questionClick() {

			},
			saveHandle() {
				if (this.result.amount == null || this.result.amount == undefined) {
					uni.showModal({
						title: "提示",
						content: '未获取到账户余额信息，请重新获取',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								this.getDetail()
							}
						}
					});
					return
				}
				//其他银行开户行信息赋值
				// if (this.bankNameIndex == 8) {
				// 	this.formData.bankName = this.otherBankName
				// }
				if (!this.validData()) {
					return
				}
				//处理档案信息
				this.imgListHandle()
			},
			//档案处理
			imgListHandle() {
				if (this.imgLoading) return
				this.imgLoading = true
				if (this.typeNeedBank && this.result.amount > 0) {
					//预存型卡并且有余额才能退款
					if (this.formData.accountType == '0') {
						console.log('个人用户补传21,33====>>>>>>>', this.uploadList)
						//个人
						let index21 = this.uploadList.findIndex((item) => {
							return item.uploadType == '21';
						});
						let p1, p2 //上传图片promise
						const promises = [];
						if (index21 == -1) {
							//不存在就掉接口传入统一档案图片
							promises.push(this.sendUploadFile(this.base64ImgUrl, '21'));
							// p1 = this.sendUploadFile(this.base64ImgUrl, '21')
						}

						let index33 = this.uploadList.findIndex((item) => {
							return item.uploadType == '33';
						});
						if (index33 == -1) {
							promises.push(this.sendUploadFile(this.base64ImgUrl, '33'));
						}


						//校验2张图片是否上传成功
						Promise.all([p1, p2]).then(res => {
							console.log('个人用户补传21,33成功弹窗====>>>>>>>', this.uploadList)
							//弹出验证码
							this.imgLoading = false
							this.showSmsDialog = true
							this.$refs.smsDialog.getCaptcha()
						}).catch(error => {
							console.log('个人用户补传21,33=========', error)
							uni.showModal({
								title: "提示",
								content: '系统正在开小差，请稍等片刻后重新提交',
								showCancel: false,
							});
							this.imgLoading = false
							//提示重新提交
						})


					} else if (this.formData.accountType == '1') {
						//单位上传证件

						let c1, c2, c3, c4, c5, c6 //上传图片promise

						//单位赋值26代办人
						let index26 = this.uploadList.findIndex((item) => {
							return item.uploadType == '26';
						});
						c1 = this.sendUploadFile(this.uploadList[index26].base64Img, '22')


						//单位赋值27代办人
						let index27 = this.uploadList.findIndex((item) => {
							return item.uploadType == '27';
						});
						c2 = this.sendUploadFile(this.uploadList[index27].base64Img, '23')

						//单位赋值24单位介绍信
						let index24 = this.uploadList.findIndex((item) => {
							return item.uploadType == '24';
						});
						if (index24 == -1) {
							//不存在就掉接口传入统一档案图片
							c3 = this.sendUploadFile(this.base64ImgUrl, '24')
						}

						if (this.formData.balanceDisposal == '1') {
							//单位退到银行卡
							let index21 = this.uploadList.findIndex((item) => {
								return item.uploadType == '21';
							});

							if (index21 == -1) {
								//不存在就掉接口传入统一档案图片
								c4 = this.sendUploadFile(this.base64ImgUrl, '21')
							}

							let index33 = this.uploadList.findIndex((item) => {
								return item.uploadType == '33';
							});
							if (index33 == -1) {
								c5 = this.sendUploadFile(this.base64ImgUrl, '33')
							}

							//校验24,26,27,21,33类型的5张图片是否上传成功
							Promise.all([c1, c2, c3, c4, c5]).then(res => {
								//弹出验证码
								this.imgLoading = false
								this.showSmsDialog = true
								this.$refs.smsDialog.getCaptcha()
							}).catch(error => {
								console.log('error', error)
								uni.showModal({
									title: "提示",
									content: '系统正在开小差，请稍等片刻后重新提交',
									showCancel: false,
								});
								this.imgLoading = false
								//提示重新提交
							})
						} else if (this.formData.balanceDisposal == '2') {
							//退到互联网账户
							let index74 = this.uploadList.findIndex((item) => {
								return item.uploadType == '74';
							});
							if (index74 == -1) {
								c6 = this.sendUploadFile(this.base64ImgUrl, '74')
							}


							//校验24,26,27,74类型的5张图片是否上传成功
							Promise.all([c1, c2, c3, c6]).then(res => {
								//弹出验证码
								this.imgLoading = false
								this.showSmsDialog = true
								this.$refs.smsDialog.getCaptcha()
							}).catch(error => {
								console.log('error', error)
								uni.showModal({
									title: "提示",
									content: '系统正在开小差，请稍等片刻后重新提交',
									showCancel: false,
								});
								this.imgLoading = false
								//提示重新提交
							})
						} else {
							//放弃退款直接弹出验证码
							this.imgLoading = false
							this.showSmsDialog = true
							this.$refs.smsDialog.getCaptcha()
						}
					}
				} else {
					//后付费月月行次次顺等用户
					if (this.formData.accountType == '1') {
						//单位上传证件

						let d1, d2, d3 //上传图片promise

						//单位赋值26代办人
						let index26 = this.uploadList.findIndex((item) => {
							return item.uploadType == '26';
						});
						d1 = this.sendUploadFile(this.uploadList[index26].base64Img, '22')


						//单位赋值27代办人
						let index27 = this.uploadList.findIndex((item) => {
							return item.uploadType == '27';
						});
						d2 = this.sendUploadFile(this.uploadList[index27].base64Img, '23')

						//单位赋值24单位介绍信
						let index24 = this.uploadList.findIndex((item) => {
							return item.uploadType == '24';
						});
						if (index24 == -1) {
							//不存在就掉接口传入统一档案图片
							d3 = this.sendUploadFile(this.base64ImgUrl, '24')
						}

						//校验24,26,27,21,33类型的5张图片是否上传成功
						Promise.all([d1, d2, d3]).then(res => {
							//弹出验证码
							this.imgLoading = false
							this.showSmsDialog = true
							this.$refs.smsDialog.getCaptcha()
						}).catch(error => {
							console.log('error', error)
							uni.showModal({
								title: "提示",
								content: '系统正在开小差，请稍等片刻后重新提交',
								showCancel: false,
							});
							this.imgLoading = false
							//提示重新提交
						})
					} else {
						console.log('个人用户直接弹出提交框====>>>>>>>', this.uploadList)
						if (this.result.amount == null || this.result.amount == undefined) {
							uni.showModal({
								title: "提示",
								content: '未获取到账户余额信息，请重新获取',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										this.getDetail()
									}
								}
							});
							this.imgLoading = false
							return
						}
						//个人用户直接弹出验证码
						this.imgLoading = false
						this.showSmsDialog = true
						this.$refs.smsDialog.getCaptcha()
					}
				}
			},
			sendUploadFile(base64Img, photoCode) {
				return new Promise((resolve, reject) => {
					let biz_content = {
						customer_id: this.formData.custMastId,
						photo_code: photoCode,
						scene: 6, // 销卡上传
						vehicle_code: this.formData.vehicleNo,
						vehicle_color: this.formData.vehicleColor,
						other_code: this.formData.cardNo
					};
					let params = {
						file_content: base64Img,
						method_code: "2", // 档案上传
						biz_content: JSON.stringify(biz_content),
					};
					this.$request.post(this.$interfaces.uploadFile, {
						data: params
					}).then(res => {
						console.log('上传图片===>>>', res)
						if (res.code == 200) {
							let imgList = {
								uploadType: res.data.photo_code,
								uploadPath: res.data.code
							}
							// this.onChange(imgList)
							this.clearErrorText(imgList.uploadType)
							//身份证上传
							if (this.uploadList.length == 0) {
								this.uploadList.push(imgList)
							} else {
								//覆盖
								let index = this.uploadList.findIndex((item) => {
									return item.uploadType == imgList.uploadType;
								});
								if (index == -1) {
									this.uploadList.push(imgList)

								} else {
									this.uploadList[index].uploadPath = imgList.uploadPath
								}
							}
							resolve(true)
						} else {
							reject(false)
						}
					}).catch(err => {
						reject(false)
					})
				})
			},
			smsConfirm(mobileCode) {
				this.isLoading = true
				let params = {
					mobile: this.formData.mobile,
					mobileCode: mobileCode
				}
				this.$request.post(this.$interfaces.smsCheckCodeV2, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('身份验证', res)
						this.save()
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
				this.showSmsDialog = false
			},
			validData() {
				if (this.formData.accountType == '0') {
					//个人车辆
					let frontIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '22';
					});
					if (frontIndex == -1) {
						this.errorId = 1
						this.errorText = '您的身份证人面像尚未上传，请先上传。'
						return false
					}
					let backIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '23';
					});
					if (backIndex == -1) {
						this.errorId = 2
						this.errorText = '您的身份证国徽面尚未上传，请先上传。'
						return false
					}
				}
				if (this.formData.accountType == '1') {
					//单位车辆
					let letterIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '25';
					});
					if (letterIndex == -1) {
						this.errorId = 3
						this.errorText = '您的委托书尚未上传，请先上传。'
						return false
					}

					let frontIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '26';
					});
					if (frontIndex == -1) {
						this.errorId = 1
						this.errorText = '您的身份证人面像尚未上传，请先上传。'
						return false
					}
					let backIndex = this.uploadList.findIndex((item) => {
						return item.uploadType == '27';
					});
					if (backIndex == -1) {
						this.errorId = 2
						this.errorText = '您的身份证国徽面尚未上传，请先上传。'
						return false
					}
				}

				if (this.typeNeedBank && this.result.amount > 0) {
					if (this.formData.balanceDisposal == '1') {
						if (!this.formData.bankName) {
							this.errorId = 4
							this.errorText = '您的开户行尚未填写，请先填写。'
							return false
						}
						if (!this.formData.bankAccount) {
							this.errorId = 5
							this.errorText = '您的银行卡号尚未填写，请先填写。'
							return false
						}
					}
				}

				this.errorText = ''
				return true
			},
			save() {
				let params = JSON.parse(JSON.stringify(this.formData))
				//银行户名默认ETC用户名
				params.beneficiaryName = this.formData.custName
				params.uploadList = this.uploadList
				console.log('params', params)

				if (this.isLoading) return;
				this.isLoading = true;

				this.$request.post(this.$interfaces.logoutApply, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res', res)
						//跳转成功页面 todo
						uni.reLaunch({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=13&cardNo=" +
								this
								.formData.cardNo + '&orderId=' + ''

						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			// hadAccount() {
			// 	uni.navigateTo({
			// 		url: '/pagesB/disputeRefund/accountList?type=onlineLogout&custName=' + this.formData.custName
			// 	})
			// },
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.apply-after-sale {
		padding-bottom: 180rpx;
		padding-top: 184rpx;
	}

	.question-wrapper {
		display: flex;

		.question-text {
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #0081FF;
		}
	}

	.section {
		background: #ffffff;
		border-radius: 10rpx;
	}

	.card-price {
		margin: 0rpx 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
		line-height: 26rpx;
	}

	.tips {
		margin: 20rpx 30rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
		line-height: 24rpx;
	}

	.amount-select {
		margin: 0 30rpx;
		padding-bottom: 36rpx;
		display: flex;
		align-items: center;

		.label {
			flex: 0 0 200rpx;
			width: 200rpx;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			line-height: 26rpx;
		}

		.select-wrapper {
			flex: 1;
			position: relative;
			line-height: 64rpx;
			padding: 0 30rpx;
			width: 100%;
			height: 64rpx;
			background: #FFFFFF;
			border-radius: 8rpx;
			border: 2rpx solid #DDDDDD;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;

			.select-icon {
				position: absolute;
				right: 13rpx;
				top: 14rpx;
			}
		}
	}

	.bank-wrapper {
		margin: 0 30rpx;
		padding-bottom: 36rpx;

		.content {
			flex: 1;
			width: 100%;
		}

		.bank-item {
			display: flex;
			margin-bottom: 16rpx;
			align-items: center;

			.label {
				flex: 0 0 147rpx;
				width: 147rpx;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 42rpx;
			}

			.value {
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 42rpx;
			}

			.select-wrapper {
				flex: 1;
				position: relative;
				line-height: 64rpx;
				padding: 0 30rpx;
				// width: 481rpx;
				width: 100%;
				height: 64rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				border: 2rpx solid #DDDDDD;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;


				.select-input {
					height: 64rpx;
					line-height: 64rpx;
					padding-right: 30rpx;
				}

				.bank-img {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 64rpx;
					width: 64rpx;
					position: absolute;
					right: 0;
					top: 0;
				}

				.select-icon {
					position: absolute;
					right: 13rpx;
					top: 14rpx;
				}
			}


			.other-input {
				margin-top: 16rpx;
				width: 100%;
				height: 64rpx;
				line-height: 64rpx;
				padding: 0 30rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				border: 2rpx solid #DDDDDD;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
			}
		}

		.other {
			align-items: flex-start;

			.label {
				margin-top: 14rpx;
			}
		}
	}

	.account-wrapper {
		padding: 30rpx;

		.account-info {
			margin-top: 20rpx;
			padding: 15rpx 30rpx 30rpx 30rpx;
			background: #F8F8F8;
			border-radius: 8rpx;

			.account-name {
				font-size: 26rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #323435;
				line-height: 37rpx;
			}

			.account-item {
				display: flex;
				align-items: center;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				margin-top: 20rpx;

				.label {
					line-height: 26rpx;
					flex: 0 0 203rpx;
					width: 203rpx;
				}

				.value {
					line-height: 26rpx;
				}
			}
		}
	}

	.tips {
		.tip {
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #888888;
			line-height: 40rpx;
		}
	}

	.error-wrapper {
		position: fixed;
		left: 0px;
		bottom: 152rpx;
		width: 100%;
		height: 65rpx;
		line-height: 65rpx;
		background: #FFEBEB;
		z-index: 98;

		.error-text {
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #F65B5B;
			line-height: 40rpx;
		}
	}

	/deep/.weui-bottom-fixed__box {
		border-top: 0;
	}

	/deep/.weui-card-hd-title {
		font-weight: bold;
	}

	/deep/.title-wrapper {
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;
		background: #ffffff;
		padding: 30rpx;
	}


	.search-wrapper {
		height: 600rpx;

		.search-title {
			justify-content: space-between;
			height: 80rpx;
			line-height: 80rpx;
			padding: 0 40rpx;
			background-color: #fbf9fe;

			.cancel {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #999999;
				line-height: 42rpx;
			}

			.confirm {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #0066E9;
				line-height: 42rpx;
			}

			.gray {
				color: #999999;
			}
		}

		.search-input {
			height: 100rpx;
			line-height: 100rpx;
			font-size: 36rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #0066E9;
			text-align: center;
			padding: 0 30rpx;
		}

		.picker-wrapper {
			height: 100%;

			.picker-view {
				height: calc(100% - 166rpx);

				.picker-item {
					text-align: center;
					line-height: 68rpx;
					font-size: 34rpx;
				}
			}
		}

		.search-bottom {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 84rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 33rpx;
			background-color: #f8f8f8;


			.bottom-left,
			.bottom-right {
				display: flex;
				align-items: center;
			}

			.bottom-left {
				margin-right: 50rpx;
			}

			.circle {
				width: 15rpx;
				height: 15rpx;
				margin-right: 10rpx;
				background: #0066E9;
				border-radius: 50%;

			}

			.red {
				background: #F65B5B;
			}
		}
	}
</style>