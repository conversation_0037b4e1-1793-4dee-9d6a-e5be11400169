<template>
	<view class="reason-index">
		<view class="tip">
			<view class="tip-title">
				为了捷通为您提供更优质的服务，办理此业务前，请您先回答几个问题。
			</view>
		</view>
		<view class="reason-wrapper">
			<commonTitle title="您申请注销的原因"></commonTitle>
			<view class="select-wrapper" @click="show = true">
				<view class="select-value" style="color: #777777;" v-if="!selectList">
					请选择注销的原因
				</view>
				<view class="select-value" v-else>
					{{selectList[0].label}}
				</view>
				<image class="select-icon" src="../../static/arrow-show.png" style="width: 40rpx;height: 40rpx;"
					mode=""></image>
			</view>
			<view class="reason-content">
				<view v-if="selectList && selectList[0].type == 'select' " class="radio-reason">
					<view class="reason-title">
						{{ selectList[0].child.label}}
					</view>
					<view class="rdio-wrapper">
						<view class="left" @click="radioSelect(true)">
							<image v-if="selectList[0].child.checked" class="check-icon" src="../../static/checked.png"
								style="width: 30rpx;height: 30rpx;" mode=""></image>
							<image v-else class="check-icon" src="../../static/check.png"
								style="width: 30rpx;height: 30rpx;" mode=""></image>
							<text>是</text>
						</view>
						<view class="right" @click="radioSelect(false)">
							<image v-if="selectList[0].child.checked == false" class="check-icon"
								src="../../static/checked.png" style="width: 30rpx;height: 30rpx;" mode=""></image>
							<image v-else class="check-icon" src="../../static/check.png"
								style="width: 30rpx;height: 30rpx;" mode=""></image>
							<text>否</text>
						</view>
					</view>
				</view>

				<view v-if="selectList && selectList[0].type == 'input' " class="radio-reason">
					<view class="reason-title">
						{{ selectList[0].child.label}} <text style="color: #FF9038;">(非必填)</text>
					</view>
					<view class="input-wrapper">
						<textarea class="textarea" placeholder="请输入您宝贵的意见" v-model="selectList[0].child.inputText"
							placeholder-class="plcss" name="" id="" cols="30" rows="10"></textarea>
					</view>
				</view>
			</view>
		</view>
		<view class="font-text">
			感谢您的配合
		</view>
		<tButton :buttonList="buttonList" @save="save"></tButton>
		<u-select v-model="show" :list="applyReasonTypeList" @confirm="confirm" :default-value="[index]">
		</u-select>
	</view>
</template>

<script>
	import commonTitle from '@/pagesC/components/common-title/common-title.vue'
	import tButton from '@/pagesC/components/t-button/t-button.vue'
	export default {
		components: {
			commonTitle,
			tButton
		},
		data() {
			return {
				show: false,
				buttonList: [{
					title: '提交',
					handle: 'save'
				}],
				formData: {
					reasonValue: ''
				},
				index: '0',
				applyReasonTypeList: [{
						label: '车不在广西',
						value: '0',
						type: 'select',
						child: {
							label: '如捷通公司提供便捷的线上售后服务，您是否愿意继续使用',
							checked: null
						}
					},
					{
						label: '设备无法正常使用',
						value: '1',
						type: 'select',
						child: {
							label: '捷通已提供便捷的线上补办及售后等功能，您是否愿意继续使用',
							checked: null
						}
					},
					{
						label: '不想用了',
						value: '2',
						type: 'input',
						child: {
							label: '您不想用的原因，捷通可以从哪方面提升服务',
							inputText: ''
						}
					},
					{
						label: '换车没换车牌',
						value: '3',
						type: 'select',
						child: {
							label: '没更换车牌可进行信息变更后继续使用，无需注销，是否进行信息变更。',
							checked: null
						}
					},
					{
						label: '卖车不再使用',
						value: '4',
					},
					{
						label: '车牌换了没换车',
						value: '5',
					},
					{
						label: '换车换车牌',
						value: '6',
					},
					{
						label: '其他',
						value: '7',
						type: 'input',
						child: {
							label: '具体原因',
							inputText: ''
						}
					}
				],
				selectList: null, // 提示选择
			}
		},
		methods: {
			confirm(options) {
				console.log('options', options)
				// this.formData.applyReasonTypeStr = options[0].label

				if (options[0].value == '5' || options[0].value == '6') {
					let vehicleInfo = this.$store.getters['afterSale/afterSaleVehicle'] || {};
					//判断产品类型
					if (vehicleInfo.gxCardType == '0' || vehicleInfo.gxCardType == '5' || vehicleInfo
						.gxCardType == '7' || vehicleInfo.gxCardType == '9') {
						let url = 'https://www.gxetc.com.cn/h5/#/businessOutlets' //线下网点地址
						uni.showModal({
							title: '温馨提示',
							content: '变更车牌不需要注销设备，前往线下网点处理即可。',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/uni-webview/h5-webview?ownPath=' +
											encodeURIComponent(url)
									})
								}
							}
						})
					}
					this.formData.reasonValue = ''
					this.index = '0'
					this.selectList = null
				}
				
				this.formData.reasonValue = options[0].value
				this.index = options[0].value

				let child = this.applyReasonTypeList.filter(item => {
					return item.value == options[0].value
				})

				console.log('child', child)
				this.selectList = child
			},
			radioSelect(flag) {
				this.selectList[0].child.checked = flag
				console.log('this.formData.reasonValue ', this.formData.reasonValue)
				let url = 'https://www.gxetc.com.cn/h5/#/businessOutlets' //线下网点地址
				let selectValue = this.formData.reasonValue
				if (flag) {
					if (selectValue == '0' || selectValue == '1') {
						uni.showModal({
							title: '温馨提示',
							content: '捷通公司己经推出了线上售后功能，如需售后可直接选择相应的功能',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									this.selectList[0].child.checked = null
									uni.navigateTo({
										url: '/pagesC/afterSaleBusiness/home/<USER>'
									})
								}
							}
						})
					} else if (selectValue == '3') {
						uni.showModal({
							title: '温馨提示',
							content: '您可前往线下网点完成信息变更，无需注销设备。',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									this.selectList[0].child.checked = null
									uni.navigateTo({
										url: '/pages/uni-webview/h5-webview?ownPath=' +
											encodeURIComponent(url)
									})
								}
							}
						})
					}
				}

			},
			validData() {
				if (!this.formData.reasonValue) {
					uni.showModal({
						title: "提示",
						content: '请先选择您申请注销的原因',
						showCancel: false,
					});
					return false
				}
				return true
			},
			save() {
				if (!this.validData()) {
					return
				}
				let question2, question3
				let cancelReason = '您申请注销的原因:'
				let question1 = this.selectList[0].label
				let cancelReasonDetail
				if (this.selectList[0].type == 'select') {
					question2 = this.selectList[0].child.label
					if (this.selectList[0].child.checked == null) {
						uni.showModal({
							title: "提示",
							content: '请先选择您是否愿意继续使用。',
							showCancel: false,
						});
						return
					} else if (this.selectList[0].child.checked == false) {
						question3 = '否'
					}
					cancelReasonDetail = cancelReason + question1 + ';' + question2 + ':' + question3
				} else if (this.selectList[0].type == 'input') {
					question2 = this.selectList[0].child.label
					question3 = this.selectList[0].child.inputText
					if (question3.trim()) {
						cancelReasonDetail = cancelReason + question1 + ';' + question2 + ':' + question3
					} else {
						cancelReasonDetail = cancelReason + question1 + ';' + question2
					}

				} else {
					cancelReasonDetail = cancelReason + question1
				}


				console.log('this.selectList ', this.selectList, cancelReasonDetail)

				//添加订阅
				this.$subscriptionMethod()

				uni.navigateTo({
					url: './apply?cancelReasonDetail=' + cancelReasonDetail
				})
			}
		}
	}
</script>


<style>
	page {
		font-family: PingFangSC-Regular, PingFang SC;
		background: #f8f8f8;
	}

	.plcss {
		font-size: 24rpx;
		font-weight: 400;
		color: #B9B9B9;
		line-height: 33rpx;
	}
</style>

<style scoped lang="scss">
	.tip {
		height: 102rpx;
		display: flex;
		align-items: center;
		padding: 0 35rpx;
		background: rgba(255, 144, 56, 0.1);

		.tip-title {
			font-size: 24rpx;
			font-weight: 400;
			color: #FF9038;
			line-height: 33rpx;
		}
	}

	.reason-wrapper {
		margin: 20rpx;
		padding: 30rpx;
		background-color: #ffffff;

		.select-wrapper {
			position: relative;
			width: 100%;
			height: 96rpx;
			line-height: 96rpx;
			padding: 0 30rpx;
			margin-top: 22rpx;
			background: #FFFFFF;
			border-radius: 12rpx;
			border: 1rpx solid #D5D5D5;

			.select-icon {
				position: absolute;
				right: 13rpx;
				top: 22rpx;
			}
		}
	}

	.reason-content {
		margin-top: 70rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
		line-height: 50rpx;

		.rdio-wrapper {
			display: flex;
			align-items: center;
			margin-top: 20rpx;
			font-size: 36rpx;

			.left {
				margin-right: 86rpx;
			}

			.check-icon {
				margin-right: 16rpx;
				margin-bottom: 6rpx;
				vertical-align: middle;
			}

		}

		.input-wrapper {
			margin-top: 20rpx;

			.textarea {
				width: 650rpx;
				height: 119rpx;
				padding: 18rpx 30rpx;
				background: #F8F8F8;
				border-radius: 4rpx;
				font-size: 24rpx;
				font-weight: 400;
				line-height: 33rpx;
			}
		}
	}

	.font-text {
		margin-top: 30rpx;
		margin-left: 20rpx;
		font-size: 26rpx;
		font-weight: 400;
		color: #666666;
		line-height: 37rpx;
	}


	/deep/.title {
		font-weight: normal !important;
	}
</style>