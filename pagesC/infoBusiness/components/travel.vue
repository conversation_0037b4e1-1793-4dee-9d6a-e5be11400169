<!--
  * @desc:出行常识列表
  * @author:zhangys
  * @date:2023-03-07 09:52:36
!-->
<template>
	<view class="information-wrap">
		<scroll-view :scroll-top="scrollTop" :style="'height:'+(windowHeight-10)+'px;'"  scroll-y="true" class="scroll-Y" :lower-threshold='lowerThreshold'
			@scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<view class="consultation-containter">
				<view class="consultation-item g-flex g-flex-justify g-flex-align-center"
					v-for="(item,index) in infoList" :key="index" @click="toDetail(item)">
					<view class="item-text g-flex g-flex-column">
						<view class="item-text-hd">
							{{item.titleName}}
						</view>
						<view class="item-text-bd g-flex g-flex-justify g-flex-align-center">
							
							<view class="">
								{{item.publishTime }}
							</view>
							<view class="">
								阅读量 ：{{item.readingAmount }}
							</view>
						</view>
					</view>
					<view class="item-img">
						<img :src="item.coverPic" alt="">
					</view>
				</view>
			</view>
			<load-more :loadStatus="noticeLoadStatus" />
		</scroll-view>

	</view>
</template>

<script>
	import loadMore from '../../components/load-more/index.vue';
		import {trackUpload} from './trackUpload.js'
	export default {

		data() {
			return {
				flag:false,
					windowHeight: this.windowHeight,
				formData: {
					classType: '2', //资讯类型
					pageNum: 1,
					pageSize: 10,
				},
				noticeLoadStatus: 3,
				scrollTop: 0,
				old: {
					scrollTop: 0
				},
				lowerThreshold: 60,
				infoList: []
			}
		},
		onLoad() {

		},
		components: {
			loadMore
		},
		created() {
			this.getInformationList()
		},
		methods: {
			toDetail(row){
				trackUpload(row);
				// articleType 1-富文本页面、2-链接跳转
				if (row.articleType == '2') {
				
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' +
							encodeURIComponent(row.articleUrl)
					})
				}
				if (row.articleType == '1') {
					uni.navigateTo({
						url: '/pagesC/infoBusiness/detail?informationId='+row.informationId
					})
				}
			},
			getInformationList() {
				this.noticeLoadStatus = 1;
				
				this.$request.post(this.$interfaces.informationList, {
					data: this.formData
				}).then(res => {

					if (res.code == 200) {
						console.log(res.data.data)
						let result = res.data.data || []
						if (res.data.data.length) {
							this.infoList = this.infoList.concat(result)
						} else {
							this.noticeLoadStatus = 3;
							this.flag = true
						}
						if (this.infoList.length == res.data.page.total) {
							this.noticeLoadStatus = 3
							this.flag = true
						}
					} else {
						this.noticeLoadStatus = 2;
					}
				}).catch((err) => {
					this.noticeLoadStatus = 2;
				})

			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				console.log(e);
				if (this.flag) return;
				let self = this;

				setTimeout(function() {
					self.formData.pageNum = self.formData.pageNum + 1;
					self.getInformationList();
				}, 500)

			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop;
			},
		}
	}
</script>

<style lang="scss">

</style>
