import vue from 'vue';
var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
import {
	getLoginUserInfo
} from '@/common/storageUtil.js'
let loading = false;
let getInfoDetail = function(row) {
	
}
let trackUpload = function(row) {
	let params = {
		bizType: '2', //1-广告、2-资讯
		bizId: row.informationId,
		eventType: 2, //1-曝光、2-点击
		eventTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
		userId: getLoginUserInfo().userId
	}
	if (loading) return
	loading = true;
	vue.prototype.$request
		.post(vue.prototype.$interfaces.trackUpload, {
			data: {
				list: [params]
			}
		})
		.then((res) => {
			loading = false;
		})
		.catch((error) => {
			loading = false;
		})
}
export {
	trackUpload,
	getInfoDetail
}
