<template>
	<view class="information" v-if="isShow">
		<u-tabs :list="consultationList" :is-scroll="false" :current="current" activeColor='#01c1b2' @change="change">
		</u-tabs>
		<importMessage v-if="current=='0'"></importMessage>
		<others v-if="current=='2'"></others>
		<travel v-if="current=='1'"></travel>
	</view>
</template>

<script>
	import importMessage from './components/importMessage'
	import others from './components/others'
	import travel from './components/travel'
	export default {
		data() {
			return {
				consultationList: [{
						name: '重要通知',
						value: '1'
					},
					{
						name: '出行常识',
						value: '2'
					},
					{
						name: '其他内容',
						value: '3'
					},
				],
				current: 0,
				isShow:true
			}
		},
		onLoad() {
		   
		},
		onShow() {
			this.isShow = true;
		},
		onHide() {
			this.isShow = false;
		},
		components: {
			importMessage,
			others,
			travel
		},
		methods: {
			change(index) {
				this.current = index;
			},
		}
	}
</script>

<style lang="scss">
	.information {
		
		background-color: #FFFFFF;
	}

	.consultation-containter {
		background-color: #fff;

		.consultation-item {
			margin: 20rpx;
			background: #fff;
			padding-bottom: 20rpx;
			border-bottom: 1px solid  #F5F5F5;
			
			.item-text {
				flex: 1;
				height: 130rpx;
				.item-text-hd {
					font-weight: bold;
					font-size: 28rpx;
					color: #333;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: normal;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					flex: 1;

				}

				.item-text-bd {
					height: 50rpx;
					line-height: 50rpx;
					font-size: 24rpx;
					color: #707070;
				}
			}

			.item-img {
				width: 130rpx;
				height: 130rpx;
				margin-left: 30rpx;
				&>img {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
</style>
