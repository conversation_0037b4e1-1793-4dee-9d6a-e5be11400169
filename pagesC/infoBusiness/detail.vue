<!--
  * @desc:咨询详情
  * @author:zhangys
  * @date:2023-03-07 09:52:36
!-->
<template>

	<view class="detail-container" v-if="isShow">
		<view class="header">
			<view class="title g-flex g-flex-center">
				{{detailInfo.titleName}}
			</view>
			<view class="desc g-flex g-flex-justify g-flex-align-center">
				<view class="">
					{{detailInfo.publishTime }}
				</view>
				<view class="">
					阅读量 ：{{detailInfo.readingAmount }}
				</view>

			</view>
		</view>
		<!-- 		<view v-html="detailInfo.content" class="content">

		</view> -->
		<mp-html :content="detailInfo.content" />
	</view>



</template>

<script>
	import {
		getAccountId
	} from '@/common/storageUtil.js'
	import mpHtml from './components/mp-html/mp-html'
	export default {
		components: {
			mpHtml
		},
		data() {
			return {
				informationId: '', // 资讯编号
				isShow: false,
				detailInfo: {},
				category: '1'
			}
		},
		onLoad(options) {
			this.informationId = options.informationId || '';
			this.category = options.category || '1'
			console.log(options)
			if (this.informationId) {
				this.getInfoDetail();
			}
		},
		methods: {

			getInfoDetail() {
				let params = {
					informationId: this.informationId,
					category: this.category
				}


				this.$request
					.post(this.$interfaces.informationDetails, {
						data: params
					}).then(res => {
						if (res.code == 200 && res.data) {
							this.detailInfo = res.data;
							this.isShow = true;
						}
					}).catch((error) => {
						uni.showModal({
							title: "提示",
							content: error.msg,
							showCancel: false,
						});
					})
			}
		}
	}
</script>

<style scoped lang="scss">
	.detail-container {
		background-color: #fff;

		.header {
			padding: 30rpx;

			.title {
				font-size: 34rpx;
				font-weight: bold;
				text-align: center;
				margint-top: 20px;
				color: #3f3f3f;
			}

			.desc {
				margin-top: 20rpx;

				color: #666;
				font-size: 26rpx
			}
		}

		.content {
			background-color: #fff;
			padding: 20rpx;
		}
	}
</style>