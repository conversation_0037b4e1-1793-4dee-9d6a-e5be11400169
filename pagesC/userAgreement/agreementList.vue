<template>
	<view>

		<view class="no-vehicle" v-if="signInfo.length==0">
			<image src="../../static/toc/no-data.png" mode="aspectFilt" class="no-vehicle_icon"></image>
			<view class="no-vehicle_des">暂无签署记录</view>
		</view>
		<view v-else>
			<signItem :singInfo='signInfo' />
		</view>
	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,

	} from "@/common/storageUtil.js";
	import signItem from './item.vue'
	export default {

		name: '',
		components: {
			signItem
		},
		data() {
			return {
				signInfo: []
			}
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},


		},
		created() {
			this.getAgreementList()
		},
		methods: {
			getAgreementList() {
				// queryLastContracts
				let data = {
					routePath: this.$interfaces.selectContracts.method,
					bizContent: {
						customer_id: this.customerInfo.customer_id,
						vehicleCode: this.vehicleInfo.vehicle_code,
						vehicleColor: this.vehicleInfo.vehicle_color,
						pageNum: 1,
						pageSize: 1
					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						console.log(res, '-----------');
						this.signInfo = res.data.records[0]

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

					// console.log(res, '-----------');
					// console.log(this.agreementList);
				})
			}
		},
	}
</script>

<style lang='scss' scoped>
	.no-vehicle {
		width: 100%;
		background-color: #f3f3f3;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 28rpx;
		color: #999999;
		font-weight: 400;
		text-align: center;
		margin-top: 20rpx;
	}
</style>