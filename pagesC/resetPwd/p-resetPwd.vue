<!-- C端 用户注册 -->
<template>
	<view class="register padding-top">
		<view class="GX-logo padding-bottom">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/etc-logo.png"
				class="home-img"></image>
			<view class="login-title">桂小通(广西捷通)</view>
		</view>
		<view>

			<form>
				<view class="cu-form-group" style="justify-content: flex-start;">
					<!-- <span style="color:red">*</span> -->
					<view class="title form_label-require">用户类型:</view>
					<radio-group @change="radioChange" class="g-flex g-flex-align-center">
						<label class="uni-list-cell uni-list-cell-pd " v-for="(item, index) in userType"
							:key="item.value">
							<view>
								<radio :value="item.value" class="cyan" style="transform: scale(0.7)"
									:checked="index==userTypeIndexSub" />
								<text style="margin: 0 10upx;">{{item.name}}</text>
							</view>

						</label>
					</radio-group>
				</view>
				<view class="cu-form-group" v-if="userTypeIndex=='2'">
					<span style="color:red">*</span>
					<view class="title form_label-require">证件号码:</view>
					<input placeholder="请输入证件号码" name="licenseNo" v-model='formData.licenseNo'
						@input="changeInput($event,'licenseNo')"></input>

				</view>
				<view class="cu-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">手机号码:</view>
					<input placeholder="请输入绑定手机号码" name="mobile" v-model='formData.mobile'
						@input="changeInput($event,'mobile')"></input>

				</view>
				<view class="cu-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">新密码:</view>
					<input placeholder="请输入登录密码" password name="newPassword" v-model='formData.newPassword'
						@input="changeInput($event,'newPassword')"></input>
				</view>
				<view class="cu-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">确认密码:</view>
					<input placeholder="请再次输入登录密码" password name="newPassword1" v-model='formData.newPassword1'
						@input="changeInput($event,'newPassword1')" @blur="onInputBlur"></input>
				</view>
				<!-- 				<view class="cu-form-group login-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">图形验证码:</view>
					<input placeholder="请输入图形验证码" name="mobileCode" v-model='mobileCode'></input>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">
				</view> -->
				<view class="cu-form-group">
					<span style="color:red">*</span>
					<view class="title form_label-require">验证码:</view>
					<input placeholder="请输入验证码" name="mobileCode" v-model='formData.mobileCode'
						@input="changeInput($event,'mobileCode')"></input>
					<text class="sendSMS" @click="showCodeHandle">{{smsName}}</text>
				</view>
			</form>

		</view>
		<view class="certification">

			<TButton title="确认修改" @clickButton="goNext" @click="gotovehicle" :isLoadding="isBtnLoader" />
		</view>
		<t-captcha id="captcha" app-id="191430362" @verify="handlerVerify" @ready="handlerReady" @close="handlerClose"
			@error="handlerError" />
			
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import tLoading from "@/components/common/t-loading.vue";
	import JSEncrypt from '@/js_sdk/jsencrypt/jsencrypt';
	import TButton from "@/components/t-button.vue";
	import {
		setLoginUserInfo,
		getCurrUserInfo,
		setCurrUserInfo,
		setTicket,
		setMd5Key,
		setAesKey,
	} from "@/common/storageUtil.js";

	import {
		checkIdCard,
		checkEmail
	} from "@/common/util.js";
	import {
		checkPhone
	} from '@/common/method/validater.js'
	import {
		getErrorMessage
	} from '@/common/method/filter.js'
	import float from '@/common/method/float.js'
	export default {
		components: {
			TButton,
			tLoading,
		},
		data() {
			return {
				smsName: "发送验证码",
				formData: {
					licenseNo: '',
					mobile: "",
					mobileCode: "",
					newPassword: "",
					newPassword1: "",
					userType: "1",
				},
				num: 0,
				isBtnLoader: false,
				userInfo: {},
				isShowRule: false,
				showInfo: false,
				isLoading: false,
				time: null,
				mobileCode: '', // 图形验证码
				codeUrl: '', // 图形验证码连接
				userType: [{
						value: '1',
						name: '个人'
					},
					{
						value: '2',
						name: '单位'
					}
				],
				userTypeIndex: '1',
				userTypeIndexSub: '0',
				codeTicket: '', //腾讯验证码
			};
		},
		onLoad(options) {

			if (options && options.handle == 'setPwd') {
				this.formData.mobile = options.mobile || ''
				uni.setNavigationBarTitle({
					title: '设置密码'
				})
			}
			if (options && options.mobile) {
				this.formData.mobile = options.mobile
			}
			if (options && options.userTypeIndex) {
				this.userTypeIndex = options.userTypeIndex
				this.userTypeIndexSub = float.sub(options.userTypeIndex, 1)
			}
		},
		created() {
			this.getCaptcha();
		},
		methods: {

			radioChange(e) {
				this.userTypeIndex = e.detail.value
				console.log('e.detail.value', e.detail.value)
				this.userTypeIndexSub = float.sub(e.detail.value, 1)
				console.log('this.userTypeIndexSub2', this.userTypeIndexSub)
			},
			showCodeHandle() {
				if (this.sendSmsVal()) {
					uni.showModal({
						title: "提示",
						content: this.sendSmsVal(),
						showCancel: false,
					});
					return;
				}
				this.selectComponent('#captcha').show()
				// 进行业务逻辑，若出现错误需重置验证码，执行以下方法
				// if (error) {
				// this.selectComponent('#captcha').refresh()
				// }
			},
			// 验证码验证结果回调
			handlerVerify(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
				if (ev.detail.ret === 0) {
					// 验证成功
					this.codeTicket = ev.detail.ticket
					console.log('ticket:', ev.detail.ticket)
					this.sendSMS()
				} else {
					// 验证失败
					// 请不要在验证失败中调用refresh，验证码内部会进行相应处理
				}
			},
			// 验证码准备就绪
			handlerReady() {
				console.log('验证码准备就绪')
			},
			// 验证码弹框准备关闭
			handlerClose(ev) {
				// 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
				if (ev && ev.detail.ret && ev.detail.ret === 2) {
					console.log('点击了关闭按钮，验证码弹框准备关闭');
				} else {
					console.log('验证完成，验证码弹框准备关闭');
				}
			},
			// 验证码出错
			handlerError(ev) {
				console.log(ev.detail.errMsg)
			},
			onInputBlur() {
				if (this.formData.newPassword != this.formData.newPassword1) {
					uni.showToast({
						title: '两次输入密码不一致，请重新输入',
						icon: "none"
					})
				}
			},
			// 发送图形验证码
			getCaptcha() {
				let params = {
					// mobile: this.formData.mobile
				}
				this.$request.post(this.$interfaces.getCaptcha, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log(res, 'yanzhegnma');
						this.codeUrl = res.data.image
						this.captchaId = res.data.captchaId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {

				})
			},
			handleNameInput(event, data) {

			},
			changeInput(event, data) {

			},
			bindUserPickerChange(e) {
				this.user_type_index = e.detail.value;
				this.formData.certificates_type =
					this.personalType[e.detail.value].value || "";
			},
			goNext() {
				console.log(this.formData);
				let msg = this.registerVal();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				this.resetPwdHandle();
			},

			resetPwdHandle() {
				if (this.formData.newPassword != this.formData.newPassword1) {
					uni.showToast({
						title: '两次输入密码不一致，请重新输入',
						icon: "none"
					})
					return
				}
				let params = JSON.parse(JSON.stringify(this.formData));
				console.log(this.$publicKey)
				var encrypt = new JSEncrypt()
				encrypt.setPublicKey(this.$publicKey)
				params.newPassword = encrypt.encrypt(params.newPassword)
				params.userType = this.userTypeIndex == '1' ? '1' : '2'
				params.licenseNo = params.userType == '1' ? '' : params.licenseNo
				delete params.newPassword1
				this.isLoading = true;
				// 个人注册 登录名=手机号

				this.$request.post(this.$interfaces.resetPwd, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.showModal({
							title: "提示",
							content: '重置密码成功,去登录',
							showCancel: false,
							success: function() {
								uni.redirectTo({
									url: "/pagesD/login/p-login"
								})
							}
						});

					} else {
						this.getCaptcha()
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch((error) => {
					this.getCaptcha()
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//发送短信
			sendSMS() {
				// if (!this.mobileCode) {
				// 	uni.showModal({
				// 		title: "提示",
				// 		content: "请输入图形验证码",
				// 		showCancel: false,
				// 	});
				// 	return;
				// }
				if (!this.time) {
					let countdown = 60
					this.isLoading = true
					let params = {
						mobile: this.formData.mobile,
						// mobileCode: this.mobileCode,
						// captchaId: this.captchaId
						ticket: this.codeTicket
					};
					this.$request.post(this.$interfaces.sendaAccountSms, {
						data: params
					}).then(res => {
						console.log(res);
						this.isLoading = false
						if (res.code == '200') {
							this.time = setInterval(() => {
								countdown = countdown - 1
								this.smsName = countdown + "秒后重新发送"
								if (countdown === 0) {
									clearInterval(this.time)
									this.time = null
									this.smsName = "重新发送"
								}
							}, 1000)
						} else {
							uni.showModal({
								title: "提示",
								content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res.code + '】',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										this.getCaptcha()
									}
								}
							});
						}
					}).catch(err => {

					})
				}
			},
			sendSmsVal() {
				let rules = {
					mobile: {
						required: '请输入手机号码',
						validator: this.checkPone
					},
					newPassword: {
						required: '请输入登录密码'
					},
					newPassword1: {
						required: '请再次输入登录密码'
					},
					// email: {
					// 	required: '请输入电子邮箱',
					// 	validator: this.checkEmailvalidator
					// },

				}
				return this.validateHandle(rules)
			},
			registerVal() {
				let rules = {
					licenseNo: {
						validator: this.checkLicenseNo
					},
					mobile: {
						required: '请输入手机号码',
						validator: this.checkPone
					},
					newPassword: {
						required: '请输入登录密码'
					},
					newPassword1: {
						required: '请再次输入登录密码'
					},

					mobileCode: {
						required: '请输入验证码'
					}
				}
				return this.validateHandle(rules)
			},
			validateHandle(rules) {

				for (let key in rules) {
					if (rules[key].hasOwnProperty('required')) {
						if (!this.formData[key]) {
							return rules[key]['required']
						}
					}
					if (rules[key].hasOwnProperty('validator')) {
						let validator = rules[key].validator && rules[key].validator()
						if (validator) {
							return validator
						}

					}
				}
				return '';

			},

			checkLicenseNo() {
				return this.userTypeIndex == '2' && !this.formData.licenseNo ? '请先输入证件号码' : ''
			},
			checkPone() {
				console.log(checkPhone(this.formData.mobile))
				return !checkPhone(this.formData.mobile) ? '' : '请输入合法手机号'
			},


		},
	};
</script>
<style lang="scss" scoped>
	.register {
		padding-top: 40rpx;

		.login-form-group .code-img {
			width: 240upx;
			height: 90upx;
			margin-left: 20upx;
		}

		.cu-form-group .title {
			max-width: 180upx;
			min-width: 140upx;
			color: #333333;
		}

		.sendSMS {
			padding: 10rpx;
			color: #1D82D2;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.btn {
			margin-top: 20rpx;
			width: 100%;
			padding: 10rpx;


			.login-btn {

				width: 730rpx;
				height: 104rpx;
				font-size: 32rpx;
				line-height: 104rpx;
				text-align: center;
				color: #fff;
			}
		}
	}

	.GX-logo {
		width: 100%;
		text-align: center;
	}

	.home-img {
		height: 180rpx;
		width: 170rpx;
	}

	.login-title {
		color: #1D2225;
		font-size: 36rpx;
	}

	.sendSMS {
		padding: 10rpx;
		color: #1d82d2;
	}

	.sendSMS:active {
		background: #ddd;
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.color {
		color: #007aff;
	}

	.c-title {
		margin-top: 30upx;
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		background: #ffffff;
	}

	.tips {
		color: #969696;
	}

	.tips>text {
		color: #007aff;
	}

	.marginBottom {
		margin-bottom: 30upx;
	}

	.img-title {
		/* margin-bottom: 30upx; */
		padding: 0 25upx;
		font-size: 28upx;
		line-height: 80upx;
		font-weight: bold;
		color: #000;
		margin-top: 30upx;
		background: #fff;
	}

	.consumer .cu-form-group .title {
		min-width: 180upx;
		color: #333333;
	}

	.consumer .cu-form-group input {
		font-size: 28rpx;
		color: #333333;
	}
</style>