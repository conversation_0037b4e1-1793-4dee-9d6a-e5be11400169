<template>
	<view class="self-check">
		<view class="container">
			<!-- 			<view class="connecting" v-if="connectStatus == '3'">
				<image class="img" src="../static/newSelfCheck/connect_device.png" alt="">
					<view class="connect-tips">
						<view class="title">
							正在检测设备
						</view>
						<view class="desc">
							检测过程中请勿关闭小程序
						</view>
					</view>
			</view> -->
			<view class="connecting" v-if="connectStatus == '2'">
				<image class="img" src="../static/newSelfCheck/connecting.gif" alt="">
					<view class="connect-tips">
						<view class="title">
							正在连接设备
						</view>
						<view class="desc">
							连接过程中请勿关闭小程序或设备
						</view>
					</view>
			</view>
			<view class="connect-fail" v-if="connectStatus == '1'">
				<image class="img" src="../static/newSelfCheck/fail_status.png" alt="">
					<view class="connect-tips">
						<view class="title red">
							蓝牙连接失败
						</view>
						<view class="desc">
							请检查手机蓝牙与设备蓝牙是否打开
						</view>
						<view class="link" @click="toNotBlue">
							设备蓝牙无法连接?
						</view>
					</view>
			</view>
			<view class="connect-success" v-if="connectStatus == '0' || connectStatus == '3'">
				<image class="img" src="../static/newSelfCheck/connect_device.png" alt="">
					<view class="connect-tips">
						<block v-if="connectStatus == '3'">
							<view class="connect-tips">
								<view class="title">
									正在检测设备
								</view>
								<view class="desc">
									检测过程中请勿关闭小程序
								</view>
							</view>
						</block>
						<block v-if="connectStatus == '0'">
							<view class="title">
								蓝牙连接成功，开始检测
							</view>
							<view class="desc">
								连接过程中请勿关闭小程序或设备
							</view>
							<view class="desc">
								检测预计完成时间为60S
							</view>
						</block>
						<view class="status-container" v-if="Object.keys(detail).length > 0">
							<view class="status-wrapper">
								<view class="item">
									<image :src="deviceStatus ? successSrc : failSrc" alt="">
										<view class="item-text">
											设备电量{{deviceStatus ? '正常':'异常'}}
										</view>
								</view>
								<view class="item">
									<image
										:src="!cardStatus || detail.showCheckResultDTO.cardStateCheck.state == '0' ?  failSrc: successSrc"
										alt="">
										<view class="item-text">
											卡片状态{{!cardStatus || detail.showCheckResultDTO.cardStateCheck.state == '0' ? '异常':'正常'}}
										</view>
								</view>
								<view class="item">
									<image
										:src="!obuStatus || detail.showCheckResultDTO.obuStateCheck.state == '0' ? failSrc: successSrc"
										alt="">
										<view class="item-text">
											电子标签状态{{!obuStatus || detail.showCheckResultDTO.obuStateCheck.state == '0' ? '异常':'正常'}}
										</view>
								</view>
								<view class="item">
									<image :src="detail.showCheckResultDTO.cardObuCarState == '0' ? failSrc: successSrc"
										alt="">
										<view class="item-text">
											ETC卡和电子标签所记录车牌号一致
										</view>
								</view>
								<view class="item" v-if="detail.showCheckResultDTO.amountState.ifShow == 0">
									<image :src="handle.amountState == '0' ? failSrc: successSrc" alt="">
										<view class="item-text">
											{{detail.showCheckResultDTO.amountState.remark}}
										</view>
								</view>
								<view class="item">
									<image :src="detail.showCheckResultDTO.isBlacklist == '0' ? failSrc: successSrc"
										alt="">
										<view class="item-text">
											是否处于黑名单
										</view>
								</view>
							</view>
						</view>
					</view>
			</view>
		</view>
		<view class="bottom-container" v-if="connectStatus == '1'">
			<confirmBtn name="点击重试" @click="reConnect()"></confirmBtn>
		</view>
		<neil-modal :show="modal.show" :title="modal.title" :auto-close="modal.close" :align="modal.align"
			:showCancel="modal.showCancel" :showConfirm="modal.showConfirm">
			<view class="content-wrapper">
				<view class="btn-wrapper">
					<view class="title">检测完成</view>
					<confirmBtn name="确定" @click="confirm()"></confirmBtn>
				</view>
			</view>
		</neil-modal>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import bleapi from '@/common/bluetooth/bleUtil.js';
	import {
		facapi
	} from '@/common/bluetooth/facUtil.js';

	// 正式，顺序优先
	const newPinCode = '123456';
	// 正式，顺序优先
	const pinCode = '************';
	const terminalNo = '123456789012';
	const money = 20000000;
	const defaultRand = '00000000';
	const defaultObuId = '0000000000000000';

	var _that;
	//蓝牙服务名前缀
	import Vue from 'vue';
	const SERVICENAME_PREFIX = Vue.prototype.$bluetoothName;

	import confirmBtn from '../components/self-check/comfirm-btn/confirm-btn.vue'

	export default {
		name: 'connect',
		components: {
			confirmBtn
		},
		data() {
			return {
				// isConnected: true,
				connectStatus: '2', //0成功1失败2连接中3非蓝牙连接
				deviceStatus: true,
				cardStatus: true,
				obuStatus: true,
				cpuNo: '',
				obuNo: '',
				isBt: '0',
				successSrc: '../static/newSelfCheck/success_status.png',
				failSrc: '../static/newSelfCheck/fail.png',
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					showConfirm: false,
				},
				detail: {}

			}
		},
		computed: {

		},
		onLoad(option) {
			_that = this
			// if (option && option.connectStatus) {
			// 	this.connectStatus = option.connectStatus
			// }
			if (option && option.notBlueData) {
				//非蓝牙检测
				this.connectStatus = '3'
				let params = JSON.parse(decodeURIComponent(option.notBlueData))
				this.getDeviceStatus(params)

			}
			if (this.connectStatus != '3') {
				//蓝牙连接时才调用
				this.connectBluetooth()
			}
		},
		created() {

		},
		methods: {
			toNotBlue() {
				uni.navigateTo({
					url: '/pagesC/selfCheck/notBlue'
				})
			},
			getDeviceStatus(params) {
				console.log('======>>>>>>>>>>>>', params)
				let data
				if (params.isBt == '0') {
					//蓝牙
					data = {
						bt: {
							flag: params.flag,
							cardNo: params.cardNo,
							obuNo: params.obuNo
						},
						isBt: params.isBt
					}
				} else {
					//非蓝牙
					data = {
						unBt: {
							allNo: params.allNo,
							carNo: params.vehicleCode,
							carNoColor: params.vehicleColor,
							deviceState: params.deviceState,
							showAmount: params.showAmount,
							showCard: params.showCard,
							showObu: params.showObu
						},
						isBt: params.isBt
					}
				}

				this.$request
					.post(this.$interfaces.getStatusByBlue, {
						data: data
					})
					.then((res) => {
						console.log('蓝牙检测结果返回', res)
						if (res.code == 200) {
							let result = res.data
							this.detail = result
							this.modal.show = true
						} else {
							if (params.isBt == '1') {
								//报错返回非蓝牙页
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false,
									success: (res) => {
										if (res.confirm) {
											uni.navigateBack()
										}
									}
								})
							} else {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
						}

					})
					.catch((error) => {
						this.isLoading = false
					})
			},
			confirm() {
				let status, result; //0正常1异常
				if (this.detail.handle.amountState == 1 || this.detail.handle.deviceState == 1 || this.detail.handle
					.restrictUse == 1) {
					result = {
						status: '1',
						...this.detail
					}
				} else {
					result = {
						status: '0',
						...this.detail
					}

				}
				uni.redirectTo({
					url: '/pagesC/selfCheck/result?result=' + encodeURIComponent(JSON.stringify(result))
				})
			},
			reConnect() {
				this.connectStatus = '2'
				this.connectBluetooth()
			},
			connectBluetooth() {
				bleapi.CloseBle((obj) => {
					_that.scanDevice()
				})

			},
			closeBleAndFail() {
				this.disConnect()
				this.connectStatus = '1'
			},
			disConnect() {
				bleapi.closeBLEConnection();
				if (facapi.facSdk && facapi.facSdk.DisconnectDevice) {
					facapi.facSdk.DisconnectDevice(function(code) {
						console.log('关闭连接结果', code);
					});
				}
			},
			closeBle() {
				// 关闭蓝牙模块，防止中途断开，连接不上
				// bleapi.CloseBle((obj) => {
				// 	console.log(obj)
				// })
				this.disConnect()
			},
			// 开启扫描蓝牙设备
			scanDevice(callback) {
				bleapi.ScanDevice(SERVICENAME_PREFIX, (devResult) => {
					console.log('ScanDevice', devResult);
					if (devResult.code != 0) {
						console.log('搜索失败', devResult);
						_that.closeBleAndFail()
						//搜索设备失败
						_that.showModal({
							title: '错误',
							devResult: devResult,
							content: devResult.err_msg,
							showMyContent: true
						});
					} else {
						console.log('搜索到设备:' + devResult + ' ' + devResult.data.device_name);
						facapi.ConnectDevice(
							devResult.data,
							function(onDisconnect) {
								console.log('连接回调：', onDisconnect);
							},
							function(result) {
								console.log(result, 'result');
								bleapi.StopScanDevice(function(code) {
									console.log('返回数据', code);
								});
								if (result.code == 0) {
									_that.connectStatus = '0'
									_that.deviceStatus = true
									console.log('连接标签设备成功');
									// callback(true);
									_that.getCardNo()
								} else {
									_that.deviceStatus = false
									_that.closeBleAndFail()
									// _that.closeLoading()
									// _that.closeBle()
									// _that.connectStatus = '1'
									// _that.showModal({
									// 	title: '错误',
									// 	devResult: result,
									// 	content: '设备连接失败，请将手机靠近设备后重试。',
									// 	showMyContent: true
									// });
									// callback(false);
								}
							}
						);
					}
				});
			},
			//读取卡号
			getCardNo() {
				facapi.OpenCard(devResult => {
					if (devResult.code == 0) {
						facapi.GetCardNo(devResult => {
							if (devResult.code == 0) {
								_that.cpuNo = devResult.data;
								_that.cardStatus = true
								//再获取OBU号
								_that.getObuNo()
							} else {
								_that.cardStatus = false
								_that.showModal({
									title: '错误',
									devResult: devResult,
									content: '获取卡号失败：' + devResult.code + ':' + devResult
										.err_msg + (devResult.msg ? ':' + devResult.msg :
											'')
								});
								// _that.disConnect();
							}
						});
					} else {
						_that.showModal({
							title: '错误',
							devResult: devResult,
							content: '打开卡失败：' + devResult.code + ':' + devResult.err_msg + (
								devResult.msg ? ':' + devResult.msg : '')
						});
						// _that.disConnect();
					}
				});
			},
			// 打开OBU通道，读取obu号
			getObuNo() {
				facapi.OpenChannel(devResult => {
					console.log('sdk的OpenChannel调用', devResult);
					if (devResult.code == 0) {
						// 读取设备sn号
						facapi.GetSerialNo(devResult => {
							console.log('getSerialNo==>>', JSON.stringify(devResult));
							// _that.setSystemInfo(devResult);
							if (!devResult.data) {
								//判断某些设备返回0时也读不到OBU
								// _that.disConnect();
								_that.closeBleAndFail()
								_that.obuStatus = false
								return
							}
							_that.obuStatus = true
							_that.obuNo = devResult.data;

							//获取OBU标志位
							facapi.GetSystemInfo(devResult => {
								if (devResult.code == 0) {
									//接口数据
									let params = {
										flag: devResult.data.flag,
										cardNo: _that.cpuNo,
										obuNo: _that.obuNo,
										isBt: _that.isBt
									}
									//关闭蓝牙
									_that.closeBle()
									_that.getDeviceStatus(params)
								} else {
									_that.obuStatus = false
									_that.showModal({
										title: '错误',
										devResult: devResult,
										content: 'OBU失效:' + devResult.code + ':' +
											devResult.err_msg + (devResult
												.msg ? ':' + devResult.msg : '')
									});
								}
							})
						});
					} else {
						_that.obuStatus = false
						_that.showModal({
							title: '错误',
							devResult: devResult,
							content: '打开Obu失败:' + devResult.code + ':' + devResult.err_msg + (devResult
								.msg ? ':' + devResult.msg : '')
						});
						// _that.getSystemInfo(devResult.code, devResult.err_msg + (devResult.msg ? ':' + devResult
						// 	.msg : ''), defaultObuId, defaultRand, result => {});
						// _that.disConnect();
					}
				});
			},
			// 显示弹框
			showModal(data) {
				//隐藏loading

				console.log(data.content, 'sdk报错');
				_that.closeBleAndFail()
				//显示弹框
				let obj = {
					...data
				}
				obj = data.showMyContent ? obj : {
					...data,
					content: data.devResult.code + ':' + data.devResult.err_msg
				}
				uni.showModal({
					...obj,
					showCancel: false,
					success: () => {}
				});
			},
			toNext() {
				uni.navigateTo({
					url: './openDeviceBlue'
				})
			}
		},
	}
</script>

<style lang='scss' scoped>
	.self-check {
		width: 100%;
		height: 100%;
		background-color: $uni-bg-color;
		font-family: PingFangSC-Medium, PingFang SC;
		color: #777777;
	}

	.container {
		padding: 100rpx 0 30rpx 0;
		text-align: center;
	}

	.connect-tips {
		margin-top: 50rpx;
	}

	.title {
		margin-bottom: 10rpx;
		font-size: 36rpx;
		color: #777777;
		font-weight: 700;
	}

	.desc {
		color: #717171;
		letter-spacing: 2rpx;
	}

	.img {
		width: 650rpx;
		height: 434rpx;
		text-align: center;
	}

	.bottom-container {
		position: fixed;
		right: 0;
		left: 0;
		margin: auto;
		bottom: 130rpx;
	}

	.confirm {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 35rpx;
	}

	.confirm-title {
		font-size: 30rpx;
		margin-left: 20rpx;
		letter-spacing: 4rpx;
		color: #7a7a7a;

	}

	.link {
		margin-top: 10rpx;
		text-align: center;
		color: #3E98FF;
		text-decoration: underline;
		text-underline-offset: 10rpx;
	}

	.red {
		color: #F50000;
	}

	.status-container {
		margin-top: 80rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.status-wrapper {
		display: flex;
		flex-direction: column;
		width: 350rpx;
	}

	.item {
		margin-top: 20rpx;
		display: flex;
		align-items: center;
	}

	.item image {
		flex: 0 0 35rpx;
		width: 35rpx;
		height: 35rpx;
	}

	.item-text {
		text-align: left;
		flex-direction: column;
		margin-left: 20rpx;
		color: #717171;
		letter-spacing: 2rpx;
	}

	.content-wrapper {
		margin-top: 40rpx;
	}

	.content-wrapper .title {
		font-size: 36rpx;
		color: #777777;
		font-weight: 700;
	}

	/deep/ .content-wrapper .btn-wrapper .btn {
		margin-top: 20rpx;
		margin-bottom: 30rpx;
	}
</style>