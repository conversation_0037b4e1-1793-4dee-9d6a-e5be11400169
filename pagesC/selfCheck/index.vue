<template>
	<view class="self-check">
		<view class="container">
			<view class="title">
				打开蓝牙开关
			</view>
			<view class="desc">
				请停车熄火关闭车载蓝牙或其他设备，以确保手机连接设备蓝牙时不受干扰。
			</view>
			<image class="img" src="../static/newSelfCheck/open_blue.png" alt="">
		</view>
		<view class="bottom-container">
			<view class="confirm" @click="check">
				<image v-if="!checked" src="../static/newSelfCheck/select.png" alt="">
					<image v-else src="../static/newSelfCheck/success_status.png" alt="">
						<view class="confirm-title">我已打开手机蓝牙</view>
			</view>
			<confirmBtn @click="toNext()"></confirmBtn>
		</view>
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:showConfirm="modal.showConfirm">
			<view class="content-wrapper">
				<view class="title">操作须知</view>
				<view class="desc-wrapper">
					<view class="desc">1.请勿移动设备，否则设备将无法正常使用。</view>
					<view class="desc">2.请先检查OBU上是否有蓝牙标志。</view>
					<image class="icon-blue" src="../static/newSelfCheck/blue.png" mode=""></image>
					<view class="desc">3.请检查OBU上是否有“ 中国交通 ”字样。</view>
					<image class="icon-zgjt" src="../static/newSelfCheck/zgjt.png" mode=""></image>
					<view class="desc">4.无蓝牙标志或“ 中国交通 ”字样请点击。</view>
					<view class="link" @click="toNotBlue">非蓝牙设备自检</view>
					<view class="desc">5.操作时需打开手机蓝牙及设备蓝牙。</view>
				</view>
				<view class="btn-wrapper">
					<confirmBtn name="我已了解，开始检测" @click="confirm()"></confirmBtn>
					<view class="confirm" @click="isCheckRemind">
						<image v-if="!isRemind" src="../static/newSelfCheck/select.png" alt="">
							<image v-else src="../static/newSelfCheck/success_status.png" alt="">
								<view class="confirm-title">下次不再提醒</view>
					</view>
				</view>
			</view>
		</neil-modal>
	</view>
</template>

<script>
	import {
		getLoginUserInfo
	} from '@/common/storageUtil.js'
	import confirmBtn from '../components/self-check/comfirm-btn/confirm-btn.vue'
	import neilModal from '@/components/neil-modal/neil-modal.vue'
	export default {
		name: 'selfCheck',
		components: {
			neilModal,
			confirmBtn
		},
		data() {
			return {
				checked: false,
				isRemind: false,
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					showConfirm: false,
				},
			}
		},
		computed: {

		},
		onLoad() {
			this.handleRemind()
		},
		methods: {
			handleRemind() {
				this.$request
					.post(this.$interfaces.deviceRemind, {
						data: {
							id: getLoginUserInfo().userIdStr
						}
					})
					.then((res) => {
						console.log('isRemind', res)
						if (res.code == 200) {
							this.modal.show = res.data
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch((error) => {
						this.isLoading = false
					})
			},
			saveRemind() {
				console.log('getLoginUserInfo', getLoginUserInfo())
				this.$request
					.post(this.$interfaces.saveDeviceRemind, {
						data: {
							id: getLoginUserInfo().userIdStr,
							status: this.isRemind ? '1' : '0'
						}
					})
					.then((res) => {
						console.log('saveRemind', res)
						if (res.code == 200) {
							// this.modal.show = res.data
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch((error) => {
						this.isLoading = false
					})
			},
			check() {
				this.checked = !this.checked
			},
			isCheckRemind() {
				this.isRemind = !this.isRemind
			},
			confirm() {
				this.modal.show = false
				this.saveRemind()

			},
			toNext() {
				if (this.checked) {
					uni.navigateTo({
						url: './openDeviceBlue'
					})
				} else {
					uni.showModal({
						title: '提示',
						content: '请先勾选已打开手机蓝牙',
						showCancel: false
					})
				}
			},
			toNotBlue() {
				uni.navigateTo({
					url: './notBlue'
				})
			},
		},
	}
</script>

<style lang='scss' scoped>
	.self-check {
		width: 100%;
		height: 100%;
		background-color: $uni-bg-color;
		font-family: PingFangSC-Medium, PingFang SC;
		color: #777777;
	}

	.container {
		padding: 100rpx 50rpx 30rpx 50rpx;
	}

	.container .title {
		margin-left: 30rpx;
		margin-bottom: 20rpx;
		font-size: 36rpx;
		color: #777777;
		font-weight: 700;
	}

	.container .desc {
		margin-left: 30rpx;
		color: #717171;
		margin-bottom: 30rpx;
		letter-spacing: 2rpx;
	}

	.img {
		width: 100%;
		height: 367rpx;
		text-align: center;
	}

	.bottom-container {
		position: fixed;
		right: 0;
		left: 0;
		margin: auto;
		bottom: 130rpx;
	}

	.confirm {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 35rpx;
	}

	.confirm image {
		width: 35rpx;
		height: 35rpx;
	}

	.confirm-title {
		font-size: 30rpx;
		margin-left: 20rpx;
		letter-spacing: 4rpx;
		color: #7a7a7a;

	}

	/deep/ .neil-modal__container {
		width: 85%;
	}

	/deep/ .neil-modal__footer-left {
		border-radius: 12rpx;
		background-color: #ffffff;
		border-width: 1rpx;
		border-style: solid;
		border-color: #0066E9;
		color: #0066E9 !important;
		height: 60rpx;
		line-height: 60rpx;
	}

	/deep/ .neil-modal__footer-right {
		border-radius: 100rpx;
		color: #ffffff;
		background-color: #0066E9;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 30rpx;
		margin: 30rpx 120rpx !important;
	}

	.desc-wrapper {
		margin-top: 20rpx;
		padding-left: 40rpx;
		padding-right: 40rpx;
	}

	.desc-wrapper .desc {
		text-align: left;
		color: #717171;
		margin-bottom: 20rpx;
	}

	.checkbox-wrapper {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.content-wrapper {
		margin-top: 40rpx;
	}

	.content-wrapper .title {
		margin-bottom: 30rpx;
		font-size: 36rpx;
		color: #777777;
		font-weight: 700;
	}

	.icon-blue {
		text-align: center;
		width: 112rpx;
		height: 96rpx;
		margin-bottom: 10rpx;
	}

	.icon-zgjt {
		text-align: center;
		width: 128rpx;
		height: 128rpx;
		margin-bottom: 10rpx;
	}

	/deep/ .content-wrapper .btn-wrapper .btn {
		padding: 10rpx 40rpx;
		margin-top: 40rpx;
		margin-bottom: 30rpx;
	}



	.link {
		margin-top: 10rpx;
		margin-bottom: 30rpx;
		text-align: center;
		color: #007AFF;
		text-decoration: underline;
		text-underline-offset: 10rpx;
	}
</style>
