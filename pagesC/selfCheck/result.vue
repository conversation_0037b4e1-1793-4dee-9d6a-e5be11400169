<template>
	<view class="result-container">
		<view class="container">
			<image class="img" src="../static/newSelfCheck/open_device.png" alt="">
				<!-- 				<view class="result">
					检测结果：<text
						:class="result.status == 1?'red-class':'green-class'">{{result.status == 0 ?'设备状态正常':'设备状态异常'}}</text>
				</view> -->
				<view class="result" v-if="Object.keys(result).length > 0">
					检测结果：<text :class="result.status == 1?'red-class':'green-class'">{{resultText()}}</text>
				</view>
		</view>
		<!-- <view class="func" v-if="result.status == '1'"> -->
		<view class="func">
			<handleText :handleCode="result.handleShowDTO.handleCode" :orgName="result.handleShowDTO.orgName">
			</handleText>
		</view>
		<view class="tips-wrapper">
			<view class="tips-title">
				温馨提示
			</view>
			<view class="tips-item">
				1、线下网点地址<text class="link" @click="toWeb">查询</text>
			</view>
			<view class="tips-item">
				2、如有其他疑问请拨打广西捷通ETC客服热线<text class="link" @click="call('0771-5896333')">0771-5896333</text>咨询
			</view>
		</view>
		<view class="status-container" v-if="Object.keys(result).length > 0">
			<view class="icon-wrapper">
				<view class="text">
					检测详情
				</view>
				<image src="../static/newSelfCheck/arrow-down.png" mode=""></image>
			</view>
			<view class="status-wrapper">
				<view class="item" v-if="result.isBt == '0'">
					<image :src="successSrc" alt="">
						<view class="item-text">
							设备电量正常
						</view>
				</view>
				<view class="item">
					<image :src="result.showCheckResultDTO.cardStateCheck.state == '0' ?  failSrc: successSrc" alt="">
						<view class="item-text">
							卡片状态{{result.showCheckResultDTO.cardStateCheck.state == '0' ? '异常':'正常'}}
						</view>
				</view>
				<view class="item">
					<image :src="result.showCheckResultDTO.obuStateCheck.state == '0' ? failSrc: successSrc" alt="">
						<view class="item-text">
							电子标签状态{{result.showCheckResultDTO.obuStateCheck.state == '0' ? '异常':'正常'}}
						</view>
				</view>
				<view class="item" v-if="result.isBt == '0'">
					<image :src="result.showCheckResultDTO.cardObuCarState == '0' ? failSrc: successSrc" alt="">
						<view class="item-text">
							ETC卡和电子标签所记录车牌号一致
						</view>
				</view>
				<view class="item" v-if="result.showCheckResultDTO.amountState.ifShow == 0">
					<image :src="handle.amountState == '0' ? failSrc: successSrc" alt="">
						<view class="item-text">
							{{result.showCheckResultDTO.amountState.remark}}
						</view>
				</view>
				<view class="item">
					<image :src="result.showCheckResultDTO.isBlacklist == '0' ? failSrc: successSrc" alt="">
						<view class="item-text">
							是否处于黑名单
						</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import confirmBtn from '../components/self-check/comfirm-btn/confirm-btn.vue'
	import handleText from '../components/self-check/handle-text/handle-text.vue'
	export default {
		name: 'openDeviceBlue',
		components: {
			confirmBtn,
			handleText
		},
		data() {
			return {
				successSrc: '../static/newSelfCheck/success_status.png',
				failSrc: '../static/newSelfCheck/fail.png',
				result: {}
			}
		},
		computed: {

		},
		onLoad(options) {
			if (options && options.result) {
				this.result = JSON.parse(decodeURIComponent(options.result))
			}
			console.log('result', this.result)
		},
		methods: {
			resultText() {
				if (this.result.status == '1') {
					if (this.result.handle.restrictUse == '1') {
						return '限制使用'
					} else if (this.result.handle.amountState == '1') {
						return '余额不足'
					} else if (this.result.handle.deviceState == '1') {
						return '设备状态异常'
					}
				} else {
					return '设备状态正常'
				}
			},
			call(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber: phoneNumber
				});
			},
			toWeb() {
				let url = 'https://www.gxetc.com.cn/h5/#/businessOutlets'
				uni.navigateTo({
					url: '/pages/uni-webview/h5-webview?ownPath=' + encodeURIComponent(url)
				})
			},
			toNext() {
				uni.navigateTo({
					url: './connect'
				})
			}
		},
	}
</script>

<style lang='scss' scoped>
	.red-class {
		color: red;
	}

	.green-class {
		color: green;
	}

	.result-container {
		/* width: 100%; */
		/* height: 100%; */
		padding-bottom: 50rpx;
		background-color: $uni-bg-color;
		font-family: PingFangSC-Medium, PingFang SC;
		color: #777777;
	}

	.container {
		padding: 100rpx 50rpx 30rpx 50rpx;
	}

	.title {
		margin-left: 30rpx;
		margin-bottom: 20rpx;
		font-size: 36rpx;
		color: #777777;
		font-weight: 700;
	}

	.desc {
		margin-left: 30rpx;
		color: #717171;
		letter-spacing: 2rpx;
	}

	.img {
		width: 100%;
		height: 400rpx;
		text-align: center;
	}

	.bottom-container {
		position: fixed;
		right: 0;
		left: 0;
		margin: auto;
		bottom: 130rpx;
	}

	.confirm {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 35rpx;
	}

	.confirm-title {
		font-size: 30rpx;
		margin-left: 20rpx;
		letter-spacing: 4rpx;
		color: #7a7a7a;

	}

	.link {
		font-weight: 700;
		margin-top: 30rpx;
		text-align: center;
		color: #3E98FF;
		text-decoration: underline;
		text-underline-offset: 5rpx;
	}

	.result {
		margin: 30rpx 0;
		font-size: 30rpx;
		font-weight: 700;
		text-align: center;
	}

	.tips-wrapper {
		margin-top: 30rpx;
		padding: 0 50rpx;

		.tips-title {}

		.tips-item {
			margin-top: 20rpx;
		}
	}

	.status-container {
		margin-top: 30rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding-bottom: 40rpx;
	}

	.status-wrapper {
		display: flex;
		flex-direction: column;
		width: 350rpx;
	}

	.icon-wrapper {
		text-align: center;

		image {
			width: 80rpx;
			height: 40rpx;
		}
	}

	.text {
		font-size: 30rpx;
		color: #a3a3a3;
		font-weight: 700;
	}

	.item {
		margin-top: 20rpx;
		display: flex;
		align-items: center;

		&:first-child {
			margin-top: 0;
		}
	}

	.item image {
		flex: 0 0 35rpx;
		width: 35rpx;
		height: 35rpx;
	}

	.item-text {
		text-align: left;
		flex-direction: column;
		margin-left: 20rpx;
		color: #717171;
		letter-spacing: 2rpx;
	}
</style>