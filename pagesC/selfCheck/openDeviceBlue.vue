<template>
	<view class="self-check">
		<view class="container">
			<view class="title">
				打开设备蓝牙
			</view>
			<view class="desc">
				请在三秒内完成：1.插卡 — 2.拔卡 — 3.插卡
			</view>
			<view class="desc" style="margin-bottom: 30rpx;">
				激活设备蓝牙功能（显示“蓝牙打开”）
			</view>
			<image class="img" src="../static/newSelfCheck/open_device.gif" alt="">
				<view class="link" @click="showVideo">
					点击查看蓝牙打开方式
				</view>
				<view class="link" @click="toNotBlue">
					设备蓝牙无法打开?
				</view>
		</view>
		<view class="bottom-container">
			<view class="confirm" @click="check">
				<image v-if="!checked" src="../static/newSelfCheck/select.png" alt="">
					<image v-else src="../static/newSelfCheck/success_status.png" alt="">
						<view class="confirm-title">我已打开设备蓝牙</view>
			</view>
			<confirmBtn @click="toNext"></confirmBtn>
		</view>
	</view>
</template>

<script>
	import confirmBtn from '../components/self-check/comfirm-btn/confirm-btn.vue'
	export default {
		name: 'openDeviceBlue',
		components: {
			confirmBtn
		},
		data() {
			return {
				checked: false,
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					showConfirm: false,
				},
			}
		},
		computed: {

		},
		created() {

		},
		methods: {
			check() {
				this.checked = !this.checked
			},
			close() {
				this.modal.show = false
			},
			showVideo() {
				let url =
					'https://mp.weixin.qq.com/s?__biz=MzAxNjUxNDYwNg==&mid=2662548045&idx=1&sn=780e51d1bbeccf23d3ec8dde2e502aa7&chksm=80b4490cb7c3c01a13ac2b109b7d52fe4178c6af9c5810b8c26ea837c7ecc73e9fb1ddd96ec3#rd'
				uni.navigateTo({
					url: '/pages/uni-webview/h5-webview?ownPath=' + encodeURIComponent(url) +'&title=蓝牙打开方式'
				})
			},
			videoErrorCallback(e) {
				uni.showModal({
					content: e.target.errMsg,
					showCancel: false
				})
			},
			toNotBlue() {
				uni.navigateTo({
					url: './notBlue'
				})
			},
			toNext() {
				if (this.checked) {
					uni.navigateTo({
						url: './connect'
					})
				} else {
					uni.showModal({
						title: '提示',
						content: '请先勾选已打开设备蓝牙',
						showCancel: false
					})
				}
			}
		},
	}
</script>

<style lang='scss' scoped>
	.self-check {
		width: 100%;
		height: 100%;
		background-color: $uni-bg-color;
		font-family: PingFangSC-Medium, PingFang SC;
		color: #777777;
	}

	.container {
		padding: 100rpx 50rpx 30rpx 50rpx;
	}

	.title {
		margin: 10rpx;
		font-size: 30rpx;
		color: #777777;
		font-weight: 700;
	}

	.desc {
		margin-left: 30rpx;
		color: #717171;
		letter-spacing: 2rpx;
	}

	.img {
		width: 100%;
		height: 367rpx;
		text-align: center;
	}

	.bottom-container {
		position: fixed;
		right: 0;
		left: 0;
		margin: auto;
		bottom: 130rpx;
	}

	.confirm {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 35rpx;
	}

	.confirm image {
		width: 35rpx;
		height: 35rpx;
	}

	.confirm-title {
		font-size: 30rpx;
		margin-left: 20rpx;
		letter-spacing: 4rpx;
		color: #7a7a7a;

	}

	.link {
		margin-top: 30rpx;
		text-align: center;
		color: #3E98FF;
		text-decoration: underline;
		text-underline-offset: 10rpx;
	}

	/deep/ .neil-modal__container {
		width: 80%;
	}

	.icon-close {
		position: absolute;
		right: 4rpx;
		top: -18rpx;
	}
</style>
