<template>
	<view class="not-blue">
		<view class="container">
			<image class="img" src="../static/newSelfCheck/open_device.png" alt="">
		</view>
		<view class="select-wrapper">
			<view class="desc">
				请输入车牌号及车牌颜色,再插卡选择电子标签屏幕上所显示内容，符合的项目请打√。
			</view>
			<view class="picker" @click="toggle">
				<view class="search-wrapper" :class="{'hasClass':formData.vehicleColor}">
					<image v-if="!formData.vehicleColor" class="icon-search" src="../static/newSelfCheck/search.png"
						mode=""></image>
					<view class="otherColor">{{colorName}}</view>
				</view>
			</view>
			<view @click="plateShow = true" class="picker" style="margin-top: 30rpx;">
				<view class="search-wrapper" :class="{'hasClass':formData.vehicleCode}">
					<image v-if="!formData.vehicleCode" class="icon-search" src="../static/newSelfCheck/search.png"
						mode=""></image>
					<view class="otherColor">{{codeName}}</view>
				</view>
			</view>
			<view class="checked-container">
				<view class="check-list" v-for="(item,index) in checkList" :key="index" @click="checkListHandle(index)">
					<image class="icon-select"
						:src="item.checked?'../static/newSelfCheck/success_status.png':'../static/newSelfCheck/select.png'"
						mode=""></image>
					<view class="text">
						{{item.desc}}
					</view>
				</view>
				<view v-if="checkList[1].checked" class="child-list" v-for="(item,index) in checkChild" :key="index"
					@click="checkChildHandle(index)">
					<image class="icon-select"
						:src="item.checked?'../static/newSelfCheck/success_status.png':'../static/newSelfCheck/select.png'"
						mode=""></image>
					<view class="text">
						{{item.desc}}
					</view>
				</view>
			</view>
		</view>
		<view class="bottom-container">
			<confirmBtn name="确定" @click="toCheck()"></confirmBtn>
		</view>
		<plate-input v-if="plateShow" :plate="formData.vehicleCode" @export="setPlate" @close="plateShow=false" />
		<uni-popup class="uni-popup" ref="select" type="bottom" safeArea backgroundColor="#fff">
			<uni-popup-select @select="selectColor">
			</uni-popup-select>
		</uni-popup>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import plateInput from '@/components/uni-plate-input/uni-plate-input.vue';
	import confirmBtn from '../components/self-check/comfirm-btn/confirm-btn.vue'
	// import uniPopup from '../components/uni-popup/uni-popup.vue'
	import uniPopupSelect from '../components/self-check/uni-popup-select/uni-popup-select.vue'
	import {
		vehicleColorPicker
	} from '@/common/const/optionData.js'
	import tLoading from "@/components/common/t-loading.vue"
	export default {
		components: {
			plateInput,
			confirmBtn,
			// uniPopup,
			tLoading,
			uniPopupSelect
		},
		data() {
			return {
				isLoading: false,
				vehicleColorPicker,
				plateShow: false,
				isBt: '1',
				formData: {
					vehicleColor: '',
					vehicleCode: ''
				},
				colorName: '请选择车牌颜色(非车身颜色)',
				codeName: '请输入车牌号',
				checkList: [{
					desc: '插卡之后,设备无反应',
					checked: true,
				}, {
					desc: '插卡之后,设备有反应',
					checked: false,
				}],
				checkChild: [{
					desc: '屏幕上显示余额',
					checked: false
				}, {
					desc: '屏幕上显示“记账卡”',
					checked: false
				}, {
					desc: '屏幕上显示“标签失效”或“已拆卸” ',
					checked: false
				}, {
					desc: '以上均不是',
					checked: false
				}],
				detail: {}
			}
		},
		methods: {
			toggle() {
				this.$refs.select.open()
			},
			selectColor(item) {
				this.colorName = item.label
				this.formData.vehicleColor = item.value
			},
			toCheck() {
				if (!this.formData.vehicleCode || !this.formData.vehicleColor) {
					uni.showModal({
						title: '提示',
						content: '请先输入车辆信息',
						showCancel: false
					})
					return
				}

				if (this.checkList[1].checked) {
					//如果选了第二项，判断子项不能为空
					let filter = this.checkChild.filter(item => {
						return item.checked
					})
					if (filter.length == 0) {
						uni.showModal({
							title: '提示',
							content: '请先选择对应的设备提示',
							showCancel: false
						})
						return
					}
				}
				let params = {
					...this.formData,
					isBt: '1',
					deviceState: ''
				}
				//设备有无反应
				this.checkList[1].checked ? params.deviceState = '0' : params.deviceState = '1'
				//设备有反应
				if (params.deviceState == '0') {
					//以上均不是
					this.checkChild[3].checked ? params.allNo = '1' : params.allNo = '0'
					if (params.allNo == '0') {
						this.checkChild[0].checked ? params.showAmount = '1' : params.showAmount = '0'
						this.checkChild[1].checked ? params.showCard = '1' : params.showCard = '0'
						this.checkChild[2].checked ? params.showObu = '1' : params.showObu = '0'
					}
				}

				console.log('params===>>>>>>>', params)

				//跳转到检测页面
				// uni.navigateTo({
				// 	url: '/pagesC/selfCheck/result?notBlueData=' + encodeURIComponent(JSON.stringify(params))
				// })

				this.getDeviceStatus(params)
			},
			getDeviceStatus(params) {
				console.log('======>>>>>>>>>>>>', params)
				let data
				if (params.isBt == '0') {
					//蓝牙
					data = {
						bt: {
							cardNo: params.cardNo,
							obuNo: params.obuNo
						},
						isBt: params.isBt
					}
				} else {
					//非蓝牙
					data = {
						unBt: {
							allNo: params.allNo,
							carNo: params.vehicleCode,
							carNoColor: params.vehicleColor,
							deviceState: params.deviceState,
							showAmount: params.showAmount,
							showCard: params.showCard,
							showObu: params.showObu
						},
						isBt: params.isBt
					}
				}

				this.isLoading = true

				this.$request
					.post(this.$interfaces.getStatusByBlue, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
						console.log('蓝牙检测结果返回', res)
						if (res.code == 200) {
							// let result = res.data
							this.detail = res.data
							let status, result; //0正常1异常
							if (this.detail.handle.amountState == 1 || this.detail.handle.deviceState == 1 || this
								.detail.handle
								.restrictUse == 1) {
								result = {
									status: '1',
									isBt: this.isBt,
									...this.detail
								}
							} else {
								result = {
									status: '0',
									isBt: this.isBt,
									...this.detail
								}

							}
							uni.navigateTo({
								url: '/pagesC/selfCheck/result?result=' + encodeURIComponent(JSON.stringify(
									result))
							})
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}

					})
					.catch((error) => {
						this.isLoading = false
					})
			},
			checkListHandle(index) {
				if (index == 0) {
					this.checkList[index].checked = true
					this.checkList[1].checked = false
				} else {
					this.checkList[index].checked = true
					this.checkList[0].checked = false
				}
			},
			checkChildHandle(index) {
				for (let i = 0; i < 4; i++) {
					if (index == i) {
						this.checkChild[index].checked = !this.checkChild[index].checked

					} else {
						this.checkChild[i].checked = false
					}
				}
			},
			// 选择车牌颜色
			confirmColor(e) {
				console.log('e', e, this.formData.vehicleColor)
				let index = e.detail.value;
				let value = this.vehicleColorPicker[index]
				this.colorName = value.label + '色'
				this.formData.vehicleColor = value.value
			},
			// //车牌获取回调
			setPlate(plate) {
				console.log('plate', plate, plate.length)
				if (plate.length >= 7) {
					this.formData.vehicleCode = plate
					this.codeName = plate
					this.plateShow = false
				}
				// let inputValueArr = plate.split('');
				// console.log('inputValueArr==>>>>>', inputValueArr)
				// if (inputValueArr.length == '7') {
				// 	//普通车
				// 	this.keyType = 1
				// } else if (inputValueArr.length >= '8') {
				// 	if (plate.slice(-2) == '应急') {
				// 		this.keyType = 3
				// 	} else {
				// 		//新能源车
				// 		this.keyType = 2
				// 	}
				// }
				// //回显车牌
				// this.currentInputValue = inputValueArr
			},

		}
	}
</script>

<style lang="scss" scoped>
	.hasClass {
		justify-content: center;
		color: #333333;
	}

	.not-blue {
		padding-bottom: 120rpx;
		// width: 100%;
		// height: 100%;
		// background-color: $uni-bg-color;
		// font-family: PingFangSC-Medium, PingFang SC;
		// color: #777777;
	}

	.container {
		padding: 100rpx 0 40rpx 0;
		text-align: center;
	}

	.select-wrapper {
		margin: 0 55rpx;
		padding: 30rpx;
		border-radius: 30rpx;
		background-color: #F7F7F7;

		.desc {
			margin-bottom: 20rpx;
			color: #717171;
			letter-spacing: 2rpx;
		}

		.picker {
			margin: 20rpx 50rpx 0 50rpx !important;
			padding: 15rpx;
			border-radius: 100rpx;
			background-color: $uni-bg-color;
		}

		.search-wrapper {
			display: flex;
		}

		.icon-search {
			margin-left: 20rpx;
			width: 36rpx;
			height: 36rpx;
		}

		.otherColor {
			margin-left: 10rpx;
		}

		.checked-container {
			// margin: 0 50rpx;
			// padding: 15rpx;
			// border-radius: 100rpx;
			// background-color: $uni-bg-color;
		}
	}

	.title {
		margin-bottom: 10rpx;
		font-size: 36rpx;
		color: #777777;
		font-weight: 700;
	}

	.img {
		width: 650rpx;
		height: 367rpx;
		text-align: center;
	}

	.check-list {
		margin-top: 20rpx;
		display: flex;
		// margin: 0 50rpx;
		padding: 15rpx;
		border-radius: 100rpx;
		background-color: $uni-bg-color;
	}

	.child-list {
		margin-top: 20rpx;
		margin-left: 50rpx;
		display: flex;
		// margin: 0 50rpx;
		padding: 15rpx;
		border-radius: 100rpx;
		background-color: $uni-bg-color;
	}

	.icon-select {
		width: 37rpx;
		height: 37rpx;
		margin-right: 10rpx;
	}

	.bottom-container {
		position: fixed;
		right: 0;
		left: 0;
		margin: auto;
		bottom: 0;
		height: 120rpx;
	}

	/deep/.btn-wrapper {
		background-color: #ffffff;
	}
</style>