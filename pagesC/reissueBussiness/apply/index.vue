<template>
	<view class="apply-after-sale">
		<view class="steps-wrapp">
			<handleStep :activeIndex='0' :stepList="stepList"></handleStep>
		</view>
		<view class="content">
			<view class="section">
				<vehicle :vehicleObj="formData"></vehicle>
			</view>
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">提交资料</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="list-item align-items-center"
								style="align-items: unset;margin-bottom:0rpx !important">
								<view class="list-item_label">
									<span style="color:red">*</span>补办设备
								</view>
								<view style="width: cal(100% - 140rpx);">
									<view class="radio" v-for="(item,index) in checkList" :key="index"
										@click="checkInfo(item)">
										<view class="round" :class="{'check-round':(formData.deviceType==index)}">
										</view>
										<view class="value">{{item.label}}</view>
									</view>
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label">
									<span style="color:red">*</span>补办原因
								</view>
								<view class="list-item_value list-item_view" @click="showReason=true">
									<view>{{formData.reasonTypeStr}}</view>
									<image src="../../static/ico_filter_down.png" mode="" class="down-icon"></image>
								</view>
							</view>
							<view class="list-item align-items-center">
								<view class="list-item_label">
									办理费用
								</view>
								<view class="list-item_value money">
									￥{{moneyFilter(formData.payAmount)}}元
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="section">
				<view class="order-address__detail">
					<view class="address-wrapper">
						<view class="title-wrapper">
							<view class="title">
								收货地址
							</view>
							<view class="wx-location" @click="autoFill">
								获取微信地址
							</view>
						</view>
						<view class="install">
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>收货人
								</view>
								<input class="input auto-input" v-model="formData.recipient" placeholder="请输入收货人"
									type="text">
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>手机号码
								</view>
								<input class="input" placeholder="请输入收货人手机号码" type="number" maxlength="11"
									v-model="formData.recipientPhone">
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>所在地区
								</view>
								<view class="input">
									<uni-data-picker ref="picker" placeholder="选择区域(仅支持广西区内)" :localdata="regionList"
										v-model="formData.recipientAreaCode" @change="onchange"
										@popupopened="onpopupopened" @popupclosed="onpopupclosed">
									</uni-data-picker>
									<image src="../../static/location.png" mode="" class="location-img">
									</image>
								</view>
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>详细地址
								</view>
								<input class="input auto-input" v-model="formData.recipientAddress"
									placeholder="请输入详细地址" type="text">
							</view>
						</view>
					</view>
				</view>

			</view>
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">支付方式</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="weui-card-bd-pay">
								微信支付
								<image src="../../static/<EMAIL>" mode="" class="ico_filter_down">
								</image>
							</view>
							<view class="list-item align-items-center" style="margin-top: 30rpx;">
								<view class="list-item_label">
									办理费用
								</view>
								<view class="list-item_value money">
									￥{{moneyFilter(formData.payAmount)}}元
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">

				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="activateApplyHandle">
						确认支付
					</button>
				</view>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<u-select v-model="showReason" :list="reprocessList" @confirm="confirm">
		</u-select>
		<u-modal v-model="uShow" @confirm="uShowConfirm" confirm-text="前往查看" :content="limitContent" ref="uModal"
			:async-close="true">
		</u-modal>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import handleStep from '../../components/handle-step/index.vue'
	import vehicle from '../../components/vehicle/vehicle.vue';
	import {
		getLoginUserInfo,
		getAccountId,
		getCurrUserInfo,
		getEtcAccountInfo,
		getOpenid,
		setOpenid
	} from '@/common/storageUtil.js'
	import uniDataPicker from '../../components/uni-data-picker/uni-data-picker.vue'
	import util from "@/common/util.js"
	export default {
		components: {
			tLoading,
			handleStep,
			vehicle,
			uniDataPicker
		},
		data() {
			return {
				stepList: [{
					value: '',
					label: '提交材料'
				}, {
					value: '',
					label: '支付费用'
				}, {
					value: '',
					label: '确认收货'
				}, {
					value: '',
					label: '设备激活'
				}],
				isLoading: false,
				formData: {
					carNo: '',
					carColor: '',
					deviceType: '0', //0-卡；1-OBU；2-卡和OBU
					reasonType: '201',
					reasonTypeStr: '设备丢了',
					recipient: '',
					recipientPhone: '',
					recipientAreaCode: '',
					recipientAreaName: '',
					recipientAddress: '',
					payAmount: '',
					vehicleType: '',
					vehicleTypeKind: '',
					custMastId: '',
					cardProduct: ''
				},
				showReason: false,
				reprocessList: [{
						label: '设备丢了',
						value: '201',
					},
					{
						label: '确定设备损坏直接补办',
						value: '202',
					},
					{
						label: '其他',
						value: '203',
					}
				],
				checkList: [{
						label: 'ETC卡',
						value: '0'
					},
					{
						label: 'ETC设备',
						value: '1'
					},
					{
						label: 'ETC卡及设备均需补',
						value: '2'
					}
				],
				regionList: [],
				orderId: '',
				//支付成功弹框提示
				uShow: false,
				limitContent: '',
				openId: ''
			};
		},
		computed: {

		},

		onLoad(options) {
			this.openId = getOpenid() || ''
			let vehicleInfo = this.$store.getters['afterSale/afterSaleVehicle'] || {};
			if (Object.keys(vehicleInfo).length) {
				this.formData.opBusinessType = options.opBusinessType || '';
				this.formData.carColor = vehicleInfo.vehicleColor || '';
				this.formData.carNo = vehicleInfo.vehicleCode || '';
				this.formData.cardNo = vehicleInfo.cardNo || '';
				this.formData.obuNo = vehicleInfo.obuNo || '';
				this.formData.vehicleType = vehicleInfo.vehicleType || '';
				this.formData.vehicleTypeKind = vehicleInfo.vehicleTypeKind || '';
				this.formData.custMastId = getEtcAccountInfo().custMastId
				this.formData.cardProduct = vehicleInfo.cardProduct
			}
		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
			this.getRegion();
			this.formData.userNo = getLoginUserInfo().userNo
			this.getAmount() //获取收费金额
		},
		methods: {
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			getRegion() {
				let params = {};
				this.$request.post(this.$interfaces.getRegion, {
					data: params
				}).then(res => {
					let list = [];
					if (res.code == 200) {
						list = res.data;
						for (let i = 0; i < list.length; i++) {
							list[i].children = list[i].child;
							list[i].text = list[i].label;
							for (let j = 0; j < list[i].children.length; j++) {
								list[i].children[j].children = list[i].children[j].child;
								list[i].children[j].text = list[i].children[j].label;
								for (let k = 0; k < list[i].children[j].children.length; k++) {
									list[i].children[j].children[k].children = list[i].children[j].children[k]
										.child;
									list[i].children[j].children[k].text = list[i].children[j].children[k].label;
								}
							}
						}
					}

					let gxArrList = list.filter(item => {
						return item.value == '450000'
					})
					//限制广西区域
					this.regionList = gxArrList;
					// this.regionList = list;
				})
			},
			getAmount() {
				if (this.isLoading) return;
				// this.isLoading = true;
				let params = {
					orderType: 0, //0-补领；1-更换；
					deviceType: this.formData.deviceType,
				}
				console.log('params', params)
				this.$request.post(this.$interfaces.getAmount, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log(res)
						this.formData.payAmount = res.data.payAmount
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			checkInfo(item) {
				this.formData.deviceType = item.value
				this.getAmount()
			},
			confirm(e) {
				this.formData.reasonType = e[0].value
				this.formData.reasonTypeStr = e[0].label
			},
			validateHandle() {
				let msg = ''
				if (!this.formData.recipientAddress) {
					msg = '请输入详细地址'
				}
				if (!this.formData.recipientAreaCode) {
					msg = '请选择所在地区'
				}
				if (!util.checkPhone(this.formData.recipientPhone)) {
					msg = "请输入正确的手机号码"
				}
				if (!this.formData.recipientPhone) {
					msg = '请输入手机号码'
				}
				if (!this.formData.recipient) {
					msg = '请输入收货人'
				}
				if (!this.formData.reasonType) {
					msg = '请选择补办原因'
				}
				if (msg) return msg;
				return ''
			},
			activateApplyHandle() {
				let msg = this.validateHandle();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				this.sendReissueApply()
				// this.$subscriptionMethod(this.sendReissueApply)
			},
			// 发送补办申请
			sendReissueApply() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					userNo: getLoginUserInfo().userNo,
					...this.formData,
				}
				console.log('params', params)
				this.$request.post(this.$interfaces.reissueApply, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.orderId = res.data.orderId
						this.confirmPay()
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//确认支付
			confirmPay() {
				let _self = this;
				this.isLoading = true;
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId,
					openId: this.openId,
					payType: '10000601',
				}
				let data = {
					routePath: this.$interfaces.exchangePay.method,
					bizContent: params
				};
				console.log('入参params', data)
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;
						console.log(res, '支付');
						if (res.code == 200) {
							let result = res.data;
							let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};

							wx.requestPayment({
								...payMessage,
								success: function(successres) {
									_self.isLoading = true
									console.log(successres, '支付成功回调', _self.isLoading);
									setTimeout(() => {
										_self.applyPayOrderQuery(result);
									}, 6000);
								},
								fail: function(err) {
									console.log('fail', err)
									_self.updatePayStatus(result, 3)
								},
							});
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			applyPayOrderQuery(data) {
				let params = {
					userNo: getLoginUserInfo().userNo,
					payOrderId: data.payOrderId,
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.exchangeQueryPay, {
					data: params
				}).then(res => {
					console.log('data===============>>>>>>>>>>', res)
					let status = res.data.status;
					let statusVal = {
						1: "支付中",
						2: "支付成功",
						3: "支付失败",
					};
					let msg = statusVal[status] || "支付中";
					//弹框
					this.uShow = true
					this.limitContent = msg + ',（付款后订单可能会有延迟)'
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			uShowConfirm() {
				this.uShow = false
				this.$subscriptionMethod()
				uni.redirectTo({
					url: '/pagesC/afterSaleBusiness/promptPage/index?type=10&orderId=' + this.orderId
				})
			},
			updatePayStatus(result, status) {
				let params = {
					payOrderId: result.payOrderId,
					status: status,
					payBusinessType: 3
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.exchangeUpdatePayStatus, {
					data: params
				}).then(res => {

				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},

			onpopupopened(e) {
				console.log('popupopened');
			},
			onpopupclosed(e) {
				console.log('popupclosed');
			},
			onchange(e) {
				if (e.detail.value.length == 0) return;
				console.log('---------onchange:', e);
				// this.formData.provinceName = e.detail.value[0].text
				// this.formData.cityName = e.detail.value[1].text
				// this.formData.areaName = e.detail.value[2].text
				this.formData.recipientAreaName = e.detail.value[0].text + '-' + e.detail.value[1].text + '-' + e.detail
					.value[2].text
				this.$nextTick(() => {
					this.formData.recipientAreaCode = e.detail.value[2].value
				})

				console.log('地区赋值=======>>', this.formData.addressDetail)
			},
			//获取微信地址信息
			autoFill() {
				console.log('微信调用地址1')
				uni.authorize({
					scope: 'scope.address',
					success: () => {
						console.log('success')
						this.getWxAddress()
					},
					fail: () => {
						console.log('fail')
					},
					complete() {
						console.log('complete')
					}
				})
			},
			getWxAddress() {
				uni.chooseAddress({
					success: user => {
						if (user.provinceName != '广西壮族自治区') {
							uni.showModal({
								title: '提示',
								content: '业务试行阶段，为不影响您的体验，暂无法处理省外订单，请您谅解',
								showCancel: false
							});
							return
						}
						//微信获取地址信息赋值
						this.formData.recipient = user.userName
						this.formData.recipientPhone = user.telNumber
						this.formData.recipientAreaName = user.provinceName + '-' + user.cityName + '-' + user
							.countyName
						this.formData.recipientAddress = user.detailInfo
						this.$nextTick(() => {
							this.formData.recipientAreaCode = user.nationalCode
							// this.$refs.picker.clear()
						})

						console.log('this.formData', this.formData)
					}
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
	};
</script>

<style lang="scss" scoped>
	$uploadWidth: 155rpx;
	$uploadHeight: 114rpx;

	.list-item_attachment {
		width: calc(100% - 160rpx);
		overflow: hidden;
		margin-left: 8rpx;
	}

	.upload-from {
		background-color: #FFFFFF;
		// padding-bottom: 16rpx;
		// margin-top: 16rpx;
		width: 100%;
		overflow: hidden;
	}

	.upload {
		width: 31%;
		float: left;
		margin-right: 10rpx;
	}

	.upload {
		width: $uploadWidth;
	}

	.upload-wrap {
		// margin-bottom: 20rpx;
	}

	.upload-wrap .upload-wrap-desc {
		font-size: 26rpx;
		text-align: center;
		width: 100%;
		color: #333333;
		font-weight: 400;
		margin-top: 26rpx;
	}

	.upload-wrap .upload-wrap-bd {
		width: $uploadWidth;
		height: $uploadHeight;
		position: relative;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__img {
		width: $uploadWidth;
		height: $uploadHeight;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__close {
		position: absolute;
		right: 0;
		top: 0;
		padding: 0 10rpx;
		font-size: 36rpx;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
		width: 30rpx;
		height: 30rpx;
		background-size: 100%;
	}

	.apply-after-sale {
		background: #F8F8F8;
		padding-bottom: 180rpx;
		position: relative;
		height: 100%;

		.content {
			height: calc(100% - 140rpx);
			overflow-y: scroll;
		}

		.weui-bottom-fixed {
			position: fixed;
			bottom: 0;
		}

		.bottom-box {
			display: flex;
		}

		.bottom-box .btn-item {
			flex: 1;
		}

		.steps-wrapp {
			background-color: #fff;
			margin-bottom: 20rpx;
			padding-top: 20rpx;
			height: 150rpx;
		}


		.list {
			padding-bottom: 50rpx;

			.list-item {
				display: flex;
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.list-item_label {
					font-size: 26rpx;
					width: 150rpx;
					margin-right: 10rpx;
					font-weight: 400;
					color: #999999;
				}

				.list-item_value {
					font-size: 26rpx;
					font-weight: 400;
					color: #333333;
				}

				.radio {
					display: flex;
					align-items: center;
					margin-bottom: 25rpx;

					.round {
						width: 26rpx;
						height: 26rpx;
						border-radius: 50%;
						border: 1rpx solid #A8A8A8;
					}

					.check-round {
						border: 1rpx solid #0066E9;
						background-color: #0066E9;
						position: relative;
					}

					.check-round::before {
						font-family: "cuIcon";
						content: "\e645";
						position: absolute;
						color: #fff;
						top: 0px;
						right: 1px;
						font-size: 19rpx;
						line-height: 12px;
					}

					.value {
						font-size: 26rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #333333;
						line-height: 40rpx;
						margin-left: 10rpx;
					}
				}

				.money {
					height: 26rpx;
					font-size: 26rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #F65B5B !important;
					line-height: 26rpx;
				}

				.list-item-textaxea {
					width: calc(100% - 160rpx);
					height: 116rpx;
					background: #FFFFFF;
					border-radius: 8rpx;
					border: 2rpx solid #DDDDDD;
					line-height: 64rpx;
					overflow-y: scroll;

					textarea {
						width: calc(100% - 36rpx);
						margin: auto;
						height: 100%;
					}
				}
			}
		}

		.order-address__detail {
			.address-wrapper {
				padding-bottom: 50rpx;
				background-color: #ffffff;
				border-radius: 12rpx;
			}

			.title-wrapper {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 30rpx 0 30rpx;

				.title {
					font-size: 30rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #323435;
				}
			}

			.wx-location {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #0081FF;
			}

			/deep/.uni-data-tree {
				width: 100%;
				line-height: 66rpx;
			}

			/deep/.uni-data-tree-dialog {
				z-index: 1002;
			}


			.install {
				margin: 20rpx 20rpx 0;
				background-color: $uni-bg-color;
				// padding-bottom: 10rpx;
				border-radius: 12rpx;

				.title-container {
					padding: 30rpx;
				}

				.input-wrapper {
					padding: 0 10rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 80rpx;
					line-height: 40rpx;
					color: rgba(16, 16, 16, 100);
					background-color: $uni-bg-color;
					font-size: 26rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;

					.input-title {
						flex: 0 0 180rpx;
						width: 180rpx;
						color: #323435;
						// text-align: left;
					}

					.input {
						flex: 1;
						// width: calc(100% - 180rpx);
						// justify-content: flex-end;
						// text-align: left;
						height: 60rpx;
						background: #fff;
						border-radius: 6rpx;
						font-size: 26rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #333333;
						padding-left: 20rpx;
						border: 2rpx solid #DDDDDD;
						position: relative;

						/deep/.input-value-border {
							border: none;
							line-height: 30px;
							color: #333;
							padding: 0px !important
						}

						/deep/.selected-list {
							padding: 0px !important
						}

						/deep/.selected-item {
							padding: 0px !important
						}

						/deep/.icon-clear {
							display: none;
						}

						/deep/.arrow-area {
							display: none;
						}
					}

					.location-img {
						position: absolute;
						right: 18rpx;
						width: 30rpx;
						height: 31rpx;
						background-size: 100%;
						top: 17rpx;
					}

					.selected-item {
						color: #333333;
					}

					/deep/.picker {
						height: 100%;
						width: auto;
					}

					.auto-input {
						position: relative;
					}

				}
			}
		}

	}

	.active-sms__dialog {
		.login-form-group .code-img {
			width: 240upx;
			height: 90upx;
			margin-left: 20upx;
		}

		.sendSMS {
			padding: 10rpx;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.desc {
			font-size: 30rpx;
			text-align: left;
			font-weight: 400;
			color: #858686;
			background: #ffffff;
			padding: 10upx 25upx;
		}

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
		}
	}

	.active-sms__dialog .cu-form-group .title {
		font-size: 32upx;
	}

	.active-sms__dialog .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.active-sms__dialog .cu-form-group input {
		text-align: left;
	}

	.active-sms__dialog .cu-form-group radio-group {
		flex: 1;
		text-align: left;
	}
</style>