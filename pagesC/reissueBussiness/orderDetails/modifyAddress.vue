<template>
	<view class="apply-after-sale">
		<view class="apply-after-sale-content">
			<!-- 收货地址 -->
			<view class="section">
				<view class="order-address__detail">
					<view class="address-wrapper">
						<view class="title-wrapper">
							<view class="title">
								收货地址
							</view>
							<view class="wx-location" @click="autoFill">
								获取微信地址
							</view>
						</view>
						<view class="install">
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>收货人
								</view>
								<input class="input auto-input" v-model="result.recipient" placeholder="请输入收货人"
									type="text">
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>手机号码
								</view>
								<input class="input" placeholder="请输入收货人手机号码" type="number" maxlength="11"
									v-model="result.recipientPhone">
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>所在地区
								</view>
								<view class="input">
									<uni-data-picker ref="picker" placeholder="选择区域(仅支持广西区内)" :localdata="regionList"
										v-model="result.recipientAreaCode" @change="onchange"
										@popupopened="onpopupopened" @popupclosed="onpopupclosed">
									</uni-data-picker>
									<image src="../../static/location.png" mode="" class="location-icon">
									</image>
								</view>
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>详细地址
								</view>
								<input class="input auto-input" v-model="result.recipientAddress" placeholder="请输入详细地址"
									type="text">
							</view>
						</view>
					</view>
				</view>

			</view>
			<!-- 待审核 审核不通过  -->
			<view class="weui-bottom-fixed">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="submit">
							提交
						</button>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import uniDataPicker from '../../components/uni-data-picker/uni-data-picker.vue'
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
	} from '@/common/method/filter.js';
	import util from "@/common/util.js"
	import {
		getLoginUserInfo
	} from '@/common/storageUtil.js'
	export default {
		components: {
			tLoading,
			uniDataPicker,
		},
		data() {
			return {
				isLoading: false,
				regionList: [],
				result: {
					orderId: '',
					recipient: '',
					recipientPhone: '',
					recipientAreaCode: '',
					recipientAreaName: '',
					recipientAddress: '',
				},
			}
		},
		created() {
			this.getRegion();
		},
		onLoad(options) {
			this.result = JSON.parse(options.result)
		},
		methods: {
			getRegion() {
				let params = {};
				this.$request.post(this.$interfaces.getRegion, {
					data: params
				}).then(res => {
					let list = [];
					if (res.code == 200) {
						list = res.data;
						for (let i = 0; i < list.length; i++) {
							list[i].children = list[i].child;
							list[i].text = list[i].label;
							for (let j = 0; j < list[i].children.length; j++) {
								list[i].children[j].children = list[i].children[j].child;
								list[i].children[j].text = list[i].children[j].label;
								for (let k = 0; k < list[i].children[j].children.length; k++) {
									list[i].children[j].children[k].children = list[i].children[j].children[k]
										.child;
									list[i].children[j].children[k].text = list[i].children[j].children[k].label;
								}
							}
						}
					}

					let gxArrList = list.filter(item => {
						return item.value == '450000'
					})
					//限制广西区域
					this.regionList = gxArrList;
					// this.regionList = list
				})
			},
			onpopupopened() {},
			onpopupclosed() {},
			//获取微信地址信息
			autoFill() {
				uni.authorize({
					scope: 'scope.address',
					success: () => {
						this.getWxAddress()
					},
					fail: () => {},
					complete() {}
				})
			},
			getWxAddress() {
				uni.chooseAddress({
					success: user => {
						if (user.provinceName != '广西壮族自治区') {
							uni.showModal({
								title: '提示',
								content: '业务试行阶段，为不影响您的体验，暂无法处理省外订单，请您谅解',
								showCancel: false
							});
							return
						}
						//微信获取地址信息赋值
						this.$nextTick(() => {
							this.result.recipient = user.userName
							this.result.recipientPhone = user.telNumber
							this.result.recipientAreaCode = user.nationalCode
							this.result.recipientAreaName = user.provinceName + '-' + user.cityName +
								'-' + user
								.countyName
							this.result.recipientAddress = user.detailInfo
							// this.$refs.picker.clear()
						})
					}
				})
			},
			onchange(e) {
				if (e.detail.value.length == 0) return;
				this.$nextTick(() => {
					this.result.recipientAreaName = e.detail.value[0].text + '-' + e.detail.value[1].text + '-' + e
						.detail.value[2].text
					this.result.recipientAreaCode = e.detail.value[2].value
				})
			},
			validateHandle() {
				let msg = ''
				if (!this.result.recipientAddress) {
					msg = '您的详细地址未填写，请先填写'
				}
				if (!this.result.recipientAreaCode) {
					msg = '您的所在地区未选择，请先选择'
				}
				if (!util.checkPhone(this.result.recipientPhone)) {
					msg = "请输入正确的手机号码"
				}
				if (!this.result.recipientPhone) {
					msg = '您的手机号码未填写，请先填写'
				}
				if (!this.result.recipient) {
					msg = '您的收货人未填写，请先填写'
				}
				if (msg) return msg;
				return ''
			},
			submit() {
				let msg = this.validateHandle();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				if (this.isLoading) return
				this.isLoading = true
				let params = {
					orderId: this.result.orderId,
					userNo: getLoginUserInfo().userNo,
					...this.result
				}
				this.$request.post(this.$interfaces.editAddress, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let pages = getCurrentPages(); //获取所有页面栈实例列表
						let prevPage = pages[pages.length - 2]; //上一页页面实例
						prevPage.$vm.addressInfo = this.result; //修改上一页面的收货地址
						prevPage.$vm.getOrderDetail()
						uni.navigateBack({
							delta: 1
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	.apply-after-sale {
		background: #F8F8F8;
		margin-bottom: 180rpx;
		position: relative;
		height: 100%;
	}

	.apply-after-sale-content {
		height: calc(100% - 240rpx);
		overflow-y: scroll;
	}

	.bottom-box {
		display: flex;

		.btn-item {
			flex: 1;
		}

		.btn-item-left {
			margin-right: 32rpx;
		}

		.btn-item-right {}
	}

	.order-address__detail {
		// padding: 20rpx;
	}

	.weui-card-bd {
		display: flex;
		width: 100%;
	}

	.vehicle-info {
		padding-bottom: 34rpx !important;
	}

	.list {
		width: calc(100% - 50rpx);
	}

	.list-item {

		.list-item_label,
		.list-item_value {
			width: auto !important;
		}

		.list-item_value {}
	}

	.order-info {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding-bottom: 20rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}

	.status-value.info {
		background: #F6F6F6;
		color: #333333;
	}


	.status-value.success {
		background: rgba(0, 189, 50, 0.1);
		color: #00BD32;
	}

	.status-value.warnning {
		background: rgba(255, 145, 0, 0.1);
		color: #FF9100;
	}

	.status-value.error {
		background: rgba(255, 84, 84, 0.1);
		color: #FF5454;
	}

	.address-wrapper {
		// margin: 20rpx;
		padding-bottom: 50rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
	}

	.title-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx 30rpx 0 30rpx;

		.title {
			font-size: 30rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #323435;
		}
	}

	.wx-location {
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #0081FF;
	}

	.ios-textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-top: 45rpx;
	}

	.textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-bottom: 38rpx;
	}

	.picker {
		width: auto;
		flex: 1;
		height: 100%;
		display: flex;
		align-items: center;
		color: #777777;

		// line-height: 120rpx;
		// text-align: center;
	}

	/deep/.uni-data-tree {
		width: 100%;
		line-height: 66rpx;
	}

	/deep/.uni-data-tree-dialog {
		z-index: 1002;
	}


	.install {
		margin: 20rpx 20rpx 0;
		background-color: $uni-bg-color;
		// padding-bottom: 10rpx;
		border-radius: 12rpx;

		.title-container {
			padding: 30rpx;
		}

		.input-wrapper {
			padding: 0 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 80rpx;
			line-height: 40rpx;
			color: rgba(16, 16, 16, 100);
			background-color: $uni-bg-color;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #999999;

			.input-title {
				flex: 0 0 180rpx;
				width: 180rpx;
				color: #323435;
				// text-align: left;
			}

			.input {
				flex: 1;
				height: 64rpx;
				background: #fff;
				border-radius: 6rpx;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				padding-left: 20rpx;
				border: 2rpx solid #DDDDDD;
				position: relative;

				/deep/.input-value-border {
					border: none;
					line-height: 30px;
					color: #333;
					padding: 0px !important
				}

				/deep/.selected-list {
					padding: 0px !important
				}

				/deep/.selected-item {
					padding: 0px !important
				}

				/deep/.icon-clear {
					display: none;
				}

				/deep/.arrow-area {
					display: none;
				}
			}

			.location-icon {
				position: absolute;
				right: 18rpx;
				width: 30rpx;
				height: 31rpx;
				background-size: 100%;
				top: 17rpx;
			}

			.selected-item {
				color: #333333;
			}

			/deep/.picker {
				height: 100%;
				width: auto;
			}

			.auto-input {
				position: relative;
			}

		}
	}
</style>