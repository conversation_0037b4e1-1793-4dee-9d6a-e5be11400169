<template>
	<view class="apply-after-sale" v-if="finish">
		<orderNo :result="orderDetail"></orderNo>
		<view class="apply-after-sale-content" :class="showNoBtn?'apply-after-sale-content-no-btn':''">
			<view class="section">
				<orderProgress v-if="Object.keys(orderDetail).length > 0" @click="viewProgress" :result="orderDetail">
				</orderProgress>
			</view>
			<!-- 车辆信息 -->
			<view class="section" v-if="showVehicle">
				<vehicle :vehicleObj="orderDetail"></vehicle>
			</view>
			<!-- 物流信息 -->
			<view class="section" v-if="showLogisticsInfo">
				<orderWl @click="toWl" :result="sfRouteResInfoDTO"></orderWl>
			</view>
			<!-- 提交材料 -->
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">提交材料</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list vehicle-info">
							<view class="list-item align-items-center">
								<view class="list-item_label">
									补办设备
								</view>
								<view class="list-item_value">
									{{orderDetail.deviceTypeName }}
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label">
									补办原因
								</view>
								<view class="list-item_value">
									{{orderDetail.reasonTypeName || ''}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 收货地址 -->
			<view class="section"
				v-if="orderDetail.status == '301' || orderDetail.status == '302' || orderDetail.status == '303' || orderDetail.status=='307'">
				<view class="order-address__detail">
					<view class="weui-card form-item">
						<tTitle title="收货地址" setPadding="30" @click="modifyAddress()" v-if="orderDetail.status!='307'">
						</tTitle>
						<view class="weui-card-hd" v-if="orderDetail.status=='307'">
							<view class="weui-card-hd-wrapper">
								<view class="weui-card-hd-title">收货地址</view>
							</view>
						</view>
						<view class="weui-card-bd" style="display: flex;width: 100%;">
							<view class="location">
								<image src="../../static/location_icon.png" mode="" class="location-icon"></image>
							</view>
							<view class="list vehicle-info">
								<view class="list-item">
									<view class="list-item_label recipient-name">
										地址：
									</view>
									<view class="list-item_value recipient">
										{{addressInfo.recipientAreaNameStr}}{{addressInfo.recipientAddress}}
									</view>
								</view>
								<view class="list-item">
									<view class="list-item_label recipient-name">
										姓名：
									</view>
									<view class="list-item_value recipient">
										{{addressInfo.recipient}} {{addressInfo.recipientPhone}}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="section" v-if="orderDetail.status == '301'">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">支付方式</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="weui-card-bd-pay">
								微信支付
								<image src="../../static/<EMAIL>" mode="" class="ico_filter_down">
								</image>
							</view>
							<view class="list-item align-items-center" style="margin-top: 30rpx;padding-bottom:43rpx">
								<view class="list-item_label">
									办理费用
								</view>
								<view class="list-item_value money">
									{{moneyFilter(orderDetail.payAmount)}}元
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 待支付 -->
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '301'">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item btn-item-left">
						<button class="weui-btn weui-btn_primary" @click="confirmPay">
							确认支付
						</button>
					</view>
					<view class="btn-item btn-item-right"><button class="weui-btn weui-btn_quit" @click="cancel">
							取消订单
						</button>
					</view>
				</view>
			</view>
			<!-- 待补办 -->
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '302'">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="cancel">
							取消订单
						</button>
					</view>
				</view>
			</view>
			<!-- 设备已寄出 -->
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '304' ">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="confirmSign(orderDetail)">
							确认签收
						</button>
					</view>
				</view>
			</view>
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '305' ">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="activate(orderDetail)">
							前往激活
						</button>
					</view>
				</view>
			</view>
		</view>
		<u-modal v-model="uShow" @confirm="uShowConfirm" confirm-text="前往查看" :content="limitContent" ref="uModal"
			:async-close="true">
		</u-modal>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getCurrUserInfo,
		getLoginUserInfo,
		getOpenid,
		setOpenid
	} from "@/common/storageUtil.js";
	import orderProgress from '../../components/order-progress/orderProgress.vue';
	import vehicle from '../../components/vehicle/vehicle.vue';
	import orderNo from '../../components/order-no/orderNo.vue'
	import orderWl from '@/pagesC/changeBussiness/orderWl/orderWl.vue'
	import tTitle from '../../components/new-title/new-title.vue';
	export default {
		components: {
			tLoading,
			orderNo,
			orderProgress,
			vehicle,
			orderWl,
			tTitle,
		},
		data() {
			return {
				finish: false,
				showNoBtn: false,
				isLoading: false,
				orderId: '',
				orderDetail: {
					id: '',
					type: '', //0-补领；1-更换；
					deviceType: '',
					deviceTypeName: '',
					status: '',
					statusName: '',
					carNo: '',
					carColor: '',
					cardNo: '',
					obuNo: '',
					reason: '',
					payAmount: '',
					recipient: '',
					recipientPhone: '',
					recipientAreaCode: '',
					recipientAreaName: '',
					recipientAddress: '',
					expressCompanyCode: '',
					expressCompanyName: '',
					expressNumber: '',
				},
				cardType: '', //卡片类型
				sfRouteResInfoDTO: {
					expressCompanyName: '',
					expressNumber: '',
					recipientAreaName: '',
					recipientAddress: '',
					sfRouteResInfos: {}
				},
				showLogisticsInfo: false,
				addressInfo: {
					orderId: '',
					recipient: '',
					recipientPhone: '',
					recipientAreaCode: '',
					recipientAreaName: '',
					recipientAddress: '',
					recipientAreaNameStr: '',
				},
				showVehicle: false,
				//支付成功弹框提示
				uShow: false,
				limitContent: '',
				openId: ''
			};
		},
		onLoad(options) {
			this.openId = getOpenid() || ''
			console.log(options)
			this.orderId = options.orderId
		},
		onShow() {
			if (this.orderId) {
				this.getOrderDetail()
			}
		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
		},
		methods: {
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			getOrderDetail() {
				let params = {
					orderId: this.orderId,
					userNo: getLoginUserInfo().userNo,
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.reissueOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log('------', res.data)
					if (res.code == 200) {
						this.getVehicleInfo(res.data.carNo, res.data.carColor)
						this.orderDetail = res.data
						this.showNoBtn = (this.orderDetail.status == '301' || this.orderDetail.status == '302' ||
								this.orderDetail.status == '304' || this.orderDetail.status == '305') ? false :
							true
						let expressOneInfo = res.data.expressInfo.filter(item => item.expressType == '0')[0] //物流信息
						if (res.data.status == '304' || res.data.status == '305' || res.data.status == '306') {
							this.getExpress(expressOneInfo)
						}
						for (let key in this.addressInfo) {
							this.addressInfo[key] = expressOneInfo[key]
						}
						this.addressInfo.orderId = this.orderDetail.orderId
						this.addressInfo.recipientAreaNameStr = this.addressInfo.recipientAreaName ? this
							.addressInfo.recipientAreaName.replaceAll('-', '') : ''

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//查询车辆信息
			getVehicleInfo(carNo, carColor) {
				let params = {
					vehicle_code: carNo,
					vehicle_color: carColor
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.queryVehicleInfo, {
					data: params
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						console.log(res)
						this.orderDetail.obuNo = res.data[0].obu_id
						this.orderDetail.cardNo = res.data[0].cpu_card_id
						this.cardType = res.data[0].card_type
						this.showVehicle = true
						this.finish = true
					}
				})
			},
			// 物流信息
			getExpress(expressOneInfo) {
				this.isLoading = true
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId,
					expressNumber: expressOneInfo.expressNumber
				}
				this.$request.post(this.$interfaces.exchangeExpress, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						// res.data = [{
						// 	"acceptAddress": "南宁市",
						// 	"acceptTime": "2022-05-25 11:28:31",
						// 	"opCode": "60",
						// 	"opName": "已揽件",
						// 	"remark": "顺丰速运 已收取快件"
						// }, {
						// 	"acceptAddress": "南宁市",
						// 	"acceptTime": "2022-05-25 11:28:31",
						// 	"opCode": "60",
						// 	"opName": "已揽件",
						// 	"remark": "顺丰速运 已收取快件"
						// }]
						for (let key in this.sfRouteResInfoDTO) {
							this.sfRouteResInfoDTO[key] = expressOneInfo[key]
						}
						this.sfRouteResInfoDTO.sfRouteResInfos = res.data
						this.showLogisticsInfo = true
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			viewProgress() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderProgress/index?orderId=' + this.orderId
				})
			},
			modifyAddress() {
				uni.navigateTo({
					url: '/pagesC/reissueBussiness/orderDetails/modifyAddress?result=' + JSON
						.stringify(this.addressInfo)
				})
			},
			//确认支付
			confirmPay() {
				let _self = this;
				this.isLoading = true;
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId,
					openId: this.openId,
					payType: '10000601',
				}
				let data = {
					routePath: this.$interfaces.exchangePay.method,
					bizContent: params
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;
						if (res.code == 200) {
							let result = res.data;
							let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};

							wx.requestPayment({
								...payMessage,
								success: function(successres) {
									_self.isLoading = true
									setTimeout(() => {
										_self.applyPayOrderQuery(result);
									}, 6000);
								},
								fail: function(err) {
									_self.updatePayStatus(result, 3)
								},
							});
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			applyPayOrderQuery(data) {
				let params = {
					userNo: getLoginUserInfo().userNo,
					payOrderId: data.payOrderId,
				}
				this.$request.post(this.$interfaces.exchangeQueryPay, {
					data: params
				}).then(res => {
					this.isLoading = false;
					console.log('data===============>>>>>>>>>>', res)
					let status = res.data.status;
					let statusVal = {
						1: "支付中",
						2: "支付成功",
						3: "支付失败",
					};
					let msg = statusVal[status] || "支付中";

					if (status == 2) {
						//支付成功弹框后，跳转成功页面
						this.uShow = true
						this.limitContent = msg
					} else {
						//支付中、支付失败时只提示,留在当前页面不操作
						uni.showModal({
							title: '支付提示',
							content: msg,
							showCancel: false
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			uShowConfirm() {
				this.uShow = false
				uni.redirectTo({
					url: '/pagesC/afterSaleBusiness/promptPage/index?type=11&orderId=' + this.orderId
				})
			},
			updatePayStatus(result, status) {
				let params = {
					payOrderId: result.payOrderId,
					status: status,
					payBusinessType: 3
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.exchangeUpdatePayStatus, {
					data: params
				}).then(res => {

				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			//确认签收
			confirmSign(item) {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确定收货？',
					confirmText: '确定',
					showCancel: true,
					success(action) {
						if (action.confirm) {
							that.confirmGoods(item)
						} else if (action.cancel) {}
					}
				})
			},
			confirmGoods(item) {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: item.orderId
				}
				this.$request.post(this.$interfaces.confirmGoods, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						//签收后需要激活
						let vehicleInfo = {}
						vehicleInfo.customerId = getCurrUserInfo().customer_id;
						vehicleInfo.vehicleCode = item.carNo;
						vehicleInfo.vehicleColor = item.carColor;
						vehicleInfo.businessSource = 4; //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
						vehicleInfo.orderId = item.orderId
						vehicleInfo.cardNo = item.oldCardNo
						vehicleInfo.obuNo = item.oldObuNo
						vehicleInfo.gxCardType = item.cardProduct
						vehicleInfo.deviceType = item.deviceType
						//新增补办原因与卡片类型
						vehicleInfo.reason = item.reasonTypeName
						vehicleInfo.cardType = this.cardType
						//按类型区别OBU更换前校验字段
						if (item.deviceType == '1' || item.deviceType == '2') {
							//补办的时候，类型1、2包含OBU
							//损坏类型: 0-非人为损坏;1-人为损坏 --默认为人为损坏1
							vehicleInfo.artificial = '1'
							//处理方式: 0-报废;1-回收  --默认处理方式报废0				
							vehicleInfo.dealType = '0'
							//有无旧设备: 1-有 2-无  --默认2
							vehicleInfo.returnOld = '2'
						}

						this.$store.dispatch(
							'setIssueVehicleInfo',
							vehicleInfo
						)
						if (item.deviceType == '0') { //卡没有Obu
							uni.redirectTo({
								url: "/pagesC/afterSaleBusiness/promptPage/index?type=9&deviceType=" +
									item.deviceType
							})
						} else {
							uni.redirectTo({
								url: "/pagesC/afterSaleBusiness/promptPage/index?type=8&deviceType=" +
									item.deviceType
							})
						}

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})

			},
			cancel() {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确定要取消补办申请吗',
					confirmText: '确定',
					showCancel: true,
					success(action) {
						if (action.confirm) {
							that.applyCancel()
						} else if (action.cancel) {}
					}
				})
			},
			applyCancel() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId
				}
				this.$request.post(this.$interfaces.reissueCancel, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=7"
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			activate(item) {
				console.log('设备补办item=>>>>>>', item)

				let vehicleInfo = {}
				vehicleInfo.customerId = getCurrUserInfo().customer_id;
				vehicleInfo.vehicleCode = item.carNo;
				vehicleInfo.vehicleColor = item.carColor;
				vehicleInfo.businessSource = 4; //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
				vehicleInfo.orderId = item.orderId
				vehicleInfo.cardNo = item.cardNo
				vehicleInfo.obuNo = item.obuNo
				vehicleInfo.gxCardType = item.cardProduct
				vehicleInfo.deviceType = item.deviceType
				//新增补办原因与卡片类型
				vehicleInfo.reason = item.reasonTypeName
				vehicleInfo.cardType = this.cardType
				//按类型区别OBU更换前校验字段
				if (item.deviceType == '1' || item.deviceType == '2') {
					//补办的时候，类型1、2包含OBU
					//损坏类型: 0-非人为损坏;1-人为损坏 --默认为人为损坏1
					vehicleInfo.artificial = '1'
					//处理方式: 0-报废;1-回收  --默认处理方式报废0				
					vehicleInfo.dealType = '0'
					//有无旧设备: 1-有 2-无  --默认2
					vehicleInfo.returnOld = '2'
				}

				this.$store.dispatch(
					'setIssueVehicleInfo',
					vehicleInfo
				)
				if (item.deviceType == '0') {
					//补办卡激活 直接连接蓝牙开始激活
					uni.navigateTo({
						url: '/pagesA/newBusiness/issue/issue-confirm'
					})
					return
				}
				uni.navigateTo({
					url: '/pagesA/newBusiness/issue/issue-install?activationType=2'
				})
			},
			toWl() {
				let sfRouteResInfoDTO = {
					expressCompanyName: this.sfRouteResInfoDTO.expressCompanyName,
					expressNumber: this.sfRouteResInfoDTO.expressNumber,
					recipientAreaName: this.sfRouteResInfoDTO.recipientAreaName,
					recipientAddress: this.sfRouteResInfoDTO.recipientAddress,
				}
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderWl/orderWlDetail?orderId=' + this.orderId + '&result=' +
						JSON.stringify(sfRouteResInfoDTO)
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
	};
</script>

<style lang="scss" scoped>
	$uploadWidth: 155rpx;
	$uploadHeight: 114rpx;


	.apply-after-sale {
		background: #F8F8F8;
		margin-bottom: 180rpx;
		position: relative;
		height: 100%;

		.apply-after-sale-content {
			height: calc(100% - 240rpx);
			overflow-y: scroll;
		}

		.apply-after-sale-content-no-btn {
			height: calc(100% - 68rpx);
			overflow-y: scroll;
		}

		.money {
			height: 26rpx;
			font-size: 26rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #F65B5B !important;
			line-height: 26rpx;
		}

		.bottom-box {
			display: flex;

			.btn-item {
				flex: 1;
			}

			.btn-item-left {
				margin-right: 32rpx;
			}

			.btn-item-right {}
		}

		.steps-wrapp {
			background-color: #fff;
			margin-bottom: 20rpx;
			height: 100rpx;
		}

		.form-item {
			border-radius: 10rpx;
			// margin: 20rpx;
		}

	}

	.u-textarea__count {
		position: absolute;
		right: 5px;
		bottom: 2px;
		font-size: 12px;
		color: #909193;
		background-color: #fff;
		padding: 1px 4px;
	}
</style>