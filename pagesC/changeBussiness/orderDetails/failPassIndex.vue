<template>
	<view class="apply-after-sale" v-if="finish">
		<orderNo :result="orderDetail"></orderNo>
		<view class="apply-after-sale-content">
			<view class="section">
				<orderProgress v-if="Object.keys(orderDetail).length > 0" @click="viewProgress(orderDetail)"
					:result="orderDetail">
				</orderProgress>
			</view>
			<!-- 车辆信息 -->
			<view class="section" v-if="showVehicle">
				<vehicle :vehicleObj="orderDetail" :showCarColor="true"></vehicle>
			</view>
			<!-- 收货地址 -->
			<view class="section">
				<view class="order-address__detail">
					<view class="weui-card form-item">
						<tTitle title="收货地址" setPadding="30" @click="modifyAddress()">
						</tTitle>
						<view class="weui-card-bd" style="display: flex;width: 100%;">
							<view class="location">
								<image src="../../static/location_icon.png" mode="" class="location-icon"></image>
							</view>
							<view class="list vehicle-info">
								<view class="list-item">
									<view class="list-item_label recipient-name">
										地址：
									</view>
									<view class="list-item_value recipient">
										{{addressInfo.recipientAreaNameStr}}{{addressInfo.recipientAddress}}
									</view>
								</view>
								<view class="list-item">
									<view class="list-item_label recipient-name">
										姓名：
									</view>
									<view class="list-item_value recipient">
										{{addressInfo.recipient}} {{addressInfo.recipientPhone}}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 提交材料 -->
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">提交材料</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="list-item align-items-center">
								<view class="list-item_label">
									更换原因
								</view>
								<view class="list-item_value">
									{{orderDetail.reasonTypeName }}
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label" style="margin-right: 0;">
									设备照片
								</view>
								<view class="list-item_attachment">
									<view class="upload-from">
										<view class="upload" v-for="(item,index) in imgList" :key="index">
											<view class="upload-wrap">
												<view class="upload-wrap-bd">
													<image :src="item.fileUrl" class="upload-wrap__img"
														mode='aspectFilt'>
													</image>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 待审核 审核不通过  -->
			<view class="weui-bottom-fixed">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item btn-item-left" v-if="">
						<button class="weui-btn weui-btn_primary" @click="modifyInfo">
							修改资料
						</button>
					</view>
					<view class="btn-item btn-item-right"><button class="weui-btn weui-btn_quit" @click="cancelOrder">
							取消订单
						</button>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<u-select v-model="show" :list="applyReasonTypeList" @confirm="confirm">
		</u-select>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getCurrUserInfo,
		getLoginUserInfo
	} from "@/common/storageUtil.js";
	import orderProgress from '../../components/order-progress/orderProgress.vue';
	import vehicle from '../../components/vehicle/vehicle.vue';
	import orderNo from '../../components/order-no/orderNo.vue'
	import tTitle from '../../components/new-title/new-title.vue';
	export default {
		components: {
			tLoading,
			orderNo,
			orderProgress,
			vehicle,
			tTitle,
		},
		data() {
			return {
				finish: false,
				isLoading: false,
				ownFlag: 0,
				value: '',
				type: 'textarea',
				border: true,
				height: 100,
				sourceType: '0',
				currentFile: {},
				inputStyle: {
					"marginLeft": "12rpx",
					"fontSize": "26rpx",
					"color": "#333333",
					"lineHeight": '60rpx',
					"height": "60rpx"
				},
				show: false,
				applyReasonTypeList: [{
						label: '标签掉落',
						value: '0',
					},
					{
						label: '插卡显示标签失效/标签拆卸',
						value: '1',
					},
					{
						label: '其他（可备注详细原因，字数上限100）',
						value: '2',
					}
				],
				orderId: '',
				orderDetail: {
					netUserId: '',
					carNo: '',
					carColor: '',
					carColorName: '',
					obuNo: '',
					cardNo: '',
					reasonType: '',
					reasonTypeName: '',
					reason: '',
					recipient: '',
					recipientPhone: '',
					recipientAreaCode: '',
					recipientAreaName: '',
					recipientAddress: '',
				},
				imgList: [],
				isEditAddress: false, //修改收货地址
				isEdit: false, //修改提交资料
				addressInfo: {
					orderId: '',
					recipient: '', //收货人
					recipientPhone: '', //收货人手机号码
					recipientAreaCode: '', //省名
					recipientAreaName: '', //市名
					recipientAddress: '', //详细门牌街道信息,
					recipientAreaNameStr: '',
				},
				showVehicle: false
			};
		},
		created() {},
		onLoad(options) {
			console.log(options)
			this.orderId = options.orderId
		},
		onShow() {
			console.log('this.orderId', this.orderId)
			if (this.orderId) {
				this.getOrderDetail()
				this.getImgList() //获取图片列表
			}
		},
		methods: {
			getOrderDetail() {
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.reissueOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log('-----', res.data)
					if (res.code == 200) {
						this.finish = true
						this.orderDetail = res.data
						this.getVehicleInfo(res.data.carNo, res.data.carColor)
						let expressOneInfo = res.data.expressInfo.filter(item => item.expressType == '0')[0] //物流信息
						for (let key in this.addressInfo) {
							this.addressInfo[key] = expressOneInfo[key]
						}
						this.addressInfo.orderId = this.orderDetail.orderId
						this.addressInfo.recipientAreaNameStr = this.addressInfo.recipientAreaName ? this
							.addressInfo.recipientAreaName.replaceAll('-', '') : ''
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//查询车辆信息
			getVehicleInfo(carNo, carColor) {
				let params = {
					vehicle_code: carNo,
					vehicle_color: carColor
				}
				this.showVehicle = false
				this.isLoading = true
				this.$request.post(this.$interfaces.queryVehicleInfo, {
					data: params
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						console.log(res)
						this.orderDetail.obuNo = res.data[0].obu_id
						this.orderDetail.cardNo = res.data[0].cpu_card_id
						this.showVehicle = true
						this.finish = true
					}
				})
			},
			getImgList() {
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.exchangeImgList, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						this.imgList = res.data.imgList
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			confirm(options) {
				this.orderDetail.applyReasonType = options[0].value
				this.orderDetail.applyReasonTypeStr = options[0].label
			},
			modifyInfo() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderDetails/modifyData?orderId=' + this.orderId
				})
			},
			cancelOrder() {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确定要取消更换申请吗',
					confirmText: '确定',
					showCancel: true,
					success(action) {
						if (action.confirm) {
							that.applyCancel()
						} else if (action.cancel) {
							console.log('用户点击取消');
						}
					}
				})
			},
			applyCancel() {
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.reissueCancel, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=2&orderId=" +
								this.orderId
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			viewProgress() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderProgress/index?orderId=' + this.orderId
				})
			},
			modifyAddress() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderDetails/modifyDeliveryAddress?result=' + JSON
						.stringify(this.addressInfo)
				})
			},
		},
	};
</script>

<style lang="scss" scoped>
	$uploadWidth: 155rpx;
	$uploadHeight: 114rpx;


	.apply-after-sale {
		background: #F8F8F8;
		margin-bottom: 180rpx;
		position: relative;
		height: 100%;

		.apply-after-sale-content {
			height: calc(100% - 240rpx);
			overflow-y: scroll;
		}

		.list-item_attachment {
			width: calc(100% - 160rpx);
			overflow: hidden;
			margin-bottom: 20rpx;

			.upload-from {
				background-color: #FFFFFF;
				padding-bottom: 16rpx;
				margin-top: 16rpx;
				width: 100%;
				overflow: hidden;
			}

			.upload {
				width: 31%;
				float: left;
				margin-right: 10rpx;
			}

			.upload-wrap {
				margin-bottom: 20rpx;
			}

			.upload-wrap .upload-wrap-desc {
				font-size: 26rpx;
				text-align: center;
				width: 100%;
				color: #333333;
				font-weight: 400;
				margin-top: 26rpx;
			}

			.upload-wrap .upload-wrap-bd {
				width: $uploadWidth;
				height: $uploadHeight;
				position: relative;
				background-size: 100%;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__img {
				width: $uploadWidth;
				height: $uploadHeight;
				background-size: 100%;
				border-radius: 8rpx;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__close {
				position: absolute;
				right: 0;
				top: 0;
				padding: 0 10rpx;
				font-size: 36rpx;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
				// font-size: 36rpx;
				width: 28rpx;
				height: 28rpx;
				background-size: 100%;
			}



			.attachment_card {
				width: 50%;
				float: left;
				margin-bottom: 20rpx;

				.attachment_icon {
					width: 231rpx;
					height: 138rpx;
					background-size: 100%;
				}

				.attachment_name {
					width: 100%;
					height: 21rpx;
					font-size: 22rpx;
					font-weight: 400;
					color: #333333;
					line-height: 21rpx;
					margin-top: 18rpx;
					text-align: center;
				}
			}
		}

		.pay_money {
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #F65B5B !important;
		}

		.weui-card-bd-pay {
			width: calc(100% - 60rpx);
			height: 73rpx;
			background: #F0F0F0;
			border-radius: 6rpx;
			margin: 0 auto;
			text-align: center;
			line-height: 73rpx;
			position: relative;

			.ico_filter_down {
				width: 20rpx;
				height: 20rpx;
				position: absolute;
				right: 30rpx;
				top: 28rpx;
			}
		}

		.bottom-box {
			display: flex;

			.btn-item {
				flex: 1;
			}

			.btn-item-left {
				margin-right: 32rpx;
			}

			.btn-item-right {}
		}

		.steps-wrapp {
			background-color: #fff;
			margin-bottom: 20rpx;
			height: 100rpx;
		}

		.form-item {
			border-radius: 10rpx;
			// margin: 20rpx;
		}

	}

	.u-textarea__count {
		position: absolute;
		right: 5px;
		bottom: 2px;
		font-size: 12px;
		color: #909193;
		background-color: #fff;
		padding: 1px 4px;
	}
</style>