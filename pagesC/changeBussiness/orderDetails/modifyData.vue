<template>
	<view class="apply-after-sale">
		<view class="apply-after-sale-content">
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">提交材料</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="list-item align-items-center">
								<view class="list-item_label">
									<span style="color:red">*</span>更换原因
								</view>
								<view class="list-item_value list-item_view" @click="show=true">
									<view>{{formData.reasonTypeName}}</view>
									<image src="../../static/ico_filter_down.png" mode="" class="down-icon"></image>
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label">
									设备照片
								</view>
								<view class="list-item_attachment">
									<view class="upload-from">
										<view class="upload" v-for="(item,index) in imgList" :key="index">
											<view class="upload-wrap">
												<view class="upload-wrap-bd">
													<image :src="item.fileUrl" class="upload-wrap__img"
														mode='aspectFilt'>
													</image>
													<view class="upload-wrap__close" @tap.stop="delImgHandle(item)">
														<image src="../../static/close.png" mode="" class="close">
														</image>
													</view>
												</view>
											</view>
										</view>
										<view class="upload upload-add" v-if="imgList.length<3">
											<view class="upload-wrap">
												<view class="upload-wrap-bd" @tap="ChooseImage(item)">
													<image src="../../static/add_icon.png" class="upload-wrap__img">
													</image>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="list-item align-items-center">
								<view class="list-item_label">
									补充说明
								</view>
								<view class="list-item-textaxea">
									<textarea v-model="formData.reason"></textarea>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="weui-bottom-fixed">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="submit">
							提交
						</button>
					</view>
				</view>
			</view>
		</view>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
		<u-select v-model="show" :list="changeReasonList" @confirm="confirm">
		</u-select>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import cpimg from "@/components/uni-yasuo/cpimg.vue";
	import {
		getLoginUserInfo
	} from "@/common/storageUtil.js";
	export default {
		components: {
			tLoading,
			cpimg,
		},
		data() {
			return {
				formData: {
					reasonType: '',
					reasonTypeName: '',
					reason: '',
				},
				imgList: [],
				deleteImgList: [],
				sourceType: '101',
				show: false,
				changeReasonList: [{
						label: '没电，插卡无反应',
						value: '101',
					},
					{
						label: '过不了车道',
						value: '102',
					},
					{
						label: '插拔卡无反应',
						value: '103',
					},
					{
						label: '无法连接蓝牙',
						value: '104',
					},
					{
						label: '查看显示异常',
						value: '105',
					},
					{
						label: '其他',
						value: '106',
					}
				],
			}
		},
		onLoad(options) {
			this.orderId = options.orderId
			this.getImgList()
		},
		methods: {
			getImgList() {
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.exchangeImgList, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						this.imgList = res.data.imgList
						this.formData.reasonType = res.data.reasonType
						this.formData.reasonTypeName = res.data.reasonTypeName
						this.formData.reason = res.data.reason
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			ChooseImage() {
				if (this.imgList.length == 0) {
					this.sourceType = '101'
				}
				if (this.imgList.length == 1) {
					let photoCode = this.imgList[0].photoCode
					this.sourceType = photoCode == '101' ? '102' : '101'
				}
				if (this.imgList.length == 2) {
					let codeList = [this.imgList[0].photoCode, this.imgList[1].photoCode]
					this.sourceType = codeList.includes('101') ? (codeList.includes('102') ? '103' : '102') : '101'
				}
				this.$refs.cpimg._changImg(this.sourceType);
			},
			delImgHandle(obj, index) {
				// 根据item.key 决定删除片内容
				this.imgList = this.imgList.filter(item => item.photoCode != obj.photoCode)
				this.deleteImgList.push(obj)
			},
			////图片压缩成功
			cpimgOk(file) {
				this.sendUploadFile(file);
			},
			//图片压缩失败
			cpimgErr(e) {},
			sendUploadFile(file) {
				let params = {
					image: file.toString(),
					photoCode: this.sourceType
				};
				this.$request.post(this.$interfaces.cacheImgUplaod, {
					data: params
				}).then(res => {
					if (res.code == 200 && res.data && res.data.data) {
						let result = res.data.data;
						this.imgList.push(result)
					}
					this.isLoading = false;
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});

				})
			},
			confirm(options) {
				this.formData.reasonType = options[0].value
				this.formData.reasonTypeName = options[0].label
			},
			validateHandle() {
				let msg = ''
				if (this.imgList.length == 0) {
					msg = '您的设备照片未上传，请先上传'
				}
				if (!this.formData.reasonType) {
					msg = '您的更换原因未填写，请先填写'
				}
				if (msg) return msg;
				return ''
			},
			submit() {
				let msg = this.validateHandle();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				if (this.isLoading) return;
				this.isLoading = true;
				var imgArray = JSON.parse(JSON.stringify(this.imgList))
				for(let i in imgArray){
					imgArray[i].fileUrl = imgArray[i].code?imgArray[i].code:imgArray[i].fileUrl
				}
				let params = {
					userNo:getLoginUserInfo().userNo,
					orderId:this.orderId,
					reasonType:this.formData.reasonType,
					imgJsonList:imgArray,
					reason: this.formData.reason,
				}
				console.log(params)
				this.$request.post(this.$interfaces.exchangeEditImg, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=12&orderId=" +
								this.orderId
						})

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	$uploadWidth: 155rpx;
	$uploadHeight: 114rpx;

	.weui-card-bd {
		padding: 0 30rpx 39rpx !important
	}

	.list-item {
		margin-bottom: 30rpx !important;
	}

	.list-item_attachment {
		width: calc(100% - 160rpx);
		overflow: hidden;
		// margin-bottom: 20rpx;

		.upload-from {
			background-color: #FFFFFF;
			// padding-bottom: 16rpx;
			// margin-top: 16rpx;
			width: 100%;
			overflow: hidden;
		}

		.upload {
			width: 31%;
			float: left;
			margin-right: 10rpx;
		}

		.upload-wrap {
			// margin-bottom: 20rpx;
		}

		.upload-wrap .upload-wrap-desc {
			font-size: 26rpx;
			text-align: center;
			width: 100%;
			color: #333333;
			font-weight: 400;
			margin-top: 26rpx;
		}

		.upload-wrap .upload-wrap-bd {
			width: $uploadWidth;
			height: $uploadHeight;
			position: relative;
			background-size: 100%;
		}

		.upload-wrap .upload-wrap-bd .upload-wrap__img {
			width: $uploadWidth;
			height: $uploadHeight;
			background-size: 100%;
		}

		.upload-wrap .upload-wrap-bd .upload-wrap__close {
			position: absolute;
			right: 10rpx;
			top: 10rpx;
			// padding: 0 10rpx;
			// font-size: 36rpx;
		}

		.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
			// font-size: 36rpx;
			width: 28rpx;
			height: 28rpx;
			background-size: 100%;
		}



		.attachment_card {
			width: 50%;
			float: left;
			margin-bottom: 20rpx;

			.attachment_icon {
				width: 231rpx;
				height: 138rpx;
				background-size: 100%;
			}

			.attachment_name {
				width: 100%;
				height: 21rpx;
				font-size: 22rpx;
				font-weight: 400;
				color: #333333;
				line-height: 21rpx;
				margin-top: 18rpx;
				text-align: center;
			}
		}
	}


	.list-item-textaxea {
		width: calc(100% - 160rpx);
		height: 116rpx;
		background: #FFFFFF;
		border-radius: 8rpx;
		border: 2rpx solid #DDDDDD;
		line-height: 64rpx;
		overflow-y: scroll;

		textarea {
			width: calc(100% - 36rpx);
			margin: auto;
			height: 100%;
		}
	}

	.list-item_value_tips {
		height: 68rpx;
		width: calc(100% - 140rpx);
		border-radius: 8rpx;
		line-height: 68rpx;
		padding-left: 18rpx;
		display: flex;
		background: #F8F8F8;

		input {
			height: 100%;
			font-size: 26rpx;
			color: #323435;
			font-family: PingFangSC-Regular, PingFang SC;
		}
	}

	.btn-item {
		flex: 1;
	}
</style>