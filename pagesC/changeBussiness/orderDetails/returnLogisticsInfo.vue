<template>
	<view class="apply-after-sale">
		<view class="apply-after-sale-content">
			<view class="return-address">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">设备寄回地址</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list vehicle-info">
							<view class="list-item">
								<view class="list-item_label">
									收货人
								</view>
								<view class="list-item_value">
									{{result.recipient}}
								</view>
								<view class="copy" @click="copy(result.recipientAllInfo)">
									复制
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label">
									联系电话
								</view>
								<view class="list-item_value">
									{{result.recipientPhone}}
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label">
									地址
								</view>
								<view class="list-item_value">
									{{result.recipientAddress}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="send-logistics">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">设备寄回物流信息</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list vehicle-info">
							<view class="list-item align-items-center">
								<view class="list-item_label">
									<span style="color:red">*</span>物流单号
								</view>
								<view class="list-item_value list-item_value_ex" style="border: 2rpx solid #DDDDDD;">
									<u-input v-model="result.expressNumber" type="text" :border="true"
										:custom-style="inputStyle" :clearable="false" placeholder=" " />
									<image @click="scan" class="scan-img " src="../../static/icon-scan.png" mode="">
									</image>
								</view>
							</view>
							<view class="list-item align-items-center">
								<view class="list-item_label">
									<span style="color:red">*</span>物流公司
								</view>
								<view class="list-item_value list-item_view">
									<view style="height: 100%;"><input type="text" v-model="result.expressCompanyName"
											style="height: 100%;"></view>
									<image src="../../static/ico_filter_down.png" mode="" class="down-icon" @click.stop="show=true"></image>
								</view>
							</view>
						</view>
					</view>
				</view>
				<u-select v-model="show" :list="expressCompanyList" @confirm="confirmInfo">
				</u-select>
			</view>
			<view class="weui-bottom-fixed">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item btn-item-left">
						<button class="weui-btn weui-btn_quit" @click="modifyWay">
							修改寄件方式
						</button>
					</view>
					<view class="btn-item btn-item-right">
						<button class="weui-btn weui-btn_primary" @click="submit">
							提交
						</button>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import {
		getLoginUserInfo
	} from "@/common/storageUtil.js";
	import tLoading from '@/components/common/t-loading.vue';
	export default {
		data() {
			return {
				isLoading: false,
				orderId:'',
				result: {
					recipient: '',
					recipientPhone: '',
					recipientAddress:'',
					recipientAllInfo:'',
					expressNumber:'',
					expressCompanyName:''
				},
				formData:{},
				show: false,
				expressCompanyList: [{
						label: '顺丰',
						value: 'SF'
					},
					{
						label: '邮政特快专递',
						value: 'EMS'
					},
					{
						label: '中通',
						value: 'ZTO'
					}
				],
			}
		},
		onLoad(options) {
			this.formData = JSON.parse(options.addressInfo)
			this.orderId = this.formData.orderId
			this.getEtcAddress()
		},
		methods: {
			getEtcAddress() {
				this.isLoading = true
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId,
					type: 1
				}
				this.$request.post(this.$interfaces.getEtcAddress, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						for (let key in this.result) {
							this.result[key] = res.data[key]?res.data[key]:''
						}
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			copy(data) {
				uni.setClipboardData({
					data: data,
					success: function() {
						uni.showToast({
							title: "复制成功"
						})
					}
				});
			},
			scan() {
				let that = this
				wx.scanCode({
					success(res) {
						console.log(res)
						that.result.expressNumber = res.result
					}
				})
			},
			modifyWay() {
				uni.redirectTo({
					url: '/pagesC/changeBussiness/orderDetails/pickUp?addressInfo='+JSON.stringify(this.formData)
				})
			},
			confirmInfo(option) {
				this.result.expressCompanyName = option[0].label

			},
			validateHandle() {
				let msg = ''
				if (!this.result.expressCompanyName) {
					msg = '您的物流公司未选择，请先选择'
				}
				if (!this.result.expressNumber) {
					msg = '您的物流单号未填写，请先填写'
				}
				if (msg) return msg;
				return ''
			},
			submit() {
				let msg = this.validateHandle();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				this.isLoading = true
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId,
					expressNumber: this.result.expressNumber,
					expressCompanyName: this.result.expressCompanyName,
				}
				this.$request.post(this.$interfaces.exchangeSelfReturn, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=3&orderId=" +
								this.orderId
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.return-address {
		margin: 20rpx;

		.list {
			padding-bottom: 40rpx;
		}

		.list-item {
			position: relative;

			.copy {
				position: absolute;
				right: 0;
				width: 52rpx;
				height: 37rpx;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #3874FF;
				line-height: 37rpx;
			}
		}
	}

	.send-logistics {
		margin: 20rpx;

		.list-item_value {
			position: relative;
		}

		.scan-img {
			width: 30rpx;
			height: 30rpx;
			position: absolute;
			right: 18rpx;
			top: 20rpx;
			filter: grayscale(1);
		}

		.view-icon {
			width: 27rpx;
			height: 27rpx;
		}

	}

	.bottom-box {
		display: flex;

		.btn-item {
			flex: 1;
		}

		.btn-item-left {
			margin-right: 32rpx;
		}

		.btn-item-right {}
	}
</style>
