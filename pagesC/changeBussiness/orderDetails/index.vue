<template>
	<view class="apply-after-sale" v-if="finish">
		<orderNo :result="orderDetail"></orderNo>
		<view class="apply-after-sale-content" :class="showNoBtn?'apply-after-sale-content-no-btn':''">
			<view class="section">
				<orderProgress v-if="Object.keys(orderDetail).length > 0" @click="viewProgress" :result="orderDetail">
				</orderProgress>
			</view>
			<!-- 车辆信息 -->
			<view class="section" v-if="showVehicle">
				<vehicle :vehicleObj="orderDetail" :showCarColor="true"></vehicle>
			</view>
			<!-- 物流信息 -->
			<view class="section" v-if="showLogisticsInfo">
				<orderWl @click="toWl" :result="sfRouteResInfoDTO"></orderWl>
			</view>
			<!-- 更换信息 -->
			<view class="section"
				v-if="orderDetail.status=='206' || orderDetail.status=='207' || orderDetail.status=='208'|| orderDetail.status=='209'|| orderDetail.status=='210'|| orderDetail.status=='211'">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">更换信息</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list vehicle-info">
							<view class="list-item">
								<view class="list-item_label">
									设备类型
								</view>
								<view class="list-item_value">
									{{orderDetail.deviceTypeName}}
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label">
									支付金额
								</view>
								<view class="list-item_value pay_money">
									￥{{moneyFilter(orderDetail.payAmount || 0)}}元
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 支付方式 -->
			<view class="section" v-if="orderDetail.status == '206'">
				<view class="weui-card form-item" style="padding-bottom: 50rpx;">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">支付方式</view>
						</view>
					</view>
					<view class="weui-card-bd-pay">
						微信支付
						<image src="../../static/<EMAIL>" mode="" class="ico_filter_down"></image>
					</view>
				</view>
			</view>
			<!-- 设备寄回地址 -->
			<view class="section" v-if="showReturnAddress">
				<view class="order-address__detail">
					<view class="weui-card form-item">
						<tTitle title="收货地址" setPadding="30" @click="modifyAddress()">
						</tTitle>
						<view class="weui-card-bd" style="display: flex;width: 100%;">
							<view class="location">
								<image src="../../static/location_icon.png" mode="" class="location-icon"></image>
							</view>
							<view class="list vehicle-info">
								<view class="list-item">
									<view class="list-item_label recipient-name">
										地址：
									</view>
									<view class="list-item_value recipient">
										{{addressInfo.recipientAreaNameStr}}{{addressInfo.recipientAddress}}
									</view>
								</view>
								<view class="list-item">
									<view class="list-item_label recipient-name">
										姓名：
									</view>
									<view class="list-item_value recipient">
										{{addressInfo.recipient}}{{addressInfo.recipientPhone}}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 设备寄回物流信息-->
			<view class="section" v-if="orderDetail.status != '202' && orderDetail.status !='212'">
				<sendLogistics :result="logisticsInfo" :orderId="orderId"></sendLogistics>
			</view>
			<!-- 收货地址 -->
			<view class="section" v-if="showDeliveryAddress">
				<view class="order-address__detail">
					<view class="weui-card form-item">
						<tTitle title="收货地址" setPadding="30" @click="modifyAddress()"
							v-if="orderDetail.status != '212'">
						</tTitle>
						<view class="weui-card-hd" v-if="orderDetail.status == '212'">
							<view class="weui-card-hd-wrapper">
								<view class="weui-card-hd-title">收货地址</view>
							</view>
						</view>
						<view class="weui-card-bd" style="display: flex;width: 100%;">
							<view class="location">
								<image src="../../static/location_icon.png" mode="" class="location-icon"></image>
							</view>
							<view class="list vehicle-info">
								<view class="list-item">
									<view class="list-item_label recipient-name">
										地址：
									</view>
									<view class="list-item_value recipient">
										{{addressInfo.recipientAreaNameStr}}{{addressInfo.recipientAddress}}
									</view>
								</view>
								<view class="list-item">
									<view class="list-item_label recipient-name">
										姓名：
									</view>
									<view class="list-item_value recipient">
										{{addressInfo.recipient}} {{addressInfo.recipientPhone}}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

			</view>
			<!-- 提交材料 -->
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">提交材料</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="list-item align-items-center">
								<view class="list-item_label">
									更换原因
								</view>
								<view class="list-item_value">
									{{orderDetail.reasonTypeName}}
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label" style="margin-right: 0;">
									设备照片
								</view>
								<view class="list-item_attachment">
									<view class="upload-from">
										<view class="upload" v-for="(item,index) in imgList" :key="index">
											<view class="upload-wrap">
												<view class="upload-wrap-bd">
													<image :src="item.fileUrl" class="upload-wrap__img"
														mode='aspectFilt'>
													</image>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 审核通过 -->
			<view class="freight-indicate" v-if="orderDetail.status == '202'">设备寄回的邮费需自行承担</view>
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '202'">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item btn-item-left">
						<button class="weui-btn weui-btn_normal" @click="selfPost">
							自行寄回
						</button>
					</view>
					<view class="btn-item btn-item-right">
						<button class="weui-btn weui-btn_primary" @click="pickUp">
							上门取件
						</button>
					</view>
				</view>
			</view>
			<!-- 待支付 -->
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '206'">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="confirmPay">
							确认支付
						</button>
					</view>
				</view>
			</view>
			<!-- 设备已寄出 -->
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '209' ">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="confirmSign(orderDetail)">
							确认签收
						</button>
					</view>
				</view>
			</view>
			<!-- 已签收待激活 已完结 -->
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '210'">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="activate(orderDetail)">
							前往激活
						</button>
					</view>
				</view>
			</view>
			<!-- 已取消 -->
			<view class="weui-bottom-fixed" v-if="orderDetail.status == '212'">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="reapply(orderDetail)">
							重新申请
						</button>
					</view>
				</view>
			</view>
		</view>
		<u-modal v-model="uShow" @confirm="uShowConfirm" confirm-text="前往查看" :content="limitContent" ref="uModal"
			:async-close="true">
		</u-modal>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getCurrUserInfo,
		getLoginUserInfo,
		getEtcAccountInfo,
		getOpenid,
		setOpenid
	} from "@/common/storageUtil.js";
	import orderProgress from '../../components/order-progress/orderProgress.vue';
	import vehicle from '../../components/vehicle/vehicle.vue';
	import orderNo from '../../components/order-no/orderNo.vue'
	import tTitle from '../../components/new-title/new-title.vue';
	import sendLogistics from '../../components/send-logistics/sendLogistics.vue'
	import orderWl from '../orderWl/orderWl.vue'
	export default {
		components: {
			tLoading,
			orderNo,
			orderProgress,
			vehicle,
			tTitle,
			sendLogistics,
			orderWl,
		},
		data() {
			return {
				finish: false,
				isLoading: false,
				isEdit: false,
				orderId: '',
				orderDetail: {
					netUserId: '',
					carNo: '',
					carColor: '',
					carColorName: '',
					obuNo: '',
					cardNo: '',
					reasonType: '',
					reasonTypeName: '',
					reason: '',
					recipient: '',
					recipientPhone: '',
					recipientAreaCode: '',
					recipientAreaName: '',
					recipientAddress: '',
					payAmount: '',
					routeType: '0', //0-上门取件；1-自行寄回
				},
				cardType: '',
				showVehicle: false,
				imgList: [],
				//物理信息
				sfRouteResInfoDTO: {
					expressCompanyName: '',
					expressNumber: '',
					recipientAreaName: '',
					recipientAddress: '',
					sfRouteResInfos: {}
				},
				showNoBtn: false,
				//模块显示
				showLogisticsInfo: false, //物流信息
				showReturnAddress: false, //寄回地址
				showDeliveryAddress: false, //收货地址
				addressInfo: {
					orderId: '',
					recipient: '', //收货人
					recipientPhone: '', //收货人手机号码
					recipientAreaCode: '', //省名
					recipientAreaName: '', //市名
					recipientAddress: '', //详细门牌街道信息,
					recipientAreaNameStr: ''
				},
				//寄回物流信息
				logisticsInfo: {
					expressCompanyName: '',
					expressNumber: '',
					routeType: '',
					recipientAreaName: '',
					recipientAddress: '',
				},
				//支付成功弹框提示
				uShow: false,
				limitContent: '',
				openId: '',
			};
		},
		onLoad(options) {
			this.openId = getOpenid() || ''
			this.orderId = options.orderId
		},
		onShow() {
			if (this.orderId) {
				this.getOrderDetail()
				this.getImgList() //获取图片列表
			}
		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
		},
		methods: {
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			getOrderDetail() {
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.reissueOrderDetail, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log('=====', res.data)
					if (res.code == 200) {
						this.orderDetail = res.data
						this.getVehicleInfo(res.data.carNo, res.data.carColor)
						if (this.orderDetail.status == '202' || this.orderDetail.status == '204' || this
							.orderDetail.status == '205' || this
							.orderDetail.status == '206' || this.orderDetail.status == '207' || this.orderDetail
							.status == '208' || this.orderDetail.status == '212') {
							//待用户支付  收货地址
							this.showDeliveryAddress = true
						}
						if (this.orderDetail.status == '204' || this.orderDetail.status == '205' || this
							.orderDetail.status == '208' || this.orderDetail.status == '207' || this.orderDetail
							.status == '211') {
							this.showNoBtn = true
						}
						let expressOneInfo = res.data.expressInfo.filter(item => item.expressType == '0')[0] //物流信息
						//已签收待激活 已完结 已取消 物流信息
						if (this.orderDetail.status == '209' || this.orderDetail.status == '210' || this
							.orderDetail.status == '211') {
							this.getExpress(expressOneInfo)

						}
						//收货地址
						for (let key in this.addressInfo) {
							this.addressInfo[key] = expressOneInfo[key]
						}
						this.addressInfo.orderId = this.orderDetail.orderId
						this.addressInfo.recipientAreaNameStr = this.addressInfo.recipientAreaName ? this
							.addressInfo.recipientAreaName.replaceAll('-', '') : ''

						let expressTwoInfo = res.data.expressInfo.filter(item => item.expressType == '1')[
							0] //设备寄回物流信息和收货地址
						//寄回物流信息
						for (let key in this.logisticsInfo) {
							this.logisticsInfo[key] = expressTwoInfo[key]
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//查询车辆信息
			getVehicleInfo(carNo, carColor) {
				let params = {
					vehicle_code: carNo,
					vehicle_color: carColor
				}
				this.showVehicle = false
				this.isLoading = true
				this.$request.post(this.$interfaces.queryVehicleInfo, {
					data: params
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						console.log(res)
						this.orderDetail.obuNo = res.data[0].obu_id
						this.orderDetail.cardNo = res.data[0].cpu_card_id
						this.cardType = res.data[0].card_type
						this.showVehicle = true
						this.finish = true
					}
				})
			},
			getImgList() {
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.exchangeImgList, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						this.imgList = res.data.imgList
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			// 物流信息
			getExpress(expressOneInfo) {
				this.isLoading = true
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId,
					expressNumber: expressOneInfo.expressNumber
				}
				this.$request.post(this.$interfaces.exchangeExpress, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log(res)
						// 物流信息
						for (let key in this.sfRouteResInfoDTO) {
							this.sfRouteResInfoDTO[key] = expressOneInfo[key]
						}
						this.sfRouteResInfoDTO.sfRouteResInfos = res.data
						console.log('sfRouteResInfoDTO', this.sfRouteResInfoDTO)
						this.showLogisticsInfo = true
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			viewProgress() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderProgress/index?orderId=' + this.orderId
				})
			},
			modifyAddress() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderDetails/modifyDeliveryAddress?result=' + JSON
						.stringify(this.addressInfo)
				})
			},
			//自行寄回
			selfPost() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderDetails/returnLogisticsInfo?addressInfo=' + JSON.stringify(
						this.addressInfo)
				})
			},
			//上门取件
			pickUp() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderDetails/pickUp?addressInfo=' + JSON.stringify(this
						.addressInfo)
				})
			},
			//确认支付
			confirmPay() {
				let _self = this;
				this.isLoading = true;
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderId,
					openId: this.openId,
					payType: '10000601',
				}
				let data = {
					routePath: this.$interfaces.exchangePay.method,
					bizContent: params
				};

				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;
						console.log(res, '支付');
						if (res.code == 200) {
							let result = res.data;
							let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};

							wx.requestPayment({
								...payMessage,
								success: function(successres) {
									_self.isLoading = true
									console.log(successres, '支付成功回调', _self.isLoading);
									setTimeout(() => {
										_self.applyPayOrderQuery(result);
									}, 6000);
								},
								fail: function(err) {
									console.log('fail', err)
									_self.updatePayStatus(result, 3)
								},
							});
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			applyPayOrderQuery(data) {
				let params = {
					userNo: getLoginUserInfo().userNo,
					payOrderId: data.payOrderId,
				}
				this.$request.post(this.$interfaces.exchangeQueryPay, {
					data: params
				}).then(res => {
					this.isLoading = false;
					console.log('data===============>>>>>>>>>>', res)
					let status = res.data.status;
					let statusVal = {
						1: "支付中",
						2: "支付成功",
						3: "支付失败",
					};
					let msg = statusVal[status] || "支付中";

					if (status == 2) {
						//支付成功弹框后，跳转成功页面
						this.uShow = true
						this.limitContent = msg
					} else {
						//支付中、支付失败时只提示,留在当前页面不操作
						uni.showModal({
							title: '支付提示',
							content: msg,
							showCancel: false
						});
					}

				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			uShowConfirm() {
				this.uShow = false
				uni.redirectTo({
					url: '/pagesC/afterSaleBusiness/promptPage/index?type=5&orderId=' + this.orderId
				})
			},
			updatePayStatus(result, status) {
				let params = {
					payOrderId: result.payOrderId,
					status: status,
					payBusinessType: 3
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.exchangeUpdatePayStatus, {
					data: params
				}).then(res => {

				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			//前往激活
			activate(item) {
				console.log('item', item)
				let vehicleInfo = {}
				vehicleInfo.customerId = getCurrUserInfo().customer_id;
				vehicleInfo.vehicleCode = item.carNo;
				vehicleInfo.vehicleColor = item.carColor;
				vehicleInfo.businessSource = 3; //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
				vehicleInfo.orderId = item.orderId
				vehicleInfo.cardNo = item.oldCardNo
				vehicleInfo.obuNo = item.oldObuNo
				vehicleInfo.gxCardType = item.cardProduct
				vehicleInfo.deviceType = item.deviceType
				//新增更换原因与卡片类型
				vehicleInfo.reason = item.reasonTypeName
				vehicleInfo.cardType = this.cardType
				//按类型区别OBU更换前校验字段
				if (item.deviceType == '1' || item.deviceType == '2') {
					//更换的时候，类型1、2包含OBU
					//损坏类型: 0-非人为损坏;1-人为损坏 --默认为人为损坏1
					vehicleInfo.artificial = '0'
					//处理方式: 0-报废;1-回收  --默认处理方式报废0				
					vehicleInfo.dealType = '1'
					//有无旧设备: 1-有 2-无  --默认2
					vehicleInfo.returnOld = '1'
				}
				console.log('vehicleInfo', vehicleInfo)
				this.$store.dispatch(
					'setIssueVehicleInfo',
					vehicleInfo
				)
				uni.navigateTo({
					url: '/pagesA/newBusiness/issue/issue-install?activationType=2'
				})
			},
			//确认签收
			confirmSign(item) {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确定签收？',
					confirmText: '确定',
					showCancel: true,
					success(action) {
						if (action.confirm) {
							that.confirmGoods(item)
						} else if (action.cancel) {
							console.log('用户点击取消');
						}
					}
				})
			},
			confirmGoods(item) {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.orderDetail.orderId
				}
				this.$request.post(this.$interfaces.confirmGoods, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						//签收后需要激活
						let vehicleInfo = {}
						vehicleInfo.customerId = getCurrUserInfo().customer_id;
						vehicleInfo.vehicleCode = item.carNo;
						vehicleInfo.vehicleColor = item.carColor;
						vehicleInfo.businessSource = 3; //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
						vehicleInfo.orderId = item.orderId
						vehicleInfo.cardNo = item.cardNo
						vehicleInfo.obuNo = item.obuNo
						vehicleInfo.gxCardType = item.cardProduct
						vehicleInfo.deviceType = item.deviceType
						//新增更换原因与卡片类型
						vehicleInfo.reason = item.reasonTypeName
						vehicleInfo.cardType = this.cardType
						//按类型区别OBU更换前校验字段
						if (item.deviceType == '1' || item.deviceType == '2') {
							//更换的时候，类型1、2包含OBU
							//损坏类型: 0-非人为损坏;1-人为损坏 --默认为人为损坏1
							vehicleInfo.artificial = '0'
							//处理方式: 0-报废;1-回收  --默认处理方式报废0				
							vehicleInfo.dealType = '1'
							//有无旧设备: 1-有 2-无  --默认2
							vehicleInfo.returnOld = '1'
						}
						this.$store.dispatch(
							'setIssueVehicleInfo',
							vehicleInfo
						)
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=6"
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})

			},
			reapply(item) {
				let params = {
					custMastId: getEtcAccountInfo().custMastId,
					userNo: getLoginUserInfo().userNo,
					page: 1,
					pageSize: 999
				}
				this.$request
					.post(this.$interfaces.activateApplyVehicle, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							if (res.data.total > 0) {
								uni.showModal({
									title: '提示',
									content: '不允许重复申请',
									showCancel: false
								})
							} else {
								item.vehicleCode = item.carNo
								item.vehicleColor = item.carColor
								this.$store.dispatch(
									'afterSale/setAfterSaleVehicle', item
								)
								uni.navigateTo({
									url: '/pagesC/afterSaleBusiness/home/<USER>'
								})
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					}).catch(err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						})
					})

			},
			toWl() {
				let sfRouteResInfoDTO = {
					expressCompanyName: this.sfRouteResInfoDTO.expressCompanyName,
					expressNumber: this.sfRouteResInfoDTO.expressNumber,
					recipientAreaName: this.sfRouteResInfoDTO.recipientAreaName,
					recipientAddress: this.sfRouteResInfoDTO.recipientAddress,
				}
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderWl/orderWlDetail?orderId=' + this.orderId +
						'&result=' + JSON.stringify(sfRouteResInfoDTO)
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
	};
</script>

<style lang="scss" scoped>
	$uploadWidth: 155rpx;
	$uploadHeight: 114rpx;


	.apply-after-sale {
		background: #F8F8F8;
		margin-bottom: 180rpx;
		position: relative;
		height: 100%;

		.apply-after-sale-content {
			height: calc(100% - 240rpx);
			overflow-y: scroll;
		}

		.apply-after-sale-content-no-btn {
			height: calc(100% - 68rpx);
			overflow-y: scroll;
		}

		.list-item_attachment {
			width: calc(100% - 160rpx);
			overflow: hidden;
			margin-bottom: 20rpx;

			.upload-from {
				background-color: #FFFFFF;
				padding-bottom: 16rpx;
				margin-top: 16rpx;
				width: 100%;
				overflow: hidden;
			}

			.upload {
				width: 31%;
				float: left;
				margin-right: 10rpx;
			}

			.upload-wrap {
				margin-bottom: 20rpx;
			}

			.upload-wrap .upload-wrap-desc {
				font-size: 26rpx;
				text-align: center;
				width: 100%;
				color: #333333;
				font-weight: 400;
				margin-top: 26rpx;
			}

			.upload-wrap .upload-wrap-bd {
				width: $uploadWidth;
				height: $uploadHeight;
				position: relative;
				background-size: 100%;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__img {
				width: $uploadWidth;
				height: $uploadHeight;
				background-size: 100%;
				border-radius: 8rpx;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__close {
				position: absolute;
				right: 0;
				top: 0;
				padding: 0 10rpx;
				font-size: 36rpx;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
				// font-size: 36rpx;
				width: 28rpx;
				height: 28rpx;
				background-size: 100%;
			}



			.attachment_card {
				width: 50%;
				float: left;
				margin-bottom: 20rpx;

				.attachment_icon {
					width: 231rpx;
					height: 138rpx;
					background-size: 100%;
				}

				.attachment_name {
					width: 100%;
					height: 21rpx;
					font-size: 22rpx;
					font-weight: 400;
					color: #333333;
					line-height: 21rpx;
					margin-top: 18rpx;
					text-align: center;
				}
			}
		}

		.pay_money {
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #F65B5B !important;
		}

		.weui-card-bd-pay {
			width: calc(100% - 60rpx);
			height: 73rpx;
			background: #F0F0F0;
			border-radius: 6rpx;
			margin: 0 auto;
			text-align: center;
			line-height: 73rpx;
			position: relative;

			.ico_filter_down {
				width: 20rpx;
				height: 20rpx;
				position: absolute;
				right: 30rpx;
				top: 28rpx;
			}
		}

		.freight-indicate {
			width: auto;
			height: 33rpx;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FF9038;
			line-height: 33rpx;
			margin-left: 20rpx;
		}

		.bottom-box {
			display: flex;

			.btn-item {
				flex: 1;
			}

			.btn-item-left {
				margin-right: 32rpx;
			}

			.btn-item-right {}
		}

		.steps-wrapp {
			background-color: #fff;
			margin-bottom: 20rpx;
			height: 100rpx;
		}

		.form-item {
			border-radius: 10rpx;
			// margin: 20rpx;
		}

	}

	.u-textarea__count {
		position: absolute;
		right: 5px;
		bottom: 2px;
		font-size: 12px;
		color: #909193;
		background-color: #fff;
		padding: 1px 4px;
	}
</style>