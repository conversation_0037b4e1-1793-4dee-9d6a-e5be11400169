<template>
	<view class="apply-after-sale">
		<view class="apply-after-sale-content">
			<view class="order-address__detail">
				<view class="address-wrapper">
					<view class="title-wrapper">
						<view class="title">
							上门取件地址
						</view>
						<view class="location" @click="autoFill">
							获取微信地址
						</view>
					</view>
					<view class="install">
						<view class="input-wrapper">
							<view class="input-title">
								<span style="color: red;">*</span>寄件人
							</view>
							<input class="input auto-input" v-model="result.recipient" placeholder="请输入寄件人" type="text">
						</view>
						<view class="input-wrapper">
							<view class="input-title">
								<span style="color: red;">*</span>联系电话
							</view>
							<input class="input" placeholder="请输入寄件人联系电话" type="number" maxlength="11"
								v-model="result.recipientPhone">
						</view>
						<view class="input-wrapper">
							<view class="input-title">
								<span style="color: red;">*</span>所在地区
							</view>
							<view class="input">
								<uni-data-picker ref="picker" placeholder="选择区域(仅支持广西区内)" :localdata="regionList"
									v-model="result.recipientAreaCode" @change="onchange" @popupopened="onpopupopened"
									@popupclosed="onpopupclosed">
								</uni-data-picker>
								<image src="../../static/location.png" mode="" class="location-icon">
								</image>
							</view>
						</view>
						<view class="input-wrapper">
							<view class="input-title">
								<span style="color: red;">*</span>详细地址
							</view>
							<input class="input auto-input" v-model="result.recipientAddress" placeholder="请输入详细地址"
								type="text">
						</view>
					</view>
				</view>
			</view>
			<view class="order-address__detail">
				<view class="address-wrapper">
					<view class="title-wrapper">
						<view class="title">
							上门取件时间
						</view>
					</view>
					<view class="install">
						<view class="input-wrapper">
							<view class="input-title">
								<span style="color: red;">*</span>取件日期
							</view>
							<view class="input">
								<view class="pick-date ">
									<picker mode="date" @change="startDateChange" :value="result.pickUpDate"
										style="width: 100%;">
										<u-cell-item title=" " :arrow="false" icon="date-fill" :border-bottom="false">
											<view class="monthData">{{result.pickUpDate}}</view>
										</u-cell-item>
									</picker>
								</view>
							</view>

						</view>
						<view class="input-wrapper">
							<view class="input-title">
								<span style="color: red;">*</span>取件时间
							</view>
							<view class="pick-time">
								<picker mode="time" :value="result.pickUpTimeStart" start="09:00" end="20:00"
									@change="startTimeChange" class="pick-time-l">
									<view class="uni-input">{{result.pickUpTimeStart}}</view>
								</picker>
								<view class="line"></view>
								<picker class="pick-time-l" mode="time" :value="result.pickUpTimeEnd" start="09:00"
									end="20:00" @change="endTimeChange">
									<view class="uni-input">{{result.pickUpTimeEnd}}</view>
								</picker>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="weui-bottom-fixed">
				<view class="weui-bottom-fixed__box bottom-box">
					<view class="btn-item btn-item-left">
						<button class="weui-btn weui-btn_quit" @click="modifyWay">
							修改寄件方式
						</button>
					</view>
					<view class="btn-item btn-item-right">
						<button class="weui-btn weui-btn_primary" @click="submit">
							提交
						</button>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import uniDataPicker from '../../components/uni-data-picker/uni-data-picker.vue'
	import util from "@/common/util.js"
	import {
		getLoginUserInfo
	} from "@/common/storageUtil.js";

	function getDate(type) {
		const date = new Date();
		let year = date.getFullYear();
		let month = date.getMonth() + 1;
		let day = date.getDate();

		if (type === 'start') {
			year = year;
		}
		month = month > 9 ? month : '0' + month;
		day = day > 9 ? day : '0' + day;

		return `${year}-${month}-${day}`;
	}
	export default {
		components: {
			tLoading,
			uniDataPicker
		},
		data() {
			return {
				isLoading: false,
				applyId: "",
				regionList: [],
				result: {
					orderId: '',
					recipient: '', //收货人
					recipientPhone: '', //收货人手机号码
					recipientAreaCode: '', //省名
					recipientAreaName: '', //市名
					recipientAddress: '', //详细门牌街道信息,
					pickUpDate: getDate('start'),
					pickUpTimeStart: '09:00',
					pickUpTimeEnd: '18:00',
				},
			}
		},
		created() {
			this.getRegion();
		},
		onLoad(options) {
			let addressInfo = JSON.parse(options.addressInfo)
			for (let key in this.result) {
				this.result[key] = addressInfo[key] ? addressInfo[key] : this.result[key]
			}
		},
		methods: {
			getRegion() {
				let params = {};
				this.$request.post(this.$interfaces.getRegion, {
					data: params
				}).then(res => {
					let list = [];
					if (res.code == 200) {
						list = res.data;
						for (let i = 0; i < list.length; i++) {
							list[i].children = list[i].child;
							list[i].text = list[i].label;
							for (let j = 0; j < list[i].children.length; j++) {
								list[i].children[j].children = list[i].children[j].child;
								list[i].children[j].text = list[i].children[j].label;
								for (let k = 0; k < list[i].children[j].children.length; k++) {
									list[i].children[j].children[k].children = list[i].children[j].children[k]
										.child;
									list[i].children[j].children[k].text = list[i].children[j].children[k].label;
								}
							}
						}
					}
					
					let gxArrList = list.filter(item => {
						return item.value == '450000'
					})
					//限制广西区域
					this.regionList = gxArrList;
					// this.regionList = list;
				})
			},
			startDateChange(e) {
				this.result.pickUpDate = e.detail.value
			},
			startTimeChange(e) {
				this.result.pickUpTimeStart = e.detail.value
			},
			endTimeChange(e) {
				this.result.pickUpTimeEnd = e.detail.value
			},
			onpopupopened() {},
			onpopupclosed() {},
			//获取微信地址信息
			autoFill() {
				uni.authorize({
					scope: 'scope.address',
					success: () => {
						this.getWxAddress()
					},
					fail: () => {},
					complete() {}
				})
			},
			getWxAddress() {
				uni.chooseAddress({
					success: user => {
						if (user.provinceName != '广西壮族自治区') {
							uni.showModal({
								title: '提示',
								content: '业务试行阶段，为不影响您的体验，暂无法处理省外订单，请您谅解',
								showCancel: false
							});
							return
						}
						//微信获取地址信息赋值
						this.$nextTick(() => {
							this.result.recipient = user.userName
							this.result.recipientPhone = user.telNumber
							this.result.recipientAreaCode = user.nationalCode
							this.result.recipientAreaName = user.provinceName + '-' + user.cityName +
								'-' + user.countyName
							this.result.recipientAddress = user.detailInfo
							// this.$refs.picker.clear()
						})
					}
				})
			},
			onchange(e) {
				if (e.detail.value.length == 0) return;
				this.$nextTick(() => {
					this.result.recipientAreaCode = e.detail.value[2].value
					this.result.recipientAreaName = e.detail.value[0].text + '-' + e.detail.value[1].text + '-' + e
						.detail.value[2].text
				})
			},
			modifyWay() {
				uni.redirectTo({
					url: '/pagesC/changeBussiness/orderDetails/returnLogisticsInfo?addressInfo=' + JSON.stringify(
						this.result)
				})
			},
			validateHandle() {
				let msg = ''
				if (!this.result.pickUpTimeStart || !this.result.pickUpTimeEnd) {
					msg = '您的上门时间未填写，请先填写'
				}
				if (!this.result.pickUpDate) {
					msg = '您的上门日期未选择，请先选择'
				}
				if (!this.result.recipientAddress) {
					msg = '您的详细地址未填写，请先填写'
				}
				if (!this.result.recipientAreaCode) {
					msg = '您的所在地区未选择，请先选择'
				}
				if (!util.checkPhone(this.result.recipientPhone)) {
					msg = "请填写正确的手机号码"
				}
				if (!this.result.recipientPhone) {
					msg = '您的手机号码未填写，请先填写'
				}
				if (!this.result.recipient) {
					msg = '您的收货人未填写，请先填写'
				}
				if (msg) return msg;
				return ''
			},
			submit() {
				let msg = this.validateHandle();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				this.isLoading = true
				let params = {
					userNo: getLoginUserInfo().userNo,
					orderId: this.result.orderId,
					sender: this.result.recipient, //收货人
					senderPhone: this.result.recipientPhone, //收货人手机号码
					senderAreaCode: this.result.recipientAreaCode, //寄件区域代码
					senderAddress: this.result.recipientAddress, //寄件详细地址
					senderAreaName: this.result.recipientAreaName,
					pickUpDate: this.result.pickUpDate,
					pickUpTimeStart: this.result.pickUpTimeStart + ':00',
					pickUpTimeEnd: this.result.pickUpTimeEnd + ':59',
				}
				this.$request.post(this.$interfaces.exchangeDoor, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=4&orderId=" +
								this.result.orderId
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-address__detail {
		margin: 20rpx;
	}

	.order-info {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding-bottom: 20rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}

	.status-value.info {
		background: #F6F6F6;
		color: #333333;
	}


	.status-value.success {
		background: rgba(0, 189, 50, 0.1);
		color: #00BD32;
	}

	.status-value.warnning {
		background: rgba(255, 145, 0, 0.1);
		color: #FF9100;
	}

	.status-value.error {
		background: rgba(255, 84, 84, 0.1);
		color: #FF5454;
	}

	.address-wrapper {
		// margin: 20rpx;
		padding-bottom: 50rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
	}

	.title-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx 30rpx 0 30rpx;

		.title {
			font-size: 30rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #323435;
		}
	}

	.location {
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #3874FF;
		width: 144rpx;
	}

	.ios-textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-top: 45rpx;
	}

	.textarea {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding-bottom: 38rpx;
	}

	.picker {
		width: auto;
		flex: 1;
		height: 100%;
		display: flex;
		align-items: center;
		color: #777777;

		// line-height: 120rpx;
		// text-align: center;
	}

	.bottom-box {
		display: flex;

		.btn-item {
			flex: 1;
		}

		.btn-item-left {
			margin-right: 32rpx;
		}

		.btn-item-right {}
	}


	.install {
		margin: 20rpx 20rpx 0;
		background-color: $uni-bg-color;
		// padding-bottom: 10rpx;
		border-radius: 12rpx;

		.title-container {
			padding: 30rpx;
		}

		.input-wrapper {
			padding: 0 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 80rpx;
			line-height: 40rpx;
			color: rgba(16, 16, 16, 100);
			background-color: $uni-bg-color;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #999999;

			.input-title {
				width: 180rpx;
				color: #323435;
				// text-align: left;
			}

			.input {
				width: calc(100% - 180rpx);
				height: 64rpx;
				background: #fff;
				border-radius: 6rpx;
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333333;
				padding-left: 20rpx;
				border: 2rpx solid #DDDDDD;
				position: relative;

				/deep/.input-value-border {
					border: none;
					line-height: 30px;
					color: #333;
					padding: 0px !important
				}

				/deep/.selected-list {
					padding: 0px !important
				}

				/deep/.selected-item {
					padding: 0px !important
				}

				/deep/.icon-clear {
					display: none;
				}

				/deep/.arrow-area {
					display: none;
				}

				.location-icon {
					position: absolute;
					right: 18rpx;
					width: 30rpx;
					height: 31rpx;
					background-size: 100%;
					top: 17rpx;
				}
			}

			.selected-item {
				color: #333;
			}

			.pick-date {
				width: 100%;
				height: 100%;
				display: flex;

				/deep/.u-cell {
					position: relative;
					padding: 0;
				}

				/deep/.u-cell__value {
					font-size: 28rpx !important;
					text-align: left;
					color: #333;
				}

				/deep/.u-cell__left-icon-wrap {
					position: absolute;
					right: 26rpx;
					margin-right: 0px !important;
					top: 12rpx;
				}

				/deep/.u-icon__icon {
					font-size: 25rpx !important;
					color: #999999;
				}
			}

			.pick-time {
				width: calc(100% - 180rpx);
				display: flex;

				.pick-time-l {
					width: 45%;
					border: 2rpx solid #DDDDDD;
					height: 64rpx;
					background: #fff;
					border-radius: 6rpx;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #333333;
					padding-left: 20rpx;
					line-height: 64rpx;
				}

				.line {
					text-align: center;
					width: 36rpx;
					height: 3rpx;
					background: #D9D9D9;
					border-radius: 2rpx;
					margin: 30rpx 13rpx;
				}
			}

			.auto-input {
				position: relative;
			}
		}
	}
</style>