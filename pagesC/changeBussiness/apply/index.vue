<template>
	<view class="apply-after-sale">
		<view class="steps-wrapp">
			<handleStep :activeIndex='0' :stepList="stepList"></handleStep>
		</view>
		<view class="content">
			<view class="section">
				<vehicle :vehicleObj="formData"></vehicle>
			</view>
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">提交资料</view>
						<!-- 	<view class="weui-card-extra">
								<view class="desc">
									查看拍照样例
								</view>
							</view> -->
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="list-item align-items-center">
								<view class="list-item_label">
									<span style="color:red">*</span>更换原因
								</view>
								<view class="list-item_value list-item_view" @click="show=true">
									<view>{{formData.reasonTypeStr}}</view>
									<image src="../../static/ico_filter_down.png" mode="" class="down-icon"></image>
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label" style="margin-right: 0;">
									<span style="color:red">*</span>设备照片
								</view>
								<view class="list-item_attachment">
									<view class="upload-from">
										<view class="upload" v-for="(item,index) in imgJsonList" :key="index">
											<view class="upload-wrap">
												<view class="upload-wrap-bd">
													<image :src="item.fileUrl" class="upload-wrap__img"
														mode='aspectFilt'>
													</image>
													<view class="upload-wrap__close" @tap.stop="delImgHandle(item)">
														<image src="../../static/close.png" mode="" class="close">
														</image>
													</view>
												</view>
											</view>
										</view>
										<view class="upload upload-add" v-if="imgJsonList.length<3">
											<view class="upload-wrap">
												<view class="upload-wrap-bd" @tap="ChooseImage(item)">
													<image src="../../static/add_icon.png" class="upload-wrap__img">
													</image>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="list-item align-items-center">
								<view class="list-item_label">
									补充说明
								</view>
								<view class="list-item-textaxea">
									<textarea v-model="formData.reason"></textarea>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="section">
				<view class="order-address__detail">
					<view class="address-wrapper">
						<view class="title-wrapper">
							<view class="title">
								收货地址
							</view>
							<view class="wx-location" @click="autoFill">
								获取微信地址
							</view>
						</view>
						<view class="install">
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>收货人
								</view>
								<input class="input auto-input" v-model="formData.recipient" placeholder="请输入收货人"
									type="text">
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>手机号码
								</view>
								<input class="input" placeholder="请输入收货人手机号码" type="number" maxlength="11"
									v-model="formData.recipientPhone">
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>所在地区
								</view>
								<view class="input">
									<uni-data-picker ref="picker" placeholder="选择区域(仅支持广西区内)" :localdata="regionList"
										v-model="formData.recipientAreaCode" @change="onchange"
										@popupopened="onpopupopened" @popupclosed="onpopupclosed">
									</uni-data-picker>
									<image src="../../static/location.png" mode="" class="location-img">
									</image>
								</view>
							</view>
							<view class="input-wrapper">
								<view class="input-title">
									<span style="color: red;">*</span>详细地址
								</view>
								<input class="input auto-input" v-model="formData.recipientAddress"
									placeholder="请输入详细地址" type="text">
							</view>
						</view>
					</view>
				</view>

			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">

				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="activateApplyHandle">
						提交
					</button>
				</view>

			</view>
		</view>
		<!-- 图片上传组件 -->
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
		<u-select v-model="show" :list="changeReasonList" @confirm="confirm">
		</u-select>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import handleStep from '../../components/handle-step/index.vue'
	import vehicle from '../../components/vehicle/vehicle.vue';
	import {
		getLoginUserInfo,
		getAccountId,
		getCurrUserInfo,
		getEtcAccountInfo
	} from '@/common/storageUtil.js'
	import uniDataPicker from '../../components/uni-data-picker/uni-data-picker.vue'
	import util from "@/common/util.js"
	export default {
		components: {
			tLoading,
			cpimg,
			handleStep,
			vehicle,
			uniDataPicker
		},
		data() {
			return {
				isLoading: false,
				stepList: [{
					value: '',
					label: '提交材料'
				}, {
					value: '',
					label: '设备寄回'
				}, {
					value: '',
					label: '支付费用'
				}, {
					value: '',
					label: '确认收货'
				}, {
					value: '',
					label: '设备激活'
				}],
				regionList: [],
				imgJsonList: [],
				sourceType: '101', //101-更换设备图片第一张图，102-更换设备图片第二张图，103-更换设备图片第三张图
				formData: {
					userNo: '',
					carNo: '',
					carColor: '',
					obuNo: '',
					cardNo: '',
					reasonType: '101',
					reasonTypeStr: '没电，插卡无反应',
					imgJsonList: [],
					reason: '',
					recipient: '',
					recipientPhone: '',
					recipientAreaCode: '',
					recipientAreaName: '',
					recipientAddress: '',
					vehicleType: '',
					vehicleTypeKind: '',
					custMastId: '',
					cardProduct: ''
				},
				show: false,
				changeReasonList: [{
						label: '没电，插卡无反应',
						value: '101',
					},
					{
						label: '过不了车道',
						value: '102',
					},
					{
						label: '插拔卡无反应',
						value: '103',
					},
					{
						label: '无法连接蓝牙',
						value: '104',
					},
					{
						label: '查看显示异常',
						value: '105',
					},
					{
						label: '其他',
						value: '106',
					}
				],
			};
		},
		computed: {

		},

		onLoad(options) {
			// url 参数 vehicleInfo 和 opBusinessType
			let vehicleInfo = this.$store.getters['afterSale/afterSaleVehicle'] || {};
			if (Object.keys(vehicleInfo).length) {
				this.formData.carColor = vehicleInfo.vehicleColor || '';
				this.formData.carNo = vehicleInfo.vehicleCode || '';
				this.formData.cardNo = vehicleInfo.cardNo || '';
				this.formData.obuNo = vehicleInfo.obuNo || '';
				this.formData.vehicleType = vehicleInfo.vehicleType || '';
				this.formData.vehicleTypeKind = vehicleInfo.vehicleTypeKind || '';
				this.formData.custMastId = getEtcAccountInfo().custMastId
				this.formData.cardProduct = vehicleInfo.cardProduct
			}

		},
		created() {
			this.getRegion()
			this.formData.userNo = getLoginUserInfo().userNo
		},
		methods: {
			confirm(options) {
				this.formData.reasonType = options[0].value
				this.formData.reasonTypeStr = options[0].label
			},
			ChooseImage() {
				if (this.imgJsonList.length == 0) {
					this.sourceType = '101'
				}
				if (this.imgJsonList.length == 1) {
					let photoCode = this.imgJsonList[0].photoCode
					this.sourceType = photoCode == '101' ? '102' : '101'
				}
				if (this.imgJsonList.length == 2) {
					let codeList = [this.imgJsonList[0].photoCode, this.imgJsonList[1].photoCode]
					this.sourceType = codeList.includes('101') ? (codeList.includes('102') ? '103' : '102') : '101'
				}
				this.$refs.cpimg._changImg(this.sourceType);
			},
			delImgHandle(obj) {
				// 根据item.key 决定删除片内容
				this.imgJsonList = this.imgJsonList.filter(item => item.photoCode != obj.photoCode)
				this.formData.imgJsonList = this.imgJsonList
				console.log(this.imgJsonList)
			},
			////图片压缩成功
			cpimgOk(file) {
				this.sendUploadFile(file);
			},
			//图片压缩失败
			cpimgErr(e) {

			},
			// 上传图片
			sendUploadFile(file) {
				let params = {
					netUserId: this.formData.netUserId,
					image: file.toString(),
					photoCode: this.sourceType
				};
				this.$request.post(this.$interfaces.cacheImgUplaod, {
					data: params
				}).then(res => {
					if (res.code == 200 && res.data && res.data.data) {
						let result = res.data.data;
						this.imgJsonList.push(result)
						console.log(this.imgJsonList)
						this.formData.imgJsonList = this.imgJsonList
					}
					this.isLoading = false;
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});

				})
			},
			validateHandle() {
				let msg = ''
				if (!this.formData.recipientAddress) {
					msg = '您的详细地址未填写，请先填写'
				}
				if (!this.formData.recipientAreaCode) {
					msg = '您的所在地区未填写，请先填写'
				}
				if (!util.checkPhone(this.formData.recipientPhone)) {
					msg = "请输入正确的手机号码"
				}
				if (!this.formData.recipientPhone) {
					msg = '您的手机号码未填写，请先填写'
				}
				if (!this.formData.recipient) {
					msg = '您的收货人未填写，请先填写'
				}
				if (this.formData.imgJsonList.length == 0) {
					msg = '您的设备照片未上传，请先上传'
				}
				if (!this.formData.reasonType) {
					msg = '您的更换原因未填写，请先填写'
				}
				if (msg) return msg;
				return ''
			},
			activateApplyHandle() {
				let msg = this.validateHandle();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				this.sendExchangeApply()
				// this.$subscriptionMethod(this.sendExchangeApply)
			},
			// 发送更换申请
			sendExchangeApply() {
				if (this.isLoading) return;
				this.isLoading = true;
				var imgArray = JSON.parse(JSON.stringify(this.formData.imgJsonList))
				for (let i in imgArray) {
					imgArray[i].fileUrl = imgArray[i].code ? imgArray[i].code : imgArray[i].fileUrl
				}
				let params = {
					// ...this.formData,
					userNo: this.formData.userNo,
					carNo: this.formData.carNo,
					carColor: this.formData.carColor,
					obuNo: this.formData.obuNo,
					cardNo: this.formData.cardNo,
					reasonType: this.formData.reasonType,
					reasonTypeStr: this.formData.reasonTypeStr,
					imgJsonList: imgArray,
					reason: this.formData.reason,
					recipient: this.formData.recipient,
					recipientPhone: this.formData.recipientPhone,
					recipientAreaCode: this.formData.recipientAreaCode,
					recipientAreaName: this.formData.recipientAreaName,
					recipientAddress: this.formData.recipientAddress,
					vehicleType: this.formData.vehicleType,
					vehicleTypeKind: this.formData.vehicleTypeKind,
					custMastId: this.formData.custMastId,
					cardProduct: this.formData.cardProduct
				}
				console.log('params', params)
				this.$request.post(this.$interfaces.exchangeApply, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.$subscriptionMethod()
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=1&orderId=" +
								res.data.orderId
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			getRegion() {
				let params = {};
				this.$request.post(this.$interfaces.getRegion, {
					data: params
				}).then(res => {
					let list = [];
					if (res.code == 200) {
						list = res.data;
						for (let i = 0; i < list.length; i++) {
							list[i].children = list[i].child;
							list[i].text = list[i].label;
							for (let j = 0; j < list[i].children.length; j++) {
								list[i].children[j].children = list[i].children[j].child;
								list[i].children[j].text = list[i].children[j].label;
								for (let k = 0; k < list[i].children[j].children.length; k++) {
									list[i].children[j].children[k].children = list[i].children[j].children[k]
										.child;
									list[i].children[j].children[k].text = list[i].children[j].children[k].label;
								}
							}
						}
					}

					let gxArrList = list.filter(item => {
						return item.value == '450000'
					})
					//限制广西区域
					this.regionList = gxArrList;
					// this.regionList = list

				})
			},
			onpopupopened(e) {
				console.log('popupopened');
			},
			onpopupclosed(e) {
				console.log('popupclosed');
			},
			onchange(e) {
				if (e.detail.value.length == 0) return;
				console.log('---------onchange:', e);
				// this.formData.provinceName = e.detail.value[0].text
				// this.formData.cityName = e.detail.value[1].text
				// this.formData.areaName = e.detail.value[2].text
				this.formData.recipientAreaName = e.detail.value[0].text + '-' + e.detail.value[1].text + '-' + e.detail
					.value[
						2]
					.text
				this.$nextTick(() => {
					this.formData.recipientAreaCode = e.detail.value[2].value
				})

				console.log('地区赋值=======>>', this.formData.addressDetail)
			},
			//获取微信地址信息
			autoFill() {
				console.log('微信调用地址1')
				uni.authorize({
					scope: 'scope.address',
					success: () => {
						console.log('success')
						this.getWxAddress()
					},
					fail: () => {
						console.log('fail')
					},
					complete() {
						console.log('complete')
					}
				})
			},
			getWxAddress() {
				uni.chooseAddress({
					success: user => {
						if (user.provinceName != '广西壮族自治区') {
							uni.showModal({
								title: '提示',
								content: '业务试行阶段，为不影响您的体验，暂无法处理省外订单，请您谅解',
								showCancel: false
							});
							return
						}
						//微信获取地址信息赋值
						this.formData.recipient = user.userName
						this.formData.recipientPhone = user.telNumber
						this.formData.recipientAreaName = user.provinceName + '-' + user.cityName + '-' + user
							.countyName
						this.formData.recipientAddress = user.detailInfo
						this.$nextTick(() => {
							this.formData.recipientAreaCode = user.nationalCode
							// this.$refs.picker.clear()
						})


						console.log('this.formData', this.formData)
					}
				})
			},
		},
	};
</script>

<style lang="scss" scoped>
	$uploadWidth: 155rpx;
	$uploadHeight: 114rpx;

	.list-item_attachment {
		width: calc(100% - 160rpx);
		overflow: hidden;
		margin-left: 8rpx;
	}

	.upload-from {
		background-color: #FFFFFF;
		// padding-bottom: 16rpx;
		// margin-top: 16rpx;
		width: 100%;
		overflow: hidden;
	}

	.upload {
		width: 155rpx;
		float: left;
		margin-right: 10rpx;
	}

	.upload:nth-child(3) {
		margin-right: 0rpx;
	}

	.upload {
		width: $uploadWidth;
	}

	.upload-wrap {
		// margin-bottom: 20rpx;
	}

	.upload-wrap .upload-wrap-desc {
		font-size: 26rpx;
		text-align: center;
		width: 100%;
		color: #333333;
		font-weight: 400;
		margin-top: 26rpx;
	}

	.upload-wrap .upload-wrap-bd {
		width: $uploadWidth;
		height: $uploadHeight;
		position: relative;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__img {
		width: $uploadWidth;
		height: $uploadHeight;
		border-radius: 8rpx;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__close {
		position: absolute;
		right: 10rpx;
		top: 10rpx;
		// padding: 0 10rpx;
		// font-size: 36rpx;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
		width: 30rpx;
		height: 30rpx;
		background-size: 100%;
	}

	.apply-after-sale {
		background: #F8F8F8;
		padding-bottom: 180rpx;
		position: relative;
		height: 100%;

		.content {
			height: calc(100% - 120rpx);
			overflow-y: scroll;
		}

		.weui-bottom-fixed {
			position: fixed;
			bottom: 0;
		}

		.bottom-box {
			display: flex;
		}

		.bottom-box .btn-item {
			flex: 1;
		}

		.steps-wrapp {
			background-color: #fff;
			// margin-bottom: 20rpx;
			padding-top: 20rpx;
			height: 150rpx;
		}

		.order-address__detail {
			.address-wrapper {
				padding-bottom: 50rpx;
				background-color: #ffffff;
				border-radius: 12rpx;
			}

			.title-wrapper {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 30rpx 0 30rpx;

				.title {
					font-size: 30rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: bold;
					color: #323435;
				}
			}

			.wx-location {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #0081FF;
			}

			/deep/.uni-data-tree {
				width: 100%;
				line-height: 66rpx;
			}
			
			/deep/.uni-data-tree-dialog {
				z-index: 1002;
			}


			.install {
				margin: 20rpx 20rpx 0;
				background-color: $uni-bg-color;
				// padding-bottom: 10rpx;
				border-radius: 12rpx;

				.title-container {
					padding: 30rpx;
				}

				.input-wrapper {
					padding: 0 10rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 80rpx;
					line-height: 40rpx;
					color: rgba(16, 16, 16, 100);
					background-color: $uni-bg-color;
					font-size: 26rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;

					.input-title {
						flex: 0 0 180rpx;
						width: 180rpx;
						color: #323435;
						// text-align: left;
					}

					.input {
						flex: 1;
						// width: calc(100% - 180rpx);
						// justify-content: flex-end;
						// text-align: left;
						height: 60rpx;
						background: #fff;
						border-radius: 6rpx;
						font-size: 26rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #333333;
						padding-left: 20rpx;
						border: 2rpx solid #DDDDDD;
						position: relative;

						/deep/.input-value-border {
							border: none;
							line-height: 30px;
							color: #333;
							padding: 0px !important
						}

						/deep/.selected-list {
							padding: 0px !important
						}

						/deep/.selected-item {
							padding: 0px !important
						}

						/deep/.icon-clear {
							display: none;
						}

						/deep/.arrow-area {
							display: none;
						}
					}

					.location-img {
						position: absolute;
						right: 18rpx;
						width: 30rpx;
						height: 31rpx;
						background-size: 100%;
						top: 17rpx;
					}

					.selected-item {
						color: #333333;
					}

					/deep/.picker {
						height: 100%;
						width: auto;
					}

					.auto-input {
						position: relative;
					}

				}
			}
		}

		.list {
			padding-bottom: 50rpx;

			.list-item {
				display: flex;
				margin-bottom: 30rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.list-item_label {
					font-size: 26rpx;
					width: 150rpx;
					margin-right: 10rpx;
					font-weight: 400;
					color: #999999;
				}

				.list-item_value {
					font-size: 26rpx;
					font-weight: 400;
					color: #333333;
				}

				.list-item-textaxea {
					width: calc(100% - 160rpx);
					height: 116rpx;
					background: #FFFFFF;
					border-radius: 8rpx;
					border: 2rpx solid #DDDDDD;
					line-height: 64rpx;
					overflow-y: scroll;

					textarea {
						width: calc(100% - 36rpx);
						margin: auto;
						height: 100%;
					}
				}
			}
		}
	}

	.active-sms__dialog {
		.login-form-group .code-img {
			width: 240upx;
			height: 90upx;
			margin-left: 20upx;
		}

		.sendSMS {
			padding: 10rpx;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.desc {
			font-size: 30rpx;
			text-align: left;
			font-weight: 400;
			color: #858686;
			background: #ffffff;
			padding: 10upx 25upx;
		}

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
		}
	}

	.active-sms__dialog .cu-form-group .title {
		font-size: 32upx;
	}

	.active-sms__dialog .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.active-sms__dialog .cu-form-group input {
		text-align: left;
	}

	.active-sms__dialog .cu-form-group radio-group {
		flex: 1;
		text-align: left;
	}
</style>