<!--
  * @projectName:gxetc-issue-mp2c
  * @desc:
  * @author:zhang<PERSON>
  * @date:2022/11/29 13:38:18
!-->
<template>
	<view class="container">
		<p style=" orphans:0; text-align:center; widows:0;margin-bottom:60rpx">
			<text style="font-family:方正小标宋_GBK; font-size:15pt; font-weight:bold">委托扣款授权书</text>
		</p>
		<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
				style="font-family:方正仿宋_GB2312; font-size:12pt">本授权书由您向广西捷通高速科技有限公司（下称“广西捷通”）出具，具有授权之法律效力。请您务必审慎阅读、充分理解本授权书各条款内容，特别是免除或者限制责任的条款，前述条款可能以加粗字体显示，您应重点阅读。除非您已阅读并接受本授权书所有条款，否则您无权使用微信支付的自动续费、自动缴费、自动扣款等服务。您同意本授权书即视为您已授权广西捷通代理您向财付通支付科技有限公司（下称“财付通”）申请开通微信支付自动续费和免密支付功能，并自愿承担由此导致的一切法律后果。</text>
		</p>
		<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
				style="font-family:方正仿宋_GB2312; font-size:12pt">您确认并不可撤销地授权广西捷通向财付通发出扣款指令，财付通即可在不验证您的支付密码、短信动态码等信息的情况下直接从您的银行账户或微信支付账户中扣划广西捷通指定的款项至广西捷通指定账户。</text>
		</p>
		<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
				style="font-family:方正仿宋_GB2312; font-size:12pt">在任何情况下，只要广西捷通向财付通发出支付指令，财付通就可按照该指令进行资金扣划，财付通对广西捷通的支付指令的正确性、合法性、完整性、真实性不承担任何法律责任，相关法律责任由您和广西捷通自行承担。</text>
		</p>
		<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
				style="font-family:方正仿宋_GB2312; font-size:12pt">您在扣款账户内必须预留有足够的资金余额，否则因账户余额不足导致无法及时扣款或扣款错误、失败的，一切责任由您自行承担。因不可归责于财付通的事由，导致的不能及时划付款项、划账错误等责任与财付通无关。</text>
		</p>
		<p style="line-height:13pt; margin:0pt; orphans:0; text-align:justify; text-indent:21pt; widows:0"><text
				style="font-family:方正仿宋_GB2312; font-size:12pt">您确认，因广西捷通的原因导致您遭受经济损失的，由您与广西捷通协商解决，与财付通无关。</text>
		</p>
		<view class="agreement">
			<view class="g-flex g-flex-align-center g-flex-center">
				<checkbox-group @change="checkboxChange">
					<label class="g-flex g-flex-center g-flex-align-center"
						:class="{'jitter-div':isJitter,'':!isJitter}">
						<checkbox value="checked" :checked="isChecked" style="transform:scale(0.7)"
							class="cyan checked remember-check" />
						<view style="line-height: 58rpx;">
							勾选则代表您同意<text style="color:#3e62e8">《委托扣款授权书》
							</text>
						</view>
					</label>
				</checkbox-group>
			</view>
		</view>


		<view class="bottom-box" style="margin: 40rpx 0px;padding-bottom: 40rpx;">
			<!-- 			<view class="btn-item">
				<button class="weui-btn" @click="back">
					上一步
				</button>
			</view> -->
			<view class="btn-item">
				<button class="weui-btn weui-btn_primary" @click="sign">
					同意协议并签约
				</button>
			</view>

		</view>

	</view>

</template>

<script>
	import TButton from "@/components/t-button.vue";
	export default {
		name: '',
		props: {},
		components: {
			TButton
		},
		data() {
			return {
				isChecked: false,
				isJitter: false,
			}
		},
		onLoad(obj) {

		},
		computed: {},
		watch: {},
		created() {},
		methods: {
			checkboxChange(e) {
				this.isChecked = !!e.detail.value.length;
			},
			sign() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
				let prevPage = pages[pages.length - 2]; //上一页页面实例

				if (!this.isChecked) {
					this.isJitter = true
					uni.vibrateLong({
						success: function() {
							console.log('success');
						}
					});
					setTimeout(() => {
						this.isJitter = false
					}, 1000)
					uni.showModal({
						title: '提示',
						content: '请阅读并同意《委托扣款授权书》',
						confirmText: '同意继续',
						success: (res) => {
							if (res.confirm) {
								prevPage.$vm.isAttorney = true //改变同意状态
								prevPage.$vm.wechatSignHandle() //调用签约方法
								uni.navigateBack({
									delta: 1
								})
							}
						}
					})
					return
				}
				prevPage.$vm.isAttorney = true //改变同意状态
				prevPage.$vm.wechatSignHandle() //调用签约方法
				uni.navigateBack({
					delta: 1
				})
			},
		}
	}
</script>

<style lang='scss'>
	page {
		background-color: #fff;
	}

	.container {
		margin: 20rpx 30rpx;
	}

	.agreement {
		margin-top: 20rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.jitter-div {
		animation: 1s jitter;
	}

	@keyframes jitter {
		0% {
			transform: scale(1.4, 1.4);
		}

		10% {
			transform: scale(1, 1);
		}

		25% {
			transform: scale(1.2, 1.2);
		}

		50% {
			transform: scale(1, 1);
		}

		70% {
			transform: scale(1.2, 1.2);
		}

		100% {
			transform: scale(1, 1);
		}
	}
</style>