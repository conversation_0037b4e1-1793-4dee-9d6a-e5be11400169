<template>
	<view class="weui-form">
		<!-- <view class="weui-cells__title"
			style="font-size: 28rpx;font-weight: bold;color: #000;margin-top: 30rpx;padding: 0 25rpx;font-size: 28rpx;line-height: 80rpx;">
			签约车辆信息
		</view> -->
		<view class="weui-cells__title">
			<view class="weui-cells__title__decoration">
				签约车辆信息
			</view>
		</view>
		<view class="weui-cells">
			<view class="vux-x-input weui-cell">
				<view class="weui-cell__hd">
					<view class="weui-label">签约车牌号:</view>
				</view>
				<view class="weui-cell__bd weui-cell__primary">
					<view class="weui-cell__value g-flex g-flex-end">
						<view class="vehicle-card_item g-flex g-flex-horizontal-vertical"
							:class="'license-plate-'+vehicleInfo.vehicleColor"
							:style="{color:vehicleInfo.vehicleColor=='0'|| vehicleInfo.vehicleColor=='2' ? '#fff' : '#000'}">
							{{vehicleInfo.vehicleCode}}
						</view>
					</view>
				</view>
			</view>
			<view class="vux-x-input weui-cell">
				<view class="weui-cell__hd">
					<view class="weui-label">签约车型:</view>
				</view>
				<view class="weui-cell__bd weui-cell__primary">
					<view class="weui-cell__value">
						{{getVehicleClassType(vehicleInfo.vehicleNationalType)}}【{{vehicleTypeList[vehicleInfo.vehicleType] +'车'}}】
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getProvince,
		provincesOptions,
		vehicleColors,
		vehicleType,
		getVehicleClassType
	} from "@/common/const/optionData.js";
	export default {
		props: {
			vehicleInfo: {
				type: Object
			},
		},
		data() {
			return {
				vehicleTypeList: vehicleType,
			}
		},
		computed: {
			// curVehicleInfo() {
			// 	return getCurrentCar() ? getCurrentCar() : {};
			// }
		},
		created() {

		},
		methods: {
			getVehicleClassType,
		}
	}
</script>

<style lang='scss' scoped>
	.vehicle-card_item {
		width: 241rpx;
		height: 61rpx;
		font-size: 30rpx;
		font-weight: 500;
		color: #FFFFFF;
	}

	.license-plate-0 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-1 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_green.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-2 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_black.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-3 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_white.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-4 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_gradient_green.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-5 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_yellow_green.png') no-repeat;
		background-size: 100% 100%;
	}

	.license-plate-6 {
		background: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/solidColorPic/plate_blue_white_gradient.png') no-repeat;
		background-size: 100% 100%;
	}
</style>