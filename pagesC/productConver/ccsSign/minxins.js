import {
	getOpenid,
	getCurrentCar,
	getCurrUserInfo
} from '@/common/storageUtil.js';
export default {
	data() {
		return {
			signMode: 'S_WECHAT',
			signModeIndex: 0,
			signModeList: [{
				label: '微信钱包代扣',
				value: 'S_WECHAT',
			}, {
				label: '银行卡代扣',
				value: 'CUP',
			}, ],
			wechatSignForm: {
				vehicleNo: "",
				vehicleColor: "",
				openId: getOpenid(),
				customerId: '',
				isFirst: '0',
				oldSignId: "",
				bankCardNo: '',
				encryptedRealName: '',
				encryptedCredentialId: ''
			},
			signId: '',
			serviceRate: '', // 通行服务费率
			overRate: '', //逾期服务费率
			signTypeDict: [],
			isAttorney: false, //是否同意委托扣款授权书
			benefitServiceFee: '',
			aftProductType: '', //产品转换目标类型
			froProductType: '',
		}
	},
	computed: {
		//字典 ：SILKY_SIGN_TYPE ,  01：微信钱包 02：微信指定卡，03：银联卡
		isWechatSignConfig() {

			let list = this.signTypeDict.filter(item => {
				return item.value == '01'
			})
			return !!list.length
		},
		// isWechatCardSignConfig() {

		// 	let list = this.signTypeDict.filter(item => {
		// 		return item.value == '02'
		// 	})
		// 	return !!list.length
		// },
		// isbankCardSignConfig() {

		// 	let list = this.signTypeDict.filter(item => {
		// 		return item.value == '03'
		// 	})
		// 	return !!list.length
		// },
		// 微信免密代扣签约 蓝牌及渐变绿牌
		isWechatSign() {
			if (this.curVehicleInfo && this.curVehicleInfo.vehicleColor) {
				return [0, 4].toString().includes(this.curVehicleInfo.vehicleColor)
			}
			return false
		},
		// 微信渠道银行卡签约 黄牌及黄绿双拼牌
		// isWechatCardSign() {

		// 	if (this.curVehicleInfo && this.curVehicleInfo.vehicle_color) {
		// 		return [1, 5].toString().includes(this.curVehicleInfo.vehicle_color)
		// 	}
		// 	return false
		// },
		// 银联卡签约 
		// isBankCardSign() {
		// 	if (this.curVehicleInfo && this.curVehicleInfo.vehicle_color) {
		// 		return ![0, 4, 1, 5].toString().includes(this.curVehicleInfo.vehicle_color)
		// 	}
		// 	return false
		// },
		curVehicleInfo() {
			return this.vehicleInfo
		},
		curUserInfo() {
			return getCurrUserInfo() ? getCurrUserInfo() : {};
		},
		// 微信指定银行卡状态
		// wechatCardBindState() {
		// 	// cancelStatus 0-未解约  1-待解约 2-已解约
		// 	let bindType = this.silkyInfo && this.silkyInfo.bankId == "C_WECHAT"; // 签约类型
		// 	let signStatus = bindType && (!this.silkyInfo.cancelStatus || this.silkyInfo.cancelStatus == "0") // 未取消签约
		// 	let wechatSign = bindType && (this.silkyInfo.signStatus == '2' || this.silkyInfo.signStatus == '') && this
		// 		.silkyInfo.wechatSign && this.silkyInfo.wechatSign != 'OPENED' // 微信签约状态
		// 	return (signStatus && wechatSign);
		// },
		// 存在次次顺签约关系
		isSignState() {
			let signStatus = !!this.silkyInfo && (!this.silkyInfo.cancelStatus || this.silkyInfo.cancelStatus == "0") &&
				(this.silkyInfo.signStatus == "2")
			// 微信免密 重新签约
			let isWeChatSign = signStatus && this.silkyInfo.bankId == "S_WECHAT" && this.silkyInfo.wechatSign ==
				"UNAUTHORIZED"
			// 微信指定银行卡 重新签约
			let isWeChatCardSign = signStatus && this.silkyInfo.bankId == "C_WECHAT" && this.silkyInfo.wechatSign !=
				"OPENED"
			// 银联卡 重新签约
			let isBankCardSign = signStatus && this.silkyInfo.bankId == "CUP"
			console.log(isWeChatSign, isWeChatCardSign, isBankCardSign)
			return isWeChatSign || isWeChatCardSign || isBankCardSign
		}
	},
	methods: {
		//获取详情
		getProductDetail() {
			this.isLoading = true
			let params = {
				orderId: this.converId,
			}
			this.$request.post(this.$interfaces.productConverDetail, {
				data: params
			}).then(res => {
				this.isLoading = false;
				if (res.code == 200) {
					let result = res.data.hsProductChangeRecord
					this.vehicleInfo = result
					this.$set(this.vehicleInfo, 'vehicleCode', result.vehicleNo)
					this.$set(this.vehicleInfo, 'vehicleType', result.isTrunk)
					this.$set(this.vehicleInfo, 'vehicleNationalType', result.carType)
					console.log('this.vehicleInfo', this.vehicleInfo)
					//获取费率
					this.getServiceRate();
				} else {
					uni.showModal({
						title: "提示",
						content: res.msg,
						showCancel: false,
					});
				}
			}).catch(error => {
				this.isLoading = false;
				uni.showModal({
					title: "提示",
					content: error.msg,
					showCancel: false,
				});
			})
		},
		getCardAccountAmount() {
			this.isLoading = true
			let params = {
				cardNo: this.vehicleInfo.cardNo
			}
			this.$request.post(this.$interfaces.getCardAccountAmount, {
				data: params
			}).then(res => {
				this.isLoading = false;
				if (res.code == 200) {
					console.log('获取余额', res)
					this.amount = res.data.amount
				} else {
					uni.showModal({
						title: "提示",
						content: res.msg,
						showCancel: false,
					});
				}
			}).catch(error => {
				this.isLoading = false;
				uni.showModal({
					title: "提示",
					content: error.msg,
					showCancel: false,
				});
			})
		},
		// 签约渠道类型change事件
		signModeChange(event) {

			this.signModeIndex = event.detail.value;
			this.signMode = this.signModeList[this.signModeIndex].value
			this.formData.bankNo = '';
			this.formData.hideBankNo = '';
			this.selectBankName = '';
			this.selectBankItem = {};
			// 查询服务费
			console.log('查询费率调用', event)
			this.getServiceRate();

		},
		// 微信免密代扣签约
		wechatSignHandle() {
			if (!this.isAttorney) {
				uni.navigateTo({
					url: './attorney'
				})
			} else {
				let data = {
					routePath: this.$interfaces.weChatSign.method,
					bizContent: {
						...this.wechatSignForm,
						customerId: this.customerInfo.customer_id,
						vehicleNo: this.vehicleInfo.vehicleCode,
						vehicleColor: this.vehicleInfo.vehicleColor,
					}
				};
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						console.log('车主签约返回信息=>>>>>', res)
						if (res.data.userState == 'NORMAL') {
							this.searchSilkyInfo();
							return
						}
						this.openOwnerService(res)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						})
					}
				})
			}
		},
		//用户授权/开通车主服务
		openOwnerService(res) {
			let extraData = res.data.extraData
			if (res.data.path) {
				let params = {
					appId: res.data.appId,
					path: res.data.path,
					extraData: {
						appid: extraData.appid,
						mch_id: extraData.mchId,
						nonce_str: extraData.nonceStr,
						sign_type: extraData.signType,
						sign: extraData.sign,
						trade_scene: extraData.tradeScene,
						plate_number: extraData.plateNumber,
						openid: extraData.openid,
						channel_type: extraData.channelType
					}
				}
				uni.navigateToMiniProgram({
					...params,
					success(res) {},
					fail(err) {}
				})
			}
		},
		//获取产品金额
		// getPackagePrice() {
		// 	let params = {
		// 		installType: '1', //安装方式; 1：快递邮寄,0: 网点自提
		// 		salesChannels: '0', //销售渠道;0 - C端小程序
		// 		carNo: this.curVehicleInfo.vehicle_code,
		// 		carColor: this.curVehicleInfo.vehicle_color,
		// 		productCode: this.curVehicleInfo.productType,
		// 		isTrunk: this.curVehicleInfo.vehicleType,
		// 		deviceType: this.curVehicleInfo.deviceType || '1'
		// 	}
		// 	this.$request.post(this.$interfaces.getPackagePrice, {
		// 		data: params
		// 	}).then(res => {
		// 		this.isLoading = false;
		// 		if (res.code == 200) {
		// 			this.benefitServiceFee = res.data.benefitServiceFee

		// 		} else {
		// 			uni.showModal({
		// 				title: '提示',
		// 				content: res.msg,
		// 				showCancel: false
		// 			});
		// 		}
		// 	}).catch(err => {
		// 		this.isLoading = false;
		// 		uni.showModal({
		// 			title: '提示',
		// 			content: err.msg,
		// 			showCancel: false
		// 		});
		// 	})
		// },
		getMarket(converId) {
			let params = {
				CarNo: this.curVehicleInfo.vehicleCode,
				CarColor: this.curVehicleInfo.vehicleColor,
				ProductType: this.froProductType,
				tarProductType: this.aftProductType,
				changeId: converId
			}
			this.isLoading = true
			// let params = JSON.parse(JSON.stringify(this.formData))

			this.$request.post(this.$interfaces.productMarket, {
				data: params
			}).then(res => {
				this.isLoading = false;
				if (res.code == 200) {
					console.log('营销方案', res)
					if (res.data) {
						this.createUserSign(converId, res.data.marketId)
					} else {
						uni.showModal({
							title: "提示",
							content: '获取不到营销方案！',
							showCancel: false,
						});
					}
				} else {
					uni.showModal({
						title: "提示",
						content: res.msg,
						showCancel: false,
					});
				}
			}).catch(error => {
				this.isLoading = false;
				uni.showModal({
					title: "提示",
					content: error.msg,
					showCancel: false,
				});
			})

		},
		createUserSign(converId, marketId) {
			// console.log('签约==========>>>>', this.curVehicleInfo.id)
			this.isLoading = true;
			// let carNoColor = this.vehicleColors[this.curVehicleInfo.vehicleColor] + '色'

			let params = {
				source: '1', //1存在etc用户
				// orderId: this.converId,
				customerId: this.customerInfo.customer_id,
				// custType: '2',
				vehicles: {
					vehicleCode: this.vehicleInfo.vehicleCode,
					vehicleColor: this.vehicleInfo.vehicleColor,
				},
				signName: this.customerInfo.customer_name,
				signPhone: this.customerInfo.link_mobile,
				signIdNo: this.customerInfo.certificates_code,
				// redirectUrl: 'https://portal.gxetc.com.cn/agreement?vehicleCode=' + encodeURIComponent(this
				// 	.curVehicleInfo
				// 	.vehicleCode) + '&vehicleColor=' + encodeURIComponent(carNoColor),
				productType: this.aftProductType,
				// saleAmount: this.benefitServiceFee,
				marketId: marketId,
				businessType: '5', //5产品转换
				isOwner: '1' //1本人，0代签,产品转换默认本人
			}

			console.log('prams===========>', params)

			let data = {
				data: params,
			};

			this.$request
				.post(this.$interfaces.getSignPreview, data)
				.then((res) => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('签章返回===>>>', res)
						let signKey = res.data.signKey
						//设置缓存key
						uni.setStorageSync('signKey', signKey)
						uni.setStorageSync('applyType', {
							signType: '2',
							cardNo: this.vehicleInfo.cardNo
						}) //2产品转换
						let pdfInfo = res.data.data
						let signUrl = 'https://portal.gxetc.com.cn/new-agreement?btnType=C&type=sign&signInfo=' +
							encodeURIComponent(JSON.stringify(
								pdfInfo))

						uni.reLaunch({
							url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
								.stringify(
									signUrl))
						})
					} else {
						uni.showModal({
							title: "错误",
							content: res.msg,
							showCancel: false,
						});
					}
				})
				.catch((err) => {
					this.isLoading = false;
					uni.showModal({
						title: "错误",
						content: err.msg,
						showCancel: false,
					});
				});
		},
		//校验合同签署状态
		getSignStatus(callback) {
			console.log('签约状态查询==========>>>>')
			this.isLoading = true;
			// let params = {
			// 	otherId: this.customerInfo.customer_id,
			// 	vehicleCode: this.curVehicleInfo.vehicleCode,
			// 	vehicleColor: this.curVehicleInfo.vehicleColor,
			// 	cardType: this.aftProductType, //-1是八桂卡，其他是卡类型
			// 	signVersion: 'V2',
			// 	businessType: '5', //产品转换5
			// }
			// let data = {
			// 	data: params,
			// };
			let data = {
				routePath: this.$interfaces.searchSignStatus.method,
				bizContent: {
					customerId: this.customerInfo.customer_id,
					vehicleCode: this.curVehicleInfo.vehicleCode,
					vehicleColor: this.curVehicleInfo.vehicleColor,
					cardType: this.aftProductType, //-1是八桂卡，其他是卡类型
					signVersion: 'V2',
					businessType: '5', //产品转换5
				}
			};

			console.log('入参', data)
			this.$request
				.post(this.$interfaces.issueRoute, {
					data: data
				}).then((res) => {
					this.isLoading = false;
					if (res.code == 200) {
						if (res.data.isNecessary && !res.data.conStatus) {
							//需要签署协议。
							callback(true)
						} else {
							callback(false)
						}
					} else {
						uni.showModal({
							title: "错误",
							content: res.msg,
							showCancel: false,
						});
					}
				})
				.catch((err) => {
					this.isLoading = false;
					uni.showModal({
						title: "错误",
						content: err.msg,
						showCancel: false,
					});
				});
		},
		//表单校验
		wechatSignValidate() {
			if (!this.selectBankItem.type) {
				uni.showModal({
					title: '提示',
					content: '请选择签约银行',
					showCancel: false
				})
				return false
			}
			if (!this.wechatSignForm.encryptedRealName) {
				uni.showModal({
					title: '提示',
					content: '请输入当前微信实名认证的姓名',
					showCancel: false
				})
				return false
			}
			if (!this.wechatSignForm.encryptedCredentialId) {
				uni.showModal({
					title: '提示',
					content: '请输入当前微信实名认证的证件号',
					showCancel: false
				})
				return false
			}
			if (!this.formData.bankNo) {
				uni.showModal({
					title: '提示',
					content: '请输入银行卡号',
					showCancel: false
				})
				return false
			}
			let reg = /^(\d{14,19})$/g
			if (!reg.test(this.formData.bankNo)) {
				uni.showModal({
					title: "提示",
					content: '银行卡号长度不合法，重新识别或重新输入',
					showCancel: false,
				});
				return
			}
			return true
		},
		// 微信渠道银行卡签约
		// wechatCardSignHandle() {
		// 	if (!this.wechatSignValidate()) return
		// 	let params = JSON.parse(JSON.stringify(this.wechatSignForm))
		// 	if (params.isFirst == '0') {
		// 		delete params.oldSignId
		// 	}
		// 	let data = {
		// 		routePath: this.$interfaces.weChatCardSign.method,
		// 		bizContent: {
		// 			...params,
		// 			openId: getOpenid(),
		// 			bankCardNo: this.formData.bankNo,
		// 			bankCode: this.selectBankItem.bankCode,
		// 			customerId: this.customerInfo.customer_id,
		// 			vehicleNo: this.vehicleInfo.vehicle_code,
		// 			vehicleColor: this.vehicleInfo.vehicle_color,
		// 		}
		// 	};
		// 	this.$request.post(this.$interfaces.issueRoute, {
		// 		data: data
		// 	}).then(res => {
		// 		if (res.code == 200) {
		// 			let data = res.data
		// 			this.signId = data.signId
		// 			uni.navigateToMiniProgram({
		// 				appId: data.appId,
		// 				path: data.path,
		// 				extraData: {
		// 					preopen_id: data.preopenId
		// 				},
		// 				success() {
		// 					// 这里是success回调函数
		// 				}
		// 			})
		// 		} else {
		// 			uni.showModal({
		// 				title: '提示',
		// 				content: res.msg,
		// 				showCancel: false
		// 			})
		// 		}
		// 	})
		// },
		// 查询服务费
		getServiceRate() {
			this.serviceRate = '';
			let scene = 'BANK_CARD'
			// 微信免密代扣
			if (this.isWechatSign && this.signMode == 'S_WECHAT') {
				scene = 'WECHAT_PLAT'
			}
			// 微信指定卡签约
			if (this.isWechatCardSign && this.selectBankItem.type == "C_WECHAT") {
				scene = 'WECHAT_CARD'
			}
			let data = {
				routePath: this.$interfaces.serviceRate.method,
				bizContent: {
					scene: scene, //WECHAT_PLAT 微信车主平台,WECHAT_CARD 微信-指定卡,BANK_CARD 银联卡
					vehicleNo: this.curVehicleInfo.vehicleCode,
					vehicleColor: this.curVehicleInfo.vehicleColor
				}
			};
			console.log('查询费率接口调用了吗==========>>>>>>>>>>>>>', data)
			this.$request.post(this.$interfaces.issueRoute, {
				data: data
			}).then((res) => {
				if (res.code == 200) {
					console.log('费率内容=>>>>>>', res)
					this.serviceRate = res.data.dRate;
					this.overRate = res.data.eRate
				}
			})
		},
		restartWechatCardSignHandle() {
			let _self = this;
			uni.openBusinessView({
				businessType: 'wxpayVehicleETC', //固定值
				extraData: {
					contract_id: this.silkyInfo.preopenId
				},
				success(res) {
					console.log(res, 'res')
				},
				fail(err) {

				},
				complete() {

				}
			})
		},
		// 根据银行卡号查询银行信息
		// getBankNameByCardNo(params) {
		// 	this.isLoading = true
		// 	let data = {
		// 		routePath: this.$interfaces.getBankName.method,
		// 		bizContent: params
		// 	}
		// 	return new Promise((resolve, reject) => {
		// 		this.$request
		// 			.post(this.$interfaces.issueRoute, {
		// 				data: data
		// 			})
		// 			.then((res) => {

		// 				resolve(res)
		// 			}).catch(err => {

		// 				reject(err)
		// 			})
		// 	})
		// },
		getDictHandle() {
			const enParams = {
				routePath: this.$interfaces.getDict.method,
				bizContent: {
					businessType: 'SILKY_SIGN_TYPE',
					ruleType: "ALL"
				}
			}
			this.$request.post(this.$interfaces.issueRoute, {
					data: enParams,
				}).then((res) => {
					console.log()
					let result = res.data;
					for (let i = 0; i < result.length; i++) {
						result[i].value = result[i].dictCode
						result[i].label = result[i].dictName
					}
					this.signTypeDict = result;
					if (!this.isbankCardSignConfig) {
						this.signModeList = this.signModeList.filter(item => {
							return item.value != 'CUP'
						})
					}

				})
				.catch((err) => {

				});
		}
	}
}