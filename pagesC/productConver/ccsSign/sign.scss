.weui-input-wrapper--border{
	border-radius: 8rpx;
	border: 2rpx solid #DDDDDD;
	padding: 10rpx 16rpx 10rpx 16rpx;
}	
.weui-input-wrapper--suffix{
	position: relative;
}
.weui-input-wrapper--suffix .weui-input {
    padding-right: 50rpx;
}

.weui-input-wrapper--suffix .weui-input__right {
    position: absolute;
    height: 100%;
    right: 20rpx;
    top: 0;
	display: flex;
	-moz-box-align: center;
	-webkit-box-align: center;
	box-align: center;
	align-items: center;
	-webkit-align-items: center;
	-moz-align-items: center;
	-moz-box-pack: center;
	-ms-box-pack: center;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-justify-content: center;
	justify-content: center;
}
.sign-container {
		padding-bottom: 20%;
		.weui-cell{
			padding: 16rpx 30rpx;
			
		}
		.weui-cell__hd{
			padding: 12rpx 0;
		}
		.weui-cell .scan-icon{
			display: block;
			width: 30rpx;
			height: 30rpx;
		}
}
.sellPay-Info {
		background-color: #ffffff;

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.cu-form-group {
		min-height: 100rpx;
	}

	.code-img {
		width: 210upx;
		height: 90upx;
		margin-right: 10upx;
	}

	.codebtn {
		
		height: 64rpx;
		// background: #0066E9;
		border-radius: 8rpx;
		min-width: 160rpx;
		font-size: 28rpx;
		font-weight: 400;
		margin-left: 10rpx;
		color: #fff;
	}


	.agreement {
		margin-top: 20rpx;
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.confirm-label {
		// color: #3e62e8;
		margin: 30rpx 0;
		font-size: 30rpx;
		font-weight: bold;
		display: flex;
		justify-content: space-between;
		.label{
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 30rpx;
			color: #999999;
			line-height: 42rpx;
			text-align: left;
		}
		.value{
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 30rpx;
			color: #333333;
			line-height: 42rpx;
			text-align: left;
		}
	}

	.confirm-content {
		padding: 0 50rpx;
		min-height: 100rpx;
	}
	.fail-content{
		margin-top: 30rpx;
		padding: 0 50rpx;
		min-height: 100rpx;
		.fail-text{
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #323435;
			line-height: 50rpx;
			text-align: left;
		}
	}

	.tips-content {
		// margin-top: 10rpx;
		// text-align: start;
		// text-indent: 2em;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 26rpx;
		color: #FE6E6E;
		line-height: 33rpx;
		text-align: left;
	}
	.fail-tips{
		justify-content: center;
		margin: 0 0 30rpx 0rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 26rpx;
		color: #FE6E6E;
		line-height: 33rpx;
	}

	.success-text {
		text-indent: 2em;
		text-align: start;
		font-size: 30rpx;
		font-weight: bold;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}


	.fail-reason {
		margin-top: 30rpx;
		padding: 26rpx;
		font-size: 28rpx;
		font-family: PingFangSC, PingFangSC-Light;
		font-weight: 400;
		color: #555555;
	}



	.bind-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.bind-Info .cu-form-group input {
		text-align: left;
	}

	.sms-tips {
		font-size: 30rpx;
		font-family: PingFangSC, PingFangSC-Light;
		font-weight: 500;
		color: #555555;
		margin-top: 10rpx;
		padding: 10rpx;
	}

	.bankList {
		color: #39b9f2;
		font-size: 28rpx;
		text-decoration: underline;
	}

	.bankImg {
		margin-right: 20rpx;

		&>img {
			width: 70rpx;
			height: 70rpx;
		}
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-cells {
		padding-top: 0;
	}

	.cu-dialog{
		border-radius: 24rpx;
		.cu-dialog_hd{
			padding: 40rpx 0 !important;
			border-bottom: 1rpx solid #e9e9e9;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #333333;
			line-height: 32rpx;
			text-align: center;
			font-style: normal;
		}
	}
	.btn-wrapper{
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 40rpx 0 60rpx 0;
		.btn{
			width: 348rpx;
			height: 71rpx;
			line-height: 71rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 30rpx;
			color: #FFFFFF;
			text-align: center;
			border-radius: 14rpx;
			background-color: #0066e9;
		}
	}
	
	.img-wrapper{
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 30rpx 0;
		.img{
			width: 200rpx;
			height: 200rpx;
		}
	}
	
	.sign-tip{
		padding: 30rpx 30rpx 10rpx 30rpx;
		color: #a7a7a7;
	}
	
	.sign-tips{
		padding: 30rpx;
		color: #FE6E6E;
	}
	