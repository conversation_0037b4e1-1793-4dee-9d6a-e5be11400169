<template>
	<view>
		<web-view ref="webview" :src="ownPath" @message="handlePostMessage" />
	</view>
</template>

<script>
	import {
		uni_decodeURIComponent
	} from '@/common/helper.js';

	export default {
		data() {
			return {
				ownPath: '',
				vehicles: {},
			}
		},
		onLoad(options) {
			this.ownPath = uni_decodeURIComponent(options.ownPath) || ''
			this.vehicles = JSON.parse(uni_decodeURIComponent(options.vehicles)) || {}
		},
		methods: {
			// webview向外部发送消息
			handlePostMessage(evt) {
				console.log("接收到消息：" + JSON.stringify(evt.detail));
				if (evt.detail.data && evt.detail.data[0] && evt.detail.data[0].action == 'toConverSuccess') {
					// this.toNext()
				}
			},
			// toNext() {
			// 	//签完下一页
			// 	uni.redirectTo({
			// 		url: '/pagesA/newBusiness/userAgreement/agreementList'
			// 	})
			// }
		}
	}
</script>

<style lang="scss" scoped>
</style>