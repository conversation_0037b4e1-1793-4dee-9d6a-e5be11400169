<template>
	<view class="apply-after-sale">
		<view class="order-detail__title">
			<view class="title-hd">
				<view class="title-hd__label">
					订单编号：
				</view>
				<view class="title-hd__value">
					<!-- {{applyId}} -->
					{{result.id}}
				</view>
			</view>
			<view class="title-bd">
				<view class="copy" @click="copy(result.id)">
					复制
				</view>
				<!-- 				<view class="icon-question">
					<image style="width: 40rpx;height: 40rpx;margin-top: 6rpx;" src="../../static/icon-question.png"
						mode="">
					</image>
				</view> -->
			</view>
		</view>
		<view class="section">
			<view class="order-progress">
				<tTitle title="办理进度" setPadding="30" rightBtn="false">
				</tTitle>
				<view class="status-wrapper">
					<view class="status-label">
						订单状态：
					</view>
					<view class="status-value" :class="statusInfoList[result.nodeStatus].status">
						{{statusInfoList[result.nodeStatus].name}}{{refundStatus ? '(退款状态:' + refundTypeList[refundStatus] + ') 金额:' + refundAmount  : ''}}
					</view>
				</view>
				<view class="progress-tip" v-if="statusInfoList[result.nodeStatus].showInfo">
					{{statusInfoList[result.nodeStatus].statusInfo}}
				</view>
				<view class="progress-tip" v-else>
					{{result.remarks || ''}}
				</view>
			</view>
		</view>
		<view class="section">
			<vehicle :vehicleObj="vehicleObj"></vehicle>
		</view>
		<view class="user-wrapper" v-if="Object.keys(queryRes).length > 0">
			<tTitle title="用户协议" setPadding="20"></tTitle>
			<view class="sign-wrapper">
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						协议名称
					</view>
					<view class="sign-wrapper__value">
						{{queryRes.conTypeDesc}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						协议编号
					</view>
					<view class="sign-wrapper__value">
						{{queryRes.conId}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						签署人
					</view>
					<view class="sign-wrapper__value">
						{{queryRes.signName}}
					</view>
				</view>
				<view class="sign-bd">
					<view class="sign-wrapper__label">
						签署时间
					</view>
					<view class="sign-wrapper__value">
						{{queryRes.conCreateTime}}
					</view>
				</view>
				<view class="btn-wrapper">
					<view class="btn" @click="showRrt">
						协议详情
					</view>
				</view>
			</view>
		</view>

		<tButton v-if="result.nodeStatus != '502'" :buttonList="buttonList" @confirm="confirmHandle"></tButton>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import vehicle from '@/pagesC/components/vehicle/vehicle.vue';
	import tButton from '@/pagesC/components/t-button/t-button.vue'
	import tTitle from '@/pagesC/components/new-title/new-title.vue';
	import {
		getCurrUserInfo,
	} from '@/common/storageUtil.js'
	import {
		statusInfoList,
	}
	from '@/pagesC/common/optionsData.js'
	import {
		vehicleColors,
	} from "@/common/const/optionData.js";
	export default {
		components: {
			tLoading,
			vehicle,
			tTitle,
			tButton
		},
		data() {
			return {
				statusInfoList,
				vehicleColors,
				isLoading: false,
				buttonList: [{
					title: '继续转换',
					handle: 'confirm'
				}],
				refundTypeList: {
					'1': '退款中',
					'2': '退款成功',
					'3': '退款失败'
				},
				orderId: '',
				vehicleObj: {
					carNo: '',
					carColorName: '',
					cardNo: '',
					obuNo: ''
				},
				result: {},
				queryRes: {},
				amount: null,
				refundStatus: '',
				refundAmount: '',
				marketAmount: '', //实付金额
			}
		},
		onLoad(option) {
			if (option && option.orderId) {
				this.orderId = option.orderId
			}
			this.getDetail()
		},
		methods: {
			copy(value) {
				uni.setClipboardData({
					data: value
				});
			},

			showRrt() {
				if (this.queryRes.version == 'V2') {
					let newArr = []
					// console.log('val.details', val.details)

					this.queryRes.details.forEach(item => {
						newArr.push({
							procotolName: item.remark,
							procotolCacheFileUrl: item.filePath
						})
					})
					let signUrl = 'https://portal.gxetc.com.cn/new-agreement?type=preview&signInfo=' + encodeURIComponent(
						JSON
						.stringify(
							newArr))

					uni.navigateTo({
						url: "/pagesB/signWebview/signPreview?ownPath=" + encodeURIComponent(JSON
							.stringify(
								signUrl))
					})
					return
				}

				// this.isLoading = true
				// let data = {
				// 	routePath: this.$interfaces.previewContracts.method,
				// 	bizContent: {
				// 		conId: this.queryRes.contractId,
				// 		vehicleCode: this.result.vehicleNo,
				// 		vehicleColor: this.result.vehicleColor
				// 	}
				// }
				// this.$request.post(this.$interfaces.issueRoute, {
				// 	data: data
				// }).then(res => {
				// 	this.isLoading = false
				// 	if (res.code == 200) {
				// 		uni.navigateTo({
				// 			url: "/pages/uni-webview/h5-webview?ownPath=" + encodeURIComponent(res.data
				// 				.viewUrl)
				// 		})
				// 	} else {
				// 		uni.showModal({
				// 			title: "提示",
				// 			content: res.msg,
				// 			showCancel: false,
				// 		});
				// 	}
				// }).catch(err => {
				// 	this.isLoading = false
				// 	uni.showModal({
				// 		title: "错误",
				// 		content: err.msg,
				// 		showCancel: false,
				// 	});
				// })
			},
			getAgreementList() {
				// queryLastContracts
				let data = {
					routePath: this.$interfaces.selectContracts.method,
					bizContent: {
						customerId: this.result.custMastId,
						vehicleCode: this.result.vehicleNo,
						vehicleColor: this.result.vehicleColor,
						pageNum: 1,
						pageSize: 1

					}
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						console.log('queryRes', res);
						let queryRes = res.data.records
						let queryFilter = queryRes.filter(item => {
							return item.conType == this.result.aftProductType
						})
						this.queryRes = queryFilter[0] || {}

					} else {
						// uni.showModal({
						// 	title: "提示",
						// 	content: res.msg,
						// 	showCancel: false,
						// });
					}
				})
			},
			getCardAccountAmount() {
				this.isLoading = true
				let params = {
					cardNo: this.result.cardNo
				}
				this.$request.post(this.$interfaces.getCardAccountAmount, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('获取余额', res)
						this.amount = res.data.amount
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//查询次次顺签约
			searchSilkyInfo(callback) {
				if (this.isLoading) return
				let params = {
					customerId: this.result.custMastId,
						vehicleCode: this.result.vehicleNo,
					vehicleColor: this.result.vehicleColor
					}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.searchSilkyInfo, {
						data: params
					})
					.then((res) => {
						console.log(res, '查询次次顺签约信息')
						// this.isLoading = false
						if (res.code == 200) {
							let searchData = res.data[0]
							if (searchData && searchData.signStatus == '2') {
								//签约并成功
								callback(true)
							} else {
								//签约失败或者无签约信息
								callback(false)
				}
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					}).catch(err => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						})
					})

			},
			goPay(amount) {
				let selectItem = {
					deviceType: this.result.deviceType,
					remark: this.result.aftProductDesc,
					productType: this.result.aftProductType,
					amount: amount,
				}
				let formData = {
					froProductType: this.result.froProductType,
					aftProductType: this.result.aftProductType,
					vehicleColor: this.result.vehicleColor,
					vehicleNo: this.result.vehicleNo,
				}
				//需要支付，跳转到支付页面
				uni.reLaunch({
					url: '/pagesC/productConver/order?productItem=' +
						encodeURIComponent(JSON
							.stringify(selectItem)) + '&formData=' +
						encodeURIComponent(JSON
							.stringify(formData)) + '&amount=' + amount
				})
			},
			confirmHandle() {
				// this.confirm(this.result.marketId)
				if (!this.result.marketId) {
					//没有营销
					this.getMarket(this.result.id, resMarket => {
						// this.marketId = resMarket.marketId
						this.marketAmount = resMarket.amount
						this.isLoading = false;
						if (this.marketAmount > 0) {
							this.goPay(this.marketAmount)
					} else {
							this.confirm(resMarket.marketId)
					}
				})
				} else {
					if (this.result.amount == 0) {
						//免费产品，不需要支付，直接走正常流程
						this.confirm(this.result.marketId)
					} else {
						//判断支付状态
						if (this.result.payStatus == '0' || this.result.payStatus == '3') {
							//需要支付，跳转到支付页面
							this.goPay(this.result.amount)
						} else if (this.result.payStatus == '2') {
							//已支付，直接走正常流程
							this.confirm(this.result.marketId)
						} else if (this.result.payStatus == '1') {
							//支付查询接口，查询完后再走正常流程
							this.paymentQuery(this.result.id)
						}
					}

				}
			},
			confirm(id) {
				let marketId = id //从id获取
				if (this.result.aftProductType == '10') {
					//次次顺需要签约
					this.searchSilkyInfo((signStatus) => {
						if (signStatus) {
				this.getSignStatus(this.result.id, resStatus => {
					if (resStatus) {
						//没签约过,去签约
									this.createUserSign(this.result.id, marketId)
								} else {
									this.isLoading = false
									console.log('this.amount', this.amount)
									//判断是否需要进行余额退款页面操作
									if (this.refundStatus) {
										//退款字段直接去转换产品
										uni.reLaunch({
											url: '/pagesC/productConver/converSuccess'
										})
										return
									}
									if (this.amount > 0) {
										//需要退款，去余额页面退款
										//todo
										uni.navigateTo({
											url: '/pagesC/productConver/refundAmount?amount=' +
												this.amount
										})
									} else if (this.amount == 0) {
										//不需要退款，直接去产品转换
										uni.reLaunch({
											url: '/pagesC/productConver/converSuccess'
										})
									} else if (this.amount == null) {
										uni.showModal({
											title: "提示",
											content: '获取金额失败，请检查网络，或者联系客服处理！',
											showCancel: false,
										});
									}
								}
							})
						} else {
							this.isLoading = false
							//未签约，跳转首页次次顺签约
							uni.navigateTo({
								url: '/pagesC/productConver/ccsSign/index?routeType=detail&aftProductType=' +
									this.result.aftProductType
							})

						}
					})
				} else {
					this.getSignStatus(this.result.id, resStatus => {
						if (resStatus) {
							//没签约过,去签约
						// this.createUserSign(this.result.id)
							this.createUserSign(this.result.id, marketId)
					} else {
							this.isLoading = false
							console.log('this.amount', this.amount)
							//判断是否需要进行余额退款页面操作
							if (this.refundStatus) {
								//退款字段直接去转换产品
								uni.reLaunch({
									url: '/pagesC/productConver/converSuccess'
								})
								return
							}
							if (this.amount > 0) {
								//需要退款，去余额页面退款
								//todo
						uni.navigateTo({
									url: '/pagesC/productConver/refundAmount?amount=' +
										this.amount
								})
							} else if (this.amount == 0) {
								//不需要退款，直接去产品转换
								uni.reLaunch({
							url: '/pagesC/productConver/converSuccess'
						})
							} else if (this.amount == null) {
								uni.showModal({
									title: "提示",
									content: '获取金额失败，请检查网络，或者联系客服处理！',
									showCancel: false,
								});
					}
						}
				})
				}

			},
			//查询支付状态
			paymentQuery(orderId) {
				let params = {
					routePath: this.$interfaces.converPaySearch.method,
					bizContent: {
						orderId: orderId
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						let status = res.data.status;
						let statusVal = {
							1: "充值中",
							2: "充值成功",
							3: "充值失败",
						};
						if (status == '1') {
							uni.showModal({
								title: "提示",
								content: '订单支付中，请等待几分钟后点击按钮查询。',
								confirmText: '知道了',
								showCancel: false,
							});
						} else if (status == '2') {
							//查询到支付完成，继续走正常流程
							this.confirm(this.result.marketId)
						} else {
							uni.showModal({
								title: "提示",
								content: '支付失败，请前往支付页面继续支付',
								confirmText: '前往',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										this.result.payStatus = status
										this.confirmHandle()
									}
								}
							});
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				})
			},
			getDetail() {
				this.isLoading = true
				let params = {
					orderId: this.orderId,
				}
				this.$request.post(this.$interfaces.productConverDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('订单详情', res)
						let result = res.data.hsProductChangeRecord
						this.result = result
						this.refundStatus = res.data.refundStatus
						this.refundAmount = res.data.refundAmount || ''

						this.vehicleObj.carNo = result.vehicleNo
						this.vehicleObj.carColorName = result.vehicleColor
						this.vehicleObj.cardNo = result.cardNo
						this.vehicleObj.obuNo = result.obuNo

						//保存订单id
						this.$store.dispatch(
							'afterSale/setConverId',
							this.result.id
						)
						this.$store.dispatch(
							'afterSale/setAfterSaleVehicle', {
								applyTime: result.createdTime,
								carColor: result.vehicleColor,
								// carColorstr: “蓝色
								carNo: result.carNo,
								cardNo: result.cardNo,
								id: result.id,
								obuNo: result.obuNo,
								orderNo: result.orderNo,
								orderStatus: result.nodeStatus,
								orderType: "5",
								orderTypestr: '产品转换',
								vehicleType: result.isTrunk,
								vehicleTypeKind: result.carType,
								aftProductType: result.froProductType,
								accountType: result.accountType
								// vehicleTypeKindstr: "一型车”,
								// vehicleTypeStr: "客车”,
							}
						)
						this.getAgreementList()
						this.getCardAccountAmount()
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			getMarket(id, callback) {
				let params = {
					CarNo: this.result.vehicleNo,
					CarColor: this.result.vehicleColor,
					ProductType: this.result.froProductType,
					tarProductType: this.result.aftProductType,
					changeId: id
				}
				this.isLoading = true
				// let params = JSON.parse(JSON.stringify(this.formData))

				this.$request.post(this.$interfaces.productMarket, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log('营销方案', res)
						if (res.data.marketId) {
							// this.createUserSign(converId, res.data.marketId)
							callback(res.data)
						} else {
							this.isLoading = false;
							uni.showModal({
								title: "提示",
								content: '获取不到营销方案！',
								showCancel: false,
							});
						}
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})

			},
			createUserSign(converId, marketId) {
				console.log('签约==========>>>>', converId)
				// this.isLoading = true;
				// console.log('签约==========>>>>', this.vehicleColors[this.vehicleInfo.vehicleColor] + '色')
				// let carNoColor = this.vehicleColors[this.result.vehicleColor] + '色'
				// let type = 'productConver'

				let params = {
					source: '1', //存在etc用户
					customerId: this.result.custMastId,
					// custType: '1', //1 customerId必填
					vehicles: {
						vehicleCode: this.result.vehicleNo,
						vehicleColor: this.result.vehicleColor,
					},
					signName: getCurrUserInfo().customer_name,
					signPhone: getCurrUserInfo().link_mobile,
					signIdNo: getCurrUserInfo().certificates_code,
					// redirectUrl: 'https://portal.gxetc.com.cn/agreement?vehicleCode=' + encodeURIComponent(this
					// 		.result
					// 		.vehicleNo) + '&vehicleColor=' + encodeURIComponent(carNoColor) + '&type=' +
					// 	encodeURIComponent(type),
					// conType: this.result.aftProductType,
					// saleAmount: '0',
					marketId: marketId,
					businessType: '5', //5产品转换
					productType: this.result.aftProductType,
					isOwner: '1' //1本人，0代签,产品转换默认本人
				}

				let data = {
					routePath: this.$interfaces.newSignPreview.method,
					bizContent: params
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('签章返回===>>>', res)
							let signKey = res.data.signKey
							uni.setStorageSync('signKey', signKey)
							uni.setStorageSync('applyType', {
								signType: '2',
								cardNo: this.result.cardNo
							}) //2产品转换
							let pdfInfo = res.data.data
							let signUrl = 'https://portal.gxetc.com.cn/new-agreement?btnType=C&type=sign&signInfo=' +
								encodeURIComponent(JSON.stringify(
									pdfInfo))

							uni.reLaunch({
								url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
									.stringify(
										signUrl))
							})


						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},

			//校验合同签署状态
			getSignStatus(converId, callback) {
				console.log('签约状态查询==========>>>>', converId)
				this.isLoading = true;
				// let params = {
				// 	customerId: this.result.custMastId,
				// 	vehicleCode: this.result.vehicleNo,
				// 	vehicleColor: this.result.vehicleColor,
				// 	cardType: this.result.aftProductType, //-1是八桂卡，其他是卡类型
				// 	// saleAmount: '0',
				// 	signVersion: 'V2',
				// 	businessType: '5', //5产品转换
				// }
				// let data = {
				// 	data: params,
				// };

				let data = {
					routePath: this.$interfaces.searchSignStatus.method,
					bizContent: {
						customerId: this.result.custMastId,
						vehicleCode: this.result.vehicleNo,
						vehicleColor: this.result.vehicleColor,
						cardType: this.result.aftProductType, //-1是八桂卡，其他是卡类型
						// saleAmount: '0',
						signVersion: 'V2',
						businessType: '5', //5产品转换
					}
				};

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					}).then((res) => {
						console.log('res', res)
						if (res.code == 200) {
							if (res.data.isNecessary && !res.data.conStatus) {
								//需要签署协议。
								callback(true)
							} else {
								callback(false)
							}
						} else {
							this.isLoading = false;
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},

		}
	}
</script>

<style lang="scss" scoped>
	.apply-after-sale {
		padding-bottom: 180rpx;
		font-family: PingFangSC-Regular, PingFang SC;
	}

	.section {
		background: #ffffff;
		border-radius: 10rpx;
	}


	.order-detail__title {
		height: 68rpx;
		padding: 0 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #ffffff;
	}

	.title-hd {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.title-hd__label {
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.title-hd__value {
		// margin-left: 10rpx;
		color: #323435;
		font-weight: 400;
	}

	.title-bd {
		flex: 0 0 210rpx;
		width: 200rpx;
		height: 31rpx;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		border-left: 1rpx solid #C6C6C6;
	}

	.title-bd .copy {
		margin-right: 50rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #3874FF;
	}

	.section {
		margin: 20rpx;
	}

	.order-progress {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;

		.status-wrapper {
			padding: 0 30rpx;
			display: flex;
			align-items: center;

		}

		.status-label {
			flex: 0 0 150rpx;
			width: 150rpx;
		}

		.status-value {
			position: relative;
			flex: 1;
			height: 68rpx;
			line-height: 68rpx;
			padding-left: 30rpx;
			font-size: 26rpx;
			font-weight: 400;
			border-radius: 8rpx;

			.question-list {
				position: absolute;
				top: 6rpx;
				right: 8rpx;
				width: 125rpx;
				background: linear-gradient(180deg, #FF8FA4 0%, #F54455 100%);
				border-radius: 8rpx;
				border: 1rpx solid #F64C5C;
				font-size: 23rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 52rpx;
				text-align: center;
			}
		}



		.status-value.info {
			background: #F6F6F6;
			color: #333333;
		}


		.status-value.success {
			background: rgba(0, 189, 50, 0.1);
			color: #00BD32;
		}

		.status-value.warnning {
			background: rgba(255, 145, 0, 0.1);
			color: #FF9100;
		}

		.status-value.error {
			background: rgba(255, 84, 84, 0.1);
			color: #FF5454;
		}

		.progress-tip {
			padding: 30rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #999999;
		}

	}

	.user-wrapper {
		background-color: $uni-bg-color;
		margin: 20rpx;
		padding-bottom: 30rpx;
		border-radius: 12rpx;
	}

	.sign-wrapper {
		margin: 0 30rpx;
		// padding: 30rpx;
		border-radius: 12rpx;
		// border: 1rpx solid #E8E8E8;
	}

	.sign-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.sign-wrapper__label {
		flex: 0 0 187rpx;
		width: 187rpx;
		line-height: 44rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.sign-wrapper__value {
		flex: 1;
		line-height: 44rpx;
		font-size: 26rpx;
	}

	.btn-wrapper {
		padding: 20rpx 20rpx 0 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-top: 1rpx dashed #C3C3C3;
	}

	.btn {
		width: 190rpx;
		height: 58rpx;
		line-height: 58rpx;
		background: #FFFFFF;
		border-radius: 36rpx;
		border: 2rpx solid #E8E8E8;
		text-align: center;
	}
</style>