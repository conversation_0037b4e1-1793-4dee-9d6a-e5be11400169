<template>
	<view class="conver-order">
		<view class="product-order">
			<view class="desc">
				您选择转换为{{formData.aftProductType == '5'?'日日通':'次次顺'}}记账卡产品，需先支付费用，相关产品信息如下：
			</view>
			<view class="product-list">
				<view class="item">
					<view class="label">
						原产品类型
					</view>
					<view class="value">
						{{productType[formData.froProductType]}}（{{productList[0].deviceType == '1'?'基础款':'进阶款'}}）
					</view>
				</view>
				<view class="item">
					<view class="label">
						转换后产品类型
					</view>
					<view class="value">
						{{formData.aftProductType == '5'?'日日通':'次次顺'}}记账卡（{{productList[0].deviceType == '1'?'基础款':'进阶款'}}）
					</view>
				</view>
				<view class="item">
					<view class="label">
						车牌颜色
					</view>
					<view class="value">
						{{vehicleColors[formData.vehicleColor]}}
					</view>
				</view>
				<view class="item">
					<view class="label">
						车牌号
					</view>
					<view class="value">
						{{formData.vehicleNo}}
					</view>
				</view>
				<view class="item">
					<view class="label">
						姓名
					</view>
					<view class="value">
						{{userInfo.customer_name}}
					</view>
				</view>
			</view>
			<view class="card-warpper">
				<view v-for="(item,index) in productList" :key="index" class="card-item_wrapper"
					:class="formData.aftProductType == item.productType?'card-wrapper__selector' :''">
					<view class="card-item" :class="formData.aftProductType == item.productType?'card-selector' :''">
						<image class="img" v-if="!formData.aftProductType"
							:src="`../static/productConver/icon-card_${index + 1}.png`" mode=""></image>
						<image class="img"
							v-if="formData.aftProductType == '5' && formData.aftProductType == item.productType"
							:src="card1_check" mode=""></image>
						<image class="img"
							v-if="formData.aftProductType == '10' && formData.aftProductType == item.productType"
							:src="card2_check" mode=""></image>
						<image class="img"
							v-if="formData.aftProductType == '5' && formData.aftProductType != item.productType"
							:src="card2" mode=""></image>
						<image class="img"
							v-if="formData.aftProductType == '10' && formData.aftProductType != item.productType"
							:src="card1" mode=""></image>
						<view class="card-text">
							<view class="card-name">
								{{item.productType == '5'?'日日通':'次次顺'}}记账卡
							</view>
							<view class="card-desc">
								{{item.productType == '5'?'预存式记账卡，灵活多样的充值方式':'绑定银行卡代扣，先通行后扣费'}}
							</view>
						</view>
					</view>
					<view class="ccs-content" v-if="item.productType == formData.aftProductType">
						<view class="content-card">
							<view class="card-title">
								捷通{{item.productType == '5'?'日日通':'次次顺'}}记账卡（{{item.deviceType == '1'?'基础款':'进阶款'}}）
							</view>
							<view class="card-content">
								<image class="img" src="../static/productConver/ccs_product.png"
									style="width: 190rpx;height: 120rpx;" mode=""></image>
								<view class="card-text">
									<!-- 	<view class="text">
										1、绑定微信支付账户代扣
									</view>
									<view class="text" style="margin-top: 24rpx;">
										2、先通行后付费
									</view> -->
									<view class="text">
										<text style="font-weight: bold;">*权益服务内容：</text>
										{{item.remark}}
									</view>
								</view>
								<view class="price">
									￥{{moneyFilter(item.amount)}}
								</view>
							</view>
							<!-- 	<view class="card-tips">
								*权益服务内容:{{item.remark}}
							</view> -->
						</view>
					</view>
				</view>
			</view>
		</view>
		<tButton :buttonList="buttonList" @confirm="confirmHandle"></tButton>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import {
		vehicleColors,
	} from "@/common/const/optionData.js";
	import {
		getCurrUserInfo,
		getOpenid,
		setOpenid
	} from '@/common/storageUtil.js'
	import tLoading from '@/components/common/t-loading.vue';
	import tButton from '@/pagesC/components/t-button/t-button.vue'
	export default {
		components: {
			tLoading,
			tButton,
		},
		data() {
			return {
				vehicleColors,
				isLoading: false,
				card1: '../static/productConver/icon-card_1.png',
				card2: '../static/productConver/icon-card_2.png',
				card1_check: '../static/productConver/icon-card_1_check.png',
				card2_check: '../static/productConver/icon-card_2_check.png',
				buttonList: [{
					title: '确定支付',
					handle: 'confirm'
				}],
				userInfo: {},
				formData: {},
				productList: [],
				// amount: '', //实付金额
				// payData: {
				// 	"orderId": "",
				// 	"payType": "",
				// 	"authCode": "",
				// 	"payMoney": "",
				// 	"actualMoney": "",
				// 	"openId": "",
				// 	"userNo": "",
				// 	"mobileCode": "",
				// 	"mobile": ""
				// },
				payData: {
					payType: '10000601',
					orderId: '',
					openId: '',
					actualMoney: ''

				},
				productType: {
					'3': '预付费绑定记账卡',
					'4': '后付费绑定记账卡',
					'5': '捷通日日通记账卡',
					'9': '捷通日日通记账卡',
					'10': '捷通次次顺记账卡'
				}
			}
		},
		onLoad(option) {
			// console.log('option', option.productItem)
			// console.log('optionformData', option.formData)
			let converId = this.$store.getters['afterSale/converId']
			this.converId = converId
			const productList = JSON.parse(decodeURIComponent(option.productItem))
			const formData = JSON.parse(decodeURIComponent(option.formData))
			const amount = option.amount
			console.log('productList', productList)
			console.log('formData', formData)
			this.userInfo = getCurrUserInfo()
			this.payData.orderId = this.converId //订单id
			this.payData.actualMoney = amount //实付金额
			this.payData.openId = getOpenid() || ''
			this.formData = formData
			this.productList.push(productList)

		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
		},
		methods: {
			confirmHandle() {
				this.payment()
			},
			payment() {
				let _self = this;
				this.isLoading = true;
				let params = JSON.parse(JSON.stringify(this.payData))
				let data = {
					routePath: this.$interfaces.converPay.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let data = res.data;
						let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};
						//拉起微信支付
						wx.requestPayment({
							...payMessage,
							"success": function(res) {
								console.log(res);
								_self.isLoading = true;
								//异步调用，确保能查到状态
								setTimeout(() => {
									_self.paymentQuery(data);
								}, 6000)
							},
							"fail": function(res) {
								// let msg = res.errMsg || ''
								// if (!msg) return
								// uni.showModal({
								// 	title: '提示',
								// 	content: msg
								// })
							},
							"complete": function(res) {}
						})
					} else {

						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
						this.$logger.info('产品转换支付请求参数：', JSON.stringify(params))
						this.$logger.error('产品转换支付返回结果：', JSON.stringify(res))
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//查询支付状态
			paymentQuery(data) {
				let params = {
					routePath: this.$interfaces.converPaySearch.method,
					bizContent: {
						orderId: data.orderId
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						let status = res.data.status;
						let statusVal = {
							1: "支付中",
							2: "支付成功",
							3: "支付失败",
						};
						let msg = statusVal[status] || "支付失败";
						uni.showModal({
							title: "提示",
							content: msg + '！（支付可能会有延迟）是否查看订单详情',
							confirmText: '查看',
							showCancel: false,
							success: function(res) {
								if (res.confirm) {
									uni.redirectTo({
										url: '/pagesC/productConver/orderDetail?orderId=' +
											data
											.orderId
									});
								}
							}
						});
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				})
			},
			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}
						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)
								}
							}
						})
					}
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.conver-order {
		font-family: PingFang SC, PingFang SC;
		overflow: hidden;

		.product-order {
			margin: 20rpx;
			// height: 300rpx;
			background: $uni-bg-color;
			border-radius: 12rpx;
			padding: 30rpx;
		}

		.desc {
			margin-bottom: 30rpx;
			font-weight: 600;
			font-size: 30rpx;
			color: #323435;
			line-height: 42rpx;
			text-align: left;
			font-style: normal;
		}

		.product-list {
			.item {
				display: flex;
				align-items: center;

				.label {
					flex: 0 0 200rpx;
					width: 200rpx;
					line-height: 44rpx;
					font-size: 26rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;
				}

				.value {
					flex: 1;
					line-height: 44rpx;
					font-size: 26rpx;
				}
			}
		}




		.card-warpper {
			margin-top: 20rpx;

			.card-item_wrapper {
				&:first-child {
					margin-bottom: 20rpx;
				}
			}

			.card-item {
				margin-bottom: 20rpx;
				display: flex;
				align-items: center;
				height: 128rpx;
				background: #F8F8F8;
				border-radius: 12rpx;
				padding: 25rpx 33rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #323435;

				&>image {
					margin-right: 28rpx;
					width: 56rpx;
					height: 56rpx;
				}

				.card-name {
					font-size: 28rpx;
					line-height: 40rpx;

				}

				.card-desc {
					line-height: 33rpx;
					font-size: 24rpx;
				}

			}

			.card-selector {
				background: #E4EFFF;
				border-radius: 12rpx;

				// border: 2rpx solid #009ff6;
				// border-image: linear-gradient(180deg, rgba(0, 159, 246, 1), rgba(0, 102, 233, 1)) 2 2;
				// clip-path: inset(0 round 12rpx);
			}

			.card-wrapper__selector {
				border: 1rpx solid #C2DDFF;
			}
		}

		.ccs-content {
			margin-top: -24rpx;
			font-family: PingFangSC, PingFang SC;
			background: #F6FAFF;
			border-radius: 0rpx 0rpx 15rpx 15rpx;
			// border: 1rpx solid #C2DDFF;

			.card-title {
				padding: 30rpx;
				font-weight: 500;
				font-size: 28rpx;
				color: #323435;
				line-height: 40rpx;
			}

			.card-content {
				display: flex;
				align-items: center;
				justify-content: center;

				.img {
					flex: 0 0 190rpx;
					width: 190rpx;
					margin-left: 30rpx;
				}

				.card-text {
					font-weight: 400;
					font-size: 24rpx;
					color: #323435;
					line-height: 33rpx;
					margin-bottom: 20rpx;
				}

				.price {
					margin-left: auto;
					height: 81rpx;
					padding: 0 10rpx;
					line-height: 81rpx;
					text-align: center;
					background: #FF9D09;
					border-radius: 100rpx 0rpx 0rpx 100rpx;
					font-weight: 500;
					font-size: 28rpx;
					color: #FFFFFF;
				}
			}

			.card-tips {
				margin: 30rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #888888;
				line-height: 33rpx;
				// text-align: center;
			}
		}

		.content {
			padding: 30rpx;
		}
	}
</style>