<template>
	<view class="order-success g-flex g-flex-center g-flex-align-center g-flex-column">
		<image class="img" v-if="orderStatusFlg!='' && orderStatusFlg == '1'"
			src="../static/productConver/pic_success.png" mode=""></image>
		<image class="img" v-if="orderStatusFlg!='' && orderStatusFlg == '0'" src="../static/productConver/pic_fail.png"
			mode=""></image>
		<view class="success-desc" v-if="orderStatusFlg!='' && orderStatusFlg == '1'">
			<view class="">
				转换成功，您<text style="font-weight: bold;">{{result.remarks}}</text>
			</view>
			<template v-if="Object.keys(result).length > 0">
				<view style="margin-top: 30rpx;" v-if="result.aftProductType != '10'">
					您当前的车辆为[<text
						style="color: #F65B5B;">{{getVehicleClassType(result.carType)}}{{getVehicleType(result.isTrunk)}}车]</text>
					，消费结算保证金最低缴存标准为[<text
						style="color: #F65B5B;">{{moneyFilter(blackLine || '')}}</text>]元。请您确保，在通行高速前，ETC卡余额不低于最低缴存标准。
				</view>
				<view style="margin-top: 30rpx;" v-if="result.aftProductType == '10'">
					使用前，请您确保代扣账户内留有足够金额，用于支付您的ETC消费费用。
				</view>
				<view style="margin-top: 30rpx;" v-if="result.froProductType == '4'">
					如您在原渠道[<text style="color: #F65B5B;">{{channelName || ''}}</text>]办理时有预留押金，请通过原渠道申请退款。联系方式
				</view>
			</template>
		</view>
		<view class="fail-desc" v-if="orderStatusFlg!='' && orderStatusFlg == '0'">
			<view style="margin-top: 30rpx;">
				转换失败，失败原因：
			</view>
			<view class="fail-reson" style="margin-top: 30rpx;">
				状态码:[{{failRes.code}}]{{failRes.msg}}
			</view>
			<view style="margin-top: 30rpx;">
				请截图联系<text class="kf" @click="toWebView">在线客服</text>或拨打客服热线<text class="link"
					@click="call('0771-5896333')">0771-5896333</text>处理，处理完成后，
				您可通过【服务订单】- 选择此车辆的产品转换订单，继续完成转换。
			</view>
		</view>
		<tButton v-if="orderStatusFlg!='' && orderStatusFlg == '1' && result.aftProductType != '10'"
			:buttonList="buttonList" @toHome="toHome" @toRecharge="toRecharge">
		</tButton>
		<tButton v-if="(orderStatusFlg!='' && orderStatusFlg == '0') || result.aftProductType == '10'"
			:buttonList="failButtionList" @toHome="toHome">
		</tButton>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tButton from '@/pagesC/components/t-button/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getVehicleType,
		getVehicleClassType,
	} from '@/common/const/optionData.js';
	import {
		getTicket,
	} from '@/common/storageUtil.js';
	export default {
		components: {
			tButton,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				converId: '',
				result: {},
				buttonList: [{
					title: '返回首页',
					handle: 'toHome'
				}, {
					title: '前往充值',
					handle: 'toRecharge'
				}],
				failButtionList: [{
					title: '返回首页',
					handle: 'toHome'
				}],
				failRes: null,
				orderStatusFlg: '',
				blackLine: '',
				channelName: '',
			}
		},
		onLoad() {
			let converId = this.$store.getters['afterSale/converId']
			this.converId = converId
			// this.converUpdate()
			this.getDetail()
		},
		methods: {
			getVehicleType,
			getVehicleClassType,
			toWebView() {
				if (!getTicket()) {
					uni.showModal({
						title: '提示',
						content: '请先登录',
						success: (res) => {
							if (res.confirm) {
								uni.reLaunch({
									url: '/pagesD/login/p-login'
								})
							}
						}
					})
					return
				}
				var callcenter =
					'https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027'
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' +
						encodeURIComponent(callcenter)
				})
			},
			//去充值页面判断
			toRecharge() {
				if (Object.keys(this.result).length > 0) {
					let vehicleInfo = {
						vehicleCode: this.result.vehicleNo,
						vehicleColor: this.result.vehicleColor,
						cardNo: this.result.cardNo
					}
					this.$goToRecharge(vehicleInfo)
				} else {
					this.toHome()
				}
			},
			toHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>/p-home'
				})
			},
			call(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber: phoneNumber //仅为示例
				});
			},
			getDetail(type) {
				this.isLoading = true
				let params = {
					orderId: this.converId,
				}
				this.$request.post(this.$interfaces.productConverDetail, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('订单详情', res)
						let result = res.data.hsProductChangeRecord
						this.result = result
						this.blackLine = res.data.blackLine
						this.channelName = res.data.channelName
						if (type == 'update') {
							//更新列表时不调用
							return
						}
						this.getSignStatus(this.converId, (res) => {
							if (res) {
								//未签约，等待签约
								uni.showModal({
									title: '提示',
									content: '您的电子协议正在签约中，请稍作等待...',
									showCancel: false,
									success: (res) => {
										if (res.confirm) {
											this.getDetail()
										}
									}
								});
							} else {
								this.converUpdate()
							}

						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//校验合同签署状态
			getSignStatus(converId, callback) {
				// console.log('签约状态查询==========>>>>', converId)
				this.isLoading = true;

				let data = {
					routePath: this.$interfaces.searchSignStatus.method,
					bizContent: {
						customerId: this.result.custMastId,
						vehicleCode: this.result.vehicleNo,
						vehicleColor: this.result.vehicleColor,
						cardType: this.result.aftProductType, //-1是八桂卡，其他是卡类型
						// saleAmount: '0',
						signVersion: 'V2',
						businessType: '5', //5产品转换
					}
				}

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					}).then((res) => {
						console.log('签约查询返回', res)
						this.isLoading = false;
						if (res.code == 200) {
							if (res.data.isNecessary && !res.data.conStatus) {
								//需要签署协议。
								callback(true)
							} else {
								callback(false)
							}
						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			converUpdate() {
				this.isLoading = true
				let params = {
					id: this.converId,
				}
				this.$request.post(this.$interfaces.productConverUpdate, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.orderStatusFlg = '1' //成功
						this.getDetail('update')
					} else {
						this.orderStatusFlg = '0' //失败
						this.failRes = res
					}
				}).catch(error => {
					this.isLoading = false;
					this.orderStatusFlg = '0' //失败
					let errObj = {
						code: '999',
						msg: '转换异常，请返回订单重试。'
					}
					this.failRes = errObj
					// uni.showModal({
					// 	title: "提示",
					// 	content: error.msg,
					// 	showCancel: false,
					// });
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-success {
		padding-top: 60rpx;
		margin: 20rpx;
		border-radius: 12rpx;
		background-color: #ffffff;

		.success-desc,
		.fail-desc {
			padding: 50rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #323435;
		}

		.img {
			width: 452rpx;
			height: 437rpx;
		}
	}

	.link {
		font-weight: 700;
		margin-top: 30rpx;
		text-align: center;
		color: #3E98FF;
		text-decoration: underline;
		text-underline-offset: 5rpx;
	}

	.kf {
		font-weight: 700;
		margin-top: 30rpx;
		text-align: center;
		color: #3E98FF;
	}
</style>