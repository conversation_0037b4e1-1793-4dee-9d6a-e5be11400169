<template>
	<view class="product-conver__apply">
		<view class="product-select">
			<title title="产品选择" rightBtn="false"></title>
			<view class="card-warpper">
				<view v-for="(item,index) in productList" :key="index" @click="selectGxCard(item)"
					class="card-item_wrapper"
					:class="formData.aftProductType == item.productType?'card-wrapper__selector' :''">
					<view class="card-item" :class="formData.aftProductType == item.productType?'card-selector' :''">
						<image class="img" v-if="!formData.aftProductType"
							:src="`../static/productConver/icon-card_${index + 1}.png`" mode=""></image>
						<image class="img"
							v-if="formData.aftProductType == '5' && formData.aftProductType == item.productType"
							:src="card1_check" mode=""></image>
						<image class="img"
							v-if="formData.aftProductType == '10' && formData.aftProductType == item.productType"
							:src="card2_check" mode=""></image>
						<image class="img"
							v-if="formData.aftProductType == '5' && formData.aftProductType != item.productType"
							:src="card2" mode=""></image>
						<image class="img"
							v-if="formData.aftProductType == '10' && formData.aftProductType != item.productType"
							:src="card1" mode=""></image>
					<view class="card-text">
						<view class="card-name">
								{{item.productTypeName}}
						</view>
						<view class="card-desc">
								{{item.productType == '5'?'预存式记账卡，灵活多样的充值方式':'绑定银行卡代扣，先通行后扣费'}}
						</view>
					</view>
				</view>
					<view class="ccs-content" v-if="item.productType == formData.aftProductType">
						<view class="content-card">
							<view class="card-title">
								捷通{{item.productType == '5'?'日日通':'次次顺'}}记账卡（{{item.deviceType == '1'?'基础款':'进阶款'}}）
			</view>
							<view class="card-content">
								<image class="img" src="../static/productConver/ccs_product.png"
									style="width: 190rpx;height: 120rpx;" mode=""></image>
								<view class="card-text">
									<!-- 	<view class="text">
										1、绑定微信支付账户代扣
		</view>
									<view class="text" style="margin-top: 24rpx;">
										2、先通行后付费
									</view> -->
									<view class="text">
										<text style="font-weight: bold;">*权益服务内容：</text>
										{{item.remark}}
									</view>
								</view>
								<view class="price">
									￥{{moneyFilter(item.amount)}}
								</view>
							</view>
							<!-- 	<view class="card-tips">
								*权益服务内容:{{item.remark}}
							</view> -->
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="product-select" v-if="selectItem">
			<title title="产品说明" rightBtn="false"></title>
			<view class="product-tips">
				<view class="content" v-html="selectItem.productMsg"></view>
			</view>
		</view>
		<smsDialog ref="smsDialog" :show.sync="showSmsDialog" :msgType="'productConver'" :mobile="mobile" @confirm="smsConfirm">
		</smsDialog>
		<tButton v-if="selectItem" :buttonList="buttonList" @confirm="confirmHandle"></tButton>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import title from '@/pagesC/components/new-title/new-title.vue'
	import smsDialog from '@/pagesC/components/logout-dialog/sms-dialog.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import tButton from '@/pagesC/components/t-button/t-button.vue'
	import {
		getCurrUserInfo,
		getAccountId,
	} from '@/common/storageUtil.js'
	import {
		// getProvince,
		// provincesOptions,
		vehicleColors,
		// vehicleType,
		// getVehicleClassType
	} from "@/common/const/optionData.js";

	export default {
		components: {
			tLoading,
			tButton,
			smsDialog,
			title
		},
		data() {
			return {
				vehicleColors,
				isLoading: false,
				showSmsDialog: false,
				card1: '../static/productConver/icon-card_1.png',
				card2: '../static/productConver/icon-card_2.png',
				card1_check: '../static/productConver/icon-card_1_check.png',
				card2_check: '../static/productConver/icon-card_2_check.png',
				buttonList: [{
					title: '确定转换',
					handle: 'confirm'
				}],
				mobile: '',
				formData: {
					accountType: '0',
					vehicleNo: '',
					vehicleColor: '',
					carType: '',
					cardNo: '',
					obuNo: '',
					isTrunk: '',
					froProductType: '',
					aftProductType: '',
					applyChannel: '2',
				},
				// content: '', //产品说明
				productList: [],
				selectItem: null,
				amount: null,
				marketId: '',
				marketAmount: null
			}
		},
		onLoad() {
			let vehicleInfo = this.$store.getters['afterSale/afterSaleVehicle']
			console.log('getCurrUserInfo', getCurrUserInfo(), vehicleInfo)
			this.mobile = getCurrUserInfo().link_mobile
			this.formData.accountType = getCurrUserInfo().customer_type
			this.formData.vehicleNo = vehicleInfo.vehicleCode
			this.formData.vehicleColor = vehicleInfo.vehicleColor
			this.formData.carType = vehicleInfo.vehicleClass
			this.formData.cardNo = vehicleInfo.cardNo
			this.formData.obuNo = vehicleInfo.obuNo
			this.formData.isTrunk = vehicleInfo.vehicleType
			this.formData.froProductType = vehicleInfo.cardProduct

			// this.getProductConfig()  //测试先注释
			this.getProductList()
			// if (this.formData.froProductType == '5' || this.formData.froProductType == '9') {
			this.getCardAccountAmount()
			// }

			this.getAccountInfo()

		},
		methods: {
			// 获取互联网账户信息
			getAccountInfo() {
				let params = {
					routePath: this.$interfaces.getAccountView.method,
					bizContent: {
						custMastId: getCurrUserInfo().customer_id,
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log('查询互联网用户信息=>>>>>>>>>', res)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch((error) => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			selectGxCard(item) {
				if (item.disabled == '1') {
					uni.showModal({
						title: "当前车辆暂不支持选择该产品。",
						content: error.msg,
						showCancel: false,
					});
					return
				}
				this.formData.aftProductType = item.productType
				this.selectItem = item
				// console.log('item===>>prodict', item)
				// if (item.gxCardType == '10' && this.formData.vehicleType && this.formData.vehicleType != '2') {
				// 	uni.showModal({
				// 		title: "提示",
				// 		content: "其他次次顺车型即将上线，敬请期待",
				// 		showCancel: false,
				// 	});
				// 	return
				// }
				// if (this.formData.gxCardType == item.gxCardType) {
				// 	this.formData.gxCardType = ''
				// } else {
				// 	this.formData.gxCardType = item.gxCardType
				// }

				// this.benefitServiceFee = item.benefitServiceFee
				// this.formData.productType = this.formData.gxCardType
				// if (this.formData.gxCardType == '10') {
				// 	this.formData.deviceType = '1'
				// 	// this.formData.vehicleType = '2'
				// } else if (this.formData.gxCardType == '') {
				// 	this.formData.deviceType = ''
				// }
			},
			getProductConfig() {
				//产品转换配置
				this.$request.post(this.$interfaces.productConfig).then(res => {
					// this.isShowNeedKnow = false
					console.log('产品转换配置====>>>>>', res)
					if (res.code == 200) {
						this.content = res.data.ddpInstructions
					}
				}).catch(err => {})
			},
			getCardAccountAmount() {
				this.isLoading = true
				let params = {
					cardNo: this.formData.cardNo
				}
				this.$request.post(this.$interfaces.getCardAccountAmount, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('获取余额', res)
						this.amount = res.data.amount
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			getProductList() {
				this.isLoading = true
				let params = {
					cardNo: this.formData.cardNo
				}
				this.$request.post(this.$interfaces.productApplyList, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('产品转换成品表', res)
						this.productList = res.data
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			confirmHandle() {
				//弹出验证码
				this.showSmsDialog = true
				this.$refs.smsDialog.getCaptcha()
			},
			smsConfirm(mobileCode) {
				this.isLoading = true
				let params = {
					mobile: this.mobile,
					mobileCode: mobileCode
				}
				this.$request.post(this.$interfaces.smsCheckCodeV1, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('身份验证', res)
						this.showSmsDialog = false
						this.confirm()
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			//查询次次顺签约
			searchSilkyInfo(callback) {
				if (this.isLoading) return
				let params = {
					customerId: getCurrUserInfo().customer_id,
					vehicleCode: this.formData.vehicleNo,
					vehicleColor: this.formData.vehicleColor
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.searchSilkyInfo, {
						data: params
					})
					.then((res) => {
						console.log(res, '查询次次顺签约信息')
						if (res.code == 200) {
							let searchData = res.data[0]
							if (searchData && searchData.signStatus == '2') {
								//签约并成功
								callback(true)
							} else {
								//签约失败或者无签约信息
								callback(false)
							}
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					}).catch(err => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						})
					})

			},
			//确认转换后去签约，
			confirm() {
				this.isLoading = true
				let params = JSON.parse(JSON.stringify(this.formData))
				params.deviceType = this.selectItem.deviceType
				params.aftProductDesc = this.selectItem.remark

				this.$request.post(this.$interfaces.productApply, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('res', res)
						let converId = res.data.id
						this.$store.dispatch(
							'afterSale/setConverId',
							converId
						)

						this.getMarket(converId, resMarket => {
							this.marketId = resMarket.marketId
							this.marketAmount = resMarket.amount
							this.isLoading = false;
							if (this.marketAmount > 0) {
								//需要支付，跳转到支付页面
								uni.reLaunch({
									url: '/pagesC/productConver/order?productItem=' +
										encodeURIComponent(JSON
											.stringify(this.selectItem)) + '&formData=' +
										encodeURIComponent(JSON
											.stringify(this.formData)) + '&amount=' + this
										.marketAmount
								})
							} else {
								//不需要支付走正常流程
								if (this.formData.aftProductType == '10') {
									//次次顺才需要签约
									this.searchSilkyInfo((signStatus) => {
										if (signStatus) {
											//已签约车主，查询电子签章
						this.getSignStatus(converId, resStatus => {
							if (resStatus) {
													//没签章过,去签章
													this.createUserSign(converId)
													// this.getMarket(converId)
							} else {
													this.isLoading = false;
													//判断是否需要进行余额退款页面操作
													if (this.amount > 0) {
														//需要退款，去余额页面退款
														//todo
								uni.reLaunch({
															url: '/pagesC/productConver/refundAmount?amount=' +
																this.amount
														})
													} else if (this.amount == 0) {
														//不需要退款，直接去产品转换
														uni.reLaunch({
									url: '/pagesC/productConver/converSuccess'
								})
													} else if (this.amount == null) {
														uni.showModal({
															title: "提示",
															content: '获取金额失败，请检查网络，或者联系客服处理！',
															showCancel: false,
														});
							}
												}
						})
					} else {
											this.isLoading = false;
											//未签约，跳转首页次次顺签约
											uni.reLaunch({
												url: '/pagesC/productConver/ccsSign/index?aftProductType=' +
													this.formData.aftProductType +
													'&froProductType=' +
													this
													.formData.froProductType
											})

										}
									})
								} else {
									//其他类型
									this.getSignStatus(converId, resStatus => {
										if (resStatus) {
											//没签章过,去签章
											this.createUserSign(converId)
											// this.getMarket(converId)
										} else {
											this.isLoading = false;
											//判断是否需要进行余额退款页面操作
											if (this.amount > 0) {
												//需要退款，去余额页面退款
												//todo
												uni.reLaunch({
													url: '/pagesC/productConver/refundAmount?amount=' +
														this.amount
												})
											} else if (this.amount == 0) {
												//不需要退款，直接去产品转换
												uni.reLaunch({
													url: '/pagesC/productConver/converSuccess'
												})
											} else if (this.amount == null) {
						uni.showModal({
							title: "提示",
													content: '获取金额失败，请检查网络，或者联系客服处理！',
													showCancel: false,
												});
											}
										}
									})
								}

							}
						})

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			getMarket(converId, callback) {
				let params = {
					CarNo: this.formData.vehicleNo,
					CarColor: this.formData.vehicleColor,
					ProductType: this.formData.froProductType,
					tarProductType: this.formData.aftProductType,
					changeId: converId
				}
				this.isLoading = true
				// let params = JSON.parse(JSON.stringify(this.formData))

				this.$request.post(this.$interfaces.productMarket, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log('营销方案', res)
						if (res.data.marketId) {
							// this.createUserSign(converId, res.data.marketId)
							callback(res.data)
						} else {
							this.isLoading = false;
							uni.showModal({
								title: "提示",
								content: '获取不到营销方案！',
								showCancel: false,
							});
						}
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})

			},
			createUserSign(converId, marketId) {
				console.log('签约==========>>>>', converId)
				// this.isLoading = true;
				// console.log('签约==========>>>>', this.vehicleColors[this.vehicleInfo.vehicleColor] + '色')
				// let carNoColor = this.vehicleColors[this.formData.vehicleColor] + '色'
				// let type = 'productConver'

				let params = {
					source: '1', //1存在etc用户
					customerId: getCurrUserInfo().customer_id,
					// custType: '1', // customerId必填
					vehicles: {
						vehicleCode: this.formData.vehicleNo,
						vehicleColor: this.formData.vehicleColor,
					},
					signName: getCurrUserInfo().customer_name,
					signPhone: getCurrUserInfo().link_mobile,
					signIdNo: getCurrUserInfo().certificates_code,
					// redirectUrl: 'https://portal.gxetc.com.cn/agreement?vehicleCode=' + encodeURIComponent(this
					// 		.formData
					// 		.vehicleNo) + '&vehicleColor=' + encodeURIComponent(carNoColor) + '&type=' +
					// 	encodeURIComponent(type),
					// conType: this.formData.aftProductType,
					// saleAmount: '0',
					marketId: this.marketId,
					businessType: '5', //5产品转换
					productType: this.formData.aftProductType,
					isOwner: '1' //1本人，0代签,产品转换默认本人
				}

				// console.log('prams===========>', params)

				// let data = {
				// 	data: params,
				// };
				let data = {
					routePath: this.$interfaces.newSignPreview.method,
					bizContent: params
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('签章返回===>>>', res)
							let signKey = res.data.signKey
							uni.setStorageSync('signKey', signKey)
							uni.setStorageSync('applyType', {
								signType: '2',
								cardNo: this.formData.cardNo
							}) //2产品转换
							let pdfInfo = res.data.data
							let signUrl = 'https://portal.gxetc.com.cn/new-agreement?btnType=C&type=sign&signInfo=' +
								encodeURIComponent(JSON.stringify(
									pdfInfo))

							uni.reLaunch({
								url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
									.stringify(
										signUrl))
							})

						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},

			//校验合同签署状态
			getSignStatus(converId, callback) {
				console.log('签约状态查询==========>>>>', converId)
				this.isLoading = true;

				let data = {
					routePath: this.$interfaces.searchSignStatus.method,
					bizContent: {
						customerId: getCurrUserInfo().customer_id,
						vehicleCode: this.formData.vehicleNo,
						vehicleColor: this.formData.vehicleColor,
						cardType: this.formData.aftProductType, //-1是八桂卡，其他是卡类型
						// saleAmount: '0',
						signVersion: 'V2',
						businessType: '5', //5产品转换
					}
				}

				console.log('入参', data)
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					}).then((res) => {
						console.log('res', res)
						if (res.code == 200) {
							if (res.data.isNecessary && !res.data.conStatus) {
								//需要签署协议。
								callback(true)
							} else {
								callback(false)
							}
						} else {
							this.isLoading = false;
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},


		}
	}
</script>

<style lang="scss" scoped>
	.product-select {
		margin: 20rpx;
		// height: 300rpx;
		background: $uni-bg-color;
		border-radius: 12rpx;
		padding: 30rpx;

		.card-warpper {
			margin-top: 20rpx;

			.card-item_wrapper {
				&:first-child {
					margin-bottom: 20rpx;
				}
			}

			.card-item {
				margin-bottom: 20rpx;
				display: flex;
				align-items: center;
				height: 128rpx;
				background: #F8F8F8;
				border-radius: 12rpx;
				padding: 25rpx 33rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #323435;

				&>image {
					margin-right: 28rpx;
					width: 56rpx;
					height: 56rpx;
				}

				.card-name {
					font-size: 28rpx;
					line-height: 40rpx;

				}

				.card-desc {
					line-height: 33rpx;
					font-size: 24rpx;
				}

			}

			.card-selector {
				background: #E4EFFF;
				border-radius: 12rpx;

				// border: 2rpx solid #009ff6;
				// border-image: linear-gradient(180deg, rgba(0, 159, 246, 1), rgba(0, 102, 233, 1)) 2 2;
				// clip-path: inset(0 round 12rpx);
			}

			.card-wrapper__selector {
				border: 1rpx solid #C2DDFF;
		}
	}
	}

	.ccs-content {
		margin-top: -24rpx;
		font-family: PingFangSC, PingFang SC;
		background: #F6FAFF;
		border-radius: 0rpx 0rpx 15rpx 15rpx;
		// border: 1rpx solid #C2DDFF;

		.card-title {
			padding: 30rpx;
			font-weight: 500;
			font-size: 28rpx;
			color: #323435;
			line-height: 40rpx;
		}

		.card-content {
			display: flex;
			align-items: center;
			justify-content: center;

			.img {
				flex: 0 0 190rpx;
				width: 190rpx;
				margin-left: 30rpx;
			}

			.card-text {
				font-weight: 400;
				font-size: 24rpx;
				color: #323435;
				line-height: 33rpx;
				margin-bottom: 20rpx;
			}

			.price {
				margin-left: auto;
				height: 81rpx;
				padding: 0 10rpx;
				line-height: 81rpx;
				text-align: center;
				background: #FF9D09;
				border-radius: 100rpx 0rpx 0rpx 100rpx;
				font-weight: 500;
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}

		.card-tips {
			margin: 30rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #888888;
			line-height: 33rpx;
			// text-align: center;
		}
	}

	.content {
		padding: 30rpx;
	}
</style>