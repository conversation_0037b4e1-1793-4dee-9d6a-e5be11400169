//车辆用户类型字典
export const statusInfoList = {
	// 二次激活
	'101': { //待审核
		status: 'warning',
		statusInfo: '我们会尽快为您审核，请耐心等待',
		showInfo: true,
	},
	'102': { //审核通过
		status: 'success',
		statusInfo: '审核通过，请尽快激活',
		showInfo: true,
	},
	'103': { //审核不通过
		status: 'danger',
		showInfo: true,
	},
	'104': { //已取消
		status: 'info',
		statusInfo: '订单已取消',
		showInfo: true,
	},
	'105': { //已完结
		status: 'success',
		statusInfo: '已激活完成',
		showInfo: false,
	},
	'106': { //已关闭
		status: 'info',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	// 线上更换
	'201': { //待审核
		status: 'info',
		statusInfo: '我们会尽快审核您的售后申请，请耐心等待！',
		showInfo: true,
	},
	'202': { //审核通过
		status: 'success',
		statusInfo: '请尽快寄回整套设备，便于后续为您处理更换业务',
		showInfo: true,
	},
	'203': { //审核不通过
		status: 'danger',
		showInfo: true,
	},
	'204': { //用户设备已寄出
		status: 'success',
		statusInfo: '我们收到设备后，会尽快为您更换',
		showInfo: true,
	},
	'205': { //待确认更换类型
		status: 'normal',
		statusInfo: '正在为您处理，请耐心等待',
		showInfo: false,
	},
	'206': { //待支付
		status: 'info',
		statusInfo: '您当前需更换的设备类型为xxx，费用为xxx,请尽快支付。',
		showInfo: true,
	},
	'207': { //待更换
		status: 'normal',
		statusInfo: '正在为您处理，请耐心等待',
		showInfo: true,
	},
	'208': { //设备待寄出
		status: 'normal',
		statusInfo: '正在为您处理，请耐心等待',
		showInfo: true,
	},
	'209': { //设备已寄出
		status: 'success',
		statusInfo: '已为您寄出设备，请留意物流信息',
		showInfo: true,
	},
	'210': { //已签收待激活
		status: 'warning',
		statusInfo: '设备已送达，请尽快安装激活',
		showInfo: true,
	},
	'211': { //已完结
		status: 'success',
		statusInfo: '订单已完成',
		showInfo: true,
	},
	'212': { //已取消
		status: 'info',
		statusInfo: '订单已取消',
		showInfo: true,
	},
	// 线上补办 
	'301': { //待支付
		status: 'info',
		showInfo: false,
		statusInfo: '请尽快支付'
	},
	'302': { //订单待处理
		status: 'warning',
		statusInfo: '正在为您处理，请耐心等待',
		showInfo: true,
	},
	'303': { //设备待寄出
		status: 'normal',
		statusInfo: '正在为您处理，请耐心等待',
		showInfo: true,
	},
	'304': { //设备已寄出
		status: 'success',
		statusInfo: '已为您寄出设备，请留意物流信息',
		showInfo: true,
	},
	'305': { //已签收待激活
		status: 'warning',
		statusInfo: '请尽快安装激活设备',
		showInfo: false,
	},
	'306': { //已完结
		status: 'success',
		statusInfo: '订单已完成',
		showInfo: false,
	},
	'307': { //已取消
		status: 'info',
		statusInfo: '订单已取消',
		showInfo: false,
	},
	//线上注销
	'401': { //待审核
		name: '待审核',
		status: 'warning',
		statusInfo: '我们会尽快为您审核，请耐心等待',
		showInfo: true,
	},
	'402': { //审核通过
		name: '审核通过',
		status: 'success',
		statusInfo: '已审核通过，预计72小时内完成注销',
		showInfo: true,
	},
	'403': { //审核不通过
		name: '审核不通过',
		status: 'danger',
		// statusInfo: '我们会尽快为您审核，请耐心等待',
		showInfo: false,
	},
	'404': { //注销不成功
		name: '注销不成功',
		status: 'danger',
		statusInfo: '注销不成功',
		showInfo: true,
	},
	'405': { //已注销清算中
		name: '已注销清算中',
		status: 'warning',
		statusInfo: '已注销清算中',
		showInfo: true,
	},
	'406': { //注销退款中
		name: '注销退款中',
		status: 'warning',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'407': { //注销退款不成功
		name: '注销退款不成功',
		status: 'danger',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'408': { //已完结
		name: '已完结',
		status: 'success',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'409': { //已取消
		name: '已取消',
		status: 'info',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'410': { //注销清算异常
		name: '注销清算异常',
		status: 'danger',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'411': { //修改材料
		name: '修改材料',
		status: 'info',
		statusInfo: '您长时间未更新订单信息，已关闭此订单',
		showInfo: true,
	},
	'501': { //初始化
		name: '订单创建',
		status: 'warning',
		statusInfo: '订单已创建，请继续进行产品转换操作。',
		showInfo: true,
	},
	'502': { //转换成功
		name: '已完结',
		status: 'success',
		statusInfo: '转换成功，订单已完结',
		showInfo: false,
	},
	'503': { //转换失败
		name: '转换失败',
		status: 'danger',
		// statusInfo: '',
		showInfo: false,
	},
}

//成功页面按钮
export const successBtnList = {
	'0': {
		content: '提交成功！我们将在3个工作日内为审核，请耐心等待。', //二次激活申请
		button: [{
			title: '查看订单详情',
			handle: 'toAfterDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'1': {
		content: '您的更换申请已成功提交，我们将在3个工作日内完成审核，请耐心等待。', //更换申请
		button: [{
			title: '查看订单详情',
			handle: 'toChangeDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'2': {
		content: '您的更换申请订单已取消', //更换取消
		button: [{
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'3': {
		content: '我们收到设备后，会尽快为您进一步排查需更换的设备类型，请耐心等待。', //更换自行寄回
		button: [{
			title: '查看订单详情',
			handle: 'toChangeDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'4': {
		content: '快递小哥正在快马加鞭赶来的路上，请耐心等待。', //更换上门取件
		button: [{
			title: '查看订单详情',
			handle: 'toChangeDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'5': {
		content: '支付成功，我们将在3个工作日内为您更换并寄出新设备，请耐心等待。', //更换支付成功
		button: [{
			title: '查看办理进度',
			handle: 'toProgress'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'6': {
		content: '请前往激活。', //更换签收成功
		button: [{
			title: '前往激活',
			handle: 'toActivate'
		}]
	},
	'7': {
		content: '取消成功', //补办取消成功
		button: [{
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'8': {
		content: '请尽快激活。', //补办 设备含OBU 确认签收
		button: [{
			title: '前往激活',
			handle: 'toActivate'
		}]
	},
	'9': {
		content: '签收成功，订单待激活，建议您请将卡片插入OBU设备中，配套使用。', //补办 设备不含OBU 确认签收
		button: [{
			title: '前往激活',
			handle: 'toActivate'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'10': {
		content: '支付成功！我们将在3个工作日内为您补办设备，请耐心等待。', //补办申请
		button: [{
			title: '查看订单详情',
			handle: 'toRessiueDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'11': {
		content: '支付成功！我们将在3个工作日内为您补办设备，请耐心等待。', //补办支付成功
		button: [{
			title: '查看办理进度',
			handle: 'toProgress'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'12': {
		content: '修改信息成功，我们将在3个工作日内完成审核，请耐心等待。', //更换申请
		button: [{
			title: '查看订单详情',
			handle: 'toChangeDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'13': {
		content: '您的注销申请已成功提交，我们将在3个工作日内完成审核，请耐心等待。', //更换申请
		button: [{
			title: '查看订单详情',
			handle: 'toLogoutDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'14': {
		content: '取消成功', //更换申请
		button: [{
			title: '返回首页',
			handle: 'toHome'
		}]
	},
	'15': {
		content: '操作成功！注销结果请查看订单详情。', //继续注销操作
		button: [{
			title: '查看订单详情',
			handle: 'toLogoutDetail'
		}, {
			title: '返回首页',
			handle: 'toHome'
		}]
	},
}