<template>
	<view class="query-enter">
		<view class="scroll-wrapper">
			<scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltolower="lower"
				lower-threshold="60" @scroll="scroll">

				<view v-if="vehicleAllDataList.length > 0">
					<view style="font-size: 36rpx;color: #666666;padding:30upx 40upx;">
						{{ vehicleAllDataList.length > 2 ? '您名下存在多个车辆,请选择' : '' }}
					</view>
					<!-- <view class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;" v-if="!showSearchList"> -->
					<view class="p-a-content" style="background: #FCFCFC;border-radius: 16upx;">
						<view v-for="(item, index) in vehicleAllDataList" :key="index">
							<view @click="vehicleInfo(item, index)">
								<CarItem :carInfo="item" />
							</view>
						</view>
					</view>
					<!-- 					<view v-else>
						<view v-if="searchList.length > 0" class="p-a-content"
							style="background: #FCFCFC;border-radius: 16upx;">
							<view v-for="(item, index) in searchList" :key="index">
								<view @click="checkRealInfo(item, index)">
									<CarItem :carInfo="item" />
								</view>
							</view>
						</view>
						<view v-else class="emptySarch">未查询到车辆</view>
					</view> -->
				</view>

				<view class="tips" v-else>
					您可以
					<text class="bind" @click="goBind">绑定ETC后</text>
					，直接选择车辆进行充值，或点击下方按钮输入车牌号充值
				</view>
			</scroll-view>
		</view>
		<view class="btn-wrapper">
			<TButton v-if="isShowBtn" title="补缴其他车辆" @clickButton="rechargeOther"></TButton>
		</view>
	</view>
</template>

<script>
	import TButton from '@/components/t-button.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import CarItem from '@/pagesC/components/t-car-item-4501/t-car-item-4501.vue';
	import {
		request,
		getCarList,
		carStatus
	} from '@/common/request-1/requestFunc.js';
	import {
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getCurrUserInfo,
		setCurrUserInfo,
		getAccountId,
		setBusinessTypes,
		setStore,
		getStore,
		setEtcVehicle
	} from '@/common/storageUtil.js';
	export default {
		components: {
			tLoading,
			TButton,
			CarItem
		},
		data() {
			return {
				isShowBtn: true,
				showBind: false,
				scrollTop: 0,
				old: {
					scrollTop: 0
				},
				vehicleAllDataList: []
			}
		},
		// onLoad() {
		// 	this.getVerhicleList()
		// },
		onShow() {
			if (getAccountId()) {
				this.getVerhicleList();
			}
			if (!(getCurrUserInfo() && getCurrUserInfo().customer_id)) {
				this.showBind = true;
			}

		},
		methods: {
			getVerhicleList() {
				let data = {
					routePath: this.$interfaces.customerBizView.method,
					bizContent: {
						customer_id: getAccountId()
					}
				};
				request(
					this.$interfaces.issueRoute, {
						data: data
					},
					res => {
						console.log(res);
						if (res.code == 200) {
							setCurrUserInfo(res.data)
							this.vehicleAllDataList = res.data.vehicles || [];
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					}
				);
			},
			vehicleInfo(item, index) {
				console.log('item', item)
				this.isShowLoding = true
				let params = {
					vehicleColor: item.vehicle_color,
					vehicleCode: item.vehicle_code
				}
				console.log('入参params', params)
				this.$request
					.post(this.$interfaces.searchCustomerInfo, {
						data: params,
					})
					.then((res) => {
						this.isShowLoding = false
						if (res.code !== 200) {
							uni.showModal({
								title: '提示',
								content: res.msg
							})
							return
						}
						this.searchAfterPay(item, res.data.cardNo)
						console.log("查询结果", res.data);
						// this.custName = res.data.custName
						// this.cardNo = res.data.cardNo

					})
					.catch((error) => {
						this.isShowLoding = false
					});
			},
			searchAfterPay(item, cardNo) {
				let nextData = {
					vehicle_code: item.vehicle_code,
					vehicle_color: item.vehicle_color,
					cardNo: cardNo
				}
				uni.navigateTo({
					url: '../recordDetail/recordDetail?nextData=' + encodeURIComponent(JSON.stringify(nextData))
				})
			},
			rechargeOther() {
				uni.navigateTo({
					url: '/pagesC/afterPay/searchInfo/searchInfo'
				});
			},
			goBind() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList?type=' + 'afterPay'
				});
			},
			lower() {
				console.log('底部');
				/* this.getMoreList() */
			},
			scroll(e) {
				// console.log(e)
				this.old.scrollTop = e.detail.scrollTop;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.p-a-content {
		margin: 0 40upx 40upx 40upx;
		border-radius: 16upx;

		/deep/.t-order-account {
			background-color: #fff;
		}
	}

	.tips {
		margin: 70rpx 60rpx;

		.bind {
			color: #2993db;
			cursor: pointer;
		}
	}

	.btn-wrapper {
		position: fixed;
		left: 0;
		right: 0;
		// top: 0;
		bottom: 0;
		width: 100%;
	}
</style>
