<template>
	<view>
		<view class="sellPay-Info">
			<view class="big-title">
				用户申诉
			</view>
			<view class="c-title">问题描述(必填):</view>
			<view class="dispute_info  g-flex g-flex-center">
				<textarea class="dispute_textarea" :disabled='isDisable' v-model="dispute"
					placeholder="请尽量写清问题来源和要表达的意思,70个文字内..." maxlength=70 />
			</view>
			<view class="c-title" v-if="isDisable">申诉时间:{{applyTime}}</view>
			<view class="c-title">申诉附件(图片):</view>
			<view class="dispute_img">
				<view class="cu-bar bg-white" v-if="!isDisable">
					<view class="action">
						最多上传五张图片
					</view>
					<view class="action">
						{{imgList.length}}/5
					</view>
				</view>
				<view class="cu-form-group">
					<view class="grid col-4 grid-square flex-sub">
						<view class="bg-img" v-for="(item,index) in imgList" :key="index" @tap="ViewImage"
							:data-url="imgList[index].url">
							<image :src="imgList[index].url" mode="aspectFill"></image>
							<view v-if="!isDisable" class="cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
								<text class='cuIcon-close'></text>
							</view>
						</view>
						<view class="solids" @tap="ChooseImage()" v-if="imgList.length<5&&!isDisable">
							<text class='cuIcon-cameraadd'></text>
						</view>
					</view>
				</view>
				<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
					:size="500" :maxWidth="800" :ql="0.9" :imgcount="maxlength" type="base64">
				</cpimg>
			</view>

			<view v-if="isDetail">
				<view class="big-title" style="margin-top: 50upx;">
					处理结果
				</view>
				<view class="c-title">处理反馈:</view>
				<view class="dispute_info  g-flex g-flex-center">
					<textarea class="dispute_textarea" :disabled='isDisable' v-model="detailDispute" maxlength="70" />
				</view>
				<view class="c-title">处理时间:{{dealTime}}</view>
				<view class="c-title">处理附件:</view>
				<view class="dispute_img">
					<view class="cu-form-group">
						<view class="grid col-4 grid-square flex-sub">
							<view class="bg-img" v-for="(item,index) in detailImgList" :key="index"
								@tap="ViewDetailImage" :data-url="detailImgList[index]">
								<image :src="detailImgList[index]" mode="aspectFill"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button v-if="!isDisable" class="weui-btn weui-btn_primary" @click="sendApply">
						提交申请
					</button>
					<button v-else class="weui-btn weui-btn_primary" @click="goBack">
						返回
					</button>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>
<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import TButton from "@/components/t-button.vue"
	import tLoading from "@/components/common/t-loading.vue"
	import {
		getEtcAccountInfo
	} from '@/common/storageUtil.js'
	export default {
		name: '',
		components: {
			cpimg,
			TButton,
			tLoading
		},
		data() {
			return {
				imgList: [],
				dispute: '',
				ownFlag: 0,
				isBtnLoader: false,
				isLoading: false,
				applyId: '',
				isDisable: false,
				detailImgList: [],
				detailDispute: '',
				applyTime: '',
				dealTime: '',
				isDetail: false,
			}
		},
		computed: {
			maxlength: function() {
				return 5 - this.imgList.length
			}
		},
		onLoad(option) {
			if (option) {
				let params = JSON.parse(option.params)
				this.applyId = params.orderNo
				if (params.disputeStatus == '1' || params.disputeStatus == '2') {
					this.getDisputeDetail()
				}
			}
		},

		methods: {
			//查询用户申述详情
			getDisputeDetail() {
				let params = {
					auditBlackListId: this.applyId.toString(),
				}
				let data = {
					data: params
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.disputeDetail, data).then(res => {
					if (res.code == 200) {
						console.log('res', res.data)
						this.isLoading = false
						this.dispute = res.data.userPlead
						//回填图片处理
						for (let i = 0; i < res.data.pleadPicAddList.length; i++) {
							this.imgList.push({
								url: res.data.pleadPicAddList[i],
								code: res.data.pleadCodes[i]
							})
						}
						// this.imgList = res.data.pleadPicAddList
						this.detailImgList = res.data.explainPicAddList
						this.detailDispute = res.data.adminExplain
						this.applyTime = res.data.createdTime
						this.dealTime = res.data.updatedTime
						this.isDisable = true
						if (this.dealTime) {
							this.isDetail = true
						}
					} else {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: res.msg
						})
					}
				})
			},
			////图片压缩成功
			cpimgOk(file) {
				console.log('file', file)
				for (let i = 0; i < file.length; i++) {
					this.uploadImg(file[i])
				}
			},
			uploadImg(file) {
				this.isLoading = true
				let base64Img = file.toString()
				let biz_content = {
					customer_id: getEtcAccountInfo().custMastId,
					scene: 12,
					photo_code: '100',
					file_name: 'file_name',
				}
				let params = {
					file_content: file.toString(),
					method_code: '2',
					biz_content: JSON.stringify(biz_content)
				};
				this.$request.post(this.$interfaces.uploadFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.imgList.push({
							code: res.data.code,
							url: res.data.file_url
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg
						})
					}

				}).catch(err => {
					this.isLoading = false;
				})
			},
			//用户提交申请
			sendApply() {
				console.log(this.dispute);
				if (!this.dispute) {
					uni.showModal({
						title: '提示',
						content: '请输入申请原因'
					})
					return
				}
				if (this.imgList.length == 0) {
					uni.showModal({
						title: '提示',
						content: '请至少上传一张申述附件'
					})
					return
				}

				let pleadPicAdd = []
				let codes = []
				this.imgList.forEach(item => {
					pleadPicAdd.push(item.url)
					codes.push(item.code)
				})
				let pleadPicAddJson = JSON.parse(JSON.stringify(pleadPicAdd))

				this.isLoading = true
				let params = {
					auditBlackListId: this.applyId.toString(),
					pleadPicAdd: pleadPicAdd,
					codes: codes,
					userPlead: this.dispute
				}
				let data = {
					'data': params
				}
				console.log('params', JSON.stringify(params))
				this.$request.post(this.$interfaces.disputeApply, data).then(res => {
					this.isLoading = false;
					console.log('res', res)
					if (res.code == 200) {
						uni.showModal({
							title: '提示',
							content: '申述提交成功'
						});
						//提交申请成功，查询申诉详情
						this.getDisputeDetail()
						// uni.navigateBack({
						// 	delta: 1
						// })
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg
						});
					}
				}).catch(err => {
					this.isLoading = false;
				})
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e);
			},
			ChooseImage() {
				let sourceType = 0
				this.$refs.cpimg._changImg(sourceType);

			},
			ViewImage(e) {
				let viewArr = []
				this.imgList.forEach(item => {
					viewArr.push(item.url)
				})
				uni.previewImage({
					urls: viewArr,
					current: e.currentTarget.dataset.url
				});
			},
			ViewDetailImage(e) {
				uni.previewImage({
					urls: this.detailImgList,
					current: e.currentTarget.dataset.url
				});
			},
			DelImg(e) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该图片吗？',
					cancelText: '取消',
					confirmText: '确定',
					success: res => {
						if (res.confirm) {
							this.imgList.splice(e.currentTarget.dataset.index, 1)
						}
					}
				})
			},
			goBack() {
				uni.navigateBack({
					delta: 1
				})
			},
		},
	}
</script>

<style lang='scss' scoped>
	.sellPay-Info {

		margin-bottom: 160upx;

		.big-title {
			background-color: #fff;
			height: 40upx;
			padding: 0 30upx;
			font-weight: bold;
			border-top: 8upx solid #FFFFFF;
			color: rgba(51, 51, 51, 100);
			font-size: 34rpx;
			font-family: PingFangSC-bold;

			&:before {
				content: '|';
				font-weight: 900;
				color: #0066E9;
				position: relative;
				right: 12rpx;
				top: -2rpx;
			}
		}

		.c-title {
			/* margin-top: 20upx; */
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}

		.dispute_info {
			height: 220upx;
			background-color: #fff;

			.dispute_textarea {
				padding: 20upx;
				height: 180upx;
				font-size: 28upx;
				width: 80%;
				background-color: #f3f3f3;
				border-radius: 8upx;
			}
		}

		.dispute_img {
			/* height: 200upx; */
			background-color: #FFFFFF;

		}

	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}
</style>