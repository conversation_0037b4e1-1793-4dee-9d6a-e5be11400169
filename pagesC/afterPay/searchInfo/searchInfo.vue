<template>
	<view>
		<view class="search-info">
			<licensecolor desc='非车身颜色' :palteColor='formData.vehicle_color' @on-change='onLicensecolorChange'></licensecolor>
			<view class="search-car">
				
				<view class="car-number-color" v-if="false">
					<view class="title">
						车牌颜色：
					</view>
					<view class="picker picker-group">
						<picker @change="PickerChange" :value="index" :range="vehiclePicker" range-key='label'
							class="input-wrapper">
							<view class="picker-value">
								{{vehiclePicker[index].label}}
							</view>
							<view class="cuIcon-right icon"></view>
						</picker>
					</view>
				</view>
				<view class="car-number">
					<view class="title">
						车牌号码：
					</view>
					<view class="input-wrapper">
						<input placeholder="请点击输入车牌号码" disabled="true" @tap="plateShow=true"
							v-model.trim="formData.vehicle_code" />
						<plate-input v-if="plateShow" :plate="formData.vehicle_code" @export="setPlate"
							@close="plateShow=false" />
					</view>
				</view>


			</view>
		</view>

		<view class="back-card-box" v-if="currentCustomerInfo">
			<view class="item">
				<view class="weui-form-preview">
					<view class="weui-form-preview__hd">
						<view class="weui-form-preview__item">
							<view class="weui-form-preview__label">
								欠费信息
							</view>
						</view>
					</view>
					<view class="weui-form-preview__bd">
						<view class="weui-form-preview__item g-flex-start ">
							<view class="weui-form-preview__label">车主姓名</view>
							<view class="weui-form-preview__value">{{currentCustomerInfo.custName}}</view>
						</view>
						<view class="weui-form-preview__item g-flex-start back-card-money">
							<view class="weui-form-preview__label desc_text" >欠费金额</view>
							<view class="weui-form-preview__value money">{{payMoneyFilter(currentCustomerInfo.sum)}}元</view>
						</view>

					</view>
				</view>
			</view>
			<view class="item">
				<view class="weui-form-preview">
					<view class="weui-form-preview__hd">
						<view class="weui-form-preview__item">
							<view class="weui-form-preview__label">
								欠费卡信息
							</view>
						</view>
					</view>
					<view class="weui-form-preview__bd" v-for="cardInfo in currentCustomerInfo.cardInfo"
						:key="cardInfo.cardNo">

						<view class="weui-form-preview__item g-flex-start">
							<view class="weui-form-preview__label">ETC卡号</view>
							<view class="weui-form-preview__value">{{cardNoFilter(cardInfo.cardNo)}}</view>
						</view>
						<view class="weui-form-preview__item g-flex-start ">
							<view class="weui-form-preview__label">状态</view>
							<view class="weui-form-preview__value">{{getCpuStatus(cardInfo.cardStatus)}}</view>

						</view>

					</view>
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed" style="z-index: 10;">
			<view class="weui-bottom-fixed__box bottom-box">

				<block v-if="currentCustomerInfo">
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="searchAfterPay">
							查询待补缴金额
						</button>
					</view>
					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="reset">
							清除信息
						</button>
					</view>
				</block>
				<block v-else>
					<button class="weui-btn weui-btn_primary" @click="handleOnSearch">
						查询ETC信息
					</button>
				</block>
			</view>

		</view>
		<TModal :showModal='dialogVisible' @cancelModal='dialogVisible=false' @okModal='onConfirmHandle' okText='确认'>
			<view slot='content' style="padding: 10rpx 30rpx 40rpx 30rpx;">
				<view style="font-size: 30rpx;text-align: left;padding-bottom: 20rpx;">当前车辆搜索存在多个用户,请输入车主姓名</view>
				<view class="cu-form-group g-flex g-flex-align-center" style="border: 1px solid #dedede;min-height: 78rpx;">
					<input style="text-align: left;height: 60rpx;" placeholder="请输入车主姓名"
						v-model='formData.custName'></input>
				</view>

			</view>
		</TModal>
		<tLoading :isShow="isShowLoding" />
	</view>
</template>

<script>
	import {
		provincesOptions
	} from '@/common/const/optionData.js'
	import interfaces from "@/common/api/interface_4501.js";
	// import bindinfo from './bindinfo.vue'
	import tLoading from '@/components/common/t-loading'
	import plateInput from '@/components/uni-plate-input/uni-plate-input'
	import TButton from '@/components/t-button.vue';
	import {
		getCpuStatus
	} from '@/common/method/filter.js';
	import TModal from '@/components/t-modal/t-modal.vue'
	export default {
		components: {
			plateInput,
			tLoading,
			TButton,
			TModal
		},
		data() {
			return {
				dialogVisible: false,
				isShowLoding: false,
				plateShow: false,
				resetFlag: false,
				provincesOptions,
				index: 0,
				currentCustomerInfo: null,
				customerList: [],
				formData: {
					custName: '',
					cardNo: '',
					vehicle_color: '',
					vehicle_code: '',

				},
				vehiclePicker: [{
						label: "蓝",
						value: "0"
					},
					{
						label: "黄",
						value: "1"
					},
					{
						label: "黑",
						value: "2"
					},
					{
						label: "白",
						value: "3"
					},
					{
						label: "渐变绿",
						value: "4"
					},
					{
						label: "黄绿双拼",
						value: "5"
					},
					{
						label: "蓝白渐变",
						value: "6"
					},
				],
				// InfoDetailList: {},
			}
		},
		watch:{
			'formData.vehicle_color':function(){
				this.customerList = []
				this.currentCustomerInfo = null;
			},
			'formData.vehicle_code':function(){
				this.customerList = []
				this.currentCustomerInfo = null;
			}
		},
		methods: {
			getCpuStatus,
			cardNoFilter(val) {
				if (!val) return ''
				return val.replace(
					/(\w{6})\w*(\w{4})/,
					"$1********$2"
				)
			},
			payMoneyFilter(val) {
				let value = val
				if (value == 0) return value
				value = value / 100
				return this.toDecimal2(value)
			},
			toDecimal2(x) {
				var f = parseFloat(x)
				if (isNaN(f)) {
					return false
				}
				var f = Math.round(x * 100) / 100
				var s = f.toString()
				var rs = s.indexOf('.')
				if (rs < 0) {
					rs = s.length
					s += '.'
				}
				while (s.length <= rs + 2) {
					s += '0'
				}
				return s
			},
			onConfirmHandle() {
				let item = this.customerList.filter(item => {
					return item.custName == this.formData.custName
				})
				if (item.length != 0) {
					this.dialogVisible = false;
					this.currentCustomerInfo = item[0];
					return;
				}
				uni.showToast({
					title: '搜索车主用户不存在',
					icon: 'none',
					duration: 2000
				});

			},
			setPlate(plate) {
				if (plate.length >= 7) this.formData.vehicle_code = plate
				this.plateShow = false
			},
			getCustomerInfo() {
				this.isShowLoding = true
				let params = {
					vehicleColor: this.formData.vehicle_color,
					vehicleCode: this.formData.vehicle_code
				}
				console.log('入参params', params)
				this.$request
					.post(interfaces.searchCustomerInfoV2, {
						data: params,
					})
					.then((res) => {
						this.isShowLoding = false
						if (res.code !== 200) {
							uni.showModal({
								title: '提示',
								content: res.msg
							})
							return
						}
						if (!res.data.length) {
							uni.showModal({
								title: '提示',
								content: '暂无欠费用户'
							})
							return
						}
						if (res.data.length > 1) {
							this.dialogVisible = true;
							this.formData.custName = '';
							this.customerList = res.data;
							return
						}
						this.currentCustomerInfo = res.data[0];

					})
					.catch((error) => {
						this.isShowLoding = false
					});
			},
			handleOnSearch() {
				if (!this.formData.vehicle_color) {
					uni.showModal({
						title: '提示',
						content: '请先选择车牌颜色'
					})
					return
				}
				if (this.formData.vehicle_code == '') {
					uni.showModal({
						title: '提示',
						content: '请先输入车牌号。'
					})
					return
				}

				console.log('数据', this.formData.vehicle_code, this.formData.vehicle_color)
				this.getCustomerInfo()
			},
			PickerChange(e) {
				this.index = e.detail.value;
				this.formData.vehicle_color = this.vehiclePicker[this.index].value;
				console.log(this.formData.vehicle_color);
			},
			searchAfterPay() {
				let nextData = {
					...this.formData,
					...this.currentCustomerInfo
				}
				uni.navigateTo({
					url: '../recordDetail/recordDetail?nextData=' + encodeURIComponent(JSON.stringify(nextData))
				})
			},
			reset() {
				this.currentCustomerInfo = null;
				for (const key in this.formData) {
					this.formData[key] = ''
				}
				this.formData.vehicle_color = '0'
			},
			onLicensecolorChange(val){
				this.formData.vehicle_color = val;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.back-card-box .weui-form-preview__label {
		min-width: 120rpx;

	}

	.back-card-box .weui-form-preview {
		padding: 0 30rpx 10rpx 30rpx;
	}

	.back-card-box .weui-form-preview__bd {
		border-bottom: 1px dashed #c3c3c3;
	}

	.back-card-box .weui-form-preview__bd:last-child {
		border: none;
	}

	.back-card-box {
		margin: 30rpx;
	}

	.back-card-box .item {
		margin-bottom: 20rpx;
	}

	.back-card-money {
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
	}

	.back-card-money .money {
		font-size: 40rpx;
		font-weight: 500;
		color: #0066E9;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.search-info {
		background-color: #FFFFFF;

		.search-car {
			padding: 0 30rpx 30rpx  30rpx;

			.car-number-color {
				display: flex;
				align-items: center;

				// justify-content: center;
				.title {
					flex: 0 0 140rpx;
				}

				.picker {
					// margin-right: 20upx;
					height: 90rpx;
					line-height: 90rpx;
					min-width: 80upx;
					// padding: 20upx 20upx;
					padding-left: 20rpx;
					// display: flex;
					border: 2upx solid #d9d9d9;

					.picker-value {
						display: inline-block;
						width: 250rpx;
					}
				}

				.icon {
					display: inline-block;
					vertical-align: top;
					// width: 60upx;
					width: 48%;
					text-align: right;
					line-height: 90rpx;
					height: 90rpx;
					font-size: 34upx;
					color: #8799a3;
				}
			}

			.car-number {
				display: flex;
				align-items: center;

				// justify-content: center;
				.title {
					flex: 0 0 140rpx;
				}

				.input-wrapper {
					height: 90rpx;
					line-height: 90rpx;
					// min-width: 80rpx;
					// padding-left: 20rpx;
					border: 2rpx solid #d9d9d9;
					// text-align: center;
					flex: 1;

					&>input {
						width: 100%;
						height: 90rpx;
						line-height: 90rpx;
						// text-align: center;
						padding: 0 20rpx;
					}
				}

				.picker {
					// margin-right: 20upx;
					min-width: 80upx;
					padding: 0 20rpx;
					line-height: 90rpx;
					height: 90rpx;
					// display: flex;
					border: 2upx solid #d9d9d9;

					// height: 1.41176471em;
					// line-height: 1.41176471;
					.picker-value {
						display: inline-block;
					}
				}

				.icon {
					display: inline-block;
					vertical-align: top;
					// width: 60upx;
					text-align: right;
					line-height: 90rpx;
					height: 90rpx;
					font-size: 34upx;
					color: #8799a3;
				}
			}

			.user-info {
				margin-top: 30rpx;
				display: flex;
				align-items: center;

				// justify-content: center
				.title {
					flex: 0 0 140rpx;
					// margin-right: 20rpx;
				}

				.info-desc {
					width: 100%;
					height: 90rpx;
					line-height: 90rpx;
					// text-align: center;
					padding: 0 20rpx;
					background-color: rgba(0, 0, 0, 0.1);
				}
			}
		}

		.search-car .picker-group {
			flex: 1;
			// margin-right: 20upx;
			// min-width: 80upx;
			// padding: 20upx 20upx;
			// display: flex;
			// border: 2upx solid #d9d9d9;
		}

		// .search-car .picker-province-group {
		// 	// margin-right: 20upx;
		// 	// min-width: 80upx;
		// 	// padding: 20upx 20upx;
		// 	// display: flex;
		// 	// border: 2upx solid #d9d9d9;
		// }

		.search-car .input {
			// padding: 20upx 20upx;
			line-height: 90rpx;
			height: 90rpx;
			flex: 1;
			display: flex;
			border: 1upx solid #d9d9d9;
			border-left: 0;

			.cuIcon-roundclose {
				padding: 0 20rpx;
			}
		}

		.search-car .input .input-wrapper {
			flex: 1;
			width: 100%;
			border: 0;
			outline: 0;
			-webkit-appearance: none;
			background-color: transparent;
			font-size: inherit;
			color: inherit;
			line-height: 90rpx;
			height: 90rpx;
			padding: 0 20rpx;
		}

		.btn-wrapper {
			margin-top: 30rpx;
			display: flex;

			&>t-button {
				flex: 1;
			}
		}
	}
</style>
