<template>
	<view class="sellPay-container">
		<view class="fixed-top">
			<view class="header-wrapper" v-if="record.length > 0">
				<view class="cu-form-group">
					<view class="sum-wrapper">
						<view class="title">待补缴通行费总金额:</view>
						<view><text style="color: #0066E9;">{{moneyFilter(sum)}}</text><text
								style="margin-left: 6rpx;">元</text></view>
					</view>
					<view class="count-wrapper">
						<view class="title">待补缴通行交易条数:</view>
						<view><text style="color: #0066E9;">{{count}}</text><text style="margin-left: 6rpx;">笔</text>
						</view>
					</view>
				</view>
			</view>
			<view class="weui-form">
				<view class="weui-cells__title">
					交易详情列表
				</view>
			</view>
		</view>
		<view class="scroll-box" v-if="isPermission">
			<item style="height: 100px;" v-for="(item,index) in record" :key="index" :info="item" :vehicle="vehicle"
				:status="status" :isShowBtn='true'>
			</item>
			<load-more v-if="record.length == 0" :loadStatus="noticeLoadStatus" />
		</view>
		<view class="scroll-box tips" v-if="!isPermission && record.length > 0">
			<view class="tips-wrapper">
				您不是该车辆ETC绑定的用户，无权查看交易详情，如需查看，可选择
				<text class="bind" @click="goBind">绑定ETC</text>
				后查看，或直接通过向该ETC用户绑定的手机号发送短信验证进行<text class="bind" @click="showDialog">授权</text>查看。
			</view>
		</view>
		<view class="btn-wrapper">
			<TButton title="在线补缴" v-if="record.length > 0" @clickButton="payOnline()"></TButton>
			<TButton title="查看补缴记录" @clickButton="searchAfterPay()"></TButton>
		</view>
		<TModal :showModal='dialogVisible' @cancelModal='cancelModal' @okModal='onBindHandle' :okText='okText'>
			<form slot='content' class="bind-Info">


				<view class="des">{{dialogTitle}}</view>
				<view class="cu-form-group login-form-group">
					<input placeholder="请输入图形验证码" name="mobileCode" v-model='mobileCode'></input>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">
				</view>
				<view class="cu-form-group">

					<input placeholder="请输入验证码" v-model="mobileInputCode" name="mobileInputCode"></input>
					<text class="sendSMS text-cyan" @click="sendSMS">{{smsName}}</text>
				</view>
			</form>
		</TModal>
		<TModal :showModal='queryDialogVisible' @cancelModal='goBack' @okModal='onConfirmHandle' okText='确认'>
			<view slot='content' style="padding: 10rpx 30rpx 40rpx 30rpx;">
				<view style="font-size: 30rpx;text-align: left;padding-bottom: 20rpx;">当前车辆搜索存在多个用户,请输入车主姓名</view>
				<view class="cu-form-group g-flex g-flex-align-center" style="border: 1px solid #dedede;height: 68rpx;">
					<input style="text-align: left;height: 60rpx;" placeholder="请输入车主姓名"
						v-model='query.custName'></input>
				</view>

			</view>
		</TModal>
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import TModal from '@/components/t-modal/t-modal.vue'
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import item from './record-item.vue'
	import loadMore from '../../components/load-more/index.vue';
	import float from '@/common/method/float.js'
	import interfaces from "@/common/api/interface_4501.js";
	import {
		getLoginUserInfo,
		getCurrUserInfo,
		getCurrentCar,
		getOpenid,
		setOpenid
	} from '@/common/storageUtil.js';
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		components: {
			TButton,
			item,
			tLoading,
			loadMore,
			TModal
		},
		data() {
			return {
				dialogVisible: false,
				isLoading: false,
				isPermission: false,
				openId: '',
				smsName: '发送验证码',
				mobileInputCode: '', //用户输入的验证那,
				mobileCode: '', // 图形验证码
				codeUrl: '', // 图形验证码连接
				captchaId: '',
				okText: '授权',
				dialogTitle: '',
				time: null,
				status: 1,
				sum: '',
				count: '',
				record: [],
				vehicle: {
					vehicle_color: '',
					vehicle_code: '',
					cardNo: '',
					custMastId: '',
					custMobile: '',
					cardMastId: '',
				},
				customerList: [],
				queryDialogVisible: false,
				query: {
					custName: '',
				},
			};
		},
		// computed: {
		// 	mobile() {
		// 		console.log('this.vehicle.custMobile', this.vehicle.custMobile)
		// 		return this.vehicle.custMobile.replace(
		// 			/(\d{3})\d{4}(\d{4})/,
		// 			"$1****$2"
		// 		);

		// 	}
		// },
		onLoad(option) {
			console.log('option', option)
			this.openId = getOpenid() || ''
			if (option && Object.keys(option).length) {
				const vehicle = JSON.parse(decodeURIComponent(option.nextData));
				this.vehicle = {
					...vehicle
				}
				this.getCustomerInfo()
			}
		},
		created() {
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
		},
		onShow() {
			let flag = uni.getStorageSync('isRefresh')
			if (flag) {
				this.getCustomerInfo()
				uni.setStorageSync('isRefresh', false)
			}
		},
		methods: {
			getOpenIdHandle() {

				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.openId = res.data.openid
									setOpenid(res.data.openid)

								}

							}
						})
					}
				})
			},
			onConfirmHandle() {
				let item = this.customerList.filter(item => {
					return item.custName == this.query.custName
				})
				if (item.length != 0) {
					this.queryDialogVisible = false;
					this.initCustomerResult(item[0]);
					return;
				}
				uni.showToast({
					title: '搜索车主用户不存在',
					icon: 'none',
					duration: 2000
				});
			},
			mobile() {
				return this.vehicle.custMobile.replace(
					/(\d{3})\d{4}(\d{4})/,
					"$1****$2"
				);
			},
			goBack() {
				uni.navigateBack({
					delta: 1
				})
			},
			searchAfterPay() {
				uni.navigateTo({
					url: './afterPayList?nextData=' + encodeURIComponent(JSON.stringify(this.vehicle))
				})
			},
			goBind() {
				uni.redirectTo({
					url: '/pagesB/accountBusiness/accountList/accountList?type=' + 'afterPay'
				});
			},

			getCustomerInfo() {
				let params = {
					vehicleColor: this.vehicle.vehicle_color,
					vehicleCode: this.vehicle.vehicle_code
				}
				this.$request
					.post(interfaces.searchCustomerInfoV2, {
						data: params,
					})
					.then((res) => {
						this.isLoading = false
						if (res.code !== 200) {
							uni.showModal({
								title: '提示',
								content: res.msg,
								icon: "none"
							})
							return
						}
						if (!res.data.length) {
							uni.showModal({
								title: '提示',
								content: '暂无欠费用户',
								icon: "none"
							})
							return;
						}
						// 补缴其他车辆逻辑
						if (this.vehicle.custMastId) {
							for (let i = 0; i < res.data.length; i++) {
								if (res.data[i].custMastId == this.vehicle.custMastId) {
									this.initCustomerResult(res.data[i]);
									break;
								}
							}
							return;
						}
						// 本人补缴存在多个用户情况下
						if (res.data.length > 1) {
							this.queryDialogVisible = true;
							this.query.custName = '';
							this.customerList = res.data;
							return;
						}
						this.initCustomerResult(res.data[0]);

					})
					.catch((error) => {
						this.isLoading = false
					});
			},
			initCustomerResult(result) {
				this.isPermission = false
				//判断ETC卡号是否是该用户车辆，如果是授权显示交易详情列表;
				for (let i = 0; i < result.cardInfo.length; i++) {
					if (getCurrentCar() && getCurrentCar().cpu_card_id == result.cardInfo[i].cardNo) {
						this.isPermission = true;
						break
					}
				}
				this.getRecord(result)
				this.vehicle = {
					...this.vehicle,
					...result
				}
				this.dialogTitle = `查看交易详情需要向预留手机号${this.mobile(this.vehicle.custMobile)}码发送验证码，授权成功后方可查看`

			},
			getRecord(result) {
				// this.isLoading = true
				let cardNo = [];
				for (let i = 0; i < result.cardInfo.length; i++) {
					cardNo.push(result.cardInfo[i].cardNo)
				}
				let params = {
					cardNo: cardNo,
					status: this.status
				}
				console.log('入参params', params)
				this.$request
					.post(interfaces.repaymentOrderQueryV2, {
						data: params,
					})
					.then((res) => {
						this.isLoading = false
						if (res.code !== 200) {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: function(res) {
									if (res.confirm) {
										uni.navigateBack({
											delta: 1
										})
									}
								}
							})
							return
						}
						console.log("查询结果", res.data);
						this.sum = res.data.sum
						this.count = res.data.count
						this.record = res.data.list
					})
					.catch((error) => {
						this.isLoading = false
					});
			},
			payOnline() {
				this.payment()
				// this.afterPayOrderQuery()
			},
			payment() {
				let _self = this;
				this.isLoading = true;
				let auditBlackListIds = ''
				// console.log('this.record',this.record)
				//获取每个流水的id
				this.record.forEach(item => {
					// console.log('item', item)
					auditBlackListIds += item.orderNo + ','
				})
				auditBlackListIds = auditBlackListIds.substr(0, auditBlackListIds.length - 1)
				let params = {
					// auditBlackListIds: '10036',
					auditBlackListIds: auditBlackListIds,
					openId: this.openId,
					payMoney: this.sum,
					// payMoney: 1,
					payType: '10000601',
					source: '3',
					cardMastId: this.vehicle.cardMastId || '',
					custMastId: this.vehicle.custMastId || ''
				}
				console.log('入参params', params)
				let data = {
					routePath: this.$interfaces.afterPay.method,
					bizContent: params
				};
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						this.isLoading = false;
						console.log(res, '支付');
						if (res.code == 200) {
							let result = res.data;
							let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};

							wx.requestPayment({
								...payMessage,
								success: function(successres) {
									_self.isLoading = true
									console.log(successres, '支付成功回调', _self.isLoading);
									// setTimeout(() => {
									// 	_self.afterPayOrderQuery(result);
									// }, 6000);
									_self.updatePayStatus(res.data.orderId, 2)
								},
								fail: function(err) {
									console.log('fail', err)
									_self.updatePayStatus(res.data.orderId, 3)
								},
							});
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(error => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						});
					});
			},
			updatePayStatus(orderId, status) {
				let params = {
					routePath: this.$interfaces.afterPayUpdate.method,
					bizContent: {
						orderId: orderId,
						status: status,
						payBusinessType: 0
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					if (res.code == 200 && status == 2) {
						this.getCustomerInfo()
						//支付成功且更新状态成功
						uni.navigateTo({
							url: './afterPayList?nextData=' + encodeURIComponent(JSON
								.stringify(this.vehicle))
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			afterPayOrderQuery(data) {
				console.log('data', data)
				let params = {
					routePath: this.$interfaces.afterPayOrderQuery.method,
					bizContent: {
						orderId: data.orderId
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					let status = res.data.status;
					let statusVal = {
						1: "补缴中",
						2: "补缴成功",
						3: "补缴失败",
					};
					let msg = statusVal[status] || "补缴失败";

					uni.showModal({
						title: "提示",
						content: msg,
						confirmText: '确定',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: './afterPayList?nextData=' + encodeURIComponent(JSON
										.stringify(this.vehicle))
								});
							}
						}
					});
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: error.msg,
						showCancel: false
					});
				});
			},
			//授权
			onBindHandle() {
				// if (this.mobileInputCode == '123456') {
				// 	this.isPermission = true
				// 	this.dialogVisible = false
				// } else {
				// 	uni.showToast({
				// 		title: '验证码不正确',
				// 		icon: "none"
				// 	})
				// }
				let params = {
					mobile: this.vehicle.custMobile,
					mobileCode: this.mobileInputCode
				}
				this.isLoading = true
				this.$request
					.post(interfaces.smsCheckCode, {
						data: params,
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							this.isPermission = true
							this.dialogVisible = false
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								icon: "none"
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: "提示",
							content: error.msg,
							showCancel: false,
						});
					})
			},
			//发送短信
			sendSMS() {
				if (!this.mobileCode) {
					uni.showModal({
						title: "提示",
						content: "请输入图形验证码",
						showCancel: false,
					});
					return;
				}
				if (!this.time) {
					let countdown = 60
					let params = {
						// userNo: getLoginUserInfo().userNo,
						mobile: this.vehicle.custMobile,
						mobileCode: this.mobileCode,
						captchaId: this.captchaId

					};


					// this.time = setInterval(() => {
					// 	countdown = countdown - 1
					// 	this.smsName = countdown + "秒后重新发送"
					// 	if (countdown === 0) {
					// 		clearInterval(this.time)
					// 		this.time = null
					// 		this.smsName = "重新发送"
					// 	}
					// }, 1000)


					this.$request.post(this.$interfaces.sendaAccountSms, {
						data: params
					}).then(res => {
						console.log(res);
						if (res.code == '200') {
							this.time = setInterval(() => {
								countdown = countdown - 1
								this.smsName = countdown + "秒后重新发送"
								if (countdown === 0) {
									clearInterval(this.time)
									this.time = null
									this.smsName = "重新发送"
								}
							}, 1000)
						} else {
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										this.getCaptcha()
									}
								}
							});
						}
					}).catch(error => {
						uni.showModal({
							title: "提示",
							content: error.msg,
							showCancel: false
						});
					})
				}
			},
			showDialog() {
				this.dialogVisible = true
				this.getCaptcha()
			},
			getCaptcha() {
				let params = {

				}
				this.$request.post(this.$interfaces.getCaptcha, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.codeUrl = res.data.image
						this.captchaId = res.data.captchaId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {

				})
			},
			cancelModal() {
				this.dialogVisible = false;
				this.smsName = '发送验证码';
				for (let key in this.formData) {
					this.formData[key] = '';
				}
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
		destroyed() {

		}
	};
</script>
<style lang="scss" scoped>
	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-form {
		box-shadow: 0rpx 12rpx 18rpx 5rpx rgba(0, 0, 0, 0.5);
	}

	.header-wrapper {
		box-shadow: 0rpx 6rpx 14rpx 0rpx rgba(0, 0, 0, 0.1)
	}

	.btn-wrapper {
		display: flex;
		padding: 20rpx;
		display: flex;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		z-index: 10;

		&>t-button {
			flex: 1;

			/deep/.t-padding {
				padding: 0;
			}

			&:last-child {
				margin-left: 20rpx;

				/deep/.t-padding {
					&>button {
						background-color: #ffffff !important;
						border: 1rpx solid #0066E9;
						color: #0066E9;
					}
				}
			}
		}
	}

	.header-wrapper .cu-form-group {
		flex-direction: column;
		align-items: normal;
	}

	.fixed-top {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		// bottom: 0;
		overflow: hidden;
		z-index: 10;
		background-color: #F3F3F3;
		box-shadow: 0rpx 6rpx 14rpx 0rpx rgba(0, 0, 0, 0.1);

		.sum-wrapper {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.count-wrapper {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
	}

	.scroll-box {
		padding-top: 260rpx;
		padding-bottom: 120rpx;
	}

	.apply-record {
		width: 150rpx;
		margin-left: 10rpx;
		color: #0066E9;
	}

	.tips {

		// margin: 60rpx 70rpx;
		.tips-wrapper {
			padding: 90rpx 30rpx;
			line-height: 60rpx;
			font-size: 32rpx;
		}

		.bind {
			color: #2993db;
			cursor: pointer;
		}
	}

	.bind-Info {
		.login-form-group .code-img {
			width: 240upx;
			height: 90upx;
			margin-left: 20upx;
		}

		.sendSMS {
			padding: 10rpx;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.des {
			font-size: 30upx;
			text-align: left;
			color: #666;
			background: #ffffff;
			padding: 10upx 25upx;
		}

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;

		}
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.bind-Info .cu-form-group .title {
		font-size: 32upx;
	}

	.bind-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.bind-Info .cu-form-group input {
		text-align: left;
	}

	.bind-Info .cu-form-group radio-group {

		flex: 1;
		text-align: left;
	}
</style>
