<template>
	<view class="sellPay-container">
		<view class="fixed-top">
			<view class="header-wrapper" v-if="record.length > 0">
				<view class="cu-form-group">
					<view class="sum-wrapper">
						<view class="title">已补缴通行费总金额:</view>
						<view><text style="color: #0066E9;">{{moneyFilter(sum)}}</text><text
								style="margin-left: 6rpx;">元</text></view>
					</view>
					<view class="count-wrapper">
						<view class="title">已补缴通行交易条数:</view>
						<view><text style="color: #0066E9;">{{count}}</text><text style="margin-left: 6rpx;">笔</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 			<view class="btn-wrapper">
				<TButton title="在线补缴" v-if="record.length > 0" @clickButton="handleOnSearch()"></TButton>
				<TButton title="查看补缴记录" @clickButton="searchAfterPay()"></TButton>
			</view> -->
			<view class="weui-form">
				<view class="weui-cells__title">
					已补缴通行费列表
				</view>
			</view>
		</view>
		<view class="scroll-box">
			<item style="height: 100px;" v-for="(item,index) in record" :key="index" :info="item" :vehicle="vehicle"
				:status="status">
			</item>
			<load-more v-if="record.length == 0" :loadStatus="noticeLoadStatus" />
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import item from './record-item.vue'
	import loadMore from '../../components/load-more/index.vue';
	import float from '@/common/method/float.js'
	import interfaces from "@/common/api/interface_4501.js";
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	export default {
		components: {
			TButton,
			item,
			tLoading,
			loadMore
		},
		data() {
			return {
				isLoading: false,
				status: 2,
				sum: '',
				count: '',
				record: [],
				vehicle: {
					vehicle_code: '',
					vehicle_color: '',
					cardNo: ''
				}

			};
		},
		onLoad(option) {
			console.log('option', option)
			const vehicle = JSON.parse(decodeURIComponent(option.nextData));
			this.vehicle = {
				...vehicle
			}
			console.log('卡号', this.vehicle)
			this.getRecord()
		},
		methods: {
			// searchAfterPay() {
			// 	uni.navigateTo({
			// 		url: './afterPayList'
			// 	})
			// },
			getRecord() {
				let cardNo = [];
				if (!this.vehicle.cardInfo) {
					return
				}
				for (let i = 0; i < this.vehicle.cardInfo.length; i++) {
					cardNo.push(this.vehicle.cardInfo[i].cardNo)
				}
				this.isLoading = true
				let params = {
					cardNo: cardNo,
					status: this.status
				}
				console.log('入参params', params)
				this.$request
					.post(interfaces.repaymentOrderQueryV2, {
						data: params,
					})
					.then((res) => {
						this.isLoading = false
						if (res.code !== 200) {
							uni.showModal({
								title: '提示',
								content: res.msg + '(如您已补缴，系统正在结算，请等待几分钟后再来查看)',
								showCancel: false,
								success: function(res) {
									if (res.confirm) {
										uni.navigateBack({
											delta: 1
										})
									}
								}
							})
							return
						}
						console.log("查询结果", res.data);
						this.sum = res.data.sum
						this.count = res.data.count
						this.record = res.data.list
					})
					.catch((error) => {
						this.isLoading = false
					});
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
		destroyed() {

		}
	};
</script>
<style lang="scss" scoped>
	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.weui-form {
		box-shadow: 0rpx 12rpx 18rpx 5rpx rgba(0, 0, 0, 0.5);
	}

	.header-wrapper {
		box-shadow: 0rpx 6rpx 14rpx 0rpx rgba(0, 0, 0, 0.1)
	}

	.btn-wrapper {
		margin-top: 30rpx;
		display: flex;
		padding: 0 20rpx;

		&>t-button {
			flex: 1;

			/deep/.t-padding {
				padding: 0;
			}

			&:last-child {
				margin-left: 20rpx;

				/deep/.t-padding {
					&>button {
						background-color: transparent !important;
						border: 1rpx solid #0066E9;
						color: #0066E9;
					}
				}
			}
		}
	}

	.cu-form-group {
		flex-direction: column;
		align-items: normal;
	}

	.fixed-top {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		// bottom: 0;
		overflow: hidden;
		z-index: 10;
		background-color: #F3F3F3;
		box-shadow: 0rpx 6rpx 14rpx 0rpx rgba(0, 0, 0, 0.1);

		.sum-wrapper {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.count-wrapper {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
	}

	.scroll-box {
		padding-top: 260rpx;
	}

	.apply-record {
		width: 150rpx;
		margin-left: 10rpx;
		color: #0066E9;
	}
</style>