<template>
	<view class="order-list">
		<view class="weui-form-preview">
			<view class="weui-form-preview__hd">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label" style="font-size: 14px;font-weight: 600;">
						<!-- 						<CarCard class="home-card" :item='item' :plateNum="vehicleInfo.vehicle_code"
							:plateColor="vehicleInfo.vehicle_color +''" :registeredType='1'></CarCard> -->
						{{vehicle.vehicle_code+'[' +plateColorToColorMap.get(vehicle.vehicle_color + '') +']'}}
					</view>
					<view class="weui-form-preview__value" style="color: #0066E9;">金额：{{moneyFilter(info.amount)}}元
					</view>
				</view>
			</view>
			<view class="weui-form-preview__bd">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">入口站点：</view>
					<view class="weui-form-preview__value">
						{{info.transList[0].enStation?info.transList[0].enStation:''}}
					</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">入口时间：</view>
					<view class="weui-form-preview__value">{{info.transList[0].enTime?info.transList[0].enTime:''}}
					</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">出口站点：</view>
					<view class="weui-form-preview__value">
						{{info.transList[0].exStation?info.transList[0].exStation:''}}
					</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">出口时间：</view>
					<view class="weui-form-preview__value">{{info.transList[0].exTime?info.transList[0].exTime:""}}
					</view>
				</view>
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label">补缴状态：</view>
					<view class="weui-form-preview__value" style="color: red;" v-if="status == 1">待补缴</view>
					<view class="weui-form-preview__value" style="color: #0066E9;;" v-if="status == 2">已补缴</view>
				</view>

			</view>
			<view class="weui-form-preview__ft" v-if="isShowBtn">
				<view class="weui-form-preview__item">
					<view class="weui-form-preview__label"></view>
					<view class="weui-form-preview__value">
						<view class="btn-item">
							<button class="weui-btn_mini weui-btn_primary" v-if="info.commitState=='0'" @click="dispute(info)">异议处理</button>
							<button class="weui-btn_mini weui-btn_primary" v-else @click="dispute(info)">{{info.commitState=='1'?'待处理':'处理完成'}}</button>
						</view>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isShowLoding" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getPayStatus
	} from '@/common/method/filter'
	import {
		getCurrUserInfo,
		getCurrentCar
	} from "@/common/storageUtil.js";
	import {
		plateColorToColorMap
	} from '@/common/systemConstant.js'
	export default {
		components: {
			tLoading
		},
		props: {
			info: {
				type: Object,
				default () {
					return {}
				}
			},
			vehicle: {
				type: Object,
				default () {
					return {}
				}
			},
			status: {
				type: Number
			},
			isShowBtn:{
				type:Boolean,
				default:false
			}
		},
		data() {
			return {
				plateColorToColorMap,
				isShowLoding: false,
				schoolDetail: {

				},
				disputeStatus:''
			}
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			vehicleInfo() {
				return getCurrentCar() || {}
			},


		},
		methods: {
			//去往争议处理界面
			dispute(val) {
				uni.setStorageSync('isRefresh',true)
				let params={
					disputeStatus:val.commitState,
					orderNo:val.orderNo
				}
				uni.navigateTo({
					url: '../dispute/dispute?params=' +JSON.stringify(params) 
				})
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>
<style lang="scss" scoped>
	.order-list {
		margin-bottom: 20rpx;
		// box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.1);
		box-shadow: 0px 0px 20rpx 0px rgba(71, 123, 217, 0.12);
	}
</style>
