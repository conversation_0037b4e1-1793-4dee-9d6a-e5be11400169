<template>
	<view class="handleStep">
		<view class="steps">
			<view :class="{'is-finish':index<current,'is-process':index==current}" class="step"
				v-for="(item,index) in stepList" :key="index">
				<view class="steps-wrapper">
					<view class="steps-arrow" v-if="(index > current) && index != 0">
						<view class="steps-hd">
							<view class="steps-item">
								{{index + 1}}
							</view>
						</view>
					</view>
					<view class="steps-arrow" v-else>
						<view class="steps-hd">
							<view class="steps-item">
							</view>
						</view>
					</view>
					<view class="step-line"></view>
					<view class="step-line__left" v-if="(index == current) && current != 0"></view>
					<view class="step-line__white" v-if="(index > current) && current != 0"></view>
				</view>
				<view class="steps-bd">
					<view class="steps-title">{{item.label}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			current: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				active: 0,
				stepList: [{
					value: '',
					label: '提交材料'
				}, {
					value: '',
					label: '订单审核'
				}, {
					value: '',
					label: '设备注销'
				}, {
					value: '',
					label: '账户清算'
				}, {
					value: '',
					label: '余额退回'
				}]
			}
		},
		computed: {

		},
		onLoad() {},
		methods: {
			getClass(index) {
				return '1111';
			}
		}
	}
</script>

<style scoped lang="scss">
	.handleStep {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		// bottom: 0;
		background-color: #FFFFFF;
		width: 100%;
		padding-top: 48rpx;
		padding-bottom: 30rpx;
		height: 184rpx;
		z-index: 998;

	}

	.steps {
		display: flex;


	}

	.step {
		flex: 1;
		// display: flex;
		// justify-content: center;
		// align-items: center;
		position: relative;
	}

	.steps-wrapper {
		position: relative;
		height: 50rpx;
		// justify-content: center;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.steps-arrow {
		position: relative;
		width: 50rpx;
		height: 50rpx;
		background: rgba(0, 102, 233, 0.25);
		border-radius: 50%;
		background: #F6F6F6;
		bottom: 6px;
	}


	.steps-hd {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
		width: 18rpx;
		height: 42rpx;
		// background: #0066E9;
		border-radius: 50rpx;
	}

	.steps-item {
		// position: absolute;
		// left: 0;
		// right: 0;
		// top: 0;
		// bottom: 0;
		// margin: auto;
		// width: 6rpx;
		// height: 6rpx;
		// background: #FFFFFF;
		// border-radius: 50rpx;
		width: 18rpx;
		height: 42rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Semibold, PingFang SC;
		font-weight: 600;
		color: #D3D3D3;
		line-height: 42rpx;
	}

	.steps-bd {
		margin-top: 30rpx;
	}

	.steps-title {
		text-align: center;
		font-size: 24rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #AFAFAF;
		line-height: 24rpx;
	}

	.step:last-of-type .step-line {
		display: none;
	}

	.step-line {
		position: absolute;
		background: #F6F6F6;
		opacity: 0.5;
		transition: background-color 0.3s;
		top: 12rpx;
		left: 92rpx;
		bottom: 0;
		width: 110rpx;
		height: 7rpx;
	}

	.is-process .steps-arrow {
		position: relative;
		width: 30rpx;
		height: 30rpx;
		background: rgba(0, 102, 233, 0.25);
		border-radius: 50%;
		bottom: 0;
	}

	.is-process .steps-hd {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
		width: 20rpx;
		height: 20rpx;
		background: #0066E9;
		border-radius: 50rpx;
	}

	.is-process .steps-item {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
		width: 6rpx;
		height: 6rpx;
		background: #FFFFFF;
		border-radius: 50rpx;
	}

	.is-process .steps-title {
		color: #323435;
		font-weight: 700;
	}

	.is-finish .steps-arrow {
		position: relative;
		width: 50rpx;
		height: 50rpx;
		background: rgba(0, 102, 233, 0.25);
		border-radius: 50%;
	}


	.is-finish .step-line {
		position: absolute;
		background: rgba(0, 102, 233, 0.28);
		opacity: 0.5;
		top: 12rpx;
		left: 101rpx;
		width: 100rpx;
		z-index: -1;
	}

	.is-finish .steps-hd {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
		width: 33rpx;
		height: 33rpx;
		background: #0066E9;
	}

	.is-finish .steps-item {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 4rpx;
		margin: auto;
		width: 10rpx;
		height: 15rpx;
		border-bottom: 2rpx solid #FFFFFF;
		border-right: 2rpx solid #FFFFFF;
		-webkit-transform: rotate(45deg);
		-moz-transform: rotate(45deg);
		transform: rotate(45deg);
	}

	.is-finish .steps-title {
		color: #323435;
	}

	.step-line__left {
		position: absolute;
		background: rgba(0, 102, 233, 0.28);
		opacity: 0.5;
		top: 12rpx;
		left: 50rpx;
		z-index: -1;
		height: 7rpx;
		width: 10rpx;
	}

	.step-line__white {
		position: absolute;
		background: #F6F6F6;
		opacity: 0.5;
		top: 12rpx;
		left: 43rpx;
		z-index: -1;
		height: 7rpx;
		width: 10rpx;
	}
</style>