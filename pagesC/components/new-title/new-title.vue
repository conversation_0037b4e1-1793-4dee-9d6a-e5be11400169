<template>
	<view class="title-wrapper" :style="{'padding':setPadding + 'rpx'}">
		<view class="title">
			{{title}}
			<text v-if="titleDesc" class="red-title">{{titleDesc}}</text>
		</view>
		<view class="btn-wrapper" v-if="rightText && rightBtn" @click="onclick">
			<view class="btn-text">
				{{rightText}}
			</view>
			<image class="right-icon" src="../../static/<EMAIL>" mode="">
			</image>
		</view>
		<slot></slot>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String
			},
			titleDesc: {
				type: String,
				default: ''
			},
			setPadding: {
				type: String,
				default: '0'
			},
			rightText: {
				type: String,
				default: ''
			},
			rightBtn: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {

			}
		},
		methods: {
			onclick() {
				this.$emit('click')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.title-wrapper {
		// padding: 20rpx 0;
		display: flex;
		justify-content: space-between;
	}

	.title {
		height: 45rpx;
		font-size: 32rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 700;
		color: #323435;
		line-height: 45rpx;
	}

	.red-title {
		color: #F65B5B;
		font-size: 30rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		line-height: 44rpx;
	}

	.btn-wrapper {
		display: flex;
		align-items: center;
	}

	.btn-text {
		height: 33rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
		line-height: 33rpx;
	}

	.right-icon {
		width: 24rpx;
		height: 24rpx;
		background-size: 100%;
		// margin-top: 5rpx;
	}
</style>