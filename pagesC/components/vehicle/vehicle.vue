<template>
	<view class="weui-card form-item">
		<view class="weui-card-hd">
			<view class="weui-card-hd-wrapper">
				<view class="weui-card-hd-title">车辆信息</view>
				<slot name="rightBtn"></slot>
			</view>
		</view>
		<view class="weui-card-bd">
			<view class="list vehicle-info">
				<view class="list-item">
					<view class="list-item_label">
						车牌号码
					</view>
					<view class="list-item_value" v-if="showCarColor">
						{{vehicleObj.carNo}}【{{vehicleObj.carColorName}}】
					</view>
					<view class="list-item_value" v-if='!showCarColor'>
						{{vehicleObj.carNo}}
					</view>
				</view>
				<view class="list-item">
					<view class="list-item_label">
						ETC卡号
					</view>
					<view class="list-item_value">
						{{vehicleObj.cardNo || ''}}
					</view>
				</view>
				<view class="list-item">
					<view class="list-item_label">
						OBU号
					</view>
					<view class="list-item_value">
						{{vehicleObj.obuNo || ''}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			vehicleObj: {
				type: Object
			},
			showCarColor: {
				type: Boolean,
				default: false
			},

		},
		components: {},
		data() {
			return {

			}
		},
		computed: {},
		created() {},
		methods: {}
	}
</script>

<style>
</style>