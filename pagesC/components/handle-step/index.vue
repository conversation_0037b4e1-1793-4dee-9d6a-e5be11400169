<template>
	<view class="dart-steps dart-steps--horizontal" v-if="currentStepList && currentStepList.length">
		<view class="dart-step  is-center" v-for="(item,index) in currentStepList" :key="index">
			<view class="dart-step__head is-finish"
				:class="{'is-finish':index<current,'is-process':index==current,'is-wait':index>current}">
				<view class="dart-step__line">
					<view class="dart-step__line-inner" style="transition-delay: 0ms; border-width: 1px; width: 100%;">
					</view>
				</view>
				<view class="dart-step__icon">
					<view class="dart-step__icon-inner">
						<image v-if="index<current" class="icon-wrap" src="../../static/is-finish-icon.png"></image>
						<image v-if="index==current" class="icon-wrap" src="../../static/is-process-icon.png"></image>
						<view v-if="index>current" class="icon-wrap g-flex g-flex-horizontal-vertical">{{index + 1}}
						</view>
					</view>
				</view>
			</view>
			<view class="dart-step__main">
				<view class="dart-step__title"
					:class="{'is-finish':index<current,'is-process':index==current,'is-wait':index>current}">
					{{item.label}}
				</view>
				<view class="dart-step__description"
					:class="{'is-finish':index<current,'is-process':index==current,'is-wait':index>current}"></view>
			</view>
		</view>
	</view>
</template>

<script>
	let list = [{
			value: '',
			label: '上传资料'
		}, {
			value: '',
			label: '后台审核'
		}, {
			value: '',
			label: '设备激活'
		}
	]
	export default {
		props: {
			stepList: {
				type: Array,
				default () {
					return list
				}
			},
			activeIndex: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				current: 0,
				currentStepList: []
			}
		},
		watch: {
			stepList(val) {
				this.currentStepList = val;
			},
			activeIndex(val) {
				this.current = val
			}
		},
		created() {
			this.currentStepList = this.stepList;
			this.current = this.activeIndex
		}

	}
</script>

<style scoped lang="scss">
	.dart-steps {
		display: flex;
	}

	.dart-steps--horizontal {
		white-space: nowrap
	}

	.dart-steps--vertical {
		height: 100%;
		flex-flow: column
	}

	.dart-step {
		position: relative;
		flex-shrink: 1;
		flex: 1;
	}

	.dart-step:last-of-type .dart-step__line {
		display: none
	}

	.dart-step:last-of-type.is-flex {
		flex-basis: auto !important;
		flex-shrink: 0;
		flex-grow: 0
	}

	.dart-step:last-of-type .dart-step__description,
	.dart-step:last-of-type .dart-step__main {
		padding-right: 0
	}

	.dart-step__head {
		position: relative;
		width: 100%;
		height: 60rpx;
	}

	.dart-step__head.is-process {
		color: #D3D3D3;
		border-color: #F6F6F6;
	}

	.dart-step__head.is-wait {
		color: #D3D3D3;
		border-color: #F6F6F6;
	}

	.dart-step__head.is-finish {
		color: #D3D3D3;
		border-color: rgba(0, 102, 233, 0.28);
	}



	.dart-step__icon {
		position: relative;
		z-index: 3;
		display: inline-flex;
		justify-content: center;
		align-items: center;
		font-weight: 600;
		font-size: 30rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.dart-step__icon-inner .icon-wrap {
		border-radius: 50%;
		font-weight: 700;
		width: 50rpx;
		height: 50rpx;
		font-size: 32rpx;
	}

	.dart-step__icon-inner {
		display: flex;
	}

	.dart-step__head.is-wait {
		.dart-step__icon-inner .icon-wrap {
			background: #F6F6F6;
			color: #D3D3D3;
			width: 50rpx;
			height: 50rpx;
		}
	}

	.dart-step__line {
		position: absolute;
		z-index: 1;
		border-color: inherit;
		background-color: #F6F6F6;
	}

	.dart-step__line-inner {
		display: block;
		border: 1px solid;
		border-color: inherit;
		transition: .15s ease-out;
		box-sizing: border-box;
		width: 0;
		height: 0
	}

	.dart-step__main {
		white-space: normal;
		text-align: left;
		margin-top: 20rpx;

	}

	.dart-step__title {
		font-size: 24rpx;
	}

	.dart-step__title.is-process {
		font-weight: 500;
		color: #323435;
	}

	.dart-step__title.is-wait {
		font-weight: 500;
		color: #AFAFAF;
	}

	.dart-step__title.is-finish {
		font-weight: 500;
		color: #323435;
	}

	.dart-step__description {
		padding-right: 10%;
		margin-top: -5px;
		font-size: 12px;
		line-height: 20px;
		font-weight: 400
	}

	.dart-step__description.is-process {
		color: #AFAFAF
	}

	.dart-step__description.is-wait {
		color: #AFAFAF
	}

	.dart-step__description.is-finish {
		color: #409eff
	}

	.dart-step .dart-step__line {
		height: 6rpx;
		top: 11px;
		left: 0;
		right: 0
	}

	.dart-step.is-vertical {
		display: flex
	}

	.dart-step.is-vertical .dart-step__head {
		flex-grow: 0;
		width: 24px
	}

	.dart-step.is-vertical .dart-step__main {
		padding-left: 10px;
		flex-grow: 1
	}

	.dart-step.is-vertical .dart-step__title {
		line-height: 24px;
		padding-bottom: 8px
	}

	.dart-step.is-vertical .dart-step__line {
		width: 6rpx;
		top: 0;
		bottom: 0;
		left: 11px
	}

	.dart-step.is-center .dart-step__head,
	.dart-step.is-center .dart-step__main {
		text-align: center
	}

	.dart-step.is-center .dart-step__description {
		padding-left: 20%;
		padding-right: 20%
	}

	.dart-step.is-center .dart-step__line {
		left: 50%;
		right: -50%
	}
</style>
