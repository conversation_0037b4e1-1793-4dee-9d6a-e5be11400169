<template>
	<view class="weui-form">
		<view class="weui-cells__title">ETC用户信息</view>
		<view class="weui-cells">
			<view class="vux-x-input weui-cell ">
				<view class="weui-cell__hd"><view class="weui-label ">客户姓名</view></view>
				<view class="weui-cell__bd weui-cell__primary">
					<view class="weui-cell__value">{{ customerInfo.customer_name + ' 【' + customerType + '】 ' }}</view>
				</view>
				<view class="weui-cell__ft"></view>
			</view>
			<view class="vux-x-input weui-cell ">
				<view class="weui-cell__hd"><view class="weui-label ">车牌号</view></view>
				<view class="weui-cell__bd weui-cell__primary">
					<view class="weui-cell__value">{{ ' [' + vehicleColorStr + '] ' + vehicleInfo.vehicle_code + ' [' + vehicleClassType + '] ' }}</view>
				</view>
				<view class="weui-cell__ft"></view>
			</view>
			<view class="vux-x-input weui-cell ">
				<view class="weui-cell__hd"><view class="weui-label ">ETC卡号</view></view>
				<view class="weui-cell__bd weui-cell__primary">
					<view class="weui-cell__value">{{ vehicleInfo.cpu_card_id + '  ' + cpuStatus }}</view>
				</view>
				<view class="weui-cell__ft"></view>
			</view>
			<view class="vux-x-input weui-cell ">
				<view class="weui-cell__hd"><view class="weui-label ">OBU编号</view></view>
				<view class="weui-cell__bd weui-cell__primary">
					<view class="weui-cell__value">{{ vehicleInfo.obu_id + '  ' + obuStatus }}</view>
				</view>
				<view class="weui-cell__ft"></view>
			</view>
		</view>
	</view>
</template>

<script>
import { getCurrUserInfo, getCurrentCar } from '@/common/storageUtil.js';
import { getVehicleColor, getVehicleClassType, getVehicleType, getObuStatus, getCpuStatus } from '@/common/method/filter.js';
export default {
	name: '',
	components: {},
	data() {
		return {
			customerType: ''
			// vehicle
		};
	},
	computed: {
		obuStatus() {
			return getObuStatus(this.vehicleInfo.obu_status);
		},
		cpuStatus() {
			return getCpuStatus(this.vehicleInfo.cpu_status);
		},
		vehicleType() {
			return getVehicleType(this.vehicleInfo.vehicle_type);
		},
		vehicleClassType() {
			return getVehicleClassType(this.vehicleInfo.vehicle_class);
		},
		vehicleColorStr() {
			return getVehicleColor(this.vehicleInfo.vehicle_color);
		},
		customerInfo() {
			return getCurrUserInfo() || {};
		},
		vehicleInfo() {
			return getCurrentCar() || {};
		}
	},
	created() {
		this.customerType = this.customerInfo.customer_type == 1 ? '单位' : '个人';
		this.vehicleInfo.cpu_card_id = this.vehicleInfo.cpu_card_id ? this.vehicleInfo.cpu_card_id : '';
		this.vehicleInfo.obu_id = this.vehicleInfo.obu_id ? this.vehicleInfo.obu_id : '';
	},
	methods: {}
};
</script>

<style lang="scss"></style>
