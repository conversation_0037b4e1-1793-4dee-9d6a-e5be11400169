<template>
	<view class="send-logistics">
		<view class="weui-card form-item">
			<view class="weui-card-hd">
				<view class="weui-card-hd-wrapper">
					<view class="weui-card-hd-title">设备寄回物流信息</view>
				</view>
			</view>
			<view class="weui-card-bd">
				<view class="list vehicle-info">
					<view class="list-item align-items-center">
						<view class="list-item_label">
							物流单号
						</view>
						<view class="list-item_value">
							{{result.expressNumber || ''}}
						</view>
						<!-- 上门可查看物流详情 -->
						<image src="../../static/view_icon.png" mode="" class="view-icon" v-if="result.routeType == '0'"
							@click="logisticsDetail"></image>
					</view>
					<view class="list-item align-items-center">
						<view class="list-item_label">
							物流公司
						</view>
						<view class="list-item_value">
							{{result.expressCompanyName || ''}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tTitle from '@/pagesC/components/new-title/new-title.vue';
	export default {
		props: {
			result: {
				type: Object
			},
			isEdit: {
				type: Boolean,
				default: false
			},
			orderId: {
				type: String,
				default: ''
			}
		},
		components: {
			tTitle
		},
		data() {
			return {
				show: false,
				inputStyle: {
					"marginLeft": "12rpx",
					"fontSize": "26rpx",
					"color": "#333333",
					"lineHeight": '60rpx',
					"height": "60rpx"
				},
			}
		},
		computed: {

		},
		created() {},
		methods: {
			copy(data) {
				uni.setClipboardData({
					data: data,
					success: function() {
						uni.showToast({
							title: "复制成功"
						})
					}
				});
			},
			logisticsDetail() {
				uni.navigateTo({
					url: '/pagesC/changeBussiness/orderWl/orderWlDetail?orderId=' + this.orderId + '&result=' +
						JSON.stringify(this.result)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.send-logistics {
		.list-item_value {
			position: relative;
		}

		.scan-img {
			width: 30rpx;
			height: 30rpx;
			position: absolute;
			right: 18rpx;
			top: 20rpx;
			filter: grayscale(1);
		}

		.view-icon {
			width: 27rpx;
			height: 27rpx;
		}

	}
</style>
