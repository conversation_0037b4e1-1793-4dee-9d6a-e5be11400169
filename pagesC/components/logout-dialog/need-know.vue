<template>
	<view class="status-dialog" v-if="show">
		<view class="status-dialog__mask"></view>
		<view class="status-dialog__content" v-if="showNeedKnow">
			<view class="tips">办理须知</view>
			<view class="content" v-html="content" :style="[contentStyle]"></view>
			<view class="radio" @click="check=true">
				<view class="round" :class="{'check-round':check}"></view>
				<view class="value" :class="{'jitter-div':isJitter,'':!isJitter}">我已阅读，确认无问题</view>
			</view>
			<view class="interaction-two">
				<view class="interaction-one-comfirm" @click="clickComfirm">
					我已知晓，前往办理
				</view>
			</view>
		</view>

		<!-- 线上注销 -->
		<view class="status-dialog2__content" :class="content ? 'top-class':''" v-if="showTips && type == 'logout'">
			<view class="tips">温馨提示</view>
			<view class="content">
				<view class="result-wrapper" v-if="content">
					{{content}}
				</view>
				<view class="result-wrapper" v-if="contentArr.length > 0">
					<view class="red-text">
						{{redText}}
					</view>
					<view class="result" v-for="(item,index) in contentArr" :key="index">
						<template v-if="item.checkType != 9">
							{{index + 1}}.{{item.checkDesc}}
							<text class="link" v-if="item.checkType == 1" @click="linkTo(item.checkType)">查看解除办法</text>
							<text class="link" v-if="item.checkType == 4" @click="linkTo(item.checkType)">点击前往补缴</text>
							<text class="link" v-if="item.checkType == 5 || item.checkType == 6" @click="linkTo(item.checkType)">点击前往充值</text>
						</template>
						<template v-else>
							<handleText :handleText="item.checkDesc"></handleText>
						</template>
					</view>
					<view class="result">
						{{contentArr.length + 1}}.如有其他疑问，请联系在线客服或拨打广西捷通ETC客服热线 <text class="link"
							@click="call('0771-5896333')">0771-5896333</text>
					</view>
					<view class="result g-flex g-flex-center">
						<image src="../../static/search_fail.png" style="width: 150rpx;height: 164rpx;" mode=""></image>
					</view>
				</view>
				<view class="search-wrapper" v-if="!content && contentArr.length == 0">
					<view class="search">
						注销条件检测中...
					</view>
					<image src="../../static/logout_search.png" style="width: 150rpx;height: 164rpx;" mode=""></image>
				</view>
			</view>
			<view class="interaction-two" v-if="!(content && contentArr.length != 0)">
				<view class="interaction-one-comfirm" :style="{color:confirmColor}" @click="tipsComfirm">
					确定
				</view>
			</view>
		</view>
		<!-- 产品转换 -->
		<view class="status-dialog2__content" :class="content ? 'top-class':''" v-if="showTips && type == 'product'">
			<view class="tips">温馨提示</view>
			<view class="content">
				<view class="result-wrapper" v-if="content">
					{{content}}
				</view>
				<view class="result-wrapper" v-if="contentArr.length > 0">
					<view class="red-text">
						{{redText}}
					</view>
					<view class="result" v-for="(item,index) in contentArr" :key="index">
						<template v-if="item.checkType != 9">
							{{index + 1}}.{{item.checkDesc}}
							<text class="link" v-if="item.checkType == 1" @click="linkTo(item.checkType)">查看解除办法</text>
							<text class="link" v-if="item.checkType == 4" @click="linkTo(item.checkType)">点击前往补缴</text>
							<text class="link" v-if="item.checkType == 5" @click="linkTo(item.checkType)">点击前往充值</text>
						</template>
						<template v-else>
							<handleText :handleText="item.checkDesc"></handleText>
						</template>
					</view>
					<view class="result">
						{{contentArr.length + 1}}.如有其他疑问，请联系在线客服或拨打广西捷通ETC客服热线 <text class="link"
							@click="call('0771-5896333')">0771-5896333</text>
					</view>
					<view class="result g-flex g-flex-center">
						<image src="../../static/search_fail.png" style="width: 150rpx;height: 164rpx;" mode=""></image>
					</view>
				</view>
				<view class="search-wrapper" v-if="!content && contentArr.length == 0">
					<view class="search">
						产品转换条件检测中...
					</view>
					<image src="../../static/logout_search.png" style="width: 150rpx;height: 164rpx;" mode=""></image>
				</view>
			</view>
			<view class="interaction-two" v-if="!(content && contentArr.length != 0)">
				<view class="interaction-one-comfirm" :style="{color:confirmColor}" @click="tipsComfirm">
					确定
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import handleText from './handle-text.vue'
	export default {
		name: 'status-dialog',
		components: {
			handleText
		},
		props: {
			content: { // 提示的文案内容
				type: String,
				default: ""
			},
			show: { // 弹窗的显示
				type: [Boolean, String],
				default: false
			},
			contentStyle: {
				type: Object,
				default () {
					return {};
				}
			},
			contentArr: {
				type: Array,
				default: []
			},
			showNeedKnow: { // 弹窗的显示
				type: [Boolean, String],
				default: false
			},
			showTips: { // 弹窗的显示
				type: [Boolean, String],
				default: false
			},
			redText: {
				type: String,
				default: '您当前不符合注销条件!'
			},
			type: {
				type: String,
				default: 'logout'
			}
		},
		data() {
			return {
				check: false,
				isJitter: false,
				showResult: false,
			}
		},

		methods: {
			call(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber: phoneNumber //仅为示例
				});
			},
			clickComfirm() {
				let that = this
				if (!that.check) {
					that.isJitter = true
					uni.vibrateLong({
						success: function() {
							console.log('success');
						}
					});
					setTimeout(() => {
						that.isJitter = false
					}, 1000)
					console.log(that.isJitter)
					return
				}
				that.check = false
				that.$emit('comfirm')
			},
			tipsComfirm() {
				this.$emit('update:show', false)
			},

			linkTo(type) {
				switch (type) {
					case 1:
						//查看解除办法
						//黑名单
						let url = 'https://mp.weixin.qq.com/s/_0mvlJbF1BTFrr24s2W4qw'

						uni.navigateTo({
							url: '/pages/uni-webview/h5-webview?ownPath=' + encodeURIComponent(url) +
								'&title=解除黑名单'
						})
						break;
					case 4:
						//前往补缴页面
						//有车辆去车辆列表
						uni.navigateTo({
							url: '/pagesB/vehicleBusiness/vehicleList?fontType=' + 'afterPay'
						})
						break;
					case 5:
						//前往充值
						this.$emit('goToRecharge')
						break;
					default:
						break;
				}
			}
		}
	}
</script>

<style lang="scss">
	$bg-color-mask: rgba(0, 0, 0, 0.5); //遮罩颜色
	$bg-color-hover: #f1f1f1; //点击状态颜色

	.top-class {
		top: calc(50% - 300rpx) !important;
	}

	.status-dialog {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 99999;

		&__mask {
			display: block;
			position: fixed;
			z-index: 1998;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: $bg-color-mask;
		}

		&__content {
			position: relative;
			z-index: 2000;
			top: calc(50% - 500rpx);
			left: 50%;
			margin-left: -310rpx;
			width: 620rpx;
			height: 1038rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.status-image {
				margin-top: 44rpx;
			}

			.tips {
				width: 100%;
				text-align: center;
				font-size: 16px;
				padding: 40rpx 0;
				border-bottom: 2rpx solid #E9E9E9;
			}

			.content {
				width: calc(100% - 90rpx);
				height: calc(100% - 330rpx);
				font-size: 28rpx;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				text-align: center;
				color: #666666;
				line-height: 50rpx;
				margin-top: 36rpx;
				overflow-y: scroll;
			}

			.radio {
				display: flex;
				align-items: center;

				.round {
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
					border: 1rpx solid #A8A8A8;
				}

				.check-round {
					border: 1rpx solid #0066E9;
					background-color: #0066E9;
					position: relative;
				}

				.jitter-div {
					animation: 1s jitter;
				}

				@keyframes jitter {
					0% {
						transform: scale(1.4, 1.4);
					}

					10% {
						transform: scale(1, 1);
					}

					25% {
						transform: scale(1.2, 1.2);
					}

					50% {
						transform: scale(1, 1);
					}

					70% {
						transform: scale(1.2, 1.2);
					}

					100% {
						transform: scale(1, 1);
					}
				}

				.check-round::before {
					font-family: "cuIcon";
					content: "\e645";
					position: absolute;
					color: #fff;
					top: 0;
					right: 0;
					font-size: 24rpx;
				}

				.value {
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #666666;
					line-height: 40rpx;
					margin-left: 18rpx;
				}
			}

			.comfirm {
				width: 100%;
				height: 88rpx;
				border-top: .5px solid #f5f5f5;
				text-align: center;
				line-height: 88rpx;
				font-size: 15px;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				position: absolute;
				bottom: 0;
			}

			.interaction-two {
				// border-top: .5px solid #f5f5f5;
				width: 100%;
				height: 71rpx;
				position: absolute;
				margin-top: 42rpx;
				bottom: 35rpx;
				display: flex;
				justify-content: center;

				.interaction-two-cancel {
					width: 50%;
					height: 88rpx;
					// border-right: .5px solid #F5F5F5;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					line-height: 88rpx;

				}

				.interaction-two-comfirm {
					width: 50%;
					height: 88rpx;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #fff;
					line-height: 88rpx;
					border-bottom-right-radius: 24rpx;
					background-color: #0066E9;
				}

				.interaction-one-comfirm {
					width: 336rpx;
					height: 71rpx;
					font-size: 30rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 71rpx;
					text-align: center;
					background-color: #0066E9;
					border-radius: 14rpx;
				}
			}
		}
	}


	.status-dialog2 {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 99999;

		&__mask {
			display: block;
			position: fixed;
			z-index: 1998;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: $bg-color-mask;
		}

		&__content {
			position: relative;
			z-index: 2000;
			top: 10%;
			left: 50%;
			margin-left: -310rpx;
			width: 620rpx;
			// height: 620rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.status-image {
				margin-top: 44rpx;
			}

			.tips {
				width: 100%;
				text-align: center;
				margin-top: 44rpx;
				font-size: 16px;
				// margin-bottom: 20rpx;
				font-weight: bold;
				padding-bottom: 43rpx;
				border-bottom: 2rpx solid #E9E9E9;
			}

			.content {
				width: calc(100% - 80rpx);
				height: auto;
				font-size: 28rpx;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				color: #666666;
				line-height: 40rpx;

				.search-wrapper {

					text-align: center;

					.search {
						margin: 43rpx 0 68rpx 0;
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #323435;
						line-height: 40rpx;
					}
				}

				.result-wrapper {
					margin-top: 27rpx;
					margin-bottom: 50rpx;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #323435;
					line-height: 48rpx;

					.red-text {
						margin-bottom: 20rpx;
						text-align: center;
						font-size: 30rpx;
						font-family: PingFangSC-Medium, PingFang SC;
						font-weight: 500;
						color: #F65B5B;
						line-height: 42rpx;
					}

					.result {
						margin-bottom: 20rpx;
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #323435;
						line-height: 48rpx;

						.link {
							color: #0066E9;
						}
					}
				}
			}

			.comfirm {
				width: 100%;
				height: 88rpx;
				border-top: .5px solid #f5f5f5;
				text-align: center;
				line-height: 88rpx;
				font-size: 15px;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				position: absolute;
				bottom: 0;
			}

			.interaction-two {
				// border-top: .5px solid #f5f5f5;
				width: 100%;
				height: 71rpx;
				// position: absolute;
				// bottom: 54rpx;
				margin-bottom: 54rpx;
				display: flex;
				justify-content: center;

				.interaction-two-cancel {
					width: 50%;
					height: 88rpx;
					// border-right: .5px solid #F5F5F5;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					line-height: 88rpx;

				}

				.interaction-two-comfirm {
					width: 50%;
					height: 88rpx;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #fff;
					line-height: 88rpx;
					border-bottom-right-radius: 24rpx;
					background-color: #0066E9;
				}

				.interaction-one-comfirm {
					width: 232rpx;
					height: 71rpx;
					font-size: 30rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 71rpx;
					text-align: center;
					background-color: #0066E9;
					border-radius: 14rpx;
				}
			}

			.blue-img {
				display: flex;
				margin-top: 55rpx;

				.blue,
				.zgjt {
					width: 48%;
					text-align: center;

					.value {
						margin-top: 39rpx;
					}
				}

				.dash-line {
					width: 1rpx;
					height: 154rpx;
					width: 1rpx;
					height: 154rpx;
					background-image: linear-gradient(to bottom, #C3C3C3 0%, #C3C3C3 60%, transparent 20%);
					background-size: 100% 8px;
					background-repeat: repeat-y;
				}
			}
		}
	}
</style>