<template>
	<view class="handle-text">
		<view class="text" v-if="handleText == '山东信联'">
			1.您当前的签约机构为山东信联，请通过“e高速”办理注销业务。如有其他疑问请拨打山东信联客服热线<text class="link" @click="call('95011')">95011</text>
		</view>
		<view class="text" v-if="handleText == '货车帮'">
			1.您当前的签约机构为货车帮，请通过“运满满”小程序办理注销业务。如有其他疑问请拨打货车帮客服热线<text class="link" @click="call('95006')">95006</text>
		</view>
		<view class="text" v-if="handleText == '微信高灯'">
			1.您当前的签约机构为ETC助手，请通过“ETC助手”小程序办理注销业务。如有其他疑问请在9:00至21:00时段拨打ETC助手客服热线<text class="link"
				@click="call('400-8291777')">400-8291777</text>
		</view>
		<view class="text" v-if="handleText == '分米'">
			1.您当前的签约机构为任通行/任货行，请通过“任货行”/“任通行”小程序办理注销业务。如有其他疑问请拨打任货行/任通行客服热线<text class="link"
				@click="call('400-9987386')">400-9987386</text>
		</view>
		<view class="text" v-if="handleText == '慧联运'">
			1.您当前的签约机构为慧联运，请打开“慧联运”APP办理注销业务。如有其他疑问请在7:00至21:30 时段拨打慧联运客服热线<text class="link"
				@click="call('0551-62620561')">0551-62620561</text>
		</view>
		<view class="text" v-if="handleText == '支付宝'">
			1.您当前的签约机构为支付宝，登录支付宝APP，搜索“ETC服务”小程序联系客服办理注销业务。如有其他疑问请拨打支付宝客服热线<text class="link"
				@click="call('95188')">95188</text>
		</view>
		<view class="text" v-if="handleText == '支付宝-中视'">
			1.您当前的签约机构为支付宝，请登录支付宝并搜索“广西捷通ETC办理在线服务”小程序提交注销申请。如有其他疑问请在9:00至21:00时段拨打支付宝客服热线<text class="link"
				@click="call('400-0555082')">400-0555082</text>、<text class="link"
				@click="call('020-22326200')">020-22326200</text>
		</view>
		<view class="text" v-if="isShow">
			1.{{handleText}}
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			handleText: {
				type: String,
				default: ''
			}
		},
		data() {
			return {}
		},
		computed: {
			isShow() {
				if (this.handleText == '山东信联' || this.handleText == '货车帮' || this.handleText == '微信高灯' || this
					.handleText == '分米' || this.handleText == '慧联运' || this.handleText == '支付宝' || this.handleText ==
					'支付宝-中视'
				) {
					return false
				} else {
					return true
				}
			}
		},
		methods: {
			call(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber: phoneNumber
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.handle-text {
		margin-bottom: 20rpx;
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
		line-height: 48rpx;
	}

	.link {
		color: #0066E9;
	}
</style>