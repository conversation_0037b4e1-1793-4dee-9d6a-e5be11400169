<template>
	<view class="weui-bottom-fixed g-flex">
		<view v-for="(item,index) in buttonList" :key="index" class="weui-bottom-fixed__box bottom-box"><button
				class="weui-btn btn-item" :class="item.type == 'info'?'gray-color':''"
				@click="handleClick(item.handle)">{{item.title}}</button>
		</view>
		<!-- 		<view class="weui-bottom-fixed__box bottom-box"><button class="weui-btn weui-btn_primary"
				@click="handleClick('toNext')">下一步</button></view> -->
	</view>
</template>

<script>
	export default {
		props: {
			buttonList: {
				type: Array
			},
			moreBtn: {
				type: Boolean,
				default: false
			}
		},
		methods: {
			handleClick(handle) {
				if (!this.moreBtn) {
					//少按钮返回
					this.$emit(handle)
				} else {
					//多按钮返回
					this.$emit('click', handle)
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.weui-bottom-fixed {
		z-index: 98;
		padding: 20rpx 40rpx 60rpx 40rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
		background-color: #0066E9;
		color: #ffffff;
	}

	.weui-bottom-fixed__box {
		padding: 0;
		border-top: 0;

		&:nth-child(2) {
			margin-left: 32rpx;
		}
	}

	.gray-color {
		background-color: #ffffff !important;
		color: #0066E9 !important;

		&::after {
			border-color: #0066E9 !important;
		}
	}
</style>