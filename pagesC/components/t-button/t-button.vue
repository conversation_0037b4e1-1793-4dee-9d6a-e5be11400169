<template>
	<view class="weui-bottom-fixed">
		<view class="weui-bottom-fixed__box bottom-box">
			<view class="btn-item" v-for="(item,index) in buttonList" :key="index" :class="index==1?'btn-item-right':'btn-item-left'">
				<button class="weui-btn weui-btn_primary"
					:class="item.title == '返回首页'?'weui-btn_quit':''"
					@click="handleClick(item.handle)">{{item.title}}</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			buttonList: {
				type: Array
			}
		},
		methods: {
			handleClick(handle) {
				this.$emit(handle)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.weui-bottom-fixed {
		z-index: 98;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}
	.btn-item-left {
		margin-right: 32rpx;
	}
</style>
