<template>
	<view class="title-wrapper">
		<view class="title">
			{{title}}
		</view>
		<view class="question-wrapper" @click="rightClick" v-if="rightTitle">
			<view class="question-text" style="margin-right: 10rpx;">
				{{rightTitle}}
			</view>
			<image v-if="questionIcon" style="width: 28rpx;height: 28rpx;margin-top: 3rpx;"
				src="../../static/help-icon.png" mode=""></image>
		</view>
		<slot name="rightBtn"></slot>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: ''
			},
			rightTitle: {
				type: String,
				default: ''
			},
			questionIcon: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {

			}
		},
		methods: {
			rightClick() {
				this.$emit('click')
			}
		}
	}
</script>

<style scoped lang="scss">
	.title-wrapper {
		display: flex;
		justify-content: space-between;

		.title {
			font-size: 32rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #333333;
		}

		.question-wrapper {
			display: flex;

			.question-text {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #0081FF;
			}
		}
	}
</style>