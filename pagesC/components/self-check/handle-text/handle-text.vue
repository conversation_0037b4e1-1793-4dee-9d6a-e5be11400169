<template>
	<view class="handle-text">
		<view class="text" v-if="handleCode == 1">
			您当前的签约机构为：<text>{{orgName}}</text>，请确保所绑定银行卡余额/信用额度充足，<text class="link"
				@click="linkTo('1')">点击此处</text>可查看解除限制方法
		</view>
		<view class="text" v-if="handleCode == 2">
			您当前的签约机构为：山东信联,请确保所绑定银行卡余额/信用额度充足，<text class="link"
				@click="linkTo('1')">点击此处</text>可查看解除限制方法。如有其他疑问请拨打客户热线<text class="link"
				@click="call('95011')">95011</text>
		</view>
		<view class="text" v-if="handleCode == 3">
			您当前的签约机构为：货车帮,请确保所绑定账户余额充足，<text class="link" @click="linkTo('1')">点击此处</text>可查看解除限制方法。如有其他疑问请拨打客户热线<text
				class="link" @click="call('95006')">95006</text>
		</view>
		<view class="text" v-if="handleCode == 4">
			您当前的签约机构为ETC助手，请确保所绑定账户余额充足，<text class="link"
				@click="linkTo('1')">点击此处</text>可查看解除限制方法。如有其他疑问请在9:00至21:00时段拨打客服热线<text class="link"
				@click="call('400-8291777')">400-8291777</text>
		</view>
		<view class="text" v-if="handleCode == 5">
			您当前的签约机构为任通行/任货行，请确保所绑定账户余额充足，<text class="link"
				@click="linkTo('1')">点击此处</text>可查看解除限制方法。如有其他疑问请拨打客服热线<text class="link"
				@click="call('400-9987386')">400-9987386</text>
		</view>
		<view class="text" v-if="handleCode == 6">
			您当前的签约机构为慧联运，请确保所绑定账户余额充足，<text class="link" @click="linkTo('1')">点击此处</text>可查看解除限制方法。如有其他疑问请在7:00至21:30
			时段拨打客服热线<text class="link" @click="call('0551-62620561')">0551-62620561</text>
		</view>
		<view class="text" v-if="handleCode == 7">
			您当前的签约机构为支付宝，请确保所绑定账户余额充足，<text class="link" @click="linkTo('1')">点击此处</text>可查看解除限制方法。如有其他疑问请拨打客服热线<text
				class="link" @click="call('95188')">95188</text>
		</view>
		<view class="text" v-if="handleCode == 8">
			您当前的签约机构为支付宝，请确保所绑定账户余额充足，<text class="link"
				@click="linkTo('1')">点击此处</text>可查看解除限制方法。如有其他疑问请在9:00至21:00时段拨打客服热线<text class="link"
				@click="call('400-0555082')">400-0555082</text>、<text class="link"
				@click="call('020-22326200')">020-22326200</text>
		</view>
		<view class="text" v-if="handleCode == 9">
			请及时充值，确保ETC卡余额充足。
		</view>
		<view class="text" v-if="handleCode == 10 || handleCode == 11">
			请及时还清月账单，点击下方链接可自助搜索网点地址
		</view>
		<view class="text" v-if="handleCode == 12">
			您的余额较低，为了不影响您的通行，请<text class="link" @click="linkTo('2')">点击此处</text>提前充值
		</view>
		<view class="text" v-if="handleCode == 13 || handleCode == 21">
			请及时前往捷通公司的“八桂行卡客户服务中心”线下网点处理，点击下方链接可自助搜索网点地址
		</view>
		<view class="text" v-if="handleCode == 14">
			您当前的签约机构为货车帮，请前往捷通公司的“八桂行卡客户服务中心”线下网点处理，点击下方链接可自助搜索网点地址。或在运满满APP线上处理，如有疑问请拨打货车帮客服热线<text class="link"
				@click="call('95006')">95006</text>
		</view>
		<view class="text" v-if="handleCode == 15">
			您当前的签约机构为货车帮，请及时联系货车帮处理，货车帮客服热线<text class="link" @click="call('95006')">95006</text>
		</view>
		<view class="text" v-if="handleCode == 16">
			您当前的签约机构为ETC助手，请尽快登陆ETC助手小程序处理设备问题。如有疑问请在9:00至21:00时段拨打客服热线<text class="link"
				@click="call('400-8291777')">400-8291777</text>
		</view>
		<view class="text" v-if="handleCode == 17">
			您当前的签约机构为任通行/任货行，请尽快登录任通行小程序处理问题。如有疑问请拨打客服热线<text class="link"
				@click="call('400-9987386')">400-9987386</text>
		</view>
		<view class="text" v-if="handleCode == 18">
			您当前的签约机构为慧联运，相关问题请在7:00至21:30 时段拨打客服热线<text class="link"
				@click="call('0551-62620561')">0551-62620561</text>处理
		</view>
		<view class="text" v-if="handleCode == 19">
			您当前的签约机构为支付宝，请登陆支付宝APP，搜索并登录ETC服务联系客服处理问题。如有其他疑问请拨打客服热线<text class="link"
				@click="call('95188')">95188</text>
		</view>
		<view class="text" v-if="handleCode == 20">
			您当前的签约机构为支付宝，请登陆支付宝，搜索并登录捷通ETC在线服务小程序联系客服处理。如有疑问请在9:00至21:00时段拨打客服热线<text class="link"
				@click="call('400-0555082')">400-0555082</text>、<text class="link"
				@click="call('020-22326200')">020-22326200</text>
		</view>
		<view class="text" v-if="handleCode == 22">
			您当前的签约机构为：<text>{{orgName}}</text>，请确保绑定银行卡余额充足，<text class="link"
				@click="linkTo('1')">点击此处</text>查看处理办法。设备异常问题请及时前往捷通公司的“八桂行卡客户服务中心”线下网点处理
		</view>
		<view class="text" v-if="handleCode == 23">
			您当前的签约机构为山东信联，请确保所绑定账户余额充足，<text class="link"
				@click="linkTo('1')">点击此处</text>可查看限制使用的处理办法。设备异常问题请及时前往捷通公司的“八桂行卡客户服务中心”线下网点处理
		</view>
		<view class="text" v-if="handleCode == 24">
			您当前的签约机构为货车帮，请确保所绑定账户余额充足，<text class="link"
				@click="linkTo('1')">点击此处</text>可查看限制使用的处理办法。设备异常问题请前往捷通公司的“八桂行卡客户服务中心”线下网点处理（点击下方链接可自助搜索网点地址）或在运满满APP线上处理，如有疑问请拨打货车帮客服热线<text
				class="link" @click="call('95006')">95006</text>
		</view>
		<view class="text" v-if="handleCode == 25">
			您当前的签约机构为货车帮，请确保所绑定账户余额充足，<text class="link"
				@click="linkTo('1')">点击此处</text>可查看限制使用的处理办法。设备异常问题请在运满满APP线上处理，如有疑问请拨打货车帮客服热线<text class="link"
				@click="call('95006')">95006</text>
		</view>
		<view class="text" v-if="handleCode == 26 || handleCode==27">
			请及时还清月账单，并尽快前往捷通公司的“八桂行卡客户服务中心”线下网点处理设备异常问题，点击下方链接可自助搜索网点地址
		</view>
		<view class="text" v-if="handleCode == 28">
			请及时充值，确保ETC账户余额充足。
		</view>
	</view>
</template>

<script>
	import {
		getAccountId,
	} from '@/common/storageUtil.js'
	export default {
		props: {
			handleCode: {
				type: String,
				default: ''
			},
			orgName: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				vehicleAllDataList: []
			}
		},
		created() {
			this.getVerhicleList()
		},
		methods: {
			call(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber: phoneNumber //仅为示例
				});
			},
			getVerhicleList() {
				// this.isLoading = true
				// setCurrUserInfo({})
				let data = {
					routePath: this.$interfaces.customerBizView.method,
					bizContent: {
						customer_id: getAccountId()
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						// this.isAttention = true
						// this.isLoading = false
						// setCurrUserInfo(res.data)
						this.customerInfo = res.data
						if (!res.data.customer_id) return
						this.getAllVerhicleList(res.data.customer_id)
					})
					.catch((error) => {
						// this.isLoading = false
					})
			},
			getAllVerhicleList(id) {
				console.log(id, '-----');
				let params = {
					customerId: id
				}
				this.$request
					.post(this.$interfaces.vehicleList, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							console.log('res.data', res.data)
							this.vehicleAllDataList = res.data || []
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
			},
			linkTo(type) {
				if (type == '1') {
					//黑名单
					let url = 'https://mp.weixin.qq.com/s/_0mvlJbF1BTFrr24s2W4qw'

					uni.navigateTo({
						url: '/pages/uni-webview/h5-webview?ownPath=' + encodeURIComponent(url)
					})
				} else {
					let flag = !!this.vehicleAllDataList.length
					let url = flag ?
						'/pagesB/vehicleBusiness/vehicleList' :
						'/pagesB/vehicleBusiness/noVehicleList'
					uni.navigateTo({
						url: url + '?fontType=' + 'recharge'
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.handle-text {
		padding: 0 50rpx;

		.text {
			text-indent: 50rpx;
			letter-spacing: 1rpx;
		}
	}

	.link {
		font-weight: 700;
		margin-top: 30rpx;
		text-align: center;
		color: #3E98FF;
		text-decoration: underline;
		text-underline-offset: 5rpx;
	}
</style>