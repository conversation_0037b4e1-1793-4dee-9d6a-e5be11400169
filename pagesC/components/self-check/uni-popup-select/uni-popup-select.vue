<template>
	<view class="select-wrapper">
		<view class="title"><text class="text">车牌颜色选择</text>
			<icon @click="close()" class="icon-cancel" type="cancel" size="26" color="#777777" />
		</view>
		<scroll-view class="scroll" scroll-y="true">
			<view @click="select(item)" class="select-container" v-for="(item,index) in dataPicker" :key="index">
				<image class="select-img" :src="item.src" mode=""></image>
				<view class="select-text">
					{{item.label}}
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import popup from '@/uni_modules/uni-popup/components/uni-popup/popup.js'
	export default {
		name: 'UniPopupShare',
		mixins: [popup],
		emits: ['select'],
		props: {
			title: {
				type: String,
				default: ''
			},
			cancelTitle: {
				type: String,
				default: ''
			},
			beforeClose: {
				type: <PERSON><PERSON>an,
				default: false
			}
		},
		data() {
			return {
				dataPicker: [{
						label: "蓝",
						value: "0",
						src: '../../../static/newSelfCheck/plate_blue.png',
					},
					{
						label: "黄",
						value: "1",
						src: '../../../static/newSelfCheck/plate_green.png'
					},
					{
						label: "黑",
						value: "2",
						src: '../../../static/newSelfCheck/plate_black.png'
					},
					{
						label: "白",
						value: "3",
						src: '../../../static/newSelfCheck/plate_white.png'
					},
					{
						label: "渐变绿",
						value: "4",
						src: '../../../static/newSelfCheck/plate_gradient_green.png'
					},
					{
						label: "黄绿双拼",
						value: "5",
						src: '../../../static/newSelfCheck/plate_yellow_green.png'
					}
				]
			}
		},

		methods: {
			/**
			 * 关闭窗口
			 */
			close() {
				if (this.beforeClose) return
				this.popup.close()
			},
			select(item) {
				this.$emit('select', item)
				this.close()
			}
		}
	}
</script>
<style lang="scss">
	.scroll {
		height: 400rpx;
	}


	.title {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		font-size: 30rpx;
		font-weight: 500;

		.icon-cancel {
			position: absolute;
			right: 12rpx;
			top: 12rpx;
		}
	}

	.select-container {
		display: flex;
		// justify-content: center;
		align-items: center;
		border-bottom: 1rpx solid #eeeeee;
		padding: 20rpx 0;
		padding-left: 30%;

		&:last-of-type {
			border-bottom: 0;
		}

		.select-img {
			margin-right: 20rpx;
			width: 209rpx;
			height: 69rpx;
		}
	}
</style>
