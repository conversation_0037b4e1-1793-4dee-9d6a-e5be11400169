<template>
	<view class="btn-wrapper">
		<view class="btn" @click="click">
			{{name}}
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			name: {
				type: String,
				default: '下一步'
			}
		},
		data() {
			return {

			}
		},
		created() {

		},
		methods: {
			click() {
				this.$emit('click')
			}
		},
	}
</script>

<style lang='scss' scoped>
	.btn-wrapper {
		height: 100%;
		/* background-color: #f7f7f7; */
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.btn {
		padding: 16rpx 180rpx;
		background-color: #007AFF;
		color: $uni-bg-color;
		border-radius: 200rpx;
		font-size: 32rpx;
		letter-spacing: 4rpx;
	}
</style>