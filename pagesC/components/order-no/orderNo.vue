<template>
	<view class="weui-top-card">
		<view class="weui-card-bd">
			<view class="list-item">
				<view class="list-item_label">
					订单编号：
				</view>
				<view class="list-item_value">
					{{result.orderNo || result.orderId}}
				</view>
			</view>
			<view class="line"></view>
			<view class="list-item">
				<view class="copy" @click="copy(result.orderNo || result.orderId)">复制</view>
			</view>
			<image src="../../static/promot.png" mode="" class="list-item-icon"></image>
		</view>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	export default {
		props: {
			result: {
				type: Object
			},
		},
		data() {
			return {

			}
		},
		methods: {
			copy(data) {
				uni.setClipboardData({
					data: data,
					success: function() {
						// uni.showToast({
						// 	title: "复制成功"
						// })
					}
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.weui-top-card {
		width: 100%;
		height: 68rpx;
		line-height: 68rpx;
		border-radius: 12rpx;
		background: #fff;

		.weui-card-bd {
			display: flex;
			width: 100%;
			padding: 0 26rpx;
			height: 100%;
			position: relative;
		}

		.line {
			width: 1rpx;
			height: 31rpx;
			border: 1rpx solid #C6C6C6;
			margin: 17rpx 35rpx;
		}

		.list-item {
			display: flex;

			.list-item_label {
				width: 150rpx;
				font-size: 30rpx;
				font-weight: 400;
				color: #999999;
			}

			.list-item_value {
				width: 350rpx;
				font-size: 30rpx;
				font-weight: 400;
				color: #323435;
			}

			.copy {
				width: auto;
				font-size: 30rpx;
				font-weight: 400;
				color: #3874FF;
			}
			
		}
		.list-item-icon {
			width: 40rpx;
			height: 40rpx;
			background-size: 100%;
			margin: 12rpx 0 0 20rpx;
			position: absolute;
			right: 26rpx;
		}
	}
</style>
