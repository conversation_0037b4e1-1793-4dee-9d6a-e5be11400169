<template>
	<view class="status-dialog" v-if="show">
		<view class="status-dialog__mask"></view>
		<view class="status-dialog__content">
			<view class="tips">提示</view>
			<view class="content">
				<view>请确认ETC设备上是否有以下两种标识之一</view>
				<view class="blue-img">
					<view class="zgjt">
						<image src="../../static/newSelfCheck/zgjt.png" mode="" style="width: 117rpx;height: 117rpx;">
						</image>
						<view class="value">中国交通标志图标</view>
					</view>
					<view class="dash-line"></view>
					<view class="blue">
						<image src="../../static/newSelfCheck/blue.png" mode="" style="width: 117rpx;height: 117rpx;">
						</image>
						<view class="value">蓝牙标志图标</view>
					</view>
				</view>
			</view>
			<view class="interaction-two">
				<view class="interaction-two-cancel" :style="{color:cancelColor}" @click="clickCancel">无
				</view>
				<view class="interaction-two-comfirm" :style="{color:confirmColor}" @click="clickComfirm">
					确定
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// import parse from 'mini-html-parser2';
	export default {
		name: 'status-dialog',
		props: {
			isShowTip: { // 是否显示提示头
				type: [Boolean, String],
				default: true
			},
			cancelColor: { //取消按钮颜色
				type: String,
				default: '#666666'
			},
			confirmColor: { //确认按钮颜色
				type: String,
				default: '#fff'
			},
			show: { // 弹窗的显示
				type: [Boolean, String],
				default: false
			},
		},
		data() {
			return {
				htmlNodes: [],
			}
		},
		watch: {

		},
		mounted() {},
		methods: {
			clickComfirm() {
				this.$emit('comfirm')
			},
			clickCancel() {
				this.$emit('cancel')
			}
		}
	}
</script>

<style lang="scss">
	$bg-color-mask:rgba(0, 0, 0, 0.5); //遮罩颜色
	$bg-color-hover:#f1f1f1; //点击状态颜色

	.status-dialog {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 99999;

		&__mask {
			display: block;
			position: fixed;
			z-index: 1998;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: $bg-color-mask;
		}

		&__content {
			position: relative;
			z-index: 2000;
			top: calc(50% - 300rpx);
			left: 50%;
			margin-left: -310rpx;
			width: 620rpx;
			height: 608rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.status-image {
				margin-top: 44rpx;
			}

			.tips {
				width: 100%;
				text-align: center;
				margin-top: 44rpx;
				font-size: 16px;
				// margin-bottom: 20rpx;
				padding-bottom: 43rpx;
				border-bottom: 2rpx solid #E9E9E9;
			}

			.content {
				width: calc(100% - 80rpx);
				height: auto;
				font-size: 28rpx;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				text-align: center;
				color: #666666;
				line-height: 40rpx;
				margin-top: 36rpx;
				// margin-top: 11rpx;
			}

			.comfirm {
				width: 100%;
				height: 88rpx;
				border-top: .5px solid #f5f5f5;
				text-align: center;
				line-height: 88rpx;
				font-size: 15px;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				position: absolute;
				bottom: 0;
			}

			.interaction-two {
				border-top: .5px solid #f5f5f5;
				width: 100%;
				height: 88rpx;
				position: absolute;
				bottom: 0;
				display: flex;

				.interaction-two-cancel {
					width: 50%;
					height: 88rpx;
					// border-right: .5px solid #F5F5F5;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					line-height: 88rpx;

				}

				.interaction-two-comfirm {
					width: 50%;
					height: 88rpx;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #fff;
					line-height: 88rpx;
					border-bottom-right-radius: 24rpx;
					background-color: #0066E9;
				}

				.interaction-one-comfirm {
					width: 100%;
					height: 88rpx;
					font-size: 28rpx;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #fff;
					line-height: 88rpx;
					background-color: #0066E9;
					border-radius: 0 0 24rpx 24rpx;
				}
			}

			.blue-img {
				display: flex;
				margin-top: 55rpx;

				.blue,
				.zgjt {
					width: 48%;
					text-align: center;

					.value {
						margin-top: 39rpx;
					}
				}

				.dash-line {
					width: 1rpx;
					height: 154rpx;
					width: 1rpx;
					height: 154rpx;
					background-image: linear-gradient(to bottom, #C3C3C3 0%, #C3C3C3 60%, transparent 20%);
					background-size: 100% 8px;
					background-repeat: repeat-y;
				}
			}
		}
	}
</style>
