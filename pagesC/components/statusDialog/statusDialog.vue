<template>
	<view class="status-dialog" v-if="show">
		<view class="status-dialog__mask"></view>
		<view class="status-dialog__content">
			<view class="tips" v-if="isShowTip">{{tips}}</view>
			<view class="status-image" v-else>
				<auto-image v-if="status" width="88rpx" height="88rpx"
					src="../../../static/components/ico_popup_success.png"></auto-image>
				<auto-image v-else width="88rpx" height="88rpx" src="../../../static/components/ico_popup_error.png">
				</auto-image>
			</view>
			<view class="content" v-html="content" @click="btnClick()" :style="[contentStyle]"></view>

			<view class="interaction-two" v-if="showCancel">
				<view class="interaction-two-cancel" :style="{color:cancelColor}" @click="clickCancel">{{cancelText}}
				</view>
				<view class="interaction-two-comfirm" :style="{color:confirmColor}" @click="clickComfirm">
					{{confirmText}}
				</view>
			</view>
			<view class="interaction-two" v-else-if="showOneBtn">
				<view class="interaction-one-comfirm" :style="{color:confirmColor}" @click="clickComfirm">
					{{confirmText}}
				</view>
			</view>
			<view v-else class="comfirm" :style="{color:confirmColor}" @click="clickComfirm">{{confirmText}}</view>
		</view>
	</view>
</template>

<script>
	// import parse from 'mini-html-parser2';
	export default {
		name: 'status-dialog',
		props: {
			isShowTip: { // 是否显示提示头
				type: [Boolean, String],
				default: false
			},
			content: { // 提示的文案内容
				type: String,
				default: ""
			},
			status: { // 提示状态的选择，默认为true
				type: [Boolean, String],
				default: true
			},
			cancelColor: { //取消按钮颜色
				type: String,
				default: '#666666'
			},
			confirmColor: { //确认按钮颜色
				type: String,
				default: '#5591ff'
			},
			confirmText: {
				type: String,
				default: '确定'
			},
			cancelText: {
				type: String,
				default: '取消'
			},
			showCancel: { //是否显示取消按钮，默认为 true
				type: [Boolean, String],
				default: true
			},
			showOneBtn: { //是否只展示一个确认按钮
				type: [Boolean, String],
				default: false
			},
			show: { // 弹窗的显示
				type: [Boolean, String],
				default: false
			},
			tips: {
				type: String,
				default: "提示"
			},
			contentStyle: {
				type: Object,
				default () {
					return {};
				}
			},
			isClick: {
				type: [Boolean, String],
				default: false
			}

		},
		data() {
			return {
				htmlNodes: [],
			}
		},
		watch: {
			content(newVal) {
				// #ifdef MP-ALIPAY
				let str = newVal.replace(/"/g, "'")
				str = str.replace(/<img /g, "<img width='100% '")
				str = str.replace(/<section/g, '<div').replace(/\/section>/g, '/div>');
				str = str.replace(/style="[^=>]*"/g, "<span style='color:red;font-weight:bold'>")
				parse(str, (err, nodesList) => {
					if (!err) {
						console.log("nodesList", nodesList)
						this.htmlNodes = nodesList
						console.log(this.htmlNodes)
					}
				})
				// #endif
			}
		},
		mounted() {

		},
		methods: {
			clickComfirm() {
				this.$emit('comfirm')
			},
			clickCancel() {
				this.$emit('cancel')
			},
			btnClick() {
				console.log(this.isClick)
			}
		}
	}
</script>

<style lang="scss">
	$bg-color-mask:rgba(0, 0, 0, 0.5); //遮罩颜色
	$bg-color-hover:#f1f1f1; //点击状态颜色

	.status-dialog {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 99999;

		&__mask {
			display: block;
			position: fixed;
			z-index: 1998;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: $bg-color-mask;
		}

		&__content {
			position: relative;
			z-index: 2000;
			top: calc(50% - 380rpx);
			left: 50%;
			margin-left: -300rpx;
			width: 600rpx;
			height: 760rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.status-image {
				margin-top: 44rpx;
			}

			.tips {
				width: 100%;
				text-align: center;
				font-size: 16px;
				padding: 40rpx 0;
				border-bottom: 2rpx solid #E9E9E9;
			}

			.content {
				width: calc(100% - 90rpx);
				height: calc(100% - 220rpx);
				font-size: 28rpx;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				text-align: center;
				color: #666666;
				line-height: 50rpx;
				padding-top: 36rpx;
				overflow-y: scroll;
			}

			.comfirm {
				width: 100%;
				height: 88rpx;
				border-top: .5px solid #f5f5f5;
				text-align: center;
				line-height: 88rpx;
				font-size: 15px;
				font-family: PingFangSC, PingFangSC-Regular;
			}
			.interaction-two{
				display: flex;
				width: 100%;
				border-top: .5px solid #f5f5f5;
				&-cancel{
					flex: 1;
					text-align: center;
					line-height: 88rpx;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
					border-right: .5px solid #f5f5f5;
				}
				&-comfirm{
					flex: 1;
					text-align: center;
					line-height: 88rpx;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
				}
			}
			.interaction-one{
				&-comfirm{
					flex: 1;
					text-align: center;
					line-height: 88rpx;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
				}
			}
		}
	}
</style> 