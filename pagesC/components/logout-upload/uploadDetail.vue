<!-- OCR识别档案上传 -->
<template>
	<view class="ocr">
		<!-- 		<view class="weui-cells__title">
			请拍照上传本人身份证
		</view> -->
		<!-- 		<t-title :title="title" setPadding="30">
			<template>
				<view class="single-wrapper">
					<view class="single">
						授权书填写样例
					</view>
					<view class="single">
						下载模板
					</view>
				</view>
			</template>
		</t-title> -->
		<view class="title">
			档案照片
		</view>
		<view class="orc-form">
			<view class="upload" v-for="(item, index) in fileList" :key="index">
				<template v-if="item.isUpload">
					<view class="upload-box" @tap="ViewImage(item)" v-if="item.file_serial">
						<image :src="item.url" class="upload-box__img" mode="aspectFilt"></image>
						<view v-if="item.file_serial && isUpdate" class="cu-tag bg-brown upload-box__close"
							@tap.stop="DelImg(item)">
							<text class="cuIcon-close close"></text>
						</view>
					</view>
					<view v-if="!item.file_serial && isUpdate" class="upload-box" @tap="ChooseImage(item)">
						<image :src="item.url" class="upload-box__img"></image>
					</view>
					<view class="upload__tip">{{ item.label }}</view>
				</template>
			</view>
		</view>
		<neil-modal @confirm="methodEnsure" :show="alltoastData.showFlag" :align="alltoastData.allign"
			:title="alltoastData.title" :content="alltoastData.msg" :confirmText="alltoastData.btnTextEnsure"
			:show-cancel="alltoastData.btnTextCloseFlag">
		</neil-modal>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	// import tTitle from '@/pagesA/components/new-title/new-title.vue'
	import {
		setCurrentCar,
		getLoginUserInfo
	} from '@/common/storageUtil.js'

	import neilModal from '@/components/neil-modal/neil-modal.vue';
	import tLoading from '@/components/common/t-loading.vue';

	export default {
		components: {
			cpimg,
			neilModal,
			tLoading,
			// tTitle
		},
		props: {
			sourceType: {
				type: Number,
				default: 0
			},
			showOne: {
				type: Boolean,
				default: false
			},
			title: {
				type: String,
				default: '请拍照上传办理人证件'
			},
			userInfo: {
				type: Object,
				default: {}
			},
			isUpdate: {
				type: Boolean,
				default: false
			},
			uploadList: {
				type: Array,
				default () {
					return []
				}
			}
		},
		data() {
			return {
				showLoad: false,
				isLoading: false,
				ownFlag: 0,
				sides: '',
				id: null,
				// customer_id: '',
				fileList: [{
						photo_code: "22",
						ocr_type: 2,
						label: "身份证人像面",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png",
						file_serial: '',
						origin_img: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png',
						photo_type: 'positiveImage',
						isUpload: true,
					},
					{
						photo_code: "23",
						ocr_type: 3,
						label: "身份证国徽面",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png",
						origin_img: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png",
						file_serial: '',
						photo_type: 'negativeImage',
						isUpload: true,
					},
					{
						photo_code: "26",
						ocr_type: 2,
						label: "身份证人像面",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png",
						file_serial: '',
						origin_img: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png',
						photo_type: 'positiveImage',
						isUpload: false,
					},
					{
						photo_code: "27",
						ocr_type: 3,
						label: "身份证国徽面",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png",
						origin_img: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_back.png",
						file_serial: '',
						photo_type: 'negativeImage',
						isUpload: false,
					},
					{
						photo_code: "25",
						md5Code: '',
						label: "车辆授权书",
						url: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unload_bg_img.png",
						origin_img: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unload_bg_img.png",
						photo_type: 'authLetterImg',
						isUpload: false,
					},
				]
			}

		},
		watch: {
			uploadList(imgArr) {
				console.log('imgArr===>>>', imgArr)
				if (imgArr.length > 0) {
					imgArr.forEach(item => {
						this.fileList.forEach(fileItem => {
							//有id说明是后端返回才修改
							if (item.uploadType == fileItem.photo_code && item.createdTime) {
								fileItem.id = item.id
								fileItem.url = item.uploadPath
								fileItem.file_serial = item.uploadType
								console.log('fileItem', fileItem)
							}

							if (this.userInfo.accountType == '1') {
								if (fileItem.photo_code == '25' || fileItem.photo_code == '26' || fileItem
									.photo_code == '27') {
									//单位用户显示授权书
									fileItem.isUpload = true
								}
								if (fileItem.photo_code == '22' || fileItem.photo_code == '23') {
									fileItem.isUpload = false
								}
							}
						})
					})
				}
			},
			// 'userInfo.accountType'(val) {
			// 	if (val == '1') {
			// 		this.fileList[0].photo_code = '26'
			// 		this.fileList[1].photo_code = '27'

			// 		console.log('this.fileList', this.fileList)
			// 		if (this.fileList.length > 0) {
			// 			this.fileList.forEach(item => {
			// 				this.fileList.forEach(fileItem => {
			// 					if (item.uploadType == fileItem.photo_code) {
			// 						console.log('进来过吗')
			// 						fileItem.url = item.uploadPath
			// 						fileItem.file_serial = item.uploadType
			// 					}

			// 					if (fileItem.photo_code == '25' && this.userInfo.accountType == '1') {
			// 						//单位用户显示授权书
			// 						fileItem.isUpload = true
			// 					}
			// 				})
			// 			})
			// 		}
			// 	}
			// }
		},
		created() {
			if (this.fileList.length > 0) {
				this.uploadList.forEach(item => {
					this.fileList.forEach(fileItem => {
						//有id说明是后端返回才修改
						if (item.uploadType == fileItem.photo_code && item.createdTime) {
							fileItem.id = item.id
							fileItem.url = item.uploadPath
							fileItem.file_serial = item.uploadType
						}

						if (this.userInfo.accountType == '1') {
							if (fileItem.photo_code == '25' || fileItem.photo_code == '26' || fileItem
								.photo_code == '27') {
								//单位用户显示授权书
								fileItem.isUpload = true
							}
							if (fileItem.photo_code == '22' || fileItem.photo_code == '23') {
								fileItem.isUpload = false
							}
						}
					})
				})
			}
		},
		methods: {
			ChooseImage(data) {
				this.sides = data.photo_code;
				this.id = data.id
				this.$refs.cpimg._changImg(this.sourceType);
			},
			////图片压缩成功
			cpimgOk(file) {
				this.isLoading = true;
				if (this.sides == '22' || this.sides == '23' || this.sides == '26' || this.sides == '27') {
					//身份信息去ocr
					this.sendOCR(file);
				}
				if (this.sides == '25') {
					let base64Img = file.toString()
					//车辆授权书直接上传
					this.sendUploadFile(base64Img, {
						side: this.sides
					})
				}

			},
			// ocr识别
			sendOCR(file) {
				let base64Img = file.toString()
				let ocr_type = '';
				let current = {};
				var imgStr = base64Img.split(';')[1].split(",")[1] + '';
				for (let i = 0; i < this.fileList.length; i++) {
					if (this.fileList[i].photo_code == this.sides) {
						current = this.fileList[i];
					}
				}
				// this.$set(current, 'base64img', base64Img)

				let biz_content = {
					ocr_type: current.ocr_type,
					file_name: 'file_name',
				}

				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						let result = res.data;
						let encryptedData = result.encryptedData;
						console.log('encryptedData', encryptedData)

						this.isLoading = false;
						console.log('userInfo', this.userInfo)
						if (this.userInfo.accountType == '0' && this.sides == '22') {
							//个人用户校验身份信息
							if (encryptedData['realName'] != this.userInfo.custName || encryptedData['cardNum'] !=
								this.userInfo.custIdNo) {
								uni.showModal({
									title: "提示",
									content: '上传的身份证信息与所注销的ETC用户信息不符，请核对后重新上传！',
									showCancel: false,
								});
								return
							}
						}

						this.sendUploadFile(base64Img)
						// this.$emit("ocr-change", result);
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			sendUploadFile(fileParams) {
				// let current = fileParams ? JSON.parse(JSON.stringify(fileParams)) : {}
				let biz_content = {
					customer_id: this.userInfo.custMastId,
					photo_code: this.sides,
					scene: 6, // 销卡上传
					vehicle_code: this.userInfo.vehicleNo,
					vehicle_color: this.userInfo.vehicleColor,
					other_code: this.userInfo.cardNo
				};
				// console.log('side==>>>>>>>>', result.side)
				let params = {
					file_content: fileParams,
					method_code: "2", // 档案上传
					biz_content: JSON.stringify(biz_content),
				};
				// this.isLoading = true;
				this.$request.post(this.$interfaces.uploadFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						console.log('====>>>', res)
						//当前页面图片显示
						for (let obj of this.fileList) {
							// console.log('obj进来===>>>>>>>>>>>', obj['photo_code'])
							if (obj['photo_code'] == res.data.photo_code) {
								// console.log('side进来===>>>>>>>>>>>')
								obj.url = res.data.file_url
								obj.file_serial = res.data.file_serial
							}
						}

						console.log('图片显示==>>>>>>>>>>>', this.fileList)
						let imgList = {
							id: this.id,
							uploadType: res.data.photo_code,
							uploadPath: res.data.code,
							base64Img: fileParams
						}
						this.$emit('on-change', imgList)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e);
			},
			//图片预览
			ViewImage(path) {
				if (!path.file_serial) return
				let newArr = [];
				newArr.push(path.url);
				uni.previewImage({
					urls: newArr
				});
			},
			// 删除图片
			DelImg(data) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							// 	let current = {};
							// 	for (let i = 0; i < this.fileList.length; i++) {
							// 		if (this.fileList[i].photo_code == data.photo_code) {
							// 			this.fileList[i].base64img = '';
							// 			current = this.fileList[i];
							// 		}
							// 	}
							// 	this.$set(current, 'file_serial', '');
							console.log('data', data)
							data.url = data.origin_img;
							data.file_serial = '';
							console.log('data', data)
							this.$emit('delImg', data.photo_code)
						}
					}
				});
			},
			delImgByRef(side) {
				console.log('父调用子', side)
				for (let obj of this.fileList) {
					// console.log('obj进来===>>>>>>>>>>>', obj['photo_code'])
					if (obj['photo_code'] == side) {
						// console.log('side进来===>>>>>>>>>>>')
						obj.url = obj.origin_img
						obj.file_serial = ""
					}
				}

			}
		}
	}
</script>

<style scoped lang="scss">
	$uploadWidth: 223rpx;
	$uploadHeight: 127rpx;

	.ocr {
		display: flex;
		background-color: #FFFFFF;
		border-radius: 12rpx;
	}

	.title {
		flex: 0 0 190rpx;
		width: 190rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
		line-height: 26rpx;
	}

	.customer-info {
		padding: 0 30rpx 20rpx 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
		line-height: 40rpx;

		&>text {
			color: #F65B5B;
		}
	}

	.title-container {
		padding: 30rpx;
	}

	.orc-form {
		padding: 0 20rpx 30rpx 20rpx;
		display: flex;
		flex-wrap: wrap;
		// justify-content: space-between;
		background-color: #FFFFFF;
		border-bottom-left-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}

	.upload {
		width: $uploadWidth;

		&:nth-child(2n-1) {
			margin-right: 10rpx;
		}

	}

	.upload .upload__tip {
		font-size: 28rpx;
		text-align: center;
		width: 100%;
		color: #333333;
		font-weight: 400;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.upload .upload-box {
		width: $uploadWidth;
		height: $uploadHeight;
		position: relative;
	}

	.upload .upload-box .upload-box__img {
		width: $uploadWidth;
		height: $uploadHeight;
	}

	.upload .upload-box .upload-box__close {
		position: absolute;
		right: 0;
		top: 0;
		padding: 0 10rpx;
		font-size: 36rpx;
	}

	.upload .upload-box .upload-box__close .close {
		font-size: 36rpx;
	}
</style>