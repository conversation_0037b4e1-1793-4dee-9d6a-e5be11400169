<!-- OCR识别档案上传 -->
<template>
	<view class="ocr">
		<neil-modal @confirm="methodEnsure" :show="alltoastData.showFlag" :align="alltoastData.allign"
			:title="alltoastData.title" :content="alltoastData.msg" :confirmText="alltoastData.btnTextEnsure"
			:show-cancel="alltoastData.btnTextCloseFlag">
		</neil-modal>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import {
		setCurrentCar,
		getLoginUserInfo
	} from '@/common/storageUtil.js'

	import neilModal from '@/components/neil-modal/neil-modal.vue';
	import tLoading from '@/components/common/t-loading.vue';

	export default {
		components: {
			cpimg,
			neilModal,
			tLoading,
		},
		props: {
			sourceType: {
				type: Number,
				default: 0
			},
			userInfo: {
				type: Object,
				default: {}
			},
		},
		data() {
			return {
				showLoad: false,
				isLoading: false,
				ownFlag: 0,
				sides: '',
			}

		},
		created() {},
		methods: {
			ChooseImage() {
				this.$refs.cpimg._changImg(this.sourceType);
			},
			////图片压缩成功
			cpimgOk(file) {
				this.isLoading = true;
				//身份信息去ocr
				this.sendOCR(file);

			},
			// ocr识别
			sendOCR(file) {

				let biz_content = {
					ocr_type: 1,
					file_name: 'file_name',
				}

				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.isLoading = false;
						let encryptedData = res.data.encryptedData;
						console.log('encryptedData', encryptedData)
						// if (encryptedData.bankNo) {
						// 	let trimBankNo = encryptedData.bankNo.replace(/\s*/g, "")
						// 	this.formData.bankNo = trimBankNo
						// }
						// if (encryptedData.bankName) {
						// 	this.otherBankName = encryptedData.bankName
						// }
						this.sendUploadFile(file.toString(), encryptedData)
					} else {
						this.isLoading = false;
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			sendUploadFile(fileParams, encryptedData) {
				// let current = fileParams ? JSON.parse(JSON.stringify(fileParams)) : {}
				let biz_content = {
					customer_id: this.userInfo.custMastId,
					photo_code: '33',
					scene: 6, // 销卡上传
					vehicle_code: this.userInfo.vehicleNo,
					vehicle_color: this.userInfo.vehicleColor,
					other_code: this.userInfo.cardNo
				};
				console.log('biz_content==>>>>>>>>', biz_content)
				let params = {
					file_content: fileParams,
					method_code: "2", // 档案上传
					biz_content: JSON.stringify(biz_content),
				};
				// this.isLoading = true;
				this.$request.post(this.$interfaces.uploadFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						//当前页面图片显示
						console.log('银行卡照片上传====>>>', res)
						let imgList = {
							uploadType: res.data.photo_code,
							uploadPath: res.data.code,
						}
						this.$emit('on-change', imgList, encryptedData)

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e);
			},
			//图片预览
			ViewImage(path) {
				if (!path.md5Code) return
				let newArr = [];
				newArr.push(path.url);
				uni.previewImage({
					urls: newArr
				});
			},
			// 删除图片
			DelImg(data) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							// 	let current = {};
							// 	for (let i = 0; i < this.fileList.length; i++) {
							// 		if (this.fileList[i].photo_code == data.photo_code) {
							// 			this.fileList[i].base64img = '';
							// 			current = this.fileList[i];
							// 		}
							// 	}
							// 	this.$set(current, 'file_serial', '');
							console.log('data', data)
							data.url = data.origin_img;
							data.md5Code = '';
							console.log('data', data)
							this.$emit('delImg', data.photo_type)
						}
					}
				});
			},
			delImgByRef(side) {
				console.log('父调用子', side)
				for (let obj of this.fileList) {
					// console.log('obj进来===>>>>>>>>>>>', obj['photo_code'])
					if (obj['photo_code'] == side) {
						// console.log('side进来===>>>>>>>>>>>')
						obj.url = obj.origin_img
						obj.md5Code = ""
					}
				}

			}
		}
	}
</script>

<style scoped lang="scss">
	$uploadWidth: 321rpx;
	$uploadHeight: 183rpx;

	.ocr {
		background-color: #FFFFFF;
		border-radius: 12rpx;
	}

	.customer-info {
		padding: 0 30rpx 20rpx 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #323435;
		line-height: 40rpx;

		&>text {
			color: #F65B5B;
		}
	}

	.title-container {
		padding: 30rpx;
	}

	.orc-form {
		padding: 0 30rpx 30rpx 30rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		background-color: #FFFFFF;
		border-bottom-left-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}

	.upload {
		width: $uploadWidth;
	}

	.upload .upload__tip {
		font-size: 28rpx;
		text-align: center;
		width: 100%;
		color: #333333;
		font-weight: 400;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.upload .upload-box {
		width: $uploadWidth;
		height: $uploadHeight;
		position: relative;
	}

	.upload .upload-box .upload-box__img {
		width: $uploadWidth;
		height: $uploadHeight;
	}

	.upload .upload-box .upload-box__close {
		position: absolute;
		right: 0;
		top: 0;
		padding: 0 10rpx;
		font-size: 36rpx;
	}

	.upload .upload-box .upload-box__close .close {
		font-size: 36rpx;
	}
</style>