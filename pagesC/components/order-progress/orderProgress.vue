<template>
	<view class="order-progress">
		<tTitle title="办理进度" setPadding="30" rightText="查看详情" @click="onclick">
		</tTitle>
		<view class="status-wrapper">
			<view class="status-label">
				订单状态：
			</view>
			<view class="status-value" :class="statusInfoList[result.orderStatus].status" v-if="result.orderStatus">
				{{result.orderStatusStr}}
			</view>
			<view class="status-value" :class="statusInfoList[result.status].status" v-if="result.status">
				{{result.statusName}}
			</view>
		</view>
		<view class="progress-tip" v-if="result.orderStatus == '103'">
			{{result.auditRemarks || ''}}
		</view>
		<view class="progress-tip" v-else-if="result.status=='203'">
			{{result.remarks || ''}}
		</view>
		<view class="progress-tip" v-else-if="result.status=='206'">
			{{'您当前需更换的设备类型为'+result.deviceTypeName+'，费用为'+moneyFilter(result.payAmount || 0)+'，请尽快支付'}}
		</view>
		<view class="progress-tip" v-else>
			{{statusText}}
		</view>
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import tTitle from '@/pagesC/components/new-title/new-title.vue';
	import {
		statusInfoList
	} from '@/pagesC/common/optionsData.js'
	export default {
		props: {
			result: {
				type: Object
			},

		},
		components: {
			tLoading,
			tTitle
		},
		data() {
			return {
				statusInfoList
			}
		},
		computed: {
			statusText() {
				if (this.result.orderStatus) {
					return this.statusInfoList[this.result.orderStatus].statusInfo
				}
				if (this.result.status) {
					return this.statusInfoList[this.result.status].statusInfo
				}
			}
		},
		created() {
			// console.log('codeName', this.codeName)
		},
		methods: {
			onclick() {
				this.$emit('click')
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-progress {
		font-family: PingFangSC-Regular, PingFang SC;
		background-color: #ffffff;
		border-radius: 12rpx;
	}

	.status-wrapper {
		padding: 0 30rpx;
		display: flex;
		align-items: center;
	}

	.status-label {
		flex: 0 0 150rpx;
		width: 150rpx;
	}

	.status-value {
		flex: 1;
		height: 68rpx;
		line-height: 68rpx;
		padding-left: 30rpx;
		font-size: 26rpx;
		font-weight: 400;
		border-radius: 8rpx;
	}



	.progress-tip {
		padding: 30rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #999999;
	}
</style>
