<template>
	<view class="status-dialog" v-if="show">
		<view class="status-dialog__mask"></view>
		<view class="status-dialog__content">
			<view class="tips">办理须知</view>
			<view class="content" v-html="content" :style="[contentStyle]"></view>
			<view class="radio" @click="check=true">
				<view class="round" :class="{'check-round':check}"></view>
				<view class="value" :class="{'jitter-div':isJitter,'':!isJitter}">我已阅读，确认无问题</view>
			</view>
			<view class="interaction-two" v-if="opBusinessType == '1' || opBusinessType == '3'">
				<view class="interaction-two-cancel" @click="clickCancel">前往设备检测
				</view>
				<view class="interaction-two-comfirm" @click="clickComfirm">
					已检测直接办理
				</view>
			</view>
			<view class="interaction-two" v-if="opBusinessType == '2'">
				<view class="interaction-one-comfirm" @click="clickComfirm">
					前往办理
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// import parse from 'mini-html-parser2';
	export default {
		name: 'status-dialog',
		props: {
			content: { // 提示的文案内容
				type: String,
				default: ""
			},
			show: { // 弹窗的显示
				type: [Boolean, String],
				default: false
			},
			contentStyle: {
				type: Object,
				default () {
					return {};
				}
			},
			opBusinessType: {
				type: [Number, String],
				default: '2'
			}
		},
		data() {
			return {
				check: false,
				isJitter: false
			}
		},

		methods: {
			clickComfirm() {
				let that = this
				if (!that.check) {
					that.isJitter = true

					setTimeout(() => {
						that.isJitter = false
					}, 1000)
					console.log(that.isJitter)
					return
				}
				that.check = false
				that.$emit('comfirm')
			},
			clickCancel() {
				this.check = false
				this.$emit('cancel')
			},
		}
	}
</script>

<style lang="scss">
	$bg-color-mask: rgba(0, 0, 0, 0.5); //遮罩颜色
	$bg-color-hover: #f1f1f1; //点击状态颜色

	.status-dialog {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 99999;

		&__mask {
			display: block;
			position: fixed;
			z-index: 1998;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: $bg-color-mask;
		}

		&__content {
			position: relative;
			z-index: 2000;
			top: calc(50% - 500rpx);
			left: 50%;
			margin-left: -310rpx;
			width: 620rpx;
			height: 1038rpx;
			background: #ffffff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.status-image {
				margin-top: 44rpx;
			}

			.tips {
				width: 100%;
				text-align: center;
				font-size: 16px;
				padding: 40rpx 0;
				border-bottom: 2rpx solid #E9E9E9;
			}

			.content {
				width: calc(100% - 90rpx);
				height: calc(100% - 330rpx);
				font-size: 28rpx;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				text-align: center;
				color: #666666;
				line-height: 50rpx;
				margin-top: 36rpx;
				overflow-y: scroll;
			}

			.radio {
				display: flex;
				align-items: center;

				.round {
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
					border: 1rpx solid #A8A8A8;
				}

				.check-round {
					border: 1rpx solid #0066E9;
					background-color: #0066E9;
					position: relative;
				}

				.jitter-div {
					animation: 1s jitter;
				}

				@keyframes jitter {
					0% {
						transform: scale(1.4, 1.4);
					}

					10% {
						transform: scale(1, 1);
					}

					25% {
						transform: scale(1.2, 1.2);
					}

					50% {
						transform: scale(1, 1);
					}

					70% {
						transform: scale(1.2, 1.2);
					}

					100% {
						transform: scale(1, 1);
					}
				}

				.check-round::before {
					font-family: "cuIcon";
					content: "\e645";
					position: absolute;
					color: #fff;
					top: 0;
					right: 0;
					font-size: 24rpx;
				}

				.value {
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #666666;
					line-height: 40rpx;
					margin-left: 18rpx;
				}
			}

			.comfirm {
				width: 100%;
				height: 88rpx;
				border-top: .5px solid #f5f5f5;
				text-align: center;
				line-height: 88rpx;
				font-size: 15px;
				font-family: PingFangSC, PingFangSC-Regular;
				font-weight: 400;
				position: absolute;
				bottom: 0;
			}

			.interaction-two {
				border-top: .5px solid #f5f5f5;
				width: 100%;
				height: 88rpx;
				position: absolute;
				bottom: 0;
				display: flex;

				.interaction-two-cancel {
					width: 50%;
					height: 88rpx;
					// border-right: .5px solid #F5F5F5;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					line-height: 88rpx;
				}

				.interaction-two-comfirm {
					width: 50%;
					height: 88rpx;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #fff;
					line-height: 88rpx;
					background-color: #0066E9;
					border-bottom-right-radius: 24rpx;
				}

				.interaction-one-comfirm {
					width: 100%;
					height: 88rpx;
					font-size: 15px;
					font-family: PingFangSC, PingFangSC-Regular;
					font-weight: 400;
					text-align: center;
					color: #ffffff;
					background-color: #0066E9;
					line-height: 88rpx;
					border-bottom-right-radius: 24rpx;
					border-bottom-left-radius: 24rpx;
				}
			}
		}
	}
</style>