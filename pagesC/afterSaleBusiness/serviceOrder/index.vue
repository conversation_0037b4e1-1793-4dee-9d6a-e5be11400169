<template>
	<view class="order-detail_list">
		<view>
			<view class="line-block"></view>
			<view class="weui-form">
				<view class="weui-cells">
					<view class="vux-x-input weui-cell weui-cell_picker" style="width:100%;height: 96rpx;">
						<view class="weui-cell__hd">
							<view class="weui-label">订单类型</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary" style="padding: 30rpx 30rpx 30rpx 0;"
							@click="showOrderType">
							<view class="weui-input" v-if="searchObj.orderTypeName" style="color: #333333;">
								{{searchObj.orderTypeName}}
							</view>
							<view class="weui-input" v-else>请选择</view>
						</view>
						<image src="../../static/down.png" mode="" style="width: 40rpx;height: 40rpx;"></image>
					</view>

					<view class="vux-x-input weui-cell weui-cell_picker">
						<view class="weui-cell__hd">
							<view class="weui-label">提交时间</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="picker">
								<view class="pick-date pick-date-one">
									<picker mode="date" @change="startDateChange" :value="searchObj.applyTimeStart"
										style="width: 100%;">
										<u-cell-item title=" " :arrow="false" icon="date-fill">
											<view class="monthData">{{searchObj.applyTimeStart}}</view>
										</u-cell-item>
									</picker>
								</view>
								<view style="margin: 0 30rpx;">至</view>
								<view class="pick-date pick-date-two">
									<picker mode="date" @change="endDateChange" :value="searchObj.applyTimeEnd"
										style="width: 100%;" :end="endDate">
										<u-cell-item title=" " :arrow="false" icon="date-fill">
											<view class="monthData">{{searchObj.applyTimeEnd}}</view>
										</u-cell-item>
									</picker>
								</view>
							</view>
						</view>
					</view>


					<view class="vux-x-input weui-cell weui-cell_picker" style="width:100%;height: 96rpx;">
						<view class="weui-cell__hd">
							<view class="weui-label">车牌号码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary" style="padding: 30rpx 30rpx 30rpx 0;"
							@click="showPlate">
							<view class="weui-input" v-if="searchObj.carNo" style="color: #333333;">
								{{searchObj.carNo}}
							</view>
							<view class="weui-input" v-else>请选择</view>
						</view>
						<icon @click="clearCarNo" type="cancel" size="26" color="#cacaca" v-if="searchObj.carNo" />
					</view>

				</view>
			</view>
			<view class="search-btn">
				<button class="weui-btn weui-btn_primary " @click="onSearchHandle()">
					搜索
				</button>
			</view>
		</view>
		<scroll-view :style="{height:height}" :scroll-top="scrollTop" scroll-y="true" class="scroll-Y"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">

			<view class="service-order-list">
				<view class="weui-form-preview" v-for="(item,index) in currentApplyList" :key="index"
					v-if="currentApplyList.length>0">
					<view class="weui-form-preview__hd weui-title__decoration">

						<view class="vehicle g-flex g-flex-align-end">
							<view class="title">{{item.carNo}}【{{item.carColorStr}}】</view>
							<view class="desc">
								【{{item.vehicleTypeStr}}】
							</view>
						</view>
						<view class="weui-tag status-value" :class="statusInfoList[item.orderStatus].status">
							{{item.orderStatusStr}}
						</view>
					</view>
					<view class="weui-form-preview__bd">
						<view class="weui-form-preview__item">
							<view class="weui-form-preview__label">订单类型：</view>
							<view class="weui-form-preview__value">{{item.orderTypeStr}}</view>
						</view>
						<view class="weui-form-preview__item">
							<view class="weui-form-preview__label">订单编号：</view>
							<view class="weui-form-preview__value">{{item.orderNo}}</view>
						</view>
						<view class="weui-form-preview__item">
							<view class="weui-form-preview__label">申领日期：</view>
							<view class="weui-form-preview__value">{{item.applyTime}}</view>
						</view>
					</view>

					<view class="weui-form-preview__ft" v-if="item.orderType == '1'">
						<view class="weui-form-preview__btn g-flex g-flex-justify">
							<view class="g-flex g-flex-start">
								<view class="btn-item" @click="goDetail(item)"><button
										class="weui-btn_mini  weui-btn_plain-default ">详情</button>
								</view>
							</view>
							<view class="g-flex" v-if="item.orderStatus==102 || item.orderStatus==103">
								<view class="btn-item" @click="cancelOrder(item)"><button
										class="weui-btn_mini weui-btn_plain-default">取消订单</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==102" @click="activate(item)"><button
										class="weui-btn_mini weui-btn_primary">设备激活</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==103" @click="edit(item)"><button
										class="weui-btn_mini weui-btn_primary">修改信息</button>
								</view>
							</view>
						</view>
					</view>
					<!-- 补办 -->
					<view class="weui-form-preview__ft" v-if="item.orderType == '2'">
						<view class="weui-form-preview__btn g-flex g-flex-justify">
							<view class="g-flex g-flex-start">
								<view class="btn-item" @click="goDetail(item)"><button
										class="weui-btn_mini  weui-btn_plain-default ">详情</button>
								</view>
							</view>
							<view class="g-flex">
								<view class="btn-item" @click="cancelOrder(item)"
									v-if="item.orderStatus==301 || item.orderStatus==302"><button
										class="weui-btn_mini weui-btn_plain-default">取消订单</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==301" @click="pay(item)"><button
										class="weui-btn_mini weui-btn_primary">确认支付</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==303" @click="edit(item)"><button
										class="weui-btn_mini weui-btn_primary">修改信息</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==304" @click="edit(item)"><button
										class="weui-btn_mini weui-btn_primary">确认收货</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==305" @click="activate(item)"><button
										class="weui-btn_mini weui-btn_primary">前往激活</button>
								</view>
							</view>
						</view>
					</view>
					<!-- 更换 -->
					<view class="weui-form-preview__ft" v-if="item.orderType == '3'">
						<view class="weui-form-preview__btn g-flex g-flex-justify">
							<view class="g-flex g-flex-start">
								<view class="btn-item" @click="goDetail(item)"><button
										class="weui-btn_mini  weui-btn_plain-default ">详情</button>
								</view>
							</view>
							<view class="g-flex">
								<view class="btn-item" @click="cancelOrder(item)"
									v-if="item.orderStatus==201 || item.orderStatus==202 || item.orderStatus==203">
									<button class="weui-btn_mini weui-btn_plain-default">取消订单</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==201 || item.orderStatus==203" @click="">
									<button class="weui-btn_mini weui-btn_primary">修改提交资料</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==202" @click=""><button
										class="weui-btn_mini weui-btn_primary">寄回设备</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==206" @click="pay(item)"><button
										class="weui-btn_mini weui-btn_primary">去支付</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==210" @click="activate(item)"><button
										class="weui-btn_mini weui-btn_primary">前往激活</button>
								</view>
								<view class="btn-item" v-if="item.orderStatus==209" @click="edit(item)"><button
										class="weui-btn_mini weui-btn_primary">确认收货</button>
								</view>
							</view>
						</view>
					</view>

				</view>
			</view>
			<view v-if="currentApplyList.length==0" class="no-data">
				<image src="../../static/no_data.png" mode="" class="no-data-img"></image>
				<view class="no-data-title">暂无交易记录</view>
			</view>
		</scroll-view>
		<plate-input v-if="plateShow" :plate="searchObj.carNo" @export="setPlate" @close="plateShow=false" />
		<u-select v-model="show" :list="orderTypeList" @confirm="confirm">
		</u-select>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import float from '@/common/method/float.js'
	import plateInput from '@/components/uni-plate-input/uni-plate-input.vue';
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
	} from "@/common/storageUtil.js";
	import {
		getVehicleColor
	} from '@/common/method/filter.js'
	import {
		afterSaleStatusList
	} from '@/pagesA/common/optionsData.js'
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')

	function getDate(type) {
		const date = new Date();
		console.log(date)
		let year = date.getFullYear();
		let month = date.getMonth() + 1;
		let day = date.getDate();

		if (type === 'start') {
			year = year - 1;
		} else if (type === 'end') {
			year = year;
		}
		month = month > 9 ? month : '0' + month;
		day = day > 9 ? day : '0' + day;

		return `${year}-${month}-${day}`;
	}
	export default {
		components: {
			plateInput
		},
		options: {
			styleIsolation: 'shared'
		},
		data() {
			return {
				statusInfoList: afterSaleStatusList,
				showPage: false,
				isLoading: false,
				plateShow: false,
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				// cardAmount: {},
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				orderListData: [],
				sumMoney: 0,
				index: 0,
				searchObj: {
					applyTimeStart: getDate('start'),
					applyTimeEnd: getDate('end'),
					carNo: "",
					orderType: '',
					orderTypeName: '全部',
					page: 1,
					pageSize: 10,
				},
				show: false,
				orderTypeList: [{
						label: '全部',
						value: '',
					},
					{
						label: '设备自助激活',
						value: '1',
					},
					{
						label: '设备补办',
						value: '2',
					},
					{
						label: '设备更换',
						value: '3',
					}, {
						label: '设备注销/注销申请',
						value: '4',
					}
				],
				height: 'calc(100% - 400rpx)',
				endDate: getDate('end'),
				currentApplyList: [],
				flag: false,
			};
		},
		computed: {

		},
		watch: {

		},
		created() {
			this.getApplyList()
		},
		onShow() {
			//this.getApplyList()
		},

		methods: {
			getVehicleColor,
			onSearchHandle() {
				console.log(this.searchObj)
				this.searchObj.page = 1
				this.flag = false
				this.getApplyList()
			},
			getApplyList() {
				this.currentApplyList = this.searchObj.page == 1 ? [] : this.currentApplyList;
				let params = {
					applyTimeStart: this.searchObj.applyTimeStart,
					applyTimeEnd: this.searchObj.applyTimeEnd,
					carNo: this.searchObj.carNo,
					orderType: this.searchObj.orderType,
					page: this.searchObj.page,
					pageSize: this.searchObj.pageSize,
				}

				this.isLoading = true
				console.log(params)
				this.$request.post(this.$interfaces.serviceOrderQuery, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					this.showPage = true
					if (res.code == 200) {
						this.orderListData = res.data.data ? res.data.data : []
						this.currentApplyList = this.currentApplyList.concat(this.orderListData)
						if (this.currentApplyList.length == res.data.total) {
							this.flag = true
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				if (this.flag) return;
				let self = this;
				setTimeout(function() {
					self.searchObj.page = self.searchObj.page + 1;
					self.getApplyList();
				}, 500)
			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop;
			},
			goDetail(item) {
				if(item.orderStatus == '101' || item.orderStatus == '102' || item.orderStatus == '103'){
					uni.navigateTo({
						url: '/pagesC/afterSaleBusiness/orderDetails/index?id=' + item.id
					})
					return
				}
				uni.navigateTo({
					url: "/pagesC/afterSaleBusiness/orderDetails/viewDetail?id=" + item.id
				})
			},
			edit(item) {
				uni.navigateTo({
					url: '/pagesC/afterSaleBusiness/orderDetails/index?id=' + item.id
				})
			},
			activate(item) {
				let vehicleInfo = {}
				vehicleInfo.customerId = getCurrUserInfo().customer_id;
				vehicleInfo.vehicleCode = item.carNo;
				vehicleInfo.vehicleColor = item.carColor;
				vehicleInfo.businessSource = 2; //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
				vehicleInfo.orderId = item.id
				vehicleInfo.cardNo = item.cardNo
				vehicleInfo.obuNo = item.obuNo
				console.log(item)
				this.$store.dispatch(
					'setIssueVehicleInfo',
					vehicleInfo
				)
				uni.navigateTo({
					url: '/pagesA/newBusiness/issue/issue-install?activationType=2'
				})
			},
			cancelOrder(item) {
				let params = {
					id: item.id,
				}
				this.isLoading = true
				console.log(params)
				this.$request.post(this.$interfaces.activateApplyCancel, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						uni.navigateTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=7&id=" +
								item.id
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			showPlate() {
				this.plateShow = true
			},

			setPlate(plate) {
				if (plate.length >= 7) this.searchObj.carNo = plate
				this.plateShow = false
			},

			clearTime() {
				this.searchObj.strartTime = ''
			},
			clearCarNo() {
				this.searchObj.carNo = ''
			},

			startDateChange(e) {
				this.searchObj.applyTimeStart = e.detail.value
			},
			endDateChange(e) {
				this.searchObj.applyTimeEnd = e.detail.value
			},
			showOrderType() {
				this.show = true
			},
			confirm(options) {
				console.log(options)
				this.searchObj.orderTypeName = options[0].label
				this.searchObj.orderType = options[0].value
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.order-detail_list {
		width: 100%;
		height: calc(100% - 88rpx);

		.line-block {
			height: 20rpx;
			width: 100%;
			background-color: #F6F6F6;
		}

		.weui-form {
			margin-top: 0px;
		}

		.picker {
			width: 100%;
			display: flex;
			height: 100%;
			align-items: center;
		}

		/deep/.u-border-bottom::after {
			border-bottom: none;
		}

		.pick-date {
			width: 192rpx;
			display: flex;
			align-items: center;

			/deep/.u-cell {
				position: relative;
			}

			/deep/.u-cell__value {
				font-size: 30rpx !important;
			}

			/deep/.u-cell__left-icon-wrap {
				position: absolute;
				right: 0;
				margin-right: 0px !important;
			}

			/deep/.u-icon__icon {
				font-size: 25rpx !important;
				color: #999999;
			}
		}

		.pick-date-two {
			// flex: 1;
		}

		/deep/.u-cell {
			padding: 0 0;
			line-height: 80rpx;
		}

		/deep/.u-cell__value {
			color: #333;
			text-align: left;
			font-size: 30rpx;
		}

		.no-data {
			width: calc(100% - 40rpx);
			text-align: center;
			background: #fff;
			margin-left: 20rpx;
			border-radius: 11rpx;
			height: 430rpx;

			.no-data-img {
				width: 248rpx;
				height: 269rpx;
				background-size: 100%;
				margin-top: 62rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}
	}

	.activation-page {
		position: relative;
	}

	.weui-cells {
		padding-top: 0;
	}

	.weui-cell {
		padding: 0rpx 30rpx;
	}

	.weui-cells::before {
		border: 0;
	}

	.weui-label {
		width: 180rpx;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 96rpx;
	}

	.weui-cell_picker .weui-picker-value {
		text-align: left;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 96rpx;
	}

	.weui-input,
	.weui-cell__value {
		text-align: left;
		height: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #CACACA;
		line-height: 30rpx;
	}

	.search-btn {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #ffffff;
		margin-top: 1rpx;
	}

	.weui-btn {
		flex: 1;
		margin-top: 0;
		// margin-right: 20rpx;
		background-color: #0066E9;
	}

	.fixed-top {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		// bottom: 0;
		overflow: hidden;
		z-index: 10;
		background-color: #F3F3F3;
	}

	.scroll-box {
		padding-top: 358rpx;
		padding-bottom: 20rpx;
	}

	.apply-record {

		color: #01C1B2;
		border: 1rpx solid #01C1B2;
		background: transparent;

	}

	.service-order-list {
		padding: 20rpx 20rpx 0;
	}

	.service-order-list .weui-form-preview {
		padding: 0rpx 0rpx 30rpx 0rpx;
		margin-bottom: 20rpx;
		border-radius: 12rpx;
	}

	.service-order-list .weui-form-preview .weui-form-preview__hd {
		padding: 28rpx 40rpx;
		// margin-bottom: 10rpx;
		border: none;
		position: relative;
	}

	.service-order-list .weui-form-preview .weui-form-preview__hd .weui-tag {
		position: absolute;
		right: 2rpx;
		top: 2rpx;
	}

	.service-order-list .weui-form-preview .weui-form-preview__hd .vehicle {
		align-items: center;

		.title {
			font-weight: 500;
			color: #333333;
			font-size: 32rpx;
		}

		.desc {
			font-size: 28rpx;
			color: #333333;

		}
	}

	.service-order-list .weui-form-preview .weui-form-preview__bd {
		padding: 12rpx 0 10rpx !important;
		margin: 0 40rpx;
		border-bottom: 1px dashed #c3c3c3;
	}

	.service-order-list .weui-form-preview .weui-form-preview__bd .weui-form-preview__item {
		display: flex;
		justify-content: flex-start !important;
		padding-bottom: 20rpx !important;
	}

	.service-order-list .weui-form-preview .weui-form-preview__bd .weui-form-preview__label {
		min-width: 160rpx;
		font-size: 30rpx;
		font-weight: 400;
		color: #999999;
	}

	.service-order-list .weui-form-preview .weui-form-preview__bd .weui-form-preview__value {
		font-size: 30rpx;
		font-weight: 400;
		color: #333333;
	}

	.service-order-list .weui-form-preview .weui-form-preview__ft {
		padding: 30rpx 40rpx 0 40rpx !important;
	}

	.service-order-list .weui-form-preview__btn {
		display: flex;
	}

	.service-order-list .weui-form-preview__btn .btn-item {
		min-width: 140rpx;
		margin-right: 20rpx;
	}

	.service-order-list .weui-form-preview__btn .btn-item:last-child {
		margin-right: 0rpx;
	}

	.service-order-list .weui-form-preview__btn .btn-item .weui-btn_mini {
		border-radius: 36rpx;
	}

	.weui-tag {
		// background: rgba(0, 189, 50, 0.1);
		border-radius: 0rpx 0rpx 0rpx 30rpx;
		display: inline-block;
		height: 62rpx;
		padding: 0 20rpx;
		line-height: 62rpx;
		min-width: 170rpx;
		font-size: 26rpx;
		text-align: center;
		// color: #3874FF;
		box-sizing: border-box;
		white-space: nowrap;
	}


	.weui-title__decoration {
		position: relative;

	}

	.weui-title__decoration:before {
		content: ' ';
		position: absolute;
		left: 0rpx;
		top: 50%;
		width: 8rpx;
		height: 30rpx;
		background: #333333;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		border-radius: 4rpx;
		background: #333333;
	}
</style>
