<template>
	<view class="order-item_wrapper">
		<view class="order-item" @click="toDetail">
			<view class="item-title">
				<text class="is-trunk">
				</text>
			</view>
			<view class="item-container">
				<view class="item-bd">
					<view class="item-label">
						订单类型：
					</view>
					<view class="item-value">
						ETC新办
					</view>
				</view>
				<view class="item-bd">
					<view class="item-label">
						订单编号：
					</view>
					<view class="item-value">
						{{orderListData.id}}
					</view>
				</view>
				<view class="item-bd">
					<view class="item-label">
						申领日期：
					</view>
					<view class="item-value">
						{{ formatHandle(new Date(orderListData.createTime).getTime(), 'yyyy-MM-dd HH:mm:ss') }}
					</view>
				</view>
			</view>
			<view class="item-status info">
				{{orderListData.nodeName}}
			</view>
			<view class="btn-container">
				<view class="left">
					<view @click.stop="toDetail()" class="item-btn info">
						详情
					</view>
				</view>
				<view class="right">
					<block>
						<view class="item-btn info" @click.stop="toIssue">
							前往激活
						</view>
					</block>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		getBusinessType,
		getApplyStatus,
		getCodeStatus
	} from '@/common/method/filter.js';
	export default {
		components: {
			tLoading
		},
		props: {
			orderListData: {
				type: Object,
				default () {
					return {};
				}
			}
		},
		data() {
			return {
				isLoading: false
			}
		},
		computed: {

		},
		methods: {
			appeal() {
				//申诉 是否已申诉 0否 1 是
				if (this.orderListData.isAppeal == '0') {
					uni.navigateTo({
						url: '/pagesA/newBusiness/appeal/appeal-apply?orderListData=' + encodeURIComponent(JSON
							.stringify(this.orderListData))
					})
				} else if (this.orderListData.isAppeal == '1') {
					uni.navigateTo({
						url: '/pagesA/newBusiness/appeal/appeal-detail?orderListData=' + encodeURIComponent(JSON
							.stringify(this.orderListData))
					})
				}
			},
			toIssue() {
				console.log('走方法======>>>>>>>', id)
				uni.navigateTo({
					url: '/pagesA/newBusiness/issue/p-issue?applyId=' + this.orderListData.id
				})
			},
			toDetail() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderDetail?applyId=' + this.orderListData.id
				})
			},
			toSF() {
				console.log('wl')
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/orderWl/orderWlDetail?applyId=' + this.orderListData.id
				})
			},
			toPay() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/order/order?applyId=' + this.orderListData.id + '&type=notNext'
				})
			},
			//签署去车辆信息表
			toSign() {
				this.$store.dispatch('setApplyId', this.orderListData.id)
				uni.navigateTo({
					url: '/pagesA/newBusiness/vehicle/vehicle'
				})
			},
			updateOrder() {
				uni.navigateTo({
					url: '/pagesA/newBusiness/productSelect/productSelect?applyId=' + this.orderListData.id
				})
			},
			toAfter(type) {
				let data = this.orderListData
				let params = {
					applyId: data.id,
					customerName: data.applyRecordVO.customerName,
					vehicleCode: data.applyRecordVO.vehicleCode,
					vehicleColor: data.applyRecordVO.vehicleColor

				}
				if (type == 'afterApply') {
					uni.navigateTo({
						url: '/pagesA/newBusiness/afterSale/select-type?applyData=' + encodeURIComponent(JSON
							.stringify(params))
					})
				} else if (type == 'afterList') {
					uni.navigateTo({
						url: '/pagesA/newBusiness/afterSale/apply-list?applyId=' +
							this.orderListData.id
					})
				}

			},
			// toAfterDetail() {
			//  uni.navigateTo({
			//   url: '/pagesA/newBusiness/afterSale/apply-detail?applyId=' + this.orderListData.id
			//  })
			// },
			// toAfterList() {
			//  uni.navigateTo({
			//   url: '/pagesA/newBusiness/afterSale/apply-list?applyId=' + this.orderListData.id
			//  })
			// },
			confirmProduct() {
				this.isLoading = true
				this.$request.post(this.$interfaces.confirmProduct, {
					data: {
						id: this.orderListData.id,
					}
				}).then(res => {
					console.log(res, '获取申请单列表');
					this.isLoading = false
					if (res.code == 200) {

						this.$emit('handleUpdate', this.orderListData.id)

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			handleConfirm() {
				uni.showModal({
					title: "提示",
					content: '确定收货?',
					success: (res) => {
						if (res.confirm) {
							this.confirmProduct(this.orderListData.id)
							// console.log('确认取消')
						}
					}

				});
			},
			handleCancelOrder() {
				uni.showModal({
					title: "取消订单原因",
					confirmText: '确定',
					placeholderText: '请输入原因',
					editable: true,
					success: (res) => {
						if (res.confirm) {
							let reason = res.content
							this.cancelApplyOrder(this.orderListData.id, reason)
							// console.log('确认取消')
						}
					}

				});

			},
			cancelApplyOrder(id, reason) {
				this.isLoading = true
				this.$request.post(this.$interfaces.cancelApplyOrder, {
					data: {
						id: id,
						reason: reason
					}
				}).then(res => {
					console.log(res, '获取申请单列表');
					this.isLoading = false
					if (res.code == 200) {

						this.$emit('handleCancel', id)

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			formatHandle(time, format) {
				var t = new Date(time);
				var tf = function(i) {
					return (i < 10 ? '0' : '') + i;
				};
				return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
					switch (a) {
						case 'yyyy':
							return tf(t.getFullYear());
							break;
						case 'MM':
							return tf(t.getMonth() + 1);
							break;
						case 'mm':
							return tf(t.getMinutes());
							break;
						case 'dd':
							return tf(t.getDate());
							break;
						case 'HH':
							return tf(t.getHours());
							break;
						case 'ss':
							return tf(t.getSeconds());
							break;
					}
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.order-item {
		position: relative;
		margin: 20rpx;
		border-radius: 12rpx;
		background-color: #ffffff;
		font-family: PingFangSC-Medium, PingFang SC;
		overflow: hidden;
	}

	.item-title {
		position: relative;
		margin: 22rpx 40rpx;
		height: 45rpx;
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
		line-height: 45rpx;

		&:before {
			content: ' ';
			position: absolute;
			left: -39rpx;
			top: 8rpx;
			width: 7rpx;
			height: 30rpx;
			background-color: #333333;
		}
	}

	.item-title .is-trunk {
		font-size: 28rpx;
	}

	.item-status {
		position: absolute;
		right: 0;
		top: 0;
		// width: 169rpx;
		padding: 0 16rpx;
		height: 63rpx;
		border-radius: 0rpx 10rpx 0rpx 30rpx;
		text-align: center;
		line-height: 63rpx;
		font-size: 26rpx;
	}

	// 待支付，已取消，已退货退款，已退货不退款
	.item-status.info {
		background: rgba(133, 134, 134, 0.15);
		color: #6A6969;
	}

	// 设备已发货，审核通过，已完结，已签收，换货审核通过，退货审核通过，设备已寄回
	.item-status.success {
		background: rgba(0, 189, 50, 0.11);
		color: #00BD32;
	}

	// 换货审核中，退货审核中，待取货
	.item-status.warnning {
		background: rgba(255, 145, 0, 0.14);
		color: #FF9100;
	}

	// 后台审核中
	.item-status.primary {
		background: rgba(0, 102, 233, 0.12);
		color: #0066E9;
	}

	// 审核不通过，换货审核不通过，退货审核不通过
	.item-status.error {
		background: rgba(255, 84, 84, 0.15);
		color: #FF5454;
	}

	.item-container {
		margin: 20rpx 40rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx dashed #C3C3C3;
	}

	.item-bd {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.item-label {
		width: 160rpx;
		font-size: 26rpx;
		font-weight: 400;
		color: #999999;
	}

	.item-value {
		flex: 1;
		font-size: 26rpx;
		font-weight: 400;
		color: #333333;
	}

	.btn-container {
		display: flex;
		justify-content: space-between;
		margin: 20rpx 40rpx;
	}

	.left {}

	.right {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		flex-wrap: wrap;
	}

	.right .item-btn {
		margin-left: 20rpx;

		&:nth-child(2n) {
			margin-bottom: 15rpx;
		}
	}

	.right .item-btn:first-child {
		margin-left: 0;
	}

	.item-btn {
		// padding: 12rpx 40rpx;
		width: 190rpx;
		height: 58rpx;
		line-height: 58rpx;
		border-radius: 36rpx;
		font-size: 26rpx;
		text-align: center;
	}

	.item-btn.info {
		border: 2rpx solid #E8E8E8;
		color: #323435;
		background: #FFFFFF;
	}

	.item-btn.primary {
		border: 2rpx solid #0066E9;
		color: #FFFFFF;
		background: #0066E9;
	}
</style>
