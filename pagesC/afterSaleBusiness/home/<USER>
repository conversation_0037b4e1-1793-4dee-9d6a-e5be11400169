<template>
	<view class="apply-after-sale" v-if="show">
		<view class="line-block"></view>
		<view class="weui-card form-item">
			<view class="weui-card-hd">
				<view class="weui-card-hd-wrapper">
					<view class="weui-card-hd-title">车辆信息</view>
				</view>
				<view class="weui-tag" :class="'weui-tag--'+formData.cpuStatus">
					{{formData.cpuStatusStr}}
				</view>
			</view>
			<view class="weui-card-bd">
				<view class="list vehicle-info">
					<view class="list-item">
						<view class="list-item_label">
							车牌号码
						</view>
						<view class="list-item_value">
							{{formData.vehicleCode}}【{{getVehicleColor(formData.vehicleColor)}}】
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							ETC卡号
						</view>
						<view class="list-item_value">
							{{formData.cardNo}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							卡片质保有效期
						</view>
						<view class="list-item_value">
							{{formData.cpuDate}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							OBU编号
						</view>
						<view class="list-item_value">
							{{formData.obuNo}}
						</view>
					</view>
					<view class="list-item">
						<view class="list-item_label">
							OBU质保有效期
						</view>
						<view class="list-item_value">
							{{formData.obuDate}}
						</view>
					</view>
					<view class="list-item" v-if="formData.amount !== ''">
						<view class="list-item_label">
							账户可用余额
						</view>
						<view class="list-item_value list-item_value_color">
							{{formData.amount}}元
						</view>
						<view class="btn-item" @click="goCarBusinessPage">
							去充值
						</view>
					</view>

				</view>
			</view>
		</view>
		<view class="line-block"></view>
		<view class="weui-card form-item">
			<view class="weui-card-hd">
				<view class="weui-card-hd-wrapper">
					<view class="weui-card-hd-title">设备售后</view>
				</view>
				<!-- <image src="../../static/help-icon.png" mode="" class="tips-icon"></image> -->
			</view>
			<view class="weui-card-bd">
				<view class="card" v-for="(item,index) in cardList" :key="index" @click="next(item,formData)">
					<view>
						<image :src="item.url" mode="" class="card-icon"></image>
					</view>
					<view class="card-title">{{item.title}}</view>
				</view>
			</view>
		</view>
		<!-- 温馨提示 -->
		<u-mask :show="warmReminder"></u-mask>
		<view class="handleTips" v-if="warmReminder">
			<view class="title">{{dialogTitle}}</view>
			<view class="tips-content" v-html="dialogMsg" @click="dot" v-if="viewDot"></view>
			<view class="tips-content" @click="dot" v-if="!viewDot">{{dialogMsg}}</view>
			<view class="bottom">
				<view class="btn btn-sure" @click="warmReminderHandle">{{buttonName}}</view>
			</view>
		</view>
		<!-- 蓝牙提示 -->
		<blueDialog :show="isShowDialog" @comfirm="comfirm" @cancel="bluetoothFree">
		</blueDialog>
		<!-- <StatusDialog :show="dialogVisiable" :content="content" :showCancel="false" @comfirm="dialogVisiable=false"
			:isShowTip="true" :contentStyle="contentStyle" :isClick="isClick">
		</StatusDialog> -->
		<!-- 用户须知 -->
		<handleInstruction :show="showHandle" :opBusinessType="opBusinessType" :content="content"
			:contentStyle="contentStyle" @comfirm="goOn" @cancel="equipmentInspect">
		</handleInstruction>
		<!-- 注销办理须知 -->
		<needKnow :show.sync="isShowNeedKnow" :showNeedKnow="showNeedKnow" :showTips="showTips" :type="needKonwType"
			:content="logoutContent" :redText="redText" :contentArr="contentArr" :contentStyle="contentStyle"
			@comfirm="logoutNext" @goToRecharge="goCarBusinessPage">
		</needKnow>
		<!-- 注销温馨提示 -->
		<!-- 		<logOutDialog v-if="isShowLogoutDialog" :show="isShowLogoutDialog" @comfirm="comfirm" :params="formData">
		</logOutDialog> -->
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getAccountId,
		getEtcAccountInfo,
		getLoginUserInfo,
		setCurrentCar,
		getCurrentCar
	} from '@/common/storageUtil.js'
	import {
		getVehicleColor
	} from '@/common/method/filter.js'
	import blueDialog from "../../components/status-dialog/index.vue"
	import StatusDialog from "@/pagesC/components/statusDialog/statusDialog.vue"
	import handleInstruction from '../../components/handle-instructions/index.vue'
	// import logOutDialog from '../../components/logout-dialog/logout-dialog.vue'
	import needKnow from '../../components/logout-dialog/need-know.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import {
		checkBluetoothObu
	} from "@/common/util.js";
	export default {
		components: {
			blueDialog,
			StatusDialog,
			handleInstruction,
			// logOutDialog,
			needKnow,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				show: false,
				showTips: false,
				showNeedKnow: false,
				isShowNeedKnow: false,
				formData: {
					vehicleCode: '',
					cardNo: '',
					cpuDate: '',
					obuDate: '',
					obuNo: '',
					cpuStatus: '', //0未售 1正常 2挂失 3销卡 4废卡 5冻结 6遗失
					cpuStatusStr: '',
					amount: '', //根据产品类型判断余额类型
					vehicleColor: ''
				},
				cardList: [{
						url: '../../static/reset_icon.png',
						title: '失效重新\n激活',
						type: '1'
					},
					{
						url: '../../static/lost_icon.png',
						title: '遗失/损毁\n补办',
						type: '2'
					}, {
						url: '../../static/change_icon.png',
						title: '设备故障\n更换',
						type: '3'
					}, {
						url: '../../static/cancellation_icon.png',
						title: '设备注销',
						type: '4'
					},
					{
						url: '../../static/productConver/product_conver.png',
						title: '产品转换',
						type: '5'
					},
					{
						url: '../../static/history_icon.png',
						title: '历史订单',
						type: '6'
					}
				],
				currentData: {}, //激活当前的数据
				warmReminder: false,
				dialogMsg: '',
				buttonName: '确定',
				dialogTitle: '温馨提示',
				redText: '', //提示标题
				resCode: null, //更换补办校验状态码
				isShowDialog: false,
				//非蓝牙提示
				// dialogVisiable: false,
				content: '',
				logoutContent: '', //注销用户须知
				contentArr: [], //注销不符合用户条件数据
				// isClick: true, //是否可点击
				contentStyle: {
					"text-align": "left"
				},
				//办理协议
				showHandle: false,
				viewDot: false, //是否可以查看网点
				opBusinessType: 1, //1-失效重新激活，2-遗失/损毁补办，3-设备故障更换，4-设备注销
				needKonwType: '', //办理须知类型
				logoutResult: {}, //注销详情信息,判断新的限制弹窗用
				setTipsFlag: false,
			}
		},
		computed: {},
		onLoad() {
			let vehicleInfo = this.$store.getters['afterSale/afterSaleVehicle']
			console.log('getCurrUserInfo', getCurrUserInfo())
			console.log('vehicleInfo', vehicleInfo)
			this.formData.vehicleCode = vehicleInfo.vehicleCode
			this.formData.vehicleColor = vehicleInfo.vehicleColor
			this.formData.cardNo = vehicleInfo.cardNo
			this.formData.obuNo = vehicleInfo.obuNo
			this.getVehicleDetail()
			// this.getLogoutDetail()
		},
		methods: {
			getVehicleColor,
			getVehicleDetail() {
				let params = {
					vehicleCode: this.formData.vehicleCode,
					vehicleColor: this.formData.vehicleColor,
					customerId: getAccountId(),
					customerName: getCurrUserInfo().customer_name,
					cpuCardId: this.formData.cardNo,
					obuId: this.formData.obuNo
				}

				this.isLoading = true
				console.log(params)
				this.$request.post(this.$interfaces.afterSalesInfo, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log('售后详情', res.data)
					if (res.code == 200) {
						this.show = true
						this.formData.cpuDate = res.data.cpuInfo.warrantyDate
						this.formData.obuDate = res.data.obuInfo.warrantyDate
						if (res.data.cpuInfo.gxCardType == '5') {
							this.formData.amount = this.moneyFilter(res.data.cardAccountAmount
								.availableAmount)
						} else if (res.data.cpuInfo.gxCardType == '0') {
							this.formData.amount = this.moneyFilter(res.data.cardAccountAmount
								.walletAmount)
						}
						this.formData.cpuStatus = res.data.cpuInfo.cpuStatus
						this.formData.cpuStatusStr = res.data.cpuInfo.cpuStatusStr
						this.formData.openTime = res.data.cpuInfo.openTime
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//获取注销详情
			getLogoutDetail(callback) {
				this.isLoading = true
				let params = {
					cardNo: this.formData.cardNo
				}

				this.$request.post(this.$interfaces.getLogoutDetail, {
					data: params
				}).then(res => {
					console.log('详情信息=========>>>>>', res)
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						this.logoutResult = result
						callback()
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			checkBlueObu(obu, callback) {
				//校验蓝牙OBU白名单
				this.isLoading = true
				let params = {
					obuNo: obu
				}

				this.$request.post(this.$interfaces.checkObuBlue, {
					data: params
				}).then(res => {
					console.log('obu校验========>>>>>', res)
					this.isLoading = false;
					if (res.code == 200) {
						// let result = res.data
						// this.logoutResult = result
						callback(res.data)
					} else {
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			},
			warmReminderHandle() {
				this.warmReminder = false
				if (this.setTipsFlag) {
					//调用办理前校验接口
					this.beforCheckLogout()
				}
			},
			showTipModal() {
				//SDXL-山东信联，HCB-货车帮，COMM-交通银行，HXB-华夏银行
				//限制提示
				//orgType: 渠道类型:0-分对分，1-总对总
				let msg = ''
				if (this.logoutResult.orgId == 'SDXL' || this.logoutResult.orgId == 'HCB') {
					msg = '该卡片为山东信联/货车帮产品，若有保证金需退款，请您在卡片注销后登陆山东信联/货车帮小程序进行申请退款。'
				} else if (this.logoutResult.orgType == '0' && this.logoutResult.orgId == 'COMM' && this.logoutResult
					.life != '0') {
					//交通银行，未满5年提示
					msg = '该卡片为交通银行分对分产品，请您先登录交行APP或拨打95559申请减免违约金再进行注销，避免后续卡片扣费情况发生。'
				} else if (this.logoutResult.orgType == '0' && this.logoutResult.orgId == 'HXB' && this.logoutResult
					.life <= 2) {
					//华夏银行，未满2年提示
					msg = '该卡片为华夏银行分对分产品，使用未满2年华夏银行将收取200元违约金。'
				}

				return msg
			},
			goCarBusinessPage() {
				// 获取车辆详情，存缓存
				this.sendVehicleBizSearch()
			},
			sendVehicleBizSearch() {
				setCurrentCar({})
				let data = {
					routePath: this.$interfaces.vehicleBizSearch.method,
					bizContent: {
						vehicle_code: this.formData.vehicleCode,
						vehicle_color: this.formData.vehicleColor
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log('车辆详情', res)
						this.isLoading = false

						if (res.code == 200 && res.data && res.data.length) {
							let item = res.data[0];
							if (this.formData.vehicleCode == item.vehicle_code && this.formData.cardNo == item
								.cpu_card_id) {

								this.goRechargeBusinessHandle(item);
							}

						}
					})
					.catch((error) => {
						console.log(error);
						this.isLoading = false
					})
			},
			// 根据查询出的卡类型进行不同充值业务
			goRechargeBusinessHandle(item) {

				if (item.gx_card_type != '5' && item.gx_card_type != '0' && item.gx_card_type != '8' && item
					.gx_card_type != '9') {
					uni.showModal({
						title: '提示',
						content: '您当前车辆绑定的卡不是捷通日日通记账卡或储值卡，无法充值'
					})
					return
				}
				if (item.gx_card_type == '5' || item.gx_card_type == '8') {
					setCurrentCar(item)
					uni.navigateTo({
						url: '/pagesB/rechargeBusiness/recharge/p-recharge'
					})
					return
				}
				if (item.gx_card_type == '0') {
					setCurrentCar(item)
					uni.navigateTo({
						url: '/pagesB/loadBusiness/recharge/recharge?businessType=cancel'
					})
					return
				}
				if (item.gx_card_type == '9') {
					this.getClientAccountInfo()
					return
				}
			},
			//查询客账信息
			getClientAccountInfo() {
				let params = {
					customerId: getAccountId()
				}
				this.$request
					.post(this.$interfaces.rechargeVehicleList, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							if (res.data.clientAccount) {
								let accountListData = res.data.clientAccount
								let params = JSON.parse(JSON.stringify(accountListData))
								params.bindVehicleList = JSON.stringify(params.bindVehicleList)
								uni.navigateTo({
									url: '/pagesB/rechargeBusiness/rechargeAccount/index?' + this
										.objToUrlParam(params)
								})
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})

			},
			next(item, data) {
				this.viewDot = false
				//1-失效重新激活，2-遗失/损毁补办，3-设备故障更换，4-设备注销
				this.currentData = data
				this.opBusinessType = item.type
				if (item.type == '1') {
					this.checkBlueObu(this.formData.obuNo, res => {
						if (res) {
							this.comfirm()
						} else {
							//无蓝牙提示
							this.isShowDialog = false
							this.dialogMsg =
								'非蓝牙OBU：您的ETC设备不支持连接自助激活，请前往线下网点办理设备激活业务。<span style="color:#0066E9">查看网点</span>'
							this.viewDot = true
							this.warmReminder = true
							// this.bluetoothFree()
						}
					})
				} else if (item.type == '2') {
					this.checkBlueObu(this.formData.obuNo, res => {
						if (res) {
							this.exchangeCheck()
						} else {
							this.isShowDialog = false
							this.dialogMsg =
								'非蓝牙OBU：您的ETC设备不支持办理线上自助补办业务，请前往线下网点办理。<span style="color:#0066E9">查看网点</span>'
							this.viewDot = true
							this.warmReminder = true
						}
					})
				} else if (item.type == '3') {
					this.checkBlueObu(this.formData.obuNo, res => {
						if (res) {
							this.exchangeCheck()
						} else {
							this.isShowDialog = false
							this.dialogMsg =
								'非蓝牙OBU：您的ETC设备不支持办理线上自助更换业务，请前往线下网点办理。<span style="color:#0066E9">查看网点</span>'
							this.viewDot = true
							this.warmReminder = true
						}
					})
				} else if (item.type == '4') {
					this.getLogoutDetail(() => {
						let msg = this.showTipModal()
						if (msg) {
							this.isShowDialog = false
							this.dialogMsg = msg
							this.viewDot = true
							this.warmReminder = true
							this.setTipsFlag = true
							return
						}
						//调用办理前校验接口
						this.beforCheckLogout()
					})
				} else if (item.type == '5') {
					//产品转换
					this.beforCheckConver()
				} else if (item.type == '6') {
					uni.navigateTo({
						url: '/pagesA/serviceOrder/afterSale/index'
					})
				}
			},
			comfirm() {
				this.isShowDialog = false
				this.activateActivateCheck()
			},

			//激活办理前校验
			activateActivateCheck() {
				//是蓝牙OBU的操作
				let params = {
					carColor: this.currentData.vehicleColor,
					carNo: this.currentData.vehicleCode,
					cardNo: this.currentData.cardNo,
					custMastId: getEtcAccountInfo().custMastId,
					netUserNo: getLoginUserInfo().userNo,
					obuNo: this.currentData.obuNo,
					opBusinessType: this.opBusinessType,
				}
				this.isLoading = true
				console.log(params)
				this.$request.post(this.$interfaces.activateApplyCheck, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						this.handlingInstructions()
					} else {
						if (res.code == '5001' || res.code == '5002' || res.code == '5004' || res.code == '5005') {
							this.dialogMsg = res.msg + '<span style="color:#0066E9">查看网点</span>'
							this.viewDot = true
						} else {
							this.viewDot = false
							this.dialogMsg = res.msg
						}
						this.buttonName = '确定'
						this.warmReminder = true
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//激活办理须知
			handlingInstructions() {
				this.isLoading = true
				this.$request.post(this.$interfaces.handlingInstructions, {
					data: {}
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						this.content = res.data
						this.showHandle = true
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//无蓝牙提示
			bluetoothFree() {
				this.isShowDialog = false
				this.dialogMsg = '非蓝牙OBU：您的ETC设备不支持连接自助激活，请前往线下网点办理设备激活业务。<span style="color:#0066E9">查看网点</span>'
				this.viewDot = true
				this.warmReminder = true
			},
			//查看网点
			dot() {
				if (!this.viewDot) {
					return
				}
				let callcenter
				if (this.resCode == 6005) {
					//更换补办查看处理办法
					callcenter = 'https://mp.weixin.qq.com/s/_0mvlJbF1BTFrr24s2W4qw'
				} else {
					//其他默认查看网点
					callcenter = "https://www.gxetc.com.cn/h5/#/businessOutlets"
				}

				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' +
						encodeURIComponent(callcenter)
				})
				this.warmReminder = false
			},
			//更换补办办理前校验
			exchangeCheck() {
				let params = {
					carColor: this.currentData.vehicleColor,
					carNo: this.currentData.vehicleCode,
					cardNo: this.currentData.cardNo,
					custMastId: getEtcAccountInfo().custMastId,
					userNo: getLoginUserInfo().userNo,
					obuNo: this.currentData.obuNo,
					opBusinessType: this.opBusinessType,
				}
				this.isLoading = true
				console.log(params)
				this.$request.post(this.$interfaces.exchangeCheck, {
					data: params
				}).then(res => {
					this.isLoading = false
					if (res.code == 200) {
						this.infomation()
					} else {
						this.dialogMsg = res.msg
						this.buttonName = '确定'
						this.warmReminder = true

						if (res.code == '6001' || res.code == '6002' || res.code == '6004') {
							this.dialogMsg = res.msg + '<span style="color:#0066E9">查看网点</span>'
							this.viewDot = true
						} else if (res.code == '6005') {
							this.dialogMsg = res.msg + '<span style="color:#0066E9">查看处理方法</span>'
							this.resCode = res.code
							this.viewDot = true
						} else {
							this.viewDot = false
							this.dialogMsg = res.msg
						}
						this.buttonName = '确定'
						this.warmReminder = true
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//更换补办办理须知
			infomation() {
				this.isLoading = true
				let params = {
					type: this.opBusinessType == 2 ? 2 : 3 //2-遗失/损毁补办，3-设备故障更换；
				}
				this.$request.post(this.$interfaces.exchangeInfomation, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						this.content = res.data
						this.showHandle = true
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//前往检测
			equipmentInspect() {
				uni.navigateTo({
					url: '/pagesC/selfCheck/index'
				})
				this.showHandle = false
			},
			goOn() {
				if (this.opBusinessType == 1) {
					//二次激活
					uni.navigateTo({
						url: '/pagesC/afterSaleBusiness/applyAfterSale/index?opBusinessType=' + this.opBusinessType
					})
				} else if (this.opBusinessType == 2) {
					//补办
					uni.navigateTo({
						url: '/pagesC/reissueBussiness/apply/index?opBusinessType=' + this.opBusinessType
					})
				} else if (this.opBusinessType == 3) {
					//更换
					uni.navigateTo({
						url: '/pagesC/changeBussiness/apply/index?opBusinessType=' + this.opBusinessType
					})
				} else if (this.opBusinessType == 4) {
					//注销
				}

				this.showHandle = false
			},
			// 线上注销办理前校验
			beforCheckLogout() {
				let params = {
					cardNo: this.formData.cardNo,
					callType: '0' //办理前校验
				}

				this.isShowNeedKnow = true
				console.log(params)
				this.$request.post(this.$interfaces.beforCheckLogout, {
					data: params
				}).then(res => {
					// this.isShowNeedKnow = false
					console.log(res)
					if (res.code == 200) {
						if (res.data && res.data.length > 0) {
							//不满足条件温馨提示
							this.showTips = true
							this.needKonwType = 'logout'
							let result = res.data
							let filterArr = result.filter(item => {
								return item.checkType == 9
							})
							if (filterArr.length > 0) {
								this.contentArr = filterArr
							} else {
								this.contentArr = result
							}
						} else {
							console.log('办理须知')
							//办理须知
							this.getLogoutConfig()
						}
					} else {
						//报错温馨提示
						this.logoutContent = res.msg
						this.showTips = true
						this.needKonwType = 'logout'
					}
				}).catch(err => {
					this.isShowNeedKnow = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			getLogoutConfig() {
				//注销办理须知
				this.$request.post(this.$interfaces.getLogoutConfig).then(res => {
					// this.isShowNeedKnow = false
					console.log(res)
					if (res.code == 200) {
						this.showNeedKnow = true
						this.logoutContent = res.data[0].freeInstructions
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//产品转换办理前校验
			beforCheckConver() {
				let params = {
					cardNo: this.formData.cardNo,
				}

				this.isShowNeedKnow = true
				console.log(params)
				this.$request.post(this.$interfaces.beforCheckConver, {
					data: params
				}).then(res => {
					// this.isShowNeedKnow = false
					console.log(res)
					if (res.code == 200) {
						if (res.data && res.data.length > 0) {
							//不满足条件温馨提示
							this.showTips = true
							this.needKonwType = 'product'
							this.redText = '您当前不符合转换条件!'

							let result = res.data
							this.contentArr = result

						} else {
							console.log('办理须知==========>>>>')
							//办理须知
							this.getProductConfig() //测试先注释
							// this.logoutNext()
						}
					} else {
						//报错温馨提示
						this.logoutContent = res.msg
						this.showTips = true
						this.needKonwType = 'product'
					}
				}).catch(err => {
					this.isShowNeedKnow = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			getProductConfig() {
				//产品转换办理须知
				this.$request.post(this.$interfaces.productConfig).then(res => {
					// this.isShowNeedKnow = false
					console.log('产品转换配置====>>>>>', res)
					if (res.code == 200) {
						this.showNeedKnow = true
						this.logoutContent = res.data.handleInstructions
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					console.log('err', err)
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			//注销下一步
			logoutNext() {
				console.log('this.opBusinessType', this.opBusinessType)
				if (this.opBusinessType == '4') {
					uni.navigateTo({
						url: '/pagesC/logoutBussiness/apply/index'
					})
				} else if (this.opBusinessType == '5') {
					uni.navigateTo({
						url: '/pagesC/productConver/apply'
					})
				}

			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},
			objToUrlParam(obj) {
				if (obj && Object.keys(obj).length) {
					return Object.keys(obj)
						.map((key) => {
							return key + '=' + obj[key]
						})
						.join('&')
				}
				return ''
			},
		}
	}
</script>

<style lang="scss" scoped>
	.apply-after-sale {
		width: 100%;
		height: 100%;
		background-color: #F8F8F8;

		.line-block {
			height: 20rpx;
			width: 100%;
			background-color: #F6F6F6;
		}

		.weui-card {
			width: calc(100% - 40rpx);
			margin: auto;
			position: relative;
			border-radius: 12rpx;

			.weui-card-hd {
				padding: 22rpx 30rpx 35rpx 30rpx !important
			}

			.list-item {
				position: relative;
				align-items: center;
			}

			.list-item_label {
				width: 247rpx !important;
			}

			.list-item_value {
				width: calc(100% - 260rpx);
			}

			.list-item_value_color {
				font-size: 28rpx !important;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500 !important;
				color: #E80101 !important;
			}

			.btn-item {
				width: 147rpx;
				height: 56rpx;
				background: #FFFFFF;
				border-radius: 14rpx;
				border: 2rpx solid #5591FF;
				font-size: 30rpx;
				font-weight: 400;
				color: #5591FF;
				line-height: 56rpx;
				text-align: center;
				position: absolute;
				right: 0rpx;
			}

			.weui-tag {
				position: absolute;
				right: 2rpx;
				top: 2rpx;
				background: rgba(0, 189, 50, 0.1);
				background: rgba(106, 105, 105, 0.1);
				border-radius: 0rpx 0rpx 0rpx 30rpx;
				display: inline-block;
				height: 62rpx;
				padding: 0 20rpx;
				line-height: 62rpx;
				min-width: 170rpx;
				font-size: 26rpx;
				text-align: center;
				color: #333333;
				box-sizing: border-box;
				white-space: nowrap;
				font-weight: 400;
			}

			.weui-tag.weui-tag--1 {
				color: #00BD32;
				background: rgba(0, 189, 50, 0.1);
			}

			.tips-icon {
				width: 31rpx;
				height: 31rpx;
				background-size: 100%;
				position: absolute;
				left: 172rpx;
				top: 28rpx;
			}

			.weui-card-bd {
				overflow: hidden;

				.card {
					width: 25%;
					text-align: center;
					display: inline-block;
					vertical-align: middle;
					margin-bottom: 43rpx;
				}

				.card-icon {
					width: 85rpx;
					height: 85rpx;
					background-size: 100%;
				}

				.card-title {
					width: 112rpx;
					height: 68rpx;
					font-size: 25rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #888888;
					line-height: 34rpx;
					display: inline-block;
				}
			}
		}

		.handleTips {
			width: calc(100% - 130rpx);
			height: 760rpx;
			background-color: #fff;
			border-radius: 24rpx;
			position: absolute;
			z-index: 99999;
			top: calc(50% - 380rpx);
			left: 65rpx;

			.title {
				font-size: 36rpx;
				font-weight: 600;
				color: #323435;
				padding: 42rpx 0;
				text-align: center;
				border-bottom: 1px solid #E9E9E9;
			}

			.tips-content {
				width: calc(100% - 90rpx);
				height: calc(100% - 300rpx);
				overflow-y: scroll;
				text-align: left;
				margin: 37rpx auto 0;
				font-size: 28rpx;
				color: #323435;
				font-weight: 400;
				line-height: 50rpx;
			}

			.bottom {
				display: flex;
				justify-content: space-around;

				.btn {
					width: auto;
					height: 70rpx;
					line-height: 70rpx;
					text-align: center;
					padding: 0 57rpx;
				}

				.btn-sure {
					background-color: #0066E9;
					color: #fff;
					border: 1px solid #e9e9e9;
					border-radius: 14rpx;
				}
			}
		}

	}
</style>