<template>
	<view class="prompt">
		<view class="line-block"></view>
		<view class="card">
			<view>
				<image src="../../static/suc.png" mode="" class="prompt-icon"></image>
			</view>
			<view class="msg">{{contentList[type].content}}</view>
		</view>
		<tButton :buttonList="buttonList" @toAfterDetail="toAfterDetail" @toActivate="toActivate"
			@toRessiueDetail="toRessiueDetail" @toHome="toHome" @toProgress="toProgress"
			@toChangeDetail="toChangeDetail" @toLogoutDetail="toLogoutDetail"></tButton>
		<tLoading :isShow="isLoading" />

	</view>
</template>

<script>
	import tButton from '@/pagesC/components/t-button/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getOpenid
	} from '@/common/storageUtil.js';
	import {
		successBtnList
	} from '@/pagesC/common/optionsData.js';
	export default {
		components: {
			// handleStep,
			tButton,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				type: '',
				id: '',
				orderId: '',
				orderStatus: '',
				buttonList: [],
				contentList: successBtnList,
				deviceType: '',
				cardNo: '' //卡号
			}
		},
		onLoad(options) {
			console.log(options)
			if (options) {
				if (options.type) {
					this.type = options.type
					this.buttonList = this.contentList[this.type].button
					console.log('this.buttonList', this.buttonList)
					if (options.type == '0') {
						uni.setNavigationBarTitle({
							title: '设备自助激活申请'
						})
					} else if (options.type == '7' || options.type == '8' || options.type == '9' || options.type == '10' ||
						options.type == '11') {
						uni.setNavigationBarTitle({
							title: '设备自助补办申请'
						})
					} else if (options.type == '13' || options.type == '14') {
						uni.setNavigationBarTitle({
							title: '设备注销申请'
						})
					} else {
						uni.setNavigationBarTitle({
							title: '设备自助更换申请'
						})
					}
				}
				if (options.id) {
					this.id = options.id
				}
				if (options.orderStatus) {
					this.orderStatus = options.orderStatus
				}
				if (options.orderId) {
					this.orderId = options.orderId
				}
				if (options.deviceType) {
					this.deviceType = options.deviceType
				}
				if (options.cardNo) {
					this.cardNo = options.cardNo
				}
			}
		},
		methods: {
			toAfterDetail() {
				if (this.orderStatus == '101' || this.orderStatus == '102' || this.orderStatus == '103') {
					uni.redirectTo({
						url: '/pagesC/afterSaleBusiness/orderDetails/index?id=' + this.id
					})
					return
				}
				uni.redirectTo({
					url: '/pagesC/afterSaleBusiness/orderDetails/viewDetail?id=' + this.id
				})

			},
			toChangeDetail() {
				if (this.type == '1' || this.type == '12' || this.type == '3' || this.type == '4') {
					let pages = getCurrentPages(); //获取所有页面栈实例列表
					let prevPage = pages[pages.length - 2]; //上一页页面实例
					if (prevPage && prevPage.$vm.getOrderDetail) {
						prevPage.$vm.getOrderDetail()
						prevPage.$vm.getImgList()
						uni.navigateBack({
							delta: 1
						})
						return
					}
					uni.redirectTo({
						url: '/pagesC/changeBussiness/orderDetails/failPassIndex?orderId=' + this.orderId
					})
					return
				}
				uni.redirectTo({
					url: '/pagesC/changeBussiness/orderDetails/index?orderId=' + this.orderId
				})
			},
			toRessiueDetail() {
				uni.redirectTo({
					url: '/pagesC/reissueBussiness/orderDetails/index?orderId=' + this.orderId
				})
			},
			toActivate() {
				if (this.deviceType == '0' && this.type == '9') {
					//补办卡激活 直接连接蓝牙开始激活
					uni.reLaunch({
						url: '/pagesA/newBusiness/issue/issue-confirm'
					})
					return
				}
				uni.reLaunch({
					url: '/pagesA/newBusiness/issue/issue-install?activationType=2'
				})
			},
			toProgress() {
				uni.redirectTo({
					url: '/pagesC/changeBussiness/orderProgress/index?orderId=' + this.orderId
				})
			},
			toHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>/p-home'
				})
			},
			toLogoutDetail() {
				uni.navigateTo({
					url: '/pagesC/logoutBussiness/orderDetail/orderDetail?cardNo=' + this.cardNo + '&orderId=' +
						this.orderId
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.prompt {
		width: 100%;
		height: 100%;

		.card {
			width: calc(100% - 40rpx);
			margin: auto;
			text-align: center;
			padding-bottom: 100rpx;
			background-color: #fff;
			border-radius: 12rpx;

			.prompt-icon {
				width: 452rpx;
				height: 437rpx;
				background-size: 100%;
			}

			.msg {
				width: calc(100% - 90rpx);
				height: 100rpx;
				margin: auto;
			}
		}

		.bottom-box {
			display: flex;

			.btn-item {
				flex: 1;
			}

			.btn-item-left {
				margin-right: 32rpx;
			}

			.btn-item-right {}
		}
	}
</style>