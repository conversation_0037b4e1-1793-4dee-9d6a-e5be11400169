<template>
	<view class="apply-after-sale" v-if="searchSuc">
		<orderNo :result="applyDetailObj"></orderNo>
		<view class="apply-after-sale-content">
			<view class="section">
				<orderProgress v-if="Object.keys(applyDetailObj).length > 0" @click="viewProgress"
					:result="applyDetailObj">
				</orderProgress>
			</view>
			<view class="section">
				<vehicle :vehicleObj="applyDetailObj"></vehicle>
			</view>
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">提交材料</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="list-item align-items-center">
								<view class="list-item_label">
									申请理由
								</view>
								<view class="list-item_value">
									{{applyDetailObj.applyReasonTypeStr}}
								</view>
							</view>
							<view class="list-item align-items-center">
								<view class="list-item_label">
									补充说明
								</view>
								<view class="list-item_value">
									{{applyDetailObj.applyReason || ''}}
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label">
									附件
								</view>
								<view class="list-item_attachment">
									<view class="upload-from">
										<view class="upload" v-for="(item,index) in vehicleFile" :key="index">
											<view class="upload-wrap">
												<view class="upload-wrap-bd" v-if="item.base64img !=''">
													<image :src="item.base64img" class="upload-wrap__img"
														mode='aspectFilt'>
													</image>
												</view>
												<view v-if="!item.base64img" class="upload-wrap-bd"
													@tap="ChooseImage(item)">
													<image :src="item.url" class="upload-wrap__img"></image>
												</view>
												<view class="upload-wrap-desc">{{item.label}}</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import orderProgress from '../../components/order-progress/orderProgress.vue';
	import vehicle from '../../components/vehicle/vehicle.vue';
	import orderNo from '../../components/order-no/orderNo.vue'
	export default {
		components: {
			tLoading,
			cpimg,
			orderNo,
			orderProgress,
			vehicle
		},
		data() {
			return {
				searchSuc: false,
				isLoading: false,
				ownFlag: 0,
				value: '',
				type: 'textarea',
				border: true,
				height: 100,
				applyPic: {
					drivingLicenseFrontUrl: '', //行驶证正页照片地址
					drivingLicenseSubpageUrl: '', //行驶证副页照片地址
					obuFailurePhotoUrl: '', //OBU失效照片
				},
				vehicleFile: [{
						photo_code: "3",
						ocr_type: 4,
						label: "行驶证正页",
						base64img: '',
						key: 'drivingLicenseFrontUrl',
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_front.png',
					},
					{
						photo_code: "12",
						ocr_type: 7,
						label: "行驶证副页",
						base64img: '',
						key: 'drivingLicenseSubpageUrl',
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_back.png',
					},
					{
						label: "OBU失效照片",
						base64img: '',
						key: 'obuFailurePhotoUrl',
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png',
					}
				],
				sourceType: '0',
				currentFile: {},
				inputStyle: {
					"marginLeft": "12rpx",
					"fontSize": "26rpx",
					"color": "#333333",
					"lineHeight": '60rpx',
					"height": "60rpx"
				},
				show: false,
				applyReasonTypeList: [{
						label: '标签掉落',
						value: '0',
					},
					{
						label: '插卡显示标签失效/标签拆卸',
						value: '1',
					},
					{
						label: '其他（可备注详细原因，字数上限100）',
						value: '2',
					}
				],
				isEdit: false,
				operate: 'edit', //操作 view 查看 edit 修改
				id: '',
				applyDetailObj: {
					orderNo: '',
					orderStatus: '', //1-待审核,2-审核通过,3-审核不通过,4-已取消,5-已完结,6-已关闭
					drivingLicense: null,
					cardNo: '',
					obuNo: '',
					applyReason: '',
					applyReasonType: '',
					userType: '', // 客户类型
					vehicleType: '', // 车辆类型
					processNode: '', //提交申请-10，系统审核-20，人工审核-30，设备激活-40
					carNo: '', //车牌
				},
				applyPic: {
					drivingLicenseFrontUrl: '', //行驶证正页照片地址
					drivingLicenseSubpageUrl: '', //行驶证副页照片地址
					obuFailurePhotoUrl: '', //OBU失效照片
				},
				drivingLicense: { // 行驶证ocr识别信息
					approvedCount: '', //核定载人数
					height: '', //高
					length: '', //长
					plateNum: '', //车牌号码
					totalMass: '', //总质量
					vehicleOwner: '', // 车辆所有人
					vehicleType: '', //车辆类型
					width: '', //
				},
			}
		},
		computed: {

		},
		watch: {

		},
		onLoad(options) {
			console.log(options)
			this.id = options.id
			this.activateApplyDetail()
		},
		methods: {
			activateApplyDetail() {
				let params = {
					id: this.id
				}

				this.isLoading = true
				console.log(params)
				this.$request.post(this.$interfaces.activateApplyDetail, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						this.searchSuc = true
						this.applyDetailObj = res.data
						this.drivingLicense = this.applyDetailObj.drivingLicense
						this.applyPic = res.data.applyPic
						for (let i = 0; i < this.vehicleFile.length; i++) {
							for (let key in this.applyPic) {
								if (this.vehicleFile[i].key == key) {
									this.vehicleFile[i].base64img = this.applyPic[key];
								}
							}
						}
						console.log(this.vehicleFile)
						console.log(this.applyPic)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			viewProgress() {
				uni.navigateTo({
					url: '/pagesC/afterSaleBusiness/orderProgress/index?id=' + this.id
				})
			},
			_getDict(list, val) {
				if (val) {
					return list.filter(item => item.value == val)[0].label
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	$uploadWidth: 231rpx;
	$uploadHeight: 138rpx;


	.apply-after-sale {
		background: #F8F8F8;
		margin-bottom: 180rpx;
		position: relative;
		height: 100%;

		.apply-after-sale-content {
			height: calc(100% - 90rpx);
			overflow-y: scroll;

			.section {
				margin: 20rpx;
			}
		}

		// .weui-top-card {
		// 	width: 100%;
		// 	height: 68rpx;
		// 	line-height: 68rpx;
		// 	border-radius: 12rpx;
		// 	background: #fff;

		// 	.weui-card-bd {
		// 		display: flex;
		// 		width: 100%;
		// 		padding: 0 26rpx;
		// 		height: 100%;
		// 	}

		// 	.line {
		// 		width: 1rpx;
		// 		height: 31rpx;
		// 		border: 1rpx solid #C6C6C6;
		// 		margin: 17rpx 47rpx;
		// 	}

		// .list-item {
		// 	display: flex;

		// 	.list-item_label {
		// 		width: auto;
		// 		font-size: 30rpx;
		// 		font-weight: 400;
		// 		color: #999999;
		// 	}

		// 	.list-item_value {
		// 		width: auto;
		// 		font-size: 30rpx;
		// 		font-weight: 400;
		// 		color: #323435;
		// 	}

		// 	.copy {
		// 		width: auto;
		// 		font-size: 30rpx;
		// 		font-weight: 400;
		// 		color: #3874FF;
		// 	}

		// 	.list-item-icon {
		// 		width: 40rpx;
		// 		height: 40rpx;
		// 		background-size: 100%;
		// 		margin: 12rpx 0 0 20rpx;
		// 	}
		// }

		// }

		.handle-card {
			background-color: #fff;

			.weui-card-hd {
				padding: 19rpx 30rpx 35rpx 30rpx;
				color: #333333;
				font-weight: 500;
				font-size: 32rpx;

				.weui-card-hd-wrapper {
					align-items: center;
					display: flex;

					.weui-card-hd-title {
						flex: 1;
					}

				}

			}

			.weui-card-bd {
				padding: 0 30rpx;

				.list {
					padding-bottom: 26rpx;

					.list-item {
						display: flex;
						margin-bottom: 27rpx;

						.list-item_label {
							width: 140rpx;
							line-height: 68rpx;
						}



						.list-item_value {
							width: calc(100% - 140rpx);
							height: 68rpx;
							border-radius: 8rpx;
							line-height: 68rpx;
							padding-left: 26rpx;


						}


						.list-item_value_2,
						.list-item_value_5 {
							background-color: rgba(0, 189, 50, 0.1);
							color: rgba(0, 189, 50, 1);
						}

						.list-item_value_3 {
							background-color: rgba(255, 84, 84, 0.1);
							color: rgba(255, 84, 84, 1);
						}

						.list-item_value_1,
						.list-item_value_4,
						list-item_value_6 {
							background-color: rgba(246, 246, 246, 1);
							color: #333333;
						}

					}

					.list-item-tips {
						width: auto;
						height: 33rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #999999;
						line-height: 33rpx;
					}
				}
			}

		}

		.list-item_attachment {
			width: calc(100% - 170rpx);
			overflow: hidden;
			margin-bottom: 4rpx;

			.upload-from {
				background-color: #FFFFFF;
				padding-bottom: 16rpx;
				margin-top: 16rpx;
				width: 100%;
				overflow: hidden;
			}

			.upload {
				width: 50%;
				float: left;
			}

			.upload-wrap {
				margin-bottom: 20rpx;
			}

			.upload-wrap .upload-wrap-desc {
				font-size: 26rpx;
				text-align: center;
				width: 100%;
				color: #333333;
				font-weight: 400;
				margin-top: 18rpx;
			}

			.upload-wrap .upload-wrap-bd {
				width: $uploadWidth;
				height: $uploadHeight;
				position: relative;
				background-size: 100%;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__img {
				width: $uploadWidth;
				height: $uploadHeight;
				background-size: 100%;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__close {
				position: absolute;
				right: 0;
				top: 0;
				padding: 0 10rpx;
				font-size: 36rpx;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
				// font-size: 36rpx;
				width: 28rpx;
				height: 28rpx;
				background-size: 100%;
			}



			.attachment_card {
				width: 50%;
				float: left;
				margin-bottom: 20rpx;

				.attachment_icon {
					width: 231rpx;
					height: 138rpx;
					background-size: 100%;
				}

				.attachment_name {
					width: 100%;
					height: 21rpx;
					font-size: 22rpx;
					font-weight: 400;
					color: #333333;
					line-height: 21rpx;
					margin-top: 18rpx;
					text-align: center;
				}
			}
		}

		.bottom-box {
			display: flex;

			.btn-item {
				flex: 1;
			}

			.btn-item-left {
				margin-right: 32rpx;
			}

			.btn-item-right {}
		}

		.steps-wrapp {
			background-color: #fff;
			margin-bottom: 20rpx;
			height: 100rpx;
		}

		.form-item {
			// border-radius: 10rpx;
			// margin: 20rpx;
		}

	}

	.u-textarea__count {
		position: absolute;
		right: 5px;
		bottom: 2px;
		font-size: 12px;
		color: #909193;
		background-color: #fff;
		padding: 1px 4px;
	}
</style>