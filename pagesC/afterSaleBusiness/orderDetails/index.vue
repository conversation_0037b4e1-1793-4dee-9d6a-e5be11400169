<template>
	<view class="apply-after-sale" v-if="finish">
		<orderNo :result="formData"></orderNo>
		<view class="apply-after-sale-content">
			<view class="section">
				<orderProgress v-if="Object.keys(formData).length > 0" @click="viewProgress" :result="formData">
				</orderProgress>
			</view>
			<view class="section">
				<vehicle :vehicleObj="formData"></vehicle>
			</view>
			<view class="section">
				<view class="weui-card form-item">
					<view class="weui-card-hd">
						<view class="weui-card-hd-wrapper">
							<view class="weui-card-hd-title">提交材料</view>
						</view>
					</view>
					<view class="weui-card-bd">
						<view class="list">
							<view class="list-item align-items-center">
								<view class="list-item_label">
									申请理由
								</view>
								<view class="list-item_value" v-if="!isEdit">
									{{formData.applyReasonTypeStr}}
								</view>
								<view class="list-item_value list-item_view" @click="show=true" v-if="isEdit">
									<view>{{formData.applyReasonTypeStr}}</view>
									<image src="../../static/ico_filter_down.png" mode="" class="down-icon"></image>
								</view>
							</view>
							<view class="list-item align-items-center">
								<view class="list-item_label">
									<span style="color:red" v-if="formData.applyReasonType=='2' && isEdit">*</span>补充说明
								</view>
								<view class="list-item_value" v-if="!isEdit">
									{{formData.applyReason || ''}}
								</view>
								<view class="list-item_value" v-if="isEdit">
									<u-input v-model="formData.applyReason" type="text" :border="true"
										:custom-style="inputStyle" :clearable="false" placeholder=" " />
								</view>
							</view>
							<view class="list-item">
								<view class="list-item_label">
									附件
								</view>
								<view class="list-item_attachment">
									<view class="upload-from">
										<view class="upload" v-for="(item,index) in vehicleFile" :key="index">
											<view class="upload-wrap">
												<view class="upload-wrap-bd" v-if="item.base64img !=''">
													<image :src="item.base64img" class="upload-wrap__img"
														mode='aspectFilt'>
													</image>
													<view class="upload-wrap__close"
														@tap.stop="delImgHandle(item,index)" v-if="isEdit">
														<!-- <text class='cuIcon-close close'></text> -->
														<image src="../../static/close.png" mode="" class="close">
														</image>
													</view>
												</view>
												<view v-if="!item.base64img" class="upload-wrap-bd"
													@tap="ChooseImage(item)">
													<image :src="item.url" class="upload-wrap__img"></image>
												</view>
												<view class="upload-wrap-desc">{{item.label}}</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!--  -->
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box" v-if="!isEdit">
				<!-- 审核不通过可以修改  -->
				<view class="btn-item btn-item-left" v-if="formData.orderStatus==103 || formData.orderStatus == '101'">
					<button class="weui-btn weui-btn_primary" @click="modifyInfo">
						修改信息
					</button>
				</view>
				<!-- 审核通过可以激活 -->
				<view class="btn-item btn-item-left" v-if="formData.orderStatus==102">
					<button class="weui-btn weui-btn_primary" @click="activate(formData)">
						前往激活
					</button>
				</view>
				<view class="btn-item btn-item-right"><button class="weui-btn weui-btn_quit" @click="cancelOrder">
						取消订单
					</button>
				</view>


			</view>
			<view class="weui-bottom-fixed__box bottom-box" v-if="isEdit">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="submit">
						提交
					</button>
				</view>
			</view>
		</view>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
		<u-select v-model="show" :list="applyReasonTypeList" @confirm="confirm">
		</u-select>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import cpimg from "@/components/uni-yasuo/cpimg.vue";
	import {
		getCurrUserInfo
	} from "@/common/storageUtil.js";
	import orderProgress from '../../components/order-progress/orderProgress.vue';
	import vehicle from '../../components/vehicle/vehicle.vue';
	import orderNo from '../../components/order-no/orderNo.vue'
	export default {
		components: {
			tLoading,
			cpimg,
			orderNo,
			orderProgress,
			vehicle
		},
		data() {
			return {
				finish: false,
				isLoading: false,
				ownFlag: 0,
				value: '',
				type: 'textarea',
				border: true,
				height: 100,
				applyPic: {
					drivingLicenseFrontUrl: '', //行驶证正页照片地址
					drivingLicenseSubpageUrl: '', //行驶证副页照片地址
					obuFailurePhotoUrl: '', //OBU失效照片
				},
				vehicleFile: [{
						photo_code: "3",
						ocr_type: 4,
						label: "行驶证正页",
						base64img: '',
						key: 'drivingLicenseFrontUrl',
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_front.png',
					},
					{
						photo_code: "12",
						ocr_type: 7,
						label: "行驶证副页",
						base64img: '',
						key: 'drivingLicenseSubpageUrl',
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_back.png',
					},
					{
						label: "OBU失效照片",
						base64img: '',
						key: 'obuFailurePhotoUrl',
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/bg_idcard_front.png',
					}
				],
				sourceType: '0',
				currentFile: {},
				inputStyle: {
					"marginLeft": "12rpx",
					"fontSize": "26rpx",
					"color": "#333333",
					"lineHeight": '60rpx',
					"height": "60rpx"
				},
				show: false,
				applyReasonTypeList: [{
						label: '标签掉落',
						value: '0',
					},
					{
						label: '插卡显示标签失效/标签拆卸',
						value: '1',
					},
					{
						label: '其他（可备注详细原因，字数上限100）',
						value: '2',
					}
				],
				isEdit: false,
				id: '',
				formData: {
					orderNo: '',
					orderStatus: '', //1-待审核,2-审核通过,3-审核不通过,4-已取消,5-已完结,6-已关闭
					drivingLicense: null,
					cardNo: '',
					obuNo: '',
					applyReason: '',
					applyReasonType: '',
					userType: '', // 客户类型
					vehicleType: '', // 车辆类型
					processNode: '', //提交申请-10，系统审核-20，人工审核-30，设备激活-40
					carNo: '', //车牌
				},
				applyPic: {
					code1: '',
					code2: '',
					code3: '',
					drivingLicenseFrontUrl: '', //行驶证正页照片地址
					drivingLicenseSubpageUrl: '', //行驶证副页照片地址
					obuFailurePhotoUrl: '', //OBU失效照片
				},
				drivingLicense: { // 行驶证ocr识别信息
					approvedCount: '', //核定载人数
					height: '', //高
					length: '', //长
					plateNum: '', //车牌号码
					totalMass: '', //总质量
					vehicleOwner: '', // 车辆所有人
					vehicleType: '', //车辆类型
					width: '', //
				},
			};
		},
		computed: {

		},
		watch: {

		},
		onLoad(options) {
			console.log(options)
			this.id = options.id
			this.applyDetail()
		},
		methods: {
			applyDetail() {
				let params = {
					id: this.id
				}

				this.isLoading = true
				console.log(params)
				this.$request.post(this.$interfaces.activateApplyDetail, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						this.finish = true
						this.formData = res.data
						this.drivingLicense = this.formData.drivingLicense
						this.applyPic = res.data.applyPic
						for (let i = 0; i < this.vehicleFile.length; i++) {
							for (let key in this.applyPic) {
								if (this.vehicleFile[i].key == key) {
									this.vehicleFile[i].base64img = this.applyPic[key];
								}
							}
						}
						console.log(this.vehicleFile)
						console.log(this.applyPic)
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			copy(data) {
				uni.setClipboardData({
					data: data,
					success: function() {
						uni.showToast({
							title: "复制成功"
						})
					}
				});
			},
			ChooseImage(data) {
				this.currentFile = data;
				this.$refs.cpimg._changImg(this.sourceType);
			},
			delImgHandle(item, index) {
				this.applyPic[item.key] = ''
				this.vehicleFile.filter((ele => ele.key == item.key))[0].base64img = ""
				if (item.key == 'drivingLicenseFrontUrl') {
					this.drivingLicense.plateNum = '';
					this.drivingLicense.vehicleOwner = '';
					this.drivingLicense.vehicleType = '';
				}
				if (item.key == 'drivingLicenseSubpageUrl') {
					this.drivingLicense.height = '';
					this.drivingLicense.length = '';
					this.drivingLicense.width = '';
					this.drivingLicense.totalMass = '';
					this.drivingLicense.approvedCount = '';
				}
			},
			////图片压缩成功
			cpimgOk(file) {
				// 根据ocr_type类型判断是否需要OCR识别
				if (this.currentFile.ocr_type) {
					this.sendOCRRequest(file);
				} else {
					this.sendUploadFile(file);
				}
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e);
			},
			// ocr识别
			sendOCRRequest(file) {
				let base64Img = file.toString()
				let biz_content = {
					ocr_type: this.currentFile.ocr_type,
					file_name: this.currentFile.label,
				}
				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						if (res.data.side == 1 && res.data.encryptedData) {
							this.drivingLicense.plateNum = res.data.encryptedData.plateNum || '';
							this.drivingLicense.vehicleOwner = res.data.encryptedData.name || '';
							this.drivingLicense.vehicleType = res.data.encryptedData.vehicleType || '';
						}
						if (res.data.side == 2 && res.data.encryptedData) {
							this.drivingLicense.height = res.data.encryptedData.height || '';
							this.drivingLicense.length = res.data.encryptedData.length || '';
							this.drivingLicense.width = res.data.encryptedData.width || '';
							this.drivingLicense.totalMass = res.data.encryptedData.totalMass || '';
							this.drivingLicense.approvedCount = res.data.encryptedData.approvedCount || '';
						}
						this.sendUploadFileScene(file);
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//2023/5/24 dwz根据后端要求修改
			sendUploadFile(file) {
				let params = {
					image: file.toString(),
					photoCode: this.currentFile.key || ''
				};
				this.$request.post(this.$interfaces.cacheImgUplaod, {
					data: params
				}).then(res => {
					if (res.code == 200 && res.data && res.data.data) {
						let result = res.data.data;
						let fileUrl = result.fileUrl || '';
						let photoCode = result.photoCode || '';
						this.applyPic[photoCode] = fileUrl;
						
						if (photoCode == 'obuFailurePhotoUrl') {
							this.applyPic.code3 = result.code
						}
						// for (let i = 0; i < this.vehicleFile.length; i++) {
						// 	if (this.vehicleFile[i].key == photoCode) {
						// 		this.vehicleFile[i].base64img = fileUrl;
						// 	}
						// }
						for (let i = 0; i < this.failureFile.length; i++) {
							if (this.failureFile[i].key == photoCode) {
								this.failureFile[i].base64img = fileUrl;
							}
						}
					}
					this.isLoading = false;
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
			
				})
			},
			//2023/5/24 dwz根据后端要求优化上传图片到档案中
			sendUploadFileScene(file) {
				let base64Img = file.toString()
				let biz_content = {
					photo_code: this.currentFile.photo_code,
					scene: 5,
					customer_id: this.formData.custMastId,
					vehicle_code: this.formData.carNo,
					vehicle_color: this.formData.carColor
			
				}
				let params = {
					file_content: base64Img,
					method_code: "2", // 档案上传
					biz_content: JSON.stringify(biz_content),
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.uploadFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						let fileUrl = result.file_url || '';
						// let photoCode = result.photoCode || '';
						this.applyPic[this.currentFile.key] = fileUrl;
						
						if (photoCode == 'drivingLicenseFrontUrl') {
							this.applyPic.code1 = result.code
						}
						if (photoCode == 'drivingLicenseSubpageUrl') {
							this.applyPic.code2 = result.code
						}
			
						for (let i = 0; i < this.vehicleFile.length; i++) {
							if (this.vehicleFile[i].photo_code == result.photo_code) {
								this.vehicleFile[i].base64img = fileUrl;
							}
						}
			
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
			
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			
			},
			
			confirm(options) {
				console.log(options)
				this.formData.applyReasonType = options[0].value
				this.formData.applyReasonTypeStr = options[0].label
			},
			modifyInfo() {
				this.isEdit = true
			},
			activate(item) {
				// 跳转激活页面

				let vehicleInfo = {}
				vehicleInfo.customerId = getCurrUserInfo().customer_id;
				vehicleInfo.vehicleCode = item.carNo;
				vehicleInfo.vehicleColor = item.carColor;
				vehicleInfo.businessSource = 2; //业务来源；1-线上发行，2-二次激活，3-设备更换，4-设备补办
				vehicleInfo.orderId = item.id
				vehicleInfo.cardNo = item.cardNo
				vehicleInfo.obuNo = item.obuNo
				this.$store.dispatch(
					'setIssueVehicleInfo',
					vehicleInfo
				)
				uni.navigateTo({
					url: '/pagesA/newBusiness/issue/issue-install'
				})
			},
			cancelOrder() {
				let params = {
					id: this.id,
				}
				this.isLoading = true
				console.log(params)
				this.$request.post(this.$interfaces.activateApplyCancel, {
					data: params
				}).then(res => {
					this.isLoading = false
					console.log(res.data)
					if (res.code == 200) {
						uni.navigateTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=7&id=" +
								this.id
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					this.isLoading = false
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			viewProgress() {
				uni.navigateTo({
					url: '/pagesC/afterSaleBusiness/orderProgress/index?id=' + this.id
				})
			},
			validateHandle() {
				let msg = ''
				if (this.formData.applyReasonType == 2 && !this.formData.applyReason) {
					msg = '补充说明未填写，请先填写！'
					return msg
				}

				for (let key in this.drivingLicense) {
					if (!this.drivingLicense[key]) {
						msg = '识别上传行驶证正副页数据不齐全，请重新上传行驶证照片！'
						break;
					}
				}
				if (msg) return msg;
				for (let i = 0; i < this.vehicleFile.length; i++) {
					if (!this.applyPic[this.vehicleFile[i].key]) {
						msg = '请上传' + this.vehicleFile[i].label
						break;
					}
				}
				if (msg) return msg;
				return ''
			},
			submit() {
				let msg = this.validateHandle();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					...this.formData,
					applyPic: this.applyPic,
					drivingLicense: this.drivingLicense
				}
				console.log(params)
				this.$request.post(this.$interfaces.activateApplyModify, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=0&id=" +
								this.id + '&orderStatus=' + this.formData.orderStatus
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			_getDict(list, val) {
				if (val) {
					return list.filter(item => item.value == val)[0].label
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	$uploadWidth: 231rpx;
	$uploadHeight: 138rpx;


	.apply-after-sale {
		background: #F8F8F8;
		margin-bottom: 180rpx;
		position: relative;
		height: 100%;

		.apply-after-sale-content {
			height: calc(100% - 240rpx);
			overflow-y: scroll;
		}

		.handle-card {
			background-color: #fff;

			.weui-card-hd {
				padding: 19rpx 30rpx 35rpx 30rpx;
				color: #333333;
				font-weight: 500;
				font-size: 32rpx;

				.weui-card-hd-wrapper {
					align-items: center;
					display: flex;

					.weui-card-hd-title {
						flex: 1;
					}
				}

			}

			.weui-card-bd {
				padding: 0 30rpx;

				.list {
					padding-bottom: 26rpx;

					.list-item {
						display: flex;
						margin-bottom: 27rpx;

						.list-item_label {
							width: 140rpx;
							line-height: 68rpx;
						}



						.list-item_value {
							width: calc(100% - 140rpx);
							height: 68rpx;
							border-radius: 8rpx;
							line-height: 68rpx;
							padding-left: 26rpx;


						}

					}

					.list-item-tips {
						width: auto;
						height: 33rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #999999;
						line-height: 33rpx;
					}
				}
			}

		}

		.list-item_attachment {
			width: calc(100% - 170rpx);
			overflow: hidden;
			margin-bottom: 20rpx;

			.upload-from {
				background-color: #FFFFFF;
				padding-bottom: 16rpx;
				margin-top: 16rpx;
				width: 100%;
				overflow: hidden;
			}

			.upload {
				width: 50%;
				float: left;
			}

			.upload-wrap {
				margin-bottom: 20rpx;
			}

			.upload-wrap .upload-wrap-desc {
				font-size: 26rpx;
				text-align: center;
				width: 100%;
				color: #333333;
				font-weight: 400;
				margin-top: 26rpx;
			}

			.upload-wrap .upload-wrap-bd {
				width: $uploadWidth;
				height: $uploadHeight;
				position: relative;
				background-size: 100%;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__img {
				width: $uploadWidth;
				height: $uploadHeight;
				background-size: 100%;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__close {
				position: absolute;
				right: 0;
				top: 0;
				padding: 0 10rpx;
				font-size: 36rpx;
			}

			.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
				// font-size: 36rpx;
				width: 28rpx;
				height: 28rpx;
				background-size: 100%;
			}



			.attachment_card {
				width: 50%;
				float: left;
				margin-bottom: 20rpx;

				.attachment_icon {
					width: 231rpx;
					height: 138rpx;
					background-size: 100%;
				}

				.attachment_name {
					width: 100%;
					height: 21rpx;
					font-size: 22rpx;
					font-weight: 400;
					color: #333333;
					line-height: 21rpx;
					margin-top: 18rpx;
					text-align: center;
				}
			}
		}

		.bottom-box {
			display: flex;

			.btn-item {
				flex: 1;
			}

			.btn-item-left {
				margin-right: 32rpx;
			}

			.btn-item-right {}
		}

		.steps-wrapp {
			background-color: #fff;
			margin-bottom: 20rpx;
			height: 100rpx;
		}

		.form-item {
			border-radius: 10rpx;
			// margin: 20rpx;
		}

	}

	.u-textarea__count {
		position: absolute;
		right: 5px;
		bottom: 2px;
		font-size: 12px;
		color: #909193;
		background-color: #fff;
		padding: 1px 4px;
	}
</style>