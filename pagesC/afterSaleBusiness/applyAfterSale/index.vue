<template>
	<view class="apply-after-sale">
		<view class="steps-wrapp">
			<handleStep :activeIndex='0'></handleStep>
		</view>
		<view class="section">
			<vehicle :vehicleObj="formData"></vehicle>
		</view>
		<view class="section">
			<view class="weui-card form-item">
				<view class="weui-card-hd">
					<view class="weui-card-hd-wrapper">
						<view class="weui-card-hd-title">请拍照上传行驶证正副页</view>
						<view class="weui-card-extra">
							<view class="desc" @click="preview()">
								查看拍照样例
							</view>
						</view>
					</view>
				</view>
				<view class="weui-card-bd">
					<view class="upload-from">
						<view class="upload" v-for="item in vehicleFile" :key='item.key'>
							<view class="upload-wrap">
								<view class="upload-wrap-bd" v-if="item.base64img !=''">
									<image :src="item.base64img" class="upload-wrap__img" mode='aspectFilt'></image>
									<view class="upload-wrap__close" @tap.stop="delImgHandle(item)">
										<image src="../../static/close.png" mode="" class="close"></image>
									</view>
								</view>
								<view v-if="!item.base64img" class="upload-wrap-bd" @tap="ChooseImage(item)">
									<image :src="item.url" class="upload-wrap__img"></image>
								</view>
								<view class="upload-wrap-desc">{{item.label}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>

		</view>
		<view class="section">
			<view class="weui-card form-item">
				<view class="weui-card-hd">
					<view class="weui-card-hd-wrapper">
						<view class="weui-card-hd-title">请上传设备失效照片</view>
						<view class="weui-card-extra">
							<view class="desc" @click="viewImg()">
								查看拍照样例
							</view>
						</view>
					</view>
				</view>
				<view class="weui-card-bd">
					<view class="upload-from">
						<view class="upload" v-for="item in failureFile" :key='item.key'>
							<view class="upload-wrap">
								<view class="upload-wrap-bd" v-if="item.base64img !=''">
									<image :src="item.base64img" class="upload-wrap__img" mode='aspectFilt'></image>
									<view class="upload-wrap__close" @tap.stop="delImgHandle(item)">
										<image src="../../static/close.png" mode="" class="close"></image>
									</view>
								</view>
								<view v-if="!item.base64img" class="upload-wrap-bd" @tap="ChooseImage(item)">
									<image :src="item.url" class="upload-wrap__img"></image>
								</view>
								<view class="upload-wrap-desc">{{item.label}}</view>
							</view>
						</view>
					</view>

				</view>
			</view>
		</view>
		<view class="section">
			<view class="weui-card form-item apply-reason">
				<view class="weui-card-hd">
					<view class="weui-card-hd-wrapper">
						<view class="weui-card-hd-title">申请理由</view>
						<view class="weui-card-extra">

						</view>
					</view>
				</view>
				<view class="weui-card-bd apply-reason_form">
					<view>
						<radio-group @change="radioChange" class="weui-checker-group">
							<view class="weui-checker" v-for="(item, index) in list" :key="item.value">
								<view class="weui-checker__icon">
									<radio style="transform:scale(0.5)" class="blue" :value="item.value"
										:checked="item.value === formData.applyReasonType" />
								</view>
								<view class="weui-checker__label">{{item.name}}</view>
							</view>
						</radio-group>
					</view>
					<view class="form-item textarea">
						<textarea v-model="formData.applyReason" maxlength='100'
							placeholder-style='font-size: 26rpx;font-weight: 400;color: #666666;'
							class="textarea__filed" placeholder="补充描述，有助于捷通处理设备问题"></textarea>
						<view class="textarea__count">{{formData.applyReason.length}}/100</view>
					</view>
				</view>
			</view>
		</view>
		<view class="section">
			<view class="weui-bottom-fixed">
				<view class="weui-bottom-fixed__box bottom-box">

					<view class="btn-item">
						<button class="weui-btn weui-btn_primary" @click="activateApplyHandle">
							提交
						</button>
					</view>

				</view>
			</view>
		</view>
		<!-- 图片上传组件 -->
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true"
			:size="500" :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<!-- 验证手机号码 -->
		<TModal :showModal='dialogVisible' @okModal='onSubmitHandle' @cancelModal="onCancelModal" modalTitle='身份验证'
			okText='提交'>
			<form slot='content' class="active-sms__dialog">
				<view class="desc">为了验证您的身份，我们将向手机{{encryptMobile(formData.mobile)}}发送短信验证码 </view>
				<view class="cu-form-group login-form-group">
					<input placeholder="请输入图形验证码" name="mobileCode" v-model='mobileCode'></input>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">
				</view>
				<view class="cu-form-group">
					<input placeholder="请输入验证码" v-model="formData.mobileCode" name="mobileCode"></input>
					<text class="sendSMS text-blue" @click="sendSMS">{{smsName}}</text>
				</view>
			</form>
		</TModal>
		<tLoading :isShow="isLoading" />
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!--  #endif -->
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import TModal from '@/components/t-modal/t-modal.vue'
	import handleStep from '../../components/handle-step/index.vue'
	import vehicle from '../../components/vehicle/vehicle.vue';
	import {
		getLoginUserInfo,
		getAccountId,
		getCurrUserInfo,
		getEtcAccountInfo,
		setOpenidForRead,
		getOpenidForRead
	} from '@/common/storageUtil.js'
	import {
		downLoadFile,
		previewFile
	} from '@/pagesA/common/method.js'
	export default {
		components: {
			tLoading,
			cpimg,
			TModal,
			handleStep,
			vehicle
		},
		data() {
			return {
				dialogVisible: false,
				time: null,
				smsName: '发送验证码',
				isLoading: false,
				ownFlag: 0,
				value: '',
				type: 'textarea',
				border: true,
				height: 100,
				autoHeight: true,
				vehicleFile: [{
						photo_code: "3",
						ocr_type: 4,
						label: "行驶证正页",
						base64img: '',
						key: 'drivingLicenseFrontUrl',
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_front.png',
					},
					{
						photo_code: "12",
						ocr_type: 7,
						label: "行驶证副页",
						base64img: '',
						key: 'drivingLicenseSubpageUrl',
						url: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_back.png',
					}
				],
				failureFile: [{
					label: "ETC设备失效照片",
					base64img: '',
					key: 'obuFailurePhotoUrl',
					url: '../../static/failure_pic.png',
				}],
				list: [{
						name: '标签掉落',
						value: '0',
					},
					{
						name: '插卡显示标签失效/标签拆卸',
						value: '1',
					},
					{
						name: '其他（需备注详细原因，字数上限100字）',
						value: '2',
					}
				],
				current: '1',
				sourceType: '0',
				currentFile: {},
				applyPic: {
					code1: '', //drivingLicenseFrontUrl
					code2: '', //drivingLicenseSubpageUrl
					code3: '', //obuFailurePhotoUrl
					drivingLicenseFrontUrl: '', //行驶证正页照片地址
					drivingLicenseSubpageUrl: '', //行驶证副页照片地址
					obuFailurePhotoUrl: '', //OBU失效照片
				},
				drivingLicense: { // 行驶证ocr识别信息	
					approvedCount: '', //核定载人数
					height: '', //高
					length: '', //长
					plateNum: '', //车牌号码
					totalMass: '', //总质量
					vehicleOwner: '', // 车辆所有人
					vehicleType: '', //车辆类型
					width: '', //
				},
				formData: {
					applyChannel: '2', // 申请渠道 0-八桂行APP，1-公众出行APP，2-微信小程序，3-微信公众号，4-Web网上营业厅
					applyPic: null, // 申请材料
					applyReason: '', //申请理由详细描述
					applyReasonType: '0', //申请理由
					carColor: '', //车牌颜色
					carNo: '', //车牌
					cardNo: '', //ETC卡号
					custMastId: '', //ETC用户ID
					drivingLicense: null, //行驶证ocr识别信息
					netUserNo: '', //互联网账户ID
					obuNo: '', //OBU号
					opBusinessType: '', // 操作业务类型 1-失效重新激活，2-遗失/损毁补办，3-设备故障更换，4-设备注销
					productType: '', // 产品类型
					userType: '', // 客户类型
					vehicleType: '', // 车辆类型
					mobileCode: '', // 短信验证码
					mobile: '', // 手机号码
				},
				mobileCode: '', //图形验证码
				codeUrl: '', // 图形验证码地址
				captchaId: '', // 图形验证码code
			};
		},
		computed: {

		},
		watch: {
			dialogVisible(val) {
				console.log(val)
				// if (val) {
				clearInterval(this.time)
				// this.time = null
				// this.smsName = '重新发送'
				this.time = null;
				this.smsName = '发送验证码';
				this.formData.mobileCode = '';
				this.mobileCode = '';
				this.captchaId = '';
				this.getCaptcha();
				// }
			}
		},
		onLoad(options) {
			// url 参数 vehicleInfo 和 opBusinessType
			let vehicleInfo = this.$store.getters['afterSale/afterSaleVehicle'] || {};
			if (Object.keys(vehicleInfo).length) {
				this.formData.mobile = getEtcAccountInfo().mobile;
				this.formData.opBusinessType = options.opBusinessType || '';
				this.formData.carColor = vehicleInfo.vehicleColor || '';
				this.formData.carNo = vehicleInfo.vehicleCode || '';
				this.formData.cardNo = vehicleInfo.cardNo || '';
				this.formData.obuNo = vehicleInfo.obuNo || '';
				this.formData.productType = vehicleInfo.cardProduct || '';
				this.formData.vehicleType = vehicleInfo.vehicleType || ''
			}

		},
		created() {
			this.formData.netUserNo = getLoginUserInfo().userNo
			this.formData.custMastId = getCurrUserInfo().customer_id
			this.formData.userType = getCurrUserInfo().customer_type
			this.formData.userType = getCurrUserInfo().customer_type
		},
		methods: {
			encryptMobile(data) {
				if (!data) return ''
				return data.replace(/^(\d{3})\d+(\d{3})$/, '$1*****$2')
			},
			getCaptcha() {
				let params = {}
				this.$request
					.post(this.$interfaces.getCaptcha, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.codeUrl = res.data.image
							this.captchaId = res.data.captchaId
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					})
					.catch((error) => {})
			},
			// 选中某个单选框时，由radio时触发
			radioChange(e) {
				this.formData.applyReasonType = e.detail.value;
			},
			ChooseImage(data) {
				this.currentFile = data;
				this.$refs.cpimg._changImg(this.sourceType);
			},
			delImgHandle(item) {
				// 根据item.key 决定删除片内容
				this.applyPic[item.key] = '';
				for (let i = 0; i < this.vehicleFile.length; i++) {
					if (this.vehicleFile[i].key == item.key) {
						this.vehicleFile[i].base64img = '';
					}
				}
				for (let i = 0; i < this.failureFile.length; i++) {
					if (this.failureFile[i].key == item.key) {
						this.failureFile[i].base64img = '';
					}
				}
				if (item.key == 'drivingLicenseFrontUrl') {
					this.drivingLicense.plateNum = '';
					this.drivingLicense.vehicleOwner = '';
					this.drivingLicense.vehicleType = '';
				}
				if (item.key == 'drivingLicenseSubpageUrl') {
					this.drivingLicense.height = '';
					this.drivingLicense.length = '';
					this.drivingLicense.width = '';
					this.drivingLicense.totalMass = '';
					this.drivingLicense.approvedCount = '';
				}
			},
			////图片压缩成功
			cpimgOk(file) {
				// 根据ocr_type类型判断是否需要OCR识别
				if (this.currentFile.ocr_type) {
					this.sendOCRRequest(file);
				} else {
					this.sendUploadFile(file);
				}
			},
			//图片压缩失败
			cpimgErr(e) {

			},
			validateHandle() {
				let msg = ''
				if (this.formData.applyReasonType == 2 && !this.formData.applyReason) {
					msg = '申请理由未填写，请先填写！'
					return msg
				}

				for (let i = 0; i < this.failureFile.length; i++) {
					if (!this.applyPic[this.failureFile[i].key]) {
						msg = '设备失效照片未上传，请先上传！'
						break;
					}
				}
				for (let key in this.drivingLicense) {
					if (!this.drivingLicense[key]) {
						msg = '行驶证OCR信息不齐全，请重新上传材料或联系客服！'
						break;
					}
				}
				for (let i = 0; i < this.vehicleFile.length; i++) {
					if (!this.applyPic[this.vehicleFile[i].key]) {
						msg = '行驶证材料未上传，请先上传！'
						break;
					}
				}
				if (msg) return msg;
				return ''
			},
			//发送短信
			sendSMS() {
				if (!this.mobileCode) {
					uni.showModal({
						title: '提示',
						content: '请输入图形验证码',
						showCancel: false
					})
					return
				}
				if (!this.time) {
					this.isLoading = true
					let countdown = 60
					let params = {
						mobile: this.formData.mobile,
						mobileCode: this.mobileCode,
						captchaId: this.captchaId,
					}
					this.$request
						.post(this.$interfaces.sendaAccountSms, {
							data: params
						})
						.then((res) => {
							this.isLoading = false
							if (res.code == '200') {
								this.time = setInterval(() => {
									countdown = countdown - 1
									this.smsName = countdown + '秒后重新发送'
									if (countdown === 0) {
										clearInterval(this.time)
										this.time = null
										this.smsName = '重新发送'
									}
								}, 1000)
							} else {
								this.getCaptcha()
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false,
								})
							}
						})
						.catch((error) => {
							this.getCaptcha()
							uni.showModal({
								title: '提示',
								content: error.msg,
								showCancel: false
							})
						})
				}
			},
			onCancelModal() {
				// clearInterval(this.time)
				// this.time = null
				// this.smsName = '重新发送'
				this.dialogVisible = false
			},
			onSubmitHandle() {
				if (!this.mobileCode) {
					uni.showModal({
						title: '提示',
						content: '请输入图形验证码',
						showCancel: false
					})
					return
				}
				if (!this.formData.mobileCode) {
					uni.showModal({
						title: '提示',
						content: '请输入短信验证码',
						showCancel: false
					})
					return
				}
				this.dialogVisible = false;
				this.sendActivateApply();

			},
			activateApplyHandle() {
				let msg = this.validateHandle();
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return;
				}
				this.dialogVisible = true

			},
			showDialog() {
				this.dialogVisible = true;
			},
			// 发送售后申请
			sendActivateApply() {
				if (this.isLoading) return;
				this.isLoading = true;
				let params = {
					...this.formData,
					applyPic: this.applyPic,
					drivingLicense: this.drivingLicense
				}
				this.$request.post(this.$interfaces.activateApply, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						// 提交之前先订阅
						this.$subscriptionMethod()
						uni.redirectTo({
							url: "/pagesC/afterSaleBusiness/promptPage/index?type=0&orderStatus=" + res
								.data.orderStatus + "&id=" +
								res.data.id
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},
			// ocr识别
			sendOCRRequest(file) {
				let base64Img = file.toString()
				let biz_content = {
					ocr_type: this.currentFile.ocr_type,
					file_name: this.currentFile.label,
				}
				let params = {
					file_content: file.toString(),
					method_code: '1',
					biz_content: JSON.stringify(biz_content)
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.ocrFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						if (res.data.side == 1 && res.data.encryptedData) {
							this.drivingLicense.plateNum = res.data.encryptedData.plateNum || '';
							this.drivingLicense.vehicleOwner = res.data.encryptedData.name || '';
							this.drivingLicense.vehicleType = res.data.encryptedData.vehicleType || '';
						}
						if (res.data.side == 2 && res.data.encryptedData) {
							this.drivingLicense.height = res.data.encryptedData.height || '';
							this.drivingLicense.length = res.data.encryptedData.length || '';
							this.drivingLicense.width = res.data.encryptedData.width || '';
							this.drivingLicense.totalMass = res.data.encryptedData.totalMass || '0';
							this.drivingLicense.approvedCount = res.data.encryptedData.approvedCount || '';
						}
						if (res.data.encryptedData.plateNum != this.formData.carNo) {
							uni.showModal({
								title: "提示",
								content: '行驶证识别车牌号码与申请车辆车牌号码不一致，请按提示上传清晰的有效图片',
								showCancel: false,
							});
							return
						}
						this.sendUploadFileScene(file);
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})
			},
			//2023/5/24 dwz根据后端要求修改
			sendUploadFile(file) {
				let params = {
					image: file.toString(),
					photoCode: this.currentFile.key || ''
				};
				this.$request.post(this.$interfaces.cacheImgUplaod, {
					data: params
				}).then(res => {
					if (res.code == 200 && res.data && res.data.data) {
						let result = res.data.data;
						let fileUrl = result.fileUrl || '';
						let photoCode = result.photoCode || '';
						this.applyPic[photoCode] = fileUrl;
						console.log(result,'result')
						if (photoCode == 'obuFailurePhotoUrl') {
							this.applyPic.code3 = result.code
						}
						// for (let i = 0; i < this.vehicleFile.length; i++) {
						// 	if (this.vehicleFile[i].key == photoCode) {
						// 		this.vehicleFile[i].base64img = fileUrl;
						// 	}
						// }
						for (let i = 0; i < this.failureFile.length; i++) {
							if (this.failureFile[i].key == photoCode) {
								this.failureFile[i].base64img = fileUrl;
							}
						}
					}
					this.isLoading = false;
				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});

				})
			},
			//2023/5/24 dwz根据后端要求优化上传图片到档案中
			sendUploadFileScene(file) {
				let base64Img = file.toString()
				let biz_content = {
					photo_code: this.currentFile.photo_code,
					scene: 5,
					customer_id: this.formData.custMastId,
					vehicle_code: this.formData.carNo,
					vehicle_color: this.formData.carColor

				}
				let params = {
					file_content: base64Img,
					method_code: "2", // 档案上传
					biz_content: JSON.stringify(biz_content),
				};
				this.isLoading = true;
				this.$request.post(this.$interfaces.uploadFile, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						let result = res.data
						let fileUrl = result.file_url || '';
						let photoCode = result.photo_code || '';
						this.applyPic[this.currentFile.key] = fileUrl;
						console.log(result,'result')
						if (photoCode == '3') {  // 以前是 drivingLicenseFrontUrl
							this.applyPic.code1 = result.code
						}
						if (photoCode == '12') {  // 以前是 drivingLicenseSubpageUrl
							this.applyPic.code2 = result.code
						}

						for (let i = 0; i < this.vehicleFile.length; i++) {
							if (this.vehicleFile[i].photo_code == result.photo_code) {
								this.vehicleFile[i].base64img = fileUrl;
							}
						}

					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

				}).catch(error => {
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
					this.isLoading = false;
				})

			},

			preview() {
				let url =
					'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/common/driving-license-sample.png';
				previewFile(url)
			},
			viewImg() {
				let url = 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/common/obu-los-sample.png';
				previewFile(url)
			},
		},
	};
</script>

<style lang="scss" scoped>
	$uploadWidth: 316rpx;
	$uploadHeight: 190rpx;


	.upload-from {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		background-color: #FFFFFF;
		padding-bottom: 16rpx;
		margin-top: 16rpx;
	}

	.upload {
		width: $uploadWidth;
	}

	.upload-wrap {
		margin-bottom: 20rpx;
	}

	.upload-wrap .upload-wrap-desc {
		font-size: 26rpx;
		text-align: center;
		width: 100%;
		color: #333333;
		font-weight: 400;
		margin-top: 26rpx;
	}

	.upload-wrap .upload-wrap-bd {
		width: $uploadWidth;
		height: $uploadHeight;
		position: relative;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__img {
		width: $uploadWidth;
		height: $uploadHeight;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__close {
		position: absolute;
		right: 0;
		top: 0;
		padding: 0 10rpx;
		font-size: 36rpx;
	}

	.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
		width: 30rpx;
		height: 30rpx;
		background-size: 100%;
	}

	.apply-after-sale {
		background: #F8F8F8;
		padding-bottom: 180rpx;
		position: relative;

		.bottom-box {
			display: flex;
		}

		.bottom-box .btn-item {
			flex: 1;
		}

		.steps-wrapp {
			background-color: #fff;
			margin-bottom: 20rpx;
			padding-top: 20rpx;
			height: 150rpx;
		}

		.form-item {
			// border-radius: 12rpx;
			// margin: 20rpx;
		}

		.weui-card {
			// background-color: #fff;

			// .weui-card-hd {
			// 	display: flex;
			// 	justify-content: center;
			// 	flex-direction: column;
			// 	margin-bottom: -1px;
			// 	padding: 16rpx 30rpx 20rpx 30rpx;
			// 	color: #333333;
			// 	font-weight: 500;
			// 	font-size: 32rpx;

			// 	.weui-card-hd-wrapper {
			// 		width: 100%;
			// 		display: flex;
			// 		align-items: center;
			// 	}

			// 	.weui-card-hd-title {
			// 		flex: 1;
			// 		overflow: hidden;
			// 		white-space: nowrap;
			// 		text-overflow: ellipsis;
			// 	}

			// 	.weui-card-extra {
			// 		.desc {
			// 			font-size: 24rpx;
			// 			font-weight: 400;
			// 			color: #0081FF;
			// 		}
			// 	}
			// }

			// .weui-card-bd {
			// 	padding: 0 30rpx;
			// }

		}

		// .vehicle-info {
		// 	padding-bottom: 46rpx;
		// }

		.list {
			.list-item {
				display: flex;
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.list-item_label {
					font-size: 26rpx;
					width: 150rpx;
					margin-right: 10rpx;
					font-weight: 400;
					color: #999999;
				}

				.list-item_value {
					font-size: 26rpx;
					font-weight: 400;
					color: #333333;
				}
			}
		}
	}

	.apply-reason {
		padding-bottom: 30rpx;

		.apply-reason_form {
			.form-item.textarea {
				height: 100%;
				position: relative;
			}

			.textarea {
				border-radius: 10rpx;
				min-height: 60rpx;
				background: #F3F3F3;
				position: relative;
				display: flex;
				flex-direction: row;
				flex: 1;
				padding: 20rpx 24rpx 40rpx 24rpx;

			}

			.textarea__filed {
				flex: 1;
				height: 140rpx;
				font-size: 30rpx;
				color: #606266;
				width: 100%;
			}

			.textarea__count {
				position: absolute;
				right: 10rpx;
				bottom: 4rpx;
				font-size: 24rpx;
				background-color: #F3F3F3;
				padding: 4rpx 12rpx;
				font-weight: 400;
				color: #666666;
			}
		}
	}

	.u-textarea__count {
		position: absolute;
		right: 5px;
		bottom: 2px;
		font-size: 12px;
		color: #909193;
		background-color: #fff;
		padding: 1px 4px;
	}

	.weui-checker-group {
		display: flex;
		flex-wrap: wrap;

		.weui-checker {
			display: flex;
			height: 40rpx;
			box-align: center;
			align-items: center;
			margin-bottom: 28rpx;
			margin-right: 20rpx;

			.weui-checker__icon {
				width: 40rpx;
				height: 40rpx;
				display: flex;
				box-align: center;
				align-items: center;
				justify-content: center;

			}

			.weui-checker__label {
				font-weight: 400;
				color: #323435;
				font-size: 30rpx;
			}
		}

	}

	.active-sms__dialog {
		.login-form-group .code-img {
			width: 240upx;
			height: 90upx;
			margin-left: 20upx;
		}

		.sendSMS {
			padding: 10rpx;
		}

		.sendSMS:active {
			background: #ddd;
		}

		.desc {
			font-size: 30rpx;
			text-align: left;
			font-weight: 400;
			color: #858686;
			background: #ffffff;
			padding: 10upx 25upx;
		}

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
		}
	}

	.active-sms__dialog .cu-form-group .title {
		font-size: 32upx;
	}

	.active-sms__dialog .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.active-sms__dialog .cu-form-group input {
		text-align: left;
	}

	.active-sms__dialog .cu-form-group radio-group {
		flex: 1;
		text-align: left;
	}
</style>