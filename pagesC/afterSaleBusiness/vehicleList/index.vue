<template>
	<view class="vehicle-list">
		<block v-if="isNoVehicle && activateApplyVehicleList.length == 0">
			<view class="no-vehicle">
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/addcar_nocar.png" mode="aspectFilt" class="no-vehicle_icon">
				</image>
				<view class="no-vehicle_des">
					暂无车辆
				</view>
			</view>
		</block>
		<block v-else>
			<!-- 待办理的车辆 -->
			<view class="vehicle-list_wrapper" v-if="!isNoVehicle">
				<view class="wrapper_hd g-flex g-flex-justify">
					<view class="title">
						请选择您要办理的车辆
					</view>
					<!-- <image src="../../static/help-icon.png" mode="aspectFilt" class="icon">
					</image> -->
				</view>
				<view class="weui-media" v-for='(item,index) in vehicleAllDataList' :key='index'
					@click="vehicleItemHandle(item)">
					<view class="weui-media-hd">
						<image v-if="item.vehicleType =='2'" src="../../static/passenger-car_icon.png" mode="aspectFilt"
							class="weui-media-hd_icon">
						</image>
						<image v-else src="../../static/trucks_icon.png" mode="aspectFilt" class="weui-media-hd_icon">
						</image>
					</view>
					<view class="weui-media-bd">
						<view class="title">{{item.vehicleCode}}【{{getVehicleColor(item.vehicleColor)}}】</view>
						<view class="value">{{getVehicleType(item.vehicleType)}}车 {{gxCardTypeFilter(item.cardProduct)}}
						</view>
					</view>
					<view class="weui-media-ft">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/right.png" mode="aspectFilt" class="weui-media-ft_icon"></image>
					</view>
				</view>
			</view>
			<!-- 办理中的车辆 -->
			<view class="vehicle-list_wrapper" v-if="activateApplyVehicleList && activateApplyVehicleList.length">
				<view class="wrapper_hd g-flex g-flex-justify">
					<view class="title">
						进行中的售后订单
					</view>
				</view>

				<view class="weui-media" v-for='(item,index) in activateApplyVehicleList'
					@click="goActivateApplyDeatil(item)" :key='index'>
					<view class="weui-media-hd">
						<image v-if="true" src="../../static/passenger-car_icon.png" mode="aspectFilt"
							class="weui-media-hd_icon">
						</image>
						<image v-if="false" src="../../static/trucks_icon.png" mode="aspectFilt"
							class="weui-media-hd_icon">
						</image>
					</view>
					<view class="weui-media-bd">
						<view class="title">{{item.carNo}}【{{getVehicleColor(item.carColor)}}】</view>
						<view class="tag">{{item.orderTypeStr}}</view>
					</view>
					<view class="weui-media-ft">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/right.png" mode="aspectFilt" class="weui-media-ft_icon"></image>
					</view>
				</view>
			</view>
		</block>



		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		gxCardTypeFilter
	} from '@/common/method/filter.js'
	import {
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getCurrUserInfo,
		setCurrUserInfo,
		getAccountId,
		setBusinessTypes,
		setStore,
		getStore,
		setEtcVehicle,
		getEtcAccountInfo,
		getLoginUserInfo
	} from '@/common/storageUtil.js'
	import TButton from '@/components/t-button.vue'
	import tLoading from '@/components/common/t-loading.vue'
	export default {
		data() {
			return {
				isNoVehicle: false,
				isLoading: false,
				formData: {},
				bindTicket: '',
				businessType: '',
				btnTitle: '',
				vehicleAllDataList: [],
				activateApplyVehicleList: [],
			}
		},
		components: {
			tLoading,
			TButton
		},
		computed: {


		},
		onShow(obj) {
			if (getAccountId()) {
				this.getAllVerhicleList();
				this.activateApplyVehicle();
			}
		},
		methods: {
			getVehicleColor,
			getVehicleClassType,
			getVehicleType,
			gxCardTypeFilter,
			getAllVerhicleList() {
				let params = {
					customerId: getAccountId()
				}
				this.$request
					.post(this.$interfaces.vehicleList, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.vehicleAllDataList = res.data || []
							this.isNoVehicle = !this.vehicleAllDataList.length;
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					}).catch(err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						})
					})
			},
			// 查询办理中的车辆
			activateApplyVehicle() {
				let params = {
					custMastId: getEtcAccountInfo().custMastId,
					netUserNo: getLoginUserInfo().userNo,
					page: 1,
					pageSize: 999
				}
				this.$request
					.post(this.$interfaces.activateApplyVehicle, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.activateApplyVehicleList = res.data.data || [];
							console.log('-----------', this.activateApplyVehicleList)
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					}).catch(err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						})
					})
			},
			vehicleItemHandle(item) {
				console.log(item);
				let vehicleCode = item.vehicleCode;
				let currentItem = this.activateApplyVehicleList.filter(item => {
					return item.carNo == vehicleCode
				})
				let vehicleItem = currentItem.length ? currentItem : this.activateApplyVehicleList;
				if (this.activateApplyVehicleList.length) {
					uni.showModal({
						title: '提示',
						content: '您有进行中的售后订单，请先前往处理',
						confirmText: '前往',
						showCancel: false,
						success(action) {
							if (action.confirm) {
								if (vehicleItem[0].orderType == '1') {
									uni.navigateTo({
										url: '/pagesC/afterSaleBusiness/orderDetails/index?id=' +
											vehicleItem[0].id
									})
								} else if (vehicleItem[0].orderType == '2') {
									uni.navigateTo({
										url: '/pagesC/reissueBussiness/orderDetails/index?orderId=' +
											vehicleItem[0].orderNo
									})
								} else if (vehicleItem[0].orderType == '3') {
									if (vehicleItem[0].orderStatus == '201' || vehicleItem[0].orderStatus ==
										'203') { //待审核、审核不通过
										uni.navigateTo({
											url: '/pagesC/changeBussiness/orderDetails/failPassIndex?orderId=' +
												vehicleItem[0].orderNo
										})
										return
									} else {
										uni.navigateTo({
											url: '/pagesC/changeBussiness/orderDetails/index?orderId=' +
												vehicleItem[0].orderNo
										})
									}
								} else if (vehicleItem[0].orderType == '4') { //注销
									uni.navigateTo({
										url: '/pagesC/logoutBussiness/orderDetail/orderDetail?cardNo=' +
											vehicleItem[0].cardNo + '&orderId=' + vehicleItem[0].id
									})
								} else if (vehicleItem[0].orderType == '5') {
									uni.navigateTo({
										url: '/pagesC/productConver/orderDetail?orderId=' +
											vehicleItem[0].id
									})
								}

							}
						}
					})
					return
				}
				this.$store.dispatch(
					'afterSale/setAfterSaleVehicle', item
				)
				uni.navigateTo({
					url: '/pagesC/afterSaleBusiness/home/<USER>'
				})
			},
			goActivateApplyDeatil(item) {
				console.log('item', item)
				if (item.orderType == '1') {
					if (item.orderStatus == '101' || item.orderStatus == '102' || item.orderStatus == '103') {
						uni.navigateTo({
							url: '/pagesC/afterSaleBusiness/orderDetails/index?id=' + item.id
						})
					} else {
						uni.navigateTo({
							url: '/pagesC/afterSaleBusiness/orderDetails/viewDetail?id=' + item.id
						})
					}
				} else if (item.orderType == '2') { //补办
					uni.navigateTo({
						url: '/pagesC/reissueBussiness/orderDetails/index?orderId=' + item.orderNo
					})
				} else if (item.orderType == '3') { //更换
					if (item.orderStatus == '201' || item.orderStatus == '203') { //待审核、审核不通过
						uni.navigateTo({
							url: '/pagesC/changeBussiness/orderDetails/failPassIndex?orderId=' + item.orderNo
						})
						return
					} else {
						uni.navigateTo({
							url: '/pagesC/changeBussiness/orderDetails/index?orderId=' + item.orderNo
						})
					}
				} else if (item.orderType == '4') { //注销
					uni.navigateTo({
						url: '/pagesC/logoutBussiness/orderDetail/orderDetail?cardNo=' + item.cardNo
					})
				} else if (item.orderType == '5') { //产品转换
					uni.navigateTo({
						url: '/pagesC/productConver/orderDetail?orderId=' + item.id
					})
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.vehicle-list {
		width: 100%;
		height: 100%;

		background: #F9F9F9;
	}

	.vehicle-list .vehicle-list_wrapper {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding-bottom: 30rpx;

		.wrapper_hd {
			padding: 30rpx;

			.title {
				font-weight: 500;
				color: #323435;
				font-size: 30rpx;
			}

			.icon {
				display: block;
				width: 40rpx;
				height: 40rpx;
			}
		}
	}

	.vehicle-list .weui-btn_wrapper {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
	}

	.no-vehicle {
		padding-top: 320rpx;
		width: 100%;
		background-color: #f9f9f9;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 29rpx;
		color: #333333;
		font-weight: 400;
		text-align: center;
		margin-top: 60rpx;
	}

	.weui-media {
		display: flex;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
		background: #ffffff;
		margin: 0rpx 30rpx 20rpx 30rpx;
		padding: 22rpx 60rpx 22rpx 34rpx;
		box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.06);
		border-radius: 16rpx;
		border: 2rpx solid #ECECEC;

		&:last-child {
			margin-bottom: 0;
		}
	}


	.weui-media .weui-media-hd {
		width: 120rpx;
		display: flex;
	}

	.weui-media .weui-media-hd .weui-media-hd_icon {
		display: block;
		width: 110rpx;
		height: 110rpx;
	}

	.weui-media .weui-media-bd {
		flex: 1;
		margin-left: 30rpx;
	}

	.weui-media .weui-media-bd .title {
		font-weight: 600;
		color: #333333;
		font-size: 34rpx;
	}

	.weui-media .weui-media-bd .value {
		font-size: 30rpx;
		font-weight: 400;
		color: #676767;
		margin-top: 22rpx;
	}

	.weui-media .weui-media-bd .tag {
		background: rgba(255, 145, 0, 0.1);
		border-radius: 8rpx;
		font-weight: 400;
		color: #FF9100;
		height: 48rpx;
		width: 170rpx;
		text-align: center;
		line-height: 48rpx;
		font-size: 26rpx;
		margin-top: 16rpx;
	}

	.weui-media .weui-media-ft {
		min-width: 30rpx;
	}

	.weui-media .weui-media-ft .weui-media-ft_icon {
		width: 15rpx;
		height: 26rpx;
		display: block;
	}
</style>