<template>
	<view class="coupons-list">
		<view class="no-vehicle" v-if="isNoList">
			<image src="../../../static/toc/no-data.png" mode="aspectFilt" class="no-vehicle_icon"></image>
			<view class="no-vehicle_des">敬请期待</view>
		</view>
		<view class="coupons-item" v-for="(item, index) in cuoponList" :key="index">
			<view class="hd">
				<view class="hd-title" v-if="item.type == '3'">
					<text class="symbol_unit">+</text>
					<text class="price">5%</text>
				</view>
				<view class="hd-des" v-if="item.type == '3'">{{ item.coupon_name }}</view>
			</view>
			<view class="bd" :style="item.type == '1'?'padding-left:0':''">
				<view class="bd-title" v-if="item.type == '1'">{{ item.coupon_name }}</view>
				<view class="bd-title" v-else>最高得{{ item.max_amount / 100 }}元</view>
				<view class="bd-des" v-if="item.is_valid == '0' && item.is_used == '0'">有效期至{{item.expire_date}}</view>
				<view class="bd-des" v-if="item.is_valid == '1' || item.is_used == '1'">已失效</view>
				<view class="bd-des" @click="goRules(item.type)">
					使用规则
					<image style="width: 30rpx;height: 30rpx;margin-left: 5rpx;"
						src="../../../static/toc/arrow-right.png" mode="aspectFilt"></image>
				</view>
			</view>
			<view class="ft">
				<view class="ft-btn" v-if="item.is_valid == '0' && item.is_used == '0'" @click="goToUse(item.type)">
					领取并使用</view>
			</view>
			<view class="tag">
				<image v-if="item.is_valid == 1" class="tag-img" mode="aspectFilt"
					src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/coupon_expired_mark.png"></image>
				<image v-if="item.is_used == 1" class="tag-img" mode="aspectFilt"
					src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/coupon_used_mark.png"></image>
			</view>
		</view>
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:confirm-text="modal.confirmText" @confirm="onConfirmHandle">
			<view class="maintaining-content-wrapper">
				<view class="title">温馨提示</view>
				<view class="msg_desc">
					<view class="msg_desc-honorific">
						尊敬的ETC用户：
					</view>
					<view class="msg_desc-content">
						因捷通E购正在全面优化升级，需暂停发放您2023年1月份的商城权益优惠券，预计2023年2月中下旬开始顺延补发。如有不便，敬请谅解，谢谢!
					</view>
				</view>
			</view>
		</neil-modal>
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getCurrUserInfo,
		getAccountId,
		getCurrentCar,
		getOpenid,
		getEtcVehicle
	} from '@/common/storageUtil.js';
	import {
		getCoupons
	} from '@/common/method/filter.js';
	import {
		onlineStoreFn
	} from '@/components/base/redirect/index.js';
	import neilModal from '@/components/neil-modal/neil-modal.vue'
	export default {
		data() {
			return {
				cuoponList: [],
				isNoList: false,
				code: '',
				isLoading: false,
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					confirmText: '确认'
				}

			};
		},
		components: {
			neilModal
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {};
			}
		},
		created() {
			this.getCouponList();
		},
		methods: {
			//跳转到兑换码详情界面
			toDetail() {

			},
			//跳转到规则界面
			goRules(type) {
				uni.navigateTo({
					url: '/pagesC/coupons/couponsRules/couponsRules?type=' + type
				});
			},
			coupons(val) {
				return getCoupons(val) || '';
			},

			//获取优惠券列表
			getCouponList() {
				this.isLoading = true;
				let params = {
					customer_id: this.customerInfo.customer_id
				};
				this.$request
					.post(this.$interfaces.getCouponList, {
						data: params
					})
					.then(res => {
						if (res.code == 200) {
							this.isLoading = false;
							this.cuoponList = res.data;
							this.isNoList = this.cuoponList.length == 0;
							this.filterList();
						} else {
							this.isLoading = false;
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(err => {
						this.isLoading = false;
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						});
					});
			},
			goToUse(type) {
				if (type == '1') {
					// this.modal.show = true;
					onlineStoreFn('jumpUrl');
				} else if (type == '3') {
					this.toCharge();
				}
			},
			//获取跳转参数
			getJumpUrl() {
				let _self = this;
				let params = {
					customer_id: this.customerInfo.customer_id
				};
				this.$request
					.post(this.$interfaces.getJumpUrl, {
						data: params
					})
					.then(res => {
						if (res.code == 200) {
							_self.toFenMiShop(res.data);
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						});
					});
			},
			//跳转到分米商城小程序
			toFenMiShop(params) {
				console.log(JSON.stringify(params));
				uni.navigateToMiniProgram({
					appId: 'wxe6e2becf3f4d37d7',
					path: '/pagesA/shop/coupon',
					envVersion: 'release', //正式版
					extraData: params,
					success(res) {
						// 打开成功
					}
				});
			},
			toCharge(item) {
				//跳转到卡账充值，
				uni.navigateTo({
					url: '/pagesB/rechargeBusiness/selectVehicle/index?fontType=' + 'recharge'
				})

			},
			//过滤兑换券列表，只显示一条兑换券,其余的兑换券点击详情查看
			filterList() {
				//筛选出兑换券
				let tmpList = this.cuoponList.filter(item => {
					if (item.type == 1) {
						return item;
					}
				});
				// 过滤掉兑换券
				this.cuoponList = this.cuoponList.filter(item => {
					if (item.type != 1) {
						return item;
					}
				});
				//把兑换券的第一条塞到数据列表里
				if (tmpList.length > 0) {
					this.cuoponList.unshift(tmpList[0]);
				}
			},
			onConfirmHandle() {
				this.modal.show = false;
			}
		}
	};
</script>

<style lang="scss">
	/deep/.neil-modal {
		.neil-modal__header {
			text {
				color: #000000 !important;
				background-color: #fff !important;
			}
		}

		.neil-modal__footer-left,
		.neil-modal__footer-right {
			color: #0066E9 !important;
			background-color: #fff;
			border: 1px solid #0066E9;
			border-radius: 10rpx;
			height: 70rpx;
			line-height: 70rpx;
		}

		.neil-modal__footer-right {
			color: #fff !important;
			background-color: #0066E9;
			border: 1px solid#0066E9;
		}
	}

	.maintaining-content-wrapper {
		.title {
			text-align: center;
			font-weight: 700;
			font-size: 34rpx;
			padding: 25rpx 50rpx;
			color: rgba(0, 0, 0, 0.9);
		}

		.msg_desc {
			padding: 0 30rpx;
			color: rgba(0, 0, 0, 0.5);
			font-size: 30rpx;
			text-align: left;

			.msg_desc-content {
				text-indent: 2em;
			}
		}
	}

	.coupons-list {
		padding: 30rpx;
	}

	.coupons-item {
		width: 100%;
		background-color: #ffffff;
		height: 192rpx;
		margin-bottom: 30rpx;
		border: 1px solid #e9e9e9;
		border-radius: 10rpx;
		box-shadow: 0px 0px 20rpx 0px rgba(71, 123, 217, 0.12);
		position: relative;

		box-align: center;
		align-items: center;
		display: flex;
	}

	.coupons-item .hd {
		padding-left: 36rpx;
	}

	.coupons-item .hd .hd-title {
		font-size: 72rpx;
		color: #0066E9;
		font-weight: 500;
		text-align: center;
		line-height: 1;
	}

	.coupons-item .hd .hd-title .symbol_unit {
		font-size: 62rpx;
		padding-right: 4rpx;
	}

	.coupons-item .hd .hd-title .price {
		display: inline-block;
		line-height: 1;
	}

	.coupons-item .hd .hd-des {
		font-size: 26rpx;
		color: #0066E9;
		margin-top: 8rpx;
		text-align: center;
	}

	.coupons-item .bd {
		flex: 1;
		padding-left: 36rpx;
	}

	.coupons-item .bd .bd-title {
		font-size: 34rpx;
		color: #666;
		font-weight: 500;
	}

	.coupons-item .bd .bd-des {
		font-size: 24rpx;
		font-weight: 400;
		margin-top: 8rpx;
		color: #999999;
		box-align: center;
		align-items: center;
		display: flex;
	}

	.coupons-item .ft {
		width: 200rpx;
	}

	.coupons-item .ft .ft-btn {
		width: 180rpx;
		height: 60rpx;
		color: #fff;
		margin: 0 auto;
		text-align: center;
		background-color: #0066E9;
		border-radius: 50rpx;
		font-size: 28rpx;
		line-height: 60rpx;
	}

	.coupons-item .tag {
		position: absolute;
		right: 0;
		top: 0;
	}

	.coupons-item .tag .tag-img {
		display: block;
		width: 90rpx;
		height: 88rpx;
	}

	.coupons-list {
		width: 100%;
		height: 100%;
		background-color: #f9f9f9;
	}

	.no-vehicle {
		padding-top: 320rpx;
		width: 100%;
		background-color: #f9f9f9;
	}

	.no-vehicle .no-vehicle_icon {
		width: 280rpx;
		height: 280rpx;
		margin: 0 auto;
		display: block;
	}

	.no-vehicle .no-vehicle_des {
		font-size: 28rpx;
		color: #999999;
		font-weight: 400;
		text-align: center;
		margin-top: 20rpx;
	}
</style>