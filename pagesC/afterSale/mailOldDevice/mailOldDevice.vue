<template>
	<view>
		<etcInfo></etcInfo>

		<view class="weui-form" style="margin-bottom: 140rpx;">
			<view class="weui-cells__title">邮寄信息</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell weui-cell_disabled">
					<view class="weui-cell__hd"><view class="weui-label weui-label__require">快递单号</view></view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.express_number" @input="changeInput($event, 'express_number')" class="weui-input" placeholder="请输入快递单号" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item"><button class="weui-btn weui-btn_primary" @click="mailDevice">确认邮寄</button></view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
import etcInfo from '../../components/etcInfo/etcInfo.vue';
import tLoading from '@/components/common/t-loading.vue';
import { getCurrUserInfo, getCurrentCar } from '@/common/storageUtil.js';
import { checkPhone } from '@/common/util.js';
export default {
	name: '',
	components: {
		etcInfo,
		tLoading
	},
	data() {
		return {
			isLoading: false,
			formData: {
				apply_id: '', //申请单id
				corporate_name: '', //快递公司名字
				device_type: '', //设备类型（0：卡，1：obu）
				express_number: '' //快递单号
			}
		};
	},
	computed: {
		customerInfo() {
			return getCurrUserInfo() || {};
		},
		vehicleInfo() {
			return getCurrentCar() || {};
		}
	},
	created() {},
	methods: {
		changeInput(event, data) {
			this.formData[data] = event.detail.value;
		},
		validate() {
			if (!this.formData.express_number) {
				uni.showModal({
					title: '提示',
					content: '请输入快递单号',
					showCancel: false
				});
			}
			return true;
		},
		mailDevice() {
			if (!this.validate()) return;
			let params = JSON.parse(JSON.stringify(this.formData));
			this.$request.post(this.$interfaces.emailDevice, { data: params }).then(res => {
				console.log(res, '邮寄旧设备');
				if (res.code == 200) {
					// this.formData=res.data
				}
			});
		}
	}
};
</script>

<style lang="scss">
.code-img {
	width: 194upx;
	height: 60upx;
	margin-left: 20upx;
}
.bottom-box {
	display: flex;
}
.bottom-box .btn-item {
	flex: 1;
}
.success-btn {
	margin: 0 60rpx;
}
.activation-page {
	position: relative;
}
.tips {
	margin: 20rpx 30rpx;
	.tips-text {
		margin-bottom: 10rpx;
		color: #3e62e8;
	}
}
.product_desc {
	margin: 40rpx 36rpx;
	.desc_title {
		font-size: 26rpx;
		font-family: PingFangSC, PingFangSC-Light;
		font-weight: 300;
		color: #555555;
		margin-bottom: 20rpx;
	}
	.desc_text {
		text-align: left;
		line-height: 50rpx;
		text-indent: 2em;
		padding-bottom: 10rpx;
		font-size: 24rpx;
		font-family: PingFangSC, PingFangSC-Light;
		font-weight: 300;
		color: #aaaaaa;
	}
}
</style>
