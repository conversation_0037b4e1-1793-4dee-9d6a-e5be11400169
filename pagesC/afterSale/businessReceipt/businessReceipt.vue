<template>
	<view class="">
		<view class="no-vehicle" v-if="receiptData.length==0">
			<image src="../../../static/toc/no-data.png" mode="aspectFilt" class="no-vehicle_icon"></image>
			<view class="no-vehicle_des">暂无记录</view>
		</view>
		<view v-else>
			<scroll-view :style="'height:'+(windowHeight-10)+'px;'" :scroll-top="scrollTop" scroll-y="true" class="scroll-Y"
				:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
				<item v-for="(item,index) in receiptData" :key="index" :receiptListData='item' />
				<load-more :loadStatus="noticeLoadStatus" />
			</scroll-view>
		</view>
		
	</view>
</template>

<script>
	import {
		getCurrUserInfo,
		getLoginUserInfo,
		getCurrentCar,
		getOpenid,
		getEtcVehicle
	} from "@/common/storageUtil.js";
	import item from './item.vue'
	import loadMore from '../../components/load-more/index.vue';
	export default {
		name: '',
		components: {
			item,
			loadMore,
		},
		data() {
			return {
				receiptData: [],
				formData: {
					custMastId: '',
					pageNum: 0,
					pageSize: 10
				},
				noticeLoadStatus: 3,
				lowerThreshold: 120,
				windowHeight: this.windowHeight,
				scrollTop: 0,
				old: {
					scrollTop: 0
				},
				flag: false,
				isLoading: false
			};
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {}
			},
			loginUserInfo() {
				return getLoginUserInfo() || {}
			}
		},
		created() {
			this.formData.custMastId = this.customerInfo.customer_id
			this.getAllReceipt()

		},
		methods: {
			upper: function(e) {

			},
			scrolltolower: function(e) {
				if (this.flag) return;
				let self = this;
				setTimeout(function() {
					self.formData.pageNum = self.formData.pageNum + 1;
					self.getAllReceipt();
				}, 500)

			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop;
			},
			getAllReceipt() {
				this.noticeLoadStatus = 1;
				let data = {
					routePath: this.$interfaces.allReceipt.method,
					bizContent: this.formData
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					if (res.code == 200) {
						let result = res.data.logPage.records || []
						if (res.data.logPage.records.length) {
							this.receiptData = this.receiptData.concat(result)
						} else {
							this.noticeLoadStatus = 3;
							this.flag = true
						}
						if (this.receiptData.length == res.data.logPage.total) {
							this.noticeLoadStatus = 3
							this.flag = true
						}
					} else {
						this.noticeLoadStatus = 2;
						uni.showModal({
							title: '提示',
							content: res.msg,
							showCancel: false
						});
					}
				}).catch(err => {
					this.noticeLoadStatus = 2;
					uni.showModal({
						title: '提示',
						content: err.msg,
						showCancel: false
					});
				})
			}
		}
	};
</script>

<style lang="scss">
.no-vehicle {
	padding-top: 320rpx;
	width: 100%;
	background-color: #f3f3f3;
}

.no-vehicle .no-vehicle_icon {
	width: 280rpx;
	height: 280rpx;
	margin: 0 auto;
	display: block;
}

.no-vehicle .no-vehicle_des {
	font-size: 28rpx;
	color: #999999;
	font-weight: 400;
	text-align: center;
	margin-top: 20rpx;
}
</style>
