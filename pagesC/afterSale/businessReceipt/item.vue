<!-- 新办订单列表 -->
<template>
	<view class="order-list">
		<view class="item">
			<view class="weui-form-preview">
				<view class="weui-form-preview__hd">
					<view class="weui-form-preview__item">

						<view class="weui-form-preview__label">
							{{ receiptListData.businessName}}
						</view>
						<view class="weui-form-preview__value"></view>
					</view>
				</view>
				<view class="weui-form-preview__bd">
					<view class="weui-form-preview__item">
						<view class="weui-form-preview__label">操作时间</view>
						<view class="weui-form-preview__value">{{ receiptListData.createTime}}</view>
					</view>
					<view class="weui-form-preview__item" v-if="receiptListData.businessSource!='3'">
						<view class="weui-form-preview__label">部门</view>
						<view class="weui-form-preview__value">{{ receiptListData.branchName}}</view>
					</view>
					<view class="weui-form-preview__item" v-if="receiptListData.businessSource!='3'">
						<view class="weui-form-preview__label">操作员</view>
						<view class="weui-form-preview__value">{{ receiptListData.operatorName}}</view>
					</view>
					<view class="weui-form-preview__item" v-if="receiptListData.type!='3'&&receiptListData.businessType!='503'&&receiptListData.businessType!='502'">
						<view class="weui-form-preview__label">卡号/obu号</view>
						<view class="weui-form-preview__value">{{ receiptListData.goodsCode}}</view>
					</view>
					<!-- customerName -->
					<view class="weui-form-preview__item" v-if="receiptListData.businessType=='302'">
						<view class="weui-form-preview__label">用户名</view>
						<view class="weui-form-preview__value">{{ receiptListData.customerName}}</view>
					</view>
					<view class="weui-form-preview__item"
						v-if="receiptListData.businessType!='302'&&receiptListData.businessType!='304'">
						<view class="weui-form-preview__label">车辆信息</view>
						<view class="weui-form-preview__value">
							{{ receiptListData.carNo+ '[' +receiptListData.carColor + ']' }}</view>
					</view>
					<view class="weui-form-preview__item" v-if="receiptListData.type=='4'&&receiptListData.businessType!='401'">
						<view class="weui-form-preview__label">充值方式</view>
						<view class="weui-form-preview__value">{{ receiptListData.businessModelName}}</view>
					</view>
					<view class="weui-form-preview__item"
						v-if="receiptListData.type=='4'||receiptListData.type=='5'">
						<view class="weui-form-preview__label">金额</view>
						<view class="weui-form-preview__value">{{ receiptListData.businessRealAmount}}元</view>
					</view>
					<view class="weui-form-preview__item" v-if="receiptListData.type=='5'">
						<view class="weui-form-preview__label">营销方案</view>
						<view class="weui-form-preview__value">{{ receiptListData.otherSchemeName}}</view>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: '',
		components: {},
		props: {
			receiptListData: {
				type: Object,
				default () {
					return {};
				}
			}
		},
		data() {
			return {};
		},
		computed: {
		},
		created() {},
		methods: {
		}
	};
</script>

<style scoped lang="scss">
	.order-list .item {
		margin: 20rpx;
		border-radius: 10px;
		background-color: #ffffff;
	}

	.weui-form-preview {
		position: relative;
		padding: 30rpx;		
		border-radius: 10px;
		background-color: #ffffff;
	}

	.weui-form-preview__hd {
		position: relative;
		padding: 20rpx 0;
		border-bottom: 1px dashed #c3c3c3;

		.weui-form-preview__label {
			min-width: 200rpx;
			color: #333;
			font-size: 32rpx;
			font-weight: 500;
			text-align: left;
		}

		.weui-form-preview__value {
			font-style: normal;
			font-size: 28rpx;
			font-weight: 400;
		}
	}

	.weui-form-preview__bd {
		padding-top: 12rpx;

		.weui-form-preview__item {
			padding-bottom: 12rpx;
		}
	}

	.weui-form-preview__item {
		display: flex;
		-moz-box-pack: justify;
		-ms-box-pack: justify;
		-webkit-box-pack: justify;
		-webkit-justify-content: space-between;
		-moz-justify-content: space-between;
		justify-content: space-between;
		-moz-box-align: center;
		-webkit-box-align: center;
		box-align: center;
		align-items: center;
		-webkit-align-items: center;
		-moz-align-items: center;
	}

	.weui-form-preview__label {
		color: #999999;
		font-size: 26rpx;
		font-weight: 400;
	}

	.weui-form-preview__value {
		color: #333;
		font-size: 26rpx;
		font-weight: 400;
		display: block;
		overflow: hidden;
		word-break: normal;
		word-wrap: break-word;
	}

	.weui-form-preview__ft {
		padding-bottom: 10rpx;
	}
</style>
