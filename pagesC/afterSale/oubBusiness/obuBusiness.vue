<template>
	<view>
		<etcInfo></etcInfo>
		<view class="weui-form" style="margin-top: 30rpx;">
			<view class="weui-cells__title">旧OBU处理方式</view>
		</view>
		<view class="tips">
			<view class="tips-text">* 温馨提示：</view>
			<view class="tips-text">1.质保期内的OBU，请选择邮寄旧OBU，否则将按照无OBU处理，需要支付设备费用；</view>
			<view class="tips-text">2.需要邮寄的OBU，需等待后台收到旧OBU方可进行下一步操作，请按实际情况选择是否邮寄旧OBU；</view>
		</view>

		<view class="weui-form">
			<view class="weui-cells">
				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">是否邮寄旧OBU</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<picker style="width:100%;" @change="bindPostHandle" :range="postType" range-key="label">
							<view class="weui-picker-value">{{ postType[post_type_index].label }}</view>
						</picker>
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell " v-if="formData.is_mail_old_card==0">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">邮寄收货人</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.consignee" @input="changeInput($event, 'consignee')" class="weui-input"
							placeholder="请输入邮寄收货人" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell " v-if="formData.is_mail_old_card==0">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">邮寄收货人联系方式</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.consignee_telephone" @input="changeInput($event, 'consignee_telephone')"
							class="weui-input" placeholder="请输入邮寄收货人联系方式" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" v-if="formData.is_mail_old_card==0">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">所在地区</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary" @click="mailShowRegion = true">
						<view class="weui-picker-value" v-if="mailCurrentRegion && mailCurrentRegion.length">
							{{mailCurrentRegion[0].label}}-{{mailCurrentRegion[1].label}}-{{mailCurrentRegion[2].label}}
						</view>
						<view class="weui-picker-placeholder" v-else>
							请选择所在地区
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell " v-if="formData.is_mail_old_card==0">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">邮寄地址</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.mail_address" @input="changeInput($event, 'mail_address')"
							class="weui-input" placeholder="请输入邮寄地址" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
			</view>
		</view>

		<view class="weui-form" style="margin-top: 30rpx;">
			<view class="weui-cells__title">新OBU安装方式</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">安装方式</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary">
							<picker style="width:100%;" @change="bindInstallChange" :range="installType"
								range-key="label">
								<view class="weui-picker-value">{{ installType[install_type_index].label }}</view>
							</picker>
						</view>
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell " v-if="formData.install_type==1">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">联系人</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.contacts_name" @input="changeInput($event, 'contacts_name')"
							class="weui-input" placeholder="请输入联系人" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell " v-if="formData.install_type==1">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">联系方式</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.contacts_telephone" @input="changeInput($event, 'contacts_telephone')"
							class="weui-input" placeholder="请输入联系方式" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" v-if="formData.install_type==1">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">所在地区</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary" @click="showRegion = true">
						<view class="weui-picker-value" v-if="currentRegion && currentRegion.length">
							{{currentRegion[0].label}}-{{currentRegion[1].label}}-{{currentRegion[2].label}}
						</view>
						<view class="weui-picker-placeholder" v-else>
							请选择所在地区
						</view>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell " v-if="formData.install_type==1">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">收货地址</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.address" @input="changeInput($event, 'address')" class="weui-input"
							placeholder="请输入收货地址" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
			</view>
		</view>
		<view class="product_desc">
			<view class="desc_title">安装说明：</view>
			<view class="desc_text">
				<text style="color:#6BA0FF">快递邮寄</text>
				：需要填写收货人姓名、收货人联系方式、收货人联系地址等快递信息，在通过审核后，管理员将按照填写的信息进行设备发货；
			</view>
			<view class="desc_text">
				<text style="color:#0066E9">网点自提</text>
				：需要您在收到审核通过消息后，前往任意一个ETC营业厅进行后续业务办理；
			</view>
		</view>
		<!-- 商品选择 -->
		<view class="weui-form" style="margin-top: 30rpx;">
			<view class="weui-cells__title">商品选择</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell weui-cell_picker">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">商品选择</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary">
							<picker style="width:100%;" @change="bindGoodsChange" :range="goodsList"
								range-key="goodsName">
								<view class="weui-picker-placeholder" v-if="goodsIndex == -1 ">
									请选择商品
								</view>
								<view class="weui-picker-value" v-else>{{ goodsList[goodsIndex].goodsName }}</view>
							</picker>
						</view>
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" v-if="marketList && marketList.length">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">营销方案选择</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<view class="weui-cell__bd weui-cell__primary">
							<picker style="width:100%;" @change="bindMarketChange" :range="marketList"
								range-key="marketActiveName">
								<view class="weui-picker-placeholder" v-if="marketIndex == -1 ">
									请选择营销方案
								</view>
								<view class="weui-picker-value" v-else>{{ marketList[marketIndex].marketActiveName }}
								</view>
							</picker>
						</view>
					</view>
					<view class="weui-cell__ft"></view>
				</view>
			</view>
		</view>

		<view class="weui-form" style="margin-bottom: 140rpx;">
			<view class="weui-cells__title">身份认证</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">预留手机号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.reserved_phone" @input="changeInput($event, 'reserved_phone')"
							class="weui-input" placeholder="请输入预留手机号" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>

				<view class="vux-x-input weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">图形验证码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="imgCode" @input="changeInput($event, 'imgCode')" class="weui-input"
							placeholder="图形验证码" />
					</view>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">
						<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">验证码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.veri_code" @input="changeInput($event, 'veri_code')" class="weui-input"
							placeholder="请输入验证码" />

					</view>
					<view class="btn-item">
						<button class="weui-btn_mini weui-btn_primary" style="margin-left: 10rpx;" :disabled="time>0"
							@click="sendSms">
							{{smsName}}
						</button>
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">换OBU原因</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.reason" @input="changeInput($event, 'reason')" class="weui-input"
							placeholder="请输入换OBU原因" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="submitApply">
						提交审核
					</button>
				</view>

			</view>
		</view>
		<!-- 地区选择控件 -->
		<u-select confirmColor='#58be6a' v-model="showRegion" mode="mutil-column-auto" :list="regionList"
			@confirm="confirm"></u-select>
		<u-select confirmColor='#58be6a' v-model="mailShowRegion" mode="mutil-column-auto" :list="mialRegionList"
			@confirm="mailConfirm"></u-select>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import etcInfo from '../../components/etcInfo/etcInfo.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getCurrUserInfo,
		getCurrentCar
	} from '@/common/storageUtil.js';
	import {
		checkPhone,
	} from "@/common/util.js";
	export default {
		name: '',
		components: {
			etcInfo,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				smsName: '发送验证码',
				time: 0,
				showRegion: false,
				mailShowRegion: false,
				autoHeight: true,
				currentRegion: [],
				mailCurrentRegion: [],
				regionList: [], // 地区列表
				mialRegionList: [],
				formData: {
					customer_type: '',
					address: '', //收货地址
					business_type: '3', //业务类型 1：新办 2：换OBU 3：换obu 4：挂失/解挂
					consignee: '', //邮寄收货人
					consignee_telephone: '', //邮寄收货人电话
					contacts_name: '', //联系人
					contacts_telephone: '', //联系电话
					install_type: '1', //安装方式
					is_mail_old_card: '0', //是否邮寄
					mail_address: '', //邮寄地址
					deptment: '',
					reason: '', //换货原因，
					reserved_phone: '', //预留手机号
					veri_code: "", //验证码
					//邮寄地址
					mail_province_code: "",
					mail_city_code: "",
					mail_area_code: '',
					mail_province_name: "",
					mail_city_name: "",
					mail_area_name: '',
					province_code: "",
					city_code: "",
					area_code: '',
					province_name: "",
					city_name: "",
					area_name: '',

					// orderMoney: "", // 订单总金额
					// goodsId: "", // 商品ID
					// goodsName: "", // 商品名称
					// marketId: "", // 营销方案ID
					// marketName: "", // 营销方案名称
					// materialIdList: [], // 物资编号列表,
					// goodsCode: "", //商品编号

				},
				goodsData: {
					goodsCode: "", //商品编号
					materialIdList: [], // 物资编号列表,
					orderMoney: "", // 订单总金额
					goodsId: "", // 商品ID
					goodsName: "", // 商品名称
					marketId: "", // 营销方案ID
					marketName: "", // 营销方案名称
					goodsType: '1', // 商品类型
					gx_card_type: '0', // 广西卡类型
					card_type: '22', //卡类型
				},
				goodsList: [],
				marketList: [], // 营销方案列表
				goodsIndex: -1, // 商品选择索引
				marketIndex: -1,
				postType: [{
						value: 0,
						label: '是'
					},
					{
						value: 1,
						label: '否'
					}
				],
				installType: [{
					value: 1,
					label: '快递邮寄'
				}, {
					value: 0,
					label: '网点自提'
				}],
				post_type_index: 0,
				install_type_index: 0,
				codeUrl: "",
				captchaId: "",
				imgCode: ''

			};
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {};
			},
			vehicleInfo() {
				return getCurrentCar() || {};
			}
		},
		created() {
			this.getRegion()
			this.getCaptcha()
			this.getOrderGoods()
			this.setCardType()
		},
		methods: {
			setCardType() {
				if (this.vehicleInfo) {
					//卡类型
					this.goodsData.card_type = this.vehicleInfo.card_type
					this.goodsData.gx_card_type = this.vehicleInfo.gx_card_type
				}
				if (this.customerInfo) {
					//用户类型
					this.formData.customer_type = this.customerInfo.customer_type
				}
			},
			// 获取商品列表
			getOrderGoods() {
				this.$request
					.post(this.$interfaces.getOrderGoods).then(res => {
						if (res.code == 200) {
							console.log(res, '获取商品列表');
							// this.goodsFilter(res.data)
							this.goodsList = res.data;
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					}).catch(error => {

					})
			},
			getOrderMarket() {
				let list = [{
					marketActiveMastId: '',
					marketActiveName: '默认',
				}];
				this.marketList = list;
				let params = {
					goodsId: this.goodsData.goodsId,
					price: this.goodsData.orderMoney,
					number: 1,
				};
				this.$request
					.post(this.$interfaces.getOrderMarket, {
						data: params
					}).then(res => {
						console.log(res, 'getOrderMarket');
						if (res.code == 200) {
							for (let i = 0; i < res.data.length; i++) {
								list.push(res.data[i])
							}
							this.marketList = list;
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					}).catch(error => {

					})
			},

			// 商品选择
			bindGoodsChange(e) {
				this.goodsIndex = e.detail.value;
				console.log(this.goodsList, '1111111111111');
				if (!this.goodsIndex) return;
				this.goodsData.goodsId = this.goodsList[this.goodsIndex].goodsId;
				this.goodsData.goodsName = this.goodsList[this.goodsIndex].goodsName;
				this.goodsData.materialIdList = this.goodsList[this.goodsIndex].goodsMaterials;
				this.goodsData.goodsCode = this.goodsList[this.goodsIndex].goodsCode
				this.goodsData.orderMoney = 0
				for (let i = 0; i < this.goodsData.materialIdList.length; i++) {
					this.goodsData.orderMoney = this.goodsData.orderMoney + this.goodsData.materialIdList[i].amount
				}
				this.getOrderMarket();
			},
			// 营销方案选择
			bindMarketChange(e) {
				this.marketIndex = e.detail.value;
				if (!this.marketIndex) return;
				let currnet = this.marketList[this.marketIndex];
				if (currnet.marketActiveMastId && currnet.marketActiveName) {
					this.goodsData.marketId = currnet.marketActiveMastId;
					this.goodsData.marketName = currnet.marketActiveName;
				}
			},

			confirm(e) {
				this.currentRegion = e;
				if (this.currentRegion && this.currentRegion.length) {
					this.formData.province_code = this.currentRegion[0].value
					this.formData.city_code = this.currentRegion[1].value
					this.formData.area_code = this.currentRegion[2].value
					this.formData.province_name = this.currentRegion[0].label
					this.formData.city_name = this.currentRegion[1].label
					this.formData.area_name = this.currentRegion[2].label

				}
			},
			mailConfirm(e) {
				this.mailCurrentRegion = e;
				if (this.mailCurrentRegion && this.mailCurrentRegion.length) {
					this.formData.mail_province_code = this.mailCurrentRegion[0].value
					this.formData.mail_city_code = this.mailCurrentRegion[1].value
					this.formData.mail_area_code = this.mailCurrentRegion[2].value
					this.formData.mail_province_name = this.mailCurrentRegion[0].label
					this.formData.mail_city_name = this.mailCurrentRegion[1].label
					this.formData.mail_area_name = this.mailCurrentRegion[2].label
				}
			},
			getRegion() {
				let params = {};
				this.$request.post(this.$interfaces.getRegion, {
					data: params
				}).then(res => {
					let list = [];
					if (res.code == 200) {
						list = res.data;
						for (let i = 0; i < list.length; i++) {
							list[i].children = list[i].child;
							for (let j = 0; j < list[i].children.length; j++) {
								list[i].children[j].children = list[i].children[j].child;
							}
						}
					}
					console.log(list);
					this.regionList = list;
					this.mialRegionList = list
				})
			},
			// 发送图形验证码
			getCaptcha() {
				let params = {
					// mobile: this.formData.mobile
				}
				this.$request.post(this.$interfaces.getCaptcha, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.codeUrl = res.data.image
						this.captchaId = res.data.captchaId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {

				})
			},
			//发送短信
			sendSms() {
				if (!checkPhone(this.formData.reserved_phone)) {
					uni.showModal({
						title: "提示",
						content: "请输入合法预留手机号",
						showCancel: false,
					});
					return;
				}
				if (!this.imgCode) {
					uni.showModal({
						title: "提示",
						content: "请输入图形验证码",
						showCancel: false,
					});
					return;
				}
				if (!this.time) {
					let countdown = 60
					let params = {
						mobile: this.formData.reserved_phone,
						mobileCode: this.imgCode,
						captchaId: this.captchaId
					};
					console.log(params, '发送验证码');
					this.$request.post(this.$interfaces.sendaAccountSms, {
						data: params
					}).then(res => {
						console.log(res);
						if (res.code == '200') {
							this.time = setInterval(() => {
								countdown = countdown - 1
								this.smsName = countdown + "秒后重新发送"
								if (countdown === 0) {
									clearInterval(this.time)
									this.time = null
									this.smsName = "重新发送"
								}
							}, 1000)
						} else {
							uni.showModal({
								title: "提示",
								content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res.code + '】',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										this.getCaptcha()
									}
								}
							});
						}
					}).catch(err => {

					})
				}
			},
			changeInput(event, data) {
				this.formData[data] = event.detail.value;
				if (data == 'imgCode') {
					this.imgCode = event.detail.value
				}
			},
			bindPostHandle(e) {
				this.post_type_index = e.detail.value;
				this.formData.is_mail_old_card = this.postType[e.detail.value].value || '';
				if (e.detail.value != 1) return;
				this.mailCurrentRegion = [];
				this.formData.consignee = ""
				this.formData.consignee_telephone = ""
				this.formData.mail_address = ""
				this.formData.mail_area_code = ""
				this.formData.mail_city_code = ""
				this.formData.mail_province_code = ""
			},
			bindInstallChange(e) {
				this.install_type_index = e.detail.value;
				this.formData.install_type = this.installType[e.detail.value].value || '';
				if (e.detail.value != 0) return;
				this.currentRegion = [];
				this.formData.contacts_name = ""
				this.formData.contacts_telephone = ""
				this.formData.address = ""
				this.formData.area_code = ""
				this.formData.city_code = ""
				this.formData.province_code = ""
			},
			validate() {
				let msg = "";
				if (!checkPhone(this.formData.reserved_phone)) {
					msg = " 请输入合法预留手机号"
				} else if (!this.formData.veri_code) {
					msg = "请输入验证码"
				} else if (!this.formData.reason) {
					msg = "请输入换OBU原因"
				}
				if (this.formData.install_type == 1) {
					if (!this.formData.contacts_name) {
						msg = "请输入联系人"
					} else if (!checkPhone(this.formData.contacts_telephone)) {
						msg = "请输入合法联系电话"
					} else if (!this.formData.address) {
						msg = "请输入收货地址"
					}
				}
				if (this.formData.is_mail_old_card == 0) {
					if (!this.formData.consignee) {
						msg = "请输入邮寄收货人";
					} else if (!checkPhone(this.formData.consignee_telephone)) {
						msg = "请输入合法邮寄收货人电话"
					} else if (!this.formData.mail_address) {
						msg = "请输入邮寄地址"
					}
				}
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return false;
				}
				return true
			},
			submitApply() {
				if (!this.validate()) return

				console.log(this.formData);
				let params = {
					...this.formData,
					applyCommodityVO: this.goodsData,
					car_no: this.vehicleInfo.vehicle_code,
					etc_card_no: this.vehicleInfo.cpu_card_id,
					obu_no: this.vehicleInfo.obu_id,
					customer_name: this.customerInfo.customer_name,
					vehicle_color: this.vehicleInfo.vehicle_color,
					vehicle_type: this.vehicleInfo.vehicle_type,
					vehicle_national_type: this.vehicleInfo.vehicle_user_type,

				}
				// let params = JSON.parse(JSON.stringify(this.formData))
				delete params.imgCode
				this.$request.post(this.$interfaces.replaceApply, {
					data: params
				}).then(res => {
					console.log(res, 'obu更换申请');
					if (res.code == 200) {
						uni.navigateTo({
							url: '/pagesA/orderBusiness/orderBusiness'
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(err => {
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
				// uni.navigateTo({
				// 	url:'/pagesA/orderBusiness/orderBusiness'
				// })
			}
		}
	};
</script>

<style lang="scss">
	.code-img {
		width: 194upx;
		height: 60upx;
		margin-left: 20upx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 230rpx;
	}

	.tips {
		margin: 20rpx 30rpx;

		.tips-text {
			margin-bottom: 10rpx;
			color: #3e62e8;
		}
	}

	.product_desc {
		margin: 40rpx 36rpx;

		.desc_title {
			font-size: 26rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #555555;
			margin-bottom: 20rpx;
		}

		.desc_text {
			text-align: left;
			line-height: 50rpx;
			text-indent: 2em;
			padding-bottom: 10rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #aaaaaa;
		}
	}
</style>
