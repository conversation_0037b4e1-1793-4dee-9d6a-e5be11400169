<template>
	<view>
		<etcInfo></etcInfo>

		<view class="weui-form" style="margin-bottom: 140rpx;">
			<view class="weui-cells__title">身份认证</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell weui-cell_disabled">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">预留手机号</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.reserved_phone" @input="changeInput($event, 'reserved_phone')"
							class="weui-input" placeholder="请输入预留手机号" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>

				<view class="vux-x-input weui-cell weui-cell_disabled">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">图形验证码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="imgCode" @input="changeInput($event, 'imgCode')" class="weui-input"
							placeholder="图形验证码" />
					</view>
					<image :src="codeUrl" class="code-img" @click="getCaptcha">
						<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_disabled">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">验证码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.veri_code" @input="changeInput($event, 'veri_code')" class="weui-input"
							placeholder="请输入验证码" />

					</view>
					<view class="btn-item">
						<button class="weui-btn_mini weui-btn_primary" style="margin-left: 10rpx;" :disabled="time>0"
							@click="sendSms">
							{{smsName}}
						</button>
					</view>
					<view class="weui-cell__ft"></view>
				</view>
				<view class="vux-x-input weui-cell weui-cell_disabled">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">挂失原因</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input :value="formData.reason" @input="changeInput($event, 'reason')" class="weui-input"
							placeholder="请输入挂失原因" />
					</view>
					<view class="weui-cell__ft"></view>
				</view>
			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="lossHandle('1')">
						卡片挂失
					</button>
				</view>
				<view class="btn-item">
					<button class="weui-btn weui-btn_primary" @click="lossHandle('2')">
						卡片解挂
					</button>
				</view>
			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import etcInfo from '../../components/etcInfo/etcInfo.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		getCurrUserInfo,
		getCurrentCar
	} from '@/common/storageUtil.js';
	import {
		checkPhone,
	} from "@/common/util.js";
	export default {
		name: '',
		components: {
			etcInfo,
			tLoading
		},
		data() {
			return {
				isLoading: false,
				smsName: '发送验证码',
				time: 0,
				formData: {
					business_type: '4', //业务类型 1：新办 2：换卡 3：换obu 4：挂失/解挂 
					net_user_no: "", //申请用户编号
					operation: '', //操作类型
					reason: '', //挂失原因，
					reserved_phone: '', //预留手机号
					veri_code: "", //验证码
				},
				codeUrl: "",
				captchaId: "",
				imgCode: ''

			};
		},
		computed: {
			customerInfo() {
				return getCurrUserInfo() || {};
			},
			vehicleInfo() {
				return getCurrentCar() || {};
			}
		},
		created() {
			this.getCaptcha()
		},
		methods: {
			// 发送图形验证码
			getCaptcha() {
				let params = {}
				this.$request.post(this.$interfaces.getCaptcha, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.codeUrl = res.data.image
						this.captchaId = res.data.captchaId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {

				})
			},
			//发送短信
			sendSms() {
				if (!checkPhone(this.formData.reserved_phone)) {
					uni.showModal({
						title: "提示",
						content: "请输入合法预留手机号",
						showCancel: false,
					});
					return;
				}
				if (!this.imgCode) {
					uni.showModal({
						title: "提示",
						content: "请输入图形验证码",
						showCancel: false,
					});
					return;
				}
				if (!this.time) {
					this.isLoading = true
					let countdown = 60
					let params = {
						mobile: this.formData.reserved_phone,
						mobileCode: this.imgCode,
						captchaId: this.captchaId
					};
					console.log(params, '发送验证码');
					this.$request.post(this.$interfaces.sendaAccountSms, {
						data: params
					}).then(res => {
						console.log(res);
						if (res.code == '200') {
							this.isLoading = false
							this.time = setInterval(() => {
								countdown = countdown - 1
								this.smsName = countdown + "秒后重新发送"
								if (countdown === 0) {
									clearInterval(this.time)
									this.time = null
									this.smsName = "重新发送"
								}
							}, 1000)
						} else {
							this.isLoading = false
							uni.showModal({
								title: "提示",
								content: res.msg,
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										this.getCaptcha()
									}
								}
							});
						}
					}).catch(err => {
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: err.msg,
							showCancel: false,

						});
					})
				}
			},
			changeInput(event, data) {
				this.formData[data] = event.detail.value;
				if (data == 'imgCode') {
					this.imgCode = event.detail.value
				}
			},
			validate() {
				let msg = "";
				if (!checkPhone(this.formData.reserved_phone)) {
					msg = "请输入合法预留手机号"
				} else if (!this.formData.veri_code) {
					msg = "请输入验证码"
				} else if (!this.formData.reason) {
					msg = "请输入挂失原因"
				}
				if (msg) {
					uni.showModal({
						title: "提示",
						content: msg,
						showCancel: false,
					});
					return false;
				}
				return true
			},
			lossHandle(val) {
				if (!this.validate()) return
				this.formData.operation = val
				let params = {
					...this.formData,
					car_no: this.vehicleInfo.vehicle_code,
					etc_card_no: this.vehicleInfo.cpu_card_id,
					obu_no: this.vehicleInfo.obu_id,
					customer_name: this.customerInfo.customer_name,
					customer_id: this.customerInfo.customer_id,
					vehicle_color: this.vehicleInfo.vehicle_color
				}
				delete params.imgCode
				console.log(params);

				this.$request.post(this.$interfaces.equipmentLoss, {
					data: params
				}).then(res => {
					if (res.data.code == 200) {
						uni.showModal({
							title: '提示',
							content: '操作成功',
							showCancel: false,
							success: response => {
								if (response.confirm) {
									console.log('点击确定')
									uni.redirectTo({
										url: '/pagesA/orderBusiness/orderBusiness'
									})
								}
							}
						});
					} else {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							showCancel: false
						});
					}

				}).catch(err => {
					uni.showModal({
						title: '提示',
						content: error.data.msg,
						showCancel: false
					});
				})
			}
		}
	};
</script>

<style lang="scss">
	.code-img {
		width: 194upx;
		height: 60upx;
		margin-left: 20upx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.success-btn {
		margin: 0 60rpx;
	}

	.activation-page {
		position: relative;
	}

	.tips {
		margin: 20rpx 30rpx;

		.tips-text {
			margin-bottom: 10rpx;
			color: #3e62e8;
		}
	}

	.product_desc {
		margin: 40rpx 36rpx;

		.desc_title {
			font-size: 26rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #555555;
			margin-bottom: 20rpx;
		}

		.desc_text {
			text-align: left;
			line-height: 50rpx;
			text-indent: 2em;
			padding-bottom: 10rpx;
			font-size: 24rpx;
			font-family: PingFangSC, PingFangSC-Light;
			font-weight: 300;
			color: #aaaaaa;
		}
	}
</style>
