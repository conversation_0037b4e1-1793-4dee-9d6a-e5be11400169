<template>
	<view class="transfers-box">
		<view class="image-box">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/package/vehicleLoading.gif"
				mode="aspectFill" class="vehicle-loading">
			</image>
		</view>
		<view class="desc">
			系统拼命加载中...
		</view>

	</view>
</template>

<script>
	import {

		getTicket,

	} from '@/common/storageUtil.js';
	export default {

		data() {
			return {
				redirectPath: '', // 重定向路由地址
				scene: '',
				envVersion: ''
			};
		},
		computed: {

		},
		onLoad(option) {
			console.log(option, 'option');
			this.scene = option.scene || '';
			this.envVersion = option.envVersion || '';
			if(option.path){
				this.redirectPath = decodeURIComponent(option.path)
				console.log(this.redirectPath, 'redirectPath');
				this.init();
			}else{
				this.getRedirectPath();
			}
		},
		created() {

		},
		methods: {
			init() {
				this.$request.post(this.$interfaces.validLogin, {
					data: {
						ticket: getTicket() || ''
					},
					aesKey: this.$aesKeyInit,
					md5Key: this.$md5KeyInit
				}).then(res => {

					if (res.data == 10200) {
						this.onRedirectHandle();
					} else {
						this.goLoginHandle();
					}
				}).catch(err => {
					this.goLoginHandle();
				})
			},
			getRedirectPath() {
				this.$request.post(this.$interfaces.getSceneValue, {
					data: {
						scene: this.scene || '',
						envVersion: this.envVersion || 'release'
					},
					aesKey: this.$aesKeyInit,
					md5Key: this.$md5KeyInit
				}).then(res => {
					if (res.code == 200) {
						this.redirectPath = res.data;
						this.init();
					} else {
						let _this = this
						uni.showModal({
							title: "提示",
							content: res.msg,
							success: function(res) {
								_this.init();
							}
						});
					}
				}).catch(err => {
					this.init();
				})
			},
			// 跳转重定向页面
			onRedirectHandle() {
				if (this.redirectPath) {
					uni.redirectTo({
						url: this.redirectPath
					})
					return;
				}
				uni.redirectTo({
					url: '/pages/home/<USER>/p-home'
				})
			},
			// 跳转登录页面
			goLoginHandle() {
				uni.setStorageSync('redirectPath', this.redirectPath);
				uni.reLaunch({
					url: "/pagesD/login/p-login"
				})
			}
		}
	};
</script>

<style scoped lang="scss">
	.transfers-box {
		width: 100%;
		height: 100%;
		background-color: #fff;

		.image-box {
			width: 100%;
			display: flex;
			justify-content: center;
			padding-top: 180rpx
		}

		.vehicle-loading {
			width: 370rpx;
			height: 278rpx;
		}

		.desc {
			font-size: 28rpx;
			width: 100%;
			font-weight: 400;
			text-align: center;
		}
	}
</style>