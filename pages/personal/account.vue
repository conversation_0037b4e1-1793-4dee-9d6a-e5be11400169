<!-- 个人中心 -->
<template>
	<view class="more my-account">
		<view class="header-account g-flex g-flex-align-center">
			<view class="header-account_hd">
				<image :src="getAvatar" class="img" mode="aspectFill"></image>
			</view>
			<view class="header-account_bd">
				<view class="name" v-if="checkLoginStatus">
					<view v-if="customerInfo.customer_name">{{customerInfo.customer_name}}</view>
					<view v-else @tap="customerChange">去绑定</view>
				</view>
				<view class="name" @click="loginOut" v-else>
					请登录
				</view>
			</view>
			<view class="header-account_ft" v-if="checkLoginStatus">
				<view class="value">
					{{loginInfo.loginName | loginNameFilter}}
				</view>
				<view class="desc g-flex" @click="sendLoginOut">
					<view>
						更换登录手机号
					</view>
					<view class="right-arrow"></view>
				</view>
			</view>
		</view>
		<view class="nav-list">
			<view class="cu-list menu">
				<view class="cu-item click-bg" @tap="toUserAgreement">
					<view class="cell-wrap u-f-a">
						<view class="cell-wrap-hd user-agreement"></view>
						<text class="cell-wrap-bd">用户协议</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
					</view>
				</view>
				<view class="cu-item click-bg" @tap="toAccountDetail">
					<view class="cell-wrap u-f-a">
						<view class="cell-wrap-hd exclusive-agreement"></view>
						<text class="cell-wrap-bd">专属对公转账充值账户查询</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
					</view>
				</view>
				<view class="cu-item click-bg" @tap="ecssSyncHandle">
					<view class="cell-wrap u-f-a">
						<view class="cell-wrap-hd ecss-sync"></view>
						<text class="cell-wrap-bd">余额迁移</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
					</view>
				</view>
				<view class="cu-item click-bg" @tap="electronicSign">
					<view class="cell-wrap u-f-a">
						<view class="cell-wrap-hd electronicSign"></view>
						<text class="cell-wrap-bd">电子协议查询</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
					</view>
				</view>
				<view class="cu-item click-bg" @tap="customerChange">
					<view class="cell-wrap u-f-a">
						<view class="cell-wrap-hd switch-accounts"></view>
						<text class="cell-wrap-bd">切换ETC账号</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
					</view>
				</view>
				<view class="cu-item click-bg" @tap="toStar">
					<view class="cell-wrap u-f-a">
						<view class="cell-wrap-hd my-collect"></view>
						<text class="cell-wrap-bd">我的收藏</text>
						<view class="arrow">
							<view class="right-arrow"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="btn-wrap">
			<button class="weui-btn weui-btn_primary" @click="sendLoginOut">
				退出登录
			</button>
		</view>
		<view class="a-copyright-bottom">
			<view class="versions">3.5.8</view>
			<view class="company">广西捷通高速科技有限公司</view>
		</view>
		<tLoading :isShow="isLoading" />
		<view class="bottom" style="height:120rpx">
			<tabbar current="account" />
		</view>

	</view>
</template>

<script>
	import tabbar from "@/components/base/tab-bar/index.vue"
	import store from 'store/index.js';
	import {
		setTicket,
		setMd5Key,
		setAesKey,
		getTicket,
		getMd5Key,
		getAesKey,
		getLoginUserInfo,
		getCurrUserInfo,
		getOpenid,
	} from '@/common/storageUtil.js';
	import tLoading from '@/components/common/t-loading.vue';
	import TModal from '@/components/t-modal/t-modal.vue'
	import {
		vehicleColorPicker
	} from '@/common/const/optionData.js'
	export default {
		components: {
			tLoading,
			tabbar,
			TModal,
		},
		data() {
			return {
				isLoading: false,
				isRelative: false,
				dialogVisible: false,

			};
		},
		onShow() {

		},
		computed: {
			checkLoginStatus() {
				return !!(getTicket() && getMd5Key() && getAesKey() && this.getUserNo)
			},
			loginInfo() {
				return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length ? getLoginUserInfo() : null
			},
			getUserNo() {
				return !!this.loginInfo ? getLoginUserInfo().userNo : ''
			},
			customerInfo() {
				return getCurrUserInfo()
			},
			getAvatar() {
				if (!this.checkLoginStatus) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				if (this.customerInfo && this.customerInfo.customer_type == 0) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				if (this.customerInfo && this.customerInfo.customer_type == 1) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
			}
		},
		filters: {
			loginNameFilter(name) {
				const regex = /(\d{3})\d+(\d{4})/;
				let hideCount = (name.toString().length - 7);
				let starString = hideCount > 0 ? '*'.repeat(hideCount) : '';
				return !!name && hideCount > 0 ? name.replace(regex, `$1${starString}$2`) : name
			}
		},
		methods: {
			toUserAgreement() {
				var callcenter = 'https://portal.gxetc.com.cn/agreement/user.png';
				// var callcenter = 'http://localhost:3000'
				uni.navigateTo({
					url: '/pages/uni-webview/h5-webview?ownPath=' + encodeURIComponent(callcenter)
				});
			},

			toAccountDetail() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/publicAccount/accountDetail'
				});
			},
			customerChange() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList'
				})
			},
			toStar() {
				uni.navigateTo({
					url: '/pagesD/star/index'
				})
			},
			vaildLogin() {
				if (!getTicket()) {
					uni.showModal({
						title: '提示',
						content: '请先登录',
						success: (res) => {
							if (res.confirm) {
								uni.reLaunch({
									url: '/pagesD/login/p-login'
								})
							}
						}
					})
					return
				}
				return true;
			},
			vaildETCAccount() {
				if (!(getCurrUserInfo() && getCurrUserInfo().customer_id)) {
					uni.showModal({
						title: '提示',
						content: '请先绑定ETC用户',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pagesB/accountBusiness/accountList/accountList'
								})
							}
						}
					})
					return
				}
				return true
			},
			ecssSyncHandle() {
				if (!this.vaildLogin()) return;
				if (!this.vaildETCAccount()) return
				uni.navigateTo({
					url: '/pagesB/ecssBusiness/index'
				})
			},
			electronicSign() {
				if (!this.vaildLogin()) return
				if (!this.vaildETCAccount()) return
				uni.navigateTo({
					url: '/pagesB/vehicleBusiness/vehicleList?fontType=' + 'signature'
				})
			},
			// 更换手机号码
			sendLoginOut() {
				if (this.isLoading) return
				this.isLoading = true;
				this.$request.post(this.$interfaces.accountLogout, {
					data: {
						netUserNo: this.getUserNo
					}
				}).then(res => {
					this.isLoading = false;
					this.loginOut();
				}).catch(err => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: '更换手机号码异常:' + err.msg,
						showCancel: false,
					});
					this.loginOut();
				})
			},
			loginOut() {
				this.isLoading = true;
				setMd5Key(this.$md5KeyInit);
				setAesKey(this.$aesKeyInit);

				setTimeout(() => {
					this.isLoading = false;
					uni.reLaunch({
						url: '/pagesD/login/p-login'
					});
				}, 1000);
			}
		}
	};
</script>

<style lang="scss" scoped>
	.my-account {
		position: relative;
		width: 100%;
		height: 100%;

		.header-account {
			padding: 16rpx 20rpx;
			background-color: #ffffff;
		}

		.header-account .header-account_hd {
			width: 66rpx;
			height: 66rpx;
		}

		.header-account .header-account_hd .img {
			display: block;
			width: 100%;
			height: 100%;
		}

		.header-account .header-account_bd {
			flex: 1;
			margin-left: 14rpx;

		}

		.header-account .header-account_bd .name {
			font-weight: 400;
			color: #323435;
			font-size: 28rpx;
		}
		.header-account .header-account_ft .value {
			font-weight: 400;
			color: #333333;
			font-size: 30rpx;
			text-align: right;
		}
		.header-account .header-account_ft .desc {
			color: #999999;
			font-size: 24rpx;
		}

		.header-account .header-account_ft .desc .right-arrow {
			position: relative;
			padding-left: 28rpx;
		}

		.header-account .header-account_ft .desc .right-arrow:after {
			content: " ";
			display: inline-block;
			height: 6px;
			width: 6px;
			border-width: 1px 1px 0 0;
			border-color: #858686;
			border-style: solid;
			-webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
			transform: matrix(.71, .71, -.71, .71, 0, 0);
			position: relative;
			top: -2px;
			position: absolute;
			top: 50%;
			margin-top: -4px;
			right: 6px;
		}

		.nav-list {
			background-color: #ffffff;
			margin: 20rpx;
		}

		.cu-list {
			.cu-item {
				height: 110rpx;
				padding-left: 0;
				padding-right: 0;

				&:after {
					border: none;
				}
			}
		}

		.cell-wrap {
			padding-left: 25rpx;
		}

		.cell-wrap .cell-wrap-bd {
			font-weight: 400;
			color: #333333;
			font-size: 30rpx;
		}

		.cell-wrap .cell-wrap-hd {
			width: 35rpx;
			height: 35rpx;
			margin: 0 25rpx;
			background-repeat: no-repeat;
			background-size: 35rpx;
		}

		.cell-wrap .arrow {
			position: absolute;
			right: 30rpx;
			display: flex;
			align-items: center;

			.right-arrow {
				width: 26rpx;
				height: 26rpx;
				background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/myAccount/right-arr.png');
				background-repeat: no-repear;
				background-size: 26rpx;
			}
		}

		.a-copyright-bottom {
			font-size: 24rpx;
			width: 100%;
			position: absolute;
			bottom: 190rpx;
			text-align: center;
			font-weight: 400;
			color: #858686;

			.versions {
				margin-bottom: 6rpx;
			}
		}

		.btn-wrap {
			margin: 96rpx 60rpx 0 60rpx;
		}
	}




	.cell-wrap {
		.exclusive-agreement {
			background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/myAccount/exclusive-agreement.png');
		}

		.user-agreement {
			background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/myAccount/user-agreement.png');
		}

		.ecss-sync {
			background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/myAccount/ecss-sync.png');
		}

		.switch-accounts {
			background-image: url('https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/myAccount/switch-accounts.png');
		}

		.electronicSign {
			background-image: url('../../static/h5/home/<USER>');
		}

		.my-collect {
			background-image: url('../../static/h5/home/<USER>');
		}
	}
</style>