<template>
	<view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		setOpenid
	} from '@/common/storageUtil.js';
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				publicCode: '',
				redirect_uri:'/pagesD/login/p-login',
				isLoading: false,
			};
		},
		computed: {

		},
		onLoad(option) {
			this.redirect_uri = sessionStorage.getItem('redirect_uri') || this.redirect_uri
			if (sessionStorage.getItem('publicOpenId')) {
				setOpenid(sessionStorage.getItem('publicOpenId'));
				this.goRedirectUriHandle();
				return
			}
			this.isLoading = true;
			this.publicCode = option.code;
			if (!this.publicCode) {
				this.wechatPublicCode();
			} else {
				this.getPublicOpenId();
			}
		},
		created() {

		},
		methods: {
			goRedirectUriHandle(){
				uni.reLaunch({
					url: this.redirect_uri,
					success:function(){
						sessionStorage.setItem('redirect_uri', '')
					}
				})
			},
			wechatPublicCode() {
				var param = {
					state: encodeURIComponent(window.location.origin + '/#/pages/wxauthorize/wxauthorize')
				}
				this.$request.post(this.$interfaces.wechatPublicCode, {
					data: param
				}).then(res => {
					this.isLoading = false;
					console.log('res.data===>>>',res.data)
					window.location.replace(res.data);
				}).catch(err => {
					this.isLoading = false;
				})
			},
			getPublicOpenId() {
				let _this = this;
				let params = {
					code: this.publicCode
				}
				this.$request.post(this.$interfaces.wechatPublicOpenId, {
					data: params
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						sessionStorage.setItem('publicOpenId', res.data.publicOpenId);
						setOpenid(res.data.publicOpenId);
						this.goRedirectUriHandle();
					} else {
						this.wechatPublicCode();
					}
				}).catch(err => {
					this.isLoading = false;
				})
			}
		}
	};
</script>

<style scoped lang="scss">

</style>
