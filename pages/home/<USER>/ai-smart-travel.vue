<template>
  <view class="ai-smart-travel">
    <!-- 标题区域 卡通人物 使用绝对定位 -->

    <view class="ai-header">
      <view class="ai-characters">
        <image src="/static/agent/agent-logo.png" class="character-boy" mode="aspectFit"></image>
      </view>
    </view>


    <!-- 功能模块区域 -->
    <view class="ai-content">
      <view class="function-grid">
        <!-- 第一行 -->
        <view class="function-row">
          <view class="function-item" @tap="handleFunction('hotel')">
            <image src="/static/agent/hotel-icon.png" class="function-icon" mode="aspectFit" @error="handleImageError">
            </image>
            <text class="function-text">酒店推荐</text>
          </view>
          <view class="function-item" @tap="handleFunction('route')">
            <image src="/static/agent/route-icon.png" class="function-icon" mode="aspectFit" @error="handleImageError">
            </image>
            <text class="function-text">路线规划</text>
          </view>
        </view>

        <!-- 第二行 -->
        <view class="function-row">
          <view class="function-item" @tap="handleFunction('food')">
            <image src="/static/agent/eat-icon.png" class="function-icon" mode="aspectFit" @error="handleImageError">
            </image>
            <text class="function-text">美食推荐</text>
          </view>
          <view class="function-item" @tap="handleFunction('attraction')">
            <image src="/static/agent/spot-icon.png" class="function-icon" mode="aspectFit" @error="handleImageError">
            </image>
            <text class="function-text">景点推荐</text>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="ai-button" @tap="handleAskQuestion">
        <text class="button-text">有问题可以向我提问</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AiSmartTravel',
  data() {
    return {}
  },
  methods: {
    // 处理功能点击
    handleFunction(type) {
      console.log('功能点击:', type)
      // 跳转到AI智能出行页面，并传递功能类型
      uni.navigateTo({
        url: `/pagesD/agent/index?type=${type}`
      })
    },

    // 处理提问按钮点击
    handleAskQuestion() {
      console.log('提问按钮被点击')
      // 跳转到AI智能出行页面
      uni.navigateTo({
        url: '/pagesD/agent/index'
      })
    },

    // 处理图片加载错误
    handleImageError(e) {
      console.log('图片加载失败:', e)
      // 可以在这里设置默认图片或显示占位符
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-smart-travel {
  margin: 20rpx;
  background: #F7E4D3;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;

  .ai-header {
    position: relative;
    height: 118rpx;
    overflow: hidden;

    .ai-characters {
      position: absolute;
      bottom: -20rpx;
      left: 62rpx;

      .character-boy {
        width: 586rpx;
        height: 118rpx;
      }
    }

  }


  .ai-content {
    background: #FFFFFF;
    margin: 0 24rpx 24rpx;
    border-radius: 12rpx;
    padding: 32rpx 24rpx;

    .function-grid {
      .function-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .function-item {
          width: 266rpx;
          height: 76rpx;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          background: #FDF2EB;
          border-radius: 12rpx;
          transition: all 0.3s ease;

          &:first-child {
            margin-right: 16rpx;
          }

          &:active {
            background: #F5E6D3;
            transform: scale(0.95);
          }

          .function-icon {
            width: 36rpx;
            height: 36rpx;
            margin-right: 16rpx;
          }

          .function-text {
            font-size: 28rpx;
            color: #FA691C;
            font-weight: 500;
          }
        }
      }
    }

    .ai-button {
      width: 558rpx;
      background: #FA691C;
      border-radius: 10rpx;
      padding: 18rpx 0;
      text-align: center;
      margin: 0 auto;
      margin-top: 42rpx;
      transition: all 0.3s ease;

      &:active {
        background: #E55A2B;
        transform: scale(0.98);
      }

      .button-text {
        font-size: 28rpx;
        color: #FFFFFF;
        font-weight: 500;
      }
    }
  }
}
</style>
