    <template>
      <view class="nav-wrap">
        <view class="info-container">
          <!-- 左侧蓝色标题区域 -->
          <view class="title-section" @click="goToTab">
            <view class="title-icon">
              <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/platform-nav.png" mode="widthFix"></image>
            </view>
            <view class="title-text">
              <text>高速</text>
              <text>资讯</text>
            </view>
          </view>

          <!-- 右侧新闻列表区域 -->
          <view class="news-section">
            <view class="news-item" v-for="(item, index) in displayedNewsList" :key="item.informationId"
              @click="goToArticleDetail(item)" :class="{ 'news-item-animate': isAnimating }">
              <view class="news-content" :class="{ 'last-item': index === displayedNewsList.length - 1 }">
                <view class="news-title">{{ item.titleName }}</view>
                <view class="news-date">{{ item.publishTime }}</view>
              </view>
            </view>

            <!-- 空状态 -->
            <view class="empty-state" v-if="allNewsList.length === 0">
              <view class="empty-text">暂无最新资讯</view>
            </view>
          </view>
        </view>
      </view>
    </template>

<script>
export default {
  data() {
    return {
      currentIndex: 0, // 当前显示的新闻起始索引
      timer: null, // 定时器引用
      isAnimating: false, // 动画状态
      formData: {
        classType: "0", //资讯类型 
        category: "2", // 一级分类
        pageNum: 1,
        pageSize: 50
      },
      // 所有新闻数据（从API获取）
      allNewsList: []
    };
  },
  computed: {
    // 当前显示的3条新闻
    displayedNewsList() {
      if (this.allNewsList.length === 0) return [];
      if (this.allNewsList.length <= 3) return this.allNewsList;

      const result = [];
      for (let i = 0; i < 3; i++) {
        const index = (this.currentIndex + i) % this.allNewsList.length;
        result.push(this.allNewsList[index]);
      }
      return result;
    }
  },
  methods: {
    // 获取资讯列表数据
    getInformationList() {
      let params = {
        ...this.formData
      };
      this.$request
        .post(this.$interfaces.informationList, {
          data: params
        })
        .then(res => {
          if (res.code == 200) {
            console.log('首页资讯数据:', res.data.data);
            let result = res.data?.data || [];
            if (result.length > 0) {
              this.allNewsList = result;
              this.startAutoPlay(); // 数据加载完成后启动轮播
            }
          } else {
            console.log('获取资讯失败:', res);
          }
        })
        .catch(err => {
          console.log('获取资讯异常:', err);
        });
    },

    // 启动自动轮播
    startAutoPlay() {
      this.stopAutoPlay(); // 先清除现有定时器
      if (this.allNewsList.length > 3) {
        this.timer = setInterval(() => {
          this.nextNews();
        }, 10000); // 10秒轮播
      }
    },

    // 切换到下一条新闻
    nextNews() {
      this.isAnimating = true;
      setTimeout(() => {
        this.currentIndex = (this.currentIndex + 1) % this.allNewsList.length;
        this.isAnimating = false;
      }, 300); // 动画持续时间
    },

    // 停止自动轮播
    stopAutoPlay() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    goToArticleDetail(row) {
      // articleType 1-富文本页面、2-链接跳转
      if (row.articleType == "2") {
        uni.navigateTo({
          url:
            "/pages/uni-webview/uni-webview?ownPath=" +
            encodeURIComponent(row.articleUrl)
        });
      }
      if (row.articleType == "1") {
        console.log(*********);
        uni.navigateTo({
          url: `/pagesC/infoBusiness/detail?informationId=${row.informationId}&category=1`
        });
      }
    },

    // 跳转到TAB页面
    goToTab() {
      uni.navigateTo({
        url: "/pagesD/travelService/information/index"
      });
    }
  },

  mounted() {
    this.getInformationList();
  },

  // 页面显示时重新启动轮播
  onShow() {
    this.startAutoPlay();
  },

  destroyed() {
    this.stopAutoPlay();
  }
};
</script>

<style lang="scss" scoped>
.nav-wrap {
  margin: 20rpx;

  .info-container {
    width: 710rpx;
    min-height: 258rpx;
    background: #ffffff;
    border-radius: 12rpx;
    display: flex;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    border: 2rpx solid #f0f0f0;
    overflow: hidden;
    box-sizing: border-box;

    // 左侧蓝色标题区域
    .title-section {
      width: 108rpx;
      background: #ECF3FF;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      box-sizing: border-box;
      flex-shrink: 0;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      flex-direction: column;

      &:active {
        opacity: 0.8;
      }

      .title-icon {
        width: 72rpx;
        height: 72rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .title-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        text {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 600;
          font-size: 26rpx;
          color: #4181FF;
          line-height: 1.0;
          margin-bottom: 4rpx;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    // 右侧新闻列表区域
    .news-section {
      flex: 1;
      padding: 20rpx 20rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: #ffffff;
      position: relative;
      box-sizing: border-box;
      min-width: 0; // 确保flex子元素可以缩小

      .news-item {
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        box-sizing: border-box;

        &.news-item-animate {
          opacity: 0.7;
          transform: translateY(-2rpx);
        }

        &:active {
          background-color: #f8f9fa;
          border-radius: 6rpx;
        }

        .news-content {
          padding: 6rpx 0 10rpx;
          border-bottom: 1rpx solid #f5f5f5;
          box-sizing: border-box;

          &.last-item {
            border-bottom: none;
          }

          .news-title {
            font-size: 24rpx;
            color: #666666;
            line-height: 32rpx;
            margin-bottom: 2rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            font-weight: 400;
            max-height: 64rpx;
            width: 100%;
            box-sizing: border-box;
          }

          .news-date {
            font-size: 20rpx;
            color: #999999;
            text-align: right;
            font-weight: 400;
          }
        }
      }

      .empty-state {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .empty-text {
          font-size: 24rpx;
          color: #999999;
        }
      }
    }

    // 整体hover效果（仅用于视觉反馈）
    &:active {
      transform: scale(0.995);
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
    }
  }
}
</style>
