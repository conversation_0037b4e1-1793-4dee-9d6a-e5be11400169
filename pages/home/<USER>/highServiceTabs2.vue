<template>
	<view class="high-tabs">
		<view class="h-title">嗨玩八桂</view>
		<!-- Banner轮播模块 -->
		<bannerSwiper :bannerList="bannerList"></bannerSwiper>

		<u-tabs :list="highList" :is-scroll="false" inactive-color='#666666' :current="current" activeColor='#0066E9'
			@change="change">
		</u-tabs>
		<highPlay v-if="current == 0"></highPlay>
		<highService v-if="current == 3"></highService>
		<etcNet v-if="current == 1"></etcNet>
		<etcPark v-if="current == 2"></etcPark>
	</view>
</template>
<script>
	import bannerSwiper from './bannerSwiper.vue';
	import highService from '../highService/highService.vue'
	import etcNet from '../etcNet/etcNet.vue'
	import etcPark from '../etcPark/etcPark.vue'
	import highPlay from '../highPlay/highPlay.vue'
	export default {
		components: {
			bannerSwiper,
			highService,
			etcNet,
			etcPark,
			highPlay
		},
		data() {
			return {
				current: 0,
				highList: [{
						name: '景区'
					},
					{
						name: '美食'
					},
					{
						name: '酒店'
					},
					{
						name: '游玩路线攻略'
					},
				],
				// Banner数据
				bannerList: [
					{
						imageUrl: 'https://etc-expand.gxjettoll.cn:8443/expTravelFile/advertising/img/2024/10/20241011/hhu48iyc75lq.jpg',
						title: '关于小客车周末自驾游广西高速公路通行优惠提醒...',
						subtitle: '2025-05-07',
						linkType: 'miniprogram',
						appId: 'wx416f21d2e7c54de9',
						path: '/pages/home/<USER>'
					},
					{
						imageUrl: 'https://etc-expand.gxjettoll.cn:8443/expTravelFile/advertising/img/2024/10/20241011/hhu48iyc75lq.jpg',
						title: '五一假期高速公路出行秩序大公开！',
						subtitle: '2025-04-27',
						linkType: 'miniprogram',
						appId: 'wx416f21d2e7c54de9',
						path: '/pages/home/<USER>'
					},
					{
						imageUrl: 'https://etc-expand.gxjettoll.cn:8443/expTravelFile/advertising/img/2024/10/20241011/hhu48iyc75lq.jpg',
						title: '2025年春节、春运期间广西区内高速公路出行指南。',
						subtitle: '2025-01-22',
						linkType: 'miniprogram',
						appId: 'wx416f21d2e7c54de9',
						path: '/pages/home/<USER>'
					}
				],
			};
		},
		methods: {
			change(index) {
				this.current = index;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.high-tabs {
		background: #FFFFFF;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		font-family: PingFang SC, PingFang SC;
		margin: 0 20rpx;

		.h-title {
			padding: 20rpx 20rpx 10rpx 20rpx;
			font-weight: 600!important;
			font-size: 30rpx!important;
			color: #333333!important;
			line-height: 40rpx;
		}
	}
</style>