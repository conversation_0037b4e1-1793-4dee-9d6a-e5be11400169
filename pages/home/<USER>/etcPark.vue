<template>
	<view class="net-park">
		<highSearch :placeholder="'输入停车场名称'" @click="search"></highSearch>
		<view v-if="text" class="tips-text">
			{{text}}
		</view>
		<scroll-view  class="item-wrapper" :style="{height:height}" scroll-y="true">
			<netItem v-for="(item,index) in netList" :item="item" :key="index"></netItem>
		</scroll-view>
	</view>
</template>
<script>
	import highSearch from '../component/highSearch'
	import netItem from './netItem.vue';
	export default {
		components: {
			netItem,
			highSearch
		},
		data() {
			return {
				text: '',
				longitude: '',
				latitude: '',
				netList: [],
				height: '900rpx'
			};
		},
		created() {
			this.getLocal()
		},
		methods: {
			search(name) {
				console.log('name==', name)
				this.getNetList(name)
			},
			getLocal() {
				uni.getLocation({
					type: 'wgs84',
					success: (res) => {
						console.log('当前位置的经度：' + res.longitude);
						console.log('当前位置的纬度：' + res.latitude);
						this.longitude = res.longitude
						this.latitude = res.latitude
						this.getNetList()
					},
					fail: (fail) => {
						this.text = '获取不到当前位置信息，无法加载网点列表'
					}
				});
			},
			getNetList(name) {
				if (!this.longitude || !this.latitude) {
					this.text = '获取不到当前位置信息，无法加载网点列表'
					return
				}
				let params = {
					id: '',
					title: name || '',
					netWorkType: 'b60e320b-0cda-481f-ad8f-9f7c40fee26d',
					selfLon: this.longitude,
					selfLat: this.latitude
				}
				this.text = '加载列表中...' //
				uni.request({
					url: 'https://www.gxetc.com.cn/website-api/financeNetworkInfo/search',
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					data: params,
					success: (res) => {
						console.log('res====>>>>', res)
						if (res.data.code == 200) {
							if (res.data.data.length > 0) {
								this.netList = res.data.data
								this.text = '' //重置错误信息
								if (this.netList.length == 1) {
									//计算少量数据时滑动高度
									this.height = '280rpx'
								} else if (this.netList.length == 2) {
									this.height = '560rpx'
								} else {
									this.height = '840rpx'
								}
							} else {
								this.netList = res.data.data || []
								this.text = '暂无数据'
							}
						} else {
							this.text = '抱歉，加载数据失败，请稍后重试'
						}
					},
					fail: (err) => {
						this.text = '抱歉，加载数据失败，请稍后重试'
					}
				})
			},
		}
	};
</script>

<style lang="scss" scoped>
	.net-park {
		font-family: PingFang SC, PingFang SC;

		.item-wrapper {
			overflow: hidden;
			height: 840rpx;
			margin-bottom: 20rpx;
		}

		.tips-text {
			margin-bottom: 20rpx;
			height: 250rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #999999;
		}
	}
</style>