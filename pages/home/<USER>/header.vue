<template>
	<view class="header">
		<view class="header-wrapper">
			<view class="header-title">
				<view class="bg-image">
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>"
						class="img" mode="aspectFill"></image>

				</view>
				<view class="title" :style="{'top':marginTop,'height':height,'lineHeight':height}">桂小通(广西捷通)</view>

			</view>
			<view class="header-account g-flex g-flex-align-center" :style="{top:headerAccountTop}">
				<view class="header-account_hd">
					<image :src="getAvatar" class="img" mode="aspectFill"></image>
				</view>
				<view class="header-account_bd">
					<view class="name g-flex g-flex-align-center">
						<block v-if="checkLoginStatus">
							<view class="name-text ellipsis">
								<text v-if="customerInfo.customer_name">
									{{isPrivacyProtected ? getMaskedCustomerName : customerInfo.customer_name}}
								</text>
								<text v-else-if="loginInfo && loginInfo.loginName">
									{{isPrivacyProtected ? getMaskedLoginName(loginInfo.loginName) : loginInfo.loginName}}
								</text>
								<text v-else>未知用户</text>
							</view>
							<!-- 添加隐私保护按钮 -->
							<view class="privacy-btn" @click="togglePrivacy" role="button" aria-label="隐私保护开关" :aria-pressed="isPrivacyProtected ? 'true' : 'false'">
								<image :src="isPrivacyProtected ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/close-eye.png' : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/open-eye.png'"
									class="privacy-icon" mode="aspectFit" :alt="isPrivacyProtected ? '隐私保护开启' : '隐私保护关闭'"></image>
							</view>
						</block>
						<block v-else>
							<view class="name-text">请登录</view>
						</block>
					</view>
				</view>
				<view class="header-account_ft g-flex g-flex-horizontal-vertical" v-if="!checkLoginStatus">
					<view class="desc">
						<!-- 搜索框和消息通知 -->
						<view class="desc" @click="goLoginHandle">
							请登录
						</view>
					</view>
				</view>
				<view v-if="checkLoginStatus" class="g-flex g-flex-horizontal-vertical">
					<view class="desc g-flex g-flex-center g-flex-align-center">
						<!-- 搜索框和消息通知 -->
						<view class="search-wrapper" @click="toSearch">
							<view class="search-text">
								搜索
							</view>
							<image class="search-icon" style="width: 33rpx;height: 33rpx"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/search_icon.png"
								mode=""></image>
						</view>
						
						<!-- 天气信息显示，放在搜索框和消息图标中间 -->
						<view class="weather-container">
							<template v-if="weatherLoading">
								<u-loading size="20" color="#666666"></u-loading>
								<text class="weather-temp">--°</text>
							</template>
							<template v-else>
								<image 
									class="weather-icon" 
									:src="getWeatherIconUrl" 
									mode="aspectFit"
									@error="onWeatherIconError"
									v-if="!weatherIconError"
								></image>
								<u-icon 
									v-else
									name="cloud" 
									size="20" 
									color="#666666"
								></u-icon>
								<view class="weather-temp-wrapper">
									<text class="weather-district">{{weatherDistrict}}</text>
									<text class="weather-temp">{{weatherTemp}}°</text>
								</view>
								
							</template>
						</view>
						
						<!-- 修改消息图标，添加红点提示 -->
						<view class="message-container" @click="toNotice">
							<image class="message-icon"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/msg_icon.png"
								mode=""></image>
							<view class="message-dot" v-if="hasNewMessage">{{newMessageCount}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	let rpxTopx = function(px) {
		let deviceWidth = uni.getSystemInfoSync().windowWidth; //获取设备屏幕宽度
		let rpx = (750 / deviceWidth) * Number(px)
		return Math.floor(rpx);
	}
	import {
		getTicket,
		getMd5Key,
		getAesKey,
		getLoginUserInfo,
		getCurrUserInfo,
		getEtcAccountInfo
	} from '@/common/storageUtil.js'
	import mapAPI from '@/common/api/map.js'
	export default {
		props: {
			customerInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {
				// 添加隐私保护状态
				isPrivacyProtected: true,
				// 添加新消息状态
				hasNewMessage: false,
				newMessageCount: 0,
				// 添加天气相关数据
				weatherTemp: '--',
				weatherDistrict: '--',
				weatherDesc: '加载中...', // 天气描述，直接使用API返回值
				// 添加天气加载状态
				weatherLoading: true,
				// 天气图标加载状态
				weatherIconError: false,
				// 天气分类映射，将复杂天气归类为主要类型
				weatherCategories: {
					'sunny': ['晴天', '晴'],
					'cloudy': ['多云', '阴', '云'],
					'rainy': ['阵雨', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨', '小到中雨', '中到大雨', '大到暴雨', '暴雨到大暴雨', '大暴雨到特大暴雨', '雨'],
					'thunderstorm': ['雷阵雨', '雷阵雨伴有冰雹', '雷'],
					'snowy': ['阵雪', '小雪', '中雪', '大雪', '暴雪', '雨夹雪', '小到中雪', '中到大雪', '大到暴雪', '雪'],
					'foggy': ['雾', '浓雾', '强浓雾', '特强浓雾', '大雾'],
					'hazy': ['霾', '中度霾', '重度霾', '严重霾', '强浓雾'],
					'dusty': ['沙尘暴', '强沙尘暴', '浮尘', '扬沙'],
					'freezing': ['冻雨'],
					'clear': ['无']
				}
			}
		},
		components: {

		},
		computed: {
			letnavHeight() {
				const {
					platform,
					statusBarHeight
				} = uni.getSystemInfoSync()
				let _statusBarHeight = statusBarHeight
				let menuButtonObject = uni.getMenuButtonBoundingClientRect();

				let data = statusBarHeight + menuButtonObject.height + (menuButtonObject.top - statusBarHeight) * 2;
				return menuButtonObject.height
			},
			height() {
				return rpxTopx(this.letnavHeight) + 'rpx'
			},
			marginTop() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect();
				return rpxTopx(menuButtonObject.top) + "rpx"
			},
			headerAccountTop() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect();
				let top = rpxTopx(menuButtonObject.bottom) + 40
				return top + "rpx"
			},
			checkLoginStatus() {
				return !!(getTicket() && getMd5Key() && getAesKey() && this.getUserNo)
			},
			getUserNo() {
				return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length ? getLoginUserInfo().userNo : ''
			},
			getAvatar() {
				if (!this.checkLoginStatus) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				if (this.customerInfo && this.customerInfo.customer_type == 0) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				if (this.customerInfo && this.customerInfo.customer_type == 1) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
			},
			loginInfo() {
				console.log('loginInfo',getLoginUserInfo())
				return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length ? getLoginUserInfo() : null
			},
			// 获取天气对应的图标URL
			getWeatherIconUrl() {
				const weatherType = this.getWeatherType();
				// 使用本地静态资源
				const iconBasePath = 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/';
				
				// 直接使用天气类型代码匹配本地png文件
				const iconFileName = weatherType === 'default' ? 'cloudy.png' : `${weatherType}.png`;
				
				return iconBasePath + iconFileName;
			},
			// 获取脱敏后的用户名
			getMaskedCustomerName() {
				if (!this.customerInfo || !this.customerInfo.customer_name) return ''
				console.log('customer_name',this.customerInfo.customer_name)
				
				// 个人用户展示姓氏：张**
				if (this.customerInfo.customer_type == 0) {
					const name = this.customerInfo.customer_name
					return name.substring(0, 1) + '**'
				}
				// 单位（展示前四位名称）：广西捷通******
				else if (this.customerInfo.customer_type == 1) {
					const name = this.customerInfo.customer_name
					if (name.length <= 4) return name
					return name.substring(0, 4) + '*'.repeat(name.length - 4)
				}
				
				return this.customerInfo.customer_name
			}
		},
		mounted() {
			// -自定义顶部高度 + 胶囊按钮顶部位置+ 用户信息高度+ 用户距离胶囊marginTop+广告marginTop
			let top = -436 + rpxTopx(uni.getMenuButtonBoundingClientRect().bottom) + 66 + 40 + 30
			let style = {
				'marginTop': top + 'rpx',
			}
			this.$emit('on-style', style);
			
			// 从本地存储读取隐私设置
			try {
				const privacySetting = uni.getStorageSync('privacyProtection')
				if (privacySetting !== '') {
					this.isPrivacyProtected = privacySetting === 'true'
				}
			} catch(e) {
				console.log('读取隐私设置失败', e)
			}
			
			// 检查是否有新消息
			this.checkNewMessage()
			
			// 获取天气信息
			this.getLocationAndWeather()
		},
		methods: {
			toSearch() {
				uni.navigateTo({
					url: '/pagesD/home/<USER>/search/index'
				})
			},
			toNotice() {
				uni.navigateTo({
					url: `/pagesD/home/<USER>/msgList/index?hasNewMessage=${this.hasNewMessage? 1 : 0}`
				})
				
				// 清除新消息红点
				this.hasNewMessage = false
			},
			customerChange() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList'
				})
			},
			goLoginHandle() {
				uni.reLaunch({
					url: '/pagesD/login/p-login'
				})
			},
			// 切换隐私保护开关
			togglePrivacy() {
				this.isPrivacyProtected = !this.isPrivacyProtected
				
				// 保存设置到本地存储
				try {
					uni.setStorageSync('privacyProtection', this.isPrivacyProtected.toString())
				} catch(e) {
					console.log('保存隐私设置失败', e)
				}
			},
			// 检查是否有新消息
			checkNewMessage() {
				// 如果未登录则跳过
				if (!this.checkLoginStatus) return
				
				// 从接口获取消息列表，判断是否有未读消息
				let params = {
					custMastId: getEtcAccountInfo().custMastId || '',
					netUserId: getLoginUserInfo().userIdStr,
					isReadType: '0' // 0: 未读, 1: 已读
				}
				
				this.$request.post(mapAPI.messageList, {
					data: params
				}).then(res => {
					if (res.code == 200 && res.data) {
						// 假设未读消息有一个标志，实际情况需根据接口返回结构判断
						const hasUnread = res.data.some(item => item.isRead == 0)
						const newMessageCount = res.data.filter(item => item.isRead == 0).length
						this.hasNewMessage = hasUnread
						this.newMessageCount = newMessageCount
					}
				}).catch(err => {
					console.log('获取消息列表失败', err)
				})
			},
			// 获取位置和天气信息
			async getLocationAndWeather() {
				this.weatherLoading = true;
				
				try {
					// 获取用户位置
					const location = await this.getCurrentLocation();
					// 获取天气信息
					await this.fetchWeatherData(location.latitude, location.longitude);
				} catch (error) {
					console.error('获取天气信息失败:', error);
					// 设置默认值
					this.weatherTemp = '--';
					this.weatherDesc = '获取失败';
				} finally {
					this.weatherLoading = false;
				}
			},
			
			// 获取当前位置
			getCurrentLocation() {
				return new Promise((resolve, reject) => {
					uni.getLocation({
						type: 'gcj02', // 使用国测局坐标系，与腾讯地图API兼容
						success: resolve,
						fail: reject
					});
				});
			},
			
			// 获取天气数据
			async fetchWeatherData(latitude, longitude) {
				const key = this.$store.state.mapKey;
				
				return new Promise((resolve, reject) => {
					uni.request({
						url: `https://apis.map.qq.com/ws/weather/v1/`,
						data: {
							key: key,
							location: `${latitude},${longitude}`,
							type: 'now' // 获取实时天气
						},
						success: (res) => {
							const { data } = res;
							
							if (data.status === 0 && data.result && data.result.realtime && data.result.realtime.length > 0) {
								const weather = data.result.realtime[0];
								this.weatherDistrict = weather.district;
								this.weatherTemp = weather.infos.temperature;
								this.weatherDesc = weather.infos.weather;
								// 重置图标错误状态
								this.weatherIconError = false;
								console.log('天气获取成功:', weather.infos);
								resolve(weather);
							} else {
								console.error('腾讯地图天气API返回错误:', data);
								reject(new Error('天气API返回异常'));
							}
						},
						fail: (error) => {
							console.error('天气API请求失败:', error);
							reject(error);
						}
					});
				});
			},

			// 天气图标加载错误处理
			onWeatherIconError() {
				console.log('天气图标加载失败，使用默认图标');
				this.weatherIconError = true;
			},
			
			// 获取天气类型分类
			getWeatherType() {
				const weatherDesc = this.weatherDesc;
				if (!weatherDesc || weatherDesc === '加载中...' || weatherDesc === '获取失败') {
					return 'default';
				}
				
				// 遍历天气分类，找到匹配的类型
				for (const [category, keywords] of Object.entries(this.weatherCategories)) {
					if (keywords.some(keyword => weatherDesc.includes(keyword))) {
						return category;
					}
				}
				
				// 如果没有匹配到，返回默认类型
				return 'default';
			},
			
			// 脱敏手机号的方法
			getMaskedLoginName(name) {
				if (!name) return '';
				name = name.toString();
				// 如果长度小于等于7，直接返回原始值
				if (name.length <= 7) return name;
				// 提取前三位和后四位，中间用星号替代
				return name.substring(0, 3) + '*'.repeat(name.length - 7) + name.substring(name.length - 4);
			},
		},
	}
</script>

<style lang="scss">
	.header {
		width: 100%;

		.search-wrapper {
			position: relative;
			width: 180rpx;
			height: 62rpx;
			background: rgba(255, 255, 255, 0.77);
			box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.07);
			border-radius: 37rpx 37rpx 37rpx 37rpx;
			flex-shrink: 0; /* 防止被压缩 */

			.search-text {
				text-align: center;
				line-height: 62rpx;
				color: #999;
			}

			.search-icon {
				position: absolute;
				right: 16rpx;
				top: 14rpx
			}
		}
		
		/* 天气信息容器样式 */
		.weather-container {
			display: flex;
			align-items: center;
			margin: 0 10rpx 0 4rpx; /* 从15rpx调整为12rpx，两侧减少边距 */
			height: 62rpx;
			padding: 0 20rpx; /* 增加内部padding从15rpx到20rpx */
			flex-shrink: 0; /* 防止被压缩 */
			min-width: 120rpx; /* 从110rpx增加到120rpx，扩大最小宽度 */
			
			.weather-icon {
				width: 60rpx;
				height: 60rpx;
				flex-shrink: 0;
			}
			.weather-temp-wrapper{
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				color: #666666;
				font-size: 20rpx;
				margin-left: 10rpx; /* 从8rpx增加到10rpx，给图标更多空间 */
				.weather-temp {
					margin-top: 4rpx;
				}
			}

		}

		/* 消息图标容器，用于定位红点 */
		.message-container {
			position: relative;
			margin-left: 12rpx;
			flex-shrink: 0; /* 防止被压缩 */
		}

		.message-icon {
			width: 40rpx;
			height: 40rpx;
		}
		
		/* 消息红点样式 
			position: absolute;
			top: -3rpx;
			right: -3rpx;
			width: 16rpx;
			height: 16rpx;
			border-radius: 50%;
			background-color: #FF4D4F;
		*/
		.message-dot {
			position: absolute;
			top: -5rpx;
			right: -9rpx;
			width: 27rpx;
			height: 18rpx;
			background: #F54657;
			border-radius: 43rpx;
			font-size: 12rpx;
			color: #fff;
			text-align: center;
			line-height: 18rpx;
		}

		.header-wrapper {
			width: 100%;
			position: relative;

			.header-title {
				height: 437rpx;
				position: relative;

				.bg-image {
					width: 100%;
					height: 437rpx;

					.img {
						display: block;
						width: 100%;
						height: 100%;
					}
				}

				.title {
					color: #fff;
					font-size: 32rpx;

					font-weight: 600;
					color: #333333;
					position: absolute;
					width: 100%;
					z-index: 5;
					text-align: center;
					font-weight: bold;
				}
			}

			.header-account {
				width: 100%;
				padding: 0 30rpx;
				position: absolute;
				display: flex;
				align-items: center;
			}

			.header-account .header-account_hd {
				width: 66rpx;
				height: 66rpx;
				flex-shrink: 0;
			}

			.header-account .header-account_hd .img {
				display: block;
				width: 100%;
				height: 100%;
			}

			.header-account .header-account_bd {
				flex: 1;
				margin-left: 14rpx;
				max-width: 55%;
				min-width: 100rpx;
			}

			.header-account .header-account_bd .name {
				font-weight: 400;
				color: #323435;
				font-size: 28rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				display: flex;
				align-items: center;
				
				.name-text {
					max-width: calc(100% - 55rpx);
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					display: inline-block;
				}
				
				.ellipsis {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
				
				/* 隐私保护按钮样式 */
				.privacy-btn {
					margin-left: 15rpx;
					flex-shrink: 0;
					
					.privacy-icon {
						width: 32rpx;
						height: 32rpx;
					}
				}
			}

			.header-account .header-account_ft {
				width: 168rpx;
				height: 50rpx;
				background: rgba(199, 199, 199, 0.27);
				border-radius: 24rpx;
				position: relative;
			}

			.header-account .header-account_ft .desc {
				font-size: 28rpx;
				font-weight: 400;
				color: #858686;
				padding-right: 8rpx;
			}

			.header-account .header-account_ft:after {
				content: " ";
				display: inline-block;
				height: 6px;
				width: 6px;
				border-width: 1px 1px 0 0;
				border-color: #858686;
				border-style: solid;
				-webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
				transform: matrix(.71, .71, -.71, .71, 0, 0);
				position: relative;
				top: -2px;
				position: absolute;
				top: 50%;
				margin-top: -4px;
				right: 6px;
			}
		}
	}
</style>