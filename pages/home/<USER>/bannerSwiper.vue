<template>
	<view class="banner-swiper">
		<swiper 
			class="swiper" 
			:indicator-dots="true" 
			:autoplay="true" 
			:interval="3000" 
			:duration="1000"
			indicator-color="rgba(255, 255, 255, 0.5)"
			indicator-active-color="#ffffff"
			:circular="true">
			<swiper-item v-for="(item, index) in bannerList" :key="index" @click="handleBannerClick(item)">
				<view class="swiper-item">
					<image :src="item.imageUrl" mode="aspectFill" class="banner-image"></image>
					<view class="banner-content">
						<view class="banner-title">{{ item.title }}</view>
						<view class="banner-subtitle" v-if="item.subtitle">{{ item.subtitle }}</view>
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
export default {
	name: 'BannerSwiper',
	props: {
		bannerList: {
			type: Array,
			default: () => []
		}
	},
	methods: {
		handleBannerClick(banner) {
			console.log('Banner点击:', banner);
			
			// 根据linkType判断跳转类型
			if (banner.linkType === 'miniprogram') {
				// 跳转小程序
				uni.navigateToMiniProgram({
					appId: banner.appId || 'wx416f21d2e7c54de9',
					path: banner.path || '/pages/home/<USER>',
					envVersion: banner.envVersion || 'trial',
					extraData: banner.extraData || {},
					success: (res) => {
						console.log('跳转小程序成功', res);
					},
					fail: (err) => {
						console.error('跳转小程序失败', err);
						uni.showToast({
							title: '跳转失败',
							icon: 'none'
						});
					}
				});
			} else if (banner.linkType === 'article') {
				// 跳转文章页面
				uni.navigateTo({
					url: banner.url
				});
			} else if (banner.linkType === 'webview') {
				// 跳转网页
				uni.navigateTo({
					url: `/pages/webview/index?url=${encodeURIComponent(banner.url)}`
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.banner-swiper {
	margin: 20rpx;
	border-radius: 12rpx;
	overflow: hidden;
	
	.swiper {
		width: 100%;
		height: 320rpx;
		border-radius: 12rpx;
		
		.swiper-item {
			position: relative;
			width: 100%;
			height: 100%;
			
			.banner-image {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}
			
			.banner-content {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
				padding: 20rpx;
				
				.banner-title {
					color: #ffffff;
					font-size: 32rpx;
					font-weight: 600;
					line-height: 44rpx;
					margin-bottom: 8rpx;
				}
				
				.banner-subtitle {
					color: rgba(255, 255, 255, 0.8);
					font-size: 26rpx;
					line-height: 36rpx;
				}
			}
		}
	}
}
</style> 