<template>
	<view class="high-tabs">
		<view class="h-title">高速服务</view>
		<u-tabs :list="highList" :is-scroll="false" inactive-color='#666666' :current="current" activeColor='#0066E9'
			@change="change">
		</u-tabs>

		<highService v-if="current == 0" :fixedHeight="true" height="840rpx"></highService>
		<etcNet v-if="current == 1"></etcNet>
		<etcPark v-if="current == 2"></etcPark>
		<highPlay v-if="current == 3"></highPlay>
	</view>
</template>
<script>
	import highService from '../highService/highService.vue'
	import etcNet from '../etcNet/etcNet.vue'
	import etcPark from '../etcPark/etcPark.vue'
	import highPlay from '../highPlay/highPlay.vue'
	export default {
		components: {
			highService,
			etcNet,
			etcPark,
			highPlay
		},
		data() {
			return {
				current: 0,
				highList: [{
						name: '服务区'
					},
					{
						name: 'ETC网点'
					},
					{
						name: 'ETC停车场'
					},
					{
						name: '嗨玩八桂'
					},
				],
			};
		},
		methods: {
			change(index) {
				this.current = index;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.high-tabs {
		background: #FFFFFF;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		font-family: PingFang SC, PingFang SC;
		margin: 0 20rpx;

		.h-title {
			padding: 20rpx 20rpx 10rpx 20rpx;
			font-weight: 600!important;
			font-size: 30rpx!important;
			color: #333333!important;
			line-height: 40rpx;
		}
	}
</style>