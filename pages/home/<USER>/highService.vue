<template>
	<view class="high-service">
		<highSearch :placeholder="'请输入服务区名称'" @click="search" showDistrictPicker>
			<!-- <template> 
				<view class="right-item" @click="go">
					<view style="color: #0066E9;">旅岛优选，</view>
					<view>最新活动戳我</view>
					<image class="right-icon"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/point_icon.png"
						mode=""></image>
				</view>
			</template> -->
		</highSearch>
		<!-- <view class="banner-wrapper">
			<travel-banner @bannerClick="handleBannerClick"></travel-banner>
		</view> -->
		<view v-if="text" class="tips-text">
			{{text}}
		</view>
		<scroll-view class="item-wrapper" :style="{height:contentHeight}" scroll-y="true" :scroll-top="scrollTop"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<seriveItem @star="star" v-for="(item,index) in netList" :item="item" :index="index + 1" :key="index">
			</seriveItem>
			<load-more v-if="!searchParams.name && !searchParams.cityCode" :loadStatus="noticeLoadStatus" />
		</scroll-view>
	</view>
</template>
<script>
	import highSearch from '../component/highSearch'
	import seriveItem from './serivceItem.vue'
	import loadMore from '@/pages/home/<USER>/load-more/index.vue';
	// import travelBanner from '@/components/common/travel-banner.vue';
	import {
		getLoginUserInfo,
		getTicket,
	} from '@/common/storageUtil.js'
	export default {
		components: {
			highSearch,
			seriveItem,
			loadMore,
			// travelBanner
		},
		// 添加props支持固定高度模式
		props: {
			// 是否使用固定高度模式
			fixedHeight: {
				type: Boolean,
				default: false
			},
			// 固定高度值
			height: {
				type: String,
				default: '840rpx'
			}
		},
		data() {
			return {
				showStarFlag: false,
				loadFlag: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				text: '',
				longitude: '',
				latitude: '',
							netList: [],
			contentHeight: '100vh', // 动态计算的高度，如果是固定模式则使用props传入的height
				systemInfo: null,
				pageNo: 1,
				pageSize: 20,
				searchParams: {
					name: '',
					cityCode: ''
				},
				// showDistance: true
			};
		},
			watch: {
		// 监听提示文本变化，重新计算高度
		text() {
			if (!this.fixedHeight) {
				this.$nextTick(() => {
					this.calculateContentHeight();
				});
			}
		}
	},
		created() {
			// 如果是固定高度模式，直接设置高度，无需获取系统信息
			if (this.fixedHeight) {
				this.contentHeight = this.height;
				console.log('固定高度模式初始化:', this.contentHeight);
			} else {
				// 动态高度模式需要获取系统信息
				this.getSystemInfo();
			}
			this.getLocal()
		},
		methods: {
			// 获取系统信息，计算内容区域高度
			getSystemInfo() {
				uni.getSystemInfo({
					success: (res) => {
						this.systemInfo = res;
						this.calculateContentHeight();
					}
				});
			},
			// 计算内容区域高度
			calculateContentHeight() {
				// 1. 如果明确指定固定高度模式，直接使用props传入的高度
				if (this.fixedHeight) {
					this.contentHeight = this.height;
					console.log('使用固定高度模式:', this.contentHeight);
					return;
				}
				
				// 2. 自动检测：检查是否在tab组件环境中
				// 通过检查父组件DOM结构来判断使用场景
				this.$nextTick(() => {
					try {
						// 在uni-app中，可能需要通过其他方式获取DOM元素
						const parentElement = this.$el && this.$el.parentElement;
						let isInTabEnvironment = false;
						
						if (parentElement) {
							// 检测是否在tab组件中（向上遍历3层DOM）
							let currentElement = parentElement;
							for (let i = 0; i < 3; i++) {
								if (currentElement) {
									const className = currentElement.className || '';
									// 检测是否包含tab相关的类名
									if (className.includes('u-tabs') || 
										className.includes('tab') || 
										className.includes('high-tabs')) {
										isInTabEnvironment = true;
										break;
									}
									currentElement = currentElement.parentElement;
								}
							}
						}
						
						// 3. 根据环境决定高度计算方式
						if (isInTabEnvironment) {
							// 在tab环境中使用固定高度
							this.contentHeight = this.height;
							console.log('自动检测到tab环境，使用固定高度:', this.contentHeight);
						} else {
							// 独立页面环境，使用动态高度计算
							this.calculateDynamicHeight();
						}
					} catch (error) {
						console.warn('自动检测失败，使用动态高度:', error);
						// 检测失败时降级到动态高度计算
						this.calculateDynamicHeight();
					}
				});
			},
			
			// 动态高度计算逻辑（从原calculateContentHeight方法拆分出来）
			calculateDynamicHeight() {
				if (!this.systemInfo) return;
				
				// 转换px为rpx (750rpx = windowWidth px)
				const rpxRatio = 750 / this.systemInfo.windowWidth;
				
				// 各个UI元素的高度计算（单位：rpx）
				const searchHeight = 104; // 搜索框区域高度
				const bannerHeight = 106; // banner高度 (86rpx + 20rpx margin)
				const tipsHeight = this.text ? 120 : 0; // 提示文本高度
				const rightItemHeight = 0; // 右侧旅岛优选被注释了，所以为0
				
				// 计算列表区域可用高度
				let availableHeight = this.systemInfo.windowHeight * rpxRatio - searchHeight - bannerHeight - tipsHeight - rightItemHeight;
				
				// 确保至少有足够的高度显示几个条目
				availableHeight = Math.max(availableHeight, 600);
				
				this.contentHeight = availableHeight + 'rpx';
				console.log('动态计算的内容高度:', this.contentHeight);
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				console.log('this.noticeLoadStatus ', this.noticeLoadStatus)
				if (this.noticeLoadStatus == 3 || this.loadFlag || this.searchParams.name || this.searchParams.cityCode) return;
				let self = this;
				setTimeout(function() {
					if (self.noticeLoadStatus != 2) {
						self.pageNo = self.pageNo + 1;
					}
					self.getNetList();
				}, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
			getNetList() {
				if (!this.longitude || !this.latitude) {
					this.text = '获取不到当前位置信息，无法加载服务区列表'
					return
				}
				this.noticeLoadStatus = 1;
				this.loadFlag = true
				// this.text = '加载列表中...' //
				// console.log('转换前====>>>>', this.longitude, this.latitude)
				// let changeObj = this.qqMapTransBMap(this.longitude, this.latitude)
				// console.log('转换后====>>>>', changeObj.longitude, changeObj.latitude)
				let params = {
					name: this.searchParams.name || '',
					cityCode: this.searchParams.cityCode || '',
					cityId: this.searchParams.cityCode || '',  // 新增cityId字段
					longitude: this.longitude,
					latitude: this.latitude,
					pageNo: this.pageNo,
					pageSize: this.pageSize
				}
				this.$request
					.post(this.$interfaces.serviceAreaList, {
						data: params
					})
					.then((res) => {
						this.loadFlag = false
						console.log('服务区数据=====>>>>>', res.data)
						if (res.code == 200) {
														if (res.data.length > 0) {
								this.noticeLoadStatus = 4
								if (this.pageNo == 1) {
									this.netList = res.data
								} else {
									this.netList = this.netList.concat(res.data)
								}
								// this.calculateDistance(res.data)
								//设置条目状态
								this.setListFlag(this.netList)
								//设置营业状态（模拟数据）
								this.setMockStatus(this.netList)
								//设置收藏状态
								if (getTicket()) {
									//登录过直接加载收藏信息
									this.getCollectList()
								}
															this.text = '' //重置错误信息
							// 重新计算高度（移除了简单的条目数量计算）
							if (!this.fixedHeight) {
								this.calculateContentHeight()
							}
							} else {
								// 数据为空时的处理
								if (this.pageNo == 1) {
									this.netList = res.data || []  // 重置列表数据
									// 判断是否为搜索状态，显示不同的提示信息
									if (this.searchParams.name || this.searchParams.cityCode) {
										this.text = '未查找到该服务区信息，请您确认输入正确的服务区名称'
									} else {
										this.text = '暂无数据'
									}
								} else {
									this.noticeLoadStatus = 3;
								}
								// 数据为空时也重新计算高度
								if (!this.fixedHeight) {
									this.calculateContentHeight()
								}
							}
						} else {
							if (this.pageNo != 1) {
								this.noticeLoadStatus = 2;
							}
													// this.text = '抱歉，加载列表失败，请稍后重试'
						// 请求失败时也重新计算高度
						if (!this.fixedHeight) {
							this.calculateContentHeight()
						}
						}
					})
					.catch((error) => {
						this.loadFlag = false
						this.noticeLoadStatus = 2;
											// this.text = '抱歉，加载列表失败，请稍后重试'
					// 错误时也重新计算高度
					if (!this.fixedHeight) {
						this.calculateContentHeight()
					}
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			go() {
				uni.showModal({
					title: "提示",
					content: '即将上线,敬请期待',
					showCancel: false
				})
			},
			star(type, item) {
				if (!getTicket()) {
					//没登录过在收藏时再加载登录信息去登录
					this.getCollectList()
				}
				console.log('type', type)
				console.log('item', item)
				if (this.showStarFlag) return
				this.showStarFlag = true
				//收藏服务区
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					collectTitle: item.name,
					// id: item.id,
					collectType: 1
				}
				let url = ''
				if (type == 'star') {
					url = this.$interfaces.addCollect
				} else {
					url = this.$interfaces.delCollect
					params.id = item.starId
				}
				this.$request
					.post(url, {
						data: params
					})
					.then((res) => {
						this.showStarFlag = false
						console.log('收藏结果=====>>>>>', res)
						if (res.code == 200) {
							uni.showToast({
								icon: 'none',
								title: type == 'star' ? '收藏成功，可在我的收藏中查看' : '已取消收藏',
								// title: '已取消收藏',
								duration: 2000
							})
							this.setStarStatus(type, item, res.data.id)
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								// title: '已取消收藏',
								duration: 2000
							})
						}
					})
					.catch((error) => {
						this.showStarFlag = false
						uni.showToast({
							icon: 'none',
							title: error.msg,
							// title: '已取消收藏',
							duration: 2000
						})
					})
			},
			getCollectList() {
				//收藏服务区
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					collectType: 1
				}
				this.$request
					.post(this.$interfaces.getCollectList, {
						data: params
					})
					.then((res) => {
						console.log('收藏查询=====>>>>>', res)
						if (res.code == 200) {
							let starList = res.data
							if (starList.length > 0) {
								this.netList.forEach((item, index) => {
									starList.forEach((item2) => {
										if (item.name == item2.collectTitle) {
											this.$set(this.netList[index], 'starFlag', true)
											this.$set(this.netList[index], 'starId', item2.id)
										}
									})
								})
							}
						}
					})
					.catch((error) => {})
			},
			setStarStatus(type, listItem, starId) {
				this.netList.forEach((item, index) => {
					if (item.name == listItem.name) {
						this.$set(this.netList[index], 'starFlag', type == 'star' ? true : false)
						this.$set(this.netList[index], 'starId', starId)
						// this.$set(this.netList[index], 'starFlag', false)
					}
				})
			},
			search(params) {
				console.log('搜索参数==', params)
				if (typeof params === 'string') {
					// 兼容旧的字符串参数
					this.searchParams = { name: params, cityCode: '' }
				} else {
					// 新的对象参数
					this.searchParams = params
				}
				this.pageNo = 1
				this.getNetList()
			},
							getLocal() {
					uni.getLocation({
						type: 'wgs84',
						success: (res) => {
							console.log('当前位置的经度：' + res.longitude);
							console.log('当前位置的纬度：' + res.latitude);
							this.longitude = res.longitude
							this.latitude = res.latitude
							this.getNetList()
						},
											fail: (fail) => {
						this.text = '获取不到当前位置信息，无法加载服务区列表'
						// 位置获取失败时也重新计算高度
						if (!this.fixedHeight) {
							this.calculateContentHeight()
						}
					}
					});
				},
			//设置服务区条目的显示状态
			setListFlag(list) {
				list.forEach((item, index) => {
					if (item.accessibleBathroom > 0 || item.thirdToilet > 0 || item.easyToilet > 0) {
						this.$set(this.netList[index], 'toiletFlag', true)
					}
					if (item.parkingArea > 0) {
						this.$set(this.netList[index], 'parkFlag', true)
					}
					if (item.tankers > 0) {
						this.$set(this.netList[index], 'tankersFlag', true)
					}
					if (item.chargingPile > 0) {
						this.$set(this.netList[index], 'chargingFlag', true)
					}
					if (item.storeArea > 0) {
						this.$set(this.netList[index], 'storeFlag', true)
					}
					if (item.restaurantCount > 0) {
						this.$set(this.netList[index], 'restaurantFlag', true)
					}
				})
				// console.log('this.netcc===>>>', list, this.netList)
			},
			//设置模拟营业状态（用于演示，实际应从API获取）
			setMockStatus(list) {
				list.forEach((item, index) => {
					// 模拟状态：大部分服务区正常营业，少数暂停营业
					let status = Math.random() > 0.2 ? 1 : 0; // 80%概率正常营业
					this.$set(this.netList[index], 'status', status)
				})
			},
			// 处理banner点击事件
			handleBannerClick(item) {
				console.log('服务区页面 - banner点击:', item);
				// TODO: 根据需求添加具体的跳转逻辑
			}
		}
	};
</script>

<style lang="scss" scoped>
	.high-service {
		font-family: PingFang SC, PingFang SC;
		background-color: #fff;
		.right-item {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 26rpx;
			line-height: 36rpx;
			text-align: left;
			font-style: normal;

			.right-icon {
				width: 32rpx;
				height: 32rpx;
				margin: 0 20rpx 0 16rpx;
			}
		}
		.banner-wrapper {
			margin: 0 20rpx;
		}
		.item-wrapper {
			overflow: hidden;
			height: 840rpx;
			margin-bottom: 20rpx;
		}

		.tips-text {
			width: 70%;
			margin: 0 auto;
			text-align: center;
			margin-bottom: 20rpx;
			height: 120rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #999999;
		}
	}
</style>