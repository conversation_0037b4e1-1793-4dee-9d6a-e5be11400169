<template>
	<view v-if="showPop">
		<view class="handleTips">
			<view class="title">{{popName}}
				<image v-if="!successFlag" src="../../../static/etc/close.png" class="image" @click="close"></image>
			</view>

			<view class="content" v-if="!successFlag">
				<view class="text" style="text-indent: 0;">
					尊敬的ETC用户:
				</view>
				<view class="text">广西捷通高速科技有限公司(以下简称捷通公司)与广西铁投吉鸿融资担保有限公司的合作终止。您办理的ETC日日通产品服务全部由捷通公司承接。需进一步了解详情，请点击<text
						@click="preview" style="color: #5591ff">【ETC账户服务优化说明】</text>，或如有疑问，请点击“联系客服”或拨打客服电话0771-5896333。
				</view>
			</view>
			<view class="content" :class="successFlag?'content-center':''" v-else>
				操作成功！
			</view>
			<view class="bottom" :class="successFlag?'bottom-center':''">
				<button class="btn active" :class="successFlag?'btn-center':''"
					@click="agree">{{successFlag?'确定':'同意'}}</button>
				<button class="btn disagree" @click="toWebView" v-if="!successFlag">联系客服</button>
			</view>
		</view>
		<view class="mask"></view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import {
		getTicket,
		getLoginUserInfo,
		getCurrUserInfo
	} from '@/common/storageUtil.js'
	import tLoading from '@/components/common/t-loading.vue'
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				successFlag: false, //成功操作
				// privacyContractName: "",
				// showPop: false,
				result: {}, //吉鸿车辆返回数据
				mobile: ''
				// privacyResolves: new Set()
			}
		},
		props: {
			showPop: {
				type: Boolean,
				default: false
			},
			popName: {
				type: String,
				default: "温馨提示"
			},
			result: {
				type: Object,
				default: {}
			},
		},
		created() {

		},
		watch: {
			showPop(val) {
				if (val) {
					this.getLoginMobile()
				}
			}
		},
		methods: {
			//获取登录相信信息
			getLoginMobile() {
				let params = {
					userNo: getLoginUserInfo().userNo,
				};
				this.$request.post(this.$interfaces.getAccountInfo, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.mobile = res.data.mobile

					} else {
						// uni.showModal({
						// 	title: "提示",
						// 	content: res.msg,
						// 	showCancel: false,
						// });
					}

				}).catch((error) => {
					// uni.showModal({
					// 	title: "提示",
					// 	content: error.msg,
					// 	showCancel: false,
					// });
				})
			},
			createUserSign(signType) {
				if (!this.mobile) {
					uni.showModal({
						title: "提示",
						content: '获取不到互联网手机号，请尝试重新加载页面获取。',
						showCancel: false,
					});
					return
				}
				let vehicles = this.result.vehicleList
				console.log('签约==========>>>>')
				let params = {
					source: '1', //存在etc用户
					customerId: getCurrUserInfo().customer_id,
					// custType: '1', // customerId必填
					vehicles: vehicles,
					signName: getCurrUserInfo().customer_name, //当前ETC账户
					signPhone: this.mobile, //获取的互联网mobile
					signIdNo: getLoginUserInfo().userNo, //当前互联网登录userNo
					marketId: this.result.marketId,
					businessType: this.result.bizType, //7补签
					productType: this.result.productType,
					isOwner: '1' //默认本人
				}

				// console.log('prams===========>', params)

				// let data = {
				// 	data: params,
				// };
				let data = {
					routePath: this.$interfaces.newSignPreview.method,
					bizContent: params
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false;
						if (res.code == 200) {
							console.log('签章返回===>>>', res)
							let signKey = res.data.signKey
							if (signType == 'sign') {
								//直接签署
								this.confirm(signKey)
							} else {
								let pdfInfo = res.data.data
								let signUrl = 'https://portal.gxetc.com.cn/new-agreement?type=' + signType +
									'&signInfo=' +
									encodeURIComponent(JSON.stringify(
										pdfInfo))

								uni.navigateTo({
									url: "/pagesB/signWebview/signWebview?ownPath=" + encodeURIComponent(JSON
										.stringify(
											signUrl))
								})
							}


						} else {
							uni.showModal({
								title: "错误",
								content: res.msg,
								showCancel: false,
							});
						}
					})
					.catch((err) => {
						this.isLoading = false;
						uni.showModal({
							title: "错误",
							content: err.msg,
							showCancel: false,
						});
					});
			},
			// 同意
			agree() {
				if (!this.successFlag) {
					this.createUserSign('sign')
				} else {
					this.successFlag = false
					this.$emit('update:showPop', false)
				}

			},
			confirm(signKey) {
				this.isLoading = true
				let data = {
					routePath: this.$interfaces.signConfirmByB.method,
					bizContent: {
						signKey: signKey,
						netUserNo: getLoginUserInfo().userNo
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						this.isLoading = false
						if (res.code == 200) {
							console.log('完成吉鸿签约====>>>', res)
							this.successFlag = true
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
							})
						}
					})
					.catch((error) => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false,
						})
					})
			},
			close() {
				this.$emit('update:showPop', false)
			},
			// 联系客服
			toWebView() {
				if (!getTicket()) {
					uni.showModal({
						title: '提示',
						content: '请先登录',
						success: (res) => {
							if (res.confirm) {
								uni.reLaunch({
									url: '/pagesD/login/p-login'
								})
							}
						}
					})
					return
				}
				var callcenter =
					'https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027'
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' +
						encodeURIComponent(callcenter)
				})
			},
			// 打开隐私弹窗
			preview() {
				this.createUserSign('preview')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.handleTips {
		width: 620rpx;
		// height: 560rpx;
		height: auto;
		background-color: #fff;
		border-radius: 24rpx;
		position: fixed;
		z-index: 999;
		top: 30%;
		margin: 0 67rpx;

		.title {
			position: relative;
			font-size: 32rpx;
			font-weight: 600;
			color: #333333;
			/* margin-top: 20rpx; */
			text-align: center;
			/* height: 32rpx; */
			border-bottom: 1rpx solid #f8f8f8;
			padding: 20rpx 0;

			.image {
				position: absolute;
				width: 50rpx;
				height: 50rpx;
				right: 16rpx;
				top: 16rpx;
			}
		}

		.content {
			margin-top: 40rpx;
			padding: 0 44rpx;

			.text {
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 48rpx;
				text-indent: 2em;
			}
		}

		.content-center {
			text-align: center;
		}

		.bottom {
			display: flex;
			justify-content: space-around;
			margin-top: 40rpx;

			.btn {
				width: 50%;
				height: 88rpx;
				line-height: 88rpx;
				text-align: center;
				font-size: 28rpx;
				color: #333333;
				background-color: #fff;
			}

			.disagree {
				border-top: 1rpx solid #E7E7E7;
				border-radius: 0px 0px 24rpx 0px;
				color: #0066E9;
			}

			.active {
				background: #0066E9;
				color: #fff;
				border: none;
				border-radius: 0px 0px 0px 24rpx;

			}

			.btn-center {
				border-radius: 24rpx
			}

			.btn::after {
				border: none;
			}
		}

		.bottom-center {
			padding-bottom: 40rpx;
		}
	}

	.uicon-close {
		position: absolute;
		right: 5rpx;
		top: 5rpx;
	}

	.mask {
		position: fixed;
		width: 100%;
		height: 100%;
		z-index: 998;
		top: 0;
		background-color: #000;
		opacity: .6;
	}
</style>