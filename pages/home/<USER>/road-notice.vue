<template>
  <view class="road-notice">
    <!-- 标题 -->
    <view class="info-title">路况信息</view>
    <!-- 卡片容器 -->
    <view class="cards-container">
      <!-- 第一个卡片 -->
      <view 
        class="info-card card-transition" 
        :class="[getCardStyling(displayItem1).class, {'card-fade-out': !showCard1}]" 
        v-if="displayItem1"
        v-show="showCard1 || isTransitioning"
        @tap.stop.prevent="goToMapLocation(displayItem1)"
      >
        <!-- <view class="card-header">
          <view class="card-tag" :class="{ 'jam-tag': displayItem1.infoType === 'jam' }">{{ getCardStyling(displayItem1).tag }}</view>
          <view class="road-name">{{ getRoadName(displayItem1) }}</view>
          <view class="voice-btn" @click.stop="speek(getVoiceContent(displayItem1))">
            <image :src="getCardStyling(displayItem1).voiceIcon"></image>
          </view>
        </view> -->
        <view class="card-type">
          <view class="card-tag" :class="{ 'jam-tag': displayItem1.infoType === 'jam' }">{{ getCardStyling(displayItem1).tag }}</view>
        </view>
        <view class="card-content">
          <view class="road-info">{{ getDescription(displayItem1) }}</view>
          <!-- <view class="road-detail">
            <text>{{ getDetailInfo(displayItem1) }}</text>
          </view> -->
        </view>
        <view class="card-voice" @click.stop="speek(getVoiceContent(displayItem1))">
          <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/voice.png"></image>
        </view>
      </view>
      
      <!-- 第二个卡片 -->
      <view 
        class="info-card card-transition" 
        :class="[
          getCardStyling(displayItem2).class, 
          {'card-shift-up': isShifting}
        ]" 
        v-if="displayItem2"
        v-show="showCard2 || isTransitioning"
        @tap.stop.prevent="goToMapLocation(displayItem2)"
      >
        <!-- <view class="card-header">
          <view class="card-tag" :class="{ 'jam-tag': displayItem2.infoType === 'jam' }">{{ getCardStyling(displayItem2).tag }}</view>
          <view class="road-name">{{ getRoadName(displayItem2) }}</view>
          <view class="voice-btn" @click.stop="speek(getVoiceContent(displayItem2))">
            <image :src="getCardStyling(displayItem2).voiceIcon"></image>
          </view>
        </view> -->
        <view class="card-type">
          <view class="card-tag" :class="{ 'jam-tag': displayItem2.infoType === 'jam' }">{{ getCardStyling(displayItem2).tag }}</view>
        </view>
        <view class="card-content">
          <view class="road-info">{{ getDescription(displayItem2) }}</view>
          <!-- <view class="road-detail">
            <text>{{ getDetailInfo(displayItem2) }}</text>
          </view> -->
        </view>
        <view class="card-voice" @click.stop="speek(getVoiceContent(displayItem2))">
          <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/voice.png"></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const plugin = requirePlugin("WechatSI");
const innerAudioContext = uni.createInnerAudioContext();

export default {
  data() {
    return {
      roadInfoList: [],
      allRoadInfo: [],      // 存储所有路况信息
      displayItem1: null,   // 存储第一个显示槽位的内容
      displayItem2: null,   // 存储第二个显示槽位的内容
      currentRotatingIndex: 0, // 当前轮播索引
      rotationIntervalId: null, // 轮播定时器
      showCard1: true,      // 控制第一个卡片显示/隐藏的标记
      showCard2: true,      // 控制第二个卡片显示/隐藏的标记
      isTransitioning: false, // 标记是否正在过渡中
      isShifting: false,    // 标记是否正在执行上移动画
      workItem: null,
      eventItem: null,
      jamItem: null,
      statusObj: {
        0: "刚上报",
        1: "研判否",
        2: "研判确认",
        3: "处理中",
        10: "处理完成"
      },
      eventSource: {
        1: "疑似拥堵",
        2: "异常拥堵",
        3: "常规拥堵"
      },
      roadType: {
        1: "高速",
        2: "环城及快速路",
        3: "主干",
        4: "次干",
        5: "支干",
        101: "省高速",
        102: "国道",
        103: "省道"
      },
      congestLevel: {
        1: "畅通",
        2: "缓行",
        3: "拥堵",
        4: "严重拥堵"
      }
    };
  },
  created() {
    // 页面创建时，获取路况信息
    this.initRoadInfo();
  },
  beforeDestroy() {
    // 清除轮播定时器
    if (this.rotationIntervalId) {
      clearInterval(this.rotationIntervalId);
      this.rotationIntervalId = null;
    }
  },
  onUnload() {
    // 页面卸载时清除轮播定时器(uni-app页面生命周期)
    if (this.rotationIntervalId) {
      clearInterval(this.rotationIntervalId);
      this.rotationIntervalId = null;
    }
  },
  methods: {
    // 初始化路况信息
    initRoadInfo() {
      // 重置数据
      this.allRoadInfo = [];
      // 获取当前位置，用于请求附近的路况信息
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          // 获取各类路况信息
          this.getEvent(location);
          this.getJamInfo(location);
          this.getWork(location);
        },
        fail: () => {
          // 位置获取失败，使用默认位置（可以根据实际情况调整）
          const defaultLocation = {
            latitude: 22.8170,  // 南宁市中心坐标
            longitude: 108.3669
          };
          this.getEvent(defaultLocation);
          this.getJamInfo(defaultLocation);
          this.getWork(defaultLocation);
        }
      });
    },
    
    // 获取道路事件信息
    getEvent(location) {
      let params = {
        latitude: location.latitude,
        longitude: location.longitude,
        pageNo: 1,
        pageSize: 2
      };
      this.$request
        .post(this.$interfaces.getMapEvent, {
          data: params
        })
        .then(res => {
          let { data } = res;
          if (data && data.length > 0) {
            let eventInfo = data.map(item => {
              return {
                infoType: "event",
                ...item
              };
            });
            this.allRoadInfo = [...this.allRoadInfo, ...eventInfo];
            // 按时间排序，最新的在前面
            this.sortAllRoadInfo();
            // 设置显示和轮播
            this.setupInitialDisplayAndRotation();
          }
        });
    },
    
    // 获取拥堵信息
    getJamInfo(location) {
      let params = {
        latitude: location.latitude,
        longitude: location.longitude,
        pageNo: 1,
        pageSize: 2
      };
      this.$request
        .post(this.$interfaces.getMapJam, {
          data: params
        })
        .then(res => {
          let { data } = res;
          if (data && data.length > 0) {
            let jamInfo = data.map(item => {
              return {
                infoType: "jam",
                ...item
              };
            });
            this.allRoadInfo = [...this.allRoadInfo, ...jamInfo];
            // 按时间排序，最新的在前面
            this.sortAllRoadInfo();
            // 设置显示和轮播
            this.setupInitialDisplayAndRotation();
          }
        });
    },
    
    // 获取施工信息
    getWork(location) {
      let params = {
        latitude: location.latitude,
        longitude: location.longitude,
        pageNo: 1,
        pageSize: 2
      };
      this.$request
        .post(this.$interfaces.getMapWork, {
          data: params
        })
        .then(res => {
          let { data } = res;
          if (data && data.length > 0) {
            let workInfo = data.map(item => {
              return {
                infoType: "work",
                ...item
              };
            });
            this.allRoadInfo = [...this.allRoadInfo, ...workInfo];
            // 按时间排序，最新的在前面
            this.sortAllRoadInfo();
            // 设置显示和轮播
            this.setupInitialDisplayAndRotation();
          }
        });
    },
    
    // 设置初始显示和轮播
    setupInitialDisplayAndRotation() {
      if (!this.allRoadInfo || this.allRoadInfo.length === 0) {
        this.displayItem1 = null;
        this.displayItem2 = null;
        this.showCard1 = true;
        this.showCard2 = true;
        this.isTransitioning = false;
        this.isShifting = false;
        if (this.rotationIntervalId) {
          clearInterval(this.rotationIntervalId);
          this.rotationIntervalId = null;
        }
        return;
      }

      // 初始化轮播索引
      this.currentRotatingIndex = 0;
      
      // 设置两个卡片的初始显示内容
      this.updateDisplayItems();
      this.showCard1 = true;
      this.showCard2 = true;
      this.isTransitioning = false;
      this.isShifting = false;

      // 清除可能存在的旧定时器
      if (this.rotationIntervalId) {
        clearInterval(this.rotationIntervalId);
        this.rotationIntervalId = null;
      }

      // 如果有超过1条信息，启动轮播
      if (this.allRoadInfo.length > 1) {
        this.rotationIntervalId = setInterval(() => {
          this.rotateCardsWithShift();
        }, 10000); // 10秒轮播一次
      }
    },

    // 更新两个卡片槽位的显示内容
    updateDisplayItems() {
      if (!this.allRoadInfo || this.allRoadInfo.length === 0) {
        this.displayItem1 = null;
        this.displayItem2 = null;
        return;
      }
      
      // 计算两个卡片显示的内容索引
      const index1 = this.currentRotatingIndex % this.allRoadInfo.length;
      const index2 = (this.currentRotatingIndex + 1) % this.allRoadInfo.length;
      
      // 更新显示内容
      this.displayItem1 = this.allRoadInfo[index1];
      this.displayItem2 = index2 !== index1 ? this.allRoadInfo[index2] : null;
    },
    
    // 带上移效果的轮播
    rotateCardsWithShift() {
      if (!this.allRoadInfo || this.allRoadInfo.length <= 1) {
        // 如果只有0-1条信息，无需轮播
        return;
      }

      // 添加一个标记表示正在过渡
      this.isTransitioning = true;
      this.isShifting = true; // 标记正在执行上移动画
      
      // 使第一个卡片淡出
      this.showCard1 = false;
      
      // 延迟更新内容，等待动画完成
      setTimeout(() => {
        // 更新轮播索引 - 将第二个卡片的内容移到第一个卡片
        this.currentRotatingIndex = (this.currentRotatingIndex + 1) % this.allRoadInfo.length;
        
        // 更新卡片内容
        this.updateDisplayItems();
        
        // 重置动画状态
        this.$nextTick(() => {
          this.isShifting = false; // 结束上移动画
          this.showCard1 = true;   // 显示新的第一个卡片(原来的第二个卡片内容)
          this.isTransitioning = false;
        });
      }, 500); // 动画时间适当调整
    },
    
    // 获取卡片样式
    getCardStyling(item) {
      if (!item) return { class: '', tag: '', voiceIcon: '' };
      switch (item.infoType) {
        case 'work':
          return { 
            class: 'work-card', 
            tag: '涉路施工', 
            voiceIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_4.png' 
          };
        case 'event':
          return { 
            class: 'event-card', 
            tag: '道路事件', 
            voiceIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_1.png' 
          };
        case 'jam':
          return { 
            class: 'jam-card', 
            tag: this.congestLevel[item.congestLevel] || '拥堵', 
            voiceIcon: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_3.png' 
          };
        default:
          return { class: '', tag: '未知类型', voiceIcon: '' };
      }
    },

    // 获取路名
    getRoadName(item) {
      if (!item) return '';
      return item.highspeedName || item.loadName || item.roadName || '未知路段';
    },

    // 获取语音内容
    getVoiceContent(item) {
      if (!item) return '';
      return item.infoType === 'jam' ? (item.congestSourceArea || '道路拥堵') : item.description;
    },

    // 获取描述内容
    getDescription(item) {
      if (!item) return '';
      if (item.infoType === 'jam') return item.congestSourceArea || '道路拥堵';
      return item.description;
    },

    // 获取详细信息（根据不同类型）
    getDetailInfo(item) {
      if (!item) return '';
      
      if (item.infoType === 'work') {
        return `${item.startTime}-${item.endTime} | ${item.direction || ''}`;
      } else if (item.infoType === 'event') {
        // 不再显示directionTraffic，可以显示其他信息如direction
        return item.direction || '';
      } else if (item.infoType === 'jam') {
        return `${item.direction || ''} | 拥堵距离: ${item.congestLength || 0}km`;
      }
      
      return '';
    },
    
    // 语音播报
    speek(content) {
      if (!content) return;
      
      plugin.textToSpeech({
        lang: "zh_CN",
        tts: true,
        content: content,
        success: (res) => {
          this.yuyinPlay(res.filename);
        },
        fail: (res) => {
          console.log("语音播报失败", res);
        }
      });
    },
    
    // 播放语音
    yuyinPlay(src) {
      if (!src) return;
      
      innerAudioContext.autoplay = true;
      innerAudioContext.src = src; // 设置音频地址
      innerAudioContext.play(); // 播放音频
    },
    
    // 跳转到地图位置
    goToMapLocation(item) {
      // 跳转到路况服务页面，并传递相关参数
      uni.navigateTo({
        url: `/pagesD/travelService/jam-info?infoType=${item.infoType}&id=${item.id}&lon=${item.lon || item.lng || item.startLongitude}&lat=${item.lat || item.startLatitude}`
      });
    },
    
    // 按时间排序路况信息列表
    sortAllRoadInfo() {
      // 根据不同类型的路况信息，选择对应的时间字段进行排序
      this.allRoadInfo.sort((a, b) => {
        let timeA, timeB;
        
        if (a.infoType === 'event') {
          timeA = a.eventTime || '';
        } else if (a.infoType === 'jam') {
          timeA = a.startTime || '';
        } else if (a.infoType === 'work') {
          timeA = a.startTime || '';
        }
        
        if (b.infoType === 'event') {
          timeB = b.eventTime || '';
        } else if (b.infoType === 'jam') {
          timeB = b.startTime || '';
        } else if (b.infoType === 'work') {
          timeB = b.startTime || '';
        }
        
        // 降序排列，最新的在前面
        return new Date(timeB) - new Date(timeA);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/* 卡片动画相关样式 */
.card-transition {
  transition: all 0.5s ease-in-out;
  will-change: opacity, transform;
  backface-visibility: hidden;
}

.card-fade-out {
  opacity: 0;
  transform: translateY(-20rpx);
}

.card-shift-up {
  transform: translateY(-174rpx); /* 根据卡片高度160rpx+间距14rpx调整，使第二卡片正好移动到第一卡片位置 */
}

/* 初始显示的卡片动画 */
.info-card {
  animation: cardFadeIn 0.5s ease-in-out;
  position: relative;
}

@keyframes cardFadeIn {
  from { 
    opacity: 0;
    transform: translateY(20rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.road-notice {
  margin: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 8rpx;
  
  .info-title {
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .cards-container {
    display: flex;
    flex-direction: column;
    gap: 14rpx;
  }
  
  .info-card {
    height: 140rpx;
    background: #F4F6FA;
    border-radius: 6rpx;
    padding: 20rpx;
    box-sizing: border-box;
    position: relative;
    display: flex;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    
    &.work-card {
      .card-type {
        border-color: #F69754;
      }
      .card-tag {
        color: #F69754;
      }
    }
    
    &.event-card {
      .card-type {
        border-color: #F65B5B;
      }
      .card-tag {
        color: #F65B5B;
      }
    }
    
    &.jam-card {
      .card-type {
        border-color: #F82A3C;
      }
      .jam-tag {
        color: #F82A3C;
      }
    }
    
    .card-type {
      width: 82rpx;
      text-align: center;
      padding: 22rpx 0;
      border-right: 7rpx solid;
      margin-right: 20rpx;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 600;
      font-size: 32rpx;
      .card-tag {
        width: 62rpx;
        word-wrap: break-word;
        font-size: 24rpx;
        font-weight: 500;
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      
      .card-tag {
        font-size: 24rpx;
        font-weight: 500;
        margin-right: 16rpx;
      }
      
      .jam-tag {
        font-size: 24rpx;
        font-weight: 500;
        margin-right: 16rpx;
      }
      
      .road-name {
        flex: 1;
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .voice-btn {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        image {
          width: 28rpx;
          height: 28rpx;
        }
      }
    }
    
    .card-content {
      flex: 1;
      max-width: 486rpx;
      .road-info {
        font-size: 24rpx;
        color: #666666;
        line-height: 34rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .road-detail {
        font-size: 22rpx;
        color: #666666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        
        .separator {
          margin: 0 8rpx;
          color: #999999;
        }
      }
    }
    .card-voice{
        width: 44rpx;
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        image{
          width: 100%;
          height: 100%;
        }
      }
  }
}
</style> 