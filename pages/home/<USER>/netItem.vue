<template>
	<view class="net-item">
		<view class="title-wrapper">
			<view class="title">
				{{item.networkName}}
			</view>
			<view class="right">
				<view class="status" :class="item.status == 1 ?'success':'info'">
					{{item.status == 1 ?'正常营业':'暂停营业'}}
				</view>
				<image @click="call(item.phone)" class="phone-icon"
					src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/call_icon.png"
					mode=""></image>
			</view>
		</view>
		<view class="address">
			{{item.address}}
		</view>
		<view class="time">
			营业时间:{{item.businessHours}}
		</view>
		<!-- <view class="desc-wrapper">
			<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/address_icon.png" mode="aspectFit"></image>
			<view class="desc-label">
				服务范围：
			</view>
			<view class="desc">
				{{item.serviceScope}}
			</view>
		</view> -->
	</view>
</template>
<script>
	export default {
		props: {
			item: {
				type: Object
			}
		},
		components: {

		},
		data() {
			return {};
		},
		created() {

		},
		methods: {
			call(phoneNumber) {
				if (phoneNumber.includes('暂无')) {
					uni.makePhoneCall({
						phoneNumber: '0771-5896333' //仅为示例
					});
				} else {
					uni.makePhoneCall({
						phoneNumber: phoneNumber //仅为示例
					});
				}
			},
		}
	};
</script>

<style lang="scss" scoped>
	.net-item {
		font-family: PingFang SC, PingFang SC;
		// height: 205rpx;
		margin: 20rpx 20rpx 38rpx 20rpx;
		border-bottom: 2rpx solid #E7E7E7;


		.title-wrapper {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.title {
				font-weight: 600;
				font-size: 30rpx;
				color: #333333;
				line-height: 40rpx;
			}

			.right {
				display: flex;
				align-items: center;


				.status {
					width: 160rpx;
					height: 40rpx;
					line-height: 40rpx;
					text-align: center;
					border-radius: 16rpx 16rpx 16rpx 16rpx;
					padding: 0 15rpx;
				}

				.status.info {
					color: #959595;
					background: rgba(149, 149, 149, 0.1);
				}

				.status.success {
					color: #08BA81;
					background: rgba(8, 186, 129, 0.1);
				}

				.phone-icon {
					margin-left: 14rpx;
					width: 32rpx;
					height: 32rpx;
				}
			}
		}

		.address {
			margin-top: 20rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 34rpx;
		}

		.time {
			margin-top: 10rpx;
			margin-bottom: 16rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 34rpx;
		}

		.desc-wrapper {
			margin-top: 12rpx;
			margin-bottom: 32rpx;
			display: flex;
			align-items: center;

			&>image {
				flex: 0 0 28rpx;
				width: 28rpx;
				height: 28rpx;
			}

			.desc-label {
				margin-left: 10rpx;
				width: 120rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #4F90FF;
				line-height: 34rpx;
			}

			.desc {
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 24rpx;
				line-height: 34rpx;
			}
		}
	}
</style>