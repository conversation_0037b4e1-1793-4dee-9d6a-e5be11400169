<template>
  <view class="highway-alliance">
    <!-- 标题和搜索更多 -->
    <view class="alliance-header">
      <view class="alliance-title">高速联盟</view>
      <view class="search-more" @tap="handleSearchMore">
        <text>搜索更多</text>
        <image src="/static/toc/arrow-right.png" class="arrow-icon"></image>
      </view>
    </view>
    
    <!-- 轮播图区域 -->
    <view class="banner-wrapper">
      <special-banner 
        :banner-list="bannerList" 
        :swiper-config="swiperConfig"
        mode="classic"
        containerHeight="226rpx"
        imageWidth="482rpx"
        imageHeight="226rpx"
        :hide-description="true"
        scaleX="1.1"
        scaleY="1.1"
        @bannerClick="handleBannerClick">
      </special-banner>
    </view>
  </view>
</template>

<script>
import specialBanner from '@/components/EtherealWheat-banner/specialBanner.vue'

export default {
  name: 'HighwayAlliance',
  components: {
    specialBanner
  },
  data() {
    return {
      // 轮播图数据
      bannerList: [
        {
          picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
          title: '桂林游实花第实花第实',
          description: '桂林游实花第实花第实',
          path: ''
        },
        {
          picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ2.png',
          title: '桂林游实花第实花第实',
          description: '桂林游实花第实花第实',
          path: ''
        },
        {
          picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ3.png',
          title: '桂林游实花第实花第实',
          description: '桂林游实花第实花第实',
          path: ''
        },
        {
          picture: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ4.png',
          title: '桂林游实花第实花第实',
          description: '桂林游实花第实花第实',
          path: ''
        }
      ],
      // 轮播图配置
      swiperConfig: {
        indicatorDots: false,
        indicatorColor: 'rgba(255, 255, 255, .4)',
        indicatorActiveColor: 'rgba(255, 255, 255, 1)',
        autoplay: false,
        previousMargin: '100rpx',
        nextMargin: '100rpx',
        interval: 4000,
        duration: 500,
        circular: true
      }
    }
  },
  methods: {
    // 处理搜索更多点击
    handleSearchMore() {
      console.log('搜索更多被点击')
      // 跳转到高速联盟文章列表页面
      uni.navigateTo({
        url: '/pagesD/highway-alliance/highway-list'
      })
    },
    
    // 处理轮播图点击
    handleBannerClick(event) {
      console.log('轮播图被点击:', event)
      const { index, item } = event
      // 这里可以根据点击的轮播图跳转到对应页面
      uni.showToast({
        title: `点击了第${index + 1}张图片`,
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.highway-alliance {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;

  .alliance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 20rpx;

    .alliance-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
    }

    .search-more {
      display: flex;
      align-items: center;
      color: #999999;
      font-size: 24rpx;

      text {
        margin-right: 8rpx;
      }

      .arrow-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  .banner-wrapper {
    padding: 0 32rpx 32rpx;
  }
}
</style>
