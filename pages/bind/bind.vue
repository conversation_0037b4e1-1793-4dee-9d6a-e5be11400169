<template>
	<view class="bind">

		<view class="weui-form">
			<view class="weui-cells__title">
				微信公众号绑定
			</view>
			<view class="weui-cells">
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">手机号码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input id="mobile" name="mobile" v-model="formData.mobile" class="weui-input"
							placeholder="请输入手机号码"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">密码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input id="password" password v-model="formData.password" class="weui-input"
							placeholder="请输入密码"></input>
					</view>
					<view class="weui-cell__ft">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">图形验证码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input id="mobileCode" v-model="mobileCode" class="weui-input" placeholder="请输入图形验证码"></input>
					</view>
					<view class="weui-cell__ft">
						<image :src="codeUrl" class="code-img" @click="getCaptcha">
					</view>
				</view>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">短信验证码</view>
					</view>
					<view class="weui-cell__bd weui-cell__primary">
						<input id="mobileCode" v-model="formData.mobileCode" class="weui-input"
							placeholder="请输入短信验证码"></input>
					</view>
					<view class="weui-cell__ft">
						<view class="sms-code" @click="sendSMS">
							{{smsName}}
						</view>
					</view>
				</view>

			</view>
		</view>
		<view class="weui-bottom-fixed">
			<view class="weui-bottom-fixed__box bottom-box">
				<button class="weui-btn weui-btn_primary" @click="sendBindUser">
					绑定
				</button>

			</view>
		</view>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import tLoading from '@/components/common/t-loading.vue';
	import {
		checkIdCard,
		checkEmail,
		checkPosttal
	} from "@/common/util.js";
	import {
		getCurrUserInfo,
		setCurrUserInfo
	} from "@/common/storageUtil.js";
	import {
		checkPhone
	} from '@/common/method/validater.js'
	import {
		getErrorMessage
	} from '@/common/method/filter.js'
	import JSEncrypt from '@/js_sdk/jsencrypt/jsencrypt';
	import {
		setLoginUserInfo,
		setTicket,
		setMd5Key,
		setAesKey,


	} from '@/common/storageUtil.js';
	export default {
		components: {
			tLoading
		},
		data() {
			return {
				isLoading: false,
				codeUrl: '', // 图形验证码
				captchaId: '', // 图形验证码ID
				smsName: '发送验证码',
				mobileCode: '', // 图形验证码
				formData: {
					mobile: '', //手机号码
					mobileCode: '', // 短信验证码
					openId: '', // 微信公众号openID
					password: '', //密码
				}
			};
		},
		computed: {

		},
		onLoad(option) {
			this.formData.openId = sessionStorage.getItem('publicOpenId');
		},
		created() {
			this.getCaptcha()
			// 清除本地登录信息
			// try {
			// 	const res = uni.getStorageInfoSync();
			// 	if (res && res.keys && res.keys.length) {
			// 		let keys = res.keys;
			// 		for (let i = 0; i < keys.length; i++) {
			// 			if (keys[i] != 'loginAccount') {
			// 				uni.removeStorageSync(keys[i]);
			// 			}

			// 		}
			// 	}
			// } catch (e) {

			// }
		},
		methods: {
			// 验证手机号码
			verifyMobile() {
				let msg = '';

				if (!this.formData.mobile) {
					msg = '请输入手机号码';
					return msg;
				}
				if (checkPhone(this.formData.mobile)) {
					msg = '请输入合法手机号码';
					return msg;
				}
				if (!this.mobileCode) {
					msg = '请输入图形验证码'
					return msg;
				}
				return msg;
			},
			//发送短信
			sendSMS() {
				if (this.verifyMobile()) {
					uni.showToast({
						title: this.verifyMobile(),
						icon: "none"
					})
					return;
				}
				if (!this.time) {
					this.isLoading = true
					let countdown = 60
					let params = {
						mobile: this.formData.mobile,
						mobileCode: this.mobileCode,
						captchaId: this.captchaId,
						type: 1
					};
					this.$request.post(this.$interfaces.sendaAccountSms, {
						data: params
					}).then(res => {
						console.log(res);
						this.isLoading = false
						if (res.code == '200') {
							this.time = setInterval(() => {
								countdown = countdown - 1
								this.smsName = countdown + "秒后重新发送"
								if (countdown === 0) {
									clearInterval(this.time)
									this.time = null
									this.smsName = "重新发送"
								}
							}, 1000)
						} else {
							uni.showModal({
								title: "提示",
								content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res.code + '】',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										this.getCaptcha()
									}
								}
							});
						}
					})
				}
			},
			getCaptcha() {

				this.$request.post(this.$interfaces.getCaptcha, {

				}).then(res => {
					if (res.code == 200) {
						this.codeUrl = res.data.image
						this.captchaId = res.data.captchaId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(error => {

				})
			},
			sendBindUser() {
				if (this.verifyMobile()) {
					uni.showToast({
						title: this.verifyMobile(),
						icon: "none"
					})
					return;
				}
				if (!this.formData.mobileCode) {
					let msg = '请输入短信验证码'
					uni.showToast({
						title: msg,
						icon: "none"
					})
					return
				}
				let params = JSON.parse(JSON.stringify(this.formData));
				var encrypt = new JSEncrypt()
				encrypt.setPublicKey(this.$publicKey)
				params.password = encrypt.encrypt(params.password)
				this.$request.post(this.$interfaces.publicBindUser, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						//未注册提示注册
						let paramsId = {
							mobile: this.formData.mobile,
							openId: this.formData.openId
						}
						if (!res.data.isRegister) {
							uni.showModal({
								title: "提示",
								content: '该手机号未注册，是否去注册',
								showCancel: true,
								success: function(res) {
									if (res.confirm) {
										uni.navigateTo({
											url: "/pagesD/register/p-register?paramsId=" +
												JSON.stringify(paramsId)
										});
									}
								}
							});
							return
						}
						if (res.data.loginRes && res.data.isRegister) {
							this.setLoginInfo(res.data.loginRes)
						}
					} else {
						uni.showModal({
							title: "提示",
							content: (getErrorMessage(res.code) || res.msg) + '【错误码：' + res.code + '】',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									this.getCaptcha()
								}
							}
						});
					}
				}).catch(err => {

				})
			},
			setLoginInfo(result) {
				console.log(result)
				setAesKey(result.aesKey)
				setMd5Key(result.md5Key)
				setTicket(result.ticket)
				setLoginUserInfo(result.data);
				// #ifdef H5
				sessionStorage.setItem('authorizetype', '');
				// #endif
				uni.redirectTo({
					url: "/pages/home/<USER>/p-home"
				})
			},
		}
	};
</script>

<style scoped lang="scss">
	.bind .code-img {
		width: 228upx;
		height: 62rpx;
		display: block;
		margin-left: 20rpx;
	}

	.bind .sms-code {
		height: 62rpx;
		min-width: 228rpx;
		display: block;
		border-radius: 12rpx;
		text-align: left;
		line-height: 62rpx;
		background-color: #f2f2f2;
		color: #0066E9;
		padding: 0 28rpx;
		margin-left: 20rpx;
	}

	.bind .weui-label {
		width: 190rpx !important;
	}

	.tips {
		margin-bottom: 170rpx;
	}

	.bottom-box {
		display: flex;
	}

	.bottom-box .btn-item {
		flex: 1;
	}

	.bottom-box .btn-item:last-child {
		margin-left: 32rpx;
	}

	.activation-page {
		position: relative;
	}

	.weui-label {
		width: 220rpx;
	}

	.idCard {
		background-color: #f6f6f6;

		.examine {
			position: absolute;
			height: calc(100% - 436rpx);
			background-color: #FFFFFF;
			top: 400rpx;
			width: 100%;
			border-radius: 16rpx 16rpx 0px 0px;

			.examine-content {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-top: 150rpx;

				.text {
					font-size: 32rpx;
					font-weight: 400;
					color: #333333;
					width: 464rpx;
					text-align: center;
					margin-top: 40rpx;
				}
			}
		}

		.idCard-top {
			position: relative;
			z-index: 9;
			padding: 48rpx 30rpx;
			background-color: #fff;
			border-radius: 16rpx 16rpx 0px 0px;

			.title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
			}

			.content {
				display: flex;

				.left,
				.right {
					position: relative;
					margin-top: 40rpx;

					.photo_icon {
						position: absolute;
						top: 55rpx;
						left: 125rpx;
					}

					.text {
						text-align: center;
						font-size: 28rpx;
						color: #333;
						font-weight: 400;
						margin-top: 20rpx;
					}

					.delIcon {
						position: absolute;
						right: 0;
						top: 0;
					}
				}

				.right {
					margin-left: 10rpx;
				}
			}

			.center {
				justify-content: center;
			}
		}

		.idCard-bottom {
			padding: 30rpx 0;
			background-color: #fff;
			margin-top: 20rpx;

			.title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
				margin-bottom: 30rpx;
				margin-left: 30rpx;
			}

			.text_line {
				padding: 0 30rpx;
				height: 100rpx;
				width: 100%;
				font-size: 30rpx;
				color: #999999;
				font-weight: 400;
				line-height: 100rpx;
				border-top: 1px solid #e9e9e9;
				display: flex;
				justify-content: space-between;

				.text {
					display: inline-block;
					width: 250rpx;
					color: #999999;
					font-size: 30rpx;
					font-weight: 400;
				}
			}

			.bottom {
				border-bottom: 1px solid #e9e9e9;
			}
		}

		.tips {
			color: #ff9000;
			font-weight: 400;
			font-size: 22rpx;
			text-align: center;
			margin: 10rpx 0;
		}

		.btn {
			display: flex;
			justify-content: space-around;
			background-color: #FFFFFF;
			padding: 20rpx;
		}
	}

	.slot-content {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
		flex-direction: column;
		align-items: center;

		.text {
			font-size: 26rpx;
			font-weight: 400;
			color: #666666;
			width: 500rpx;
			text-align: center;
			margin-bottom: 10rpx;
		}

		.phone {
			font-size: 36rpx;
			font-weight: 500;
			color: #333333;
		}
	}

	.tips {
		margin-top: 14rpx;
		margin-top: 12rpx;
		text-align: center;

		.tips-text {
			font-size: 11px;
			font-family: PingFangSC, PingFangSC-Regular;
			color: #ff9038;
		}
	}
</style>
