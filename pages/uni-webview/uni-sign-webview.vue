<template>
	<view>
		<web-view :src="ownPath" @message="handleMessage"></web-view>
	</view>
</template>

<script>
	import { uni_decodeURIComponent } from '@/common/helper.js';
	import store from 'store/index.js';
	
	export default {
		data() {
            return {
				ownPath:''
            }
        },
		onShow() {
		     
		},
		onLoad:function(options){
			console.log("webview onLoad:options:"+options)
			this.ownPath=uni_decodeURIComponent(options.ownPath);
			//this.ownPath = "https://www.baidu.com";
			console.log("webview onLoad:"+options.ownPath)
		},
		methods: {
			handleMessage(evt) {  
			    console.log('接收到的消息：' + JSON.stringify(evt.detail)); 
				//判断是那个回调 登录还是银行回调还是活体认证回调
				var str = JSON.stringify(evt.detail);
				let status = str.split('status=')[1].split('\"}]}')[0];
				//status=1;//成功    status=2;//失败
				try{
					if(status == '1'){//签约成功
						uni.setStorageSync('signStatus',1);
						console.log("签约成功，signStatus：1");
					}else if(status == '2'){ //签约失败
						uni.setStorageSync('signStatus',2);
						console.log("签约失败，signStatus：2");
					}
				}catch(e){
					
				}
				//console.log('tokenId：' + tokenId);
				//uni.setStorageSync('tokenId',tokenId)//存入缓存   阮文忠
			}
		}
	}
</script>

<style>

</style>
