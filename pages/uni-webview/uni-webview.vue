<template>
	<view>
		<web-view :src="ownPath" @message="handleMessage"></web-view>
	</view>
</template>

<script>
	import { uni_decodeURIComponent } from '@/common/helper.js';
	import { getTokenId,setTokenId,setMustFresh } from '@/common/storageUtil.js';
	import store from 'store/index.js';
	import Api from "@/common/api/index.js"
	import {request} from '@/common/request-1/requestFunc.js'
	export default {
		data() {
            return {
				ownPath:''
            }
        },
		onShow() {
		     
		},
		onLoad:function(options){
			//console.log("webview onLoad:options:"+options)
			this.ownPath=uni_decodeURIComponent(options.ownPath);
			//this.ownPath = "https://www.baidu.com";
			//console.log("webview onLoad:"+options.ownPath)
		},
		methods: {
			handleMessage(evt) {
			    //console.log('接收到的消息：' + JSON.stringify(evt.detail)); 
				//判断是那个回调 登录还是银行回调还是活体认证回调
				var str = JSON.stringify(evt.detail);
				let tokenId = str.split('&tokenId=')[1].split('\"}]}')[0];
				setTokenId(tokenId);
				setMustFresh("1");
				console.log('成功登录')
				// this.checkRealName()
			},
			//校验是否实名
			checkRealName(){
				let data = {
					'fileName': Api.getUserUser.method,
					'data': {
						tokenId: getTokenId()
					}
				};
				request(Api.getUserUser.url, data,(res) => {
					console.log(res)
					//未实名
					if(res.status === 1){
						uni.navigateTo({
							url: "/pagesA/etc/certification-new/certification-confirm"
						})
					}
				})
			}
		}
	}
</script>

<style>

</style>
