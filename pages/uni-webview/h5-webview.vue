<template>
	<view>
		<web-view :src="ownPath"></web-view>
	</view>
</template>


<script>
	export default {
		data() {
			return {
				ownPath: ''
			}
		},
		onLoad: function(options) {
			this.ownPath = decodeURIComponent(options.ownPath);
			// this.ownPath = "https://www.baidu.com";
			console.log("webview onLoad:" + options.ownPath)

			if (options.title) {
				uni.setNavigationBarTitle({
					title: options.title
				})
			}
		},
	}
</script>

<style>
</style>