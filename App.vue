<script>
	import Vue from 'vue'
	import {
		setReturnCode,
		removeStore,
		setMd5Key,
		setAesKey,
		getMd5Key,
		getAesKey,
		setStore
	} from '@/common/storageUtil.js';
	import bleapi from '@/common/bluetooth/bleUtil.js';
	export default {
		globalData: {
			ticket: '', //登录票据
			md5Key: '', //md5签名密钥
			aesKey: '', //aes加密密钥
			tokenId:'',
		    currentPerson:{},
			currentCar:{},
			productinfo:{},
			wxAccountInfo:{},
			returnCode:"",
			accountId:'',
			openId:"",
			readOpenId:"",
			session_key:"",
			phone:'',
			channelId:{},
			timerListData:{}
		}, 
		onLaunch: function() {
			const app = getApp();
			uni.getSystemInfo({
				success: function(e) {
					Vue.prototype.windowHeight = e.windowHeight;
					Vue.prototype.windowWidth = e.windowWidth;
				}
			})
			// 2022-09-08 引导关注公众号功能
			setStore('launchGuide','1')
			// removeStore('defaultChannel')
			//#ifdef MP-WEIXIN
			//判断微信版本是否 兼容小程序更新机制API的使用
			if (wx.canIUse('getUpdateManager')) {
				//创建 UpdateManager 实例
				const updateManager = wx.getUpdateManager();
				console.log('是否进入模拟更新');
				//检测版本更新
				updateManager.onCheckForUpdate(function(res) {
					console.log('是否获取版本', res.hasUpdate);
					// 请求完新版本信息的回调
					if (res.hasUpdate) {
						//监听小程序有版本更新事件
						updateManager.onUpdateReady(function() {
							console.log('已经更新');
							//TODO 新的版本已经下载好，调用 applyUpdate 应用新版本并重启 （ 此处进行了自动更新操作）
							updateManager.applyUpdate();
						})
						updateManager.onUpdateFailed(function() {
							// 新版本下载失败
							wx.showModal({
								title: '已经有新版本喽~',
								content: '请您删除当前小程序，到微信 “发现-小程序” 页，重新搜索打开哦~',
							})
						})
					} else {
						console.log('当前已经是最新版本');
					}
				})
			} else {
				//TODO 此时微信版本太低（一般而言版本都是支持的）
				wx.showModal({
					title: '溫馨提示',
					content: '当前微信版本过低，无法使用自动更新功能，请升级到最新微信版本后重试。'
				})
			}
			//#endif

			// uni.clearStorageSync()
			// uni.reLaunch({
			// 	url:'/pages/login/p-login'
			// })
			// setMd5Key(this.$md5KeyInit)
			// setAesKey(this.$aesKeyInit)
			console.log(getMd5Key(), '------------', getAesKey());
		},
		onShow: function(res) {
			//城市服务返回code
			if (res.referrerInfo && res.referrerInfo.extraData && res.referrerInfo.extraData.code) {
				setReturnCode(res.referrerInfo.extraData.code.auth_code)
			}
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		created() {

			bleapi.onBLEConnectionStateChange();
		},


	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import "uview-ui/theme.scss";
	@import "uview-ui/index.scss";
	@import "/common/css/weui.scss";
	@import "/common/color-ui/animation.css";
	@import "/common/color-ui/main.css";
	@import "/common/color-ui/icon.css";
	@import "/common/css/iconfont.css";
	@import "/common/css/flex.css";
	@import "/common/css/common.css";
	@import "/common/css/weui.scss";
	@import "/common/css/main.scss";
	@import "/common/css/weui/weui-card.scss";
	@import  "/common/css/style.css";
	page{
		overflow-y:scroll !important;
		height: 100%;
	}
</style>
