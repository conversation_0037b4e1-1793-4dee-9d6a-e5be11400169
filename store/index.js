import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)
const modulesFiles = require.context('./modules', true, /\.js$/)

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
	// set './app.js' => 'app'
	const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
	const value = modulesFiles(modulePath)
	modules[moduleName] = value.default
	return modules
}, {})
const store = new Vuex.Store({
	modules,
	state: {
		loginAccount: {}, // 登录账户
		ticket: '', //登录票据
		md5Key: '', //md5签名密钥
		aesKey: '', //aes加密密钥
		tokenId: "", //登录返回唯一辨识值
		networkType: "", //网络状态
		accessToken: "", //接口调用凭证
		mustFresh: "", //是否强制刷新
		vehicleInfo: {}, //车辆信息
		customerInfo: {}, //用户信息信息
		recieveAddress: {}, //编辑收货地址
		productInfo: {},
		wxAccountInfo: {}, //微信个人信息,
		returnCode: "", //小程序返回的code
		accountId: '',
		openId: "",
		readOpenId: "", //订阅使用的openId
		session_key: "",
		phone: '',
		decodingKey: "",
		oweData: '', //欠费信息
		applyId: '', //新办申请id
		issueVehicleInfo: '', //激活车辆信息
		afterSaleBusinessType: '',
		vehicleDetail:{},//车辆详情
		refundList: [], //集装箱图片信息
		mapKey:'HTRBZ-BOAWB-AA2UZ-NNSM2-KT46T-2LFXE' // 地图key
	},
	getters: {
		loginAccount: (state) => state.loginAccount,
		ticket: (state) => state.ticket,
		md5Key: (state) => state.md5Key,
		aesKey: (state) => state.aesKey,
		tokenId: (state) => state.tokenId,
		networkType: (state) => state.networkType,
		accessToken: (state) => state.accessToken,
		mustFresh: (state) => state.mustFresh,
		vehicleInfo: (state) => state.vehicleInfo,
		customerInfo: (state) => state.customerInfo,
		recieveAddress: (state) => state.recieveAddress,
		productInfo: (state) => state.productInfo,
		wxAccountInfo: (state) => state.wxAccountInfo,
		returnCode: (state) => state.returnCode,
		accountId: (state) => state.accountId,
		openId: (state) => state.openId,
		readOpenId: (state) => state.readOpenId,
		session_key: (state) => state.session_key,
		phone: (state) => state.phone,
		decodingKey: (state) => state.decodingKey,
		oweData: (state) => state.oweData,
		applyId: (state) => state.applyId,
		issueVehicleInfo: (state) => state.issueVehicleInfo,
		afterSaleBusinessType: (state) => state.afterSaleBusinessType,
		refundList: (state) => state.refundList,
		vehicleDetail:(state)=>state.vehicleDetail
	},
	mutations: {
		SET_LOGIN_ACCOUNT: function(state, loginAccount) {
			state.loginAccount = loginAccount; //
		},
		SET_TICKET: function(state, ticket) {
			state.ticket = ticket; //ticket
		},
		SET_MD5_KEY: function(state, md5Key) {
			state.md5Key = md5Key; //md5Key
		},
		SET_AES_KEY: function(state, aesKey) {
			state.aesKey = aesKey; //aesKey
		},
		SET_TOKENID: function(state, tokenId) {
			state.tokenId = tokenId; //tokenId
		},
		SET_NETWORKTYPE: function(state, networkType) {
			state.networkType = networkType; //网络状态
		},
		SET_ACCESSTOKEN: function(state, accessToken) {
			state.accessToken = accessToken; //accessToken
		},
		SET_MUSTFRESH: function(state, mustFresh) {
			state.mustFresh = mustFresh; //accessToken
		},
		SET_CUSTOMERINFO: function(state, customerInfo) {
			state.customerInfo = customerInfo; //个人信息
		},
		SET_VEHICLEINFO: function(state, vehicleInfo) {
			state.vehicleInfo = vehicleInfo; //车辆信息
		},
		SET_RECIEVEADDRESS: function(state, recieveAddress) {
			state.recieveAddress = recieveAddress; //地址
		},
		SET_PRODUCTINFO: function(state, productInfo) {
			state.productInfo = productInfo; //发行方产品列表
		},
		SET_WX_ACCOUNTINFO: function(state, accountInfo) {
			state.wxAccountInfo = accountInfo; //微信账户信息
		},
		SET_RETURN_CODE: function(state, code) {
			state.returnCode = code; //小程序返回的code
		},
		SET_ACCOUNTID: function(state, id) {
			state.accountId = id; //小程序返回的code
		},
		SET_OPENID: function(state, id) {
			state.openId = id;
		},
		SET_OPENID_READ: function(state, id) {
			state.readOpenId = id;
		},
		SET_SESSION_KEY: function(state, key) {
			state.session_key = key;
		},
		SET_PHONE: function(state, phone) {
			state.phone = phone;
		},
		CLEAR_RETURN_CODE: function(state) {
			state.returnCode = "";
		},
		SET_AESKEY: function(state, decodingKey) {
			state.decodingKey = decodingKey;
		},

		SET_OWEDATA: function(state, oweData) {
			state.oweData = oweData;
		},
		SET_APPLYID: function(state, applyId) {
			state.applyId = applyId;
		},
		SET_ISSUE_VEHICLEINFO: function(state, issueVehicleInfo) {
			state.issueVehicleInfo = issueVehicleInfo;
		},
		SET_AFTERSALEBUSINESSTYPE: function(state, data) {
			state.afterSaleBusinessType = data;
		},
		SET_REFUNDLIST: function(state, refundList) {
			state.refundList = refundList;
			console.log('refundList', refundList, state.refundList)
		},
		SET_VEHICLEDETAIL:function(state,vehicleDetail){
			state.vehicleDetail=vehicleDetail
		},
		CLEAR_DATA: function(state) { //退出登录后清除缓存
			state.ticket = ''
			state.md5Key = ''
			state.aesKey = ''
			state.tokenId = "" //登录返回唯一辨识值
			state.networkType = "" //网络状态
			state.accessToken = "" //接口调用凭证
			state.mustFresh = "" //是否强制刷新
			state.vehicleInfo = {} //车辆信息
			state.customerInfo = {} //用户信息信息
			state.recieveAddress = {} //编辑收货地址
			state.productInfo = {}
			state.wxAccountInfo = {} //微信个人信息,
			state.returnCode = "" //小程序返回的code
			state.accountId = '' //选择的账户accountId
			state.openId = ""
			state.readOpenId = ""
			state.sessio_key = ""
			state.phone = ""
			state.decodingKey = ""
			state.oweData = ''
			state.applyId = ''
			state.issueVehicleInfo = {}
			state.refundList = []
		},
		removeaAll(keys) {
			let _key = keys || uni.getStorageInfoSync().keys;
			if (_key.length > 0) {
				_key.map(el => {
					uni.removeStorageSync(el);
				})
			}
		}
	},
	actions: {
		// 登录账户
		setLoginAccount({
			commit
		}, data) {
			commit('SET_LOGIN_ACCOUNT', data)
		},
		// 设置ETC客户
		setCustomerInfo({
			commit
		}, data) {
			commit('SET_CUSTOMERINFO', data)
		},
		// 设置网络状态
		setNetWorkType({
			commit
		}, data) {
			commit('SET_NETWORKTYPE', data)
		},
		// 设置ETC客户
		setCustomerInfo({
			commit
		}, data) {
			commit('SET_CUSTOMERINFO', data)
		},
		// 强制刷新
		setCustomerInfo({
			commit
		}, data) {
			commit('SET_MUSTFRESH', data)
		},
		// 设置车辆信息
		setVehicleInfo({
			commit
		}, data) {
			commit('SET_VEHICLEINFO', data);
		},
		// 设置发行系统用户编号
		setTokenId({
			commit
		}, data) {
			commit('SET_TOKENID', data)
		},
		// 设置接口调用凭证
		setAccessToken({
			commit
		}, data) {
			commit('SET_ACCESSTOKEN', data)
		},
		// 设置收货地址
		setRecieveAddress({
			commit
		}, data) {
			commit('SET_RECIEVEADDRESS', data)
		},
		// 设置产品信息
		setProductInfo({
			commit
		}, data) {
			commit('SET_PRODUCTINFO', data)
		},
		//设置欠费信息
		setOWeData({
			commit
		}, data) {
			commit('SET_OWEDATA', data)
		},
		//新办申请单id
		setApplyId({
			commit
		}, data) {
			commit('SET_APPLYID', data)
		},
		//新办申请单id
		setIssueVehicleInfo({
			commit
		}, data) {
			commit('SET_ISSUE_VEHICLEINFO', data)
		},
		//售后业务类型
		setAfterSaleBusinessType({
			commit
		}, data) {
			commit('SET_AFTERSALEBUSINESSTYPE', data)
		},
		//设置欠费信息
		setRefundList({
			commit
		}, data) {
			commit('SET_REFUNDLIST', data)
		},
		setVehicleDetail({
			commit
		},data){
			commit('SET_VEHICLEDETAIL',data)
		}

	}
})

export default store
