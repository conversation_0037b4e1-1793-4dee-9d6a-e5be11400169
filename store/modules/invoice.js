const state = {
	historyDetail: {}, //售后车辆信息
	titleDetail: {}, //抬头详情
	// timer: null, //计时器
	// countDown: 60, //读秒
	timerData: {}
}
const getters = {
	historyDetail: state => state.historyDetail,
	titleDetail: state => state.titleDetail,
	timerData: state => state.timerData,
}
const mutations = {
	SET_HISTORYDETAIL: (state, data) => {
		state.historyDetail = data
	},
	SET_TITLEDETAIL: (state, data) => {
		state.titleDetail = data
	},
	SET_TIMER: (state, data) => {
		state.timerData = {
			...data.timerListData
		}
		state.timerData[data.id].countDown -= 1
		// // 如果计时器为0，重置为60秒
		if (state.timerData[data.id].countDown == 0) {
			state.timerData[data.id].countDown = null
			clearInterval(state.timerData[data.id].timer);
		}
	},

}

const actions = {
	setHistoryDetail({
		commit
	}, data) {
		commit('SET_HISTORYDETAIL', data);
	},
	setTitleDetail({
		commit
	}, data) {
		commit('SET_TITLEDETAIL', data);
	},
	setTimer({
		commit,
		state
	}, data) {
		data.timerListData[data.id].timer = setInterval(() => {
			commit('SET_TIMER', data);
		}, 1000);
	}
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}