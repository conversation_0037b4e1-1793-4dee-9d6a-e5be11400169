import request from "@/common/request-1/request.js";
import interfaces from "@/common/api/interface_4501.js";
const state = {
	gxCardTypeAllOptions: [],
}
const getters = {
	gxCardTypeAllOptions: state => state.gxCardTypeAllOptions,
}
const mutations = {
	SET_GXCARDTYPEALLOPTIONS: (state, options) => {
		state.gxCardTypeAllOptions = options
	},
}

const actions = {
	getAllOptions({
		commit
	}, params) {
		return new Promise((resolve, reject) => {
			const enParams = {
				routePath: interfaces.getDict.method,
				bizContent: params
			}
			request.post(interfaces.issueRoute, {
					data: enParams,
				}).then((res) => {
					console.log()
					let result = res.data;
					for (let i = 0; i < result.length; i++) {
						result[i].value = result[i].dictCode
						result[i].label = result[i].dictName
					}
					console.log(result,'------result');
					commit('SET_GXCARDTYPEALLOPTIONS', result);
					resolve(res);
				})
				.catch((err) => {
					reject(err)
				});
		})
	},
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}
