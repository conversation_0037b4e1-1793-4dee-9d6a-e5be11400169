const state = {
	afterSaleVehicle: null, //售后车辆信息
	afterSaleDetails: null, //售后订单信息
	afterSaleBusinessType: '', // 售后业务类型
	converId: '' //产品转换订单id
}
const getters = {
	afterSaleVehicle: state => state.afterSaleVehicle,
	afterSaleDetails: state => state.afterSaleDetails,
	afterSaleBusinessType: state => state.afterSaleBusinessType,
	converId: state => state.converId,
}
const mutations = {
	SET_AFTERSALEVEHICLE: (state, data) => {
		state.afterSaleVehicle = data
	},
	SET_AFTERSALEDETAILS: (state, data) => {
		state.afterSaleDetails = data
	},
	SET_AFTERSALEBUSINESSTYPE: (state, data) => {
		state.afterSaleBusinessType = data
	},
	SET_CONVERID: (state, data) => {
		state.converId = data
	}
}

const actions = {
	setAfterSaleVehicle({
		commit
	}, data) {
		commit('SET_AFTERSALEVEHICLE', data);
	},
	setAfterSaleDetails({
		commit
	}, data) {
		commit('SET_AFTERSALEDETAILS', data);
	},
	setAfterSaleBusinessType({
		commit
	}, data) {
		commit('SET_AFTERSALEBUSINESSTYPE', data);
	},
	setConverId({
		commit
	}, data) {
		commit('SET_CONVERID', data);
	}
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}