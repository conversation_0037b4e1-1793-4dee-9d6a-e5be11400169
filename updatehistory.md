Version 1.3.3
修改token过期提示和跳转问题

Version 1.3.2
修改验签失败的问题

Version 1.3.1
1.修改无车辆和用户时顶部文字提示
2.修改卡状态查询的一些问题
3.修改提示信息

Version 1.3.0


Version 1.2.9
1.前装办理

Version 1.2.8
1.etc账户名下有没车辆时，样式修改

Version 1.2.7
1.前装发行(隐藏)
2.渠道二维码(隐藏)
3.检测设备(隐藏)
4.实名认证身份证去掉空格
5.车牌验证只支持蓝色和渐变绿
6.变更经办人改为表单提交数据
7.申办ETC流程优化（个人身份证页面取消，路由层级控制在5层内）
8.车辆关联支付页面，没有可签约渠道时，新增渠道绑定
9.按钮添加点击效果
10.账户列表部门改为分支机构
11.查询所有车辆bug修改
12.车牌号校验正则修改
13.跳转解除车牌占用页面传值为空导致接口报错bug修复
14.客服接口bug修改
15.解除车牌占用正副本车牌号不一致，不允许挂起
16.意见反馈文案修改

Version 1.2.6
1.开放修改密码入口

Version 1.2.5
1.实名认证尾号x转大写
2.选择发行服务机构添加搜索
3.前装发行(屏蔽)
4.渠道二维码(屏蔽)
5.动态加密aeskey
6.获取openid和实名认证不再传秘钥
7.屏蔽修改密码入口

Version 1.2.4
1.tokenId只用微信登录接口返回的

Version 1.2.3
1.我的订单，售后订单，投诉工单页面新增空数据展示
2.申請ETC先选择发行方在签约
3.查询渠道列表接口替换
4.在车辆关联支付方式,签约支付方式，需获取当前已签约渠道，并根据5.10接口剔除不在5.10接口范围内的已签约渠道。
5.更多页面，添加loading，等账户列表获取完后再操作
6.微信登录后新增短信验证码登录流程
7.投诉界面删除延期扣费和未整合扣费
8.首页底部显示在某些手机重叠显示，根据高度计算底部空间，过小就修改成relative定位
9.修复有个人账户且有企业户但是都没车的情况下，不能进入预约通行界面的问题

Version 1.2.2
1.变更企业经办人信息输入完成后，点击变更需要弹出一个缓冲提示，另外变更成功之后弹出一个提示 点击确认再回首页
2.个人信息不可编辑
3.支付账户管理，我的车辆-个人车辆，关于我们，预约通行，ETC发行图标替换成背景图片，防止延迟image标签延迟加载图片
4.投诉工单页面，个人资料，我的车辆，数据渲染延迟加loading在页面渲染完成之后一起显示
5.售后订单接口替换,获取obu接口替换，车辆信息接口替换

Version 1.2.1
1.新增月账单(先隐藏)
2.投诉工单页面css优化
3.投诉用单新增获取预处理工单，与处理完后的工单合并去重展示
4.更多中添加账号同步
5.账户列表企业新增证件号码、证件类型显示
6.我的车辆列表，针对线下办理车辆需要判断是否有正常OBU才显示
7.企业经办人变更文字说明修改
8.车辆信息详情页，新增车辆使用性质

Version 1.2.0
1.实名认证成功之后 要去获取户列表 如果户列表为空或者没有个人账户才开户
2.接口处理错误提示优化
3.收货地址电话号码正则修改
4.实名认证默认不跳转城市服务，点击提交调用
5.实名认证返回无效的code，直接提示重新走登录流程

Version 1.1.9
1.个人中心底部留白
2.实名认证，用户未授权认证，点击认证按钮，重新调用城市服务认证
3.未获取手机号码的用户，在实名认证页面点击获取手机号码按钮后，会调用提交认证接口
4.首页不再调用wx.login
5.开启分包优化，部分只被一个模块引用的自定义组件分包，减少主包大小
6.分包预载
7.实名认证后调用开户接口
8.首页点击办理的时候 如果是未实名直接先实名认证
9.预约通行已认证未开户的情况下，直接调用小程序
10.个人中心已实名未开户跳转，默认开户后跳转
11.账户列表添加部门
12.个人中心和登录页图标加载有延迟
13.登录信息过期，重新登录，已经认证的直接跳转首页
14.提交认证按钮重复点击问题
15.个人资料显示头像，姓名，昵称，性别，电话，证件号码
16.更多修改手机号码隐藏
17.用户未在微信进行实名认证，提示请到微信进行实名认证


Version 1.1.4
1、修正部分手机底部文字覆盖BUG
2、调整选择通行省份界面，尽量一页显示完整

Version 1.1.3
1、新增自助工单功能
2、在我的ETC界面，新增我要投诉、我的投诉工单入口
3、在首页新增我要投诉的入口

