<template>
	<view class="">
		<view class="ad-swiper-wrap">
			<swiper @change="change" :interval="interval" circular :duration="duration" :autoplay='autoplay'
				style="height: 250rpx;background-color: rgb(243, 244, 246);">
				<swiper-item class="swiper-item" v-for="(item, index) in bannerList" :key="index">
					<view class="list-image-wrap" @tap.stop.prevent="listClick(item)"
						:class="[uCurrent != index ? 'list-scale' : '']">
						<image class="swiper-image" :src="item.adPic" mode="">
						</image>
					</view>
				</swiper-item>
			</swiper>
			<view class="swiper-indicator" style="top: auto;bottom: 4px;justify-content: center;padding: 0px 8px;">
				<view class="indicator-item-round" :class="{ 'indicator-item-round-active': index == uCurrent }"
					v-for="(item, index) in bannerList" :key="index"></view>
			</view>
		</view>
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:confirm-text="modal.confirmText" @confirm="onConfirmHandle">
			<view class="message-content-wrapper">
				<view class="title">提示</view>
				<view class="msg_desc">

					<view class="msg_desc-content">
						{{dislogMsg}}
					</view>
				</view>
			</view>
		</neil-modal>
	</view>

</template>

<script>
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')
	import {
		getLoginUserInfo
	} from '@/common/storageUtil.js'
	import {
		throttle
	} from '@/common/util.js'
	export default {
		props: {
			type: {
				type: String,
				default: '1'
			}
		},
		data() {
			return {
				disableTouch: true,
				autoplay: true,
				interval: 5000,
				duration: 500, // 滚动一个周期的时间长，单位ms
				dislogMsg: "", //弹窗提示内容
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					confirmText: '确认'
				},
				uCurrent: 0,
				bannerList: [], // 广告图列表
				exposureList: [], // 曝光数据列表
				isLoading: false
			}
		},
		components: {

		},
		created() {
			if (process.env.NODE_ENV === "development") {
				this.autoplay = false
			}
			this.getAdvBannerList();
		},
		beforeDestroy() {
			// 组件销毁时发生未上报数据
			if (this.exposureList.length) {
				this.exposureHandle()
			}
		},
		methods: {
			stopTouchMove() {
				return false;
			},
			// 获取广告图列表
			getAdvBannerList() {
				let param = {
					displayPosition: this.type
				}
				this.$request
					.post(this.$interfaces.advBannerList, {
						data: param
					}).then(res => {
						console.log('bannerList=======>>>', res)
						if (res.code == 200 && res.data) {
							this.bannerList = res.data;


						}
					}).catch(err => {

					})
			},

			change(e) {
				let current = e.detail.current;
				this.uCurrent = current;
				if (e.detail.source == 'autoplay') {
					let row = this.bannerList[current] || {};
					this.acquisitionHandle(row)

				}
			},
			// 广告图点击事件
			listClick(row) {
				let params = {
					bizType: '1', //1-广告、2-资讯
					bizId: row.adId,
					eventType: 2, //1-曝光、2-点击
					eventTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
					userId: getLoginUserInfo().userId
				}
				this.eventUpload([params])
				// 点击H5页面类型广告
				if (row.jumpType == '1') {
					uni.navigateTo({
						url: '/pages/uni-webview/uni-webview?ownPath=' +
							encodeURIComponent(row.jumpUrl)
					})
				}
				// 点击小程序类型广告
				if (row.jumpType == '2') {
					uni.navigateToMiniProgram({
						appId: row.appid,
						path: row.jumpUrl,
						envVersion: 'trial'
					});
				}
				// 点击弹窗类型广告
				if (row.jumpType == '3') {
					this.modal.show = true;
					this.dislogMsg = row.popupContent || ''
				}
				// 点击文章类型广告
				if (row.jumpType == '4') {
					uni.navigateTo({
						url: '/pagesC/infoBusiness/detail?informationId=' + row.linkId
					})
				}
			},
			onConfirmHandle() {
				this.modal.show = false;
				this.dislogMsg = '';
			},
			// 曝光数据采集
			acquisitionHandle(row) {
				if (row.adId) {
					let obj = {
						bizType: '1', //1-广告、2-资讯
						bizId: row.adId,
						eventType: 1, //1-曝光、2-点击
						eventTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
						userId: getLoginUserInfo().userId
					}
					this.exposureList.push(obj);
					this.throttleExposure();
				}
			},
			throttleExposure: throttle(function(...args) {
				this.exposureHandle(...args)
			}, 20000),
			exposureHandle() {
				let list = JSON.parse(JSON.stringify(this.exposureList))
				this.exposureList = []
				this.eventUpload(list)
			},
			// 事件统计
			eventUpload(params) {
				if (!(params && params.length)) return;
				if (this.isLoading) return;
				this.isLoading = true
				this.$request
					.post(this.$interfaces.trackUpload, {
						data: {
							list: params
						}
					}).then(res => {
						this.isLoading = false
					}).catch(() => {
						this.isLoading = false
					})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.ad-swiper-wrap {
		position: relative;
		overflow: hidden;
		transform: translateY(0);

		.swiper-image {
			width: 100%;
			will-change: transform;
			height: 100%;
			/* #ifndef APP-NVUE */
			display: block;
			/* #endif */
			/* #ifdef H5 */
			pointer-events: none;
			/* #endif */
		}

		.swiper-item {
			display: flex;
			overflow: hidden;
			align-items: center;
		}

		.list-image-wrap {
			width: 100%;
			height: 100%;
			flex: 1;
			transition: all 0.5s;
			overflow: hidden;
			box-sizing: content-box;
			position: relative;
		}

		.swiper-indicator {
			padding: 0 8px;
			position: absolute;
			display: flex;
			flex-direction: row;
			width: 100%;
			z-index: 1;

		}


		.indicator-item-round {
			width: 14rpx;
			height: 14rpx;
			margin: 0 6rpx;
			border-radius: 20rpx;
			transition: all 0.5s;
			background-color: rgba(0, 0, 0, 0.3);
		}

		.indicator-item-round-active {
			width: 34rpx;
			background-color: rgba(255, 255, 255, 0.8);
		}
	}



	.message-content-wrapper {
		.title {
			text-align: center;
			font-weight: 700;
			font-size: 34rpx;
			padding: 25rpx 50rpx;
			color: rgba(0, 0, 0, 0.9);
		}

		.msg_desc {
			padding: 0 30rpx;
			color: rgba(0, 0, 0, 0.5);
			font-size: 30rpx;
			text-align: left;

			.msg_desc-content {
				text-indent: 2em;
			}
		}
	}
</style>