import vue from 'vue';
import {
	getTicket,
	getOpenid,
	getEtcAccountInfo,
	getCurrentCar
} from '@/common/storageUtil.js'
// 登录信息校验
let loginCheckHandle = function() {
	if (!getTicket()) {
		uni.showModal({
			title: '提示',
			content: '请先登录',
			success: (res) => {
				if (res.confirm) {
					uni.reLaunch({
						url: '/pagesD/login/p-login'
					})
				}
			}
		})
		return true
	}
	return false
}
// 校验ETC用户选择
let etcAccountCheck = function() {
	if (!(getEtcAccountInfo() && Object.keys(getEtcAccountInfo()).length)) {
		uni.showModal({
			title: '提示',
			content: '请先绑定ETC用户',
			success: function(res) {
				if (res.confirm) {
					uni.navigateTo({
						url: '/pagesB/accountBusiness/accountList/accountList'
					})
				}
			}
		})
		return true
	}
	return false
}
let skipControlHandle = function(event) {
	let handle = {
		'spread': spreadHandleFn
	}
	if (!handle[event]) return true;
	handle[event] && handle[event]();
	return false;
}

// 拓展平台跳转链接
let spreadHandleFn = function() {
	if (loginCheckHandle()) return;
	if (etcAccountCheck()) return;
	let etcAccountInfo = getEtcAccountInfo() && Object.keys(getEtcAccountInfo()).length ? getEtcAccountInfo() : {}
	let vehicleInfo=getCurrentCar()&& Object.keys(getCurrentCar()).length?getCurrentCar():{}
	let data = {
		ticket: getTicket() || '',
		custMastId: etcAccountInfo.custMastId || '',
		openId: getOpenid(),
		// carNo:vehicleInfo.vehicle_code || '',
		// carColor: vehicleInfo.vehicle_color || '',
		type: 'home'
	}
	
	vue.prototype.$request
		.post(vue.prototype.$interfaces.expandUrl, {
			data: data
		})
		.then((res) => {
			console.log(res, '获取拓展业务跳转url');
			if (res.code == 200) {
				const callcenter = res.data
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' +
						encodeURIComponent(callcenter)
				})
			} else {
				uni.showModal({
					title: '提示',
					content: res.msg,
					showCancel: false
				})
			}

		})
		.catch((error) => {
			uni.showModal({
				title: '提示',
				content: error.msg,
				showCancel: false
			})
		})
}
export {
	skipControlHandle
}
