<template>
	<view>
		<movable-area :style="{height:`calc(100% - ${isIos ? '249' : '189'}rpx)`}">
			<movable-view :x="x" :y="y" direction="all" @change="onChange" animation="true">
				<view class="customerService" @click="toWebView">
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/onlineService.png" class="customerService-img" mode="aspectFill"></image>
				</view>
			</movable-view>
		</movable-area>
	</view>
</template>

<script>
	import {
		getTicket
	} from '@/common/storageUtil.js'
	export default {
		name: "customerService",
		data() {
			return {
				x: 300,
				y: 500,
				old: {
					x: 0,
					y: 0
				},
			}
		},
		computed: {
			isIos() {
				return uni.getSystemInfoSync().platform === "ios";
			}
		},
		mounted() {

			let info = uni.getSystemInfoSync()
			console.log(info);
			this.x = info.safeArea.width - 75
			this.y = info.safeArea.height - 325

		},
		methods: {
			onChange: function(e) {
				this.old.x = e.detail.x
				this.old.y = e.detail.y
			},
			toWebView() {
				if (!getTicket()) {
					uni.showModal({
						title: '提示',
						content: '请先登录',
						success: (res) => {
							if (res.confirm) {
								uni.reLaunch({
									url: '/pagesD/login/p-login'
								})
							}
						}
					})
					return
				}
				var callcenter =
					'https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027'
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' +
						encodeURIComponent(callcenter)
				})
			}
		}
	}
</script>

<style lang="scss">
	movable-view {
		pointer-events: auto;
		z-index: 999;
		position: absolute;
		display: inline-block;
		height: 10px;
		left: 0;
		top: 0;
		width: 10px;
		transform-origin: center center;
		will-change: auto;
	}

	movable-area {
		position: fixed;
		height: 100%;
		width: 100%;
		pointer-events: none;
		top: 0;
		display: block;
	}

	.customerService {
		width: 102rpx;
		height: 138rpx;
	}

	.customerService .customerService-img {
		display: block;
		width: 100%;
		height: 100%;
	}
</style>
