<template>
	<view>
		<view class="u-f-a u-f-dr  u-f-ajs uplaod" style="background: #FFFFFF;">
			<view class="cu-form-group u-f-dc">
				<view class="flex-sub">
					<view style="width:240upx;height:150upx;" class="bg-img position-father" @tap="ViewImage(leftPath)" v-if="leftPath !=''">
						<image :src="leftPath" mode="aspectFit" style="width:240upx;height:150upx;"></image>
						<view class="cu-tag bg-brown position-right" @tap.stop="DelImg('left')">
							<text class='cuIcon-close'></text>
						</view>
					</view>
					<view class="image-show" style="width:240upx;height:150upx;" @tap="ChooseImage({side:1})" v-if="leftPath ==''">
						<image src="/static/afterSale/yyzz.png" style="width:240upx;height:150upx;"></image>
					</view>
				</view>
				<view class="id-tip">企业营业执照</view>
			</view>
		</view>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true" :size="500"
		 :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>

</template>

<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import {
		dateFtt
	} from '@/common/helper.js';
	import {
		getTokenId,
		getStore,
		setStore,
		getBusinessTypes,
		getAccountId
	} from '@/common/storageUtil.js'
	import Api from '@/common/api/index.js'
	import tLoading from '@/components/common/t-loading.vue';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			cpimg,
			tLoading
		},
		props: {
			sourceType: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				imgList: [],
				showLoad: false,
				isLoading: false,
				showTag: false,
				bannerShow: false,
				state: true,
				identity: {},
				returnData: {},
				results: '',
				vehicleId: '',
				showInfo: false,
				leftPath: '',
				rightPath: '',
				showright: false,
				showleft: false,
				sides: 1,
				ownFlag: 1
			}

		},
		methods: {
			ChooseImage({
				side
			}) {
				this.sides = side;
				// this.ownFlag=1;
				// console.log(JSON.stringify(this.$refs))
				this.$refs.cpimg._changImg(this.sourceType);
			},
			////图片压缩成功
			cpimgOk(file) {
				console.log(this.sides)
				// this.isLoading = true;
				// 正面
				if (this.sides == 1) {
					this.leftPath = file.toString()
					var imgStr = this.leftPath.split(';')[1].split(",")[1] + '';
					this.$emit('leftImage', imgStr);
					this.showleft = true
				}
				let params = {
					imageStr: imgStr,
					type: 1
				};
				let data = {

					data: params
				};
				this.isLoading = true;
				request(this.$interfaces.ocrBusiness, data, (res) => {
					this.isLoading = false;
					this.$emit('on-change',this.sides, res.data.data)
				}, (msg) => {
					this.isLoading = false;
				})
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e)
			},
			ViewImage(path) {
				let newArr = [];
				newArr.push(path)
				uni.previewImage({
					urls: newArr
				});
			},
			DelImg(e) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							if (e == 'left') {
								this.leftPath = '';
							} else if (e == 'right') {
								this.rightPath = '';
							}
							this.$emit("delImg", e);
						}
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.uplaod {
		display: flex;
		justify-content: center;
	}

	.id-tip {
		font-size: 28upx;
		color: #666;
		line-height: 80upx;
	}

	.position-father {
		position: relative;
	}

	.position-right {
		position: absolute;
		right: 0upx;
	}

	.cu-form-group+.cu-form-group {
		border: 0upx;
	}
</style>
