<template>
	<view>
		<view class="u-f-a u-f-dr  u-f-ajs uplaod" style="background: #FFFFFF;">
			<view class="cu-form-group u-f-dc">
				<view class="flex-sub">
					<view style="width:240upx;height:150upx;" class="bg-img position-father" @tap="ViewImage(leftPath)" v-if="leftPath !=''">
						<image :src="leftPath" mode="aspectFit" style="width:240upx;height:150upx;"></image>
						<view class="cu-tag bg-brown position-right" @tap.stop="DelImg('left')">
							<text class='cuIcon-close'></text>
						</view>
					</view>
					<view class="image-show" style="width:188upx;height:154upx;" @tap="ChooseImage({side:1})" v-if="leftPath ==''">
						<image src="/static/afterSale/sign.png" style="width:188upx;height:154upx;"></image>
					</view>
				</view>
				<view class="id-tip">正面</view>
			</view>

			<view class="cu-form-group u-f-dc">
				<view class="flex-sub">
					<view style="width:240upx;height:150upx;" class="bg-img position-father" v-if="rightPath !=''" @tap="ViewImage(rightPath)">
						<image :src="rightPath" mode="aspectFit" style="width:240upx;height:150upx;"></image>
						<view class="cu-tag bg-brown position-right" @tap.stop="DelImg('right')">
							<text class='cuIcon-close'></text>
						</view>
					</view>
					<view class="image-show" style="width:188upx;height:154upx;" @tap="ChooseImage({side:2})" v-if="rightPath ==''">
						<image src="/static/afterSale/addImage.png" style="width:188upx;height:154upx;"></image>
					</view>
				</view>
				<view class="id-tip">侧面</view>
			</view>

		</view>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true" :size="500"
		 :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>

</template>

<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import {
		dateFtt
	} from '@/common/helper.js';
	import {
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getStore,
		getBusinessTypes,
		getCurrUserInfo,
		setCurrUserInfo,
		getAccountId
	} from '@/common/storageUtil.js'
	import Api from '@/common/api/index.js'
	import tLoading from '@/components/common/t-loading.vue';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			cpimg,
			tLoading
		},
		props: {
			sourceType: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				imgList: [],
				showLoad: false,
				isLoading: false,
				showTag: false,
				bannerShow: false,
				state: true,
				ownFlag:'-1',
				identity: {
					vin: '', //车架号
					engineNum: '', //发动机号
					issueDate: '', //发证日期
					name: '', //姓名
					plateNum1: '', //正页车牌号
					registerDate: '', //发证日期
					useCharacter: '', //使用性质:1:营运2:非营运
					vehicleType: '', //车辆类型
					plateNum2: '', //副页车牌号
					fileNum: '', //档案编号
					approvedCount: '', //核定人数 核定人数
					totalMass: '', //总质量
					maintenanceMass: '', //整备质量
					permittedWeight: '', //核定载质量
					length: '', //长
					width: '', //宽
					height: '', //高
					permittedTowWeight: '', //准牵引总质量outsideDimensions
					brand_type: '', //品牌类型
					vehicle_identify_code: '', //车辆识别码
					external_dimensions: '', //外廓尺寸
				},
				returnData: {},
				results: '',
				vehicleId: '',
				showInfo: false,
				leftPath: '',
				rightPath: '',
				showright: false,
				showleft: false,
				sides: 1,
			}

		},
		methods: {
			ChooseImage({
				side
			}) {
				this.sides = side;
				// this.ownFlag=1;
				// console.log(JSON.stringify(this.$refs))
				this.$refs.cpimg._changImg(this.sourceType);
			},
			////图片压缩成功
			cpimgOk(file) {
				console.log(this.sides)
				// this.isLoading = true;
				// 正面
				if (this.sides == 1) {
					this.leftPath = file.toString()
					var imgStr = this.leftPath.split(';')[1].split(",")[1] + '';
					this.$emit('leftImage', imgStr);
				}else{
					this.rightPath = file.toString()
					var imgStr = this.rightPath.split(';')[1].split(",")[1] + '';
					this.$emit('rightImage', imgStr);
				}
				
				let params = {
					
					vehicleId: getCurrentCar().vehicleId,
					imageStr: imgStr,
					idType: this.sides == 1 ? 5 : 6
				};
				let data = {
					
					data: params
				};
				this.isLoading = true;
				console.log(data,'11111');
				request(this.$interfaces.certifyVehicleMateria, data, (res) => {
					this.isLoading = false;
					if(this.sides == 1){
						this.$emit('changeCarInfo',1)
					}else{
						this.$emit('changeCarInfo',2)
					}
				}, (msg) => {
					this.isLoading = false;
				})	
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e)
			},
			ViewImage(path) {
				let newArr = [];
				newArr.push(path)
				uni.previewImage({
					urls: newArr
				});
			},
			DelImg(e) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							if (e == 'left') {
								this.leftPath = '';
							} else if (e == 'right') {
								this.rightPath = '';
							}
							this.$emit("delImg", e);
						}
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.uplaod{
		display: flex;
		justify-content: space-around;
	}
	.id-tip {
		font-size: 28upx;
		color: #666;
		line-height: 80upx;
	}

	.position-father {
		position: relative;
	}

	.position-right {
		position: absolute;
		right: 0upx;
	}

	.cu-form-group+.cu-form-group {
		border: 0upx;
	}
</style>

