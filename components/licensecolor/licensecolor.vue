<template>
	<view class="">
		<dartCard>
			<view slot="title" class="g-flex g-flex-align-center">
				<view class="">
					{{title}}
				</view>
				<view class="" v-if="desc" style="color: #F56C6C;font-weight: 400;">
					（{{desc}}）
				</view>
			</view>
			<view class="plate_color">
				<view v-for="(item,index) in palteColorList" :key="index" class="plateColorList" @click="slectPlateColor(index)">
					<view class="plateColor-img">
						<image :src="item.icon" class="plateColor" mode='aspectFilt'></image>
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/icon_select_license_plate_color.png" v-show="item.checked"
							class="checkplateColor" width="40rpx" height="40rpx" mode='aspectFilt'></image>
					</view>
					<view class="plateColor-desc">
						{{item.label}}
					</view>
				</view>
				<view class="plateColorList" @click="slectPlateColor('more')">
					<view class="plateColor-img">
						<picker @change="confirmColor" :value="otherPalteColor" :range="otherPalteColorList"
							range-key='label'>
							<view class="otherColor">{{!!otherPalteColor ? otherPalteColorFilter(otherPalteColor) : '选择其他颜色'}}</view>
						</picker>
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/icon_select_license_plate_color.png" v-show="!!otherPalteColor"
							class="checkplateColor" width="40rpx" height="40rpx" mode='aspectFilt'></image>
					</view>
					<view class="plateColor-desc">
						{{!!otherPalteColor ? otherPalteColorFilter(otherPalteColor) : '其他'}}
					</view>
					
				</view>
			</view>
		</dartCard>
	</view>
</template>

<script>
	import dartCard from './dart-card.vue';
	export default {
		props:{
			palteColor:{
				type:String,
				default:''
			},
			title:{
				type:String,
				default:'车牌颜色'
			},
			desc:{
				type:String,
				default:''
			}
		},
		data() {
			return {
				currentPalteColor:'0',//当前车牌颜色key
				otherPalteColorList: [{
						value: '3',
						label: '白色'
					},
					{
						value: '6',
						label: '蓝白渐变色'
					}
				],
				palteColorList: [{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/plate_blue.png",
						checked: false,
						value: '0',
						label:'蓝色'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/plate_gradient_green.png",
						checked: false,
						value: '4',
						label:'渐变绿色'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/plate_green.png",
						checked: false,
						value: '1',
						label:'黄色'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/plate_yellow_green.png",
						checked: false,
						value: '5',
						label:'黄绿色'
					},
					{
						icon: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/licensecolor/plate_black.png",
						checked: false,
						value: '2',
						label:'黑色'
					},
				],
				otherPalteColor:'',
				isLoading: false,
			}
		},
		components: {
			dartCard
		},
		watch:{
			palteColor(val){
				this.initColor();
			},
			currentPalteColor(value){
				 this.$emit('update:palteColor', value);
				 this.$nextTick(() => {
					 this.$emit('on-change',value);
				 })
				
			}
		},
		created() {
			this.initColor();
		},
		methods: {
			initColor(){
				this.currentPalteColor = this.palteColor
				
				for(let i = 0;i<this.palteColorList.length;i++){
					this.palteColorList[i].checked = false;
					if(!!this.currentPalteColor && this.palteColorList[i].value == this.currentPalteColor){
						this.palteColorList[i].checked = true;
					}
				}
			},
			// 选择车牌颜色
			slectPlateColor(index) {

				if (index === "more") {
					this.showMoreColor = true
				} else {
					this.otherChecked = false
					for (let obj of this.palteColorList) {
						obj.checked = false
					}
					this.palteColorList[index].checked = true
					this.otherPalteColor = '';
					this.currentPalteColor = this.palteColorList[index].value;
				}

			},
			confirmColor(e) {
				for (let obj of this.palteColorList) {
					obj.checked = false
				}
				this.otherChecked = true
				let index = e.detail.value;
				let item = this.otherPalteColorList[index]
				this.otherPalteColor = item.value;
				this.currentPalteColor = item.value;
			},
			otherPalteColorFilter(val){
				if(!val) return val;
				let data = this.otherPalteColorList.filter(item => {
					return item.value == val;
				})
				let label = data.length ? data[0].label : '';
				return label
			}
		}
	}
</script>

<style scoped lang="scss">
	.plate_color {
		position: relative;
		display: flex;
		flex-wrap: wrap;

		.plateColor {
			width: 199rpx;
			height: 64rpx;
		}

		.plateColorList {
			position: relative;
			margin-right: 30rpx;
			margin-bottom: 20rpx;
			 .plateColor-img{
				 position: relative;
				 display: block;
				 width: 199rpx;
				 height: 64rpx;
			 }
			 .plateColor-desc{
				 width: 100%;
				 text-align: center;
				 font-size: 30rpx;
				 font-weight: 400;
				 margin-top: 10rpx;
				 color: #666666;
			 }
			.checkplateColor {
				position: absolute;
				bottom: -8rpx;
				right: -8rpx;
				width: 40rpx;
				height: 40rpx;
			}
		}

		.otherColor {
			width: 199rpx;
			height: 64rpx;
			color: #666666;
			background: #ffffff;
			border: 1px solid #666666;
			border-radius: 4px;
			line-height: 64rpx;
			text-align: center;
			font-size: 30rpx;
		}
	}
</style>
