<template>
	<view>
		<view class="bg-white padding">
			<view class="cu-steps">
				<view class="cu-item" :class="index>num?'':'text-blue'" v-for="(item,index) in stepList" :key="index" v-if="showAfter">
					<text class="num" :class="status==='0'?'err':''" :data-index="index + 1"></text> {{item.name}}
				</view>
				<view v-if="!showAfter" class="steps" :class="index>num?'':'text-blue'" v-for="(item,index) in stepList" :key="index">
					<view v-if="index>num"><text class="num">{{index + 1}}</text></view>
					<view v-else>
						<image src="/static/etc/select.png" class="select" />
					</view>
					<text class="line" v-if="index !== stepList.length-1"></text>
					<view :class="!isPre ? 'name' : index > 0 ? 'name' : 'name fx'">{{item.name}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getBusinessTypes,
		getStore
	} from '@/common/storageUtil.js'
	export default {
		props: {
			num: {
				type: Number,
				required: true,
				default: 1
			},
			status: {
				type: String, // 0 失败，1成功
				// required: true,
				default: '1'
			},
			list: {
				type: Array,

				default () {
					return [{
						name: '用户信息'
					}, {
						name: '银行信息'
					}, {
						name: '车辆信息'
					}, {
						name: '一键激活'
					}]
				}
			},
			showAfter: {
				type: Boolean,
				default: false
			}

		},
		data() {
			return {
				isPre: false
			}
		},
		computed: {
			stepList() {
				let stepList = this.list



				if ((getStore('curBusiness') == 4501)) {
					stepList = [{
						name: '用户信息'
					}, {
						name: '车辆信息'
					}, {
						name: '银行信息'
					}, {
						name: '一键激活'
					}]
				}
				return stepList
			}
		}
	}
</script>

<style lang="scss">
	/* 	.cu-steps .cu-item:not([class*="text-"]){
		color: #D2F1F0
	}
	.cu-steps .cu-item .num{
		color: #FFFFFF
	}
	.cu-steps .cu-item[class*="text-"] .num::after{
		color: var(--blue)
	} */
	.cu-steps {
		display: flex;

		.steps {
			flex: 1;
			text-align: center;
			position: relative;
			height: 88rpx;

			.num,
			.select {
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				border: 1px solid #666;
				display: inline-block;
				line-height: 40rpx;
			}

			.select {
				border: none;
				width: 44rpx;
				height: 44rpx;
			}

			.line {
				width: calc(100% - 80rpx);
				display: inline-block;
				position: absolute;
				border: 0.5px solid #666;
				top: 20rpx;
				left: 110rpx;
			}

			.name {
				position: absolute;
				width: 100%;
				bottom: 0;
			}

			.fx {
				bottom: -30rpx;
			}
		}

		.text-blue {
			color: #0066E9;

			.num,
			.line {
				border-color: #0066E9;
			}
		}
	}
</style>
