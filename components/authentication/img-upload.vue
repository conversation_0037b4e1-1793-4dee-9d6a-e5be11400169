<template>
	<view>
		<view class="u-f-a u-f-new-dc">
			<view>
				<view class="cu-form-new-group">
					<view class="flex-sub">
						<view style="width:240upx;height:150upx;" class="bg-img position-father" @tap="ViewImage(leftPath)" v-if="leftPath !=''">
							<image :src="leftPath" mode="aspectFit" style="width:240upx;height:150upx;"></image>
							<view class="cu-tag bg-brown position-right" @tap.stop="DelImg('left')">
								<text class='cuIcon-close'></text>
							</view>
						</view>
						<view class="solids image-show" style="width:240upx;height:150upx;" @tap="ChooseImage({side:1})"  v-if="leftPath ==''">
							<image  src="/static/etc/image-upload.png" style="width:240upx;height:150upx;"></image>
						</view>
					</view>
				</view>
				<view class="id-tip">拍摄上传车主与车头合影</view>
			</view>
			<view>
				<view class="flex-sub" @tap="ViewImageSource()">
				
				</view>
				<view class="id-tip-hide">上传申请人手持身份证</view>
				<view class="id-tip-hide">与车头合影，合影需显</view>
				<view class="id-tip-hide">示清晰的车牌号码。</view>
			</view>
		</view>
		<neil-modal 
			@confirm="methodEnsure"
		    :show="alltoastData.showFlag" 
			:align="alltoastData.allign"
		    :title="alltoastData.title" 
		    :content="alltoastData.msg"
			:confirmText="alltoastData.btnTextEnsure" 
		    :show-cancel="alltoastData.btnTextCloseFlag">
		</neil-modal>
		<cpimg ref="cpimg" 
		@result="cpimgOk" 
		@err="cpimgErr" 
		:flag='ownFlag'
		:number="1" 
		:fixOrientation="true" 
		:size="500" 
		:maxWidth="800" 
		:ql="0.9" 
		type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import {mapGetters, mapActions} from 'vuex'
	import neilModal from '@/components/neil-modal/neil-modal.vue';
	import tLoading from '@/components/common/t-loading.vue';
	
	export default {
		components: {
			cpimg,
			neilModal,
			tLoading
		},
		props: {
			carType:{
				type: Number,
				default:1
			},
		},
		data(){
			return {
				ownFlag:0,
				showLoad:false,
				isLoading: false,
				sides:1,
				leftPath:'',
				bannerShow:false,
				state:true,
				base64Img:'',
				results:'',
				returnData:{},
				showInfo:false,
				isLiving:false,
				isLivingPath:'',
				certifyId:'',
				showright:false,
				showleft:false,
				picbase64:'data:image/png;base64,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'
			}
			
		},
		
		methods:{
			//选择图片
			ChooseImage({side}) {
				this.sides=side;
				this.$refs.cpimg._changImg(0);
			},
			////图片压缩成功
			cpimgOk(file) {
				//正面
				this.isLoading = true;
				if(this.sides==1){
					let that = this
					that.leftPath = file.toString()
					//console.log("图片路径:"+that.leftPath);
					var imgStr=that.leftPath.split(';')[1].split(",")[1]+'';
					this.$emit('leftImage',imgStr);
				}
				this.isLoading = false;
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e)
			},
			ViewImage(path) {
				let newArr=[];
				newArr.push(path)
				uni.previewImage({
					urls: newArr
				});
			},
			ViewImageSource(){
				let newArr=[];
				newArr.push(this.picbase64);
				uni.previewImage({
					urls: newArr
				});
			},
			DelImg(e) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {	
							if(e=='left'){
								this.leftPath='';
							}else if(e=='right'){
								this.rightPath='';
							}
						}
					}
				})
			},
		}
	}
</script>

<style scoped>
	.cu-form-new-group {
		padding: 1upx 30upx;
		display: flex;
		align-items: center;
		min-height: 100upx;
		justify-content: space-between;
	}
	.id-tip{
		font-size: 28upx;
		color: #333;
		line-height: 80upx;
	}
	.id-tip-hide{
		font-size: 28upx;
		color: #333;
		line-height: 40upx;
	}
	.position-father{
		position: relative;
	}
	.position-right{
		position: absolute;
		right: 0upx;
	}
	.u-f-new-dc{
		flex-direction:row;
		justify-content:space-around;
		background-color: #fff;
	}
	.flex-sub{
		display: flex;
		justify-content:center;
	}
</style>
