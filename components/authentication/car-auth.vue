<template>
	<view>
		<view class="u-f-a u-f-dr  u-f-ajs" style="background: #FFFFFF;">
			<view class="cu-form-group u-f-dc">
				<view class="flex-sub">
					<view style="width:240upx;height:150upx;" class="bg-img position-father" @tap="ViewImage(leftPath)" v-if="leftPath !=''">
						<image :src="leftPath" mode="aspectFit" style="width:240upx;height:150upx;"></image>
						<view class="cu-tag bg-brown position-right" @tap.stop="DelImg('left')">
							<text class='cuIcon-close'></text>
						</view>
					</view>
					<view class="solids image-show" style="width:240upx;height:150upx;" @tap="ChooseImage({side:1})" v-if="leftPath ==''">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_front.png" style="width:240upx;height:150upx;"></image>
					</view>
				</view>
				<view class="id-tip">拍摄上传行驶证（主页）</view>
			</view>

			<view class="cu-form-group u-f-dc">
				<view class="flex-sub">
					<view style="width:240upx;height:150upx;" class="bg-img position-father" v-if="rightPath !=''" @tap="ViewImage(rightPath)">
						<image :src="rightPath" mode="aspectFit" style="width:240upx;height:150upx;"></image>
						<view class="cu-tag bg-brown position-right" @tap.stop="DelImg('right')">
							<text class='cuIcon-close'></text>
						</view>
					</view>
					<view class="solids image-show" style="width:240upx;height:150upx;" @tap="ChooseImage({side:2})" v-if="rightPath ==''">
						<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vehicle_back.png" style="width:240upx;height:150upx;"></image>
					</view>
				</view>
				<view class="id-tip">拍摄上传行驶证（副页）</view>
			</view>

		</view>
		<neil-modal @confirm="methodEnsure" :show="alltoastData.showFlag" :align="alltoastData.allign" :title="alltoastData.title"
		 :content="alltoastData.msg" :confirmText="alltoastData.btnTextEnsure" :show-cancel="alltoastData.btnTextCloseFlag">
		</neil-modal>
		<cpimg ref="cpimg" @result="cpimgOk" @err="cpimgErr" :flag='ownFlag' :number="1" :fixOrientation="true" :size="500"
		 :maxWidth="800" :ql="0.9" type="base64">
		</cpimg>
		<tLoading :isShow="isLoading" />
	</view>

</template>

<script>
	import cpimg from "@/components/uni-yasuo/cpimg.vue"
	import {
		dateFtt
	} from '@/common/helper.js';
	import {
		setCurrentCar,
		getCurrentCar,
		getTokenId,
		setTokenId,
		getStore,
		getBusinessTypes,
		getCurrUserInfo,
		setCurrUserInfo,
		getAccountId
	} from '@/common/storageUtil.js'
	import Api from '@/common/api/index.js'
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import neilModal from '@/components/neil-modal/neil-modal.vue';
	import tLoading from '@/components/common/t-loading.vue';
	import {
		request
	} from '@/common/request-1/requestFunc.js'
	export default {
		components: {
			cpimg,
			neilModal,
			tLoading
		},
		props: {
			sourceType: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				ownFlag: 0,
				imgList: [],
				showLoad: false,
				isLoading: false,
				tanMessage: {
					text: "您上传的车辆档案未通过信息验证，请检查输入内容是否正确",
					btnText: "我知道了"
				},
				showTag: false,
				bannerShow: false,
				state: true,
				alltoastData: {
					allign: "left",
					msgShow: false, //弹层内容格式不同，false,true,显示的东西不一样
					showFlag: false, //弹层显示
					title: "", //弹框标题
					msg: "您已完成使命认证<br>可以申请办理ETC", //弹框内容
					btnTextClose: "", //左边关闭按钮
					btnTextCloseFlag: false, //左边关闭按钮显示状态
					btnTextEnsure: "申请办理" //右边确认按钮
				},
				identity: {
					vin: '', //车架号
					engineNum: '', //发动机号
					issueDate: '', //发证日期
					name: '', //姓名
					plateNum1: '', //正页车牌号
					registerDate: '', //发证日期
					useCharacter: '', //使用性质:1:营运2:非营运
					vehicleType: '', //车辆类型
					plateNum2: '', //副页车牌号
					fileNum: '', //档案编号
					approvedCount: '', //核定人数 核定人数
					totalMass: '', //总质量
					maintenanceMass: '', //整备质量
					permittedWeight: '', //核定载质量
					length: '', //长
					width: '', //宽
					height: '', //高
					permittedTowWeight: '', //准牵引总质量outsideDimensions
					brand_type: '', //品牌类型
					vehicle_identify_code: '', //车辆识别码
					external_dimensions: '', //外廓尺寸
				},
				returnData: {},
				results: '',
				vehicleId: '',
				showInfo: false,
				leftPath: '',
				rightPath: '',
				showright: false,
				showleft: false,
				sides: 1,
			}

		},
		methods: {
			ChooseImage({
				side
			}) {
				this.sides = side;
				// this.ownFlag=1;
				// console.log(JSON.stringify(this.$refs))
				this.$refs.cpimg._changImg(this.sourceType);
			},
			////图片压缩成功
			cpimgOk(file) {
				console.log(this.sides)
				this.isLoading = true;
				// 正面
				if (this.sides == 1) {
					this.leftPath = file.toString()
					var imgStr = this.leftPath.split(';')[1].split(",")[1] + '';
					this.$emit('leftImage', imgStr);
				}else{
					this.rightPath = file.toString()
					var imgStr = this.rightPath.split(';')[1].split(",")[1] + '';
					this.$emit('rightImage', imgStr);
				}
				
				let params = {
					imageStr: imgStr,
					side: this.sides,
					accountId:getAccountId()
				};
				
				
				if (this.showright) {
					this.isLoading = true;
				}
				
				console.log('车辆ocr',params);
				this.$request.post(this.$interfaces.orcVehicleLicense, {
					data: params
				}).then(res => {
					if(res.resultCode === '0' && res.data){
						let data = res.data;
						let dataed = getCurrentCar() || {};
						let verhicleInfo = data.encryptedData || {}
						if(this.sides == 1){
							console.log("行驶证正面返回数据", verhicleInfo)
							dataed['leftPath'] = this.leftPath;
							dataed['vin'] = verhicleInfo.vin;
							dataed['engineNum'] = verhicleInfo.engineNum;
							dataed['issueDate'] = verhicleInfo.issueDate;
							dataed['name'] = verhicleInfo.name;
							dataed['plateNum'] = verhicleInfo.plateNum;
							dataed['registerDate'] = verhicleInfo.registerDate;
							dataed['useCharacter'] = verhicleInfo.useCharacter;
							dataed['vehicleType'] = verhicleInfo.vehicleType;
							dataed['imageIdSide1'] = data.imageId
							setCurrentCar(dataed);
							this.$emit("changeCarInfo", "true");
							this.identity.vin = verhicleInfo.vin; //车架号
							this.identity.engineNum = verhicleInfo.engineNum; //发动机号
							this.identity.issueDate = verhicleInfo.issueDate; //发证日期
							this.identity.name = verhicleInfo.name; //姓名
							this.identity.plateNum1 = verhicleInfo.plateNum; //主页车牌号
							this.identity.registerDate = verhicleInfo.registerDate; //注册日期
							this.identity.useCharacter = verhicleInfo.useCharacter; //使用性质
							this.identity.vehicleType = verhicleInfo.vehicleType; //车辆类型
							console.log('正本' + JSON.stringify(verhicleInfo))
							this.showleft = true;
							if (this.showleft && this.showright) {
								this.showInfo = true
								this.$emit("getAll", true);
								this.isLoading = false;
							} else {
								this.$emit("getAll", false);
							}
							if (this.identity.plateNum2 && this.identity.plateNum2 != '') {
								if (this.identity.plateNum1 != this.identity.plateNum2) {
									// this.alltoastData.btnTextEnsure = "我知道了";
									// this.alltoastData.msgShow = true;
									// this.alltoastData.allign = "center";
									// this.alltoastData.msg = "正本和副本车牌号不一致<br>请核对副本证件信息。"
									// this.alltoastData.showFlag = true;
									uni.showModal({
										title: "提示",
										content: "正副本车牌号不一致,请核对副本证件信息",
										showCancel:false,
										confirmText:"我知道了"
									})		
									this.$emit("getCarPersonFlag", false);
								} else {
									this.$emit("getCarPersonFlag", true);
								}
							}
						}else{
							console.log("行驶证反面返回数据", verhicleInfo)
							dataed['rightPath'] = this.rightPath;
							dataed['approvedCount'] = verhicleInfo.approvedCount;
							//拆分长宽高
							dataed['outsideDimensions'] = verhicleInfo.outsideDimensions;
							if (verhicleInfo.outsideDimensions.length > 14) {
								dataed['length'] = verhicleInfo.outsideDimensions.split("×")[0]
								dataed['width'] = verhicleInfo.outsideDimensions.split("×")[1]
								dataed['height'] = verhicleInfo.outsideDimensions.split("×")[2].split("m")[0];
							}
							dataed['totalMass'] = verhicleInfo.totalMass;
							dataed['maintenanceMass'] = verhicleInfo.maintenanceMass;
							dataed['permittedWeight'] = verhicleInfo.permittedWeight;
							dataed['imageIdSide2'] = data.imageId
							setCurrentCar(dataed);
							this.$emit("changeCarInfo", "true");
							this.identity.plateNum2 = verhicleInfo.plateNum; //副页车牌号
							this.identity.fileNum = verhicleInfo.fileNum; //档案编号
							this.identity.approvedCount = verhicleInfo.approvedCount; //核定人数
							this.identity.totalMass = verhicleInfo.totalMass; //总质量
							this.identity.maintenanceMass = verhicleInfo.maintenanceMass; //整备质量
							this.identity.permittedWeight = verhicleInfo.permittedWeight; //核定载质量
							this.identity.length = verhicleInfo.length; //长
							this.identity.width = verhicleInfo.width; //宽
							this.identity.height = verhicleInfo.height; //高
							this.identity.permittedTowWeight = verhicleInfo.permittedTowWeight; //准牵引总质量
							this.showright = true;
							if (this.showleft && this.showright) {
								this.showInfo = true;
								this.$emit("getAll", true);
								this.isLoading = false;
							} else {
								this.$emit("getAll", false);
							}
							
							if (this.identity.plateNum1 && this.identity.plateNum1 != '') {
								if (this.identity.plateNum1 != this.identity.plateNum2) {
									// this.alltoastData.btnTextEnsure = "我知道了";
									// this.alltoastData.msgShow = true;
									// this.alltoastData.allign = "center";
									// this.alltoastData.msg = "正本和副本车牌号不一致<br>请核对正本证件信息。"
									// this.alltoastData.showFlag = true;
									uni.showModal({
										title: "提示",
										content: "正副本车牌号不一致,请核对正本证件信息",
										showCancel:false,
										confirmText:"我知道了"
									})
									this.$emit("getCarPersonFlag", false);
								} else {
									this.$emit("getCarPersonFlag", true);
								}
							}
						}
					}
					this.isLoading = false;
				}).catch((error) => {
					this.isLoading = false;
				})
				return;
				request(requestObj.url, data, (res) => {
					let datas = typeof res == 'string' ? JSON.parse(res) : res;
					let dataed = getCurrentCar() || {}
					let verhicleInfo = {}
					if(getBusinessTypes() == 11){
						verhicleInfo = JSON.parse(datas.encryptedData);
					}else{
						verhicleInfo = JSON.parse(datas.idData);
					}
					if(this.sides == 1){
						console.log("行驶证正面返回数据", verhicleInfo)
						dataed['leftPath'] = this.leftPath;
						dataed['vin'] = verhicleInfo.vin;
						dataed['engineNum'] = verhicleInfo.engineNum;
						dataed['issueDate'] = verhicleInfo.issueDate;
						dataed['name'] = verhicleInfo.name;
						dataed['plateNum'] = verhicleInfo.plateNum;
						dataed['registerDate'] = verhicleInfo.registerDate;
						dataed['useCharacter'] = verhicleInfo.useCharacter;
						dataed['vehicleType'] = verhicleInfo.vehicleType;
						if(getBusinessTypes() == 11){
							dataed['imageIdSide1'] = datas.imageId
						}
						setCurrentCar(dataed);
						this.$emit("changeCarInfo", "true");
						this.identity.vin = verhicleInfo.vin; //车架号
						this.identity.engineNum = verhicleInfo.engineNum; //发动机号
						this.identity.issueDate = verhicleInfo.issueDate; //发证日期
						this.identity.name = verhicleInfo.name; //姓名
						this.identity.plateNum1 = verhicleInfo.plateNum; //主页车牌号
						this.identity.registerDate = verhicleInfo.registerDate; //注册日期
						this.identity.useCharacter = verhicleInfo.useCharacter; //使用性质
						this.identity.vehicleType = verhicleInfo.vehicleType; //车辆类型
						console.log('正本' + JSON.stringify(verhicleInfo))
						this.showleft = true;
						if (this.showleft && this.showright) {
							this.showInfo = true
							this.$emit("getAll", true);
							this.isLoading = false;
						} else {
							this.$emit("getAll", false);
						}
						if (this.identity.plateNum2 && this.identity.plateNum2 != '') {
							if (this.identity.plateNum1 != this.identity.plateNum2) {
								// this.alltoastData.btnTextEnsure = "我知道了";
								// this.alltoastData.msgShow = true;
								// this.alltoastData.allign = "center";
								// this.alltoastData.msg = "正本和副本车牌号不一致<br>请核对副本证件信息。"
								// this.alltoastData.showFlag = true;
								uni.showModal({
									title: "提示",
									content: "正副本车牌号不一致,请核对副本证件信息",
									showCancel:false,
									confirmText:"我知道了"
								})		
								this.$emit("getCarPersonFlag", false);
							} else {
								this.$emit("getCarPersonFlag", true);
							}
						}
					}else{
						console.log("行驶证反面返回数据", verhicleInfo)
						dataed['rightPath'] = this.rightPath;
						dataed['approvedCount'] = verhicleInfo.approvedCount;
						//拆分长宽高
						dataed['outsideDimensions'] = verhicleInfo.outsideDimensions;
						if (verhicleInfo.outsideDimensions.length > 14) {
							if(getBusinessTypes() == 11){
								dataed['length'] = verhicleInfo.outsideDimensions.split("×")[0]
								dataed['width'] = verhicleInfo.outsideDimensions.split("×")[1]
								dataed['height'] = verhicleInfo.outsideDimensions.split("×")[2].split("m")[0];
							}else{
								dataed['length'] = verhicleInfo.outsideDimensions.split("X")[0]
								dataed['width'] = verhicleInfo.outsideDimensions.split("X")[1]
								dataed['height'] = verhicleInfo.outsideDimensions.split("X")[2].split("m")[0];
							}
						}
						dataed['totalMass'] = verhicleInfo.totalMass;
						dataed['maintenanceMass'] = verhicleInfo.maintenanceMass;
						dataed['permittedWeight'] = verhicleInfo.permittedWeight;
						if(getBusinessTypes() == 11){
							dataed['imageIdSide2'] = datas.imageId
						}
						setCurrentCar(dataed);
						this.$emit("changeCarInfo", "true");
						this.identity.plateNum2 = verhicleInfo.plateNum; //副页车牌号
						this.identity.fileNum = verhicleInfo.fileNum; //档案编号
						this.identity.approvedCount = verhicleInfo.approvedCount; //核定人数
						this.identity.totalMass = verhicleInfo.totalMass; //总质量
						this.identity.maintenanceMass = verhicleInfo.maintenanceMass; //整备质量
						this.identity.permittedWeight = verhicleInfo.permittedWeight; //核定载质量
						this.identity.length = verhicleInfo.length; //长
						this.identity.width = verhicleInfo.width; //宽
						this.identity.height = verhicleInfo.height; //高
						this.identity.permittedTowWeight = verhicleInfo.permittedTowWeight; //准牵引总质量
						this.showright = true;
						if (this.showleft && this.showright) {
							this.showInfo = true;
							this.$emit("getAll", true);
							this.isLoading = false;
						} else {
							this.$emit("getAll", false);
						}
						
						if (this.identity.plateNum1 && this.identity.plateNum1 != '') {
							if (this.identity.plateNum1 != this.identity.plateNum2) {
								// this.alltoastData.btnTextEnsure = "我知道了";
								// this.alltoastData.msgShow = true;
								// this.alltoastData.allign = "center";
								// this.alltoastData.msg = "正本和副本车牌号不一致<br>请核对正本证件信息。"
								// this.alltoastData.showFlag = true;
								uni.showModal({
									title: "提示",
									content: "正副本车牌号不一致,请核对正本证件信息",
									showCancel:false,
									confirmText:"我知道了"
								})
								this.$emit("getCarPersonFlag", false);
							} else {
								this.$emit("getCarPersonFlag", true);
							}
						}
					}
					this.isLoading = false;
				}, (msg) => {
					this.isLoading = false;
				})	
			},
			//图片压缩失败
			cpimgErr(e) {
				console.log(e)
			},
			//弹框确认按钮
			methodEnsure(events) {
				if (events == "我知道了") {
					this.alltoastData.showFlag = false;

					// uni.navigateTo({
					// 	url: '/pages/uni-home/confirm-label/index/index'
					// })
				}
			},
			ViewImage(path) {
				let newArr = [];
				newArr.push(path)
				uni.previewImage({
					urls: newArr
				});

			},
			DelImg(e) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此照片吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							let data = getCurrentCar();
							if (e == 'left') {
								this.leftPath = '';
								data.leftPath = '';
							} else if (e == 'right') {
								this.rightPath = '';
								data.rightPath = '';
							}
							setCurrentCar(data);
							this.$emit("changeCarInfo", "false");
							this.$emit("getAll", false);
							this.$emit("delImg", e);
							//console.log(getCurrentCar())
						}
					}
				})
			},
		}
	}
</script>

<style scoped>
	.id-tip {
		font-size: 28upx;
		color: #333;
		line-height: 80upx;
	}

	.position-father {
		position: relative;
	}

	.position-right {
		position: absolute;
		right: 0upx;
	}

	.cu-form-group+.cu-form-group {
		border: 0upx;
	}
</style>
