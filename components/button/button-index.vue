<template>
	<view>
		<view class="u-f-ajc t-padding">
			<button hover-class="color-two" class="cu-btn shadow lg radius" :class="status==='1'?'color-two':'bg-indexstart'" style="width: 100%;height: 100upx;" :style="{background:isLoadding?'#98A4FF':''}" @tap="click()" >
				<text class="btn-text">{{title}}</text>
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			title:{
				type: String,
				required: true,
			},
			// clickButton: {
			// 	type: Function,
			// },
			status: {
				type: String,
				default: "0", // 0 正常，1不正常
			},
			isLoadding:{
				type: Boolean,
				default: false,
			}
		},
		data() {
			return{}
		},
		
		methods:{
			click(){
				if(this.isLoadding){
					return;
				}
				this.$emit("clickButton");
			}
		}
	}
</script>

<style>
	.color-two{
		background:#56a8fc;
		/* background: #01A863; */
		color: #FFFFFF;
	}
	.btn-text{
		font-size: 1.2em;
	}
</style>
