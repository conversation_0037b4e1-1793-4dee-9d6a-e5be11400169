<template>
	<view>
		<view class="u-f-ajc t-padding">
			<button hover-class="color-two" class="cu-btn shadow lg" :class="status==='1'?'color-two':'bg-blue'" style="width: 80%;height: 300upx;" :style="{background:isLoadding?'#98A4FF':''}" @tap="clickNext" >
				<text :class="isLoadding?'cuIcon-loading2 iconfont-spin':''" style="font-size:48px;">{{title}}</text>
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			title:{
				type: String,
				required: true,
			},
			// clickButton: {
			// 	type: Function,
			// },
			status: {
				type: String,
				default: "0", // 0 正常，1不正常
			},
			isLoadding:{
				type: Boolean,
				default: false,
			}
		},
		data() {
			return{}
		},
		
		methods:{
			clickNext(){
				if(this.isLoadding){
					return;
				}
				this.$emit("clickButton");
			}
		}
	}
</script>

<style>
	.color-two{
		background:#98A4FF;
		/* background: #01A863; */
		color: #FFFFFF;
	}
</style>
