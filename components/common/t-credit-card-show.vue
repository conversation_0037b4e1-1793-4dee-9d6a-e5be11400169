<template>
	<view class="t-credit-card">
		<!-- <view class="t-c-discount">{{creditCardInfo.info}}</view> -->
		<view class="t-c-top" >
			<view class="t-c-t-left">
				<view :class="getClass" :style="{ color:bankCodeToBgMap.get(creditCardInfo.type) }">
				</view>
			</view>
			<view class="t-c-t-right">
				<view class="u-f-ajs">
					<view>{{bankCodeToBankNameMap.get(creditCardInfo.type+"")}}</view>

				</view>
				<view  style="font-size: 24upx;line-height: 40upx;">
					{{cardTypeMap.get(''+creditCardInfo.cardType)}}
				</view>
				<view class="t-c-b-right"  style="font-size: 32upx;">{{creditCardInfo.accountNum}}</view>
				
			</view>
		</view>
	</view>
</template>

<script>
	import {
		bankCodeToIconMap,
		bankCodeToBgMap,
		cardTypeMap,
		payCardStatusMap,
		bankCodeToBankNameMap
	} from '@/common/systemConstant.js'
	export default {
		props: {
			creditCardInfo: {
				type: Object,
				required: true,
			},
		},
		data() {
			return {
				bankCodeToIconMap,
				bankCodeToBgMap,
				cardTypeMap,
				payCardStatusMap,
				bankCodeToBankNameMap
			};
		},
		computed:{
			getClass(){
				return bankCodeToIconMap.get(''+this.creditCardInfo.type);
			}
		},
		methods:{
			changeRelevance(){
				this.$emit("changeRelevance",this.creditCardInfo);
			},
			unty(){
				this.$emit("unty",this.creditCardInfo)
			}
		}
	}
</script>

<style scope>
	.t-credit-card {
		border: 1upx solid #E5E5E5;
		border-radius: 12upx;
		display: flex;
		flex-direction: column;
		margin-bottom: 30upx;
		background: #FFFFFF;
		/* justify-content; */

		/* -webkit-box-shadow: 0 0 10upx #CCCCCC; */
		/* -moz-box-shadow: 0 0 10upx #CCCCCC; */
		/* box-shadow: 0 0 10upx #CCCCCC; */
	}



	.t-c-top {
		
		padding: 10upx 14upx;
		border-top-left-radius: 6upx;
		border-top-right-radius: 6upx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.t-c-t-left {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: flex-start;
	}

	.t-c-t-left>view {
		font-size: 90upx;
		line-height: 120upx;
		text-align: center;
		
	}

	.t-c-t-right {
		flex: 3;
		font-size: 28upx;
		line-height: 50upx;
	}

	/* .t-c-t-right>view:first-of-type {
		font-size: 30upx;
	} */

	.t-c-t-r-bottom {
		display: flex;
		flex-direction: row;
		font-size: 20upx;
		
		/* margin-top: 20upx; */
	}

	.t-c-t-r-bottom>view:first-of-type {
		flex: 8;
		font-size: 20upx;
	}

</style>
