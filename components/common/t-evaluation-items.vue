<template>
  <view class="extra-items">
    <view class="detail-title" v-if="title">{{title}}</view>
    <view class="extra-item" v-for="(item, index) in items" :key="index" v-if="!readonly || item.rate > 0">
      <view class="extra-item-name">{{item.name}}</view>
      <t-star-rating 
        :value="item.rate" 
        :readonly="readonly"
        :size="24"
        @change="(value) => handleRateChange(index, value)"
      />
    </view>
  </view>
</template>

<script>
import TStarRating from './t-star-rating.vue';

export default {
  name: 'TEvaluationItems',
  components: {
    TStarRating
  },
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 评价项目列表
    items: {
      type: Array,
      default: () => []
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 处理评分变化
    handleRateChange(index, value) {
      if (this.readonly) return;
      
      // 拷贝一份数据，避免直接修改props
      const newItems = JSON.parse(JSON.stringify(this.items));
      newItems[index].rate = value;
      
      // 更新父组件的值
      this.$emit('update:items', newItems);
      this.$emit('change', { index, value, items: newItems });
    }
  }
}
</script>

<style lang="scss" scoped>
.extra-items {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.detail-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 24rpx;
}

.extra-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.extra-item:last-child {
  border-bottom: none;
}

.extra-item-name {
  font-size: 28rpx;
  color: #333333;
}
</style> 