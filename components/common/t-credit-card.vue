<template>
	<view class="t-credit-card">
		<!-- <view class="t-c-discount">{{creditCardInfo.info}}</view> -->
		<view class="t-c-top" :style="{ background:''+bankCodeToBgMap.get(''+creditCardInfo.type) }">
			<view class="t-c-t-left">
				<view :class="getClass">
				</view>
			</view>
			<view class="t-c-t-right">
				<view class="u-f-ajs">
					<view>{{bankCodeToBankNameMap.get(creditCardInfo.type+"")}}</view>
					<view style="font-size: 24upx;">{{creditCardInfo.currStatus}}</view>
				</view>
				<view  style="font-size: 24upx;line-height: 40upx;">
					{{cardTypeMap.get(''+creditCardInfo.cardType)}}
				</view>

				<view class="t-c-b-right"  style="font-size: 32upx;">{{creditCardInfo.account}}</view>
	
			</view>
			
			
		</view>
		<view class="t-c-bottom u-f-ajs" :style="{background: ''+bankCodeToBgMap.get(creditCardInfo.type)}">
			
			<view class="t-c-b-left">
				<!--{{creditCardInfo.massage}} -->
			</view>
			
			<view class="guanlian">
				<view :style="{color: ''+bankCodeToBgMap.get(creditCardInfo.type)}" v-show="creditCardInfo.status!=3&&creditCardInfo.status!=3" @tap="changeRelevance">{{'关联'}}</view>
			</view>
		</view>
		
<!-- 		<template v-if="creditCardInfo.haveRelevancedCarList&&creditCardInfo.haveRelevancedCarList.length>0">
			<view class="t-c-add" :style="{ background:bankCodeToBgMap.get(creditCardInfo.bankCode) }">
				<view class="t-c-a-top">已关联车辆</view>
				<view class="t-c-a-bottom">
					<block v-for="(item, index) in creditCardInfo.haveRelevancedCarList" :key="index">
						<view>{{item.carNumber}}</view>
					</block>
				</view>
			</view>
		</template> -->

	</view>
</template>

<script>
	import {
		bankCodeToIconMap,
		bankCodeToBgMap,
		cardTypeMap,
		payCardStatusMap,
		bankCodeToBankNameMap
	} from '@/common/systemConstant.js'
	export default {
		props: {
			creditCardInfo: {
				type: Object,
				required: true,
			},
			// changeRelevance: {
			// 	type:Function,
			// 	// required: true,
			// },
			canOpertion: {
				type:Boolean,
				default: true,
			}
		},
		data() {
			return {
				bankCodeToIconMap,
				bankCodeToBgMap,
				cardTypeMap,
				payCardStatusMap,
				bankCodeToBankNameMap
			};
		},
		computed:{
			getClass(){
				return bankCodeToIconMap.get(''+this.creditCardInfo.type);
			}
		},
		methods:{
			changeRelevance(){
				this.$emit("changeRelevance",this.creditCardInfo);
			},
			unty(){
				this.$emit("unty",this.creditCardInfo)
			}
		}
	}
</script>

<style scope>
	.t-credit-card {
		border: solid 1upx #CCCCCC;
		border-radius: 6upx;
		display: flex;
		flex-direction: column;
		position: relative;
		margin-bottom: 30upx;
		/* justify-content; */

		-webkit-box-shadow: 0 0 10upx #CCCCCC;
		-moz-box-shadow: 0 0 10upx #CCCCCC;
		box-shadow: 0 0 10upx #CCCCCC;
	}

	.t-c-discount {
		position: absolute;
		right: 6upx;
		font-size: 24upx;
		top: 10upx;
		color: #f0f0f0;
	}

	.t-c-top {
		padding: 10upx 14upx;
		border-top-left-radius: 6upx;
		border-top-right-radius: 6upx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.t-c-t-left {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: flex-start;
	}

	.t-c-t-left>view {
		font-size: 90upx;
		line-height: 120upx;
		text-align: center;
		
	}

	.t-c-t-right {
		flex: 3;
		font-size: 28upx;
		line-height: 50upx;
	}

	/* .t-c-t-right>view:first-of-type {
		font-size: 30upx;
	} */

	.t-c-t-r-bottom {
		display: flex;
		flex-direction: row;
		font-size: 20upx;
		
		/* margin-top: 20upx; */
	}

	.t-c-t-r-bottom>view:first-of-type {
		flex: 8;
		font-size: 20upx;
	}

	.guanlian {
		/* border: 1upx solid #fff; */
		border-radius: 20upx;
		padding: 0upx 20upx ;
		font-size: 20upx;
		line-height: 42upx;
		background: #FFFFFF;
		text-align: center;
		/* flex: 3; */
	}

	.t-c-bottom {
		color: #FFFFFF;
		display: flex;
		flex-direction: row;
		padding: 12upx 20upx;
	}

	.t-c-b-left{
		font-size: 24upx;
	}

	.t-c-add {
		display: flex;
		flex-direction: column;
		text-align: center;
		font-size: 29upx;
		padding: 10upx;
	}

	.t-c-a-bottom {
		font-size: 27upx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		margin-top: 10upx;
	}

	.t-c-a-bottom>view {
		text-align: center;
	}
</style>
