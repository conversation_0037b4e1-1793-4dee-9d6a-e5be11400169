<template>
	<view class="cu-load load-modal zindex" v-if="isShow">
		<view class="loading-wrapper"></view>
		<view class="loading"></view>
		<view class="gray-text">请稍候...</view>
	</view>
</template>

<script>
	export default {
		props: {
			isShow: <PERSON>olean,
		},
		data() {
			return {

			};
		}
	}
</script>

<style>
	.loading {
		background-image: url('~@/static/etc/etc.png');
		width: 70rpx;
		height: 70rpx;
		background-repeat: no-repeat;
		background-size: 70rpx;
	}

	.loading-wrapper {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		margin: auto;
		width: 260rpx;
		height: 260rpx;
		background-color: #ffffff;
		z-index: -1;
	}
</style>
