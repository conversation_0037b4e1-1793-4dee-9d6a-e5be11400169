<template>
  <view class="star-container" :class="{'star-readonly': readonly}">
    <image 
      v-for="(item, index) in 5" 
      :key="index" 
      :src="index < value ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/evaStar.png' : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/evaStar_gray.png'"
      mode="aspectFit"
      class="star-image"
      :style="{
        width: size + 'rpx',
        height: size + 'rpx',
        marginRight: marginRight + 'rpx'
      }"
      @click="handleStarClick(index + 1)"
    ></image>
  </view>
</template>

<script>
export default {
  name: 'TStarRating',
  props: {
    // 当前评分值
    value: {
      type: Number,
      default: 0
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 星星大小
    size: {
      type: Number,
      default: 24
    },
    // 星星颜色
    color: {
      type: String,
      default: '#FFB800'
    },
    // 星星间距
    marginRight: {
      type: Number,
      default: 0
    }
  },
  methods: {
    // 处理星星点击
    handleStarClick(value) {
      if (this.readonly) return;
      this.$emit('update:value', value);
      this.$emit('change', value);
    }
  }
}
</script>

<style lang="scss" scoped>
.star-container {
  display: flex;
  justify-content: center;
  gap: 8rpx;
}

.star-image {
  width: 24rpx;
  height: 24rpx;
}

.star-readonly {
  pointer-events: none;
}
</style> 