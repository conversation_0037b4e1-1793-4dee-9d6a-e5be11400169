<template>
	<view class="t-pay-account">
		<view :class="getClass" :style="{color: bankCodeToIconColorMap.get(bankInfo.type+'')}">
		</view>
		<view class="t-p-content">
			<view class='t-p-c-bank'>
				<view class='t-p-c-b-name'>{{bankCodeToBankNameMap.get(bankInfo.type+"")}}</view>
				<!-- <view class='t-p-c-b-msg'>{{payTypeMap.get(bankInfo.type+"")}}</view> -->
			</view>
			<view class="t-p-c-type">{{cardTypeMap.get(bankInfo.cardType+"")}}</view>
			<view class="t-p-c-id">{{bankInfo.account}}</view>
		</view>
		
		<view class="t-p-status" @tap="clickStatus">{{payCardStatusMap.get(bankInfo.status+"")}}<!-- 合约于2019.04.30到期 --></view>
		<view class="t-p-icon icon iconfont icon-jiantouarrow487">
		</view>
	</view>
</template>

<script>
	import { payCardStatusMap,bankCodeToIconMap,bankCodeToIconColorMap,payTypeMap,cardTypeMap,bankCodeToBankNameMap } from '@/common/systemConstant.js'
	export default {
		props: {
			bankInfo: {
				type: Object,
				required: true,
			}
		},
		data() {
			return {
				bankCodeToIconMap,
				bankCodeToIconColorMap,
				payTypeMap,
				cardTypeMap,
				payCardStatusMap,
				bankCodeToBankNameMap
			};
		},
		computed:{
			getClass(){
				return bankCodeToIconMap.get(this.bankInfo.type+'') + " t-p-img";
			}
		},
		methods:{
			clickStatus(){
				this.$emit("clickStatus", this.bankInfo);
			}
		}
	}
</script>

<style>
	.t-pay-account{
		border: 1upx solid #CCCCCC;
		box-shadow: 0px 3px 6px 0px rgba(50,54,63,0.23);
		border-radius: 16upx;
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 20upx 0;
		text-align: center;
		/* justify-content: space-around	; */
	}
	.t-p-img{
		flex: 2;
		font-size: 100upx;
		/* color: green; */
	}
	.t-p-content{
		flex: 5;
		display: flex;
		flex-direction: column;
		text-align: left;
	}
	.t-p-c-bank{
		display: flex;
		flex-direction: row;
		/* justify-content: space-between; */
		align-items: center;
	}
	.t-p-c-b-name{
		font-weight: bold;
		font-size: 32upx;
	}
	.t-p-c-b-msg{
		padding-left: 26upx;
	}
	.t-p-c-type{}
	.t-p-c-id{}
	.t-p-status{
		flex: 2;
		background: #F18934;
		color:#FFFFFF;
		border-radius: 30upx;
	}
	.t-p-icon{
		flex: 1;
	}
</style>
