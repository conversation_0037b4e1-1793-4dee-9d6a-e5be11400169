<template>
  <view class="tag-container">
    <view class="tag-title" v-if="title">{{title}}</view>
    <view class="tag-list">
      <view 
        v-for="(tag, index) in tags" 
        :key="index" 
        class="tag-item" 
        :class="{ 'tag-selected': selectedTags.includes(tag) }"
        @tap="toggleTag(tag)"
      >
        {{tag}}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TEvaluationTags',
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 标签列表
    tags: {
      type: Array,
      default: () => []
    },
    // 已选择的标签
    value: {
      type: Array,
      default: () => []
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedTags: [...this.value]
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedTags = [...newVal];
      },
      deep: true
    }
  },
  methods: {
    // 切换标签选择状态
    toggleTag(tag) {
      if (this.readonly) return;
      
      const index = this.selectedTags.indexOf(tag);
      if (index === -1) {
        // 添加标签
        this.selectedTags.push(tag);
      } else {
        // 移除标签
        this.selectedTags.splice(index, 1);
      }
      
      // 更新父组件的值
      this.$emit('update:value', this.selectedTags);
      this.$emit('change', this.selectedTags);
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  width: 100%;
}

.tag-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 24rpx;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  margin-right: -20rpx;
  margin-bottom: -20rpx;
}

.tag-item {
  padding: 12rpx 24rpx;
  background-color: #FFFFFF;
  border: 1rpx solid #DDDDDD;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #666666;
  transition: all 0.3s;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 56rpx;
}

.tag-item:active {
  transform: scale(0.97);
  background-color: #F8F8F8;
}

.tag-selected {
  background-color: #E1F0FF;
  color: #0066E9;
  font-weight: 500;
  border: 1rpx solid #0066E9;
  box-shadow: 0 2rpx 8rpx rgba(0, 102, 233, 0.1);
}

.tag-selected:active {
  transform: scale(0.97);
  background-color: #D5E8FF;
}
</style> 