<template>
  <view class="travel-banner">
    <swiper
      class="banner-swiper"
      :indicator-dots="false"
      :autoplay="true"
      :interval="5000"
      :duration="500"
      :circular="true"
      @change="handleChange"
    >
      <swiper-item v-for="(item, index) in bannerList" :key="index">
        <view class="banner-item" @click="handleBannerClick(item)">
          <view class="banner-content">
            <text class="banner-text">{{ item.title }}</text>
            <view class="banner-icon" v-if="item.showIcon">
              <text class="icon-text">✨</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  name: 'TravelBanner',
  data() {
    return {
      currentIndex: 0,
      bannerList: [
        {
          id: 1,
          title: '一键开启智慧旅程',
          showIcon: true,
          type: 'travel'
        },
        {
          id: 2,
          title: '智能导航，畅享出行',
          showIcon: true,
          type: 'navigation'
        },
        {
          id: 3,
          title: '贴心服务，安心旅途',
          showIcon: true,
          type: 'service'
        }
      ]
    };
  },
  methods: {
    handleChange(e) {
      this.currentIndex = e.detail.current;
    },
    handleBannerClick(item) {
      console.log('点击banner:', item);
      // 这里可以添加具体的跳转逻辑
      this.$emit('bannerClick', item);
    }
  }
};
</script>

<style lang="scss" scoped>
.travel-banner {
  border-radius: 4rpx;
  overflow: hidden;
  
  .banner-swiper {
    width: 100%;
    height: 86rpx;
    border-radius: 4rpx;
    
    .banner-item {
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);
      border-radius: 4rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      
      .banner-content {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        
        .banner-text {
          color: #ffffff;
          font-size: 32rpx;
          font-weight: 600;
          line-height: 44rpx;
        }
        
        .banner-icon {
          margin-left: 16rpx;
          
          .icon-text {
            color: #ffffff;
            font-size: 28rpx;
          }
        }
      }
    }
  }
}
</style> 