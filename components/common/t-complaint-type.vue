<template>
	<view>
		<view class="t-order-account">
			<view class="t-p-icon">
				<image :src="itemInfo.iconurl" class="png" mode="aspectFit"></image>
			</view>
			<view class="t-p-content">
				<view class='t-p-c-bank'>
					<view class='t-p-c-b-name'>{{itemInfo.title}}
					</view>
				</view>
			</view>
			<view class="t-p-icon icon iconfont icon-jiantouarrow487" v-show="showArrow">
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			itemInfo: {
				type: Object,
				required: true,
			},
			showArrow: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
			    
			};
		},
		computed:{
			
		},
		onShow(){
		},
		methods:{
			
		}
	}
</script>

<style>
	.png{
		width:1.6rem;
		height:1.6rem;
	}
	.t-order-account{
		border-bottom: 1upx solid #EDEDED;
		display: flex;
		flex-direction: row;
		align-items: center;
		height: 150upx;
		text-align: center;
	}
	.t-p-img{
		flex: 2;
		font-size: 100upx;
	}
	.t-p-content{
		flex: 6;
		display: flex;
		flex-direction: column;
		text-align: left;
	}
	.t-p-c-bank{
		display: flex;
		flex-direction: row;
		margin-left: 5upx;
		align-items: center;
	}
	.t-p-c-b-name{
		font-weight: bold;
		font-size: 32upx;
		margin-left: 10upx;
	}
	.t-p-icon{
		flex: 1;
	}
	
</style>
