<template>
	<view class="t-pay-account">
		<view :class="getClass" :style="{color: bankCodeToIconColorMap.get(bankInfo.type+'')}">
		</view>
		<view class="t-p-content">
			<view class='t-p-c-bank'>
				<view class='t-p-c-b-name'>{{bankInfo.name}}</view>
				<!--
				<view class='t-p-c-b-name'>{{bankCodeToBankNameMap.get(bankInfo.type+"")}}</view>
				 <view class='t-p-c-b-msg'>{{payTypeMap.get(bankInfo.type+"")}}</view> -->
			</view>
			<view class="t-p-c-type">{{cardTypeMap.get(bankInfo.cardType+"")}}</view>
			<view class="t-p-c-id" v-if="bankInfo.account!='null'">{{bankInfo.account}}</view>
			<view class="t-p-c-info" v-if="bankInfo.info!='null' && showSign==true && showBind==false">{{bankInfo.info}}</view>
		</view>
		<view class="qianyue" v-show="showSign">
			签约
		</view>
		<view class="qianyue" v-show="showBind">
			绑定
		</view>
		<view class="qianyue" v-show="showStart">
			关联
		</view>
		<view class="qianyue" v-show="showStop">
			停用
		</view>
		<view class="t-p-icon icon iconfont icon-jiantouarrow487" v-show="showArrow">
		</view>
	</view>
</template>

<script>
	import { payCardStatusMap,bankCodeToIconMap,bankCodeToIconColorMap,payTypeMap,cardTypeMap,bankCodeToBankNameMap } from '@/common/systemConstant.js'
	export default {
		props: {
			bankInfo: {
				type: Object,
				required: true,
			},
			showArrow: {
				type: Boolean,
				default: true
			},
			showSign:{
				type: Boolean,
				default: false,
			},
			showBind:{
				type: Boolean,
				default: false,
			},
			showStart:{
				type: Boolean,
				default: false,
			},
			showStop:{
				type: Boolean,
				default: false,
			}
		},
		data() {
			return {
				bankCodeToIconMap,
				bankCodeToIconColorMap,
				payTypeMap,
				cardTypeMap,
				payCardStatusMap,
				bankCodeToBankNameMap
			};
		},
		computed:{
			getClass(){
				return bankCodeToIconMap.get(this.bankInfo.type+'') + " t-p-img";
			}
		},
		methods:{
			clickStatus(){
				this.$emit("clickStatus", this.bankInfo);
			}
		}
	}
</script>

<style>
	.t-pay-account{
		border-bottom: 1upx solid #EDEDED;

		/* background: rbga(256,256,256,0.2); */

		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 20upx 0;
		text-align: center;
		/* justify-content: space-around	; */
	}
	.t-p-img{
		flex: 2;
		font-size: 100upx;
		background-size: 96rpx;
		width: 100rpx;
		height: 108rpx;
	}
	.t-p-content{
		flex: 5;
		display: flex;
		flex-direction: column;
		text-align: left;
	}
	.t-p-c-bank{
		display: flex;
		flex-direction: row;
		/* justify-content: space-between; */
		align-items: center;
	}
	.t-p-c-b-name{
		font-weight: bold;
		font-size: 32upx;
	}
	.t-p-c-b-msg{
		padding-left: 26upx;
	}
	.t-p-c-type{}
	.t-p-c-id{
		font-size: 32upx;
	}
	.t-p-c-info{
		font-size: 24upx;
	}
	.t-p-status{
		flex: 2;
		background: #F18934;
		color:#FFFFFF;
		border-radius: 30upx;
	}
	.t-p-icon{
		flex: 1;
	}
	.qianyue{
		color:var(--topicColor)
	}

</style>
