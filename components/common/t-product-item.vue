<template>
	<view class="t-product-item">
		<view class="t-p-content">
			<view class='t-p-c-bank'>
				<view class='t-p-c-b-name'>{{productInfo.goodsName}}</view>
			</view>
			<view class='t-p-c-bank'>
				<view class="t-p-i-c-content u-f-a" v-if="productInfo.comment==null">
					
				</view>
				<view class="t-p-i-c-content u-f-a" v-else>
					 {{productInfo.comment}}
				</view>
			</view>
		</view>
		<!--
		<view class="t-p-icon icon iconfont icon-jiantouarrow487">
		* </view>
		-->
		<view class="right">
			<view class="radio-item">
				<radio class='blue' :checked="ownIndex === index"></radio>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		props: {
			productInfo: {
				type: Object,
				required: true,
			},
			index:{
				type: Number,
				required: false,
			},
			ownIndex:{
				type: Number,
				required: false,
			},
		},
		data() {
			return {
				
			};
		},
		methods:{
			showMoreInfo(){
				
			}
		}
	}
</script>

<style>
	.t-product-item{
		border-bottom: 1upx solid #EDEDED;

		/* background: rbga(256,256,256,0.2); */
		
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 20upx 0;
		text-align: center;
		/* justify-content: space-around	; */
	}
	.t-p-img{
		flex: 2;
		font-size: 100upx;
		/* color: green; */
	}
	.t-p-content{
		flex: 5;
		display: flex;
		flex-direction: column;
		text-align: left;
	}
	.t-p-c-bank{
		display: flex;
		flex-direction: row;
		margin-left: 5upx;
		/* justify-content: space-between; */
		align-items: center;
	}
	.t-p-c-b-name{
		font-weight: bold;
		font-size: 32upx;
		margin-left: 10upx;
	}
	.t-p-i-c-content{
		margin-left: 10upx;
	}
	.t-p-c-b-msg{
		padding-left: 26upx;
	}
	.t-p-c-type{
	    margin-left: 10upx;
	}
	.t-p-c-id{
		margin-left: 10upx;
	}
	.t-p-status{
		flex: 2;
		background: #F18934;
		color:#FFFFFF;
		border-radius: 30upx;
	}
	.t-p-icon{
		flex: 1;
	}
	.qianyue{
		color:var(--blue)
	}
	
	.right{
		margin-right: 20upx;
	}
	
</style>
