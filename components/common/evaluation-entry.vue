<template>
    <view class="evaluation-entry" :class="{ 'evaluation-entry--disabled': disabled }" @tap="handleTap">
        <view class="evaluation-entry__content">
            <view class="evaluation-entry__left">
                <view class="evaluation-entry__title">{{ displayTitle }}</view>
                <view class="evaluation-entry__count">（{{ formatCount }}）</view>
                <view class="evaluation-entry__message-icon">
                    <image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/comment.png" mode="aspectFit"></image>
                </view>
            </view>
            <view class="evaluation-entry__right">
                <view class="evaluation-entry__arrow-icon">
                    <image src="@/pagesD/static/<EMAIL>" mode="aspectFit"></image>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'EvaluationEntry',
    props: {
        // 业务类型：serviceArea(服务区), tollStation(收费站), chargingService(充电服务)
        businessType: {
            type: String,
            required: true,
            validator(value) {
                return ['serviceArea', 'tollStation', 'chargingService'].includes(value);
            }
        },
        // 业务ID
        businessId: {
            type: [String, Number],
            required: true
        },
        // 评价数量
        evaluationCount: {
            type: Number,
            required: true,
            default: 0
        },
        // 自定义标题
        customTitle: {
            type: String,
            default: ''
        },
        // 是否禁用点击
        disabled: {
            type: Boolean,
            default: false
        },
        // 自定义样式类名
        customClass: {
            type: String,
            default: ''
        },
        // 业务名称
        businessName: {
            type: String,
            default: ''
        }
    },
    computed: {
        // 显示的标题
        displayTitle() {
            return this.customTitle || '用户评价';
        },

        // 格式化后的评价数量
        formatCount() {
            if (this.evaluationCount <= 0) {
                return '0';
            }
            if (this.evaluationCount > 999) {
                return '999+';
            }
            return this.evaluationCount.toString();
        }
    },
    methods: {
        // 处理点击事件
        handleTap() {
            if (this.disabled) {
                return;
            }

            // 触发自定义事件，允许父组件自定义处理逻辑
            this.$emit('tap', {
                businessType: this.businessType,
                businessId: this.businessId,
                evaluationCount: this.evaluationCount,
                businessName: this.businessName
            });

            // 默认跳转逻辑
            this.navigateToEvaluationList();
        },

        // 跳转到评价列表页面
        navigateToEvaluationList() {
            try {
                // 构造跳转URL
                const url = `/pagesD/evaluationPage/commentList?type=${this.businessType}&businessId=${this.businessId}&businessName=${this.businessName}`;

                uni.navigateTo({
                    url: url,
                    fail: (err) => {
                        console.error('跳转评价列表页面失败：', err);
                        uni.showToast({
                            title: '页面跳转失败',
                            icon: 'none'
                        });
                    }
                });
            } catch (error) {
                console.error('构造跳转URL失败：', error);
                uni.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.evaluation-entry {
    background-color: #FFFFFF;
    height: 90rpx;
    box-sizing: border-box;
    transition: all 0.3s ease;
    margin: 20rpx 0;
    padding: 0 20rpx;
    &--disabled {
        opacity: 0.6;
    }

    &:not(.evaluation-entry--disabled):active {
        opacity: 0.8;
    }
}

.evaluation-entry__content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 0;
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
}

.evaluation-entry__left {
    display: flex;
    align-items: center;
    flex: 1;
}

.evaluation-entry__count {
    margin-left: 8rpx;
}

.evaluation-entry__right {
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.evaluation-entry__message-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    image{
        width: 34rpx;
        height: 34rpx;
    }
}

.evaluation-entry__arrow-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    image{
        width: 30rpx;
        height: 30rpx;
    }
}

/* 支持自定义样式覆盖 */
.evaluation-entry.custom-style {
    .evaluation-entry__content {
        border-bottom: none;
        padding: 20rpx 0;
    }

    .evaluation-entry__title,
    .evaluation-entry__count {
        font-size: 26rpx;
        color: #666666;
    }
}

/* 无边框样式变体 */
.evaluation-entry.no-border {
    .evaluation-entry__content {
        border-bottom: none;
    }
}

/* 简洁样式变体 */
.evaluation-entry.simple {
    .evaluation-entry__content {
        padding: 20rpx 0;
    }

    .evaluation-entry__title,
    .evaluation-entry__count {
        font-size: 26rpx;
    }
}
</style>