<template>
	<view class='t-car-license u-f-ajc'>
		<view class="car-card u-f-ajc" :class="getClass" :style="{color:carInfo.plateColor=='0'|| carInfo.plateColor=='2' ? '#fff' : '#000'}">
			{{carInfo.plateNum}}
			<!-- {{carInfo.plateNum.substring(0,2)}}
			<view class="bot-car" :style="{background:carInfo.plateColor=='0'|| carInfo.plateColor=='2' ? '#fff' : '#000'}"></view>
			{{carInfo.plateNum.substring(2)}} -->
		</view>
	</view>
</template>

<script>
	import { plateColorToClassMap,plateColorToFirstMap } from '@/common/systemConstant.js'
	export default {
		props: {
			carInfo: {
				type: Object,
				required: true,
			}
		},
		data() {
			return {
				plateColorToClassMap,plateColorToFirstMap
			};
		},
		computed:{
			getClass(){
				return plateColorToFirstMap.get(this.carInfo.plateColor+'');
			}
		},
		onLoad() {
			console.log("son--carinfo",this.carInfo);
		}
	}
</script>

<style scoped>
	.t-car-license{
		
	}
	.car-card{
		width: 364upx;
		height: 104upx;
		font-size: 48upx;
		line-height: 104upx;
		text-align: center;
		/* background: url(/static/car-license-bg/0.png) no-repeat 100% 100%; */
	}
	.bot-car{
		width: 12upx;
		height: 12upx;
		/* background: #fff; */
		border-radius: 100%;
		margin: 0 10upx;
		
	}

</style>
