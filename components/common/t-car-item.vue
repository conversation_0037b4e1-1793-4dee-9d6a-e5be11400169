<template>
	<view>
		<view class="t-order-account">
			<view class="t-p-icon">
				<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/tab-bar/personal/wdcl.png" class="png" mode="aspectFit"></image>
			</view>
			<view class="t-p-content">
				<view class='t-p-c-bank'>
					<view class='t-p-c-b-name'>{{carInfo.plateNum}}
						<text style="color:#2484E8">[{{plateColorToColorMap.get(carInfo.plateColor+'')}}]</text>
					</view>
				</view>
				<view class="t-p-c-type"  v-show="carInfo.status&&carInfo.status!='' || carInfo.registeredType === '2'">办理状态：{{getVehicleStatus.get(carInfo.registeredType === "2" ? "1" : carInfo.status+'')}}</view>
				<view class="t-p-c-id" v-show="carInfo.type&&carInfo.type!=''">签约银行：{{bankCodeToBankNameMap.get(carInfo.type)}}</view>
			</view>
			<view class="t-p-icon icon iconfont icon-jiantouarrow487" v-show="showArrow">
			</view>
		</view>
	</view>
</template>

<script>
	import { plateColorToColorMap,getVehicleStatus,bankCodeToBankNameMap} from '@/common/systemConstant.js';
	export default {
		props: {
			carInfo: {
				type: Object,
				required: true,
			},
			showArrow: {
				type: Boolean,
				default: true
			},
			showSign:{
				type: Boolean,
				default: false,
			}
		},
		data() {
			return {
			    plateColorToColorMap,
				getVehicleStatus,
				bankCodeToBankNameMap
			};
		},
		computed:{
			
		},
		onShow(){
		},
		methods:{
			
		},
		watch:{
		}
	}
</script>

<style>
	.png{
		width:1.6rem;
		height:1.6rem;
	}
	.t-order-account{
		border-bottom: 1upx solid #EDEDED;

		/* background: rbga(256,256,256,0.2); */
		
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 20upx 0;
		text-align: center;
		/* justify-content: space-around	; */
	}
	.t-p-img{
		flex: 2;
		font-size: 100upx;
		/* color: green; */
	}
	.t-p-content{
		flex: 5;
		display: flex;
		flex-direction: column;
		text-align: left;
	}
	.t-p-c-bank{
		display: flex;
		flex-direction: row;
		margin-left: 5upx;
		/* justify-content: space-between; */
		align-items: center;
	}
	.t-p-c-b-name{
		font-weight: bold;
		font-size: 32upx;
		margin-left: 10upx;
	}
	.t-p-c-b-msg{
		padding-left: 26upx;
	}
	.t-p-c-type{
	    margin-left: 10upx;
	}
	.t-p-c-id{
		margin-left: 10upx;
	}
	.t-p-status{
		flex: 2;
		background: #F18934;
		color:#FFFFFF;
		border-radius: 30upx;
	}
	.t-p-icon{
		flex: 1;
	}
	.qianyue{
		color:var(--blue)
	}
	
</style>
