<template>
	<view class="t-complaint">
		
		<view class="t-order-account">
			<view class="t-p-content">
				<view class="t-p-c-type">工单号：{{complaitInfo.complainNum}}</view>
				<view class="t-p-c-type">车牌号：{{complaitInfo.plateNum}}</view>
				<view class="t-p-c-type">投诉类型：{{complaitInfo.complainType}}</view>
				<view class="t-p-c-type">投诉时间：{{acceptTime}}</view>
				<view class="t-p-c-type">处理状态：<text class="status">{{complaitInfo.complainStatus}}</text></view>
			</view>
			<image src="/static/tab-bar/index/right-ar.png" class="right-arrow" v-show="showArrow"></image>
			<view class="line" v-if="!hideLine"></view>
		</view>
	</view>
</template>

<script>
	import { plateColorToColorMap} from '@/common/systemConstant.js';
	export default {
		props: {
			complaitInfo: {
				type: Object,
				required: true,
			},
			showArrow: {
				type: Boolean,
				default: true
			},
			showSign:{
				type: Boolean,
				default: false,
			},
			hideLine:{
				type:Boolean,
				default: false
			}
		},
		data() {
			return {
			    plateColorToColorMap
			};
		},
		computed:{
			acceptTime: function () {
			  let tempTime = this.complaitInfo.acceptTime;
			  var timeFormat;
			  if(tempTime != ""){
				  timeFormat = tempTime.substring(0,4)+'-'+tempTime.substring(4,6)+'-'+tempTime.substring(6,8)+
				  ' '+tempTime.substring(8,10)+':'+tempTime.substring(10,12)+':'+tempTime.substring(12,14);
			  }else{
				  timeFormat = tempTime;
			  }
			  return timeFormat;
			}
		},
		onShow(){
		},
		methods:{
			
		}
	}
</script>

<style lang="scss" scoped>
	.t-complaint{
		display: flex;
		flex-direction: row;
		align-items: center;
		text-align: center;
		.t-order-account{
			width:680rpx;
			padding: 34rpx;
			color:#333;
			font-size: 28rpx;
			position: relative;
			.line{
				border: 1px solid #E5E5E5;
				width: 610rpx;
				position: absolute;
				bottom: 0;
			}
			.right-arrow{
				width: 25rpx;
				height: 25rpx;
				position: absolute;
				position: absolute;
				right: 35rpx;
				top: 50%;
			}
			.t-p-content{
				flex: 5;
				display: flex;
				flex-direction: column;
				text-align: left;
				.status{
					color:#1D82D2;
				}
				.t-p-c-type{
				    margin-left: 10upx;
				}
			}
		}
	}
</style>
