<template>
	<view class="tabbar">

		<u-tabbar :list="tabberList" height='110rpx' :mid-button="false" :value="currentIndex" icon-size="48rpx"
			active-color="#0066E9" inactive-color="#B7B7B7" @change="changeTabber" :before-switch="beforeSwitch">
		</u-tabbar>
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:confirm-text="modal.confirmText" @confirm="onConfirmHandle">
			<view class="maintaining-content-wrapper">
				<view class="title">提醒</view>
				<view class="msg_desc">
					<view class="msg_desc-honorific">
						尊敬的ETC用户：
					</view>
					<view class="msg_desc-content">
						因捷通E购正在全面优化升级，2023年1月份用户的商城权益优惠券暂停发放，从2023年2月中下旬开始顺延补发，如有不便，敬请谅解，谢谢！
					</view>
				</view>
			</view>
		</neil-modal>
	</view>
</template>

<script>
	import {
		loginValidHandle,
		etcAccountValidHandle,
		spreadHandleFn,
		onlineStoreFn,
		_toOnlineStore
	} from '@/components/base/redirect/index.js';
	import {
		getTicket
	} from '@/common/storageUtil.js'
	import neilModal from '@/components/neil-modal/neil-modal.vue'
	export default {
		props: {
			current: {
				type: String,
				default: 'etcService'
			}
		},
		comments: {
			neilModal
		},
		watch: {
			current(val) {
				this.init();
			}
		},
		data() {
			return {
				currentIndex: 0,
				tabberList: [{
						iconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/home_icon.png",
						selectedIconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/home_icon_highlight.png",
						text: '首页',
						type: 'etcHome'
					},
					// 线上发行上线先屏蔽
					{
						iconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/tabbar/tab_bar_spread_unselect.png",
						selectedIconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/tabbar/tab_bar_spread_select.png",
						text: '路况服务',
						// text: '车生活',
						type: 'spread'
					},
					{
						iconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/tabbar/tab_bar_etcService_unselect.png",
						selectedIconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/tabbar/tab_bar_etcService_select.png",
						text: 'ETC服务',
						type: 'etcService'
					},
					{
						iconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/tabbar/tab_bar_onlineStore_unselect.png",
						selectedIconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/tabbar/tab_bar_onlineStore_select.png",
						text: '捷通E购',
						type: 'onlineStore'
					},
					{
						iconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/tabbar/tab_bar_account_unselect.png",
						selectedIconPath: "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/tabbar/tab_bar_account_select.png",
						text: '我的',
						type: 'account',
					},
				],
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					confirmText: '确认'
				},
			};
		},
		onShow() {
			uni.hideTabBar({
				animation: false
			})
		},
		created() {
			this.init();
		},
		methods: {
			init() {
				for (let i = 0; i < this.tabberList.length; i++) {
					if (this.current == this.tabberList[i].type) {
						this.currentIndex = i;
					}
				}
			},
			beforeSwitch(index) {
				let type = this.tabberList[index] && this.tabberList[index].type;
				// 拓展平台和在线生活业务需要校验是否登录，是否绑定ETC用户
				if (!type) return false
				if (!(type == 'onlineStore' || type == 'spread')) return true;
				// if (loginValidHandle()) return false
				// if (etcAccountValidHandle()) return false
				return true;
			},
			changeTabber(index) {
				let _self = this;

				this.$nextTick(() => {
					this.redirectHandle(index);
				})
			},
			redirectHandle(index) {
				let type = this.tabberList[index] && this.tabberList[index].type;
				if (!type) return;
				if (type == 'etcHome') {
					this.currentIndex = index;
					uni.redirectTo({
						url: '/pages/home/<USER>/p-home'
					});
				}
				if (type == 'onlineStore') {
					//微信审核优化
					if (!getTicket()) {
						//未登录直接跳转在线商场
						uni.navigateToMiniProgram({
							appId: 'wx1b6a17b21753c694',
							path: '',
							envVersion: 'release', //正式版
							extraData: '',
							success(res) {
						
							}
						});
					} else {
						onlineStoreFn();
					}
					// onlineStoreFn();
				}
				if (type == 'spread') {
					// spreadHandleFn();
					this.currentIndex = index;
					uni.redirectTo({
						url: '/pagesD/travelService/jam-info'
					});
				}
				if (type == 'etcService') {
					this.currentIndex = index;
					uni.redirectTo({
						url: '/pagesD/home/<USER>/p-home'
					});
				}
				if (type == 'account') {
					this.currentIndex = index;
					uni.redirectTo({
						url: '/pages/personal/account'
					});
				}
			},
			onConfirmHandle() {
				this.modal.show = false;
			}
		}
	};
</script>

<style lang="scss">
	/deep/.u-tabbar__content__item__button {
		top: 10rpx;
	}

	.maintaining-content-wrapper {
		.title {
			text-align: center;
			font-weight: 700;
			font-size: 34rpx;
			padding: 25rpx 50rpx;
			color: rgba(0, 0, 0, 0.9);
		}

		.msg_desc {
			padding: 0 30rpx;
			color: rgba(0, 0, 0, 0.5);
			font-size: 30rpx;
			text-align: left;

			.msg_desc-content {
				text-indent: 2em;
			}
		}
	}
</style>
