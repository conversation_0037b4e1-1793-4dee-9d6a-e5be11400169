import vue from 'vue';
import {
	getTicket,
	getOpenid,
	getEtcAccountInfo
} from '@/common/storageUtil.js'
// 登录信息校验
let loginValidHandle = function() {
	if (!getTicket()) {
		uni.showModal({
			title: '提示',
			content: '请先登录',
			success: (res) => {
				if (res.confirm) {
					uni.reLaunch({
						url: '/pagesD/login/p-login'
					})
				}
			}
		})
		return true
	}
	return false
}
// 校验ETC用户选择
let etcAccountValidHandle = function() {
	if (!(getEtcAccountInfo() && Object.keys(getEtcAccountInfo()).length)) {
		uni.showModal({
			title: '提示',
			content: '请先绑定ETC用户',
			success: function(res) {
				if (res.confirm) {
					uni.navigateTo({
						url: '/pagesB/accountBusiness/accountList/accountList'
					})
				}
			}
		})
		return true
	}
	return false
}
// 跳转拓展平台
let spreadHandleFn = function() {
	if (loginValidHandle()) return;
	if (etcAccountValidHandle()) return;
	let etcAccountInfo = getEtcAccountInfo() && Object.keys(getEtcAccountInfo()).length ? getEtcAccountInfo() : {}
	let data = {
		ticket: getTicket() || '',
		custMastId: etcAccountInfo.custMastId || '',
		openId: getOpenid(),
		type: 'home'
	}

	vue.prototype.$request
		.post(vue.prototype.$interfaces.expandUrl, {
			data: data
		})
		.then((res) => {
			if (res.code == 200) {
				const callcenter = res.data
				uni.navigateTo({
					url: '/pages/uni-webview/uni-webview?ownPath=' +
						encodeURIComponent(callcenter)
				})
			} else {
				uni.showModal({
					title: '提示',
					content: '跳转车生活异常：' + res.msg,
					showCancel: false
				})
			}

		})
		.catch((error) => {
			uni.showModal({
				title: '提示',
				content: '跳转车生活异常：' + error.msg,
				showCancel: false
			})
		})
}
// 请求在线商城跳转参数
let onlineStoreFn = (action) => {
	if (loginValidHandle()) return;
	if (etcAccountValidHandle()) return;
	let etcAccountInfo = getEtcAccountInfo() && Object.keys(getEtcAccountInfo()).length ? getEtcAccountInfo() : {}
	let params = {
		customer_id: etcAccountInfo.custMastId || ''
	}
	if (action == 'jumpUrl') {
		_getJumpUrl()
		return;
	}
	vue.prototype.$request
		.post(vue.prototype.$interfaces.getCouponList, {
			data: params
		})
		.then(res => {
			if (res.code == 200) {
				_getJumpUrl()
			} else {
				uni.showModal({
					title: '提示',
					content: '跳转捷通E购失败：' + res.msg,
					showCancel: false
				})
			}

		})
		.catch(err => {
			uni.showModal({
				title: '提示',
				content: '获取优惠券失败' + error.msg,
				showCancel: false
			})
		});
}
let _getJumpUrl = () => {
	let etcAccountInfo = getEtcAccountInfo() && Object.keys(getEtcAccountInfo()).length ? getEtcAccountInfo() : {}
	let params = {
		customer_id: etcAccountInfo.custMastId || ''
	}

	vue.prototype.$request
		.post(vue.prototype.$interfaces.getJumpUrl, {
			data: params
		})
		.then(res => {
			if (res.code == 200) {
				_toOnlineStore(res.data)
			} else {
				uni.showModal({
					title: '提示',
					content: '跳转捷通E购失败：' + res.msg,
					showCancel: false
				})
			}
		})
		.catch(err => {
			uni.showModal({
				title: '提示',
				content: '跳转捷通E购异常：' + error.msg,
				showCancel: false
			})
		});
}
// 跳转在线商城小程序
let _toOnlineStore = (params) => {
	uni.navigateToMiniProgram({
		appId: 'wx1b6a17b21753c694',
		path: '/pages/index/auth',
		envVersion: 'trial', //正式版
		extraData: params,
		success(res) {

		}
	});
}
export {
	loginValidHandle,
	etcAccountValidHandle,
	spreadHandleFn,
	onlineStoreFn
}
