<template>
	<view>
		<image :src="src" :style="{width,height}" :lazy-load="lazyLoad" :mode="mode" :class="shape === 'circle'? 'circle' : 'square'" @click="clickImage"></image>
		<!-- <view :class="shape === 'circle'? 'circle image' : 'square image'" :style="{width,height,backgroundImage:'url(' + src + ')'}" ></view> -->
	</view>
</template>

<script>
	export default {
		name:"autoImage",
		props:{
			shape:{
				type: String,
				default: "square"
			},
			mode:{
				type: String,
				default: "aspectFilt"
			},
			src:{
				type: String,
				default: ""
			},
			width:{
				type: String,
				default: "100%"
			},
			height:{
				type: String,
				default: "100%"
			},
			lazyLoad:{
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				
			};
		},
		methods:{
			clickImage(){
				this.$emit("click")
			}
		}
		
	}
</script>

<style lang="scss" scoped>
	.circle{
		border-radius: 50%;
	}
	.image{
		background-repeat: no-repeat;
		background-size: contain;
	}
</style>
