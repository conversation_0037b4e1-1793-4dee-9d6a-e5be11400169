<template>
	<view class="cu-modal" :class="showModal?'show':''">
		<view class="cu-dialog" :style="modalStyle">
			<view class="u-f-ajc cu-dialog_hd" v-if="showTitle">
				<view class="modal-title" :style='modalTitleStyle'>{{modalTitle}}</view>
			</view>
			<view>
				<slot name="content"></slot>
			</view>
			<view :style="footerStyle" class="cu-dialog_ft bg-white solid-top g-flex g-flex-horizontal-vertical"
				v-if="showCancelFlag||showOkFlag">
				<view v-if="showCancelFlag"
					class="g-flex g-flex-horizontal-vertical action margin-0 flex-sub solid-right cancel"
					@tap="cancelModal">{{cancelText}}</view>
				<view v-if="showOkFlag"
					class=" g-flex g-flex-horizontal-vertical action margin-0 flex-sub solid-left text-cyan"
					:style="comfirmBtnStyle"
					@tap="okModal">{{okText}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			modalTitle: {
				type: String,
				default: "提示",
			},
			showModal: {
				type: Boolean,
				default: false,
			},
			okText: {
				type: String,
				default: "确认"
			},
			cancelText: {
				type: String,
				default: "取消"
			},
			showCancelFlag: {
				type: Boolean,
				default: true,
			},
			showOkFlag: {
				type: Boolean,
				default: true,
			},
			showTitle: {
				type: Boolean,
				default: true,
			},
			footerStyle: {
				type: String,
				default: 'background: #fff'
			},
			//弹框样式
			modalStyle: {
				type: String,
				default: ''
			},
			//确认按钮样式
            comfirmBtnStyle: {
                type: String,
				default: ''
			},
			//标题样式
			modalTitleStyle:{
				type: String,
				default: ''
			},

		},
		methods: {
			hideModal(e) {
				this.$emit("hideModal");
			},
			cancelModal(e) {
				this.$emit("cancelModal");
			},
			okModal(e) {
				this.$emit("okModal");
			}

		}
	}
</script>

<style lang="scss" scoped>
	.cu-modal .cu-dialog {
		background-color: #FFFFFF;
	}

	.cu-modal .cu-dialog .cu-dialog_hd {
		font-size: 32rpx;
		font-weight: 500;
		padding: 40rpx 0 8rpx 0;
	}

	.cu-modal .cu-dialog .cu-dialog_ft {
		height: 88rpx;

		.action {
			height: 100%;
			line-height: 100%;
			font-size: 30rpx;
			font-weight: 400;
		}
	}
</style>
