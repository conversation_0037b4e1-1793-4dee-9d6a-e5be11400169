<template>
	<view class="u-f-ajc t-padding">
		<button class="cu-btn shadow lg"
		:class="status==='1'?'color-two':'bg-topic'"
		style="width: 100%;" d="disabled"
		 @tap="clickNext" >
			<text :class="isLoadding?'cuIcon-loading2 iconfont-spin':''"></text>{{title}}
		</button>
	</view>
</template>

<script>
	export default {
		props:{
			title:{
				type: String,
				required: true,
			},
			// clickButton: {
			// 	type: Function,
			// },
			status: {
				type: String,
				default: "0", // 0 正常，1不正常
			},
			isLoadding:{
				type: Boolean,
				default: false,
			},
			disabled:{
				type:Boolean,
				default:false
			}
		},
		data() {
			return{}
		},

		methods:{
			clickNext(){
				if(this.isLoadding){
					return;
				}
				this.$emit("clickButton");
			}
		}
	}
</script>

<style>
	.color-two{
		background:#FFFFFF;
		/* background: #01A863; */
		color: #0066E9;
		border: 1px solid #0066E9;
	}
	.bg-topic{
		background-color: #0066E9 !important;
	}
</style>
