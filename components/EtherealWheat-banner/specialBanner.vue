<template>
  <view class="banner-container" :class="['mode-' + mode]" :style="{height: bannerHeight}">
    <swiper class="swiper" :indicator-dots="swiperConfig.indicatorDots"
      :indicator-color="swiperConfig.indicatorColor" :indicator-active-color="swiperConfig.indicatorActiveColor"
      :autoplay="swiperConfig.autoplay" :interval="swiperConfig.interval" :duration="swiperConfig.duration"
      :circular="swiperConfig.circular" :previous-margin="computedPreviousMargin" :next-margin="computedNextMargin"
      @change="swiperChange" @animationfinish="animationfinish">
      <swiper-item v-for="(item, i) in bannerList" :key="i">
        <view class="image-container"
          :class="[curIndex === 0 ? ((i === listLen - 1) ? 'item-left' : (i === 1 ? 'item-right' : 'item-center')) : (curIndex === listLen - 1 ? (i === 0 ? 'item-right' : (i === listLen - 2 ? 'item-left' : 'item-center')) : (i === curIndex - 1 ? 'item-left' : (i === curIndex + 1 ? 'item-right' : 'item-center')))]">
          <image :src="item.picture" class="slide-image"  :style="{
            width: computedImageWidth,
            height: computedImageHeight,
            transitionDuration: '.4s',
            transitionTimingFunction: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            borderRadius: computedBorderRadius,
            boxShadow: computedBoxShadow,
            transform: curIndex === i ? 'scale(' + computedScaleX + ',' + computedScaleY + ')' : 'scale(1,1)',
            opacity: curIndex === i ? '1' : computedSideOpacity
          }" @click="getBannerDetail(i)" />
        </view>
      </swiper-item>
    </swiper>
    <view v-if="!hideDescription" class="desc-wrap" :class="[isDescAnimating ? 'hideAndShowDesc' : '']">
      <view class="title">{{ bannerList[descIndex].title }}</view>
      <view class="desc">{{ bannerList[descIndex].description }}</view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'SpecialBanner',
  props: {
    bannerList: {
      type: Array,
      default() {
        return []
      }
    },
    swiperConfig: {
      type: Object,
      default() {
        return {
          indicatorDots: true,
          indicatorColor: 'rgba(255, 255, 255, .4)',
          indicatorActiveColor: 'rgba(255, 255, 255, 1)',
          autoplay: false,
          interval: 3000,
          duration: 300,
          circular: true
        }
      }
    },
    mode: {
      type: String,
      default: 'classic', // classic（经典大图模式）, card（卡片模式）, compact（紧凑模式）
      validator: function (value) {
        return ['classic', 'card', 'compact'].indexOf(value) !== -1
      }
    },
    containerHeight: {
      type: String,
      default: ''
    },
    imageWidth: {
      type: String,
      default: ''
    },
    imageHeight: {
      type: String,
      default: ''
    },
    hideDescription: {
      type: Boolean,
      default: false
    },
    scaleX: {
      type: String,
      default: ''
    },
    scaleY: {
      type: String,
      default: ''
    }
  },
  computed: {
    listLen() {
      return this.bannerList.length
    },
    // 容器样式配置
    bannerHeight() {
      let height = '524rpx'
      if (this.containerHeight) {
        height = this.containerHeight
        console.log('containerHeight', height)
      } else {
        switch (this.mode) {
          case 'card':
            height = '260rpx'
            break
          case 'compact':
            height = '220rpx'
            break
          default: // classic
            height = '524rpx'
        }
      }
      return height
    },
    // 图片宽度配置
    computedImageWidth() {
      if (this.imageWidth) return this.imageWidth
      switch (this.mode) {
        case 'card':
          return '260rpx'
        case 'compact':
          return '220rpx'
        default: // classic
          return '550rpx'
      }
    },
    // 图片高度配置
    computedImageHeight() {
      if (this.imageHeight) return this.imageHeight
      switch (this.mode) {
        case 'card':
          return '180rpx'
        case 'compact':
          return '150rpx'
        default: // classic
          return '328rpx'
      }
    },
    // 前边距配置
    computedPreviousMargin() {
      if (this.swiperConfig.previousMargin) return this.swiperConfig.previousMargin
      switch (this.mode) {
        case 'card':
          return '120rpx'
        case 'compact':
          return '100rpx'
        default: // classic
          return '150rpx'
      }
    },
    // 后边距配置
    computedNextMargin() {
      if (this.swiperConfig.nextMargin) return this.swiperConfig.nextMargin
      switch (this.mode) {
        case 'card':
          return '120rpx'
        case 'compact':
          return '100rpx'
        default: // classic
          return '150rpx'
      }
    },
    // 圆角配置
    computedBorderRadius() {
      switch (this.mode) {
        case 'card':
          return '16rpx'
        case 'compact':
          return '12rpx'
        default: // classic
          return '8rpx'
      }
    },
    // 阴影配置
    computedBoxShadow() {
      switch (this.mode) {
        case 'card':
          return '0 8rpx 32rpx rgba(0,0,0,0.12), 0 4rpx 16rpx rgba(0,0,0,0.08)'
        case 'compact':
          return '0 4rpx 16rpx rgba(0,0,0,0.1)'
        default: // classic
          return '0 12rpx 48rpx rgba(0,0,0,0.15), 0 6rpx 24rpx rgba(0,0,0,0.1)'
      }
    },
    // X轴缩放配置
    computedScaleX() {
      if (this.scaleX) return this.scaleX
      switch (this.mode) {
        case 'card':
          return '1.08'
        case 'compact':
          return '1.05'
        default: // classic
          return (634 / 550).toFixed(4)
      }
    },
    // Y轴缩放配置
    computedScaleY() {
      if (this.scaleY) return this.scaleY
      switch (this.mode) {
        case 'card':
          return '1.08'
        case 'compact':
          return '1.05'
        default: // classic
          return (378 / 328).toFixed(4)
      }
    },
    // 侧边图片透明度
    computedSideOpacity() {
      switch (this.mode) {
        case 'card':
          return '0.75'
        case 'compact':
          return '0.8'
        default: // classic
          return '0.7'
      }
    }
  },
  data() {
    return {
      curIndex: 0,
      descIndex: 0,
      isDescAnimating: false
    }
  },
  methods: {
    swiperChange(e) {
      const that = this
      this.curIndex = e.mp.detail.current
      this.isDescAnimating = true
      let timer = setTimeout(function () {
        that.descIndex = e.mp.detail.current
        clearTimeout(timer)
      }, 200)
    },
    animationfinish(e) {
      this.isDescAnimating = false
    },
    getBannerDetail(index) {
      // 自定义点击事件，可以通过emit传递给父组件
      this.$emit('bannerClick', {
        index: index,
        item: this.bannerList[index]
      })

      uni.showLoading({
        title: '跳转中...',
        duration: 1500,
        mask: true
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.banner-container {
  width: 100%;
  position: relative;
  .swiper {
    height: 100%;
  }
  .image-container {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .slide-image {
      z-index: 200;
      object-fit: cover;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
  }

  .item-left {
    justify-content: flex-end;
  }

  .item-right {
    justify-content: flex-start;
  }

  .item-center {
    justify-content: center;
  }

  .desc-wrap {
    box-sizing: border-box;
    width: 100%;
    height: 98rpx;
    padding: 24rpx 66rpx 0;

    .title {
      width: 100%;
      height: 42rpx;
      line-height: 42rpx;
      color: #222222;
      font-size: 30rpx;
      font-family: 'PingFangTC-Regular';
      font-weight: 600;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .desc {
      margin-top: 4rpx;
      width: 100%;
      height: 34rpx;
      line-height: 34rpx;
      color: #999999;
      font-size: 24rpx;
      font-family: 'PingFangTC-Regular';
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 经典模式样式
.mode-classic {
  .item-left {
    padding: 30rpx 16rpx 0 0;
  }

  .item-right {
    padding: 30rpx 0 0 16rpx;
  }

  .item-center {
    // padding: 56rpx 0 0 0;
  }
}

// 卡片模式样式
.mode-card {
  .item-left {
    padding: 20rpx 15rpx 0 0;
  }

  .item-right {
    padding: 20rpx 0 0 15rpx;
  }

  .item-center {
    padding: 20rpx 0 0 0;
  }
}

// 紧凑模式样式  
.mode-compact {
  .item-left {
    padding: 15rpx 12rpx 0 0;
  }

  .item-right {
    padding: 15rpx 0 0 12rpx;
  }

  .item-center {
    padding: 15rpx 0 0 0;
  }
}

// 描述文字动画
@keyframes descAnimation {
  0% {
    opacity: 1;
  }

  25% {
    opacity: .5;
  }

  50% {
    opacity: 0;
  }

  75% {
    opacity: .5;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes descAnimation {
  0% {
    opacity: 1;
  }

  25% {
    opacity: .5;
  }

  50% {
    opacity: 0;
  }

  75% {
    opacity: .5;
  }

  100% {
    opacity: 1;
  }
}

.hideAndShowDesc {
  animation: descAnimation .4s ease 1;
  -webkit-animation: descAnimation .4s ease 1;
}
</style>
