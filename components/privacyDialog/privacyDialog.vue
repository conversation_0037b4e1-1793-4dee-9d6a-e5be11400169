<template>
	<view v-if="showPop">
		<view class="handleTips">
			<view class="title">{{popName}}</view>
			<view class="content">
				<view class="text">在你使用【{{popName}}】服务之前，请仔细阅读<text @click="openPrivacy"
						style="color: #5591ff">{{privacyContractName}}</text>。如你同意{{privacyContractName}}，请点击”同意“开始使用【{{popName}}】
				</view>
			</view>
			<view class="bottom">
				<button class="btn disagree" @click="disAgree">拒绝</button>
				<button class="btn active" id="agree-btn" open-type="agreePrivacyAuthorization"
					@agreeprivacyauthorization="agree">同意</button>
			</view>
		</view>
		<view class="mask"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				privacyContractName: "",
				showPop: false,
				privacyResolves: new Set()
			}
		},
		props: {
			popName: {
				type: String,
				default: "桂小通(广西捷通)"
			}
		},
		created() {
			console.log('隐私协议监听启动')
			if (wx.getPrivacySetting) {
				wx.onNeedPrivacyAuthorization((resolve) => {
					console.log("监听权限", resolve)
					this.showPop = true
					if (typeof this.privacyHandler === 'function') {
						this.privacyHandler(resolve)
					}
				})	
				// 查询是否授权隐私
				wx.getPrivacySetting({
					success: (res) => {
						console.log('查询是否授权隐私', res)
						if (res.privacyContractName) {
							this.privacyContractName = res.privacyContractName
						}
					},
					fail: () => {},
				})
			}
		},
		methods: {
			// 回调方法set
			privacyHandler(resolve) {
				this.privacyResolves.add(resolve)
			},
			// 同意
			agree() {
				this.privacyResolves.forEach(resolve => {
					resolve({
						event: 'agree',
						buttonId: 'agree-btn'
					})
				})
				this.privacyResolves.clear()
				this.showPop = false
				wx.getPrivacySetting({
					success: (res) => {
						console.log('查询是否授权隐私', res)
					},
				})
				// this.$emit("authResult", true)
			},
			// 拒绝
			disAgree() {
				this.privacyResolves.forEach(resolve => {
					resolve({
						event: 'disagree'
					})
				})
				this.privacyResolves.clear()
				console.log(this.privacyResolves)
				this.showPop = false
				// this.$emit("authResult", false)
			},
			// 打开隐私弹窗
			openPrivacy() {
				wx.openPrivacyContract({
					success: () => {
						console.log('success')
					}, // 打开成功
					fail: () => {
						console.log('fail')
					}, // 打开失败
					complete: () => {}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.handleTips {
		width: 620rpx;
		// height: 560rpx;
		height: auto;
		background-color: #fff;
		border-radius: 24rpx;
		position: fixed;
		z-index: 999999;
		top: 30%;
		margin: 0 67rpx;

		.title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333333;
			margin-top: 50rpx;
			text-align: center;
			height: 32rpx;
		}

		.content {
			margin-top: 50rpx;
			padding: 0 44rpx;

			.text {
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 54rpx;
				text-indent: 2em;
			}
		}

		.bottom {
			display: flex;
			justify-content: space-around;
			margin-top: 66rpx;

			.btn {
				width: 50%;
				height: 88rpx;
				line-height: 88rpx;
				text-align: center;
				font-size: 28rpx;
				color: #333333;
				background-color: #fff;
			}

			.disagree {
				border-top: 1rpx solid #E7E7E7;
				border-radius: 0px 0px 0px 24rpx;
			}

			.active {
				background: #0066E9;
				color: #fff;
				border: none;
				border-radius: 0px 0px 24rpx 0px;
			}

			.btn::after {
				border: none;
			}
		}
	}

	.mask {
		position: fixed;
		width: 100%;
		height: 100%;
		z-index: 99999;
		top: 0;
		background-color: #000;
		opacity: .6;
	}
</style>