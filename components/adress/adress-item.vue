<template>
	<view>
		<view class="content">
			<view class="list">
				<view class="row">
					<view class="center">
						<view class="name-tel">
							<view class="name">{{data.name}}</view>
							<view class="tel">{{data.phone}}</view>
	
						</view>
						<view class="address">
							{{data.address}}
						</view>
					</view>
					<view class="right">
						<view class="radio-item">
							<radio class='blue' :checked="ownIndex== idx" value=""  @tap="selAdress(idx)"></radio>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>	
</template>

<script>
	export default {
		props:{
			data:{
				type: Object,
				required: true,
			},
			curAdress:{
				type: String,
				default:'-1'
			},
			idx:{
				type: Number,
				default:0
			},
			changeAdress:{
				type: Function,
			}
		},
		data() {
			return {
				ownIndex: 0,
			};
		},
		methods:{
			selAdress(idx){
				console.log(this.ownIndex);
				this.ownIndex=idx;
				this.$emit("changeAdress",idx)
			}
		}
	}
</script>

<style lang="scss" scoped>
	view{
		display: flex;
	}
	.list{
		flex-wrap: wrap;
		.row{
			width: 100%;
			padding: 20upx 2%;
			.left{
				width: 90upx;
				flex-shrink: 0;
				align-items: center;
				.head{
					width: 70upx;
					height: 70upx;
					background:linear-gradient(to right,#ccc,#aaa);
					color: #fff;
					justify-content: center;
					align-items: center;
					border-radius: 60upx;
					font-size: 35upx;
				}
			}
			.center{
				width: 100%;
				flex-wrap: wrap;
				.name-tel{
					width: 100%;
					align-items: baseline;
					height:60upx;
					line-height:60upx;
					.name{
						width:60%;
						font-size: 34upx;
					}
					.tel{
						margin-left: 0upx;
						font-size: 24upx;
						color: #777;
					}
					.default{

						font-size: 22upx;
						
						background-color: #f06c7a;
						color: #fff;
						padding: 0 18upx;
						border-radius: 24upx;
						margin-left: 20upx;
					}
				}
				.address{
					width: 100%;
					font-size: 24upx;
					align-items: baseline;
					color: #777;
				}
			}
			.right{
				flex-shrink: 0;
				align-items: center;
				margin-left: 20upx;
				.icon{
					justify-content: center;
					align-items: center;
					width: 80upx;
					height: 60upx;
					border-left: solid 1upx #aaa;
					font-size: 40upx;
					color: #777;
				}
			}
		}
	}
</style>
