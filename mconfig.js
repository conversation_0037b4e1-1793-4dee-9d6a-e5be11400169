// 环境变量配置文件

// 开发环境
// #ifdef H5-DEVELOPMENT
let NODE_ENV = 'development'
let baseUrl = 'https://micro-gateway.gxjettoll.cn:8443'
let msgUrl = baseUrl + '/site/message/mobile/'

// #endif

// 生产环境
// #ifdef H5-PRODUCTION

let NODE_ENV = 'release'
let baseUrl = 'https://gateway.gxetc.com.cn'
let msgUrl = baseUrl + '/site/message/mobile/'

// #endif

let config = {
  NODE_ENV,
  baseUrl,
  msgUrl
}

module.exports = config
