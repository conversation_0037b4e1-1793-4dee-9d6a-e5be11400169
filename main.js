import Vue from "vue"
import App from "./App"
import store from "./store"
import uView from "uview-ui";
Vue.use(uView);
import customerService from './components/proComponents/customerService/customerService.vue'
import privacyDialog from './components/privacyDialog/privacyDialog.vue'
Vue.component('customerService', customerService)
Vue.component('privacyDialog', privacyDialog)
import request from "@/common/request-1/request.js";
import {
	decryptionParams,
	encryptionParams
} from "@/common/request-1/security.js"
import {
	getTicket,
	getMd5Key,
	getAesKey,
	getDecodingKey,
	getStore,
	getAccessToken
} from "@/common/storageUtil.js";
Vue.config.productionTip = false;

import initInterfaces from '@/common/api/main.js';
import logger from '@/common/method/log.js'
initInterfaces();
App.mpType = "app"
Vue.prototype.$publicKey =
	"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC/gASgOJ2T/YEArehpKql7Hk6bFhqKkXoWezhJ9w54/o10P3rKQWlx3a6iUv/+SfOAoiHB3Zzept2xJZMksBRnk0WbD9nOw/pf1V7yGaVEn1CA3PjwvEqf80DWEgKY7CHx3WHWnfinNvwmd93MuAsbsHoBjnjLxZmMRbbr7m+LEwIDAQAB"
Vue.prototype.$store = store;
Vue.prototype.$serviceName = ['obu', 'etc', 'jl', "jt", 'gv', 'wx', 'LY']
// 正式环境不打印日志
// if (process.env.NODE_ENV !== "development") {
// 	console.log = () => {}
// }
// 微信公众号appid
Vue.prototype.$publicAppId = 'wx09a244d60adf808d'
import autoImage from '@/components/t-autoImage/autoImage.vue'
Vue.component('autoImage', autoImage)

import {
	subscriptionMethod,
	goToRecharge
} from '@/common/commonMethod.js'

Vue.prototype.$subscriptionMethod = subscriptionMethod
Vue.prototype.$goToRecharge = goToRecharge

//默认生产环境参数 TODO 生产环境部署注意
// let baseUrl = "https://micro-gateway.gxjettoll.cn:8443" // 测试环境地址
let baseUrl = "https://gateway.gxetc.com.cn" // 生产环境地址
console.log(baseUrl + 'ddd');
let decodingKey = "9d00b145246dd105" //只做登录和获取openid时使用
// 测试环境参数
if (process.env.NODE_ENV === "development") {
	baseUrl = "https://micro-gateway.gxjettoll.cn:8443"

	decodingKey = "8d7a715b633d394c" //只做登录和获取openid时使用
}


Vue.prototype.$decodingKey = decodingKey
Vue.prototype.$baseUrl = baseUrl
let aesKeyInit = 'f0b426c13dcc42a5b630637cd4b324c1';
let md5KeyInit = '5b630637cd4b324c1f0b426c13dcc42b';
Vue.prototype.$aesKeyInit = aesKeyInit
Vue.prototype.$md5KeyInit = md5KeyInit
request.setConfig({
	baseUrl,
	aesKey: aesKeyInit,
	md5Key: md5KeyInit,
	ticket: '',
	appId: '100001',
	dataType: 'json', // 可删除，默认为json
	responseType: 'text', // 可删除，默认为text
	header: {
		"Content-Type": "application/json"
	}
})
// 设置请求拦截器
request.interceptors.request(config => {
	return config; // 返回修改后的配置，如未修改也需添加这行
})

let modalInstance = null;
const resetModal = (options) => {
	// logger.info('resetModal提示:', modalInstance)
	if (!modalInstance) {
		modalInstance = +new Date();
		uni.showModal({
			title: options.title,
			content: options.content,
			showCancel: false,
			success: (res) => {
				modalInstance = null;
				if (res.confirm) {
					uni.reLaunch({
						url: "/pagesD/login/p-login"
					})
				}
			}
		});
	}
};
// 设置响应拦截器
request.interceptors.response((response) => {
	
	let code = !!(response &&   response.data && response.data.code) ? response.data.code : ''
	let msg  = !!(response &&   response.data && response.data.msg) ? response.data.msg : ''
	try {
		if(code == '960'){
			logger.info('响应960日志:', JSON.stringify(response))
		}
	} catch (e) {
		
	}
	// 网络500错误
	if (response && response.statusCode == 500) {
		resetModal({
			title: "提示",
			// content: "服务器异常，请稍后再试，" + "错误码:" + "[" + response.statusCode + "]",
			content: response.data.msg,		
		})
	}
	// 接收请求，执行响应操作
	if (!response.data) {
		return Promise.reject({
			code: "999",
			msg: "网络异常"
		});
	}
	if (response && (response.statusCode == 401 || response.statusCode == 403)) {
		resetModal({
			title: "提示",
			// content: "登录信息过期，请重新登录，" + "错误码:" + "[" + response.statusCode + "]",
			content: response.data.msg,	
		})
	}
	
	const res = response;
	console.log(response)
	let result = null;
	let deAesKey = getAesKey() || aesKeyInit
	let deMd5Key = getMd5Key() || md5KeyInit
	if (res.data && res.data.method && (res.data.method == Vue.prototype.$interfaces.validLogin.method || res
			.data.method == Vue.prototype.$interfaces.getSceneValue.method)) {
		deAesKey = aesKeyInit;
		deMd5Key = md5KeyInit;
	}
	result = decryptionParams(res.data, deAesKey, deMd5Key);
	if (result) {
		result = JSON.parse(result);
		console.log(result);
		try {
			result.data = result.data ? JSON.parse(result.data) : '';
		} catch (e) {
			result.data = result.data ? result.data : ''
		}
		if (result.code == '401') {
			resetModal({
				title: "提示",
				content: result.msg,
			})
			return
		}
		return result;
	}
	return Promise.reject({
		code: code,
		msg: msg
	})
}, error => {
	return Promise.reject({
		code: error.return_code,
		msg: error.message
	})
})

//挂载到全局vue实例上，在页面中可以使用this.$request调用request实例下相应方法
Vue.prototype.$request = request;
Vue.prototype.$logger = logger
const app = new Vue({
	store,
	...App
})
app.$mount()